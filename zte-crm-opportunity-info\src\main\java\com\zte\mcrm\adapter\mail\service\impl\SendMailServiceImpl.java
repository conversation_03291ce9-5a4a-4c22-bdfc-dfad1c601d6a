package com.zte.mcrm.adapter.mail.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.adapter.mail.domain.ZmailModel;
import com.zte.mcrm.adapter.mail.service.SendMailService;
import com.zte.mcrm.adapter.mail.wsdl.MailTemplateServiceServiceStub;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.channel.model.entity.OpportunityMailEntity;
import com.zte.mcrm.channel.util.UrlEncodeUtils;
import com.zte.mcrm.common.exception.BusinessRuntimeException;
import com.zte.mcrm.mail.access.dao.OptyMailDao;
import com.zte.mcrm.mail.access.vo.OptyMailModelVO;
import com.zte.mcrm.mail.access.vo.OptyMailVO;
import com.zte.mcrm.mail.business.service.IOptyMailService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
public class SendMailServiceImpl implements SendMailService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SendMailServiceImpl.class);

    @Value("${mail.url}")
    private String mailUrl;
    @Value("${mail.ssHttpHead}")
    private String ssHttpHead;
    @Value("${prmui.url}")
    private String prmUrl;
    @Value("${ipartnerui.url}")
    private String ipartnerUrl;
    @Value("${opportunity.internalui.url}")
    private String internaluiUrl;
    @Value("${opportunity.ichannelui.url}")
    private String ichanneluiUrl;

    @Autowired
    IOptyMailService optyMailService;

    @Autowired
    OptyMailDao optyMailDao;

    @Override
    public void send(ZmailModel zmailModel) {
        try {
            String zmailContent = zmailModel.buildMailModelInXml();
            MailTemplateServiceServiceStub stub = new MailTemplateServiceServiceStub(mailUrl);
            MailTemplateServiceServiceStub.SendMail mail = new MailTemplateServiceServiceStub.SendMail();
            mail.setMailBody(zmailContent);
            MailTemplateServiceServiceStub.SendMailResponse sendMailResponse = stub.sendMail(mail);
            if(sendMailResponse == null || !Objects.equals(RetCode.SUCCESS_CODE, sendMailResponse.getSendMailReturn())){
               throw new IllegalStateException(OpportunityConstant.SEND_MAIL_ERROR);
            }
            LOGGER.info("send mail:{} sucess", zmailModel);
        } catch (Exception e) {
            LOGGER.error("send mail: {} error: {}", zmailModel, e.getMessage(), e);
            throw new BusinessRuntimeException(OpportunityConstant.SEND_MAIL_ERROR);
        }
    }


    @Override
    public void sendOptyMail(OpportunityMailEntity opportunityMailEntity) {
        // 邮件发送
        String mailResult = "fail";
        OptyMailModelVO optyMailModelVO = new OptyMailModelVO();
        try {
            opportunityMailEntity.setSsHttpHead(ssHttpHead);
            // 业务参数
            JSONObject businessJson = (JSONObject)  com.alibaba.fastjson.JSON.toJSON(opportunityMailEntity);

            optyMailModelVO = optyMailService.getOptyMailModel(opportunityMailEntity.getModelCode(),
                    opportunityMailEntity.getModelType(),
                    businessJson);
            String mailLink = optyMailModelVO.getMailLink();
            optyMailModelVO.setReceiverIds(opportunityMailEntity.getReceiverIds());
            if (mailLink.startsWith(ichanneluiUrl)){
                mailLink = ipartnerUrl + UrlEncodeUtils.unescapeAndBtoa(URLEncoder.encode(mailLink,"UTF-8"));
            }else if(mailLink.startsWith(internaluiUrl)){
                mailLink = prmUrl + URLEncoder.encode(mailLink, "UTF-8");
            }
            optyMailModelVO.setMailLink(mailLink);
            optyMailModelVO.setMailLinkEn(mailLink);
            OpportunityConstant.MailClickEnum mailClickEnum = OpportunityConstant.MailClickEnum.valueOfCode(optyMailModelVO.getClickType());
            optyMailModelVO.setClickLookCn(mailClickEnum.getMsgCn());
            optyMailModelVO.setClickLookEn(mailClickEnum.getMsgEn());
            optyMailModelVO.setMailTo(opportunityMailEntity.getMailTo());
            // 抄送人邮件地址(直发),分号隔开
            if(StringUtils.isNotBlank(opportunityMailEntity.getMailCc())){
                optyMailModelVO.setMailToCc(opportunityMailEntity.getMailCc());
            }
            LOGGER.info("商机邮件发送,optyMailModelVO:{}", optyMailModelVO);
            send(new ZmailModel(optyMailModelVO));
            mailResult = "success";
        } catch (Exception e) {
            mailResult = "fail";
            LOGGER.error("send mail: {} error: {}", optyMailModelVO, e.getMessage(), e);
            throw new BusinessRuntimeException(OpportunityConstant.SEND_MAIL_ERROR);
        } finally {
            // 保存邮件发送记录
            Map<String, String> params = new HashMap<>(6);
            params.put("mailTitle", optyMailModelVO.getMailTitle());
            params.put("templateCode", optyMailModelVO.getTemplateCode());
            params.put("templateType", optyMailModelVO.getTemplateType());
            params.put("mailReceiver", optyMailModelVO.getMailTo());
            params.put("optyMailModel", JSON.toJSONString(optyMailModelVO));
            params.put("mailResult", mailResult);
            OptyMailVO optyMailVO = new OptyMailVO(params);
            optyMailVO.setBusinessId(opportunityMailEntity.getRowId());
            optyMailDao.insertOptyMail(optyMailVO);
        }
    }

}
