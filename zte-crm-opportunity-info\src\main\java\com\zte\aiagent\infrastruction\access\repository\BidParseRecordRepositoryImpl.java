package com.zte.aiagent.infrastruction.access.repository;

import com.zte.aiagent.domain.repository.BidParseRecordRepository;
import com.zte.aiagent.infrastruction.access.converter.BidParseRecordConverter;
import com.zte.aiagent.infrastruction.access.mapper.BidParseRecordMapper;
import com.zte.aiagent.infrastruction.access.po.BidParseRecordPO;
import com.zte.aiagent.ui.dto.vo.BidParseRecordVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 解析项记录数据访问仓储实现类
 * 负责解析项记录相关的数据访问操作
 *
 * <AUTHOR>
 */
@Service
public class BidParseRecordRepositoryImpl implements BidParseRecordRepository {

    @Autowired
    private BidParseRecordMapper bidParseRecordMapper;

    @Override
    public int insert(BidParseRecordPO bidParseRecord) {
        return bidParseRecordMapper.insert(bidParseRecord);
    }

    @Override
    public BidParseRecordPO selectByPrimaryKey(String rowId) {
        return bidParseRecordMapper.selectByPrimaryKey(rowId);
    }

    @Override
    public int updateByPrimaryKey(BidParseRecordPO bidParseRecord) {
        return bidParseRecordMapper.updateByPrimaryKey(bidParseRecord);
    }

    @Override
    public int deleteByPrimaryKey(String rowId) {
        return bidParseRecordMapper.deleteByPrimaryKey(rowId);
    }

    @Override
    public List<BidParseRecordVO> selectByDocumentId(String documentId) {
        List<BidParseRecordPO> recordPOList = bidParseRecordMapper.selectByDocumentId(documentId);
        return BidParseRecordConverter.INSTANCE.toVOList(recordPOList);
    }
}
