package com.zte.mcrm.channel.constant;

/**
 * 审批中心流程实例状态
 *
 * <AUTHOR>
 * @date 2021/11/3
 */
public enum ApprovalStatusEnum {

    /**
     * 实例仍在审批中
     */
    ACTIVE("ACTIVE"),
    /**
     * 实例已经审批结束
     */
    COMPLETE("COMPLETE"),
    /**
     * 实例被撤销
     */
    EXTERNALLY_TERMINATED("EXTERNALLY_TERMINATED");

    private String descEn;

    public String getDescEn() {
        return descEn;
    }

    ApprovalStatusEnum(String descEn) {
        this.descEn = descEn;
    }
}