/**
 *  Copyright  2018 赵世光 All rights reserved.
 *  @author: 6011000197 
 *  @date: 2018年5月28日 下午2:08:03 
 */
package com.zte.mcrm.common.util;

import com.zte.springbootframe.common.exception.BusiException;

/**  
 * <p>Title: RowIdUtilThread</p>  
 * <p>Description: </p>  
 * <AUTHOR> <PERSON><PERSON><PERSON>uang
 * @date 2018年5月28日  
 */
public class RowIdUtilThread extends Thread{
	
//	public RowIdUtilThread(String name){
//		super(name);
//	}
//	public void run() {
//		 try {
//				 for(int i=0;i<100;i++){
//					 String id = RowIdUtil.generateRowId();
//					 System.out.println("insert into zhaoshiguagn_test (row_id) values('"+id+"');");
//				 } 
//			 }
//		 catch (BusiException e) {
//				e.printStackTrace();
//		 }
//	 }
//
//	 public static void main(String[] args)throws Exception{
//		 RowIdUtilThread t1 = new RowIdUtilThread("线程1");
//		 RowIdUtilThread t2 = new RowIdUtilThread("线程2");
//		 RowIdUtilThread t3 = new RowIdUtilThread("线程3");
//		 RowIdUtilThread t4 = new RowIdUtilThread("线程4");
//		 RowIdUtilThread t5 = new RowIdUtilThread("线程4");
//		 RowIdUtilThread t6 = new RowIdUtilThread("线程4");
//		 RowIdUtilThread t7 = new RowIdUtilThread("线程4");
//		 RowIdUtilThread t8 = new RowIdUtilThread("线程4");
//		 RowIdUtilThread t9 = new RowIdUtilThread("线程4");
//		 
//		 t1.start();
//		 t2.start();
//		 t3.start();
//		 t4.start();
//		 t5.start();
//		 t6.start();
//		 t7.start();
//		 t8.start();
//		 t9.start();
//
//		 
//		
//	 }
	

}
