package com.zte.mcrm.channel.dao;

import com.zte.mcrm.channel.model.entity.ComApprovalRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 * 审批记录 数据访问接口类 
 * <AUTHOR>
 * @date 2021/10/09 
 */
@Mapper
public interface ComApprovalRecordDao{
	List<ComApprovalRecord> getFlowIdByOpportunityCode(@Param("codes") List<String> codes);

    /**
     * 根据主键查询
     * <AUTHOR>
     * @param rowId 主键
     * @date 2021/10/09 
     * @return 实体
     */
	ComApprovalRecord get(@Param("rowId")Long rowId);


	/**
	 * 根据业务id查询
	 * <AUTHOR>
	 * @param businessId 主键
	 * @date 2021/10/09
	 * @return 实体
	 */
	ComApprovalRecord getByBusinessId(@Param("businessId")String  businessId);
	
    /**
     * 查询列表
     * <AUTHOR>
     * @param map 查询条件
     * @date 2021/10/09 
     * @return 实体集合
     */
	List<ComApprovalRecord> getList(Map<String, Object> map);
	
	/**
     * 软删除，enabled_flag字段更新为N
     * <AUTHOR>
     * @param rowId 主键
     * @date 2021/10/09 
     * @return 删除总数
     */	
	int softDelete(@Param("rowId")Long rowId);

	/**
	 * 根据流程id和业务Id失效记录
	 * @param businessId
	 * @param workFlowInstanceId
	 * @return
	 */
	int softDeleteByBusinessIdAndFlowId(@Param("businessId")String businessId, @Param("flowId")String workFlowInstanceId);
	
    /**
     * 删除
     * <AUTHOR>
     * @param rowId 主键
     * @date 2021/10/09 
     * @return 删除总数
     */	
	int delete(@Param("rowId")Long rowId);

    /**
     * 动态新增
     * <AUTHOR>
     * @param entity 新增实体
     * @date 2021/10/09 
     * @return 新增总数
     */	
	int insert(ComApprovalRecord entity);

    /**
     * 批量新增
     * <AUTHOR>
     * @param list 新增实体集合
     * @date 2021/10/09 
     * @return 新增总数
     */	
	int insertByBatch(List<ComApprovalRecord> list);

    /**
     * 更新
     * <AUTHOR>
     * @param entity 更新条件
     * @date 2021/10/09 
     * @return 更新影响总数
     */		
	int update(ComApprovalRecord entity);

    /**
     * 统计
     * <AUTHOR>
     * @param map 查询条件
     * @date 2021/10/09 
     * @return 统计总数
     */	
	long getCount(Map<String, Object> map);

    /**
     * 分页查询
     * <AUTHOR>
     * @param map 查询条件
     * @date 2021/10/09 
     * @return 实体集合
     */	
	List<ComApprovalRecord> getPage(Map<String, Object> map);

	/**
	 * 获取流程实例Id
	 * <AUTHOR>
	 * @param businessId 业务Id
	 * @date 2021/10/12
	 * @return 流程实例Id
	 */
	String queryFlowInstance(String businessId);
}
