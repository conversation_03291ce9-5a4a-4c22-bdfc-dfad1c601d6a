package com.zte.mcrm.adapter.service;

import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.adapter.model.dto.BlackListDTO;
import com.zte.mcrm.adapter.model.dto.ChannelCompanySearchDTO;
import com.zte.mcrm.adapter.model.dto.CompanyInfoSearchDTO;
import com.zte.mcrm.adapter.model.vo.CompanyOrgInfoVO;
import com.zte.mcrm.adapter.model.vo.UnregisteredCompanyVO;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2021/07/20
 * @Description:
 */
public interface ICpcService {
    /**
     * 根据公司名称模糊搜索分页获取公司信息接口-注册前使用
     * @param form
     * @return
     */
    PageRows<UnregisteredCompanyVO> queryCompanyInfoList(FormData<ChannelCompanySearchDTO> form);

    /**
     * 通过企业统一代码获取公司详细信息-注册前使用
     * @param orgUnifiedCode
     * @return
     */
    UnregisteredCompanyVO searchCompanyInfo(String orgUnifiedCode);

    /**
     * 通过企业统一代码获取公司 黄黑名单信息
     * <AUTHOR>
     */
    List<BlackListDTO> getBlackListInfo(String orgUnifiedCode) throws Exception;

    List<BlackListDTO> getBlackListInfoByCrmCustomerCode(String crmCustomerCode) throws Exception;

    CompanyOrgInfoVO queryOrgInfoByCustomerName(CompanyInfoSearchDTO companyInfoSearch) throws BusiException, RouteException;
}
