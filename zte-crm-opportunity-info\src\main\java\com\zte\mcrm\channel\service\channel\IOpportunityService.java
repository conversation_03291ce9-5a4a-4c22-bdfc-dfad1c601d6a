package com.zte.mcrm.channel.service.channel;

import com.zte.crm.eva.base.infrastructure.client.model.PrmQuickCodeValue;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.adapter.model.vo.PrmQuickCodeValueVO;
import com.zte.mcrm.channel.constant.OpportunityCurrentStatus;
import com.zte.mcrm.channel.model.dto.OpportunityQueryDTO;
import com.zte.mcrm.channel.model.dto.SendEmailPreDto;
import com.zte.mcrm.channel.model.entity.*;
import com.zte.mcrm.channel.model.vo.OpportunityVO;
import com.zte.mcrm.channel.model.vo.OpptyCustomerCreateRecordVO;
import com.zte.opty.common.enums.OptyStatusEnum;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 *  服务接口类
 * <AUTHOR>
 * @date 2021/09/14
 */
public interface IOpportunityService {
    /**
     * 根据ID查询
     * @param rowId 主键ID
     * @return 实体
     * <AUTHOR>
     * @date 2021/09/14
     */
	Opportunity get(String rowId);

    /**
     * 根据ID查询-不过滤enabledFlag
     * @param rowId 主键ID
     * @return 实体
     * <AUTHOR>
     * @date 2021/09/14
     */
    Opportunity getAll(String rowId);

    Opportunity getByOptyCd(String optyCd);

    /**
     * 更新商机状态
     * @param rowId
     * @param status
     * @return
     */
    int updateStatus(String rowId, OptyStatusEnum status);

    OpportunityInfo getIChannelOpportunity(String rowId) throws Exception;

    OpportunityInfo getOldOpportunityInfo(String rowId) throws Exception;

    OpportunityInfo getOpportunity(String rowId) throws Exception;

    OpportunityInfo getOpportunityInfo(String rowId) throws Exception;

    /**
     * 查询列表
     * @param map 参数集合
     * @return 实体集合
     * <AUTHOR>
     * @date 2021/09/14
     */
	List<Opportunity> getList(Map<String, Object> map);

    /**
     * 软删除草稿
     * @param rowId 主键ID
     * @return 删除记录个数
     * <AUTHOR>
     * @date 2021/09/14
     */
    int softDeleteDraft(String rowId);

    /**
     * 新增
     *
     * @param entity            实体对象
     * @param opportunityDetail
     * @return 新增的记录对象, 注意是提交数据库之前的实体对象
     * <AUTHOR>
     * @date 2021/09/14
     */
	Opportunity insert(Opportunity entity, OpportunityDetail opportunityDetail);


    /**
     * 保存或更新商机主表信息
     *
     * @param opportunity       商机主表信息
     * @param opportunityDetail
     * @return
     */
    Opportunity insertOrUpdate(Opportunity opportunity, OpportunityDetail opportunityDetail) throws Exception;

    /**
     * 更新
     * @param entity 实体对象
     * @return 修改的记录对象,注意是提交数据库之前的实体对象
     * <AUTHOR>
     * @date 2021/09/14
     */
	Opportunity update(Opportunity entity);

    /**
     * 更新提交时间
     * @param rowId
     * @param submitDate
     */
    void updateSubmitDate(String rowId, Date submitDate);

    /**
     * 更新商机当前状态
     * 1. 商机当前状态维护为丢标后，商机状态直接更新为：丢单
     * 2. 商机当前状态维护为取消后，商机状态直接更新为：取消
     * @param currentStatus
     * @return
     */
    int updateCurrentStatus(String rowId, OpportunityCurrentStatus currentStatus);

    Opportunity updateOnTheQuiet(Opportunity entity);

    /**
     * 商机主表全量更新
     *
     * @param entity            实体对象
     * @param opportunityDetail
     * @return 修改的记录对象, 注意是提交数据库之前的实体对象
     * <AUTHOR>
     * @date 2021/09/14
     */
    Opportunity updateAll(Opportunity entity, OpportunityDetail opportunityDetail);

    /**
     * 统计
     * @param map 参数集合
     * @return 统计总数
     * <AUTHOR>
     * @date 2021/09/14
     */
	long getCount(Map<String, Object> map);

   /**
    * 分页查询
    * @param map 参数集合
    * @return 实体集合
    * <AUTHOR>
    * @date 2021/09/14
    */
	List<OpportunityVO> getPage(Map<String, Object> map, boolean manageAuthFlag) throws Exception;

    /**
    * 分页查询
    * @param form 参数集合
    * @return 实体集合
    * <AUTHOR>
    * @date 2021/09/14
    */
//    PageRows<Opportunity> getPageRows(FormData<Opportunity> form);

    /**
     * 撤销商机
     * @param rowId
     * @param systemSource
     * @return
     */
    String cancelOpportunity(String rowId,String systemSource);

    void sendMail(String rowId, List<String> receivers, String modelCode, String modelType);

    void sendMailWithOpinion(SendEmailPreDto sendEmailPreDto);

    OpportunityMailEntity getOpptyMailEntity(String rowId) throws Exception;

    /**
     * 发送邮件通知
     * @param rowId 商机id
     * @param receiver 收件人地址
     * @param modelCode 模板编码
     * @param modelType 模板类型
     */
    void sendMail(String rowId, String receiver, String modelCode,String modelType);

    List<String> getApproverList(String rowId);

    /**
     * 校验商机权限
     * @param rowId
     * @param systemSource
     * @return
     */
    public boolean verifyAuth(String rowId, String systemSource) ;

    /**
     * 激活商机
     * @param rowId
     * @param systemSource
     * @return
     */
    String activateOpportunity(String rowId,String systemSource);

    /**
     *
     * @param rowID
     * @return
     */
    String urgeOpportunity(String rowID);
    /**
     * 分页查询
     * @param form
     * @param isExport 是否导出
     * @return
     */
    PageRows<OpportunityVO> getOpportunityVoPageRows(FormData<OpportunityQueryDTO> form, Boolean isExport) throws Exception;

    /**
     * 获取当前月还未更新月报的商机列表
     * @param form
     * @param empNo 工号
     * @return
     */
    PageRows<OpportunityVO> getMonthReportOpportunity(FormData<OpportunityQueryDTO> form, String empNo);


    Map<String,Object> getTotalOpportunity(String crmCustomerCode);

    /**
     * ichannel 商机导出
     * @param form
     * @param response
     */
    void exportOpportunityList(FormData<OpportunityQueryDTO> form, HttpServletResponse response) throws Exception;
    



    /**
     * 校验是否具有查看详情权限
     * @param rowId
     * @return
     */
    Boolean checkDetailReviewPermission(String rowId);

    /**
     * 根据 rowId 来更新 主表状态
     * @param rowId 主键
     * @return 更新总数
     */
    int updateStatusByRowId(String rowId, String status);

    /**
     * 根据rowId 刷新合规状态
     * */
    Boolean refreshComplianceStatus(String rowId) throws Exception;


    /**
     * 根据rowId 发送受限制主体邮件通知
     * */
    void sendRestrictedEntityMail(String rowId);

    /**
     *
     * */
    void dailyClosingTasksHandleComplianceStatus();

    /**
     * iChannel查询最终客户创建记录
     *
     * @param formData 参数集合
     * @return 实体集合
     * @throws Exception
     * <AUTHOR>
     * @date 2023/05/10
     */
    PageRows<OpptyCustomerCreateRecordVO> queryCustomerRecordWithIChannel(FormData<IChannelOpptyCustomerCreateRecordQuery> formData) throws Exception;

    /**
     *  判断对应商机是否需要维护主产品
     * @param rowIds 商机主键
     * @param createdTimeStart 创建时间范围起点
     * @param createdTimeEnd 创建时间范围终点
     * @return 没有传入主键，则查询全部主键数据；没传入时间，则查询全部时间数据
     */
    Map<String, Boolean> judgeNeedMaintainMainProds(List<String> rowIds, String createdTimeStart, String createdTimeEnd);

    /**
     * 根据业务类型查询参数配置
     * @param codeTypeList
     * @return
     */
    List<PrmQuickCodeValueVO> queryByCodeType(List<String> codeTypeList);
}
