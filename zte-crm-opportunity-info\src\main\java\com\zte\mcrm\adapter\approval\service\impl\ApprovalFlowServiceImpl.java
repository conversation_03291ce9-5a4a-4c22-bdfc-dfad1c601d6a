package com.zte.mcrm.adapter.approval.service.impl;

import com.alibaba.fastjson.TypeReference;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.mcrm.adapter.approval.model.*;
import com.zte.mcrm.adapter.approval.model.dto.*;
import com.zte.mcrm.adapter.approval.service.ApprovalFlowService;
import com.zte.mcrm.adapter.approval.service.ApprovalHeaderBuilder;
import com.zte.mcrm.common.consts.HttpMethodEnum;
import com.zte.mcrm.common.exception.ApprovalOperationException;
import com.zte.mcrm.common.exception.RemoteServiceException;
import com.zte.mcrm.common.util.ApprovedTaskUtils;
import com.zte.mcrm.common.util.MicroServiceInvokeUtil;
import com.zte.springbootframe.util.local.LocalMessageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: 10243305
 * @date: 2021/6/24 下午3:13
 */
@Slf4j
@Service
public class ApprovalFlowServiceImpl implements ApprovalFlowService {

    private final static String URL_START_FLOW = "/approval/flow/start";
    private final static String URL_APPROVE = "/approval/task/submit";
    private final static String URL_REASSIGN = "/approval/task/reassign";
    private final static String URL_RESET_FLOW_PARAMS = "/approval/parameters/reset";
    private final static String URL_QUERY_TASKS = "/approval/tasks";
    private final static String URL_FLOW_PROGRESS = "/approval/flow/progress";
    private final static String URL_FLOW_REVOKE = "/approval/flow/revoke";
    private final static String URL_TASK_URGE = "/approval/task/urge";
    private final static String URL_FLOW_URGE = "/approval/flow/urge";
    private final static String URL_FLOW_ROLLBACK = "/approval/flow/rollback";
    private final static String URL_FLOW_ROLLBACK_NODE_LIST = "/approval/flow/rollback/node/list";
    private final static String URL_FLOW_TASK_LIST = "/flow/nodeCodeInfo";
    private final static String URL_APPROVING_TASK_LIST = "/approval/current/active/node";
    private final static String URL_BATCH_FLOW_PROGRESS = "/approval/batch/flow/progress";
    private final static String URL_ADVANCED_QUERY_TASKS = "/approval/tasks/advanced/query";
    private final static String URL_APPROVED_ADVANCED_QUERY_TASKS = "/approval/tasks/history/advanced/query";
    private final static String URL_FLOW_APPROVAL_RECORDS = "/approval/flow/approve/records";
    private final static String URL_FLOW_INSTANCE_PANORAMA = "/approval/flow/panorama";
    private final static String URL_REFRESH_MOA_PARAMETERS = "/approval/moa/parameters/refresh";

    private final static String URL_CONNECTION = "?";
    private final static String URL_PARAM_CONNECTION = "&";
    private final static String EQUAL = "=";

    private final Logger logger = LoggerFactory.getLogger(this.getClass());


    @Override
    public String startFlow(ApprovalStartParamsDTO startParams) {
        return this.invokeApprovalService(URL_START_FLOW, HttpMethodEnum.POST, startParams,
                new TypeReference<ServiceData<String>>() {});
    }

    @Override
    public void approve(OpinionDTO opinion) {
        this.invokeApprovalService(URL_APPROVE, HttpMethodEnum.POST, opinion,
                new TypeReference<ServiceData<Void>>() {});

    }

    @Override
    public Boolean systemApprove(OpinionDTO opinionDTO, String empNo) {
        ApprovalHeaderBuilder approvalHeaderBuilder =
                (ApprovalHeaderBuilder) SpringContextUtil.getBean("ApprovalHeaderBuilder");
        Map<String, String> headerParams = new HashMap<>();
        if (null != approvalHeaderBuilder) {
            headerParams = approvalHeaderBuilder.getHeaderParams(MicroServiceInvokeUtil.headerParams());
        }
        headerParams.put("X-Emp-No", empNo);
        try {
            Map<String, String> paramString = new HashMap<>(3);
            paramString.put("serviceName", "zte-iss-approval-manage");
            paramString.put("version", "v1");
            paramString.put("url", URL_APPROVE);
            MicroServiceInvokeUtil.invokeServiceAndReturnBO(paramString, HttpMethodEnum.POST, opinionDTO, new TypeReference<ServiceData<Void>>(){}, headerParams);

        }catch (Exception e){
            throw new com.zte.itp.msa.core.exception.BusiException(RetCode.BUSINESSERROR_CODE, LocalMessageUtils.getMessage("customerDraftCreateException"));
        }
        return true;
    }

    @Override
    public String refreshMoaParameters(FlowBusiRefreshParamDTO flowBusiRefreshParamDTO) {
        return this.invokeApprovalService(URL_REFRESH_MOA_PARAMETERS,HttpMethodEnum.POST,flowBusiRefreshParamDTO,new TypeReference<ServiceData<String>>() {});
    }

    @Override
    public String reassignApproval(ReassignDTO opinion) {
        return this.invokeApprovalService(URL_REASSIGN, HttpMethodEnum.POST, opinion,
                new TypeReference<ServiceData<String>>() {});
    }

    @Override
    public String resetFlowParams(ApprovalResetStartParamsDTO params) {
        return this.invokeApprovalService(URL_RESET_FLOW_PARAMS, HttpMethodEnum.POST, params,
                new TypeReference<ServiceData<String>>() {});
    }

    @Override
    public PageRows<ApprovingNodeInfo> getApprovingNodes(ApprovingParameter approvingParameter) {
        return this.invokeApprovalService(URL_QUERY_TASKS, HttpMethodEnum.GET, approvingParameter,
                new TypeReference<ServiceData<PageRows<ApprovingNodeInfo>>>() {});

    }

    @Override
    public List<ApprovalProgressDTO> getFlowProgress(FlowParameter flowParameter) {
        return this.invokeApprovalService(URL_FLOW_PROGRESS, HttpMethodEnum.GET, flowParameter,
                new TypeReference<ServiceData<List<ApprovalProgressDTO>>>() {});
    }

    @Override
    public String revokeFlow(ApprovalRevokeParamsDTO revokeParams) {
        return this.invokeApprovalService(URL_FLOW_REVOKE, HttpMethodEnum.POST, revokeParams,
                new TypeReference<ServiceData<String>>() {});
    }

    @Override
    public String urgeTask(FlowTaskParameter flowTaskParameter) {
        return this.invokeApprovalService(URL_FLOW_URGE, HttpMethodEnum.POST, flowTaskParameter,
                new TypeReference<ServiceData<String>>() {});
    }

    @Override
    public void rollBack(ApprovalRollBackParamDTO rollBackParam) {
        this.invokeApprovalService(URL_FLOW_ROLLBACK, HttpMethodEnum.POST, rollBackParam,
                new TypeReference<ServiceData<Void>>() {});
    }

    @Override
    public List<FlowTask> getRollBackNodeList(FlowParameter flowParameter) {
        return this.invokeApprovalService(URL_FLOW_ROLLBACK_NODE_LIST, HttpMethodEnum.GET, flowParameter,
                new TypeReference<ServiceData<List<FlowTask>>>() {});
    }

    @Override
    public List<FlowTaskInfo> getFlowTaskList(String flowCode) {
        return this.invokeApprovalService(URL_FLOW_TASK_LIST, HttpMethodEnum.GET, new FlowTaskInfoParamDTO(flowCode),
                new TypeReference<ServiceData<List<FlowTaskInfo>>>() {});
    }

    @Override
    public PageRows<FlowActiveTaskInfo> getApprovingTaskList(ApprovalActiveTaskParamsDTO param) {
        String url = buildParamUrl(URL_APPROVING_TASK_LIST, buildPageParams(param));
        return this.invokeApprovalService(url, HttpMethodEnum.POST, param,
                new TypeReference<ServiceData<PageRows<FlowActiveTaskInfo>>>() {});
    }

    @Override
    public Map<String, ApprovalProgressDTO> getFlowProgressByFlowInstanceIds(List<String> flowInstanceIds) {
        return this.invokeApprovalService(URL_BATCH_FLOW_PROGRESS, HttpMethodEnum.POST, flowInstanceIds,
                new TypeReference<ServiceData<Map<String, ApprovalProgressDTO>>>() {});
    }


    /**
     * 获取所有待我审批单据
     * @param approvingQueryParam
     * @return
     */
    @Override
    public List<ApprovingNodeInfo> getAllMyPending(ApprovingQueryParam approvingQueryParam){
        long theRemainingAmount = 1L;
        long currentPage = 1L;
        long pageSize = 500L;
        List<ApprovingNodeInfo> allApprovingNodeInfoList = new ArrayList<>();
        while (theRemainingAmount > 0) {
            approvingQueryParam.setPageSize(pageSize);
            approvingQueryParam.setPageNo(currentPage);
            PageRows<ApprovingNodeInfo> approvingTasksByCondition;
            approvingTasksByCondition = getApprovingTasksByCondition(approvingQueryParam);
            List<ApprovingNodeInfo> approvingNodeInfos = approvingTasksByCondition.getRows();
            // 避免死循环
            if(CollectionUtils.isEmpty(approvingNodeInfos)){
                logger.info("MyPendingInfos_return_approvingNodeInfos_is_empty,the_loop_will_end!");
                return allApprovingNodeInfoList;
            }
            allApprovingNodeInfoList.addAll(approvingNodeInfos);
            long total = approvingTasksByCondition.getTotal();
            logger.info("MyPendingInfos total:{}", total);
            theRemainingAmount = total - allApprovingNodeInfoList.size();
            currentPage++;
        }
        return allApprovingNodeInfoList;
    }

    /**
     * 获取所有我已审批单据
     * @param approvingQueryParam
     * @return
     */
    @Override
    public List<ApprovedNodeInfo> getAllMyPended(ApprovingQueryParam approvingQueryParam){
        long theRemainingAmount = 1L;
        long currentPage = 1L;
        long pageSize = 500L;
        List<ApprovedNodeInfo> allApprovedTaskInfo = new ArrayList<>();
        while (theRemainingAmount > 0) {
            approvingQueryParam.setPageSize(pageSize);
            approvingQueryParam.setPageNo(currentPage);
            PageRows<ApprovedNodeInfo> approvedTasksByCondition;
            approvedTasksByCondition = getApprovedTasksByCondition(approvingQueryParam);
            List<ApprovedNodeInfo> approvedNodeInfos = approvedTasksByCondition.getRows();
            // 避免死循环
            if(CollectionUtils.isEmpty(approvedNodeInfos)){
                logger.info("getAllMyPended_return_approvedNodeInfos_is_empty,the_loop_will_end!");
                return allApprovedTaskInfo;
            }
            allApprovedTaskInfo.addAll(approvedNodeInfos);
            long total = approvedTasksByCondition.getTotal();
            logger.info("MyPendedInfos total:{}", total);
            theRemainingAmount = total - allApprovedTaskInfo.size();
            currentPage++;
        }
        return allApprovedTaskInfo;
    }


    @Override
    public PageRows<ApprovingNodeInfo> getApprovingTasksByCondition(ApprovingQueryParam approvingQueryParam) {
        if (StringUtils.isBlank(approvingQueryParam.getBusinessId())) {
            approvingQueryParam.setBusinessId(null);
        }
        if (StringUtils.isBlank(approvingQueryParam.getFlowInstanceId())) {
            approvingQueryParam.setFlowInstanceId(null);
        }
        return this.invokeApprovalService(URL_ADVANCED_QUERY_TASKS, HttpMethodEnum.POST, approvingQueryParam,
                new TypeReference<ServiceData<PageRows<ApprovingNodeInfo>>>() {});
    }

    @Override
    public PageRows<ApprovedNodeInfo> getApprovedTasksByCondition(ApprovingQueryParam approvingQueryParam) {
        if (StringUtils.isBlank(approvingQueryParam.getBusinessId())) {
            approvingQueryParam.setBusinessId(null);
        }
        if (StringUtils.isBlank(approvingQueryParam.getFlowInstanceId())) {
            approvingQueryParam.setFlowInstanceId(null);
        }
        return this.invokeApprovalService(URL_APPROVED_ADVANCED_QUERY_TASKS, HttpMethodEnum.POST, approvingQueryParam,
                new TypeReference<ServiceData<PageRows<ApprovedNodeInfo>>>() {});
    }

    @Override
    public List<ApprovalRecordsDTO> getApprovalRecords(FlowParameter flowParameter) throws Exception {
        List<ApprovalRecordsDTO> records = invokeApprovalService(URL_FLOW_APPROVAL_RECORDS, HttpMethodEnum.GET, flowParameter,
                new TypeReference<ServiceData<List<ApprovalRecordsDTO>>>() {
                });
        if (CollectionUtils.isEmpty(records)) {
            return records;
        }
        ApprovedTaskUtils.order(records);
        return records;
    }

    @Override
    public List<FlowNodeApproverDTO> getFlowInstanceaPnorama(String flowInstanceId) {
        Map<String, Object> params = new HashMap<>();
        params.put("flowInstanceId", flowInstanceId);
        return invokeApprovalService(buildParamUrl(URL_FLOW_INSTANCE_PANORAMA, params), HttpMethodEnum.GET, null,
                new TypeReference<ServiceData<List<FlowNodeApproverDTO>>>() {});
    }

    /**
     * 调用审批服务
     * @param url
     * @param methodType
     * @param params
     * @param resultType
     * @param <T>
     * @return
     */
    private <T, R> R invokeApprovalService(String url, HttpMethodEnum methodType, T params,
                                        TypeReference<ServiceData<R>> resultType) {
        try {
            return MicroServiceInvokeUtil.invokeApprovalService(url, methodType, params, resultType);
        } catch (RemoteServiceException e) {
            throw new ApprovalOperationException("[" + e.getErrorMsg() + "]:" + e.getBody());
        }
    }

    /**
     * 构建分页参数
     * @param param
     * @return
     */
    private Map<String, Object> buildPageParams(ApprovalActiveTaskParamsDTO param) {
        Map<String, Object> pageParams = new HashMap<>();
        Integer pageNo = param.getPageNo();
        Integer pageSize = param.getPageSize();
        if (null != pageNo && pageNo > 0) {
            pageParams.put("pageNo", pageNo);
        }
        if (null != pageSize && pageSize > 0) {
            pageParams.put("pageSize", pageSize);
        }
        return pageParams;
    }

    /**
     * 构建参数url
     * @param prefixUrl
     * @param params
     * @return
     */
    private String buildParamUrl(String prefixUrl, Map<String, Object> params) {
        StringBuilder sb = new StringBuilder(prefixUrl);
        if (null != params && !params.isEmpty()) {
            boolean notFirst = false;
            sb.append(URL_CONNECTION);
            for (Map.Entry<String, Object> param : params.entrySet()) {
                if (notFirst) {
                    sb.append(URL_PARAM_CONNECTION);
                }
                sb.append(param.getKey()).append(EQUAL).append(param.getValue());
                notFirst = true;
            }
        }

        return sb.toString();
    }
}
