package com.zte.mcrm.channel.controller.prm;

import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import com.zte.mcrm.adapter.authorization.service.RoleService;
import com.zte.mcrm.adapter.clouddisk.util.ServiceDataUtil;
import com.zte.mcrm.channel.constant.SourceOfOpportunityEnum;
import com.zte.mcrm.channel.model.dto.OpportunityInfoDTO;
import com.zte.mcrm.channel.model.entity.Opportunity;
import com.zte.mcrm.channel.model.mapper.OpportunityInfoVOMapper;
import com.zte.mcrm.channel.model.vo.OpportunityAddVO;
import com.zte.mcrm.channel.model.vo.OpportunityDetailVO;
import com.zte.mcrm.channel.model.vo.OpportunityInfoVO;
import com.zte.mcrm.channel.model.vo.OpportunityPartInfoVO;
import com.zte.mcrm.channel.service.channel.IOpportunityInfoService;
import com.zte.mcrm.channel.service.channel.IOpportunityService;
import com.zte.mcrm.channel.service.prm.IPrmOpportunityService;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.exception.BusinessRuntimeException;
import com.zte.mcrm.common.framework.exception.PrmRetCode;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.mcrm.common.util.ValidationGroups;
import com.zte.springbootframe.util.local.LocalMessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.util.Assert;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;


/**
 * 新建商机相关 MVC 控制类
 * <AUTHOR>
 * @date 2021/09/15
 */
@Api(tags = "新建商机相关API")
@RestController
@RequestMapping("/prm/newOpportunity")
public class PrmOpportunityAddController {
    /** 日志对象 */
    private static final Logger log = LoggerFactory.getLogger(PrmOpportunityAddController.class);

    /** 服务对象，SPRING自动装配 */
    @Autowired
    IOpportunityService opportunityServiceImpl ;
    @Autowired
    RoleService roleService;
    @Autowired
    IOpportunityInfoService opportunityInfoService;
    @Autowired
    MessageSource messageSource;

    @ApiOperation("prm侧-提交商机")
    @PostMapping(value = "/submit")
    public ServiceData<OpportunityInfoVO> submitOpportunityInfo(@Validated(ValidationGroups.Submit.class) @RequestBody OpportunityInfoVO prmOpportunityInfo,
                                                                BindingResult bindingResult) throws Exception {
        // 数据校验
        if (bindingResult != null && bindingResult.hasErrors()) {
            throw new com.zte.springbootframe.common.exception.ValidationException(bindingResult);
        }
        ServiceData<OpportunityInfoVO> sd = new ServiceData<>();
        OpportunityAddVO opportunityAddVO = prmOpportunityInfo.getOpportunity();
        Assert.notNull(opportunityAddVO, LocalMessageUtils.getMessage("opportunityNull"));
        if(!CommonConst.Y.equals(opportunityAddVO.getIsNewBusiness()) && !CommonConst.N.equals(opportunityAddVO.getIsNewBusiness())) {
            throw new BusiException(RetCode.BUSINESSERROR_CODE,"sopty.save.check.isNewBusiness");
        }
        opportunityAddVO.setDataSource(SourceOfOpportunityEnum.ZTE_SELF_BUILT.getValue());
        OpportunityInfoDTO opportunityInfo = OpportunityInfoVOMapper.transOpportunityInfoVOToOpportunityInfoDTO(prmOpportunityInfo);
        OpportunityInfoDTO opportunityInfoResult = opportunityInfoService.submitPrmOpportunity(opportunityInfo);
        OpportunityInfoVO result = OpportunityInfoVOMapper.transOpportunityInfoDTOToOpportunityInfoVO(opportunityInfoResult);
        sd.setBo(result);
        return sd;
    }


    @ApiOperation("prm侧-暂存商机")
    @PostMapping(value = "/storage")
    public ServiceData<OpportunityInfoVO> storageOpportunityInfo(@Validated(ValidationGroups.Storage.class) @RequestBody OpportunityInfoVO prmOpportunityInfo,
                                                                 BindingResult bindingResult) throws Exception {
        // 数据校验
        if (bindingResult != null && bindingResult.hasErrors()) {
            throw new com.zte.springbootframe.common.exception.ValidationException(bindingResult);
        }
        ServiceData<OpportunityInfoVO> sd = new ServiceData<>();
        OpportunityAddVO opportunityAddVO = prmOpportunityInfo.getOpportunity();
        Assert.notNull(opportunityAddVO, LocalMessageUtils.getMessage("opportunityNull"));
        if(!CommonConst.Y.equals(opportunityAddVO.getIsNewBusiness()) && !CommonConst.N.equals(opportunityAddVO.getIsNewBusiness())) {
            throw new BusiException(RetCode.BUSINESSERROR_CODE,"sopty.save.check.isNewBusiness");
        }
        opportunityAddVO.setDataSource(SourceOfOpportunityEnum.ZTE_SELF_BUILT.getValue());
        OpportunityInfoDTO opportunityInfo = OpportunityInfoVOMapper.transOpportunityInfoVOToOpportunityInfoDTO(prmOpportunityInfo);
        OpportunityInfoDTO opportunityInfoResult = opportunityInfoService.storageOpportunityInfo(opportunityInfo);
        OpportunityInfoVO result = OpportunityInfoVOMapper.transOpportunityInfoDTOToOpportunityInfoVO(opportunityInfoResult);
        sd.setBo(result);
        return sd;
    }


    @Autowired
    private IPrmOpportunityService prmOpportunityService;

    @ApiOperation("prm侧-勘误接口(修改最终用户所在地和)")
    @PostMapping(value = "/errata/oppty")
    public ServiceData<Boolean> updateOpportunityPartInfo(@RequestBody OpportunityPartInfoVO vo) throws Exception {
        return ServiceResultUtil.success(prmOpportunityService.errataOppty(vo));
    }

    @ApiOperation("删除PRM商机草稿")
    @DeleteMapping(value="/delete/{rowId}")
    public ServiceData<Boolean> deletePrmOpportunity(@PathVariable("rowId") String rowId) throws Exception{
        Opportunity opportunity = opportunityServiceImpl.get(rowId);
        if (Objects.nonNull(opportunity) && opportunity.getCreatedBy().equals(CommonUtils.getEmpNo())){
            opportunityServiceImpl.softDeleteDraft(rowId);
        }else {
            throw new BusinessRuntimeException(PrmRetCode.PERMISSIONDENIED_CODE, PrmRetCode.PERMISSIONDENIED_MSGID);
        }
        //返回统一的服务端数据
        return ServiceResultUtil.success(true);
    }

    @ApiOperation("修改明细数据(当前对于细分市场使用)")
    @PostMapping("/updateDetail")
    public ServiceData<Boolean> updateDetail(@Validated(ValidationGroups.OptyAttribute.class) @RequestBody OpportunityDetailVO opportunityDetail,
                                             BindingResult bindingResult) throws Exception {
        // 数据校验
        if (bindingResult != null && bindingResult.hasErrors()) {
            throw new com.zte.springbootframe.common.exception.ValidationException(bindingResult);
        }
        return ServiceDataUtil.success(opportunityInfoService.updateOptyAttribute(opportunityDetail));
    }

    @ApiOperation("判断修改操作权限")
    @GetMapping("/verifyOperatorAuth/{rowId}")
    public ServiceData<Boolean> verifyOperatorAuth(@PathVariable("rowId") String rowId) throws Exception {
        ServiceData<Boolean> result = new ServiceData<>();
        result.setBo(opportunityInfoService.verifyOperatorAuth(rowId));
        return result;
    }
}
