package com.zte.mcrm.channel.constant;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

public class OpportunityConstant {

    public static final int FIRST_SHARDING = 0;

    public static final String CUSTOMER_COUNTRY_ID_CHINA = "0001";

    public static final Integer CERTIFICATION_INFO_MAX_LENGTH = 500;

    public static final String ELLIPSIS = "...";

    public static final String OPTY_SOURCE = "opty_source";


    /** ACTIVE-待处理*/
    public static final String APPROVAL_TASK_ACTIVE="ACTIVE";

    /**  REASSIGN-已转交*/
    public static final String APPROVAL_TASK_REASSIGN="REASSIGN";

    /** COMPLETED-已处理*/
    public static final String APPROVAL_TASK_COMPLETED="COMPLETED";
    /** 审批通过*/
    public static final String APPROVAL_Y="Y";
    /** 审批通过*/
    public static final String APPROVAL_Y_NAME="通过";

    /** 审批通过*/
    public static final String APPROVAL_N="N";
    /** 审批不通过*/
    public static final String APPROVAL_N_NAME="不通过";
    /** 审批节点*/
    public static final String APPROVAL_NODE="审批节点";
    /** 直销*/
    public static final String ZX = "ZX";


    /**
     * 审批完成的时间
     */
    public static final String SUCCESS_DATE = "successDate";

    /**
     * 提交审批的时间（从审批节点开始）
     */
    public static final String SUBMIT_DATE = "submitDate";


    /** PRM*/
    public static final String PRM = "PRM";

    /** iChannel*/
    public static final String ICHANNEL = "iChannel";

    /** 业务类型-新建商机 */
    public static final String NEW_OPPORTUNITY = "newOpportunity";

    /** 业务类型-商机转立项*/
    public static final String TRANSFER_PROJECT = "transferProject";

    /** 业务类型-公司主产品*/
    public static final String PDM_PROD = "pdm_prod";

    /** 商机月报业务类型-提交月报 */
    public static final String MONTH_REPORT = "monthReport";

    /** 商机月报业务类型-备份第一次提交月报前的商机内容 */
    public static final String MONTH_REPORT_BAK = "monthReportBackUp";

    /** 业务类型-更新状态 */
    public static final String MONTH_REPORT_UPDATE_STATE = "monthReportUpdateState";

    /** 当月月报状态-已提交/草稿 - 已提交 */
    public static final String MONTH_REPORT_STATUS_SUBMITTED = "submitted";

    /** 新建商机、填写月报时上传附件的uploadType */
    public static final String UPLOAD_TYPE_NEW_OPPORTUNITIES = "channel_new_opportunities";

    /** 商机状态*/
    public static final String OPPORTUNITY_STATUS = "opportunityStatus";


    /** PRM商机状态*/
    public static final String PRM_OPPORTUNITY_STATUS = "prmOpportunityStatus";

    /** 商机来源*/
    public static final String SOURCE_OF_OPPORTUNITY = "sourceOfOpportunity";

    /** 最终用户类型 */
    public static final String END_USER_TYPE_DICTIONARY = "endUserType";

    /** 最终用户的最终用途 */
    public static final String END_USE_OF_END_USER_DICTIONARY = "endUseOfEndUser";

    /** 授权状态*/
    public static final String AUTHORIZATION_STATUS = "authorizationStatus";
    /** 输单原因*/
    public static final String TICKET_WIN_REASON = "ticketWinReason";
    /** 丢单*/
    public static final String CLOSED_REASON = "ClosedReason";
    /** 取消原因*/
    public static final String CANCEL_REASON = "cancelReason";

    /** 月报状态*/
    public static final String MONTHLY_REPORT_STATUS = "monthlyReportStatus";

    /** 招标类型 */
    public static final String TYPE_FOR_TENDER_TYPE = "typeForTenderType";

    /** 商机当前状态 */
    public static final String OPPORTUNITY_CURRENT_STATUS = "opportunityCurrentStatus";

    /** 赢率 */
    public static final String WIN_RATE_TYPE = "winRateType";
    /**
     * prm新赢率
     */
    public static final String PRM_WIN_RATE_TYPE = "prmWinRateType";
    /** 最终用途 */
    public static final String ZTE_FINAL_USAGE = "ZTE_FINAL_USAGE";
    /** 销售模式 */
    public static final String ZTE_OPTY_SALES = "ZTE_OPTY_SALES";
    /** 商机产品体系内部分类 */
    public static final String ZTE_OPTY_INNER_CLASS = "ZTE_OPTY_INNER_CLASS";

    /** 商机报备发起审批-flowcode*/
    public static final String  OPPTY_FLOW_CODE= "ZXICCP-iSales300-create-opportunity";

    /**
     * 业务范围
     */
    public static final String ZTE_OPPTY_BUS_TYPE = "ZTE_OPPTY_BUS_TYPE";

    /** 当前阶段 */
    public static final String CURRENT_PHASES_TYPE = "currentPhasesType";

    /** 通过/仲裁原因 */
    public static final String FAILURE_REASON= "failureReason";

    /** 无 */
    public static final String NO_THING = "无";

    /** nothing */
    public static final String NO_THING_ENG = "no";

    /** 全部商机类型编码 */
    public static final List<String> PRM_OPPORTUNITY_TYPES = ImmutableList.of(
            PRM_OPPORTUNITY_STATUS,
            SOURCE_OF_OPPORTUNITY,
            MONTHLY_REPORT_STATUS,
            AUTHORIZATION_STATUS,
            TYPE_FOR_TENDER_TYPE,
            WIN_RATE_TYPE,
            CURRENT_PHASES_TYPE);

    /** 商机类型编码*/
    public static final List<String> OPPORTUNITY_TYPES = Lists.newArrayList(
            OPPORTUNITY_STATUS,
            SOURCE_OF_OPPORTUNITY,
            AUTHORIZATION_STATUS,
            MONTHLY_REPORT_STATUS,
            TYPE_FOR_TENDER_TYPE,
            WIN_RATE_TYPE,
            CURRENT_PHASES_TYPE);

    public static final String DATE_FORMAT_YYYYMMDD = "yyyyMMdd";

    public static final String DATE_FORMAT_YYYYMM = "yyyyMM";

    public static final String DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd HH:mm:ss";

    public static final String DATEFORMAT_YYYYMMDD = "yyyy-MM-dd";

    public static final String KEY_WORD = "keyWord";

    public static final String KEY_WORD_FOR_QUERY = "keyWordForQuery";

    public static final String STATUS_CD = "statusCd";

    public static final String MIN_CREATE_DATE = "minCreateDate";

    public static final String MAX_CREATE_DATE = "maxCreateDate";

    public static final String TENDER_TYPE_CODE = "tenderTypeCode";

    public static final String PROJECT_PHASES_CODE = "projectPhasesCode";

    public static final String DEPT_NO = "deptNo";

    public static final String FINAL_CUSTOMER_CHILD_TRADE = "finalCustomerChildTrade";

    public static final String DATA_SOURCE = "dataSource";

    public static final Set<String> PENGDING_ADVANCE_PARAM_SET = ImmutableSet.of(
            KEY_WORD,
            STATUS_CD);

    public static final Set<String> PRM_OPPORTUNITY_ADVANCE_PARAM_SET = ImmutableSet.of(
            KEY_WORD_FOR_QUERY,
            STATUS_CD,
            TENDER_TYPE_CODE,
            PROJECT_PHASES_CODE,
            DEPT_NO,
            FINAL_CUSTOMER_CHILD_TRADE,
            DATA_SOURCE);

    /** 商机报备编号前缀 */
    public static final String OPPORTUNITY_PREFIX = "XS";
    /** 商机报备编号流水号长度 */
    public static final String OPPORTUNITY_DIGIT = "3";

    /** 是受限制主体 */
    public static final String IS_RESTRICTED = "3";

    /** 已有报备 */
    public static final String HAS_REPORTED = "1";

    /** 校验商机编号的正则表达式 */
    public static final String OPTY_CD_REGEX = "^XS(20\\d{2})(\\d{2})(\\d{2})(\\d{3})";

    public static final String URL_CODE_REGEX = "([^\\u0000-\\u00ff])";

    /** 商机月报归属期格式：yyyyMM */
    public static final String REPORT_MONTH_REGEX = "^\\d{4}((0([1-9]))|(1(0|1|2)))";

    /** 非数字 */
    public static final String REG_CN = "\\d";
    /** 数字 */
    public static final String REG_NO = "[^0-9]";

    /** 当前处理人工号正则化表达式 */
    public static final String EMP_NO_REGEX = "^\\d{1,}$";

    /** 提交审批流程时关键字分隔符 */
    public static final String KEY_WORD_SPLIT = "#";

    /**查询关键字 */
    public static final String KEYWORD = "keyword";

    public static final String SHORTNO_AND_CRMCUSTOMERCODE_EMPTY = "渠道商客户编码，与中兴业务经理工号必填一个";

    /** 客户id */
    public static final String CUSTOMER_ID = "customerId";

    public static final String CRM_CUSTOMER_CODE = "crmCustomerCode";

    /**角色编码-公司账号 */
    public static final String COMPANY_ACCOUNT = "company_account";

    /**角色编码-业务经理 */
    public static final String SERVICE_MANAGER = "600010";

    /**角色编码-商务经理 */
    public static final String BUSINESS_MANAGER = "60006";

    /**商机来源个数 */
    public static final int SOURCE_NUM = 2;

    /**月报状态个数 */
    public static final int REPORT_STATUS_NUM = 2;

    /**商机来源类型 */
    public static final String SOURCE_TYPE = "sourceType";

    /**商机来源类型 */
    public static final String REPORT_STATUS_TYPE = "reportStatusType";
    /**招标项目 */
    public static final String TENDER_PROJECT = "tenderProject";

    /**30天每月 */
    public static final int DAY_PRE_MONTH = 30;

    /** 数字3 */
    public static final int THREE = 3;

    /** 数字6 */
    public static final int SIX = 6;

    /** 数字1 */
    public static final int ONE = 1;

    /** 数字0 */
    public static final int ZERO = 0;

    /**
     * 新商机模块编码：OpportunityManagement
     */
    public static final String OPPORTUNITY_MANAGEMENT = "OpportunityManagement";

    /** 中兴组织_五层(树状)约束编码*/
    public static final String ORG_LEVEL_5 = "ZTE_ORG_5_LEVEL";

    /** 商机子行业约束编码 */
    public static final String SUB_INDUSTRY_CONSTRAINT = "SubIndustryConstraint";

    /** 可快速立项的最大金额-300万 */
    public static final BigDecimal MAX_FAST_PROJECT_TRANSFER_MONEY = BigDecimal.valueOf(3000000);

    /** 仲裁流程判断的预计签单金额节点-300万 */
    public static final BigDecimal ARBITRATION_LEVEL_JUDGE_MONEY = BigDecimal.valueOf(3000000);

    /** 仲裁流程-1 */
    public static final String ARBITRATION_PROCESS_ONE = "1";

    /** 仲裁流程-2 */
    public static final String ARBITRATION_PROCESS_TWO = "2";

    /** 新建商机-审批中心流程编码 */
    public static final String NEW_OPPORTUNITY_FLOW_CODE = "ZXICCP-iSales300-create-opportunity";
    /**  新建商机-审批中心AppCode */
    public static final String NEW_OPPORTUNITY_APP_CODE = "ZXICCP-iSales300";

    /**是、否*/
    public static final String YES = "是";
    public static final String NO = "否";
    public static final String INVESTIGATION_DATE_CN = "处罚日期";
    public static final String REASON_CN = "原因";

    /** 数字500 */
    public static final int NUMBER_FIVE_HUNDRED = 500;

    /** 最终客户状态：生效 */
    public static final String ACTIVE_STATUS_EFFECTIVE = "生效";
    public static final String BLACK_LIST_STATUS_EFFECTIVE = "effective";

    public static final String CUSTOMER_DRAFT_CREATED = "创建客户草稿节点";


    public static final String SUCCESS_MSG = "Success";

    public static final String ERROR_MSG = "repeatFlowInstanceId";

    /** 最大激活次数*/
    public static final int MAX_ACTIVE_NUM = 2;

    /** 系统鉴权token有效期 */
    public static final long MAX_TIMESTAMP_GAP = 60L;

    public static final String SYSTEM = "System";
    public static final String SYSTEM_SERVICE = "System Service";
    public static final String SYSTEM_ENGINEE_SERVICE = "System Enginee Service";
    public static final String SYSTEM_EXCLUDE_ENGINEE = "System Exclude Enginee";
    public static final String SERVICE = "Service";
    /** 项目一级分类(默认为当期类项目)*/
    public static final String PROJECT_FIRST_CATEGORY = "70";
    public static final String IS_EXPORT = "isExport";
    public static final String IS_FROM_PRM = "isFromPrm";
    public static final String OLD_STATUS_CD = "oldStatusCd";
    public static final String IS_PERMISSION = "isPermission";
    public static final String MANAGE_AUTH_FLAG = "manageAuthFlag";
    public static final String PROD_NAME = "prodName";
    public static final String PROD_ID = "prodId";
    public static final long EXPORT_MAX_ROW = 10000L;

    /** 商机审批转交通知邮件模板**/
    public static final String OPPORTUNITY_EMAIL_FOR_CREATE_CUSTOMER = "OPPORTUNITY_EMAIL_FOR_CREATE_CUSTOMER";

    /** 商机撤销邮件模板 */
    public static final String OPPORTUNITY_EMAIL_FOR_CANCEL_OPPTY = "OPPORTUNITY_EMAIL_FOR_CANCEL_OPPTY";

    /** 商机受限制主体提醒邮件-为是*/
    public static  final String  OPPORTUNITY_EMAIL_FOR_RESTRICTED_ENTITY_YES= "OPPORTUNITY_EMAIL_FOR_RESTRICTED_ENTITY_YES";

    /** 商机受限制主体提醒邮件-为空或者待确认*/
    public static  final String  OPPORTUNITY_EMAIL_FOR_RESTRICTED_ENTITY_NULL_OR_PENDING= "OPPORTUNITY_EMAIL_FOR_RESTRICTED_ENTITY_NULL_OR_PENDING";

    /** 商机受限制主体提醒邮件-禁运国*/
    public static  final String  OPPORTUNITY_EMAIL_FOR_RESTRICTED_ENTITY_EMBARGO= "OPPORTUNITY_EMAIL_FOR_RESTRICTED_ENTITY_EMBARGO";

    /** 商机填写月报提醒邮件 */
    public static final String OPPORTUNITY_EMAIL_FOR_MONTH_REPORT_REMINDER = "OPPORTUNITY_EMAIL_FOR_MONTH_REPORT_REMINDER";

    /** 商机失效提醒邮件-内部 */
    public static final String OPPORTUNITY_EMAIL_FOR_MONTHLY_INVALID_REMINDER_INTERNAL = "OPPORTUNITY_EMAIL_FOR_MONTHLY_INVALID_REMINDER_INTERNAL";

    /** 商机失效提醒邮件-外部 */
    public static final String OPPORTUNITY_EMAIL_FOR_MONTHLY_INVALID_REMINDER_EXTERNAL = "OPPORTUNITY_EMAIL_FOR_MONTHLY_INVALID_REMINDER_EXTERNAL";

    /** 商机报备通过邮件-内部 */
    public static final String OPPORTUNITY_REPORT_SUCCESS_MAIL_INTERNAL = "OPPORTUNITY_REPORT_SUCCESS_MAIL_INTERNAL";

    /** 商机报备通过邮件-外部 */
    public static final String OPPORTUNITY_REPORT_SUCCESS_MAIL_EXTERNAL = "OPPORTUNITY_REPORT_SUCCESS_MAIL_EXTERNAL";

    /** 商机报备不通过邮件-内部 */
    public static final String OPPORTUNITY_REPORT_FAIL_MAIL_INTERNAL = "OPPORTUNITY_REPORT_FAIL_MAIL_INTERNAL";

    /** 商机报备不通过邮件-外部 */
    public static final String OPPORTUNITY_REPORT_FAIL_MAIL_EXTERNAL = "OPPORTUNITY_REPORT_FAIL_MAIL_EXTERNAL";


    /** notification知会邮件**/
    public static final String TEMPLATE_TYPE_NOTIFICATION = "notification";

    public static final String RESET_PROCESS_PARAMETERS_LOG_TIP = "重设流程参数";
    public static final String MODIFY_BUSINESS_MANAGER_LOG_TIP = "修改中兴业务经理";
    public static final String ARBITRATION_APPROVAL_PARAMS = "仲裁";
    public static final String SEND_EMAIL_LOG_TIP = "发送邮件";

    public static final String SIMILAR_OPPTY_CN = "类似商机";
    public static final String OPPTY_CODE_CN ="商机编号";
    public static final String OPPTY_NAME_CN ="商机名称";
    public static final String CUSTOMER_NAME_CN ="渠道商名称";
    public static final String LAST_ACC_NAME_CN ="最终用户名称";
    public static final String DEPT_NAME_CN ="投资方所在地";
    public static final String OPTTY_STATUS_CN ="商机状态";

    public static final String PROJECT_APPROVAL_PASSED = "立项审批通过";




    /** 第一个索引，数字0 */
    public static final int FIRST_INDEX = 0;
    /** kafka消息topic*/
    public static final String APPROVAL_CENTER_TOPIC ="zte-iss-approval-nj";
    /** kafka消息key*/
    public static final String APPROVAL_CENTER_KEY ="ZXICCP-iSales300-taskCompleted";

    /** kafka：变更渠道商*/
    public static final String KAFKA_UPDATE_DEALER_KEY = "zte-bmt-pcs-api_updateDealer_key";

    public enum MailClickEnum {
        /** 点击查看详情 */
        CLICK_LOOK_DETAIL("B","点击查看详情","Click to view details"),
        /** 点击前去处理 */
        CLICK_REVIEW("A","点击前去处理","Click to process");

        private final String code;
        private final String msgCn;
        private final String msgEn;

        public String getCode() {
            return code;
        }

        public String getMsgCn() {
            return msgCn;
        }

        public String getMsgEn() {
            return msgEn;
        }

        MailClickEnum(String code, String msgCn, String msgEn) {
            this.code = code;
            this.msgCn = msgCn;
            this.msgEn = msgEn;
        }

        public static MailClickEnum valueOfCode(String code){
            for (MailClickEnum mailClickEnum : MailClickEnum.values()){
                if(mailClickEnum.code.equals(code)){
                    return mailClickEnum;
                }
            }
            return CLICK_LOOK_DETAIL;
        }
    }

    public static final String SEND_MAIL_ERROR = "send mail error";

    /** 审批页面url*/
    public static final String APPROVAL_REPORT_URL = "/projectsTransactions/projectManagement/approvalReport";
    /** ichannel侧商机详情页面url*/
    public static final String ICHANNEL_APPROVAL_DETAIL_URL = "/projectsTransactions/projectManagement/reportDetail?rowId={0}";
    /** prm侧商机详情页面url*/
    public static final String PRM_APPROVAL_DETAIL_URL = "/projectsTransactions/projectManagement/approvalDetail?rowId={0}&from={1}&pageFrom=query";
    /** 售前*/
    public static final String PRE_SALES = "pre_sales";
    /** 售后*/
    public static final String AFTER_SALES = "after_sales";
    // flowCode配置过滤
    public static final List<String> FLOWCODE_LIST = Lists.newArrayList("ZXICCP-iSales300-create-opportunity");

    /**
     * 项目的角色项目经理，code为“110”
     */
    public static final String PROJECT_MANAGER = "110";
    /**
     * 项目的指委会主任，code为“300”
     */
    public static final String DIRECTOR_OF_PSC = "300";
    /**
     * 融资经理
     */
    public static final String FINANCING_MANAGER_OF_PSC = "10001";
    /**
     * 模板类型-消息
     */
    public static final Byte MSG_NOTIFY_TYPE = 1;
    /**
     * 模板类型-邮件
     */
    public static final Byte MSG_MAIL_TYPE = 2;
    /**
     * 审批中
     */
    public static final String APPROVING = "审批中";

    public static final String CUSTOMER_STATUS_ABNORMAL = "客户状态判断异常";
    public static final String CREATE_CUSTOMER_FAILURE = "创建最终用户失败";

    public static final String FINAL_USAGE_CODE_TYPE = "finalUsageForCompliance";

    /**
     * 商机id
     */
    public static final String OPPTY_ID = "opptyId";
    /**
     * 业务类型
     */
    public static final String BUSINESS_TYPE = "businessType";
    /**
     * 大产品线
     */
    public static final String PROD_LV2_NAME = "prodLv2Name";
    /**
     * 关联政企产品的ROW_ID
     */
    public static final String PAR_PROD_ID = "parProdId";
    
    public static final String UPDATE_STRING = "修改";

    /**
     * 责任人
     */
    public static final String EMPLOYEE_OWNER = "1";

    /**
     * 项目指委会主任
     */
    public static final String EMPLOYEE_DESC = "2";

    /**
     * 创建人
     */
    public static final String EMPLOYEE_CREATE = "3";
}
