package com.zte.mcrm.channel.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-10-08
 * @Description 流程表, 主要存储的是流程实例的信息
 */
@Data
public class InsFlow {
    /**
     * '实例化ID';
     */
    @ExcelProperty(value = "ins_flow_id",index = 0)
    private String insFlowId;
    /**
     * '审批关联id';
     */
    @ExcelProperty(value = "business_id",index = 1)
    private String businessId;
    /**
     * '流程编码';
     */
    @ExcelProperty(value = "flow_code",index = 2)
    private String flowCode = OpportunityConstant.NEW_OPPORTUNITY_FLOW_CODE;
    /**
     * 流程实例id';
     */
    @ExcelProperty(value = "process_instance_id",index = 3)
    private String processInstanceId;
    /**
     * '参数变量类型';
     */
    @ExcelProperty(value = "busniess_param",index = 4)
    private String busniessParam;
    /**
     * 'ACTIVE' ; '流程状态码：ACTIVE 激活状态；REVOKE 已撤回；COMPLETED 已完成';
     */
    @ExcelProperty(value = "status_code",index = 5)
    private String statusCode;
    /**
     * '10001' ; '租户';
     */
    @ExcelProperty(value = "tenant_id",index = 6)
    private String tenantId = "10001";
    /**
     * '应用编码';
     */
    @ExcelProperty(value = "app_code",index = 7)
    private String appCode = OpportunityConstant.NEW_OPPORTUNITY_APP_CODE;
    /**
     * '创建人'，工号或者accountid;
     */
    @ExcelProperty(value = "created_by",index = 8)
    private String createdBy;
    /**
     * 创建时间';
     */
    @ExcelProperty(value = "create_date",index = 9)
    private Date createDate;
    /**
     * '更新人'，工号或者accountid;
     */
    @ExcelProperty(value = "last_update_by",index = 10)
    private String lastUpdateBy;
    /**
     * '更新时间';
     */
    @ExcelProperty(value = "last_update_date",index = 11)
    private Date lastUpdateDate;
    /**
     * 'Y有效，N无效'
     */
    @ExcelProperty(value = "enable_flag",index = 12)
    private String enableFlag;
}
