package com.zte.mcrm.common.upload.enums;


import com.zte.mcrm.common.upload.constants.UploadConst;

/**
 * 文件类型枚举类
 * <AUTHOR>
 * @date 2021/8/31
 */
public enum FileTypeEnum {
    /**
     * 01文本类型
     */
    TXT("01"),
    PDF("01"),
    CSV("01"),
    XLSX("01"),
    XLS("01"),
    ET("01"),
    XLSM("01"),
    PPT("01"),
    PPTX("01"),
    POT("01"),
    POTX("01"),
    POTM("01"),
    PPS("01"),
    PPSX("01"),
    DPS("01"),
    DOC("01"),
    DOCX("01"),
    WPS("01"),
    DOCM("01"),
    /**
     * 02图片类型
     */
    JPG("02"),
    JPEG("02"),
    GIF("02"),
    PNG("02"),
    BMP("02"),
    TIF("02"),
    TIFF("02"),
    /**
     * 03音频类型
     */
    MP3("03"),
    WMA("03"),
    /**
     * 04视频类型
     */
    MP4("04"),
    FLV("04");

    private final String fileType;

    FileTypeEnum(String fileType) {
        this.fileType = fileType;
    }

    @Override
    public String toString() {
        return fileType;
    }

    public static String getEnumNameForValues(String name) {
        // 获取枚举的集合
        FileTypeEnum[] values = FileTypeEnum.values();
        // 自定义枚举name变量
        String enumName = null;
        // 自定义枚举的value变量
        String enumValue = null;
        for(FileTypeEnum eachValue : values) {
            // 枚举的value值
            enumValue = eachValue.toString();
            // 枚举的name值
            enumName = eachValue.name();
            // 判断传过来的参数是否与name值相等
            if (name.equals(enumName)) {
                // 若相等，则返回枚举对应的value值
                return enumValue;
            }
        }
        return UploadConst.FILE_TYPE_5;
    }
}
