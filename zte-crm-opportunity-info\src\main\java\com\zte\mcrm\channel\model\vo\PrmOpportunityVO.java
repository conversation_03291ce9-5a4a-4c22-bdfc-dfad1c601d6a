package com.zte.mcrm.channel.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class PrmOpportunityVO {
    @ApiModelProperty(value = "主键")
    private String rowId;

    @ApiModelProperty(value = "商机编码")
    private String optyCd;

    @ApiModelProperty(value = "商机名称")
    private String opportunityName;

    @ApiModelProperty(value = "投资方所在地（商机所属部门）编码")
    private String deptNo;

    @ApiModelProperty(value = "投资方所在地（商机所属部门）名称")
    private String deptName;

    @ApiModelProperty(value = "最终用户行业编码")
    private String finalCustomerTradeCode;

    @ApiModelProperty(value = "最终用户行业(父行业-子行业形式)")
    private String finalCustomerTradeName;

    @ApiModelProperty(value = "最终用户子行业编码")
    private String finalCustomerTradeChildCode;

    @ApiModelProperty(value = "产品Id")
    private String productIds;

    @ApiModelProperty(value = "产品名称")
    private String productNames;

    @ApiModelProperty(value = "最终用户是否受限制主体")
    private String finalCustomerRestrictionFlag;
    @ApiModelProperty(value = "渠道商是否受限制主体")
    private String agencyRestrictionFlag;

    @ApiModelProperty(value = "预计签单金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "当前阶段编码")
    private String projectPhasesCode;

    @ApiModelProperty(value = "当前阶段编码-老商机")
    private String oldProjectPhasesCode;

    @ApiModelProperty(value = "当前阶段")
    private String projectPhasesName;

    @ApiModelProperty(value = "招标类型编码")
    private String tendTypeCode;

    @ApiModelProperty(value = "招标类型编码-老商机")
    private String oldTendTypeCode;

    @ApiModelProperty(value = "招标类型名称")
    private String tendTypeName;

    @ApiModelProperty(value = "预计发标/议标日期")
    @JsonFormat(pattern ="yyyy-MM-dd", timezone = "GMT+8")
    private Date estimatedBiddingTime;

    @ApiModelProperty(value = "报备时间")
    @JsonFormat(pattern ="yyyy-MM-dd", timezone = "GMT+8")
    private Date created;

    @ApiModelProperty(value = "渠道商名称")
    private String channelBusiness;

    @ApiModelProperty(value = "商机来源编码")
    private String dataSource;

    @ApiModelProperty(value = "商机来源")
    private String dataSourceName;

    @ApiModelProperty(value = "业务经理姓名")
    private String businessManagerName;

    @ApiModelProperty(value = "业务经理id")
    private String businessManagerId;

    @ApiModelProperty(value = "当前处理人")
    private String currentProcessor;

    @ApiModelProperty(value = "商机创建人")
    private String createdBy;

    @ApiModelProperty(value = "商机状态编码")
    private String statusCd;

    @ApiModelProperty(value = "商机状态")
    private String statusName;

    @ApiModelProperty(value = "授权状态编码")
    private String statusAuth;

    @ApiModelProperty(value = "授权状态")
    private String statusAuthName;

    @ApiModelProperty(value = "授权次数")
    private Long numberOfAuthorizations;

    @ApiModelProperty(value = "流程实例ID")
    private String flowInstanceId;

    @ApiModelProperty(value = "操作按钮")
    private PrmOperationButtonVO operationButton;

    /**
     * 最终用户名称
     */
    @ApiModelProperty(value = "最终用户名称")
    private String finalCustomerName;

    @ApiModelProperty(value = "月报状态")
    private String reportStatus;

    @ApiModelProperty("商机当前状态")
    private String currentStatus;

    @ApiModelProperty(value = "最终客户状态")
    private Integer lastAccStatus;
    /**
     * 竞标截止日期
     */
    private Date biddingDeadline;
    /**
     * 授权编号/授权状态
     */
    private String authIdAndStatus;
    /**
     * 赢率编码
     */
    private String winRate;
    /**
     * 赢率
     */
    private String winRateName;
    /**
     * 招标方全称
     */
    private String bidProviderName;
    /**
     * 激活次数
     */
    private Integer activeCount;
    /**
     * 失效日期
     */
    private Date expiryDate;
    /**
     * 是否属于激活报备(Y/N)
     */
    private String fromActiveFlag;
    /**
     * 从哪个商机激活的(记录商机id)
     */
    private String fromActiveOpty;
    /**
     *最终用户客户Id
     */
    @ApiModelProperty(value = "最终用户客户Id")
    private String finalCustomerId;
    /**
     *最终用户客户编码
     */
    @ApiModelProperty(value = "最终用户客户编码")
    private String finalCustomerCode;
    /**
     *渠道商客户编码
     */
    @ApiModelProperty(value = "渠道商客户编码")
    private String crmCustomerCode;
}
