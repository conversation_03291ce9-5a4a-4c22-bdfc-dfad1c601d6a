package com.zte.mcrm.channel.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class PrmOpportunityPendingVO {

    @ApiModelProperty(value = "主键")
    private String rowId;

    @ApiModelProperty(value = "商机编码")
    private String optyCd;

    @ApiModelProperty(value = "商机名称")
    private String opportunityName;

    @ApiModelProperty(value = "最终用户名称")
    private String lastAccName;

    @ApiModelProperty(value = "投资方所在地（商机所属部门）编码")
    private String deptNo;

    @ApiModelProperty(value = "投资方所在地（商机所属部门）名称")
    private String deptName;

    @ApiModelProperty(value = "最终用户父行业编码")
    private String finalCustomerTradeCode;

    @ApiModelProperty(value = "最终用户行业(父行业-子行业形式)")
    private String finalCustomerTradeName;

    @ApiModelProperty(value = "最终用户子行业编码")
    private String finalCustomerTradeChildCode;

    @ApiModelProperty(value = "当前阶段编码")
    private String projectPhasesCode;

    @ApiModelProperty(value = "当前阶段名称")
    private String projectPhasesName;

    @ApiModelProperty(value = "最终用户联系人姓名")
    private String finalCustomerContactName;

    @ApiModelProperty(value = "商机来源编码")
    private String dataSource;

    @ApiModelProperty(value = "商机来源")
    private String dataSourceName;

    @ApiModelProperty(value = "报备时间")
    @JsonFormat(pattern ="yyyy-MM-dd", timezone = "GMT+8")
    private Date created;

    @ApiModelProperty(value = "商机创建人")
    private String createdBy;

    @ApiModelProperty(value = "商机状态编码")
    private String statusCd;

    @ApiModelProperty(value = "商机状态名称")
    private String statusName;
    
    @ApiModelProperty(value = "渠道商")
    private String channelBusiness;

    @ApiModelProperty(value = "任务ID 待审批任务的唯一标识，用于后续审批任务的处理")
    private String taskId;

    @ApiModelProperty(value = "流程实例ID")
    private String flowInstanceId;

    @ApiModelProperty(value = "节点名称")
    private String nodeName;

    /**
     * APPROVAL -- 审批节点
     * COUNTERSIGN -- 会签节点
     * */
    @ApiModelProperty(value = "节点类型")
    private String nodeType;
}
