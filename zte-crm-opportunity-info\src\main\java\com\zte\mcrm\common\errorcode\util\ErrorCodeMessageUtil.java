package com.zte.mcrm.common.errorcode.util;

import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;
import org.jetbrains.annotations.PropertyKey;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * @Author: <EMAIL>
 * @Date: 2022/04/24
 * @Description:
 */

@Component
@Slf4j
public class ErrorCodeMessageUtil {

    private static final ConcurrentMap<Locale, ConcurrentMap<String, String>> CACHE_TIP_LOCALETOERRORCODETOTIP = new ConcurrentHashMap<>();
    private static final ConcurrentMap<Locale, Map<String, String>> CACHE_ERROR_CODE_MSG = new ConcurrentHashMap<>();
    private static final String TIP_SEPARATOR = "---";
    private static MessageSource messageSource;
    public static final String ERRORCODEMESSAGES_BUNDLE = "messages";

    private ErrorCodeMessageUtil(@Autowired MessageSource messageSource) {
        ErrorCodeMessageUtil.messageSource = messageSource;
    }

    /**
     * 获取错误码对应的错误消息
     *
     * @param key
     * @param params
     * @return
     */
    public static String getErrorCodeMessage(@PropertyKey(resourceBundle = ERRORCODEMESSAGES_BUNDLE) String key, Object... params) {
        if (null == messageSource) {
            return key;
        }
        return ErrorCodeMessageUtil.messageSource.getMessage(key, params, key, LocaleContextHolder.getLocale());
    }

    /**
     * 获取错误码对应的界面提示信息
     *
     * @param key
     * @param args
     * @return
     */
    public static String getErrorCodeTip(@PropertyKey(resourceBundle = ERRORCODEMESSAGES_BUNDLE) String key, Object... args) {
        return getErrorCodeTipByErrorMsg(getErrorCodeMessage(key, args));
    }

    /**
     * 根据错误消息截取界面提示信息
     *
     * @param errorCodeMessage
     * @return
     */
    public static String getErrorCodeTipByErrorMsg(String errorCodeMessage) {
        return StringUtils.substringBefore(errorCodeMessage, TIP_SEPARATOR);
    }

    /**
     * 获取错误码对应的界面提示信息
     *
     * @param key
     * @param args
     * @return
     */
    public static String getErrorCodeTipByCache(@PropertyKey(resourceBundle = ERRORCODEMESSAGES_BUNDLE) String key, Object... args) {
        Locale locale = LocaleContextHolder.getLocale();
        ConcurrentMap<String, String> cacheMapKeyToTip = CACHE_TIP_LOCALETOERRORCODETOTIP.computeIfAbsent(locale,x->new ConcurrentHashMap<>());
        String originalTip = cacheMapKeyToTip.get(key);
        if (originalTip != null) {
            return MessageFormat.format(originalTip, args);
        }
        originalTip = StringUtils.substringBefore(getErrorCodeMessage(key), TIP_SEPARATOR);
        cacheMapKeyToTip.put(key, originalTip);
        return MessageFormat.format(originalTip, args);
    }

    /**
     * 获取所有错误码
     *
     * @return
     */
    public static Map<String, String> getAllErrorCodeMessage() {
        Locale locale = LocaleContextHolder.getLocale();
        Map<String, String> cacheCodeToMsg = CACHE_ERROR_CODE_MSG.get(locale);
        if (cacheCodeToMsg != null) {
            return cacheCodeToMsg;
        }
        cacheCodeToMsg = CACHE_ERROR_CODE_MSG.computeIfAbsent(locale, bn -> new TreeMap<>());
        ResourceBundle errorcodemessagesBundle = ResourceBundle.getBundle(ERRORCODEMESSAGES_BUNDLE, locale);
        convertResourceBundleToMap(errorcodemessagesBundle, cacheCodeToMsg);
        return cacheCodeToMsg;
    }

    /**
     * 将resourcebundle转换成map
     *
     * @param resource
     * @return
     */
    private static void convertResourceBundleToMap(ResourceBundle resource, Map<String, String> map) {
        Enumeration<String> keys = resource.getKeys();
        while (keys.hasMoreElements()) {
            String key = keys.nextElement();
            map.put(key, resource.getString(key));
        }
    }

    public static ServiceData getErrorCodeServiceData(@PropertyKey(resourceBundle = ERRORCODEMESSAGES_BUNDLE) String errorCode, Object... arguments) {
        String errorCodeMessage = ErrorCodeMessageUtil.getErrorCodeMessage(errorCode, arguments);
        String errorCodeTip = ErrorCodeMessageUtil.getErrorCodeTipByErrorMsg(errorCodeMessage);
        return getErrorCodeServiceDataWithErrorCodeTip(errorCode, errorCodeTip);
    }

    /**
     * 兼容ValidationException同一个错误码不同提示的场景
     * @param errorCode
     * @param errorCodeTip
     * @return
     */
    public static ServiceData getErrorCodeServiceDataWithErrorCodeTip(@PropertyKey(resourceBundle = ERRORCODEMESSAGES_BUNDLE)String errorCode, String errorCodeTip ) {
        ServiceData resultServiceData = new ServiceData();
        RetCode retCode = new RetCode();
        retCode.setCode(errorCode);
        retCode.setMsgId(errorCode);
        retCode.setMsg(errorCodeTip);
        resultServiceData.setCode(retCode);
        return resultServiceData;
    }
}