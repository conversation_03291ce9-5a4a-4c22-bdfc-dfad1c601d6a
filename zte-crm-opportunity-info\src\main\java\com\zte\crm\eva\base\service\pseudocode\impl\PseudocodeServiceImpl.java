package com.zte.crm.eva.base.service.pseudocode.impl;

import com.zte.crm.eva.base.domain.pseudocode.model.PseudocodeRule;
import com.zte.crm.eva.base.infrastructure.adapter.IchannelBaseAdapter;
import com.zte.crm.eva.base.infrastructure.adapter.PseudocodeExecutor;
import com.zte.crm.eva.base.service.pseudocode.PseudocodeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;

@Service
public class PseudocodeServiceImpl implements PseudocodeService {

    private static final String RULE_EMPTY = "the rule is empty";

    @Autowired
    private IchannelBaseAdapter ichannelBaseAdapter;

    @Autowired
    private PseudocodeExecutor pseudocodeExecutor;

    @Override
    public String pseudocodeTask(String module, String ruleName, Map<String, Object> param) throws Exception {
        List<PseudocodeRule> ruleList = ichannelBaseAdapter.getPseudocodeRule(module, ruleName);
        if (CollectionUtils.isEmpty(ruleList)) {
            return RULE_EMPTY;
        }
        PseudocodeRule rule = ruleList.get(0);
        if (ObjectUtils.isEmpty(rule) || StringUtils.isBlank(rule.getRuleContent())) {
            return RULE_EMPTY;
        }
        return pseudocodeExecutor.executePseudocode(rule, param);
    }

}
