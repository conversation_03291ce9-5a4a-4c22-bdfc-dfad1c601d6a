<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.leadinfo.leadinfo.mapper.CxOptyEmpMapper">

    <resultMap id="BaseResultMap" type="com.zte.leadinfo.leadinfo.entity.CxOptyEmpDO">
        <id property="rowId" column="ROW_ID" jdbcType="VARCHAR"/>
        <result property="created" column="CREATED" jdbcType="TIMESTAMP"/>
        <result property="createdBy" column="CREATED_BY" jdbcType="VARCHAR"/>
        <result property="lastUpd" column="LAST_UPD" jdbcType="TIMESTAMP"/>
        <result property="lastUpdBy" column="LAST_UPD_BY" jdbcType="VARCHAR"/>
        <result property="activeFlag" column="ACTIVE_FLAG" jdbcType="VARCHAR"/>
        <result property="opptyId" column="OPPTY_ID" jdbcType="VARCHAR"/>
        <result property="empId" column="EMP_ID" jdbcType="VARCHAR"/>
        <result property="optyRoleType" column="OPTY_ROLE_TYPE" jdbcType="VARCHAR"/>
        <result property="login" column="LOGIN" jdbcType="VARCHAR"/>
        <result property="startTime" column="START_TIME" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="END_TIME" jdbcType="TIMESTAMP"/>
        <result property="actualEndTime" column="ACTUAL_END_TIME" jdbcType="TIMESTAMP"/>
        <result property="secretInfoFlag" column="SECRET_INFO_FLAG" jdbcType="CHAR"/>
        <result property="dataSource" column="DATA_SOURCE" jdbcType="VARCHAR"/>
        <result property="filingInfoFlag" column="FILING_INFO_FLAG" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ROW_ID,CREATED,CREATED_BY,
        LAST_UPD,LAST_UPD_BY,ACTIVE_FLAG,
        OPPTY_ID,EMP_ID,OPTY_ROLE_TYPE,
        LOGIN,START_TIME,END_TIME,
        ACTUAL_END_TIME,SECRET_INFO_FLAG,DATA_SOURCE,
        FILING_INFO_FLAG
    </sql>

</mapper>