package com.zte.mcrm.channel.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 公司主产品导入
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class ImportPdmDTO {
    @ExcelProperty(value = "商机编号")
    private String optyCd;

    @ExcelProperty(value = "政企产品")
    private String prodLvName;

    @ExcelProperty(value = "体系内部分类编码")
    private String prodLv1Id;

    @ExcelProperty(value = "体系内部分类")
    private String prodLv1Name;

    @ExcelProperty(value = "大产品线编码")
    private String prodLv2Id;

    @ExcelProperty(value = "大产品线")
    private String prodLv2Name;

    @ExcelProperty(value = "产品线编码")
    private String prodLv21Id;

    @ExcelProperty(value = "产品线")
    private String prodLv21Name;

    @ExcelProperty(value = "产品大类编码")
    private String prodLv3Id;

    @ExcelProperty(value = "产品大类")
    private String prodLv3Name;

    @ExcelProperty(value = "产品小类编码")
    private String prodLv4Id;

    @ExcelProperty(value = "产品小类")
    private String prodLv4Name;
}
