package com.zte.mcrm.channel.model.mapper;

import com.zte.mcrm.channel.model.entity.OpportunityProduct;
import com.zte.mcrm.channel.model.vo.OpportunityProductVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2021/10/19
 */
@Mapper
public interface OpportunityProductMapper {
    OpportunityProductMapper INSTANCE = Mappers.getMapper(OpportunityProductMapper.class);

    /**
     * PrmOpportunityProductVO 转 OpportunityProduct
     * @param opportunityProductVO
     * @return
     */
    OpportunityProduct transOpportunityProductVOToOpportunityProduct(OpportunityProductVO opportunityProductVO);


    /**
     * OpportunityProduct 转 PrmOpportunityProductVO
     * @param opportunityProduct
     * @return
     */
    OpportunityProductVO transOpportunityProductToOpportunityProductVO(OpportunityProduct opportunityProduct);
}
