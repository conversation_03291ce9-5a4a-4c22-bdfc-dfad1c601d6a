package com.zte.mcrm.common.util;

import com.google.common.collect.Maps;
import com.zte.itp.msa.core.model.FormData;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 描述：实体转换，eq: FormData to Map
 *
 * <AUTHOR> yang<PERSON><PERSON>han
 * @date 2019/5/29
 */
public class EntityTransformUtils {

    private EntityTransformUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static final String SERIAL_VERSION_UID = "serialVersionUID";
    /**
     * Object 转map
     * <AUTHOR>
     * @date 2019/05/10
     * @param bo
     * @return
     */
    public static <T> Map<String, Object> toMapParams(T bo) {
        return toMapParams(bo, null, null);
    }
    /**
     * Object 转map
     *
     * @param bo
     * @return
     * <AUTHOR>
     * @date 2019/05/10
     */
    public static <T> Map<String, Object> objectToMapParamsExcludeFields(T bo, Set<String> excludeFields) {
        return toMapParams(bo, null, excludeFields);
    }

    /**
     * Object 转map
     * <AUTHOR>
     * @date 2019/05/10
     * @param bo
     * @return
     */
    public static <T> Map<String, Object> objectToMapParamsWithFields(T bo,  Set<String> includeFields) {
        return toMapParams(bo, includeFields, null);
    }

    /**
     * FormData转map
     * <AUTHOR>
     * @date 2019/05/10
     * @param form
     * @return
     */
    public static <T> Map<String, Object> toMapParams(FormData<T> form) {
        return toMapParams(form, null, null);
    }

    /**
     * FormData includeFields 转map
     * <AUTHOR>
     * @date 2019/05/10
     * @param form
     * @param includeFields
     * @return
     */
    public static <T> Map<String, Object> toMapParamsWithFields(FormData<T> form, Set<String> includeFields) {
        return toMapParams(form, includeFields, null);
    }

    /**
     * FormData excludeFields 转map
     * <AUTHOR>
     * @date 2019/05/10
     * @param form
     * @param excludeFields
     * @return
     */
    public static <T> Map<String, Object> toMapParamsExcludeFields(FormData<T> form, Set<String> excludeFields) {
        return toMapParams(form, null, excludeFields);
    }


    /**
     * 转换为map
     * <AUTHOR>
     * @date 2019/05/10
     * @param form
     * @param includeFields
     * @param excludeFields
     * @return
     */
    private static <T> Map<String, Object> toMapParams(FormData<T> form, Set<String> includeFields,
                                                       Set<String> excludeFields) {
        if (null == form || null == form.getBo()) {
            return Maps.newHashMap();
        }
        T bo = form.getBo();
        return toMapParams(bo,includeFields, excludeFields);
    }

    private static <T> Map<String, Object> toMapParams( T bo,Set<String> includeFields, Set<String> excludeFields) {
        if(null==bo){
            return Maps.newHashMap();
        }
        Map<String, Object> map = Maps.newHashMap();
        Class clazz = bo.getClass();
        Field[] fields = clazz.getDeclaredFields();
        List<Set<String>> inAndExFields = new ArrayList<>();
        inAndExFields.add(includeFields);
        inAndExFields.add(excludeFields);
        Arrays.stream(fields).forEach(field -> put2Map(inAndExFields, map, bo, clazz, field));
        return map;
    }

    /**
     * unknown
     * <AUTHOR>
     * @date 2019/05/06
     * @param includeFields
     * @param excludeFields
     * @param map
     * @param bo
     * @param clazz
     * @param field
     */
    private static <T> void put2Map(List<Set<String>> inAndExFields, Map<String, Object> map, T bo,
                                    Class clazz, Field field) {
        Set<String> includeFields = inAndExFields.get(0);
        Set<String> excludeFields = inAndExFields.get(1);
        field.setAccessible(true);
        String key = field.getName();
        Object value = null;
        try {
            value = field.get(bo);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(clazz.getName() + "." + field.getName() + " access exception,cause by： ", e);
        }
        if (pass(includeFields, excludeFields, key) && isNotEmpty(value) && !SERIAL_VERSION_UID.equals(key)) {
            map.put(key, value);
        }
    }

    /**
     * 判断非空
     * <AUTHOR>
     * @date 2019/05/10
     * @param value
     * @return
     */
    private static boolean isNotEmpty(Object value) {
        if (null == value) {
            return false;
        }
        if (value instanceof String) {
            return StringUtils.isNotEmpty((String) value);
        }
        return true;
    }
    /**
     * 筛选
     * <AUTHOR>
     * @date 2019/05/10
     * @param includeFields
     * @param excludeFields
     * @param key
     * @return
     */
    private static boolean pass(Set<String> includeFields, Set<String> excludeFields, String key) {
        boolean include = null == includeFields || includeFields.contains(key);
        boolean exclude = null == excludeFields || !excludeFields.contains(key);
        return include && exclude;
    }

}
