package com.zte.mcrm.common.business.service;

import com.alibaba.fastjson.TypeReference;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.common.consts.HttpMethodEnum;
import com.zte.mcrm.common.model.ComMsgForwardDTO;
import com.zte.mcrm.common.util.MsaUtils;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/12/21
 */
@Service
public class MessageService {
    private static final Logger log = LoggerFactory.getLogger(MessageService.class);
    @Value("${base.comMsgForward.asyncUrl}")
    private String asyncUrl;

    /**
     * 异步发送消息
     *
     * <AUTHOR>
     * @date 2021/12/25
     */
    public Boolean sendMessageAsync(ComMsgForwardDTO msgDTO) {
        try {
            return MsaUtils.invokeBaseService(
                    asyncUrl,
                    HttpMethodEnum.POST,
                    msgDTO,
                    new TypeReference<ServiceData<Boolean>>() {
                    });
        } catch (RouteException e) {
            log.error("Send message failed, Caused by: ", e);
            return Boolean.FALSE;
        }
    }
}
