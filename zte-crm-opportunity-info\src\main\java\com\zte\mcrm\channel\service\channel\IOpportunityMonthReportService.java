package com.zte.mcrm.channel.service.channel;


import com.zte.mcrm.channel.model.entity.OpportunityMonthReport;
import com.zte.mcrm.channel.model.entity.OpportunityKeyInfoEntity;
import com.zte.mcrm.channel.model.vo.OpportunityMonthReportVO;
import com.zte.springbootframe.common.exception.BusiException;

import java.util.List;

/**
 *  商机月报 服务接口类
 * <AUTHOR>
 * @date 2021/10/20
 */
public interface IOpportunityMonthReportService {

    /**
     * 根据 当前商机 当前归属期 查询 商机当前状态 和 原因代码
     * @return 实体
     * <AUTHOR>
     * @date 2021/10/20
     */
    OpportunityMonthReport queryOpportunityStatusAndReason(String currentOpportunity, String currentReportMonth);

    /**
     * 更新 商机当前状态 和 原因代码
     * @return 实体
     * <AUTHOR>
     * @date 2021/10/20
     */
    int updateOpportunityStatusAndReason(OpportunityMonthReport updateBody) throws Exception;

    /**
     * 更新月报
     * @param opportunityMonthReportVO 商机月报实体类
     * @return
     * @throws Exception
     */
    OpportunityMonthReport updateOpportunityMonthReport(OpportunityMonthReportVO opportunityMonthReportVO) throws Exception;

    /**
     * 查询商机月报数目
     * @param rowId 商机id
     * @return
     */
    long getMonthReportCountByRowId(String rowId);

    /**
     * 查询商机月报列表
     * @param rowId 商机id
     * @return
     */
    List<OpportunityMonthReport> getMonthReportList(String rowId);

    /**
     * 查询商机月报归属期列表
     * @param optyId 商机id
     * @return
     */
    List<String> getReportMonthList(String optyId);

    /**
     * 插入月报表
     * @param opportunityMonthReport 商机月报信息
     * @throws BusiException
     */
    void insertMonthReport(OpportunityMonthReport opportunityMonthReport) throws BusiException;

    /**
     * 查询商机月报详情
     * @param optyId 商机id
     * @param reportMonth 商机月报归属期，如202202
     * @return
     */
    OpportunityMonthReportVO getMonthReportDetail(String optyId, String reportMonth);

    /**
     * 获取需要发送提醒邮件的商机列表
     * @return
     */
    List<OpportunityKeyInfoEntity> getMonthReportReminders(String reportMonth);

    /**
     * 获取由于未及时更新月报需要失效的商机列表
     * @return
     */
    List<OpportunityKeyInfoEntity> getMonthlyInvalidOptys(String reportMonth);
}
