package com.zte.mcrm.adapter.authorization.constant;

import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/9/30
 */
@Component
public class UppAuthPropertiesConstant {
    public static final Map<String,String> MODULE_MAP = Maps.newHashMap();
    @Value("${ichannel.upp.auth.moduleId}")
    public String ichannelModuleId;
    @Value("${upp.auth.moduleId}")
    public String prmModuleId;

    @PostConstruct
    private void init() {
        MODULE_MAP.put("ichannel",ichannelModuleId);
        MODULE_MAP.put("OpportunityManagement",prmModuleId);
    }
}
