<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.mcrm.common.access.dao.IComDictionaryMaintainDao">

    <resultMap id="BaseMap" type="com.zte.mcrm.common.access.vo.ComDictionaryMaintainVO">
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="chinese_name" property="chineseName" jdbcType="VARCHAR"/>
        <result column="english_name" property="englishName" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="parent_code" property="parentCode" jdbcType="VARCHAR"/>
        <result column="value" property="value" jdbcType="VARCHAR"/>
        <result column="order_by" property="orderBy" jdbcType="DECIMAL"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_date" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR"/>
        <result column="last_updated_date" property="lastUpdatedDate" jdbcType="TIMESTAMP"/>
        <result column="enabled_flag" property="enabledFlag" jdbcType="VARCHAR"/>
        <result column="memo" property="memo" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="allColumn">
        m.`id`,
        m.`type`,
        m.`code`,
        m.`chinese_name`,
        m.`english_name`,
        m.`value`,
        m.`description`,
        m.`parent_code`,
        m.`order_by`,
        m.`created_by`,
        m.`created_date`,
        m.`last_updated_by`,
        m.`last_updated_date`,
        m.`enabled_flag`,
        m.`memo`
    </sql>

    <!-- 根据“类型集合”查询测算数据字典 -->
    <select id="queryByTypeList" parameterType="list" resultMap="BaseMap">
        SELECT
        <include refid="allColumn"></include>
        FROM
        com_dictionary_maintain m
        WHERE m.enabled_flag = 'Y'
        and m.type in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY m.order_by ASC
    </select>

    <update id="updateDictById"  parameterType="com.zte.mcrm.common.access.vo.ComDictionaryMaintainVO">
        update com_dictionary_maintain
        <set>
            <if test="type != null and type!=''">type = #{type, jdbcType=VARCHAR}</if>
            <if test="code != null and code != ''">code = #{code, jdbcType=VARCHAR}</if>
            <if test="chineseName != null and chineseName!=''">chinese_name = #{chineseName, jdbcType=VARCHAR}</if>
            <if test="englishName != null and englishName!=''">english_name = #{englishName, jdbcType=VARCHAR}</if>
            <if test="description != null and description!=''">description = #{description, jdbcType=VARCHAR}</if>
            <if test="parentCode != null and parentCode!=''">parent_code = #{parentCode, jdbcType=VARCHAR}</if>
            <if test="value != null and value!=''">value = #{value, jdbcType=VARCHAR}</if>
            <if test="orderBy != null">order_by = #{orderBy, jdbcType=VARCHAR}</if>
            <if test="createdBy != null">created_by = #{createdBy, jdbcType=VARCHAR}</if>
            <if test="createdDate != null">created_date = #{createdDate, jdbcType=VARCHAR}</if>
            <if test="enabledFlag != null and enabledFlag!=''">enabled_flag = #{enabledFlag, jdbcType=VARCHAR}</if>
            <if test="memo != null">memo = #{memo, jdbcType=VARCHAR}</if>
            <if test="lastUpdatedBy != null">last_updated_by = #{lastUpdatedBy, jdbcType=VARCHAR}</if>
        </set>
        where id = #{id, jdbcType=VARCHAR}
    </update>

    <!--添加一条记录 -->
    <insert id="insert" parameterType="com.zte.mcrm.common.access.vo.ComDictionaryMaintainVO" >
        INSERT INTO com_dictionary_maintain
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null">type ,</if>
            <if test="code != null">code ,</if>
            <if test="chineseName != null">chinese_name ,</if>
            <if test="englishName != null">english_name ,</if>
            <if test="value != null">value ,</if>
            <if test="description != null">description ,</if>
            <if test="parentCode != null">parent_code ,</if>
            <if test="orderBy != null">order_by ,</if>
            <if test="createdBy != null">created_by ,</if>
            <if test="createdDate != null">created_date ,</if>
            <if test="lastUpdatedBy != null">last_updated_by ,</if>
            <if test="lastUpdatedDate != null">last_updated_date ,</if>
            <if test="enabledFlag != null">enabled_flag ,</if>
            <if test="memo != null">memo </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="type != null">#{type,jdbcType=VARCHAR} ,</if>
            <if test="code != null">#{code,jdbcType=VARCHAR} ,</if>
            <if test="chineseName != null">#{chineseName,jdbcType=VARCHAR} ,</if>
            <if test="englishName != null">#{englishName,jdbcType=VARCHAR} ,</if>
            <if test="value != null">#{value,jdbcType=VARCHAR} ,</if>
            <if test="description != null">#{description,jdbcType=VARCHAR} ,</if>
            <if test="parentCode != null">#{parentCode,jdbcType=VARCHAR} ,</if>
            <if test="orderBy != null">#{orderBy,jdbcType=VARCHAR} ,</if>
            <if test="createdBy != null">#{createdBy,jdbcType=VARCHAR} ,</if>
            <if test="createdDate != null">#{createdDate,jdbcType=VARCHAR} ,</if>
            <if test="lastUpdatedBy != null">#{lastUpdatedBy,jdbcType=VARCHAR} ,</if>
            <if test="lastUpdatedDate != null">#{lastUpdatedDate,jdbcType=VARCHAR} ,</if>
            <if test="enabledFlag != null">#{enabledFlag,jdbcType=VARCHAR} ,</if>
            <if test="memo != null">#{memo,jdbcType=VARCHAR} </if>
        </trim>
    </insert>

</mapper>
