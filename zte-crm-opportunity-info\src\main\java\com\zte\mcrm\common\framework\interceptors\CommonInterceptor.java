package com.zte.mcrm.common.framework.interceptors;


import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.util.web.HttpHeaderUtil;
import com.zte.mcrm.adapter.constant.HeaderNameConst;
import com.zte.mcrm.common.model.SysGlobalConstVo;
import com.zte.mcrm.common.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 拦截器（获取EmpNo）
 * <AUTHOR>
 * @date 2021/01/16
 */
@Component
public class CommonInterceptor implements HandlerInterceptor {

	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	
	@Override
	public boolean preHandle(HttpServletRequest request,
			HttpServletResponse response, Object handler)
			throws Exception {
		String empNo = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
		String language = request.getHeader(SysGlobalConst.HTTP_HEADER_X_LANG_ID);
		String xAuthValue = request.getHeader(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE);
		String xOrgId = request.getHeader(SysGlobalConst.HTTP_HEADER_X_ORG_ID);
		String xTenantId = request.getHeader(SysGlobalConst.HTTP_HEADER_X_TENANT_ID);
		String uuid = HttpHeaderUtil.getHeader(HeaderNameConst.X_AUTH_NONCE);
		String timeStampStr = HttpHeaderUtil.getHeader(HeaderNameConst.X_AUTH_TIMESTAMP);
		String accessKey = HttpHeaderUtil.getHeader(HeaderNameConst.X_AUTH_ACCESSKEY);
		String signature = HttpHeaderUtil.getHeader(HeaderNameConst.X_AUTH_SIGNATURE);
		String serviceName = HttpHeaderUtil.getHeader(HeaderNameConst.X_ORIGIN_SERVICENAME);
		String subTenantId = HttpHeaderUtil.getHeader(HeaderNameConst.X_TENANT_ID_SUB);
		String companyId = HttpHeaderUtil.getHeader(HeaderNameConst.X_COMPANY_ID);
		SysGlobalConstVo vo = new SysGlobalConstVo();
		vo.setxEmpNo(empNo);
		vo.setxAuthValue(xAuthValue);
		vo.setxLangId(language);
		vo.setxOrgId(xOrgId);
		vo.setxTenantId(xTenantId);
		vo.setAuthNotice(uuid);
		vo.setAuthSignature(signature);
		vo.setAuthAccessKey(accessKey);
		vo.setTimeStampStr(timeStampStr);
		vo.setxOriginServiceName(serviceName);
		vo.setSubTenantId(subTenantId);
		vo.setCompanyId(companyId);
		CommonUtils.setSysGlobalConstVo(vo);
		return true;
	}

	@Override
	public void postHandle(
			HttpServletRequest request, 
			HttpServletResponse response, 
			Object handler,
			ModelAndView modelAndView) throws Exception {
	    
	}

	@Override
	public void afterCompletion(
			HttpServletRequest request, 
			HttpServletResponse response, 
			Object handler, 
			Exception ex)
			throws Exception {
		
	}

}
