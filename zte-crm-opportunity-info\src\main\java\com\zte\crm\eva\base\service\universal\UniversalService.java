package com.zte.crm.eva.base.service.universal;

import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;

import java.util.Map;

/**
 * @ClassName: UniversalService
 * @Description: 通用模板服务层
 * <AUTHOR>
 * @date: 2023-04-13
 **/
public interface UniversalService {

    /**
     * 新增
     * @param map
     * @param fileName
     */
    boolean insertUniversal(Map<String, Object> map, String fileName) throws Exception;

    /**
     * 更新
     * @param map
     * @param fileName
     */
    boolean updateUniversal(Map<String, Object> map,  String fileName) throws Exception;

    /**
     * 组合分页查询
     * @param form
     * @param fileName
     * @return
     */
    PageRows<Map<String,Object>> selectCommonByPage(FormData<Map<String, Object>> form, String fileName) throws Exception;

    /**
     * 批量软删除
     * @param map
     * @param fileName
     */
    boolean deleteUniversal(Map<String, Object> map, String fileName) throws Exception;





}
