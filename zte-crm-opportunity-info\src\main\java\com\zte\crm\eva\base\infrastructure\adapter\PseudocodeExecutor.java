package com.zte.crm.eva.base.infrastructure.adapter;

import com.zte.crm.eva.base.domain.pseudocode.model.PseudocodeRule;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-04-28
 */
public interface PseudocodeExecutor {

    /**
     * 执行伪代码
     * @param rule
     * @param param
     * @return
     */
    String executePseudocode(PseudocodeRule rule, Map<String, Object> param) throws Exception;
}
