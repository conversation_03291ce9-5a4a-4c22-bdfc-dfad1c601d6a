package com.zte.mcrm.adapter.model.mapper;

import com.zte.mcrm.adapter.model.dto.AccountInfo;
import com.zte.mcrm.adapter.model.dto.CustomerDetailInfoDTO;
import com.zte.mcrm.channel.constant.AccountStatusEnum;
import com.zte.mcrm.channel.constant.RestrictedPartyEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface CustomerDetailInfoMapper {

    CustomerDetailInfoMapper INSTANCE = Mappers.getMapper(CustomerDetailInfoMapper.class);

    @Mapping(source = "baseInfo.customerCode", target = "accountNum")
    @Mapping(source = "baseInfo.customerName", target = "accountName")
    @Mapping(source = "baseInfo.customerStatus", target = "activeStatusCode", qualifiedByName = "statusToOldStatus")
    @Mapping(source = "baseInfo.customerFrozenFlag", target = "frozenFlag")
    @Mapping(source = "baseInfo.customerMergeFlag", target = "acctMergeFlag")
    @Mapping(source = "baseInfo.sanctionedParty", target = "restrictedPartyCode")
    @Mapping(source = "baseInfo.customerStatus", target = "activeStatus", qualifiedByName = "statusCodeToName")
    @Mapping(source = "baseInfo.sanctionedParty", target = "restrictedParty", qualifiedByName = "sanctionedPartyToName")
    @Mapping(source = "mainCustBaseInfo.customerCode", target = "mainAcctNum")
    @Mapping(source = "mainCustBaseInfo.customerName", target = "mainAcctName")
    AccountInfo customerDetailInfoDTOToAccountInfo(CustomerDetailInfoDTO customerDetailInfoDTO);

    List<AccountInfo> customerDetailInfoDTOToAccountInfoBatch(List<CustomerDetailInfoDTO> customerDetailInfoDTOs);

    /* Started by AICoder, pid:4f46885a07ba4574883dabe86d0cb544 */
    @Named("statusToOldStatus")
    default String statusToOldStatus(String status) {
        AccountStatusEnum accountStatusEnum = AccountStatusEnum.findAccountStatusEnum(status);
        return accountStatusEnum.getValue();
    }

    @Named("statusCodeToName")
    default String statusCodeToName(String status) {
        AccountStatusEnum accountStatusEnum = AccountStatusEnum.findAccountStatusEnum(status);
        return accountStatusEnum.getDesc();
    }

    @Named("sanctionedPartyToName")
    default String sanctionedPartyToName(String status) {
        return RestrictedPartyEnum.getNameByCode(status);
    }
    /* Ended by AICoder, pid:4f46885a07ba4574883dabe86d0cb544 */
}
