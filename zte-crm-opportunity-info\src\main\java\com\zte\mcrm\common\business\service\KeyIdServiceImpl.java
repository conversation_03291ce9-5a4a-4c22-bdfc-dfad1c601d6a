package com.zte.mcrm.common.business.service;

import com.zte.mcrm.common.business.KeyIdBusiness;
import org.springframework.stereotype.Service;

/**
 * @author: 李小刚 10165513
 * @date: 2018年2月8日 上午9:43:16
 * @description:
 */
@Service
public class KeyIdServiceImpl implements IKeyIdService {

	@Override
	public String getKeyId() {
		String keyId = String.valueOf(KeyIdBusiness.getID());
		return keyId;
	}
	
	@Override
	public Long getKeyLongId() {
		return KeyIdBusiness.getID();
	}
}
