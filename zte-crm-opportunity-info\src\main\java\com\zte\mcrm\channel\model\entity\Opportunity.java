package com.zte.mcrm.channel.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
*  实体类
* <AUTHOR>
* @date 2021/09/14
*/

@Setter @Getter @ToString
@ApiModel(description="")
public class Opportunity implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "")
    private String rowId;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "")
    private Date lastUpd;
    @ApiModelProperty(value = "")
    private String lastUpdBy;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "")
    private Date created;
    @ApiModelProperty(value = "")
    private String createdBy;
    @ApiModelProperty(value = "商机编码")
    private String optyCd;
    @ApiModelProperty(value = "商机组织Id")
    private String buId;
    @ApiModelProperty(value = "旧组织编码")
    private String oldBuId;
    @ApiModelProperty(value = "组织全路径")
    private String orgNamePath;
    @ApiModelProperty(value = "商机状态")
    private String statusCd;
    @ApiModelProperty(value = "月报状态")
    private String reportStatus;
    @ApiModelProperty(value = "商机当前状态")
    private String currentStatus;
    @ApiModelProperty(value = "客户Id")
    private String prDeptOuId;
    @ApiModelProperty(value = "业务范围")
    private String businessTypeCd;
    @ApiModelProperty(value = "MKT活动")
    private String xMktId;
    @ApiModelProperty(value = "父商机id")
    private String parOptyId;
    @ApiModelProperty(value = "商机来源")
    private String dataSource;
    @ApiModelProperty(value = "用于Oracle ODI用的字段，非空")
    private String name;
    @ApiModelProperty(value = "")
    private String spaceId;
    @ApiModelProperty(value = "有效标记")
    private String enabledFlag;
    @ApiModelProperty(value = "租户id")
    private Integer tenantId;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "报备失效时间")
    private Date expiryDate;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "报备成功时间")
    private Date successDate;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "报备提交时间")
    private Date submitDate;
    @ApiModelProperty(value = "是否新业务(Y/N)")
    private String isNewBusiness;
    /**
     * 转立项时间
     */
    private Date transferToProjectTime;
}
