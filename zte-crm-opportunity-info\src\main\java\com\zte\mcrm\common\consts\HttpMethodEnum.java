package com.zte.mcrm.common.consts;

public enum HttpMethodEnum {
    // GET
    GET("get"),
    // POST
    POST("post"),
    // PUT
    PUT("put"),
    // DELETE
    DELETE("delete"),
    // HEAD
    HEAD("head"),
    // OPTIONS
    OPTIONS("options");

    private String key;

    private HttpMethodEnum(String key) {
        this.key = key;
    }

    public static HttpMethodEnum valueOfKey(String key) {
        for (HttpMethodEnum item : HttpMethodEnum.values()) {
            if (item.getKey().equalsIgnoreCase(key)) {
                return item;
            }
        }
        return null;
    }

    public String getKey() {
        return this.key;
    }
}
