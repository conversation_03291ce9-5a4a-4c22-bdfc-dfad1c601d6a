package com.zte.mcrm.adapter.model.dto;

/* Started by AICoder, pid:0af71198a50848249d66b221f20bff6f */
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(description = "客户信息模型")
@Getter
@Setter
public class CustomerCreateInfoDTO {

    @ApiModelProperty(value = "客户类型", required = true, example = "10008")
    private String accountType;

    @ApiModelProperty(value = "客户别名", example = "完美世界（成都）文化发展有限公司_whx")
    private String accountAlias;

    @ApiModelProperty(value = "财务类型", required = true, example = "FINS ACNT 02")
    private String finsType;

    @ApiModelProperty(value = "所属部门", example = "ORG0100811，如果有多个，使用逗号连接")
    private String fromDepartment;

    @ApiModelProperty(value = "是否子公司", required = true, example = "Y、N")
    private String subsidiaryFlg;

    @ApiModelProperty(value = "主管部门编号", required = true, example = "ORG0100811")
    private String deptNo;

    @ApiModelProperty(value = "客户名称", required = true, example = "完美世界（成都）文化发展有限公司_whx")
    private String accountName;

    @ApiModelProperty(value = "创建时间", example = "2023-11-27 17:56:05")
    private String created;

    @ApiModelProperty(value = "国家id", required = true, example = "0001")
    private String country;

    @ApiModelProperty(value = "通信地址", required = true, example = "1234")
    private String local;

    @ApiModelProperty(value = "客户团队", example = "********")
    private String accountTeam;

    @ApiModelProperty(value = "营业执照的附件id", example = "")
    private String attachmentId;

    @ApiModelProperty(value = "城市编码", required = true, example = "0728")
    private String cityCode;

    @ApiModelProperty(value = "注册地址", example = "123456")
    private String registerAddress;

    @ApiModelProperty(value = "创建人", example = "********")
    private String operator;

    @ApiModelProperty(value = "PRM ID", required = true, example = "************")
    private String prmId;

    @ApiModelProperty(value = "客户子类型", required = true, example = "10134")
    private String accountSubType;

    @ApiModelProperty(value = "ZTE业务客户分类", required = true, example = "10504")
    private String accountThiType;

    @ApiModelProperty(value = "DME URL", example = "")
    private String dmeUrl;

    @ApiModelProperty(value = "集团客户简称", example = "")
    private String mtoCode;

    @ApiModelProperty(value = "主要电话", required = true, example = "***********")
    private String mainPhone;

    @ApiModelProperty(value = "城市id", required = true, example = "************")
    private String city;

    @ApiModelProperty(value = "客户范围", required = true, example = "N")
    private String accountScope;

    @ApiModelProperty(value = "来源系统", required = true, example = "PRM")
    private String sourceSys;

    @ApiModelProperty(value = "客户状态", required = true, example = "CUSTOMER")
    private String accountStatus;

    @ApiModelProperty(value = "联系人状态，正在联系：Y，失去联系：N", required = true, example = "Y、N")
    private String contactStatus;

    @ApiModelProperty(value = "国家编码", required = true, example = "86")
    private String countryCode;

    @ApiModelProperty(value = "渠道商名称")
    private String channelName;

    /* Started by AICoder, pid:3e2ce6922cbd44a89a075887292eff5f */
    public CustomerCreateInfoDTO() {
        this.finsType = "FINS ACNT 02";
        this.subsidiaryFlg = "N";
        this.country = "0001";
        this.cityCode = "0728";
        this.prmId = "************";
        this.accountThiType = "10504";
        this.accountScope = "N";
        this.sourceSys = "Prm";
        this.accountStatus = "CUSTOMER";
        this.contactStatus = "Y";
        this.countryCode = "86";
        this.operator = "cpc";
    }
    /* Ended by AICoder, pid:3e2ce6922cbd44a89a075887292eff5f */

}
/* Ended by AICoder, pid:0af71198a50848249d66b221f20bff6f */