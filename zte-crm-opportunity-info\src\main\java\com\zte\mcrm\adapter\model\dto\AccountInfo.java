package com.zte.mcrm.adapter.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/10/8
 */
@Data
public class AccountInfo {
    @ApiModelProperty("最终用户ID")
    private String id;
    @ApiModelProperty("最终用户编码")
    private String accountNum;
    @ApiModelProperty("客户编码")
    private String custNo;
    @ApiModelProperty(value = "客户名称")
    private String accountName;
    @ApiModelProperty("状态")
    private String activeStatus;
    @ApiModelProperty("状态编码")
    private String activeStatusCode;
    @ApiModelProperty("客户受限制主体")
    private String restrictedParty;
    @ApiModelProperty("客户受限制主体code")
    private String restrictedPartyCode;
    @ApiModelProperty("所属国家id")
    private String countryId;
    @ApiModelProperty("是否被合并标识，M:主客户，H:被合并客户")
    private String acctMergeFlag;
    @ApiModelProperty("被合并至客户的编码")
    private String mainAcctNum;
    @ApiModelProperty("被合并至客户名称")
    private String mainAcctName;
    @ApiModelProperty("是否活跃标识，F:沉默")
    private String frozenFlag;
    @ApiModelProperty("创建人")
    private String createdBy;
}
