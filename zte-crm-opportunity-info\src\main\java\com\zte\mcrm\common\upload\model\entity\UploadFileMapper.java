package com.zte.mcrm.common.upload.model.entity;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface UploadFileMapper {

    UploadFileMapper INSTANCE = Mappers.getMapper(UploadFileMapper.class);

    /**
     * vo轉DTO
     * @param uploadFile
     * @return
     */
    @Mapping(source = "id", target = "uploadFileId")
    SimpleUploadFileInfo uploadFileToSimpleInfo(ComUploadFile uploadFile);

    /**
     * DTO转BO
     * @param uploadFile
     * @return
     */
    @Mapping(source = "uploadFileId", target = "id")
    ComUploadFile simpleInfoToUploadFile(SimpleUploadFileInfo uploadFile);
}
