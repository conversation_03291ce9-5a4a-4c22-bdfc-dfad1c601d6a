package com.zte.mcrm.channel.service.prm;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.itp.authorityclient.entity.input.CommonModuleIdEntity;
import com.zte.itp.authorityclient.entity.output.RoleVO;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.approval.enums.ParamQueryModelEnum;
import com.zte.mcrm.adapter.approval.model.ApprovedNodeInfo;
import com.zte.mcrm.adapter.approval.model.ApprovingNodeInfo;
import com.zte.mcrm.adapter.approval.model.ApprovingQueryParam;
import com.zte.mcrm.adapter.approval.model.DateTimeInterval;
import com.zte.mcrm.adapter.approval.model.dto.*;
import com.zte.mcrm.adapter.approval.service.ApprovalFlowService;
import com.zte.mcrm.adapter.authorization.service.UppAuthorityService;
import com.zte.mcrm.adapter.constant.HeaderNameConst;
import com.zte.mcrm.adapter.constant.ProjectAuthStatusEnum;
import com.zte.mcrm.adapter.model.AuthConstraintDTO;
import com.zte.mcrm.adapter.model.dto.AccountInfo;
import com.zte.mcrm.adapter.model.dto.CrmProjectInfo;
import com.zte.mcrm.adapter.projectauthorization.dto.OppAuthInfo;
import com.zte.mcrm.adapter.projectauthorization.service.ProjectAuthorizationService;
import com.zte.mcrm.adapter.projectauthorization.utils.ProjectAuthorizationUtils;
import com.zte.mcrm.adapter.service.*;
import com.zte.mcrm.channel.constant.*;
import com.zte.mcrm.channel.dao.*;
import com.zte.mcrm.channel.model.dto.*;
import com.zte.mcrm.channel.model.entity.*;
import com.zte.mcrm.channel.model.mapper.OpportunityDetailMapper;
import com.zte.mcrm.channel.model.vo.*;
import com.zte.mcrm.channel.service.ImportPdmListener;
import com.zte.mcrm.channel.service.OpptyCustomerCreateRecordService;
import com.zte.mcrm.channel.service.channel.*;
import com.zte.mcrm.channel.util.ExceptionMsgUtils;
import com.zte.mcrm.channel.util.CommonRemoteUtils;
import com.zte.mcrm.common.access.dao.IComDictionaryMaintainDao;
import com.zte.mcrm.common.access.vo.ComDictionaryMaintainVO;
import com.zte.mcrm.common.business.service.IComDictionaryMaintainService;
import com.zte.mcrm.common.business.service.IKeyIdService;
import com.zte.mcrm.common.business.service.SensitiveEncryptor;
import com.zte.mcrm.common.consts.ComDictionaryMaintainConsts;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.consts.CommonConstant;
import com.zte.mcrm.common.consts.CommonRetCode;
import com.zte.mcrm.common.exception.BusinessRuntimeException;
import com.zte.mcrm.common.framework.exception.PrmRetCode;
import com.zte.mcrm.common.model.PersonAndOrgInfoVO;
import com.zte.mcrm.common.upload.dao.base.ComUploadFileDao;
import com.zte.mcrm.common.upload.model.entity.ComUploadFile;
import com.zte.mcrm.common.util.*;
import com.zte.mcrm.export.tool.ExportExcel;
import com.zte.mcrm.export.tool.ExportExcelHandle;
import com.zte.mcrm.export.tool.ExportListObjectExcel;
import com.zte.mcrm.lov.business.service.LovService;
import com.zte.mcrm.opportunity.access.vo.ConversionProjectOrdinary;
import com.zte.mcrm.opportunity.access.vo.DocumentList;
import com.zte.mcrm.opportunity.common.OppSysConst;
import com.zte.mcrm.opportunity.utils.PersonAndOrgInfoUtil;
import com.zte.mcrm.predict.access.vo.ProductOrderForecasting;
import com.zte.mcrm.team.access.vo.CoreTeamMember;
import com.zte.mcrm.team.access.vo.GuidanceCommitteeMembe;
import com.zte.mcrm.team.access.vo.ProjectTeam;
import com.zte.opty.common.enums.OptyStatusEnum;
import com.zte.opty.config.OptyAuthConfigProperties;
import com.zte.opty.dao.SOpportunityRepository;
import com.zte.opty.dao.SOptyDao;
import com.zte.opty.dao.SOptyXDao;
import com.zte.opty.feign.PlmProjectCategoryClient;
import com.zte.opty.model.bo.SOptyBO;
import com.zte.opty.model.bo.SOptyXBO;
import com.zte.opty.model.dto.IHolOrgQueryDTO;
import com.zte.opty.model.dto.OptyPlmProjectQueryDTO;
import com.zte.opty.model.vo.IHolOrgVO;
import com.zte.opty.model.vo.OptyPlmProjectVO;
import com.zte.opty.service.SOpportunityExternalService;
import com.zte.opty.sync.util.CommonMapUtil;
import com.zte.opty.sync.util.LcapConverterUtil;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import com.zte.springbootframe.common.model.HttpResultData;
import com.zte.springbootframe.common.model.RetCodeCopy;
import com.zte.springbootframe.common.serviceregister.MicroServiceWithConfigUtil;
import com.zte.springbootframe.util.local.LocalMessageUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zte.mcrm.channel.constant.OpportunityConstant.*;
import static com.zte.opty.common.constants.CommonConst.PLM_STATUS_ENABLE;

/**
 * <AUTHOR>
 */
@Service
public class PrmOpportunityServiceImpl implements IPrmOpportunityService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private OpportunityDao opportunityDao;
    @Autowired
    private OpportunityQueryDao opportunityQueryDao;
    @Autowired
    private SOpportunityRepository sOpportunityRepository;
    @Autowired
    IOpportunityService iOpportunityService;

    @Autowired
    private IComDictionaryMaintainService comDictionaryMaintainService;

    @Autowired
    PrmService prmService;

    @Autowired
    ComposeService composeService;

    @Autowired
    UppAuthorityService uppAuthorityService;

    @Autowired
    private ApprovalFlowService approvalFlowService;

    @Autowired
    private IOpportunityInfoService iOpportunityInfoService;

    @Autowired
    private IComDictionaryMaintainDao comDictionaryMaintainDao;

    @Autowired
    private ProjectAuthorizationService projectAuthorizationService;

    @Autowired
    ICrmProjectInfoService crmProjectInfoService;

    @Autowired
    private LovService lovService;

    @Autowired
    private CustomerInfoService customerInfoService;

    @Autowired
    private IOpportunityProductService iOpportunityProductService;

    @Autowired
    private ComApprovalRecordDao approvalRecordDao;

    @Autowired
    private OpportunityInfoServiceImpl opportunityInfoServiceImpl;

    @Autowired
    CommonRemoteUtils commonRemoteUtils;

    @Autowired
    private SOpportunityExternalService sOpportunityExternalService;

    @Autowired
    private PlmProjectCategoryClient plmProjectCategoryClient;

    private static final String IS_CREATED_BY = "isCreatedBy";

    private static final String IS_BUSINESS_MANAGER = "isBusinessManager";

    private static final String IS_ADMIN = "isAdmin";

    private static final String IS_OFFICE_BUSINESS_MANAGER = "isOfficeBusinessManager";

    private static final String IS_OFFICE_INDUSTRY_SECTION_CHIEF = "isOfficeIndustrySectionChief";

    private static final String IS_CHANNEL_DIRECTOR = "isChannelDirector";

    private static final String ROLE_MAP = "roleMap";

    private static final String CONSTRAINED_ORG_LIST = "constrainedOrgList";

    private static final String CONSTRAINED_INDUSTRY_LIST = "constrainedIndustryList";

    @Value("${zte.org.code}")
    private String orgCode;

    @Autowired
    private SMonthReportDao sMonthReportDao;


    @Value("${encryption.secretKeySixteen}")
    private String secretKeySixteen;

    @Value("${encryption.iv}")
    private String iv;

    @Value("${project.opportunity.transfer}")
    private String opportunityTransferUrl;

    @Value("${project.opportunity.fastTransfer}")
    private String opportunityFastTransferUrl;
    @Value("${maintain.main.prods.start.time:2024-01-01}")
    private String maintainMainProdsStartTime;

    @Autowired
    private IOpportunityDetailService opportunityDetailService;
    @Autowired
    private OpportunityDetailDao opportunityDetailDao;
    @Autowired
    private ComUploadFileDao comUploadFileDao;
    @Autowired
    private OpportunityProductDao opportunityProductDao;
    @Autowired
    private IKeyIdService iKeyIdService;

    @Autowired
    private IPrmOpportunityApprovalService prmOpportunityApprovalService;

    @Autowired
    private ComApprovalRecordDao comApprovalRecordDao;

    @Autowired
    private OpptyCustomerCreateRecordService opptyCustomerCreateRecordService;

    @Autowired
    private SOptyXDao sOptyXDao;

    @Autowired
    private SOptyDao sOptyDao;


    @Autowired
    private OptyAuthConfigProperties optyAuthConfigProperties;

    /**
     * prm侧商机列表分页查询
     *
     * @param formData
     * @param isExport
     * @return
     * @throws Exception
     */
    @Override
    public PageRows<PrmOpportunityVO> getPrmOpportunityPage(FormData<PrmOpportunityQueryDTO> formData, boolean isExport) throws Exception {
        PageRows<PrmOpportunityVO> page = new PageRows<>();
        if( null == formData){
            page.setTotal(0);
            page.setRows(Collections.emptyList());
            return page;
        }
        Map<String, Object> prmOpportunityQueryMap = setPrmOpportunityQueryMap(formData, isExport);

        PageMethod.startPage(FormDataHelpUtil.getPageNum(formData), FormDataHelpUtil.getPageSize(formData));
        List<PrmOpportunityVO> prmOpportunityPage = getPrmOpportunityConditionally(prmOpportunityQueryMap);
        PageInfo<PrmOpportunityVO> pageInfo = new PageInfo<>(prmOpportunityPage);
        page.setTotal(pageInfo.getTotal());
        if (CollectionUtils.isNotEmpty(prmOpportunityPage)) {
            setNameValuesAndButton(prmOpportunityPage, prmOpportunityQueryMap);
            // 设置是否显示合规状态
            setIsDisplayComplianceRefreshButton(prmOpportunityQueryMap,prmOpportunityPage);
        }
        page.setRows(prmOpportunityPage);
        page.setCurrent(FormDataHelpUtil.getPageNum(formData));
        return page;
    }

    private List<PrmOpportunityVO> getPrmOpportunityConditionally(Map<String, Object> prmOpportunityQueryMap) {
        List<PrmOpportunityVO> prmOpportunityPage = opportunityDao.getPrmOpportunityPage(prmOpportunityQueryMap);
        if (CollectionUtils.isEmpty(prmOpportunityPage)) {
            return prmOpportunityPage;
        }
        for (PrmOpportunityVO prmOpportunityVO : prmOpportunityPage) {
            prmOpportunityVO.setTendTypeCode(CommonMapUtil.BIDDING_TYPE_MAP.inverse().getOrDefault(prmOpportunityVO.getTendTypeCode(), prmOpportunityVO.getTendTypeCode()));
        }
        return prmOpportunityPage;
    }

    private void setIsDisplayComplianceRefreshButton(Map<String, Object> map, List<PrmOpportunityVO> prmOpportunityPage) {
        Map<String, RoleVO> roleVOMap = (Map<String, RoleVO>) map.get(ROLE_MAP);

        // 判断是否有管理员和政企中国中兴业务经理权限
        Boolean anyMatch = Boolean.FALSE;
        if (roleVOMap != null && roleVOMap.keySet() != null) {
            anyMatch = roleVOMap.keySet().stream().anyMatch(ele -> OpportunityRoleEnum.getDisplayComplianceRefreshButtonRoles().contains(ele));
        }

        // 不满足返回
        if (!anyMatch) {
            return;
        }
        // 判断是否有【刷新合规状态】显示按钮权限
        // 存在受限制主体 && 状态为报备审批中
        prmOpportunityPage.forEach(ele -> {
            if (OptyStatusEnum.APPROVING.getCode().equals(ele.getStatusCd())
                    && (RestrictedPartyEnum.isRestrictedParty(ele.getAgencyRestrictionFlag())
                    || RestrictedPartyEnum.isRestrictedParty(ele.getFinalCustomerRestrictionFlag()))
            ) {
                if (ele.getOperationButton() != null) {
                    ele.getOperationButton().setComplianceRefreshButtonFlag(Boolean.TRUE);
                }
            }
        });
    }

    private Map<String, Object> setPrmOpportunityQueryMap(FormData<PrmOpportunityQueryDTO> formData, boolean isExport) throws ExecutionException, InterruptedException, ParseException {
        Map<String, Object> prmOpportunityQueryMap = EntityTransformUtils.toMapParams(formData);
        PrmOpportunityQueryDTO prmOpportunityQueryDTO =  formData.getBo();
        compatibleOldBusinessOpportunities(prmOpportunityQueryDTO, prmOpportunityQueryMap);
        List<String> statusReports = prmOpportunityQueryDTO.getStatusReport();
        processingReportStatus(statusReports, prmOpportunityQueryMap);
        String empNo = CommonUtils.getEmpNo();
        prmOpportunityQueryMap.put("empNo", empNo);
        // step1: 权限相关-获取是否管理员或者相关约束信息
        boolean isAdmin = setAuthConstraintsAndJudgeAdmin(prmOpportunityQueryMap);
        // step2: 获取待我评审和我已审批单据
        if (!isAdmin) {
            getMyPendingAndPendedIds(prmOpportunityQueryMap);
        }
        logger.info("开始查询商机列表：{}", prmOpportunityQueryMap);
        prmOpportunityQueryMap.put(OpportunityConstant.IS_EXPORT, isExport);
        Optional.ofNullable(prmOpportunityQueryMap.get("tenderTypeCode"))
                .ifPresent(e -> prmOpportunityQueryMap.put("tenderTypeCode", CommonMapUtil.BIDDING_TYPE_MAP.get(e)));
        return prmOpportunityQueryMap;
    }

    /**
     * 处理月报状态到map中
     * @param statusReports 月报状态列表
     * @param map 查询map
     */
    private void processingReportStatus(List<String> statusReports, Map<String, Object> map) {
        if (CollectionUtils.isEmpty(statusReports) || (statusReports.size() == OpportunityConstant.REPORT_STATUS_NUM)) {
            map.put(OpportunityConstant.REPORT_STATUS_TYPE, "");
        } else if (statusReports.size() == CommonConstant.ONE) {
            map.put(OpportunityConstant.REPORT_STATUS_TYPE, statusReports.get(0));
        }
    }

    private void compatibleOldBusinessOpportunities(PrmOpportunityQueryDTO prmOpportunityQueryDTO, Map<String, Object> prmOpportunityQueryMap){
        if (null == prmOpportunityQueryDTO){
            return;
        }
        List<String> statusCd = prmOpportunityQueryDTO.getStatusCd();
        // 老商机的closed状态对应新商机的报备失效
        if(CollectionUtils.isNotEmpty(statusCd)){
            List<String> oldStatusCd = statusCd.stream().filter(s -> !s.equals(
                    OptyStatusEnum.TICKET_LOSS.getCode())).map(s -> {
                if (s.equals(OptyStatusEnum.OPTY_SUSPEND.getCode())){
                    return OptyStatusEnum.TICKET_LOSS.getCode();
                }
                return s;
            }).collect(Collectors.toList());

            prmOpportunityQueryMap.put(OLD_STATUS_CD, oldStatusCd);
        }
    }

    /**
     * 获取所有待我评审和我已评审单据信息
     *
     */
    /* Started by AICoder, pid:ka146vf9469c56d14db80a28202d2e0876f698b0 */
    public List<String> getMyPendingAndPendedIds() throws ParseException {
        Map<String, Object> reMap = new HashMap<>();
        this.getMyPendingAndPendedIds(reMap);
        List<String> myRelatedIds = (List<String>) reMap.get("myRelatedIds");
        return opportunityDao.getPrmOpportunityByFlowIds(myRelatedIds);
    }
    /* Ended by AICoder, pid:ka146vf9469c56d14db80a28202d2e0876f698b0 */
    /**
     * 获取所有待我评审和我已评审单据信息
     *
     * @param prmOpportunityQueryMap
     * @throws ParseException
     */
    private void getMyPendingAndPendedIds(Map<String, Object> prmOpportunityQueryMap) throws ParseException {
        ApprovingQueryParam approvingQueryParam = setPendingAdvanceQueryParam(prmOpportunityQueryMap, OpportunityConstant.PRM_OPPORTUNITY_ADVANCE_PARAM_SET);
        List<ApprovingNodeInfo> allApprovingNodeInfo = new ArrayList<>();
        try {
            allApprovingNodeInfo = approvalFlowService.getAllMyPending(approvingQueryParam);
        } catch (Exception e) {
            logger.error("所有待我审批列表高级查询出错，Exception：{}", ExceptionMsgUtils.getStackTrace(e, 2000));
        }
        List<String> myRelatedIds = allApprovingNodeInfo.stream()
                .map(ApprovingNodeInfo::getFlowInstanceId).collect(Collectors.toList());
        List<ApprovedNodeInfo> allApprovedTaskInfo = new ArrayList<>();
        try {
            allApprovedTaskInfo = approvalFlowService.getAllMyPended(approvingQueryParam);
        } catch (Exception e) {
            logger.error("所有我已审批列表高级查询出错，Exception：{}", ExceptionMsgUtils.getStackTrace(e, 2000));
        }
        List<String> myPendedIdList = allApprovedTaskInfo.stream()
                .map(ApprovedNodeInfo::getFlowInstanceId)
                .collect(Collectors.toList());
        myRelatedIds.addAll(myPendedIdList);
        prmOpportunityQueryMap.put("myRelatedIds", myRelatedIds);
    }




    /**
     * 获取当前处理人map
     *
     * @param flowInstances
     * @return
     */
    private Map<String, String> getCurrentProcessor(List<String> flowInstances) {
        Map<String, String> currentProcessorMap = new HashMap<>();
        if (CollectionUtils.isEmpty(flowInstances)) {
            return currentProcessorMap;
        }
        for (List<String> flowInstancePartition : Lists.partition(flowInstances, CommonConstant.HUNDRED)) {
            try {
                Map<String, ApprovalProgressDTO> approvalProgressDTOMap = approvalFlowService.getFlowProgressByFlowInstanceIds(flowInstancePartition);
                for (Map.Entry<String, ApprovalProgressDTO> entry : approvalProgressDTOMap.entrySet()) {
                    String key = entry.getKey();
                    ApprovalProgressDTO approvalProgress = entry.getValue();
                    if (OpportunityConstant.APPROVAL_TASK_ACTIVE.equals(approvalProgress.getStatus())) {
                        List<ApprovalTask> approvalTasks = approvalProgress.getApprovalTaskList();
                        String currentProcessor = approvalTasks.stream()
                                .filter(ApprovalTask::judgeIsApproving)
                                .map(ApprovalTask::getApprover)
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.joining(","));
                        currentProcessorMap.put(key, currentProcessor);
                    }
                }
                getCurrentProcessorNameNo(currentProcessorMap);
            } catch (Exception e) {
                logger.error("查询当前处理人出错，Exception：{}", ExceptionMsgUtils.getStackTrace(e, 2000));
            }
        }
        return currentProcessorMap;
    }

    /**
     * 获取当前处理人姓名+工号
     *
     * @param currentProcessorMap
     */
    private void getCurrentProcessorNameNo(Map<String, String> currentProcessorMap) {
        try {
            List<String> currentProcessors = new ArrayList<>(currentProcessorMap.values()).stream()
                    .filter(StringUtils::isNotBlank)
                    .map(str -> str.split(","))
                    .flatMap(Stream::of)
                    .distinct()
                    .collect(Collectors.toList());
            List<PersonAndOrgInfoVO> currentProcessorInfos = PersonAndOrgInfoUtil.getEmpInfo(currentProcessors);
            Map<String, String> mapCurrentProcessorNameNos = currentProcessorInfos.stream()
                    .filter(Objects::nonNull)
                    .filter(personInfo -> StringUtils.isNotBlank(personInfo.getEmpNO()))
                    .collect(Collectors.toMap(PersonAndOrgInfoVO::getEmpNO, PersonAndOrgInfoVO::getEmpName));
            for (Map.Entry<String, String> entry : currentProcessorMap.entrySet()) {
                String processorEmpNo = entry.getValue();
                if (StringUtils.isBlank(processorEmpNo)){
                    continue;
                }
                List<String> processorEmpNos = Arrays.stream(processorEmpNo.split(","))
                        .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                String processorEmpNoValue = processorEmpNos.stream()
                        .map(str -> mapCurrentProcessorNameNos.getOrDefault(str, "") + str)
                        .collect(Collectors.joining(","));
                entry.setValue(processorEmpNoValue);
            }
        } catch (Exception e) {
            logger.error("设置当前处理人姓名工号出错：Exception：{}", ExceptionMsgUtils.getStackTrace(e, 2000));
        }
    }

    /**
     * 从获取的到的roleMap（角色map）中取出需要获取约束值的角色的roleId列表
     *
     * @param roleMap 角色Map
     * @return 需要获取约束值的角色的roleId列表
     */
    private List<String> setConstrainedRoleIdsFromRoleMap(Map<String, RoleVO> roleMap) {
        List<String> constrainedRoleIds = new ArrayList<>();
        for (String constrainRole : OpportunityRoleEnum.getContraintRoles()) {
            if (roleMap.containsKey(constrainRole)) {
                RoleVO roleVO = roleMap.get(constrainRole);
                constrainedRoleIds.add(String.valueOf(roleVO.getId()));
            }
        }
        return constrainedRoleIds;
    }

    /**
     * 查询相关权限约束如行业、组织，并返回当前登录人是否政企中国商机管理员角色。
     *
     * @param map 入参，内容包括empNo工号， 经过改函数后map里面会保存当前empNo所包含的相关约束值。
     * @return 是否政企中国商机管理员角色
     */
    private boolean setAuthConstraintsAndJudgeAdmin(Map<String, Object> map) throws ExecutionException, InterruptedException {
        List<String> constrainedOrgList = new ArrayList<>();
        List<String> constrainedIndustryList = new ArrayList<>();
        boolean isAdmin = false;
        CommonModuleIdEntity commonModuleIdEntity = new CommonModuleIdEntity();
        commonModuleIdEntity.setEmpidui(CommonUtils.getEmpNo());
        commonModuleIdEntity.setToken(CommonUtils.getAuthValue());
        logger.info("查询所属角色Map, getRoleMap({})", JSON.toJSONString(commonModuleIdEntity));
        Map<String, RoleVO> roleMap = uppAuthorityService.getRoleMap(commonModuleIdEntity);
        logger.info("getRoleMap returns:{}", roleMap);
        map.put(ROLE_MAP, roleMap);
        List<String> constrainedRoleIds = setConstrainedRoleIdsFromRoleMap(roleMap);
        // 政企中国商机管理员可以查询到除草稿状态外的所有商机报备单据
        if (roleMap.containsKey(OpportunityRoleEnum.GEC_OPPORTUNITY_ADMIN.getCode())) {
            isAdmin = true;
        } else if (CollectionUtils.isNotEmpty(constrainedRoleIds)) {
            CompletableFuture<List<String>> constrainedOrgListFuture = CompletableFutureWrapper.supplyAsync(() ->
                    uppAuthorityService.getAuthInfoByUserAndRoleId(constrainedRoleIds,
                            optyAuthConfigProperties.getOrgConstraintCode(), commonModuleIdEntity));
            CompletableFuture<List<String>> constrainedIndustryListFuture = CompletableFutureWrapper.supplyAsync(() ->
                    uppAuthorityService.getAuthInfoByUserAndRoleId(constrainedRoleIds,
                            OpportunityConstant.SUB_INDUSTRY_CONSTRAINT, commonModuleIdEntity));
            constrainedIndustryList = constrainedIndustryListFuture.get();
            constrainedOrgList = constrainedOrgListFuture.get();
        }
        map.put(IS_ADMIN, isAdmin);
        map.put(CONSTRAINED_ORG_LIST, constrainedOrgList);
        map.put(CONSTRAINED_INDUSTRY_LIST, constrainedIndustryList);
        logger.info("查询相关权限约束如行业、组织，并返回当前登录人是否政企中国商机管理员角色： returns:{}", map);
        return isAdmin;
    }

    /**
     * 设置商机列表一些字段的中文名称及操作按钮映射
     *
     * @param prmOpportunityPage 查询得到的商机列表
     * @param map            查询条件map
     * @throws Exception 异常信息
     */
    private void setNameValuesAndButton(List<PrmOpportunityVO> prmOpportunityPage, Map<String, Object> map) throws Exception {
        // 获取字典类型值
        Map<String, List<ComDictionaryMaintainVO>> mapList = comDictionaryMaintainService.queryByTypeList(OpportunityConstant.PRM_OPPORTUNITY_TYPES);
        List<String> deptNos = prmOpportunityPage.stream().map(PrmOpportunityVO::getDeptNo).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<String> opptyIds = prmOpportunityPage.stream().map(PrmOpportunityVO::getRowId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        // 根据新组织(ORG打头)编码去HR查询组织信息
        List<PersonAndOrgInfoVO> orgAndChildCompanyInfo = PersonAndOrgInfoUtil.getOrgAndChildCompanyInfo(deptNos);
        // 获取商机的主产品维护关系
        Map<String, Boolean> needMatainMainProdsMap = this.judgeNeedMaintainMainProds((boolean) map.get(IS_EXPORT), opptyIds, maintainMainProdsStartTime, null);
        // 从行业树获取行业列表
        List<AuthConstraintDTO> subIndustryList = prmService.getSubIndustryListWithNoException("Y");
        List<String> flowInstances = prmOpportunityPage.stream()
                .filter(this::statusHasProcessor)
                .map(PrmOpportunityVO::getFlowInstanceId)
                .collect(Collectors.toList());
        List<String> nonDraftOpportunityCodes = prmOpportunityPage.stream()
                .filter(opportunityVO -> !OptyStatusEnum.DRAFT.getCode().equals(opportunityVO.getStatusCd()))
                .map(PrmOpportunityVO::getOptyCd).collect(Collectors.toList());
        // 商机状态 -- // 商机来源 -- // 授权状态 -- // 招标类型 -- // 当前阶段 -- // 赢率
        List<ComDictionaryMaintainVO> statusCdList = mapList.get(OpportunityConstant.PRM_OPPORTUNITY_STATUS);
        List<ComDictionaryMaintainVO> dataSourceList = mapList.get(OpportunityConstant.SOURCE_OF_OPPORTUNITY);
        List<ComDictionaryMaintainVO> statusAuthList = mapList.get(OpportunityConstant.AUTHORIZATION_STATUS);
        List<ComDictionaryMaintainVO> tenderTypeList = mapList.get(OpportunityConstant.TYPE_FOR_TENDER_TYPE);
        List<ComDictionaryMaintainVO> currentPhaseTypeList = mapList.get(OpportunityConstant.CURRENT_PHASES_TYPE);
        List<ComDictionaryMaintainVO> winRateTypeList = mapList.get(OpportunityConstant.WIN_RATE_TYPE);
        boolean isZh = CommonConst.ZH_CN.equalsIgnoreCase(CommonUtils.getxLangId());
        Map<String, String> winRateTypeMap = winRateTypeList.stream()
                .collect(Collectors.toMap(ComDictionaryMaintainVO::getCode,
                        comDictionaryMaintainVO -> getDictName(comDictionaryMaintainVO, isZh)));
        Map<String, String> mapDataSource = dataSourceList.stream()
                .collect(Collectors.toMap(ComDictionaryMaintainVO::getCode,
                        comDictionaryMaintainVO -> getDictName(comDictionaryMaintainVO, isZh)));
        Map<String, String> mapStatusAuth = statusAuthList.stream()
                .collect(Collectors.toMap(ComDictionaryMaintainVO::getCode,
                        comDictionaryMaintainVO -> getDictName(comDictionaryMaintainVO, isZh)));
        Map<String, String> mapTenderType = tenderTypeList.stream()
                .collect(Collectors.toMap(ComDictionaryMaintainVO::getCode,
                        comDictionaryMaintainVO -> getDictName(comDictionaryMaintainVO, isZh)));
        Map<String, String> mapCurrentPhaseType = currentPhaseTypeList.stream()
                .collect(Collectors.toMap(ComDictionaryMaintainVO::getCode,
                        comDictionaryMaintainVO -> getDictName(comDictionaryMaintainVO, isZh)));
        Map<String, String> mapStatusCd = statusCdList.stream()
                .collect(Collectors.toMap(ComDictionaryMaintainVO::getCode,
                        comDictionaryMaintainVO -> getDictName(comDictionaryMaintainVO, isZh)));
        Map<String, String> mapSubIndustry = subIndustryList.stream()
                .collect(Collectors.toMap(AuthConstraintDTO::getShowValue, AuthConstraintDTO::getShowName));
        Map<String, String> mapOrganizationNames = orgAndChildCompanyInfo.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(PersonAndOrgInfoVO::getHrOrgID, PersonAndOrgInfoVO::getHrOrgName));
        Map<String, String> mapCurrentProcessor = getCurrentProcessor(flowInstances);
        Map<String, ProjectAuthInfoDto> projectAuthInfoDtoMap = getProjectAuthInfoDtoMap(nonDraftOpportunityCodes);

        for (PrmOpportunityVO vo : prmOpportunityPage) {
            setOperationButtonAndExpectSignMoneyModify(vo, map, needMatainMainProdsMap.get(vo.getRowId()));
            // 设置商机来源字段中文名称
            vo.setDataSourceName(mapDataSource.getOrDefault(vo.getDataSource(), vo.getDataSource()));
            // 设置授权状态字段中文名称
            vo.setStatusAuthName(mapStatusAuth.getOrDefault(vo.getStatusAuth(), vo.getStatusAuth()));
            String defaultTrade = StringUtils.isNotBlank(vo.getFinalCustomerTradeChildCode()) ?
                    vo.getFinalCustomerTradeCode() + CommonConstant.MID_LINE + vo.getFinalCustomerTradeChildCode() : null;
            // 设置最终用户行业字段中文名称
            vo.setFinalCustomerTradeName(mapSubIndustry.getOrDefault(vo.getFinalCustomerTradeChildCode(), defaultTrade));
            // 设置投资方所在地字段中文名称
            vo.setDeptName(mapOrganizationNames.getOrDefault(vo.getDeptNo(), vo.getDeptNo()));
            // 设置商机状态字段中文名称
            vo.setStatusName(OptyStatusEnum.getStatusName(vo.getStatusCd(), CommonUtils.getxLangId()));
            // 设置当前阶段字段中文名称
            setProjectPhasesName(vo, mapCurrentPhaseType);
            // 设置招标类型字段中文名称
            setTendTypeName(vo, mapTenderType);
            // 设置当前处理人
            vo.setCurrentProcessor(mapCurrentProcessor.getOrDefault(vo.getFlowInstanceId(), getDefaultCurrentProcessor(vo)));
            // 设置赢率
            vo.setWinRateName(CommonMapUtil.SUCCESS_TATE_NAME_MAP.getOrDefault(vo.getWinRate(), vo.getWinRate()));
            // 设置项目授权信息
            setProjectAuthInfoToVo(vo, projectAuthInfoDtoMap, isZh);
        }
    }

    private Map<String, Boolean> judgeNeedMaintainMainProds(boolean isExport, List<String> opptyIds, String createdTimeStart, String createdTimeEnd) {
        // 导出时不需要维护主产品按钮
        if (Boolean.TRUE.equals(isExport)) {
            return Collections.emptyMap();
        }
        return iOpportunityService.judgeNeedMaintainMainProds(opptyIds, createdTimeStart, createdTimeEnd);
    }

    private void setMaintainMainProdButtonFlag(PrmOpportunityVO vo, boolean isAdmin, String empNo, Boolean needMaintainMainProdsFlag) {
        PrmOperationButtonVO buttonVO = Optional.ofNullable(vo.getOperationButton()).orElseGet(PrmOperationButtonVO::new);
        vo.setOperationButton(buttonVO);
        if (Boolean.TRUE.equals(needMaintainMainProdsFlag)) {
            buttonVO.setMaintainMainProdsFlag(isAdmin || StringUtils.equals(empNo, vo.getBusinessManagerId()));
        }
    }

    private String getDefaultCurrentProcessor(PrmOpportunityVO vo){
        String defaultCurrentProcessor = "";
        if (OptyStatusEnum.APPROVING.getCode().equals(vo.getStatusCd())
            && (null == vo.getLastAccStatus()
                    || LastAccStatusEnum.EFFECTIVE_CUSTOMER.getKey() > vo.getLastAccStatus())){
            defaultCurrentProcessor = vo.getBusinessManagerName() + vo.getBusinessManagerId();
        }
        return defaultCurrentProcessor;
    }

    private void setOperationButtonAndExpectSignMoneyModify(PrmOpportunityVO vo, Map<String, Object> map, Boolean needMaintainMainProdsFlag) {
        boolean isExport = (boolean) map.get(IS_EXPORT);
        // 设置操作按钮
        if (!isExport && vo.getOptyCd().startsWith(OPPORTUNITY_PREFIX)) {
            setOperationButton(vo, map, needMaintainMainProdsFlag);
        } else if (!vo.getOptyCd().startsWith(OPPORTUNITY_PREFIX)){
            // 老系统商机按钮不显示
            vo.setOperationButton(new PrmOperationButtonVO());
            if(OptyStatusEnum.TICKET_LOSS.getCode().equals(vo.getStatusCd())){
                vo.setStatusCd(OptyStatusEnum.OPTY_SUSPEND.getCode());
            }
            // 转换后无需转换金额单位
        }
    }

    @Override
    public List<AccountInfo> getCustomerInformationBatch(List<String> accountCodeList){
        List<AccountInfo> result = new ArrayList<>();
        try {
            List<AccountInfo> accountInfos = customerInfoService.getCustomerInformationBatch(accountCodeList);
            if(null != accountInfos){
                result = accountInfos;
            }
        }catch (Exception e){
            logger.error("查询商机列表,获取老商机渠道商/客户信息失败", e);
        }
        return result;
    }

    private void setProjectPhasesName(PrmOpportunityVO vo, Map<String, String> mapCurrentPhaseType){
        if(StringUtils.isNotBlank(vo.getProjectPhasesCode())) {
            vo.setProjectPhasesName(mapCurrentPhaseType.getOrDefault(vo.getProjectPhasesCode(), vo.getProjectPhasesCode()));
        }else{
            vo.setProjectPhasesName(lovService.getlovVal(OppSysConst.OPTY_PHASE, vo.getOldProjectPhasesCode()));
        }
    }

    private void setTendTypeName(PrmOpportunityVO vo, Map<String, String> mapTenderType){
        if(StringUtils.isNotBlank(vo.getTendTypeCode())) {
            vo.setTendTypeName(mapTenderType.getOrDefault(CommonMapUtil.BIDDING_TYPE_MAP.inverse().getOrDefault(vo.getTendTypeCode(), vo.getTendTypeCode()), vo.getTendTypeCode()));
        }else{
            vo.setTendTypeName(lovService.getlovVal(OppSysConst.ZTE_INVITE_TYPE, vo.getOldTendTypeCode()));
        }
    }

    private Map<String, ProjectAuthInfoDto> getProjectAuthInfoDtoMap(List<String> opportunityCodes){
        Map<String, ProjectAuthInfoDto> projectAuthInfoMap = new HashMap<>(opportunityCodes.size());
        try {
            projectAuthInfoMap = ProjectAuthorizationUtils.getProjectAuthInfo(opportunityCodes);
        }catch (Exception e){
            logger.error("Prm侧查询商机列表获取授权信息失败", e);
        }
        return projectAuthInfoMap;
    }

    private void setProjectAuthInfoToVo(PrmOpportunityVO vo, Map<String, ProjectAuthInfoDto> projectAuthInfoDtoMap, boolean isZh){
        if(null == projectAuthInfoDtoMap){
            return;
        }
        ProjectAuthInfoDto projectAuthInfoDto = projectAuthInfoDtoMap.get(vo.getOptyCd());
        if(Objects.nonNull(projectAuthInfoDto)) {
            ProjectAuthStatusEnum projectAuthStatusEnum = projectAuthInfoDto.getStatusAuth();
            if (Objects.nonNull(projectAuthStatusEnum)) {
                String statusAuthName = isZh ? projectAuthStatusEnum.getCnName() : projectAuthStatusEnum.getEnName();
                vo.setStatusAuth(projectAuthStatusEnum.getCode());
                vo.setStatusAuthName(statusAuthName);
            }
            vo.setNumberOfAuthorizations(projectAuthInfoDto.getNumberOfAuthorizations());
            vo.setAuthIdAndStatus(projectAuthInfoDto.getAuthIdAndStatus());
        }else{
            vo.setNumberOfAuthorizations(0L);
            vo.setStatusAuth(ProjectAuthStatusEnum.NOT_APPLIED.getCode());
            String statusAuthName = isZh ? ProjectAuthStatusEnum.NOT_APPLIED.getCnName() : ProjectAuthStatusEnum.NOT_APPLIED.getEnName();
            vo.setStatusAuthName(statusAuthName);
        }
    }


    private String getDictName(ComDictionaryMaintainVO comDictionaryMaintainVO, boolean isZh){
        return isZh ? comDictionaryMaintainVO.getChineseName() : comDictionaryMaintainVO.getEnglishName();
    }

    /**
     * 筛选单据状态是否包含当前处理人
     *
     * @param prmOpportunityVO
     * @return
     */
    private boolean statusHasProcessor(PrmOpportunityVO prmOpportunityVO) {
        return Arrays.asList(OptyStatusEnum.APPROVING.getCode(),
                             OptyStatusEnum.OPTY_SUSPEND.getCode()).contains(prmOpportunityVO.getStatusCd());
    }

    /**
     * 设置操作按钮
     *
     * @param vo      商机信息VO
     * @param prmQueryConditionMap 决策条件map
     */
    private void setOperationButton(PrmOpportunityVO vo, Map<String, Object> prmQueryConditionMap, Boolean needMaintainMainProdsFlag) {

        PrmOperationButtonVO button = new PrmOperationButtonVO();
        Map<String, Boolean> map = setConditionMap(vo, prmQueryConditionMap);
        // 设置 “查看月报” 按钮
         setReadReportFlag(vo, button);
        if (OptyStatusEnum.DRAFT.getCode().equals(vo.getStatusCd())) {
            setDraftStatusOperator(map, button, vo);
        } else if (OptyStatusEnum.APPROVING.getCode().equals(vo.getStatusCd())) {
            setReportedApprovalingStatusOperator(map, button, vo);
        } else if (OptyStatusEnum.OPTY_RENEWING.getCode().equals(vo.getStatusCd())) {
            setReportedSuccessStatusOperator(map, button, vo);
        } else if (OptyStatusEnum.OPTY_SUSPEND.getCode().equals(vo.getStatusCd())) {
            setCancelOrFailStatusOperator(map, button, vo);
        } else if (OptyStatusEnum.OPTY_TRANSFERRED.getCode().equals(vo.getStatusCd())) {
            setProjectSuccessStatusOperator(map, button, vo);
        }else if (OptyStatusEnum.OPTY_PROJ_SUBMIT.getCode().equals(vo.getStatusCd())){
            setProjectApprovingStatusOperator(map, button, vo);
        }
        vo.setOperationButton(button);
        // 设置维护主产品按钮的显示
        this.setMaintainMainProdButtonFlag(vo, map.get(IS_ADMIN), (String) prmQueryConditionMap.get("empNo"), needMaintainMainProdsFlag);
    }

    private Map<String, Boolean> setConditionMap(PrmOpportunityVO vo, Map<String, Object> prmQueryConditionMap){
        Map<String, Boolean> map = new HashMap<>();
        map.put(IS_OFFICE_BUSINESS_MANAGER, false);
        map.put(IS_OFFICE_INDUSTRY_SECTION_CHIEF, false);
        map.put(IS_ADMIN, false);
        map.put(IS_CHANNEL_DIRECTOR, false);
        boolean isCreatedBy = false;
        if (StringUtils.isNotBlank(vo.getCreatedBy())) {
            isCreatedBy = vo.getCreatedBy().equals(CommonUtils.getEmpNo());
        }
        boolean isBusinessManager = false;
        if (StringUtils.isNotBlank(vo.getBusinessManagerId())) {
            isBusinessManager = vo.getBusinessManagerId().equals(CommonUtils.getEmpNo());
        }
        Map<String, RoleVO> roleMap = (Map<String, RoleVO>) prmQueryConditionMap.get(ROLE_MAP);
        if (null != roleMap){
            if (roleMap.containsKey(OpportunityRoleEnum.GEC_REPRESENTATIVE_OFFICE_BUSINESS_MANAGER.getCode())){
                map.put(IS_OFFICE_BUSINESS_MANAGER, true);
            }
            if (roleMap.containsKey(OpportunityRoleEnum.GEC_DIRECTOR_OFFICE_INDUSTRY_SECTION_CHIEF.getCode())){
                map.put(IS_OFFICE_INDUSTRY_SECTION_CHIEF, true);
            }
            if (roleMap.containsKey(OpportunityRoleEnum.GEC_CHANNEL_DIRECTOR_OF_CHANNEL_BUSINESS_DEPARTMENT.getCode())){
                map.put(IS_CHANNEL_DIRECTOR, true);
            }
        }
        boolean isAdmin = (boolean) prmQueryConditionMap.get(IS_ADMIN);
        map.put(IS_CREATED_BY, isCreatedBy);
        map.put(IS_BUSINESS_MANAGER, isBusinessManager);
        map.put(IS_ADMIN, isAdmin);
        return map;
    }

    /**
     * 查看月报的按钮逻辑控制
     *
     * @param vo
     * @param button
     */
    private void setReadReportFlag(PrmOpportunityVO vo, PrmOperationButtonVO button) {
        if ((OptyStatusEnum.OPTY_RENEWING.getCode().equals(vo.getStatusCd())
             || OptyStatusEnum.OPTY_SUSPEND.getCode().equals(vo.getStatusCd())
             || OptyStatusEnum.OPTY_PROJ_SUBMIT.getCode().equals(vo.getStatusCd())
             || OptyStatusEnum.OPTY_TRANSFERRED.getCode().equals(vo.getStatusCd())
             || OptyStatusEnum.TICKET_WIN.getCode().equals(vo.getStatusCd())
             || OptyStatusEnum.TICKET_LOSS.getCode().equals(vo.getStatusCd())
             || OptyStatusEnum.OPTY_CLOSED.getCode().equals(vo.getStatusCd()))
                && ReportStatusEnum.FILLED.getValue().equals(vo.getReportStatus())) {
            button.setReadReportFlag(Boolean.TRUE);
        }
    }

    /**
     * 草稿状态的按钮逻辑控制
     *
     * @param map    权限相关的Map，如isAdmin是否管理员、IS_CREATED_BY是否创建人等
     * @param button 按钮VO
     * @param vo     商机信息VO（可能有用到、也可能没用到）
     */
    private void setDraftStatusOperator(Map<String, Boolean> map, PrmOperationButtonVO button, PrmOpportunityVO vo) {
        if (Boolean.TRUE.equals(map.get(IS_CREATED_BY))) {
            // 修改
            button.setEditFlag(Boolean.TRUE);
            // 删除
            button.setDeleteFlag(Boolean.TRUE);
        }
    }

    /**
     * 报备审批中状态下单据的按钮逻辑控制
     *
     * @param map    权限相关的Map，如isAdmin是否管理员、IS_CREATED_BY是否创建人等
     * @param button 按钮VO
     * @param vo     商机信息VO（可能有用到、也可能没用到）
     */
    private void setReportedApprovalingStatusOperator(Map<String, Boolean> map, PrmOperationButtonVO button, PrmOpportunityVO vo) {
        if (Boolean.TRUE.equals(map.get(IS_CREATED_BY)) || Boolean.TRUE.equals(map.get(IS_ADMIN))) {
            // 撤销
            button.setCancelFlag(Boolean.TRUE);
            // 催办
            button.setUrgeFlag(Boolean.TRUE);
        }

        if (Boolean.TRUE.equals(map.get(IS_BUSINESS_MANAGER))) {
            // 催办
            button.setUrgeFlag(Boolean.TRUE);
        }
    }

    /**
     * 报备成功状态的按钮逻辑控制
     *
     * @param map    权限相关的Map，如isAdmin是否管理员、IS_CREATED_BY是否创建人等
     * @param button 按钮VO
     * @param vo     商机信息VO（可能有用到、也可能没用到）
     */
    private void setReportedSuccessStatusOperator(Map<String, Boolean> map, PrmOperationButtonVO button, PrmOpportunityVO vo) {
        boolean isUpdateStatus = (OpportunityCurrentStatus.WINNING.getValue().equals(vo.getCurrentStatus())
                && SourceOfOpportunityEnum.CHANNEL_FILING.getValue().equals(vo.getDataSource()))
                || SourceOfOpportunityEnum.ZTE_SELF_BUILT.getValue().equals(vo.getDataSource());
        if (Boolean.TRUE.equals(map.get(IS_BUSINESS_MANAGER)) || Boolean.TRUE.equals(map.get(IS_ADMIN))) {
            if (isUpdateStatus) {
                // 更新状态
                button.setUpdateStatusFlag(Boolean.TRUE);
            }
            if (OpportunityConstant.ICHANNEL.equalsIgnoreCase(vo.getDataSource())
                    && OpportunityConstant.MAX_FAST_PROJECT_TRANSFER_MONEY.compareTo(vo.getTotalAmount()) > 0) {
                    // 金额300万以内，可以快速立项
                    button.setFastTransferProjectFlag(Boolean.TRUE);
            }else {
                button.setTransferProjectFlag(Boolean.TRUE);
            }
        }

        setAppointButton(map, button);
    }

    /**
     * 取消和报备失效状态的按钮逻辑控制
     *
     * @param map    权限相关的Map，如isAdmin是否管理员、IS_CREATED_BY是否创建人等
     * @param button 按钮VO
     * @param vo     商机信息VO（可能有用到、也可能没用到）
     */
    private void setCancelOrFailStatusOperator(Map<String, Boolean> map, PrmOperationButtonVO button, PrmOpportunityVO vo) {
        //Boolean checkActiveCount = opportunityDetailService.checkActiveCount(vo.getRowId());
        boolean activeDisplayFlag = (Boolean.TRUE.equals(map.get(IS_CREATED_BY)) || Boolean.TRUE.equals(map.get(IS_ADMIN)))
                && (vo.getActiveCount() == null || vo.getActiveCount() < OpportunityConstant.MAX_ACTIVE_NUM);
        if (activeDisplayFlag) {
            // 激活：1个商机激活2次后，不再显示激活按钮
            button.setActivationFlag(Boolean.TRUE);
        }
    }

    /**
     * 立项成功状态的按钮逻辑控制
     *
     * @param map    权限相关的Map，如isAdmin是否管理员、IS_CREATED_BY是否创建人等
     * @param button 按钮VO
     * @param vo     商机信息VO（可能有用到、也可能没用到）
     */
    private void setProjectSuccessStatusOperator(Map<String, Boolean> map, PrmOperationButtonVO button, PrmOpportunityVO vo) {
        if (Boolean.TRUE.equals(map.get(IS_BUSINESS_MANAGER)) || Boolean.TRUE.equals(map.get(IS_ADMIN))) {
            button.setUpdateStatusFlag(Boolean.TRUE);
        }
        setAppointButton(map, button);
    }

    /**
     * 指派按钮
     * @param map
     * @param button
     */
    private void setAppointButton(Map<String, Boolean> map, PrmOperationButtonVO button){
        if (Boolean.TRUE.equals(map.get(IS_ADMIN))
                || Boolean.TRUE.equals(map.get(IS_OFFICE_INDUSTRY_SECTION_CHIEF))
                || Boolean.TRUE.equals(map.get(IS_OFFICE_BUSINESS_MANAGER))
                || Boolean.TRUE.equals(map.get(IS_CHANNEL_DIRECTOR))){
            button.setAppointFlag(Boolean.TRUE);
        }
    }

    /**
     * 立项审批中状态的按钮逻辑控制
     * @param map
     * @param button
     * @param vo
     */
    private void setProjectApprovingStatusOperator(Map<String, Boolean> map, PrmOperationButtonVO button, PrmOpportunityVO vo) {
        setAppointButton(map, button);
    }

        /**
         * 商机报备查询待我审批
         *
         * @param formData 查询map
         * @return
         * @throws Exception
         */
    @Override
    public PageRows<PrmOpportunityPendingVO> getOpportunityPendingPage(FormData<PrmOpportunityPendingDTO> formData) throws Exception {
        PageRows<PrmOpportunityPendingVO> returnPage = new PageRows<>();
        Map<String, Object> prmPendingParamMap = EntityTransformUtils.toMapParams(formData);
        ApprovingQueryParam approvingQueryParam = setPendingAdvanceQueryParam(prmPendingParamMap, OpportunityConstant.PENGDING_ADVANCE_PARAM_SET);

        PageRows<ApprovingNodeInfo> approvingTasksByCondition;
        try {
            approvingQueryParam.setPageSize((long) FormDataHelpUtil.getPageSize(formData));
            approvingQueryParam.setPageNo((long) FormDataHelpUtil.getPageNum(formData));
            approvingTasksByCondition = approvalFlowService.getApprovingTasksByCondition(approvingQueryParam);
        } catch (Exception e) {
            logger.error("审批中心查询待我评审失败，Exception：{}", ExceptionMsgUtils.getStackTrace(e, 2000));
            throw new BusiException(RetCode.BUSINESSERROR_CODE, LocalMessageUtils.getMessage("pendingServerError"));
        }
        List<ApprovingNodeInfo> approvingNodeInfos = approvingTasksByCondition.getRows();
        List<PrmOpportunityPendingVO> resultPending = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(approvingNodeInfos)) {
            List<String> myPendingIdList = approvingNodeInfos.stream()
                    .map(ApprovingNodeInfo::getBusinessId).distinct()
                    .collect(Collectors.toList());
            List<PrmOpportunityPendingVO> pendingInfos = opportunityDao.getPrmPendingInfos(myPendingIdList);
            resultPending = setPendingNameValue(pendingInfos, approvingNodeInfos);
        }
        returnPage.setCurrent(FormDataHelpUtil.getPageNum(formData));
        returnPage.setTotal(approvingTasksByCondition.getTotal());
        returnPage.setRows(resultPending);
        return returnPage;
    }


    @Override
    public OpportunityInfo getOpportunity(String rowId) throws Exception {
        OpportunityInfo opportunity = iOpportunityService.getOpportunityInfo(rowId);
        OpportunityInfo.OpptyBaseInfo opptyBaseInfo = opportunity.getOpptyBaseInfo();
        if (opptyBaseInfo == null) {
            return opportunity;
        }
        opptyBaseInfo.setMonthReports(getMonthReport(rowId));

        Map<String, List<OppAuthInfo>> oppAuthInfosMap = getOppAuthInfosMap(opptyBaseInfo.getOptyCd());
        opptyBaseInfo.setOppAuthInfo(oppAuthInfosMap.getOrDefault(opptyBaseInfo.getOptyCd(), null));

        //设置项目code
        List<CrmProjectInfo> crmProjectInfos = crmProjectInfoService.queryProjectCode(opptyBaseInfo.getOptyCd());

        if (CollectionUtils.isNotEmpty(crmProjectInfos)) {
            CrmProjectInfo crmProjectInfo =  crmProjectInfos.get(0);
            opptyBaseInfo.setProjectCode(crmProjectInfo.getProjectCode());
            opptyBaseInfo.setProjectId(crmProjectInfo.getId());
        }

        // 转立项信息
        OpportunityInfo.TransferProjectInfo transferProjectInfo = opportunity.getTransferProjectInfo();
        List<OpportunityInfo.OpptyProductInfo> opptyProductInfos = Lists.newArrayList();
        Map<String, Object> params = Maps.newHashMap();
        params.put("opptyId", rowId);
        params.put("businessType", "transferProject");
        List<OpportunityProduct> products = opportunityProductDao.getList(params);
        for (OpportunityProduct product : products) {
            OpportunityInfo.OpptyProductInfo opptyProductInfo = new OpportunityInfo.OpptyProductInfo();
            BeanUtils.copyProperties(product, opptyProductInfo);
            opptyProductInfos.add(opptyProductInfo);
        }
        transferProjectInfo.setOpptyProductInfos(opptyProductInfos);


        return opportunity;
    }

    private Map<String, List<OppAuthInfo>> getOppAuthInfosMap(String optyCd) {
        Map<String, List<OppAuthInfo>> oppAuthInfosMap = new HashMap<>();
        try {
            oppAuthInfosMap = ProjectAuthorizationUtils.getProjectAuthInfoMap(Collections.singletonList(optyCd));
        }catch (Exception e){
            logger.error("商机详情获取授权信息失败,商机编号:{}", optyCd, e);
        }
        return oppAuthInfosMap;
    }

    private List<OpportunityInfo.MonthReport> getMonthReport(String rowId){
        Map<String, Object> params = Maps.newHashMap();
        params.put("optyId", rowId);
        params.put("enabledFlag", "Y");
        params.put("businessType", MONTH_REPORT_UPDATE_STATE);
        List<OpportunityInfo.MonthReport> monthReports = new ArrayList<>();
        OpportunityInfo.MonthReport monthReport = new OpportunityInfo.MonthReport();
        List<SMonthReport> smonthReports = sMonthReportDao.getList(params);
        if (CollectionUtils.isNotEmpty(smonthReports)) {
            List<ComDictionaryMaintainVO> dicts = comDictionaryMaintainDao
                    .queryByTypeList(Lists.newArrayList(TICKET_WIN_REASON, CLOSED_REASON, CANCEL_REASON));
            for (SMonthReport smonthReport : smonthReports) {
                monthReport.setId(smonthReport.getId());
                monthReport.setOptyId(smonthReport.getOptyId());
                monthReport.setMemo(smonthReport.getMemo());
                monthReport.setReasonCode(smonthReport.getReasonCode());
                monthReport.setReportMonth(smonthReport.getReportMonth());
                String name = DictUtils.getName(smonthReport.getReasonCode(), dicts);
                if (name != null && name.contains(";")) {
                    monthReport.setReason(name.split(";")[0]);
                    monthReport.setDirections(name.split(";")[1]);
                }
                monthReports.add(monthReport);
            }
        }
        return monthReports;
    }

    @Autowired
    private IBmtUcsService iBmtUcsService;

    /**
     * 商机转立项
     *
     * @param entity
     */
    @Override
    public ConversionProjectOrdinary transferProject(OpportunityTransferredProjectDTO entity) throws Exception {
        // 校验是否为立项审批中
        checkTransferProject(entity.getRowId());

        OpportunityDetail detail = opportunityDetailDao.get(entity.getRowId());

        // 调用项目接口完成转立项
        ConversionProjectOrdinary result = sendOpportunityTransferProject(setTransferProjectInfo(entity, detail), opportunityTransferUrl);

        if (OpportunityConstant.SUCCESS_MSG.equalsIgnoreCase(result.getStatus())) {
            // 保存商机转立项信息
            saveTransferProjectInfo(entity);
        }
        return result;
    }

    /**
     * 商机快速转立项
     *
     * @param entity
     */
    @Override
    public ConversionProjectOrdinary fastTransferProject(OpportunityTransferredProjectDTO entity) throws Exception {
        // 校验是否为渠道报备
        if (!OpportunityConstant.ICHANNEL.equalsIgnoreCase(entity.getDataSource())) {
            // 非渠道报备不能快速转立项
            throw new BusinessRuntimeException(PrmRetCode.BUSINESSERROR_CODE, CommonRetCode.OPPORTUNITY_TYPE_ERROR);
        }
        // 校验是否为立项审批中
        checkTransferProject(entity.getRowId());
        // 校验附件是否已上传
        List<DocumentList> documentLists = Lists.newArrayList();
        checkFileUpload(entity, documentLists);
        // 组装参数
        OpportunityDetail detail = opportunityDetailDao.get(entity.getRowId());
        JSONObject jsonObject = setTransferProjectInfo(entity, detail);
        if(StringUtils.isNotBlank(detail.getLastAccCode())){
            entity.setLastAccId(detail.getLastAccCode());
        }
        String businessTypeCd = entity.getBusinessTypeCd();
        // 商机业务范围code转换
        setBusinessType(entity);
        //项目一级分类(默认为当期类项目)
        jsonObject.put("firstCategoryCode", OpportunityConstant.PROJECT_FIRST_CATEGORY);
        //项目二级分类(商机的业务范围)
        jsonObject.put("secondCategoryCode", entity.getBusinessTypeCd());
        //项目执行地
        StringBuffer buffer = new StringBuffer();
        buffer.append(entity.getNationalAreaId());
        if (StringUtils.isNotEmpty(entity.getNationalAreaProvinceId())) {
            buffer.append(CommonConstant.COMMA + entity.getNationalAreaProvinceId());
        }
        if (StringUtils.isNotEmpty(entity.getNationalAreaCityId())) {
            buffer.append(CommonConstant.COMMA + entity.getNationalAreaCityId());
        }
        jsonObject.put("projectExecutionPlaceCode", buffer.toString());
        // 项目文档
        jsonObject.put("documentList", documentLists);

        //团队成员
        jsonObject.put("projectTeam", getProjectTeam(detail, entity));
        //设置新业务
        setNewBusiness(jsonObject);
        // 调用项目接口完成快速转立项
        ConversionProjectOrdinary result = sendOpportunityTransferProject(jsonObject, opportunityFastTransferUrl);

        if (OpportunityConstant.SUCCESS_MSG.equalsIgnoreCase(result.getStatus())) {
            // 保存商机快速转立项信息
            entity.setBusinessTypeCd(businessTypeCd);
            saveTransferProjectInfo(entity);
        }
        return result;
    }

    /**
     * 获取团队成员
     * @param detail
     * @param entity
     * @return
     */
    private ProjectTeam getProjectTeam(OpportunityDetail detail, OpportunityTransferredProjectDTO entity){
        ProjectTeam projectTeam = new ProjectTeam();
        List<CoreTeamMember> coreTeamMemberList = new ArrayList<>();
        List<GuidanceCommitteeMembe> guidanceCommitteeMemberList = new ArrayList<>();
        //应项目团队张倍要求，将商机负责人（中兴业务经理）传入核心团队成员
        CoreTeamMember coreTeamMember = new CoreTeamMember();
        coreTeamMember.setRole(OpportunityConstant.PROJECT_MANAGER);
        coreTeamMember.setEmpId(detail.getBusinessManagerId());
        coreTeamMemberList.add(coreTeamMember);
        // 项目指委会主任
        GuidanceCommitteeMembe guidTeamMemberPsc = new GuidanceCommitteeMembe();
        guidTeamMemberPsc.setRole(OpportunityConstant.DIRECTOR_OF_PSC);
        guidTeamMemberPsc.setEmpId(entity.getDirectorOfPsc());
        guidanceCommitteeMemberList.add(guidTeamMemberPsc);
        projectTeam.setCoreTeamMemberList(coreTeamMemberList);
        projectTeam.setGuidanceCommitteeMemberList(guidanceCommitteeMemberList);
        return projectTeam;
    }

    /**
     * 商机转立项跳转验证
     *
     * @param rowId
     */
    @Override
    public ConversionProjectOrdinary transferProjectValidate(String rowId) throws Exception {
        // 校验是否为立项审批中
        checkTransferProject(rowId);
        List<OpportunityProduct> opportunityProducts = getOpportunityProducts(rowId);
        if (CollectionUtils.isNotEmpty(opportunityProducts)) {
            // 已进行转立项操作，组装数据，直接再次转立项
            OpportunityTransferredProjectDTO entity = new OpportunityTransferredProjectDTO();
            Opportunity opportunity = opportunityDao.get(rowId);
            OpportunityDetail detail = opportunityDetailDao.get(rowId);
            detail.setLastAccId(detail.getLastAccCode());
            opportunity.setBusinessTypeCd(CommonMapUtil.DELIVERY_INTERFACE_MAP.inverse().getOrDefault(opportunity.getBusinessTypeCd(), opportunity.getBusinessTypeCd()));
            BeanUtils.copyProperties(opportunity, entity);
            BeanUtils.copyProperties(detail, entity);
            // 普通立项不需要传商机名称
            entity.setAttrib46(null);
            List<OpportunityProductDTO> productList = Lists.newArrayList();
            for (OpportunityProduct product : opportunityProducts) {
                OpportunityProductDTO productDTO = new OpportunityProductDTO();
                BeanUtils.copyProperties(product, productDTO);
                productList.add(productDTO);
            }
            entity.setProductList(productList);
            entity.setSignCustomerCode(opportunity.getPrDeptOuId());
            // 转立项
            return transferProject(entity);
        }
        return new ConversionProjectOrdinary();
    }

    private JSONObject setTransferProjectInfo(OpportunityTransferredProjectDTO entity, OpportunityDetail detail) {
        Opportunity opportunity = opportunityDao.get(detail.getRowId());
        JSONObject jsonObject = new JSONObject();
        // 商机编码
        jsonObject.put("opportunityCode", entity.getOptyCd());
        // 商机ID
        jsonObject.put("opportunityId", entity.getRowId());
        // 项目申请人ID
        jsonObject.put("projectApplicationAcountID", CommonUtils.getEmpNo());
        // 是否来源渠道报备
        jsonObject.put("channelReporting", OpportunityConstant.ICHANNEL.equalsIgnoreCase(entity.getDataSource()));
        // 最终用途
        jsonObject.put("finalUsage", entity.getFinalUsage());
        // 具体用途描述
        jsonObject.put("usageDesc", entity.getSpecificUsageDesc());
        // 销售模式
        if (StringUtils.equals(CommonUtils.getSubTenantId(), HeaderNameConst.DEFAULT_X_TENANT_ID)) {
            jsonObject.put("salesType", entity.getSalesType());
        }
        // 产品订货信息
        jsonObject.put("productList", getProductOrderInfos(entity));
        //客户名称Code
        jsonObject.put("customerCode", entity.getSignCustomerCode());
        //最终用户Code
        jsonObject.put("finalCustomerCode", detail.getLastAccCode());
        // 行业
        jsonObject.put("industryCode", detail.getFinalCustomerParentTrade());
        // 子行业
        jsonObject.put("subIndustryCode", detail.getFinalCustomerChildTrade());
        // 经销商名称Code
        jsonObject.put("dealerCode", StringUtils.isNotBlank(entity.getCustomerCode()) ?
                entity.getCustomerCode() : detail.getCrmCustomerCode());
        // 是否融资类项目
        jsonObject.put("financedFlag", false);
        // 设置归属单位
        jsonObject.put("projectDepartCode", detail.getDeptNo());
        // 客户属性
        jsonObject.put("customerType", detail.getAccountAttribute());
        // 招标类型
        String tenderTypeName = comDictionaryMaintainService.getTenderTypeName(detail.getTenderTypeCode());
        jsonObject.put("bideType", tenderTypeName);
        //商机名称
        jsonObject.put("projectName", entity.getAttrib46());

        //增加赢率到立项中
        jsonObject.put("winRateType", detail.getWinRate());
        // 线索编号——老商机未传
        // 增加细分市场到立项中
        jsonObject.put("optyAttribute",detail.getOptyAttribute());
        // 增加是否新业务到立项中
        jsonObject.put("isNewBusiness",opportunity.getIsNewBusiness());
        return jsonObject;
    }


    /**
     * 商机导出
     *
     * @param form
     * @param response
     */
    @Override
    public void exportOpportunityList(FormData<PrmOpportunityQueryDTO> form, HttpServletResponse response) throws Exception {
        if( null == form){
            return;
        }
        List<ComDictionaryMaintainVO> dictionaryMaintainVOS = comDictionaryMaintainService.queryByType(ComDictionaryMaintainConsts.PRM_EXPORT_OPPORTUNITY_TITLE);
        LinkedHashMap<String, String> titleCodeToName = new LinkedHashMap<>();
        String langId = CommonUtils.getxLangId();
        boolean isEn = CommonConst.EN_US.equalsIgnoreCase(langId);
        dictionaryMaintainVOS.stream().forEach(x -> {
            titleCodeToName.put(x.getCode(), isEn ? x.getEnglishName() : x.getChineseName());
        });

        //code:商机导出
        Map<String, Object> prmOpportunityQueryMap = setPrmOpportunityQueryMap(form, Boolean.TRUE);
        List<PrmOpportunityVO> rows = getPrmOpportunityConditionally(prmOpportunityQueryMap);
        if (CollectionUtils.isNotEmpty(rows)) {
            setNameValuesAndButton(rows, prmOpportunityQueryMap);
        }
        List<String> deptNos = rows.stream().map(PrmOpportunityVO::getDeptNo).distinct().collect(Collectors.toList());
        // 根据新组织(ORG打头)编码去HR查询组织信息
        List<PersonAndOrgInfoVO> orgList = PersonAndOrgInfoUtil.getOrgAndChildCompanyInfo(deptNos);
        Map<String, String> orgIdToOrgNamePath = orgList.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(PersonAndOrgInfoVO::getHrOrgID, PersonAndOrgInfoVO::getHrOrgNamePath, (v1, v2) -> v2));
        //被激活的商机id映射到激活的商机实体
        Map<String, List<PrmOpportunityVO>> fromActiverIdToEntity = rows.stream()
                .filter(Objects::nonNull)
                .filter(x -> CommonConstant.COMMON_FLAG_Y.equalsIgnoreCase(x.getFromActiveFlag())
                        && StringUtils.isNotBlank(x.getFromActiveOpty()))
                .collect(Collectors.groupingBy(PrmOpportunityVO::getFromActiveOpty));
        List<ExportOppotrunityVO> exportOppotrunitys = new ArrayList<>();
        rows.stream().forEach(x -> {
            ExportOppotrunityVO exportOppotrunityVO = prmOpportunityVOTransToExportOppotrunityVO(x, langId, orgIdToOrgNamePath, fromActiverIdToEntity);
            exportOppotrunitys.add(exportOppotrunityVO);
        });
        ExportExcel exportExcel = new ExportListObjectExcel<>(titleCodeToName, exportOppotrunitys, LocalMessageUtils.getMessage("business.opportunity.export") + System.currentTimeMillis());
        ExportExcelHandle excelHandle = new ExportExcelHandle(exportExcel);
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        excelHandle.process(response);
    }

    private ExportOppotrunityVO prmOpportunityVOTransToExportOppotrunityVO(PrmOpportunityVO x, String langId,  Map<String, String> orgIdToOrgNamePath, Map<String, List<PrmOpportunityVO>> fromActiverIdToEntity){
        boolean isEn = CommonConst.EN_US.equalsIgnoreCase(langId);
        ExportOppotrunityVO exportOppotrunityVO = new ExportOppotrunityVO();
        BeanUtils.copyProperties(x, exportOppotrunityVO);
        exportOppotrunityVO.setActiveCount(null == x.getActiveCount() ? "0" : String.valueOf(x.getActiveCount()));
        exportOppotrunityVO.setCreated(DateFormatUtil.dateFormat(x.getCreated()));
        exportOppotrunityVO.setExpiryDate(DateFormatUtil.dateFormat(x.getExpiryDate()));
        exportOppotrunityVO.setEstimatedBiddingTime(DateFormatUtil.dateFormat(x.getEstimatedBiddingTime()));
        exportOppotrunityVO.setBiddingDeadline(DateFormatUtil.dateFormat(x.getBiddingDeadline()));
        exportOppotrunityVO.setFromActiveFlag(CommonConstant.EnableFlagEnum.getMsgByLang(x.getFromActiveFlag(), langId));
        boolean isSelfUse = (StringUtils.isNotBlank(x.getFinalCustomerId()) && x.getFinalCustomerId().equals(x.getCrmCustomerCode())) || (StringUtils.isNotBlank(x.getFinalCustomerName()) && x.getFinalCustomerName().equals(x.getChannelBusiness()));
        CommonConstant.EnableFlagEnum isSelfUseEnableFlagEnum = isSelfUse ? CommonConstant.EnableFlagEnum.Y : CommonConstant.EnableFlagEnum.N;
        exportOppotrunityVO.setSelfUseFlag(isEn ? isSelfUseEnableFlagEnum.getEnMsg() : isSelfUseEnableFlagEnum.getZhMsg());
        /*
         * 组织全路径
         * 激活的报备编号/状态
         * */
        exportOppotrunityVO.setHrOrgNamePath(orgIdToOrgNamePath.getOrDefault(x.getDeptNo(), x.getDeptNo()));
        List<PrmOpportunityVO> activeOpportunitys = fromActiverIdToEntity.get(x.getRowId());
        if (CollectionUtils.isNotEmpty(activeOpportunitys)) {
            exportOppotrunityVO.setActivatedIdAndStatus(activeOpportunitys.stream().map(y -> y.getOptyCd() + "/" + y.getStatusName()).collect(Collectors.joining(";")));
        }
        // 处理中兴业务经理+工号
        exportOppotrunityVO.setBusinessManagerName(x.getBusinessManagerName() + x.getBusinessManagerId());
        return exportOppotrunityVO;
    }


    private List<ProductOrderForecasting> getProductOrderInfos(OpportunityTransferredProjectDTO entity) {

        List<ProductOrderForecasting> productList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(entity.getProductList())) {
            List<OpportunityProductDTO> productDTOList = entity.getProductList();
            for (OpportunityProductDTO product : productDTOList) {
                ProductOrderForecasting productOrderForecasting = new ProductOrderForecasting();
                // 项目主产品标识
                if (CommonConst.Y.equals(product.getZteMainProduct())) {
                    productOrderForecasting.setMainProductFlag(true);
                } else {
                    productOrderForecasting.setMainProductFlag(false);
                }
                // 体系内部分类Code
                productOrderForecasting.setSystemInternalClassificationCode(product.getProdLv1Name());
                // 大产品线Code
                productOrderForecasting.setMajorProductLineCode(product.getProdLv2Id());
                // 大产品线名称
                productOrderForecasting.setMajorProductLineName(product.getProdLv2Name());
                // 产品线Code
                productOrderForecasting.setProductLineCode(product.getProdLv21Id());
                // 产品线名称
                productOrderForecasting.setProductLineName(product.getProdLv21Name());
                // 产品大类Code
                productOrderForecasting.setProductMajorClassCode(product.getProdLv3Id());
                // 产品大类名称
                productOrderForecasting.setProductMajorClassName(product.getProdLv4Name());
                // 产品小类Code
                productOrderForecasting.setProductSubClassCode(product.getProdLv4Id());
                // 产品小类名称
                productOrderForecasting.setProductSubClassName(product.getProdLv4Name());
                // 预计签单时间
                productOrderForecasting.setEstimateSignDate(dateToString(product.getForSignDate()));
                // 预计签单金额（万）
                productOrderForecasting.setEstimateSignAmount(product.getProductAmount());
                // 赢率
                productOrderForecasting.setSuccessRate(entity.getWinRate());
                productList.add(productOrderForecasting);
            }
        }
        return productList;
    }

    /**
     * 日期转string
     *
     * @param date
     */
    private String dateToString(Date date) {
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return localDateTime.format(DateTimeFormatter.ofPattern(OpportunityConstant.DATEFORMAT_YYYYMMDD));
    }

    /**
     * 商机业务范围code转换
     *
     * @param entity
     */
    private void setBusinessType(OpportunityTransferredProjectDTO entity) {
        if (OpportunityConstant.SYSTEM.equals(entity.getBusinessTypeCd())) {
            entity.setBusinessTypeCd("40");
        } else if (OpportunityConstant.SYSTEM_SERVICE.equals(entity.getBusinessTypeCd())) {
            entity.setBusinessTypeCd("10");
        } else if (OpportunityConstant.SYSTEM_ENGINEE_SERVICE.equals(entity.getBusinessTypeCd())) {
            entity.setBusinessTypeCd("70");
        } else if (OpportunityConstant.SYSTEM_EXCLUDE_ENGINEE.equals(entity.getBusinessTypeCd())) {
            entity.setBusinessTypeCd("50");
        } else if (OpportunityConstant.SERVICE.equals(entity.getBusinessTypeCd())) {
            entity.setBusinessTypeCd("30");
        }
    }

    private void checkTransferProject(String rowId) {
        Opportunity opportunity = opportunityDao.get(rowId);
        opportunity.setBusinessTypeCd(CommonMapUtil.DELIVERY_INTERFACE_MAP.inverse().getOrDefault(opportunity.getBusinessTypeCd(), opportunity.getBusinessTypeCd()));
        if (OptyStatusEnum.OPTY_PROJ_SUBMIT.getCode().equalsIgnoreCase(opportunity.getStatusCd()) ||
            OptyStatusEnum.OPTY_TRANSFERRED.getCode().equalsIgnoreCase(opportunity.getStatusCd())) {
            // 重复转立项
            throw new BusinessRuntimeException(PrmRetCode.BUSINESSERROR_CODE, CommonRetCode.HAS_TRANSFER_PROJECT);
        }
        if (!OptyStatusEnum.OPTY_RENEWING.getCode().equalsIgnoreCase(opportunity.getStatusCd())) {
            // 状态不是报备成功
            throw new BusinessRuntimeException(PrmRetCode.BUSINESSERROR_CODE, "Opportunity is not reported success!");
        }
    }

    private List<OpportunityProduct> getOpportunityProducts(String rowId) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("opptyId", rowId);
        map.put("businessType", OpportunityConstant.TRANSFER_PROJECT);
        return opportunityProductDao.getList(map);
    }

    private void checkFileUpload(OpportunityTransferredProjectDTO entity, List<DocumentList> documentLists) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("billId", entity.getRowId());
        List<ComUploadFile> fileInfos = comUploadFileDao.getList(map);
        if (CollectionUtils.isEmpty(fileInfos)) {
            // 附件信息为空
            return;
        }
        // 线程安全
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(OpportunityConstant.DATE_FORMAT_YYYY_MM_DD);
        fileInfos.stream().forEach(e -> {
            DocumentList document = new DocumentList();
            document.setDocumentName(e.getDocName());
            document.setDocKey(e.getDmeKey());
            document.setCreator(e.getCreatedBy());
            LocalDateTime ldt = LocalDateTime.ofInstant(e.getCreatedDate().toInstant(), ZoneId.systemDefault());
            formatter.format(ldt);
            document.setCreateTime(formatter.format(ldt));
            documentLists.add(document);
        });
    }

    /**
     * 保存商机转立项信息
     */
    @Transactional(propagation = Propagation.SUPPORTS, isolation = Isolation.READ_UNCOMMITTED, rollbackFor = Exception.class)
    public void saveTransferProjectInfo(OpportunityTransferredProjectDTO entity) {
        Date now = new Date();
        // 保存商机业务范围及客户名称编码
        if (StringUtils.isNotBlank(entity.getBusinessTypeCd()) || StringUtils.isNotBlank(entity.getSignCustomerCode())) {
            Opportunity opportunity = new Opportunity();
            opportunity.setRowId(entity.getRowId());
            opportunity.setBusinessTypeCd(entity.getBusinessTypeCd());
            opportunity.setPrDeptOuId(entity.getSignCustomerCode());
            opportunity.setTransferToProjectTime(now);
            sOpportunityRepository.updateById(LcapConverterUtil.buildSopty(opportunity, null));
        }

        OpportunityDetail detail = OpportunityDetailMapper.INSTANCE.transTransferredProjectToOpportunityDetail(entity);
        detail.setWinRate(entity.getWinRate());
        // 保存详情数据
//        opportunityDetailDao.update(detail);
        opportunityInfoServiceImpl.getCustomerId(detail,CommonUtils.getSysGlobalConstVo());
        SOptyXBO sOptyXBO = LcapConverterUtil.buildSoptyx(detail);

        sOptyXDao.updateById(sOptyXBO);

        List<OpportunityProduct> opportunityProducts = Lists.newArrayList();
        List<OpportunityProductDTO> productList = entity.getProductList();
        String emp = CommonUtils.getEmpNo();
        for (OpportunityProductDTO dto : productList) {
            OpportunityProduct product = new OpportunityProduct();
            BeanUtils.copyProperties(dto, product);
            product.setBusinessType(OpportunityConstant.TRANSFER_PROJECT);
            product.setOpptyId(entity.getRowId());
            product.setCreatedBy(emp);
            product.setLastUpdBy(emp);
            product.setCreated(now);
            product.setLastUpd(now);
            product.setRowId(iKeyIdService.getKeyId());
            product.setActiveFlg(CommonConstant.COMMON_FLAG_Y);
            product.setDataSource(entity.getDataSource());
            opportunityProducts.add(product);
        }
        LcapConverterUtil.buildProduct(opportunityProducts,detail);
        // 批量删除原转立项产品
        opportunityProductDao.batchLogicDeleteByOptyId(entity.getRowId(), OpportunityConstant.TRANSFER_PROJECT);
        // 批量插入立项产品
        opportunityProductDao.insertByBatch(opportunityProducts);
    }

    private ConversionProjectOrdinary sendOpportunityTransferProject(JSONObject jsonObject, String url) throws Exception {
        ConversionProjectOrdinary result = new ConversionProjectOrdinary();
        try {
            logger.info("传送数据：" + jsonObject.toJSONString());
            Map<String, String> headerParamsMap = new HashMap<>(8);
            headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO, CommonUtils.getEmpNo());
            headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE, RequestMessage.getToken());
            HttpResultData httpResult = MicroServiceWithConfigUtil.invokeServiceWithPostMethod("8", url, jsonObject, headerParamsMap);
            String resultCode = httpResult.getCode().getCode();
            if (!RetCodeCopy.SUCCESS_CODE.equals(resultCode)) {
                result.setStatus("error");
                logger.error("调用转立项接口失败！信息为：{}", httpResult.getCode().getMsg());
                throw new com.zte.springbootframe.common.exception.BusiException(RetCodeCopy.BUSINESSERROR_CODE, httpResult.getBo().toString());
            }
            Object resultBo = httpResult.getBo();
            if (null != resultBo) {
                Map<String, String> bo = (Map<String, String>) resultBo;
                result.setStatus("Success");
                result.setProjectApplicationCode(bo.get("projectApplicationCode"));
                result.setProjectUrl(bo.get("projectUrl"));
            }
        } catch (com.zte.springbootframe.common.exception.BusiException e) {
            result.setStatus("error");
            logger.error("调用转立项接口失败！", e);
            throw new BusinessRuntimeException(PrmRetCode.EXTERNAL_SERVERERROR_CODE, "project.initiation.failed", e.getExMsg());
        }
        return result;
    }

    /**
     * 设置传立项的信息是否包含新业务的字段
     *
     * @param jsonObject
     */
    private void setNewBusiness(JSONObject jsonObject) {
        String industryCode = jsonObject.getString("industryCode");
        if (StringUtils.isBlank(industryCode)) {
            return;
        }
        jsonObject.put("isNewBusiness", prmService.isNewBusinessIndustry(industryCode) ? "Y" : "N");
    }


    /**
     * 待办列表中文字段转换
     */
    private List<PrmOpportunityPendingVO> setPendingNameValue(List<PrmOpportunityPendingVO> pendingList, List<ApprovingNodeInfo> approvingNodeInfos) throws Exception {
        List<PrmOpportunityPendingVO> resultPendingList = new ArrayList<>();

        // 获取字典类型值
        Map<String, List<ComDictionaryMaintainVO>> mapList = comDictionaryMaintainService.queryByTypeList(OpportunityConstant.PRM_OPPORTUNITY_TYPES);
        // 过滤投资方所在地编码列表，消除重复及为null的值。
        List<String> orgCodeList = pendingList.stream().filter(vo -> Objects.nonNull(vo.getDeptNo())).map(PrmOpportunityPendingVO::getDeptNo).distinct().collect(Collectors.toList());
        // 从组织树获取组织信息列表
        List<IHolOrgVO> orgVOList = commonRemoteUtils.queryOrg(orgCodeList);
        // 从行业树获取行业列表
        List<AuthConstraintDTO> subIndustryList = prmService.getSubIndustryList("Y");
        // 商机状态
        List<ComDictionaryMaintainVO> statusCdList = mapList.get(OpportunityConstant.PRM_OPPORTUNITY_STATUS);
        // 商机来源
        List<ComDictionaryMaintainVO> dataSourceList = mapList.get(OpportunityConstant.SOURCE_OF_OPPORTUNITY);
        // 当前阶段
        List<ComDictionaryMaintainVO> currentPhaseTypeList = mapList.get(OpportunityConstant.CURRENT_PHASES_TYPE);

        boolean isZh = CommonConst.ZH_CN.equalsIgnoreCase(CommonUtils.getxLangId());

        Map<String, String> mapDataSource = dataSourceList.stream()
                .collect(Collectors
                        .toMap(ComDictionaryMaintainVO::getCode,
                                comDictionaryMaintainVO -> getDictName(comDictionaryMaintainVO, isZh)));
        Map<String, String> mapCurrentPhaseType = currentPhaseTypeList.stream()
                .collect(Collectors
                        .toMap(ComDictionaryMaintainVO::getCode,
                                comDictionaryMaintainVO -> getDictName(comDictionaryMaintainVO, isZh)));
        Map<String, String> mapStatusCd = statusCdList.stream()
                .collect(Collectors.toMap(ComDictionaryMaintainVO::getCode,
                        comDictionaryMaintainVO -> getDictName(comDictionaryMaintainVO, isZh)));
        Map<String, String> mapSubIndustry = subIndustryList.stream()
                .collect(Collectors.toMap(AuthConstraintDTO::getShowValue, AuthConstraintDTO::getShowName));
        Map<String, String> mapOrganizationNames = orgVOList.stream()
                .collect(Collectors.toMap(IHolOrgVO::getCode, IHolOrgVO::getName));
        Map<String, PrmOpportunityPendingVO> pendingInfoMap = pendingList.stream()
                .collect(Collectors.toMap(PrmOpportunityPendingVO::getRowId, Function.identity()));

        for (ApprovingNodeInfo approvingNodeInfo : approvingNodeInfos) {
            PrmOpportunityPendingVO vo = pendingInfoMap.getOrDefault(approvingNodeInfo.getBusinessId(), new PrmOpportunityPendingVO());
            PrmOpportunityPendingVO clonedVo = JSON.parseObject(JSON.toJSONString(vo), PrmOpportunityPendingVO.class);
            // 设置商机来源字段中文名称
            clonedVo.setDataSourceName(mapDataSource.containsKey(clonedVo.getDataSource()) ?
                    mapDataSource.get(clonedVo.getDataSource()) : clonedVo.getDataSource());
            // 设置最终用户行业字段中文名称
            String defaultTrade = StringUtils.isNotBlank(clonedVo.getFinalCustomerTradeChildCode()) ?
                    clonedVo.getFinalCustomerTradeCode() + CommonConstant.MID_LINE + clonedVo.getFinalCustomerTradeChildCode() : null;
            // 设置最终用户行业字段中文名称
            clonedVo.setFinalCustomerTradeName(mapSubIndustry.getOrDefault(clonedVo.getFinalCustomerTradeChildCode(), defaultTrade));
            // 设置投资方所在地字段中文名称
            clonedVo.setDeptName(mapOrganizationNames.containsKey(clonedVo.getDeptNo()) ?
                    mapOrganizationNames.get(clonedVo.getDeptNo()) : clonedVo.getDeptNo());
            // 设置商机状态字段中文名称
            clonedVo.setStatusName(mapStatusCd.containsKey(clonedVo.getStatusCd()) ?
                    mapStatusCd.get(clonedVo.getStatusCd()) : clonedVo.getStatusCd());
            // 设置当前阶段字段中文名称
            clonedVo.setProjectPhasesName(mapCurrentPhaseType.containsKey(clonedVo.getProjectPhasesCode()) ?
                    mapCurrentPhaseType.get(clonedVo.getProjectPhasesCode()) : clonedVo.getProjectPhasesCode());
            clonedVo.setFlowInstanceId(approvingNodeInfo.getFlowInstanceId());
            clonedVo.setTaskId(approvingNodeInfo.getTaskId());
            clonedVo.setNodeName(approvingNodeInfo.getNodeName());
            clonedVo.setNodeType(approvingNodeInfo.getNodeType());
            resultPendingList.add(clonedVo);
        }
        return resultPendingList;
    }

    /**
     * 设置待办高级查询参数
     *
     * @param pendingAdvanceQueryMap 请求入参formData
     * @return 组装好的待办高级查询参数
     */
    private ApprovingQueryParam setPendingAdvanceQueryParam(Map<String, Object> pendingAdvanceQueryMap, Set<String> advanceParamSet) throws ParseException {
        ApprovingQueryParam approvingQueryParam = new ApprovingQueryParam();
        approvingQueryParam.setEmpNo(CommonUtils.getEmpNo());
        approvingQueryParam.setFlowCodes(Collections.singletonList(OpportunityConstant.NEW_OPPORTUNITY_FLOW_CODE));
        Map<String, ParamQueryDTO> paramQueryMap = new HashMap<>();
        if (pendingAdvanceQueryMap.isEmpty()) {
            return approvingQueryParam;
        }
        ParamQueryDTO paramQueryDTO = new ParamQueryDTO();
        for (Map.Entry<String, Object> entry : pendingAdvanceQueryMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (!advanceParamSet.contains(key)) {
                continue;
            }
            boolean isFuzzyMatch = (OpportunityConstant.KEY_WORD.equals(key) || OpportunityConstant.KEY_WORD_FOR_QUERY.equals(key))
                    && StringUtils.isNotBlank((String) value) && !OpportunityConstant.KEY_WORD_SPLIT.equals(value);
            if (List.class.isAssignableFrom(value.getClass()) && CollectionUtils.isNotEmpty((List) value)) {
                paramQueryDTO = setAdvancedCriteriaDto(ParamQueryModelEnum.EXACT_MATCH_IN.getCode(), value);
                continue;
            }
            if (isFuzzyMatch) {
                paramQueryDTO = setAdvancedCriteriaDto(ParamQueryModelEnum.FUZZY_MATCH.getCode(), value);
            } else if (String.class.isAssignableFrom(value.getClass()) && StringUtils.isNotBlank((String) value)) {
                paramQueryDTO = setAdvancedCriteriaDto(ParamQueryModelEnum.EXACT_MATCH.getCode(), value);
            }
            paramQueryMap.put(key, paramQueryDTO);
        }
        approvingQueryParam.setAdvancedCriteria(paramQueryMap);
        // 报备时间间隔
        DateTimeInterval dateTimeInterval = new DateTimeInterval();
        if (StringUtils.isNotBlank((String) pendingAdvanceQueryMap.get(OpportunityConstant.MIN_CREATE_DATE))
                && StringUtils.isNotBlank((String) pendingAdvanceQueryMap.get(OpportunityConstant.MAX_CREATE_DATE))) {
            SimpleDateFormat parser = new SimpleDateFormat(OpportunityConstant.DATE_FORMAT_YYYY_MM_DD);
            Date minCreatedDate = parser.parse(pendingAdvanceQueryMap.get(OpportunityConstant.MIN_CREATE_DATE).toString());
            Date maxCreatedDate = parser.parse(pendingAdvanceQueryMap.get(OpportunityConstant.MAX_CREATE_DATE).toString());
            dateTimeInterval.setLowerLimit(minCreatedDate);
            dateTimeInterval.setUpperLimit(maxCreatedDate);
            approvingQueryParam.setSubmitTimeInterval(dateTimeInterval);
        }
        return approvingQueryParam;
    }

    /**
     * 设置高级状态查询时的条件参数
     *
     * @param queryMode  查询模式 参数见 ParamQueryModelEnum
     * @param queryValue 参数值
     * @return 高级状态查询时的条件参数
     */
    private ParamQueryDTO setAdvancedCriteriaDto(String queryMode, Object queryValue) {
        ParamQueryDTO paramQueryDTO = new ParamQueryDTO();
        paramQueryDTO.setQueryMode(queryMode);
        paramQueryDTO.setQueryValue(queryValue);
        return paramQueryDTO;
    }


    /**
     * 设计的要求，先注销旧审批，再发起新的审批，新的审批失败改为草稿状态，删除审批记录  无法做到密等
     * 不能开启事务
     *
     * 这个方法违背开发意愿
     * @param vo
     * @return
     * @throws Exception
     */
    @Override
    public boolean errataOppty (OpportunityPartInfoVO vo) throws Exception {
        OpportunityDetail dbDetail = null;
        ComApprovalRecord approvalRecord = null;
        List<ApprovedTask> approvalNodes = null;
        String rowId = vo.getRowId();
        if ((dbDetail = opportunityDetailDao.get(rowId)) == null
                || (approvalRecord = comApprovalRecordDao.getByBusinessId(rowId)) == null
                || (CollectionUtils.isEmpty(approvalNodes = prmOpportunityApprovalService.getApprovedTasks(approvalRecord.getWorkFlowInstanceId())))) {
            logger.error("勘误商机,商机或者审批信息不存在,vo:{}",vo);
            throw new BusinessRuntimeException(RetCode.AUTHFAILED_CODE, "OpptyOrApproveError");
        }
        validateOpptyPartInfo(vo, dbDetail);
        String oldApprover = approvalNodes.get(0).getApprover();
        String oldFlowId = approvalRecord.getWorkFlowInstanceId();
        // ApprovedNodeInfo approvedNodeInfo = getApprovedNodeInfo(oldFlowId, oldApprover);
        String approver = iOpportunityInfoService.queryApprover(vo.getFinalCustomerChildTrade(), vo.getDeptNo());
        logger.info("勘误商机{},新的审批人:{}", rowId, approver);
        if (StringUtils.isBlank(approver)){
            throw new BusiException(RetCode.BUSINESSERROR_CODE, "approvingPersonEmpty");
        }
        //  撤销审批单据
        ApprovalRevokeParamsDTO revokeParams = getApprovalRevokeParamsDTO(oldFlowId);
        logger.info("errataOppty.revokeFlow撤销审批单据:{}, revokeParams:{}", rowId, revokeParams);
        approvalFlowService.revokeFlow(revokeParams);
        // 更新审批id
        String flowId = null;
        ComApprovalRecord newApprovalRecord = null;
        try {
            updateOpptyDetail(vo, rowId);
            //获取新的审批参数
            ApprovalStartParamsDTO startParams = iOpportunityInfoService.getStartProcessParams(rowId);
            //getApprovalStartParamsDTO(approver, parentTradeName, approvedNodeInfo);
            startParams.setBusinessId(rowId);
            flowId = approvalFlowService.startFlow(startParams);
            logger.info("勘误商机{},发起新的审批:{}, 新的flowId:{}", rowId, startParams, flowId);
            //保存新的审批记录 删除旧的审批记录
            newApprovalRecord = getComApprovalRecord();
            newApprovalRecord.setBusinessId(rowId);
            newApprovalRecord.setWorkFlowInstanceId(flowId);
            newApprovalRecord.setBusinessType(approvalRecord.getBusinessType());
            comApprovalRecordDao.softDelete(approvalRecord.getRowId());
            comApprovalRecordDao.insert(newApprovalRecord);
        } catch (Exception e) {
            handleErrataException(rowId, flowId, newApprovalRecord);
            throw e;
        }
        return true;
    }




    private void handleErrataException(String rowId,String flowId, ComApprovalRecord newApprovalRecord) {
        logger.error("勘误商机{}异常,flowId:{},newApprovalRecord:{}", rowId, flowId,newApprovalRecord);
        ApprovalRevokeParamsDTO revokeParams;
        //  异常 删除审批记录
        Opportunity opportunity = new Opportunity();
        opportunity.setRowId(rowId);
        opportunity.setStatusCd("Draft");
        // 商机更新为草稿状态
        sOpportunityRepository.updateById(LcapConverterUtil.buildSopty(opportunity, null));
        if (newApprovalRecord != null) {
            comApprovalRecordDao.softDelete(newApprovalRecord.getRowId());
        }
        // 撤销新发起的审批流程
        if (flowId != null) {
            revokeParams = getApprovalRevokeParamsDTO(flowId);
            approvalFlowService.revokeFlow(revokeParams);
        }
    }

    private ComApprovalRecord getComApprovalRecord() {
        ComApprovalRecord newApprovalRecord = new ComApprovalRecord();
        newApprovalRecord.setRowId(iKeyIdService.getKeyLongId());
        newApprovalRecord.setCreatedBy(CommonUtils.getEmpNo());
        newApprovalRecord.setLastUpdatedBy(CommonUtils.getEmpNo());
        newApprovalRecord.setCreatedDate(new Date());
        newApprovalRecord.setLastUpdatedDate(new Date());
        return newApprovalRecord;
    }





    private ApprovalRevokeParamsDTO getApprovalRevokeParamsDTO(String rowId) {
        ApprovalRevokeParamsDTO revokeParams = new ApprovalRevokeParamsDTO();
        revokeParams.setFlowCode(OPPTY_FLOW_CODE);
        revokeParams.setFlowInstanceId(rowId);
        return revokeParams;
    }

    private void validateOpptyPartInfo(OpportunityPartInfoVO vo, OpportunityDetail detail) {

        if (StringUtils.equalsIgnoreCase(vo.getDeptNo(), detail.getDeptNo())
                && StringUtils.equalsIgnoreCase(vo.getFinalCustomerParentTrade(), detail.getFinalCustomerParentTrade())
                && StringUtils.equalsIgnoreCase(vo.getFinalCustomerChildTrade(), detail.getFinalCustomerChildTrade())) {
            logger.error("勘误商机,商机信息没有发生变化,vo:{}",vo);
            throw new BusinessRuntimeException(RetCode.AUTHFAILED_CODE, "OpptyInfoValidateError");
        }
    }



    private ApprovalStartParamsDTO getApprovalStartParamsDTO (String approver, String parentTradeName, ApprovedNodeInfo approvedNodeInfo){
        Map<String, Object> businessParam = approvedNodeInfo.getBusinessParam();
        if (businessParam == null) {
            businessParam = Maps.newHashMap();
        }
        businessParam.put("finalCustomerParentTrade", parentTradeName);
        businessParam.put("approvingPerson", approver);
        businessParam.put("createdBy", CommonUtils.getEmpNo());

        ApprovalStartParamsDTO startParams = new ApprovalStartParamsDTO();
        startParams.setFlowCode(OPPTY_FLOW_CODE);
        startParams.setParams(businessParam);
        return startParams;
    }


    private void updateOpptyDetail(OpportunityPartInfoVO vo, String rowId) {

        OpportunityDetail detail = new OpportunityDetail();
        detail.setRowId(rowId);
        detail.setFinalCustomerParentTrade(vo.getFinalCustomerParentTrade());
        detail.setFinalCustomerChildTrade(vo.getFinalCustomerChildTrade());
        sOptyXDao.updateById(LcapConverterUtil.buildSoptyx(detail));

        if(StringUtils.isNotBlank(vo.getDeptNo())) {
            sOptyDao.update(Wrappers.lambdaUpdate(SOptyBO.class).eq(SOptyBO::getId,rowId).set(SOptyBO::getOrgTree,vo.getDeptNo()));
        }
    }

    /**
     * 发起新的审批前，查询目前已发起的审批
     *
     * @param flowInstanceId
     * @param oldApprover
     * @return
     */
//    @Transactional(propagation = Propagation.SUPPORTS, isolation = Isolation.READ_UNCOMMITTED, rollbackFor = Exception.class)
//    private ApprovedNodeInfo getApprovedNodeInfo(String flowInstanceId, String oldApprover) {
//        ApprovingQueryParam approvingQueryParam = new ApprovingQueryParam();
//        approvingQueryParam.setFlowInstanceId(flowInstanceId);
//
//        approvingQueryParam.setEmpNo(oldApprover);
//        approvingQueryParam.setPageNo(1L);
//        approvingQueryParam.setPageSize(1L);
//        PageRows<ApprovingNodeInfo> pageRows = approvalFlowService.getApprovingTasksByCondition(approvingQueryParam);
//        if (CollectionUtils.isNotEmpty(pageRows.getRows())) {
//            ApprovingNodeInfo approving = pageRows.getRows().get(0);
//            ApprovedNodeInfo approved = new ApprovedNodeInfo();
//            BeanUtils.copyProperties(approving, approved);
//            return approved;
//        }
//        PageRows<ApprovedNodeInfo> page = approvalFlowService.getApprovedTasksByCondition(approvingQueryParam);
//        if (CollectionUtils.isNotEmpty(page.getRows())) {
//            return page.getRows().get(0);
//        }
//        return new ApprovedNodeInfo();
//    }

    /**
     * 校验是否有查看详情权限
     * @param empNo
     * @param rowId
     * @return
     */
    @Override
    public Boolean checkDetailReviewPermission(String empNo, String rowId) throws ExecutionException, InterruptedException {
        Boolean result = Boolean.FALSE;
        Map<String, Object> paramMap = new HashMap<>();
        boolean isAdmin = setAuthConstraintsAndJudgeAdmin(paramMap);
        List<String> constrainedOrgList = (List<String>) paramMap.get(CONSTRAINED_ORG_LIST);
        List<String> constrainedIndustryList = (List<String>) paramMap.get(CONSTRAINED_INDUSTRY_LIST);
        Opportunity opportunity = opportunityDao.get(rowId);
        OpportunityDetail opportunityDetail = opportunityDetailDao.get(rowId);
        String flowInstanceId = comApprovalRecordDao.queryFlowInstance(rowId);
        ApprovingQueryParam approvingQueryParam = new ApprovingQueryParam();
        approvingQueryParam.setEmpNo(empNo);
        approvingQueryParam.setFlowInstanceId(flowInstanceId);
        approvingQueryParam.setPageNo(1L);
        approvingQueryParam.setPageSize(10L);
        PageRows<ApprovingNodeInfo> approvingNodeInfoPageRows = approvalFlowService.getApprovingTasksByCondition(approvingQueryParam);
        List<ApprovingNodeInfo> approvingNodeInfos = approvingNodeInfoPageRows.getRows();
        PageRows<ApprovedNodeInfo> approvedNodeInfoPageRows = approvalFlowService.getApprovedTasksByCondition(approvingQueryParam);
        List<ApprovedNodeInfo> approvedNodeInfos = approvedNodeInfoPageRows.getRows();
        if (null == opportunity || null == opportunityDetail) {
            return result;
        }
        opportunity.setBusinessTypeCd(CommonMapUtil.DELIVERY_INTERFACE_MAP.inverse().getOrDefault(opportunity.getBusinessTypeCd(), opportunity.getBusinessTypeCd()));
        // 管理员可以查看除草稿状态外的所有商机
        boolean hasPermissionA = !OptyStatusEnum.DRAFT.getCode().equals(opportunity.getStatusCd()) && isAdmin;
        // 创建人和中兴业务经理可以查看商机
        boolean hasPermissionB = (empNo.equals(opportunity.getCreatedBy())
                || empNo.equals(opportunityDetail.getBusinessManagerId()));
        // 可以查看到授权范围内的商机
        boolean hasPermissionC = (constrainedOrgList.contains(opportunityDetail.getDeptNo()))
                && (constrainedIndustryList.contains(opportunityDetail.getFinalCustomerChildTrade()));
        // 可以查看待我审批或者我已审批的商机
        boolean hasPermissionD = CollectionUtils.isNotEmpty(approvedNodeInfos) || CollectionUtils.isNotEmpty(approvingNodeInfos);

        if (hasPermissionA || hasPermissionB || hasPermissionC || hasPermissionD) {
            result = Boolean.TRUE;
        }
        return result;
    }
    /**
     * 发起新的审批前，查询目前已发起的审批
     * @param flowInstanceId
     * @param oldApprover
     * @return
     */
    private ApprovedNodeInfo getApprovedNodeInfo (String flowInstanceId, String oldApprover){
        ApprovingQueryParam approvingQueryParam = new ApprovingQueryParam();
        approvingQueryParam.setFlowInstanceId(flowInstanceId);

        approvingQueryParam.setEmpNo(oldApprover);
        approvingQueryParam.setPageNo(1L);
        approvingQueryParam.setPageSize(1L);
        PageRows<ApprovingNodeInfo> pageRows = approvalFlowService.getApprovingTasksByCondition(approvingQueryParam);
        if (CollectionUtils.isNotEmpty(pageRows.getRows())) {
            ApprovingNodeInfo approving = pageRows.getRows().get(0);
            ApprovedNodeInfo approved = new ApprovedNodeInfo();
            BeanUtils.copyProperties(approving, approved);
            return approved;
        }
        PageRows<ApprovedNodeInfo> page = approvalFlowService.getApprovedTasksByCondition(approvingQueryParam);
        if (CollectionUtils.isNotEmpty(page.getRows())) {
            logger.info("getApprovedTasksByCondition,workFlowInstanceId:{},return:{}",flowInstanceId, page.getRows());
            return page.getRows().get(0);
        }
        return new ApprovedNodeInfo();

    }

    @Override
    public PageRows<OpptyCustomerCreateRecordVO> queryCustomerRecord(FormData<PrmOpptyCustomerCreateRecordQuery> formData) throws Exception {
        checkParam(formData);
        // 政企中国商机管理员才有查看权限
        Boolean hasRole = uppAuthorityService.hasPermissionInRoleCodes(Collections.singletonList(OpportunityRoleEnum.GEC_OPPORTUNITY_ADMIN.getCode()));
        if (!hasRole) {
            throw new BusiException(RetCode.PERMISSIONDENIED_CODE, "No Permissions");
        }
        return opptyCustomerCreateRecordService.queryCustomerRecordWithPrm(formData);
    }

    @Override
    public void importPdm(MultipartFile file) throws IOException {
        EasyExcelFactory.read(file.getInputStream(), ImportPdmDTO.class, new ImportPdmListener(opportunityDao, iOpportunityProductService)).sheet().doRead();
    }
    
    /* Started by AICoder, pid:1241exd086yf6ae1419f0b3ff024202adff78836 */
    @Override
    public List<PrmOpportunityVO> getOpportunityLowCodeList(List<String> ids) throws Exception {
        List<PrmOpportunityVO> prmOpportunityList = opportunityDao.getPrmOpportunityByIds(ids);
        List<String> flowInstances = prmOpportunityList.stream()
                .filter(this::statusHasProcessor)
                .map(PrmOpportunityVO::getFlowInstanceId)
                .collect(Collectors.toList());

        Map<String, Object> prmOpportunityQueryMap = setPrmOpportunityQueryMap(new FormData(){{setBo(new PrmOpportunityQueryDTO());}}, Boolean.FALSE);

        // 获取商机的主产品维护关系
        Map<String, Boolean> needMatainMainProdsMap = this.judgeNeedMaintainMainProds(Boolean.FALSE, ids, maintainMainProdsStartTime, null);

        List<String> nonDraftOpportunityCodes = prmOpportunityList.stream()
                .filter(opportunityVO -> ! OptyStatusEnum.DRAFT.getCode().equals(opportunityVO.getStatusCd()))
                .map(PrmOpportunityVO::getOptyCd).collect(Collectors.toList());

        Map<String, ProjectAuthInfoDto> opportunityAuth = getProjectAuthInfoDtoMap(nonDraftOpportunityCodes);

        Map<String, String> mapCurrentProcessor = getCurrentProcessor(flowInstances);
        for (PrmOpportunityVO vo : prmOpportunityList) {
            setOperationButtonAndExpectSignMoneyModify(vo, prmOpportunityQueryMap, needMatainMainProdsMap.get(vo.getRowId()));
            vo.setCurrentProcessor(mapCurrentProcessor.getOrDefault(vo.getFlowInstanceId(), getDefaultCurrentProcessor(vo)));
            setProjectAuthInfoToVo(vo, opportunityAuth, CommonConst.ZH_CN.equalsIgnoreCase(CommonUtils.getxLangId()));
        }
        setIsDisplayComplianceRefreshButton(prmOpportunityQueryMap, prmOpportunityList);
        return prmOpportunityList;
    }
    /* Ended by AICoder, pid:1241exd086yf6ae1419f0b3ff024202adff78836 */

    private void checkParam(FormData<PrmOpptyCustomerCreateRecordQuery> formData) {
        if (formData.getPage() < 1) {
            formData.setPage(CommonConst.DEFAULT_PAGE_NUM);
        }
        if (formData.getRows() < 1 ) {
            formData.setRows(CommonConst.DEFAULT_PAGE_SIZE);
        }
        if (formData.getRows() > CommonConst.MAX_PAGE_SIZE) {
            formData.setRows(CommonConst.MAX_PAGE_SIZE);
        }
    }

    @Override
    public ServiceData<PageRows<IHolOrgVO>> queryOrg(IHolOrgQueryDTO queryDTO) {
        OrgProductDTO orgProduct = commonRemoteUtils.getOrgProduct();
        if (StringUtils.equals(queryDTO.getUseDefault(), CommonConst.Y)) {
            queryDTO.setCodeList(orgProduct.getOrgList());
        } else if (StringUtils.equals(queryDTO.getParentCode(), orgCode)) {
            queryDTO.setCodeList(orgProduct.getZteOrgCodes());
            queryDTO.setParentCode(null);
        } else {
            queryDTO.setIsPartakePerfStats(1);
        }
        return sOpportunityExternalService.queryOrgTree(queryDTO);
    }

    @Override
    public ServiceData<PageRows<OptyPlmProjectVO>> queryProduct(OptyPlmProjectQueryDTO deptPath) {
        if (StringUtils.equals(deptPath.getUseDefault(), CommonConst.Y)) {
            deptPath.setCodeList(commonRemoteUtils.getOrgProduct().getProductList());
        }
        if (StringUtils.equals(deptPath.getStatusFlag(), CommonConst.Y)) {
            deptPath.setStatus(Collections.singletonList(PLM_STATUS_ENABLE));
        }
        return plmProjectCategoryClient.prodTreeQuery(deptPath);
    }

    @Override
    public List<WinOpportunityVO> queryWinOpportunities(WinOpportunityQueryDTO queryDTO) throws Exception {
        logger.info("查询中标商机信息，参数: {}", queryDTO);
        List<WinOpportunityVO> result = opportunityQueryDao.queryWinOpportunities(queryDTO.getCrmCustomerCode());
        return result;
    }
}
