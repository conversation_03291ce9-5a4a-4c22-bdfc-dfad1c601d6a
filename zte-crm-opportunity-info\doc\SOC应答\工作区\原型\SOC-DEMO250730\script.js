// SOC智能应答系统 JavaScript

// 全局数据状态管理
let globalData = {
    tasks: [
        {
            id: 1,
            code: 'TASK001',
            name: '华为云Stack解决方案技术标',
            country: '中国',
            customer: '某银行',
            project: '云计算平台建设项目',
            company: '华为技术有限公司',
            dataSource: 'GBBS',
            itemCount: 25,
            completedCount: 20,
            satisfaction: 85,
            status: '进行中',
            isPersonal: false,
            createTime: '2024-01-10 09:00:00',
            updateTime: '2024-01-15 16:30:00'
        },
        {
            id: 2,
            code: 'TASK002',
            name: 'FusionSphere虚拟化技术标',
            country: '新加坡',
            customer: '某运营商',
            project: '数据中心虚拟化改造',
            company: '华为技术有限公司',
            dataSource: 'GBBS',
            itemCount: 18,
            completedCount: 18,
            satisfaction: 92,
            status: '已完成',
            isPersonal: false,
            createTime: '2024-01-08 14:00:00',
            updateTime: '2024-01-14 18:45:00'
        },
        {
            id: 3,
            code: 'TASK003',
            name: '安全产品技术方案',
            country: '德国',
            customer: '某制造企业',
            project: '工业互联网安全建设',
            company: '华为技术有限公司',
            dataSource: 'GBBS',
            itemCount: 12,
            completedCount: 0,
            satisfaction: 0,
            status: '未开始',
            isPersonal: false,
            createTime: '2024-01-12 11:00:00',
            updateTime: '2024-01-12 11:00:00'
        },
        {
            id: 4,
            code: 'TASK004',
            name: '5G核心网解决方案',
            country: '英国',
            customer: '英国电信',
            project: '5G网络建设',
            company: '华为技术有限公司',
            dataSource: 'GBBS',
            itemCount: 15,
            completedCount: 8,
            satisfaction: 75,
            status: '进行中',
            isPersonal: false,
            createTime: '2024-01-05 10:00:00',
            updateTime: '2024-01-16 14:20:00'
        },
        {
            id: 5,
            code: 'TASK005',
            name: 'AI大模型训练平台',
            country: '美国',
            customer: '某科技公司',
            project: 'AI基础设施建设',
            company: '华为技术有限公司',
            dataSource: 'GBBS',
            itemCount: 22,
            completedCount: 15,
            satisfaction: 88,
            status: '进行中',
            isPersonal: false,
            createTime: '2024-01-01 09:30:00',
            updateTime: '2024-01-17 11:45:00'
        }
    ],
    items: [
        {
            id: 1,
            taskId: 1,
            code: 'CODE001',
            description: '云平台基础架构能力要求，需要支持虚拟机、容器、存储等资源的统一管理',
            product: '华为云Stack',
            tags: ['基础架构', '云平台'],
            status: '应答中',
            satisfaction: null,
            assignee: '张三（123456）',
            responseMethod: 'AI应答',
            responseContent: '华为云Stack提供统一的云平台基础架构...',
            source: 'GBBS',
            index: 'GBBS-001',
            remark: '',
            updateUser: '张三（123456）',
            updateTime: '2024-01-15 10:30'
        },
        {
            id: 2,
            taskId: 1,
            code: 'CODE001',
            description: '云平台基础架构能力要求，需要支持虚拟机、容器、存储等资源的统一管理',
            product: 'FusionSphere',
            tags: ['基础架构', '虚拟化'],
            status: '已应答',
            satisfaction: 'PC',
            assignee: '张三（123456）',
            responseMethod: '手工应答',
            responseContent: 'FusionSphere虚拟化平台支持基础架构管理...',
            source: 'GBBS',
            index: 'GBBS-002',
            remark: '需要补充容器支持',
            updateUser: '张三（123456）',
            updateTime: '2024-01-15 11:15'
        },
        {
            id: 3,
            taskId: 1,
            code: 'CODE002',
            description: '虚拟化资源管理能力，支持计算、存储、网络资源的动态分配和调度',
            product: 'FusionSphere',
            tags: ['虚拟化', '资源管理'],
            status: '已应答',
            satisfaction: 'FC',
            assignee: '李四（789012）',
            responseMethod: 'AI应答',
            responseContent: 'FusionSphere提供完整的虚拟化资源管理能力...',
            source: 'GBBS',
            index: 'GBBS-003',
            remark: '',
            updateUser: '李四（789012）',
            updateTime: '2024-01-15 12:00'
        },
        {
            id: 4,
            taskId: 1,
            code: 'CODE003',
            description: '安全防护机制，包括网络安全、数据安全和应用安全',
            product: '云安全服务',
            tags: ['安全', '防护'],
            status: '未应答',
            satisfaction: null,
            assignee: '王五（345678）',
            responseMethod: null,
            responseContent: null,
            source: 'GBBS',
            index: 'GBBS-004',
            remark: '',
            updateUser: null,
            updateTime: '2024-01-15 09:00'
        },
        {
            id: 5,
            taskId: 1,
            code: 'CODE004',
            description: '高可用性和容灾能力，确保业务连续性',
            product: '华为云Stack',
            tags: ['高可用', '容灾'],
            status: '应答中',
            satisfaction: null,
            assignee: '张三（123456）',
            responseMethod: 'AI应答',
            responseContent: null,
            source: 'GBBS',
            index: 'GBBS-005',
            remark: '',
            updateUser: '张三（123456）',
            updateTime: '2024-01-15 13:30'
        },
        {
            id: 6,
            taskId: 1,
            code: 'CODE005',
            description: '监控和运维管理能力，支持实时监控和自动化运维',
            product: 'FusionSphere',
            tags: ['监控', '运维'],
            status: '已应答',
            satisfaction: 'PC',
            assignee: '李四（789012）',
            responseMethod: '手工应答',
            responseContent: 'FusionSphere提供全面的监控运维管理能力...',
            source: 'GBBS',
            index: 'GBBS-006',
            remark: '建议增加告警功能',
            updateUser: '李四（789012）',
            updateTime: '2024-01-15 14:00'
        },
        {
            id: 7,
            taskId: 1,
            code: 'CODE006',
            description: '数据备份和恢复能力，保障数据安全',
            product: '云安全服务',
            tags: ['备份', '恢复'],
            status: '未应答',
            satisfaction: null,
            assignee: '王五（345678）',
            responseMethod: null,
            responseContent: null,
            source: 'GBBS',
            index: 'GBBS-007',
            remark: '',
            updateUser: null,
            updateTime: '2024-01-15 09:00'
        },
        {
            id: 8,
            taskId: 1,
            code: 'CODE007',
            description: '性能优化和调优能力，提升系统整体性能',
            product: 'FusionCompute',
            tags: ['性能', '优化'],
            status: '已应答',
            satisfaction: 'FC',
            assignee: '赵六（901234）',
            responseMethod: 'AI应答',
            responseContent: 'FusionCompute提供强大的性能优化功能...',
            source: 'GBBS',
            index: 'GBBS-008',
            remark: '',
            updateUser: '赵六（901234）',
            updateTime: '2024-01-15 15:00'
        },
        {
            id: 9,
            taskId: 1,
            code: 'CODE008',
            description: '网络配置和管理能力，支持SDN和NFV',
            product: 'FusionSphere',
            tags: ['网络', 'SDN'],
            status: '应答中',
            satisfaction: null,
            assignee: '李四（789012）',
            responseMethod: '手工应答',
            responseContent: null,
            source: 'GBBS',
            index: 'GBBS-009',
            remark: '',
            updateUser: '李四（789012）',
            updateTime: '2024-01-15 16:00'
        },
        {
            id: 10,
            taskId: 1,
            code: 'CODE009',
            description: '容器编排和管理能力，支持Kubernetes',
            product: '华为云Stack',
            tags: ['容器', 'Kubernetes'],
            status: '未应答',
            satisfaction: null,
            assignee: '张三（123456）',
            responseMethod: null,
            responseContent: null,
            source: 'GBBS',
            index: 'GBBS-010',
            remark: '',
            updateUser: null,
            updateTime: '2024-01-15 09:00'
        }
    ],
    currentTask: null,
    personalTaskCounter: 0,
    selectedItems: new Set(), // 记忆选中状态
    selectAllState: false, // 记忆全选状态
    targetProductItems: [], // 目标产品条目
    pendingProductOperation: null // 待执行的产品操作
};

// 全局变量
let autoRefreshInterval;
let refreshCountdown = 5;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('SOC智能应答系统加载完成');
    
    // 初始化所有任务的条目计数
    initializeTaskCounts();
    
    renderTaskList();
    
    // 添加标签输入功能
    setupTagInput();
    
    // 初始化列筛选
    initColumnFilter();
});

// 设置标签输入功能
function setupTagInput() {
    // 单条录入弹窗的标签输入
    const addItemTagInput = document.querySelector('#addItemModal input[placeholder="输入标签后按回车添加"]');
    if (addItemTagInput) {
        addItemTagInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                const tagValue = this.value.trim();
                if (tagValue) {
                    const tagList = this.parentNode.querySelector('.tag-list');
                    const tagElement = document.createElement('span');
                    tagElement.className = 'tag primary';
                    tagElement.innerHTML = `${tagValue} <span onclick="this.parentNode.remove()" style="cursor: pointer; margin-left: 4px;">×</span>`;
                    tagList.appendChild(tagElement);
                    this.value = '';
                }
            }
        });
    }
    
    // 批量标签输入
    const batchTagInput = document.getElementById('batchTagInput');
    if (batchTagInput) {
        batchTagInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                const tagValue = this.value.trim();
                if (tagValue) {
                    const tagList = document.getElementById('batchTagList');
                    const tagElement = document.createElement('span');
                    tagElement.className = 'tag primary';
                    tagElement.innerHTML = `${tagValue} <span onclick="this.parentNode.remove()" style="cursor: pointer; margin-left: 4px;">×</span>`;
                    tagList.appendChild(tagElement);
                    this.value = '';
                }
            }
        });
    }
}

// 初始化所有任务的条目计数
function initializeTaskCounts() {
    globalData.tasks.forEach(task => {
        const taskItems = globalData.items.filter(item => item.taskId === task.id);
        task.itemCount = taskItems.length;
        task.completedCount = taskItems.filter(item => item.status === '已应答').length;
        
        // 计算满足度
        const satisfiedItems = taskItems.filter(item => item.satisfaction === 'FC');
        const partiallySatisfiedItems = taskItems.filter(item => item.satisfaction === 'PC');
        const totalRatedItems = taskItems.filter(item => item.satisfaction);
        
        if (totalRatedItems.length > 0) {
            task.satisfaction = Math.round(((satisfiedItems.length * 1 + partiallySatisfiedItems.length * 0.5) / totalRatedItems.length) * 100);
        } else {
            task.satisfaction = 0;
        }
        
        // 更新任务状态
        if (task.completedCount === 0) {
            task.status = '未开始';
        } else if (task.completedCount === task.itemCount) {
            task.status = '已完成';
        } else {
            task.status = '进行中';
        }
    });
    
    console.log('✅ 所有任务的条目计数已初始化');
}

// 渲染任务列表
function renderTaskList() {
    const tbody = document.getElementById('taskTableBody');
    const paginationInfo = document.getElementById('paginationInfo');
    
    tbody.innerHTML = '';
    
    // 任务排序：优先展示正在处理或刚处理完的任务
    const sortedTasks = [...globalData.tasks].sort((a, b) => {
        // 1. 按状态优先级排序（进行中 > 已完成 > 未开始）
        const statusPriority = {
            '进行中': 3,
            '已完成': 2,
            '未开始': 1
        };
        
        const statusDiff = statusPriority[b.status] - statusPriority[a.status];
        if (statusDiff !== 0) return statusDiff;
        
        // 2. 同状态下按更新时间倒序排序（最新的在前面）
        const timeA = new Date(a.updateTime || a.createTime);
        const timeB = new Date(b.updateTime || b.createTime);
        return timeB - timeA;
    });
    
    sortedTasks.forEach(task => {
        const progress = task.itemCount > 0 ? Math.round((task.completedCount / task.itemCount) * 100) : 0;
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${task.code}</td>
            <td>${task.name}${task.isPersonal ? ' <span class="tag primary">个人任务</span>' : ''}</td>
            <td>${task.country}</td>
            <td>${task.customer}</td>
            <td>${task.project}</td>
            <td>${task.itemCount}</td>
            <td>
                <div class="progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${progress}%"></div>
                    </div>
                    <span class="progress-text">${task.completedCount}/${task.itemCount}</span>
                </div>
            </td>
            <td>${task.satisfaction}%</td>
            <td>
                <div class="table-actions">
                    <button class="btn btn-sm" onclick="openTaskDetail(${task.id})">应答</button>
                    <button class="btn btn-sm" onclick="editTask(${task.id})">编辑</button>
                    <button class="btn btn-sm" onclick="copyTask(${task.id})">复制</button>
                    ${!task.isPersonal ? `<button class="btn btn-sm" onclick="deleteTask(${task.id})">删除</button>` : ''}
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
    
    paginationInfo.textContent = `共 ${globalData.tasks.length} 条记录，第 1/1 页`;
}

// 获取状态样式类
function getStatusClass(status) {
    switch(status) {
        case '进行中': return 'processing';
        case '已完成': return 'completed';
        case '未开始': return 'pending';
        default: return 'pending';
    }
}

// 页面切换
function showPage(pageId) {
    // 隐藏所有页面
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => page.style.display = 'none');
    
    // 显示指定页面
    document.getElementById(pageId).style.display = 'block';
    
    // 更新导航状态
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => item.classList.remove('active'));
    
    // 查找对应的导航项
    navItems.forEach(item => {
        const text = item.textContent.trim();
        if ((pageId === 'task-management' && text === '任务管理') ||
            (pageId === 'quick-response' && text === '快捷应答')) {
            item.classList.add('active');
        }
    });

    // 停止自动刷新
    stopAutoRefresh();
    
    // 如果切换到任务管理页面，重新渲染任务列表
    if (pageId === 'task-management') {
        renderTaskList();
    }
}

// 标签页切换
function showTab(tabId) {
    // 隐藏所有标签页内容
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => content.style.display = 'none');
    
    // 显示指定标签页内容
    document.getElementById(tabId).style.display = 'block';
    
    // 更新标签页状态
    const tabs = document.querySelectorAll('.tab');
    tabs.forEach(tab => tab.classList.remove('active'));
    event.target.classList.add('active');

    // 如果是条目管理页签，启动自动刷新
    if (tabId === 'item-management') {
        startAutoRefresh();
    } else {
        stopAutoRefresh();
    }
    
    // 如果是数据分析页签，更新指派人选项并渲染统计数据
    if (tabId === 'data-analysis' && globalData.currentTask) {
        // 先更新指派人筛选选项
        updateAnalysisAssigneeOptions();
        
        // 获取当前选中的指派人筛选条件
        const assigneeSelect = document.querySelector('#data-analysis select[onchange="filterAnalysisData()"]');
        const selectedAssignee = assigneeSelect ? assigneeSelect.value : '';
        
        // 渲染统计数据，应用筛选条件
        renderAnalysisStats(selectedAssignee);
        renderProductStats(selectedAssignee);
        
        console.log('切换到数据分析页面，当前任务:', globalData.currentTask.id, '筛选条件:', selectedAssignee);
    }
}

// 自动刷新功能
function startAutoRefresh() {
    const refreshDisplay = document.getElementById('autoRefresh');
    const timerDisplay = document.getElementById('refreshTimer');
    
    if (!refreshDisplay || !timerDisplay) {
        console.warn('自动刷新UI元素未找到');
        return;
    }
    
    // 停止之前的刷新计时器
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
    
    refreshDisplay.style.display = 'block';
    refreshCountdown = 5; // 改为5秒刷新间隔
    timerDisplay.textContent = refreshCountdown;
    
    console.log('启动自动刷新，间隔:', refreshCountdown, '秒');
    
    autoRefreshInterval = setInterval(() => {
        refreshCountdown--;
        timerDisplay.textContent = refreshCountdown;
        
        if (refreshCountdown <= 0) {
            // 检查是否有正在编辑的条目，如果有则跳过刷新
            if (!editingItemId) {
                console.log('执行自动刷新...');
            refreshItemList();
            } else {
                console.log('跳过自动刷新：有条目正在编辑中');
            }
            refreshCountdown = 5; // 重置为5秒
            timerDisplay.textContent = refreshCountdown;
        }
    }, 1000);
}

function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
    }
    document.getElementById('autoRefresh').style.display = 'none';
}

function refreshItemList() {
    console.log('自动刷新条目列表');
    
    // 如果有条目正在编辑，则不执行刷新
    if (editingItemId) {
        console.log('跳过刷新：条目正在编辑中');
        return;
    }
    
    // 保存当前勾选状态和其他UI状态
    const selectedCheckboxes = document.querySelectorAll('input[name="itemSelect"]:checked');
    globalData.selectedItems.clear();
    selectedCheckboxes.forEach(checkbox => {
        globalData.selectedItems.add(checkbox.value);
    });
    globalData.selectAllState = document.getElementById('selectAll')?.checked || false;
    
    // 保存当前的列筛选状态
    const columnFilterState = {};
    document.querySelectorAll('.column-filter input[type="checkbox"]').forEach(checkbox => {
        columnFilterState[checkbox.value] = checkbox.checked;
    });
    
    // 保存当前的查询条件
    const queryState = {
        code: document.getElementById('queryCode')?.value || '',
        description: document.getElementById('queryDescription')?.value || '',
        product: document.getElementById('queryProduct')?.value || '',
        status: document.getElementById('queryStatus')?.value || '',
        tag: document.getElementById('queryTag')?.value || '',
        satisfaction: document.getElementById('querySatisfaction')?.value || '',
        assignee: document.getElementById('queryAssignee')?.value || ''
    };
    
    if (globalData.currentTask) {
        // 如果有查询条件，执行查询而不是简单的renderItemList
        const hasQueryConditions = Object.values(queryState).some(value => value.trim() !== '');
        
        if (hasQueryConditions) {
            // 有查询条件时，重新执行查询
            console.log('重新执行查询刷新');
            queryItems();
        } else {
            // 没有查询条件时，直接渲染所有条目
            console.log('渲染所有条目');
        renderItemList(globalData.currentTask.id);
        }
        
        // 恢复勾选状态
        setTimeout(() => {
            // 恢复全选状态
            const selectAllCheckbox = document.getElementById('selectAll');
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = globalData.selectAllState;
            }
            
            // 恢复单个条目的勾选状态
            globalData.selectedItems.forEach(itemId => {
                const checkbox = document.querySelector(`input[name="itemSelect"][value="${itemId}"]`);
                if (checkbox && !checkbox.disabled) {
                    checkbox.checked = true;
                }
            });
            
            // 恢复列筛选状态
            Object.entries(columnFilterState).forEach(([value, checked]) => {
                const checkbox = document.querySelector(`.column-filter input[type="checkbox"][value="${value}"]`);
                if (checkbox) {
                    checkbox.checked = checked;
                }
            });
            
            // 重新应用列筛选
            applyCurrentColumnFilter();
            
            console.log('状态恢复完成 - 勾选项:', globalData.selectedItems.size, '全选:', globalData.selectAllState);
        }, 100);
        
        // 更新数据分析如果可见
        updateDataAnalysisIfVisible();
    }
}

// 全选功能
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const itemSelects = document.querySelectorAll('input[name="itemSelect"]');
    
    itemSelects.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// 点击模态框外部关闭
document.addEventListener('click', function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.classList.remove('show');
        }
    });
});

// 调试函数 - 可以在浏览器控制台中调用
window.debugSOC = function() {
    console.log('=== SOC系统调试信息 ===');
    console.log('📋 任务数量:', globalData.tasks.length);
    console.log('📝 条目数量:', globalData.items.length);
    console.log('🎯 当前任务:', globalData.currentTask);
    
    // 检查各任务的条目分布
    const taskItemCounts = globalData.tasks.map(task => {
        const itemCount = globalData.items.filter(item => item.taskId === task.id).length;
        return {id: task.id, name: task.name, itemCount: itemCount};
    });
    console.log('📊 各任务条目数量:', taskItemCounts);
    
    // 检查DOM元素
    const tbody = document.getElementById('itemTableBody');
    console.log('🔍 itemTableBody元素存在:', !!tbody);
    if (tbody) {
        console.log('📏 当前显示的行数:', tbody.children.length);
    }
    
    return {
        tasks: globalData.tasks.length,
        items: globalData.items.length,
        currentTask: globalData.currentTask,
        hasItemTableBody: !!tbody,
        itemDistribution: taskItemCounts
    };
};

console.log('💡 提示：可以在控制台中输入 debugSOC() 来查看系统调试信息'); 

// 切换产品查看
function switchProduct() {
    const selectedProduct = event.target.value;
    console.log('切换产品:', selectedProduct);
    // 这里应该重新加载该产品的应答数据
}

// 应答页面标签页切换
function showResponseTab(tabId) {
    // 隐藏所有标签页内容
    document.querySelectorAll('#manual-response .tab-content').forEach(content => {
        content.style.display = 'none';
    });
    
    // 显示选中的标签页内容
    const targetTab = document.getElementById(tabId);
    if (targetTab) {
        targetTab.style.display = 'block';
    }
    
    // 更新标签页状态
    document.querySelectorAll('#manual-response .tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    document.querySelector(`#manual-response .tab[onclick="showResponseTab('${tabId}')"]`).classList.add('active');
    
    // 如果是匹配详情页，初始化数据
    if (tabId === 'match-details') {
        // 重置到第一页
        currentMatchPage = 1;
        // 重置筛选条件
        const filterSelect = document.querySelector('#match-details select');
        if (filterSelect) {
            filterSelect.value = '';
        }
        // 显示所有匹配结果
        filterMatches();
    }
}

// AI功能
function aiEnhance() {
    const additionalInfo = document.getElementById('additionalInfo');
    
    // 显示处理中状态
    const btn = event.target;
    const originalText = btn.textContent;
    btn.disabled = true;
    btn.textContent = '处理中...';
    
    // 模拟AI处理
    setTimeout(() => {
        additionalInfo.value = '基于华为云Stack的基础架构管理能力分析：\n1. 支持OpenStack标准API\n2. 提供统一的资源管理界面\n3. 支持多种虚拟化技术\n4. 具备自动化运维能力';
        
        btn.disabled = false;
        btn.textContent = originalText;
        
        alert('AI应答补充信息已生成！');
    }, 2000);
}

function aiPolish() {
    const content = document.getElementById('responseContent');
    const originalContent = content.innerHTML;
    
    // 显示AI结果面板
    showAiResult('AI润色结果', '华为云Stack基于先进的OpenStack架构，提供企业级云基础设施即服务(IaaS)解决方案。该平台具备完整的基础架构管理能力，能够实现虚拟机、容器、存储等多种计算资源的统一管理和智能调度，为用户提供稳定可靠的云服务体验。', content);
}

function aiTranslate() {
    const content = document.getElementById('responseContent');
    const originalContent = content.innerHTML;
    
    // 显示AI翻译结果
    showAiResult('AI翻译结果', 'Huawei Cloud Stack provides unified cloud platform infrastructure management capabilities, supporting unified management of virtual machines, containers, storage and other resources. Through the FusionSphere virtualization platform, dynamic allocation and management of computing, storage, and network resources can be achieved.', content);
}

function showAiResult(title, result, targetElement) {
    // 创建AI结果面板
    let resultPanel = document.querySelector('.ai-result-panel');
    if (!resultPanel) {
        resultPanel = document.createElement('div');
        resultPanel.className = 'ai-result-panel';
        targetElement.parentNode.appendChild(resultPanel);
    }
    
    resultPanel.innerHTML = `
        <div class="ai-result-header">
            <span class="ai-result-title">${title}</span>
            <div class="ai-result-actions">
                <button class="btn btn-sm" onclick="closeAiResult()">取消</button>
                <button class="btn btn-primary btn-sm" onclick="applyAiResult()">应用</button>
            </div>
        </div>
        <div class="ai-result-content">${result}</div>
    `;
    
    resultPanel.classList.add('show');
    resultPanel.setAttribute('data-target', targetElement.id);
}

function applyAiResult() {
    const resultPanel = document.querySelector('.ai-result-panel.show');
    const targetId = resultPanel.getAttribute('data-target');
    const targetElement = document.getElementById(targetId);
    const resultContent = resultPanel.querySelector('.ai-result-content').textContent;
    
    if (targetElement.contentEditable === 'true') {
        targetElement.innerHTML = resultContent;
    } else {
        targetElement.value = resultContent;
    }
    
    closeAiResult();
    alert('AI结果已应用！');
}

function closeAiResult() {
    const resultPanel = document.querySelector('.ai-result-panel.show');
    if (resultPanel) {
        resultPanel.classList.remove('show');
    }
}

// 编辑器功能
function formatText(command) {
    document.execCommand(command, false, null);
}

function insertImage() {
    const url = prompt('请输入图片URL:');
    if (url) {
        document.execCommand('insertImage', false, url);
    }
}

function viewSource() {
    window.open('https://gbbs.example.com/item/001', '_blank');
}

// 历史版本功能
function loadHistoryVersion() {
    const version = event.target.value;
    if (!version) return;
    
    console.log('加载历史版本:', version);
    
    // 模拟加载历史版本数据
    const versionData = {
        'v1': {
            content: '华为云Stack提供基础的云平台管理能力。',
            satisfaction: 'PC',
            updateTime: '2024-01-15 10:30'
        },
        'v2': {
            content: '华为云Stack提供统一的云平台基础架构管理能力，支持虚拟机管理。',
            satisfaction: 'PC', 
            updateTime: '2024-01-15 11:15'
        },
        'current': {
            content: '华为云Stack提供统一的云平台基础架构管理能力，支持虚拟机、容器等多种计算资源的统一管理。',
            satisfaction: 'PC',
            updateTime: '2024-01-15 14:20'
        }
    };
    
    if (versionData[version]) {
        const data = versionData[version];
        document.getElementById('responseContent').innerHTML = data.content;
        alert(`已加载${version === 'current' ? '当前' : '历史'}版本 (${data.updateTime})`);
    }
}

// 全局变量存储选中的历史版本数据
let selectedVersionData = null;

// 历史版本选择变化事件
function onHistoryVersionChange() {
    const selectElement = document.getElementById('historyVersionSelect');
    const selectedVersion = selectElement.value;
    
    if (!selectedVersion) {
        selectedVersionData = null;
        return;
    }
    
    // 模拟历史版本数据
    const versionDataMap = {
        'v1': {
            additionalInfo: '基于GBBS数据库的初步分析结果',
            satisfaction: 'PC',
            responseContent: 'FusionSphere提供基础的虚拟化平台管理能力，支持基本的虚拟机创建和管理功能。',
            sourceIndex: 'GBBS-001',
            sourceOrigin: 'GBBS',
            remark: '版本1的基础应答内容',
            updateTime: '2024-01-15 10:30',
            updateUser: '系统AI',
            responseMethod: 'AI应答'
        },
        'v2': {
            additionalInfo: '结合用户反馈进行的手工修改和完善',
            satisfaction: 'FC',
            responseContent: 'FusionSphere虚拟化平台支持多种虚拟化资源的统一管理，包括计算、存储、网络资源的动态分配和调度。提供完整的虚拟化基础架构管理能力，支持企业级应用场景。',
            sourceIndex: 'GBBS-001',
            sourceOrigin: 'GBBS',
            remark: '手工优化后的详细应答内容',
            updateTime: '2024-01-15 11:15',
            updateUser: '张三（123456）',
            responseMethod: '手工应答'
        },
        'v3': {
            additionalInfo: '基于最新产品特性的AI增强应答',
            satisfaction: 'FC',
            responseContent: 'FusionSphere虚拟化平台基于成熟的虚拟化技术，提供云平台基础架构的统一管理能力。支持虚拟机、容器、存储等多种计算资源的统一管理和调度，具备高可用、高性能、易管理的特点，满足企业级云计算场景的全面需求。',
            sourceIndex: 'GBBS-001',
            sourceOrigin: 'GBBS',
            remark: 'AI增强后的完整应答内容',
            updateTime: '2024-01-15 14:20',
            updateUser: '系统AI',
            responseMethod: 'AI应答'
        },
        'current': {
            additionalInfo: '当前正在编辑的版本',
            satisfaction: 'PC',
            responseContent: 'FusionSphere虚拟化平台...',
            sourceIndex: 'GBBS-001',
            sourceOrigin: 'GBBS',
            remark: '',
            updateTime: '2024-01-15 16:45',
            updateUser: '张三（123456）',
            responseMethod: '手工应答'
        }
    };
    
    selectedVersionData = versionDataMap[selectedVersion];
    
    // 立即应用历史版本数据到表单
    if (selectedVersionData) {
        try {
            // 应用补充信息
            const additionalInfoElement = document.getElementById('additionalInfo');
            if (additionalInfoElement) {
                additionalInfoElement.value = selectedVersionData.additionalInfo || '';
            }
            
            // 应用满足度
            const satisfactionSelect = document.querySelector('#response-result select');
            if (satisfactionSelect) {
                satisfactionSelect.value = selectedVersionData.satisfaction || '';
            }
            
            // 应用应答说明
            const responseContent = document.getElementById('responseContent');
            if (responseContent) {
                responseContent.innerHTML = selectedVersionData.responseContent || '';
            }
            
            // 应用索引
            const indexInput = document.querySelector('input[value="GBBS-001"]');
            if (indexInput) {
                indexInput.value = selectedVersionData.sourceIndex || 'GBBS-001';
            }
            
            // 应用来源
            const sourceInput = document.querySelector('input[value="GBBS"]');
            if (sourceInput) {
                sourceInput.value = selectedVersionData.sourceOrigin || 'GBBS';
            }
            
            // 应用备注
            const remarkTextarea = document.querySelector('textarea[placeholder="可添加额外的描述"]');
            if (remarkTextarea) {
                remarkTextarea.value = selectedVersionData.remark || '';
            }
            
            console.log('历史版本数据已应用:', selectedVersionData);
            
        } catch (error) {
            console.error('应用历史版本时发生错误:', error);
            alert('应用历史版本时发生错误，请重试');
        }
    }
    
    console.log('选择历史版本:', selectedVersion, selectedVersionData);
}

function compareVersions() {
    // 显示版本对比弹窗
    alert('版本对比功能开发中...');
}

// 匹配详情功能
function filterMatches() {
    const filterSelect = event.target;
    const minMatch = parseInt(filterSelect.value) || 0;
    
    console.log('筛选匹配结果，最小匹配度:', minMatch);
    
    // 模拟匹配数据
    const allMatches = [
        { 
            id: 1, 
            percentage: 95, 
            satisfaction: 'FC', 
            description: '云平台基础架构统一管理能力，支持虚拟机、容器、存储资源管理', 
            strategy: '标准化口径',
            country: '中国',
            company: '华为技术有限公司',
            customer: '某银行',
            responseDescription: '华为云Stack基于OpenStack架构，提供完整的基础设施即服务(IaaS)能力...', 
            index: 'GBBS-HCS-001' 
        },
        { 
            id: 2, 
            percentage: 87, 
            satisfaction: 'PC', 
            description: '云平台基础架构管理，包括计算、存储、网络资源的统一调度', 
            strategy: '客户定制',
            country: '中国',
            company: '华为技术有限公司',
            customer: '某保险公司',
            responseDescription: '华为云Stack提供云平台基础架构管理能力，通过统一的管理平台...', 
            index: 'GBBS-HCS-002' 
        },
        { 
            id: 3, 
            percentage: 82, 
            satisfaction: 'PC', 
            description: '基础设施即服务平台，提供虚拟化资源管理', 
            strategy: '客户保守',
            country: '新加坡',
            company: '华为软件技术有限公司',
            customer: '某银行',
            responseDescription: '华为云Stack基于成熟的虚拟化技术，提供IaaS服务...', 
            index: 'GBBS-HCS-003' 
        },
        { 
            id: 4, 
            percentage: 78, 
            satisfaction: 'PC', 
            description: '虚拟化资源管理和调度能力', 
            strategy: '客户积极',
            country: '德国',
            company: '华为技术有限公司',
            customer: '某制造企业',
            responseDescription: 'FusionSphere提供完整的虚拟化资源管理...', 
            index: 'GBBS-FS-001' 
        },
        { 
            id: 5, 
            percentage: 75, 
            satisfaction: 'NC', 
            description: '云平台监控和运维管理能力', 
            strategy: '策略不确定',
            country: '英国',
            company: '华为云计算技术有限公司',
            customer: '某电信公司',
            responseDescription: '基础的监控运维功能，需要扩展...', 
            index: 'GBBS-MON-001' 
        },
        { 
            id: 6, 
            percentage: 72, 
            satisfaction: 'PC', 
            description: '容器编排和管理平台', 
            strategy: '标准化口径',
            country: '中国',
            company: '华为云计算技术有限公司',
            customer: '某银行',
            responseDescription: '支持Kubernetes容器编排...', 
            index: 'GBBS-K8S-001' 
        },
        { 
            id: 7, 
            percentage: 68, 
            satisfaction: 'NC', 
            description: '安全防护和访问控制', 
            strategy: '客户保守',
            country: '美国',
            company: '华为技术有限公司',
            customer: '某政府机构',
            responseDescription: '基础安全防护机制...', 
            index: 'GBBS-SEC-001' 
        },
        { 
            id: 8, 
            percentage: 65, 
            satisfaction: 'NC', 
            description: '数据备份和恢复能力', 
            strategy: '策略不确定',
            country: '法国',
            company: '华为软件技术有限公司',
            customer: '某制造企业',
            responseDescription: '基础的备份恢复功能...', 
            index: 'GBBS-BAK-001' 
        },
        { 
            id: 9, 
            percentage: 92, 
            satisfaction: 'FC', 
            description: '高可用性和容灾设计', 
            strategy: '客户定制',
            country: '中国',
            company: '华为技术有限公司',
            customer: '某银行',
            responseDescription: '完整的高可用和容灾解决方案...', 
            index: 'GBBS-HA-001' 
        }
    ];
    
    // 根据匹配度筛选
    const filteredMatches = allMatches.filter(match => match.percentage >= minMatch);
    
    // 更新显示的匹配结果
    renderMatchCards(filteredMatches);
    
    // 更新统计信息
    updateMatchStats(filteredMatches);
}

// 渲染匹配卡片
function renderMatchCards(matches) {
    const container = document.getElementById('matchCardsGrid');
    if (!container) return;
    
    container.innerHTML = matches.map(match => `
        <div class="match-card">
            <div class="match-card-header">
                <div class="match-score">
                    <span class="match-percentage">${match.percentage}%</span>
                    <div class="match-location">${match.country}/${match.company}/${match.customer}</div>
                </div>
                <button class="btn btn-primary btn-sm" onclick="applyMatch(${match.id})">应用</button>
            </div>
            <div class="match-card-content">
                <div class="match-field" style="margin-bottom: 8px;">
                    <strong>条目描述：</strong>${match.description}
                </div>
                <div class="match-field" style="margin-bottom: 8px;">
                    <strong>应答策略：</strong>
                    <span class="strategy-tag">${match.strategy}</span>
                </div>
                <div class="match-field" style="margin-bottom: 8px;">
                    <strong>满足度：</strong><span class="satisfaction-tag ${match.satisfaction.toLowerCase()}">${match.satisfaction}</span>
                </div>
                <div class="match-field" style="margin-bottom: 8px;">
                    <strong>应答说明：</strong>${match.responseDescription}
                </div>
                <div class="match-field">
                    <strong>索引：</strong><a href="#" target="_blank">${match.index}</a>
                </div>
            </div>
        </div>
    `).join('');
}

// 生成星级显示
function generateStars(percentage) {
    const fullStars = Math.floor(percentage / 33); // 每33%一颗星
    const stars = [];
    for (let i = 0; i < 3; i++) {
        stars.push(`<span class="star ${i < fullStars ? 'active' : ''}"">★</span>`);
    }
    return stars.join('');
}

// 更新匹配统计信息
function updateMatchStats(matches) {
    const fcCount = matches.filter(m => m.satisfaction === 'FC').length;
    const pcCount = matches.filter(m => m.satisfaction === 'PC').length;
    const ncCount = matches.filter(m => m.satisfaction === 'NC').length;
    
    // 更新页面中的统计显示
    const statsElements = document.querySelectorAll('.match-stats .stat-item');
    if (statsElements.length >= 4) {
        statsElements[0].textContent = `总数: ${matches.length}`;
        statsElements[1].textContent = `FC: ${fcCount}`;
        statsElements[2].textContent = `PC: ${pcCount}`;
        statsElements[3].textContent = `NC: ${ncCount}`;
    }
}

// 匹配详情分页功能
let currentMatchPage = 1;
const matchPageSize = 9; // 3行3列，每页9个
let totalMatchItems = 0;

function nextMatchPage() {
    const totalPages = Math.ceil(totalMatchItems / matchPageSize);
    if (currentMatchPage < totalPages) {
        currentMatchPage++;
        // 重新应用当前筛选条件
        filterMatches();
        updateMatchPagination(totalMatchItems);
    }
}

function prevMatchPage() {
    if (currentMatchPage > 1) {
        currentMatchPage--;
        // 重新应用当前筛选条件
        filterMatches();
        updateMatchPagination(totalMatchItems);
    }
}

function updateMatchPagination(totalItems) {
    totalMatchItems = totalItems;
    const totalPages = Math.ceil(totalItems / matchPageSize);
    
    // 更新分页信息
    const paginationInfo = document.querySelector('.match-pagination .pagination-info');
    if (paginationInfo) {
        paginationInfo.textContent = `第 ${currentMatchPage}/${totalPages} 页`;
    }
    
    // 更新按钮状态
    const prevBtn = document.querySelector('.match-pagination button[onclick="prevMatchPage()"]');
    const nextBtn = document.querySelector('.match-pagination button[onclick="nextMatchPage()"]');
    
    if (prevBtn) {
        prevBtn.disabled = currentMatchPage <= 1;
    }
    if (nextBtn) {
        nextBtn.disabled = currentMatchPage >= totalPages;
    }
}

function loadMatchPage(page) {
    // 模拟加载不同页面的匹配数据
    const matchGrid = document.getElementById('matchCardsGrid');
    if (!matchGrid) return;
    
    // 这里可以根据页面加载不同的匹配数据
    console.log(`加载第 ${page} 页的匹配数据`);
}

function toggleMatchSource(sourceId) {
    const content = document.getElementById(`${sourceId}-content`);
    const toggle = document.getElementById(`${sourceId}-toggle`);
    
    if (content.classList.contains('collapsed')) {
        content.classList.remove('collapsed');
        toggle.classList.remove('collapsed');
        toggle.textContent = '▼';
    } else {
        content.classList.add('collapsed');
        toggle.classList.add('collapsed');
        toggle.textContent = '▶';
    }
}

function applyMatch(matchId) {
    if (confirm('将覆盖当前应答结果，请确认是否继续！')) {
        console.log('应用匹配结果:', matchId);
        
        // 模拟应用匹配数据
        const matchData = {
            1: {
                content: '华为云Stack基于OpenStack架构，提供完整的基础设施即服务(IaaS)能力，支持虚拟机、容器、存储等资源的统一管理和调度。',
                satisfaction: 'FC',
                index: 'GBBS-HCS-001'
            },
            2: {
                content: '华为云Stack提供云平台基础架构管理能力，通过统一的管理平台实现计算、存储、网络资源的集中管理和动态分配。',
                satisfaction: 'PC',
                index: 'GBBS-HCS-002'
            },
            3: {
                content: '华为云Stack基于成熟的虚拟化技术，提供IaaS服务，支持虚拟机的创建、配置、监控等全生命周期管理。',
                satisfaction: 'PC',
                index: 'GBBS-HCS-003'
            }
        };
        
        if (matchData[matchId]) {
            const data = matchData[matchId];
            // 切换到应答结果页签
            showResponseTab('response-result');
            // 应用数据
            document.getElementById('responseContent').innerHTML = data.content;
            // 更新满足度
            const satisfactionSelect = document.querySelector('#response-result select');
            satisfactionSelect.value = data.satisfaction;
            
            alert('匹配结果已应用，应答方式已设置为AI！');
        }
    }
}

// 保存和重置功能
function saveResponse() {
    const itemId = globalData.currentItem?.id;
    const item = globalData.items.find(i => i.id === itemId);
    
    if (!item) {
        console.error('当前条目不存在');
        return;
    }
    
    // 获取表单数据
    const additionalInfo = document.getElementById('additionalInfo')?.value || '';
    const satisfactionSelect = document.querySelector('#response-result select');
    const satisfaction = satisfactionSelect?.value || '';
    const responseContent = document.getElementById('responseContent')?.innerHTML || '';
    const indexInput = document.querySelector('input[value="GBBS-001"]');
    const sourceInput = document.querySelector('input[value="GBBS"]');
    const remarkTextarea = document.querySelector('textarea[placeholder="可添加额外的描述"]');
    
    // 应用历史版本数据（如果有选择）
    if (selectedVersionData) {
        item.additionalInfo = selectedVersionData.additionalInfo || additionalInfo;
        item.satisfaction = selectedVersionData.satisfaction || satisfaction;
        item.responseContent = selectedVersionData.responseContent || responseContent;
        item.index = selectedVersionData.sourceIndex || (indexInput?.value || 'GBBS-001');
        item.source = selectedVersionData.sourceOrigin || (sourceInput?.value || 'GBBS');
        item.remark = selectedVersionData.remark || (remarkTextarea?.value || '');
        
        console.log('应用历史版本数据:', selectedVersionData);
    } else {
        // 使用当前表单数据
        item.additionalInfo = additionalInfo;
        item.satisfaction = satisfaction;
        item.responseContent = responseContent;
        item.index = indexInput?.value || 'GBBS-001';
        item.source = sourceInput?.value || 'GBBS';
        item.remark = remarkTextarea?.value || '';
    }
    
    // 更新条目状态
            item.status = '已应答';
            item.responseMethod = '手工应答';
    item.responseDescription = item.responseContent;
            item.updateUser = '张三（123456）';
            item.updateTime = new Date().toLocaleString('zh-CN');
    
    // 清除原始状态备份
    delete item.originalStatus;
    delete item.originalResponseMethod;
    
    // 清除版本选择数据
    selectedVersionData = null;
    
    // 返回条目列表页面
    showPage('task-detail');
    
    // 重新渲染列表
    renderItemList(globalData.currentTask?.id);
    
    // 如果数据分析页面正在显示，则更新数据
    updateDataAnalysisIfVisible();
    
    // 更新任务进度
    updateTaskProgress(globalData.currentTask?.id);
    
    console.log('保存应答成功:', item);
}

function resetResponse() {
    if (confirm('将放弃当前的编辑内容，确定要取消吗？')) {
        console.log('取消应答，恢复原始状态');
        
        // 如果有当前条目，恢复其原始状态
        if (globalData.currentItem) {
            const item = globalData.items.find(i => i.id === globalData.currentItem.id);
            if (item) {
                // 恢复原始状态
                if (item.originalStatus !== undefined) {
                    item.status = item.originalStatus;
                    item.responseMethod = item.originalResponseMethod || null;
                    item.updateTime = new Date().toLocaleString('zh-CN');
                    
                    // 清除原始状态备份
                    delete item.originalStatus;
                    delete item.originalResponseMethod;
            
            // 更新当前条目引用
            globalData.currentItem = item;
            
            // 如果是从任务详情页进入的，更新任务统计并返回任务详情页
            if (globalData.currentTask) {
                updateTaskItemCount(globalData.currentTask.id);
                renderTaskList(); // 更新任务列表
                
                        // 返回到任务详情页
                openTaskDetail(globalData.currentTask.id);
                    }
                    
                    // 取消成功，不显示提示，直接返回
                } else {
                    // 没有找到原始状态，也不显示提示
                }
            }
        } else {
            // 如果没有当前条目信息，只是重置表单内容
        document.getElementById('responseContent').innerHTML = '华为云Stack提供统一的云平台基础架构管理能力，支持虚拟机、容器等多种计算资源的统一管理。通过FusionSphere虚拟化平台，可以实现计算、存储、网络资源的动态分配和管理。';
            // 重置成功，不显示提示
        }
    }
}

// 批量标签输入处理
document.addEventListener('DOMContentLoaded', function() {
    const batchTagInput = document.getElementById('batchTagInput');
    if (batchTagInput) {
        batchTagInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                const tagValue = this.value.trim();
                if (tagValue) {
                    const tagList = document.getElementById('batchTagList');
                    const tagElement = document.createElement('span');
                    tagElement.className = 'tag primary';
                    tagElement.innerHTML = `${tagValue} <span onclick="this.parentNode.remove()" style="cursor: pointer; margin-left: 4px;">×</span>`;
                    tagList.appendChild(tagElement);
                    this.value = '';
                }
            }
        });
    }
});

// 复制文件上传处理
document.addEventListener('DOMContentLoaded', function() {
    const copyFileInput = document.getElementById('copyFileInput');
    if (copyFileInput) {
        copyFileInput.addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const uploadArea = event.target.parentNode;
                uploadArea.innerHTML = `
                    <div class="upload-icon">✓</div>
                    <div class="upload-text">已选择文件: ${file.name}</div>
                `;
            }
        });
    }
});

function refreshTasks() {
    console.log('刷新任务列表');
    renderTaskList();
}

function openTaskDetail(taskId) {
    const task = globalData.tasks.find(t => t.id === taskId);
    if (!task) {
        console.error('任务不存在:', taskId);
        return;
    }
    
        globalData.currentTask = task;
        
    // 显示任务详情页面
    showPage('task-detail');
    
    // 更新面包屑
    const taskDetailTitle = document.getElementById('taskDetailTitle');
    if (taskDetailTitle) {
        taskDetailTitle.textContent = task.title || task.name;
    }
    
    // 渲染任务信息头部
        const taskInfoHeader = document.getElementById('taskInfoHeader');
        if (taskInfoHeader) {
            taskInfoHeader.innerHTML = `
                <div class="task-info-grid">
                    <div class="task-info-item">
                        <div class="task-info-label">国家/MTO</div>
                    <div class="task-info-value">${task.country || '中国'}</div>
                    </div>
                    <div class="task-info-item">
                        <div class="task-info-label">省公司/分支</div>
                    <div class="task-info-value">${task.company || '华为技术有限公司'}</div>
                    </div>
                    <div class="task-info-item">
                        <div class="task-info-label">客户</div>
                    <div class="task-info-value">${task.customer || '某银行'}</div>
                    </div>
                    <div class="task-info-item">
                        <div class="task-info-label">项目</div>
                    <div class="task-info-value">${task.project || '量子计算研发'}</div>
                    </div>
                </div>
            `;
        }
        
    // 渲染条目列表（默认显示条目管理页签）
    renderItemList(taskId);
        
    // 开始自动刷新
        startAutoRefresh();
        
    // 如果当前显示的是数据分析页签，则同时更新数据分析数据
    updateDataAnalysisIfVisible();
    
    console.log('打开任务详情:', task.title || task.name, '任务ID:', taskId);
}

function editTask(taskId) {
    console.log('编辑任务:', taskId);
    
    const task = globalData.tasks.find(t => t.id === taskId);
    if (task) {
        // 填充编辑表单
        document.getElementById('editTaskId').value = task.id;
        document.getElementById('editTaskName').value = task.name;
        document.getElementById('editTaskCountry').value = task.country;
        document.getElementById('editTaskCompany').value = task.company;
        document.getElementById('editTaskCustomer').value = task.customer;
        document.getElementById('editTaskProject').value = task.project;
        
        showEditTaskModal();
    }
}

function updateTask() {
    const taskId = parseInt(document.getElementById('editTaskId').value);
    const task = globalData.tasks.find(t => t.id === taskId);
    
    if (task) {
        task.name = document.getElementById('editTaskName').value;
        task.country = document.getElementById('editTaskCountry').value;
        task.company = document.getElementById('editTaskCompany').value;
        task.customer = document.getElementById('editTaskCustomer').value;
        task.project = document.getElementById('editTaskProject').value;
        task.updateTime = new Date().toLocaleString('zh-CN');
        
        renderTaskList();
        hideEditTaskModal();
        
        console.log('更新任务:', task);
        alert('任务更新成功！');
    }
}

function copyTask(taskId) {
    console.log('复制任务:', taskId);
    
    const task = globalData.tasks.find(t => t.id === taskId);
    if (task) {
        // 填充复制表单
        document.getElementById('copyTaskId').value = task.id;
        document.getElementById('copyTaskName').value = task.name + ' - 副本';
        document.getElementById('copyTaskCountry').value = task.country;
        document.getElementById('copyTaskCompany').value = task.company;
        document.getElementById('copyTaskCustomer').value = task.customer;
        document.getElementById('copyTaskProject').value = task.project;
        document.getElementById('copyItemResults').checked = false;
        
        showCopyTaskModal();
    }
}

function showCopyTaskModal() {
    document.getElementById('copyTaskModal').classList.add('show');
}

function hideCopyTaskModal() {
    document.getElementById('copyTaskModal').classList.remove('show');
}

function confirmCopyTask() {
    const originalTaskId = parseInt(document.getElementById('copyTaskId').value);
    const copyItemResults = document.getElementById('copyItemResults').checked;
    
    const newId = Math.max(...globalData.tasks.map(t => t.id)) + 1;
    const newTaskCode = `TASK${String(newId).padStart(3, '0')}`;
    
    const originalTask = globalData.tasks.find(t => t.id === originalTaskId);
    
    const newTask = {
        ...originalTask,
        id: newId,
        code: newTaskCode,
        name: document.getElementById('copyTaskName').value,
        country: document.getElementById('copyTaskCountry').value,
        company: document.getElementById('copyTaskCompany').value,
        customer: document.getElementById('copyTaskCustomer').value,
        project: document.getElementById('copyTaskProject').value,
        itemCount: 0,
        completedCount: 0,
        satisfaction: 0,
        status: '未开始',
        createTime: new Date().toLocaleString('zh-CN'),
        updateTime: new Date().toLocaleString('zh-CN')
    };
    
    globalData.tasks.push(newTask);
    
    // 复制条目
    const originalItems = globalData.items.filter(item => item.taskId === originalTaskId);
    originalItems.forEach(originalItem => {
        const newItemId = Math.max(...globalData.items.map(i => i.id)) + 1;
        const newItem = {
            ...originalItem,
            id: newItemId,
            taskId: newId,
            status: copyItemResults ? originalItem.status : '未应答',
            satisfaction: copyItemResults ? originalItem.satisfaction : null,
            responseMethod: copyItemResults ? originalItem.responseMethod : null,
            responseContent: copyItemResults ? originalItem.responseContent : null,
            updateUser: '张三（123456）',
            updateTime: new Date().toLocaleString('zh-CN')
        };
        globalData.items.push(newItem);
    });
    
    // 更新新任务的条目计数
    updateTaskItemCount(newId);
    renderTaskList();
    hideCopyTaskModal();
    
    alert('任务复制成功！');
}

function deleteTask(taskId) {
    if (confirm('确定要删除此任务吗？此操作不可恢复。')) {
        console.log('删除任务:', taskId);
        
        // 删除任务相关的所有条目
        globalData.items = globalData.items.filter(item => item.taskId !== taskId);
        
        // 删除任务
        const taskIndex = globalData.tasks.findIndex(t => t.id === taskId);
        if (taskIndex > -1) {
            globalData.tasks.splice(taskIndex, 1);
        }
        
        renderTaskList();
        alert('任务删除成功！');
    }
}

function showEditTaskModal() {
    document.getElementById('editTaskModal').classList.add('show');
}

function hideEditTaskModal() {
    document.getElementById('editTaskModal').classList.remove('show');
}

// 搜索相关函数
function searchTasks() {
    const filters = {
        taskCode: document.getElementById('searchTaskCode').value.trim(),
        taskName: document.getElementById('searchTaskName').value.trim(),
        country: document.getElementById('searchCountry').value,
        customer: document.getElementById('searchCustomer').value.trim(),
        project: document.getElementById('searchProject').value.trim(),
        status: document.getElementById('searchStatus').value
    };
    
    console.log('搜索任务:', filters);
    
    let filteredTasks = globalData.tasks.filter(task => {
        // 任务编码 - 精确查询
        if (filters.taskCode && task.code !== filters.taskCode) return false;
        // 任务名称 - 模糊查询
        if (filters.taskName && !task.name.toLowerCase().includes(filters.taskName.toLowerCase())) return false;
        // 国家 - 精确查询
        if (filters.country && task.country !== filters.country) return false;
        // 客户 - 精确查询
        if (filters.customer && task.customer !== filters.customer) return false;
        // 项目 - 精确查询
        if (filters.project && task.project !== filters.project) return false;
        // 状态 - 精确查询
        if (filters.status && task.status !== filters.status) return false;
        return true;
    });
    
    // 临时替换任务列表进行渲染
    const originalTasks = globalData.tasks;
    globalData.tasks = filteredTasks;
    renderTaskList();
    globalData.tasks = originalTasks;
}

function resetTaskSearch() {
    document.getElementById('searchTaskCode').value = '';
    document.getElementById('searchTaskName').value = '';
    document.getElementById('searchCountry').value = '';
    document.getElementById('searchCustomer').value = '';
    document.getElementById('searchProject').value = '';
    document.getElementById('searchStatus').value = '';
    renderTaskList();
}

// 辅助函数
function clearQuickForm() {
    document.getElementById('quickProductSelect').value = '';
    document.querySelector('#quickResponseForm textarea').value = '';
    const countrySelect = document.querySelector('#quickResponseForm select');
    if (countrySelect) countrySelect.value = '';
    const customerInput = document.querySelector('#quickResponseForm input[placeholder="请输入客户名称"]');
    if (customerInput) customerInput.value = '';
}

function startQuickResponse() {
    const product = document.getElementById('quickProductSelect').value;
    const description = document.querySelector('#quick-response textarea').value.trim();
    const countrySelect = document.querySelector('#quick-response select');
    const companySelect = document.querySelectorAll('#quick-response select')[1];
    const customerInput = document.querySelector('#quick-response input[placeholder="请输入客户名称"]');
    
    if (!product) {
        alert('请选择产品');
        return;
    }
    
    if (!description) {
        alert('请输入条目描述');
        return;
    }
    
    // 显示加载状态
    const submitBtn = event.target;
    const originalText = submitBtn.textContent;
    submitBtn.disabled = true;
    submitBtn.textContent = '处理中...';
    
    // 模拟AI应答处理时间
    setTimeout(() => {
        // 查找是否已存在个人任务
        let personalTask = globalData.tasks.find(task => task.isPersonal);
        
        if (!personalTask) {
            // 如果没有个人任务，创建一个
            const personalTaskId = Math.max(...globalData.tasks.map(t => t.id)) + 1;
            const personalTaskCode = `PERSONAL001`;
            
            personalTask = {
                id: personalTaskId,
                code: personalTaskCode,
                name: '个人任务区',
                country: countrySelect ? countrySelect.value || '中国' : '中国',
                customer: customerInput ? customerInput.value || '个人客户' : '个人客户',
                project: '快捷应答项目',
                company: companySelect ? companySelect.value || '华为技术有限公司' : '华为技术有限公司',
                dataSource: 'GBBS',
                itemCount: 0,
                completedCount: 0,
                satisfaction: 0,
                status: '进行中',
                isPersonal: true,
                createTime: new Date().toLocaleString('zh-CN'),
                updateTime: new Date().toLocaleString('zh-CN')
            };
            
            globalData.tasks.push(personalTask);
            console.log('创建个人任务:', personalTask);
        }
        
        // 在个人任务中创建新条目
        const newItemId = Math.max(...globalData.items.map(i => i.id)) + 1;
        const existingPersonalItems = globalData.items.filter(item => item.taskId === personalTask.id);
        const nextCodeNum = String(existingPersonalItems.length + 1).padStart(3, '0');
        
        const newItem = {
            id: newItemId,
            taskId: personalTask.id,
            code: `QUICK${nextCodeNum}`,
            description: description,
            product: product,
            tags: ['快捷应答'],
            status: '应答中',
            satisfaction: null,
            assignee: '张三（123456）',
            responseMethod: 'AI应答',
            responseContent: null,
            responseDescription: null,
            source: 'GBBS',
            index: null,
            remark: '通过快捷应答创建',
            updateUser: '张三（123456）',
            updateTime: new Date().toLocaleString('zh-CN')
        };
        
        globalData.items.push(newItem);
        console.log('添加条目到个人任务:', newItem);
        
        // 更新个人任务的条目计数
        updateTaskItemCount(personalTask.id);
        updateTaskProgress(personalTask.id);
        
        // 恢复按钮状态
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
        
        // 更新任务列表显示
        renderTaskList();
        
        // 直接跳转到个人任务详情页
        globalData.currentTask = personalTask;
        openTaskDetail(personalTask.id);
        
        // 清空表单
        clearQuickForm();
        
        // 显示成功提示
        alert('快捷应答已提交并跳转到个人任务详情页面！');
    }, 1500);
}

function updateTaskItemCount(taskId) {
    const task = globalData.tasks.find(t => t.id === taskId);
    if (task) {
        const taskItems = globalData.items.filter(item => item.taskId === taskId);
        task.itemCount = taskItems.length;
        task.completedCount = taskItems.filter(item => item.status === '已应答').length;
        task.satisfaction = task.completedCount > 0 ? 
            Math.round((taskItems.filter(item => item.satisfaction === 'FC').length / task.completedCount) * 100) : 0;
        task.updateTime = new Date().toLocaleString('zh-CN');
        
        // 更新任务状态
        if (task.completedCount === 0) {
            task.status = '未开始';
        } else if (task.completedCount === task.itemCount) {
            task.status = '已完成';
        } else {
            task.status = '进行中';
        }
    }
}

function updateTaskProgress(taskId) {
    updateTaskItemCount(taskId);
}

function getSelectedItems() {
    const checkboxes = document.querySelectorAll('input[name="itemSelect"]:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// 任务详情操作函数
function manualResponse(itemId) {
    console.log('手工应答:', itemId);
    const item = globalData.items.find(i => i.id == itemId);
    if (item) {
        // 保存原始状态，用于取消时恢复
        if (!item.originalStatus) {
            item.originalStatus = item.status;
            item.originalResponseMethod = item.responseMethod;
        }
        
        // 设置当前条目为全局变量，供手工应答页面使用
        globalData.currentItem = item;
        
        // 更新手工应答页面的条目信息
        updateManualResponsePage(item);
        
        // 跳转到手工应答页面
        showPage('manual-response');
        
        // 更新状态为应答中
        item.status = '应答中';
        item.responseMethod = '手工应答';
        item.updateTime = new Date().toLocaleString('zh-CN');
        
        if (globalData.currentTask) {
            renderItemList(globalData.currentTask.id);
            updateTaskProgress(globalData.currentTask.id);
            renderTaskList();
        }
    }
}

// 更新手工应答页面的条目信息
function updateManualResponsePage(item) {
    // 更新面包屑导航 - 修复面包屑问题
    const breadcrumb = document.querySelector('#manual-response .breadcrumb');
    if (breadcrumb && globalData.currentTask) {
        breadcrumb.innerHTML = `
            <a href="#" onclick="showPage('task-management')">任务管理</a>
            <span>></span>
            <a href="#" onclick="openTaskDetail(${globalData.currentTask.id})">
                ${globalData.currentTask.name}
            </a>
            <span>></span>
            <span>人工应答</span>
        `;
    }
    
    // 获取当前条目编号下的所有产品 - 修复产品切换问题
    const sameCodeItems = globalData.items.filter(i => 
        i.taskId === item.taskId && i.code === item.code
    );
    const availableProducts = [...new Set(sameCodeItems.map(i => i.product))];
    
    // 更新条目信息头部
    const taskInfoHeader = document.querySelector('#manual-response .task-info-header');
    if (taskInfoHeader) {
        taskInfoHeader.innerHTML = `
            <!-- 条目基本信息 -->
            <div class="task-info-grid" style="margin-bottom: 16px;">
                <div class="task-info-item" style="grid-column: span 3;">
                    <div class="task-info-label" style="font-size: 14px;">条目描述</div>
                    <div class="task-info-value" style="font-size: 18px; font-weight: 600; color: #262626;">${item.description}</div>
                </div>
            </div>

            <!-- 任务相关信息 -->
            <div class="task-info-grid" style="margin-bottom: 16px;">
                <div class="task-info-item">
                    <div class="task-info-label" style="font-size: 14px;">国家/MTO</div>
                    <div class="task-info-value" style="font-size: 16px; font-weight: 500;">${globalData.currentTask?.country || '中国'}</div>
                </div>
                <div class="task-info-item">
                    <div class="task-info-label" style="font-size: 14px;">省公司/分支</div>
                    <div class="task-info-value" style="font-size: 16px; font-weight: 500;">${globalData.currentTask?.company || '华为技术有限公司'}</div>
                </div>
                <div class="task-info-item">
                    <div class="task-info-label" style="font-size: 14px;">客户</div>
                    <div class="task-info-value" style="font-size: 16px; font-weight: 500;">${globalData.currentTask?.customer || '某银行'}</div>
                </div>
            </div>
            
            <!-- 产品切换 -->
            <div style="display: flex; gap: 24px; align-items: center; margin-top: 16px;">
                <div>
                    <label class="form-label" style="margin: 0 8px 0 0; font-weight: 500;">切换产品查看：</label>
                    <select class="form-control" style="width: 200px;" onchange="switchProduct()">
                        ${availableProducts.map(product => 
                            `<option value="${product}" ${item.product === product ? 'selected' : ''}>${product}</option>`
                        ).join('')}
                    </select>
                </div>
            </div>
        `;
    }
    
    // 更新应答表单内容
    const responseContent = document.getElementById('responseContent');
    if (responseContent) {
        responseContent.innerHTML = item.responseContent || '请输入应答说明...';
    }
    
    // 更新满足度选择
    const satisfactionSelect = document.querySelector('#response-result select');
    if (satisfactionSelect) {
        satisfactionSelect.value = item.satisfaction || '';
    }
}

function aiResponse(itemId) {
    console.log('AI应答:', itemId);
    const item = globalData.items.find(i => i.id == itemId);
    if (item) {
        // 模拟AI应答处理
        item.status = '应答中';
        item.responseMethod = 'AI应答';
        item.updateTime = new Date().toLocaleString('zh-CN');
        
        // 模拟异步处理
        setTimeout(() => {
            item.status = '已应答';
            item.satisfaction = 'FC';
            item.responseContent = 'AI自动生成的应答内容...';
            item.updateTime = new Date().toLocaleString('zh-CN');
            
            if (globalData.currentTask) {
                renderItemList(globalData.currentTask.id);
                updateTaskProgress(globalData.currentTask.id);
                renderTaskList();
            }
            
            alert('AI应答完成！');
        }, 2000);
        
        if (globalData.currentTask) {
            renderItemList(globalData.currentTask.id);
            updateTaskProgress(globalData.currentTask.id);
            renderTaskList();
        }
        
        alert('AI应答处理中...');
    }
}

function deleteItem(itemId) {
    if (confirm('确定要删除此条目吗？')) {
        console.log('删除条目:', itemId);
        const index = globalData.items.findIndex(i => i.id == itemId);
        if (index > -1) {
            globalData.items.splice(index, 1);
            
            if (globalData.currentTask) {
                renderItemList(globalData.currentTask.id);
                updateTaskProgress(globalData.currentTask.id);
                renderTaskList();
            }
            
            alert('条目删除成功！');
        }
    }
}

function loadProductOptions() {
    // 产品选项加载函数，这里已经在HTML中静态定义了选项
    console.log('加载产品选项');
}

// 模态框相关函数
function showCreateTaskModal() {
    document.getElementById('createTaskModal').classList.add('show');
}

function hideCreateTaskModal() {
    document.getElementById('createTaskModal').classList.remove('show');
}

function createTask() {
    console.log('创建任务');
    // 这里应该有创建任务的逻辑
    hideCreateTaskModal();
    alert('任务创建功能开发中...');
}

function showAddItemModal() {
    document.getElementById('addItemModal').classList.add('show');
}

function hideAddItemModal() {
    document.getElementById('addItemModal').classList.remove('show');
    
    // 清空表单内容
    const form = document.getElementById('addItemForm');
    if (form) {
        form.reset();
        
        // 清空标签列表
        const tagList = form.querySelector('.tag-list');
        if (tagList) {
            tagList.innerHTML = '';
        }
        
        // 清空富文本编辑器
        const editorContent = form.querySelector('.editor-content');
        if (editorContent) {
            editorContent.innerHTML = '';
        }
    }
}

function addItem() {
    console.log('添加条目');
    
    const form = document.getElementById('addItemForm');
    const code = form.querySelector('input[placeholder="请输入条目编号"]').value.trim();
    const description = form.querySelector('textarea[placeholder="请输入条目描述内容"]').value.trim();
    const productSelect = document.getElementById('addItemProduct');
    const assigneeInput = form.querySelector('input[placeholder="请选择指派人"]');
    const remarkInput = form.querySelector('textarea[placeholder="条目其他事项说明"]');
    const autoResponseCheck = document.getElementById('autoResponse');
    const overwriteCheck = document.getElementById('overwriteWhenDuplicate');
    
    // 验证必填字段
    if (!code) {
        alert('请输入条目编号');
        return;
    }
    
    if (!description) {
        alert('请输入条目描述');
        return;
    }
    
    if (!productSelect || !productSelect.value) {
        alert('请选择产品');
        return;
    }
    
    if (!globalData.currentTask) {
        alert('请先选择一个任务');
        return;
    }
    
    // 修改防重逻辑：只有条目编号+产品重复时才考虑是覆盖还是跳过
    const existingItem = globalData.items.find(item => 
        item.taskId === globalData.currentTask.id && 
        item.code === code && 
        item.product === productSelect.value
    );
    
    if (existingItem) {
        if (!overwriteCheck.checked) {
            if (confirm(`条目编号"${code}"在产品"${productSelect.value}"下已存在，是否覆盖？\n\n点击"确定"覆盖现有条目\n点击"取消"跳过此条目`)) {
                // 用户选择覆盖，继续执行覆盖逻辑
            } else {
                // 用户选择跳过
                alert('已跳过重复条目');
                return;
            }
        }
        
        // 收集标签
        const tags = [];
        const tagElements = form.querySelectorAll('.tag-list .tag');
        tagElements.forEach(tagEl => {
            const tagText = tagEl.textContent.replace(' ×', '').trim();
            if (tagText) tags.push(tagText);
        });
        
        // 覆盖现有条目
        existingItem.description = description;
        existingItem.product = productSelect.value;
        existingItem.tags = tags;
        existingItem.assignee = assigneeInput ? assigneeInput.value : null;
        existingItem.remark = remarkInput ? remarkInput.value : '';
        existingItem.updateUser = '张三（123456）';
        existingItem.updateTime = new Date().toLocaleString('zh-CN');
        
        if (autoResponseCheck.checked) {
            existingItem.status = '应答中';
            existingItem.responseMethod = 'AI应答';
        }
        
        console.log('覆盖现有条目:', existingItem);
    } else {
        // 收集标签
        const tags = [];
        const tagElements = form.querySelectorAll('.tag-list .tag');
        tagElements.forEach(tagEl => {
            const tagText = tagEl.textContent.replace(' ×', '').trim();
            if (tagText) tags.push(tagText);
        });
        
        // 创建新条目
        const newItemId = Math.max(...globalData.items.map(i => i.id)) + 1;
        const newItem = {
            id: newItemId,
            taskId: globalData.currentTask.id,
            code: code,
            description: description,
            product: productSelect.value,
            tags: tags,
            status: '未应答',
            satisfaction: null,
            assignee: assigneeInput ? assigneeInput.value : null,
            responseMethod: null,
            responseContent: null,
            responseDescription: null,
            source: 'GBBS',
            index: null,
            remark: remarkInput ? remarkInput.value : '',
            updateUser: '张三（123456）',
            updateTime: new Date().toLocaleString('zh-CN')
        };
        
        if (autoResponseCheck.checked) {
            newItem.status = '应答中';
            newItem.responseMethod = 'AI应答';
        }
        
        globalData.items.push(newItem);
        console.log('新增条目:', newItem);
    }
    
    // 更新任务统计
    updateTaskItemCount(globalData.currentTask.id);
    renderTaskList();
    
    // 刷新条目列表
    renderItemList(globalData.currentTask.id);
    
    // 关闭弹窗
    hideAddItemModal();
    
    alert('条目添加成功！');
}

function showBatchImportModal() {
    document.getElementById('batchImportModal').classList.add('show');
}

function hideBatchImportModal() {
    document.getElementById('batchImportModal').classList.remove('show');
}

function batchImport() {
    console.log('批量导入');
    hideBatchImportModal();
    alert('批量导入功能开发中...');
}

// 下载导入模板
function downloadTemplate() {
    // 创建模板数据
    const templateData = [
        ['编码', '条目描述', '产品', '指派给', '标签', '自动应答', '备注', '重复时覆盖'],
        ['CODE001', '云平台基础架构能力要求示例', '华为云Stack', '张三（123456）', '基础架构;云平台', '是', '示例数据', '是'],
        ['CODE002', '虚拟化资源管理能力示例', 'FusionSphere', '李四（789012）', '虚拟化;资源管理', '否', '示例数据', '是'],
        ['CODE003', '网络安全防护机制示例', '云安全服务', '王五（345678）', '安全;防护', '是', '示例数据', '否']
    ];
    
    // 创建CSV内容
    let csvContent = templateData.map(row => 
        row.map(cell => `"${cell}"`).join(',')
    ).join('\n');
    
    // 添加BOM以支持中文
    csvContent = '\uFEFF' + csvContent;
    
    // 创建下载链接
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', 'SOC条目导入模板.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        alert('模板文件下载成功！请按照模板格式填写数据后上传。\n\n说明：\n- 标签字段多个标签用分号(;)分隔\n- 自动应答可选值：是/否\n- 重复时覆盖可选值：是/否\n- 指派给格式：姓名（工号）');
    } else {
        alert('当前浏览器不支持文件下载');
    }
}

// 根据条目状态获取操作按钮
function getItemActionButtons(item) {
    let buttons = [];
    
    // 应答中的条目：只显示删除按钮，不显示手工应答和AI应答按钮
    if (item.status === '应答中') {
        buttons.push(`<button class="btn btn-sm" onclick="startEditItem(${item.id})">编辑</button>`);
        buttons.push(`<button class="btn btn-sm btn-danger" onclick="deleteItem(${item.id})">删除</button>`);
    } else {
        // 未应答和已应答的条目：显示手工应答、AI应答、编辑、删除按钮
        buttons.push(`<button class="btn btn-sm btn-primary" onclick="manualResponse(${item.id})">手工应答</button>`);
        buttons.push(`<button class="btn btn-sm" onclick="aiResponse(${item.id})">AI应答</button>`);
        buttons.push(`<button class="btn btn-sm" onclick="startEditItem(${item.id})">编辑</button>`);
        buttons.push(`<button class="btn btn-sm btn-danger" onclick="deleteItem(${item.id})">删除</button>`);
    }
    
    return buttons.join('');
}

// 条目编辑功能
function editItem(itemId) {
    console.log('编辑条目:', itemId);
    const item = globalData.items.find(i => i.id == itemId);
    if (!item) {
        alert('条目不存在');
        return;
    }
    
    // 应答中的条目不可编辑
    if (item.status === '应答中') {
        alert('应答中的条目不可编辑');
        return;
    }
    
    showEditItemModal(item);
}

function showEditItemModal(item) {
    // 创建编辑弹窗
    const existingModal = document.getElementById('editItemModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    const modalHTML = `
        <div id="editItemModal" class="modal show">
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3 class="modal-title">编辑条目</h3>
                    <button class="modal-close" onclick="hideEditItemModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="editItemForm">
                        <div class="form-row">
                            <div class="form-item">
                                <label class="form-label">应答状态</label>
                                <select class="form-control" id="editItemStatus">
                                    <option value="未应答" ${item.status === '未应答' ? 'selected' : ''}>未应答</option>
                                    <option value="已应答" ${item.status === '已应答' ? 'selected' : ''}>已应答</option>
                                </select>
                            </div>
                            <div class="form-item">
                                <label class="form-label">应答</label>
                                <select class="form-control" id="editItemSatisfaction">
                                    <option value="">请选择</option>
                                    <option value="FC" ${item.satisfaction === 'FC' ? 'selected' : ''}>FC - 完全满足</option>
                                    <option value="PC" ${item.satisfaction === 'PC' ? 'selected' : ''}>PC - 部分满足</option>
                                    <option value="NC" ${item.satisfaction === 'NC' ? 'selected' : ''}>NC - 不满足</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-item">
                                <label class="form-label">指派给</label>
                                <input type="text" class="form-control" id="editItemAssignee" value="${item.assignee || ''}" placeholder="请输入指派人">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-item">
                                <label class="form-label">应答说明</label>
                                <textarea class="form-control" rows="4" id="editItemResponseContent" placeholder="请输入应答说明">${item.responseContent || ''}</textarea>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-item">
                                <label class="form-label">备注</label>
                                <textarea class="form-control" rows="2" id="editItemRemark" placeholder="请输入备注">${item.remark || ''}</textarea>
                            </div>
                        </div>
                        <input type="hidden" id="editItemId" value="${item.id}">
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn" onclick="hideEditItemModal()">取消</button>
                    <button class="btn btn-primary" onclick="saveEditItem()">保存</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

function hideEditItemModal() {
    const modal = document.getElementById('editItemModal');
    if (modal) {
        modal.remove();
    }
}

function saveEditItem(itemId) {
    const item = globalData.items.find(i => i.id === itemId);
    if (!item) {
        console.error('条目不存在:', itemId);
        return;
    }
    
    // 获取编辑后的数据
    const satisfactionSelect = document.getElementById(`edit-satisfaction-${itemId}`);
    const responseTextarea = document.getElementById(`edit-response-${itemId}`);
    const remarkTextarea = document.getElementById(`edit-remark-${itemId}`);
    
    if (satisfactionSelect) {
        item.satisfaction = satisfactionSelect.value;
    }
    if (responseTextarea) {
        item.responseDescription = responseTextarea.value;
    }
    if (remarkTextarea) {
        item.remark = remarkTextarea.value;
    }
    
    // 更新条目状态
    if (item.responseDescription && item.responseDescription.trim()) {
        item.status = '已应答';
        item.responseMethod = '手工应答';
        item.updateUser = '张三（123456）'; // 示例用户
        item.updateTime = new Date().toLocaleString('zh-CN');
    }
    
    // 清除编辑状态
    editingItemId = null;
    originalItemData = null;
    
    // 重新渲染列表
    renderItemList(globalData.currentTask?.id);
    
    // 如果数据分析页面正在显示，则更新数据
    updateDataAnalysisIfVisible();
    
    // 更新任务进度
    updateTaskProgress(globalData.currentTask?.id);
    
    console.log('保存条目编辑:', itemId);
}

// 辅助函数：如果数据分析页面可见，则更新数据
function updateDataAnalysisIfVisible() {
    const dataAnalysisTab = document.getElementById('data-analysis');
    if (dataAnalysisTab && dataAnalysisTab.style.display !== 'none' && globalData.currentTask) {
        updateAnalysisAssigneeOptions();
        const assigneeSelect = document.querySelector('#data-analysis select[onchange="filterAnalysisData()"]');
        const selectedAssignee = assigneeSelect ? assigneeSelect.value : '';
        renderAnalysisStats(selectedAssignee);
        renderProductStats(selectedAssignee);
    }
}

// 批量操作相关函数
function showBatchAddTagModal() {
    document.getElementById('batchAddTagModal').classList.add('show');
}

function hideBatchAddTagModal() {
    document.getElementById('batchAddTagModal').classList.remove('show');
}

function confirmBatchAddTag() {
    const tags = [];
    const tagElements = document.querySelectorAll('#batchTagList .tag');
    tagElements.forEach(tagEl => {
        const tagText = tagEl.textContent.replace(' ×', '').trim();
        if (tagText) tags.push(tagText);
    });
    
    if (tags.length === 0) {
        alert('请添加至少一个标签');
        return;
    }
    
    const selectedItemIds = getSelectedItems();
    if (selectedItemIds.length === 0) {
        alert('请选择要添加标签的条目');
        return;
    }
    
    // 批量添加标签
    selectedItemIds.forEach(itemId => {
        const item = globalData.items.find(i => i.id == itemId);
        if (item) {
            if (!item.tags) item.tags = [];
            tags.forEach(tag => {
                if (!item.tags.includes(tag)) {
                    item.tags.push(tag);
                }
            });
            item.updateTime = new Date().toLocaleString('zh-CN');
        }
    });
    
    // 重新渲染列表
    if (globalData.currentTask) {
        renderItemList(globalData.currentTask.id);
    }
    
    hideBatchAddTagModal();
    alert(`已为 ${selectedItemIds.length} 个条目添加标签`);
}

function showBatchRemoveTagModal() {
    const selectedItemIds = getSelectedItems();
    if (selectedItemIds.length === 0) {
        alert('请选择要移除标签的条目');
        return;
    }
    
    // 收集所有选中条目的标签
    const allTags = new Set();
    selectedItemIds.forEach(itemId => {
        const item = globalData.items.find(i => i.id == itemId);
        if (item && item.tags) {
            item.tags.forEach(tag => allTags.add(tag));
        }
    });
    
    const tagsList = document.getElementById('availableTagsList');
    tagsList.innerHTML = '';
    
    allTags.forEach(tag => {
        const tagElement = document.createElement('div');
        tagElement.className = 'checkbox';
        tagElement.innerHTML = `
            <input type="checkbox" id="removeTag_${tag}" value="${tag}">
            <label for="removeTag_${tag}">${tag}</label>
        `;
        tagsList.appendChild(tagElement);
    });
    
    document.getElementById('batchRemoveTagModal').classList.add('show');
}

function hideBatchRemoveTagModal() {
    document.getElementById('batchRemoveTagModal').classList.remove('show');
}

function confirmBatchRemoveTag() {
    const selectedTags = [];
    const checkboxes = document.querySelectorAll('#availableTagsList input[type="checkbox"]:checked');
    checkboxes.forEach(cb => selectedTags.push(cb.value));
    
    if (selectedTags.length === 0) {
        alert('请选择要移除的标签');
        return;
    }
    
    const selectedItemIds = getSelectedItems();
    
    // 批量移除标签
    selectedItemIds.forEach(itemId => {
        const item = globalData.items.find(i => i.id == itemId);
        if (item && item.tags) {
            item.tags = item.tags.filter(tag => !selectedTags.includes(tag));
            item.updateTime = new Date().toLocaleString('zh-CN');
        }
    });
    
    // 重新渲染列表
    if (globalData.currentTask) {
        renderItemList(globalData.currentTask.id);
    }
    
    hideBatchRemoveTagModal();
    alert(`已从 ${selectedItemIds.length} 个条目中移除选中标签`);
}

function showSetProductModal() {
    const selectedItems = getSelectedItems();
    if (selectedItems.length === 0) {
        alert('请先选择要操作的条目');
        return;
    }
    
    // 更新目标条目数量显示
    document.getElementById('targetItemsCount').textContent = `将对 ${selectedItems.length} 个条目进行操作`;
    
    // 清空产品名称输入框
    document.getElementById('manualProductName').value = '';
    
    // 显示弹窗
    document.getElementById('setProductModal').classList.add('show');
}

function hideSetProductModal() {
    document.getElementById('setProductModal').classList.remove('show');
}

function toggleDataSourceOptions() {
    const dataSource = document.getElementById('dataSource').value;
    const manualInput = document.getElementById('manualProductInput');
    const gbusTreeSelect = document.getElementById('gbusTreeSelect');
    
    if (dataSource === 'manual') {
        manualInput.style.display = 'block';
        gbusTreeSelect.style.display = 'none';
    } else if (dataSource === 'gbus') {
        manualInput.style.display = 'none';
        gbusTreeSelect.style.display = 'block';
        // 初始化GBUS目录树
        initGbusProductTree();
    }
}

function initGbusProductTree() {
    const treeSelect = document.getElementById('gbusProductTree');
    // 模拟GBUS数据源的目录树结构
    treeSelect.innerHTML = `
        <option value="">请选择产品</option>
        <optgroup label="SOC标准库目录树">
            <option value="soc-standard-1">SOC标准产品A</option>
            <option value="soc-standard-2">SOC标准产品B</option>
        </optgroup>
        <optgroup label="SOC积累库目录树">
            <option value="soc-accumulate-1">SOC积累产品A</option>
            <option value="soc-accumulate-2">SOC积累产品B</option>
        </optgroup>
        <optgroup label="产品目录树">
            <option value="product-tree-1">华为云Stack</option>
            <option value="product-tree-2">FusionSphere</option>
            <option value="product-tree-3">云安全服务</option>
            <option value="product-tree-4">FusionCompute</option>
        </optgroup>
    `;
}

function confirmSetProduct() {
    const selectedItems = getSelectedItems();
    if (selectedItems.length === 0) {
        alert('请先选择要操作的条目');
        return;
    }

    const productName = document.getElementById('manualProductName').value.trim();
    if (!productName) {
        alert('请输入产品名称');
        return;
    }

    // 检查是否选择了已经勾选的产品
    const existingProducts = new Set();
    selectedItems.forEach(itemId => {
        const item = globalData.items.find(item => item.id === itemId);
        if (item && item.product) {
            existingProducts.add(item.product);
        }
    });

    if (existingProducts.has(productName)) {
        alert('不能选择已经勾选的产品，请选择其他产品名称');
        return;
    }

    const message = `确认为 ${selectedItems.length} 个条目新增产品"${productName}"并触发自动应答？`;
    showProductConfirmModal(message, 'add', productName);
}

function showProductConfirmModal(message, operationType, selectedProduct) {
    document.getElementById('productConfirmMessage').textContent = message;
    
    // 存储操作参数
    globalData.pendingProductOperation = {
        type: operationType,
        product: selectedProduct
    };
    
    document.getElementById('productConfirmModal').classList.add('show');
}

function hideProductConfirmModal() {
    document.getElementById('productConfirmModal').classList.remove('show');
}

function confirmProductOperation() {
    const selectedItems = getSelectedItems();
    
    if (selectedItems.length === 0) {
        alert('没有选中的条目');
        hideProductConfirmModal();
        return;
    }
    
    // 执行新增产品操作
    selectedItems.forEach(itemId => {
        const item = globalData.items.find(item => item.id === itemId);
        if (item) {
            // 新增产品到现有产品中
            if (item.product) {
                item.product = item.product + ', ' + globalData.pendingProductOperation.selectedProduct;
            } else {
                item.product = globalData.pendingProductOperation.selectedProduct;
            }
            
            // 触发自动应答
            item.status = '应答中';
            item.lastUpdated = new Date().toLocaleString();
            item.lastUpdatedBy = '当前用户';
            
            // 模拟AI应答完成
            setTimeout(() => {
                item.status = '已应答';
                item.satisfaction = 'FC';
                item.responseDescription = `基于${globalData.pendingProductOperation.selectedProduct}产品的自动应答结果`;
                item.responseSource = 'GBBS';
                item.index = 'GBBS-AUTO-' + Math.random().toString(36).substr(2, 6).toUpperCase();
                renderItemList();
                updateDataAnalysisIfVisible();
            }, 2000);
        }
    });
    
    // 重新渲染列表
    renderItemList();
    updateDataAnalysisIfVisible();
    
    // 显示成功消息
    alert(`已成功为 ${selectedItems.length} 个条目新增产品并触发自动应答`);
    
    // 关闭弹窗并清理
    hideProductConfirmModal();
    hideSetProductModal();
    
    // 清空选择
    document.getElementById('selectAll').checked = false;
    toggleSelectAll();
}

function cancelProductOperation() {
    hideProductConfirmModal();
    // 重新显示产品选择弹窗
    showSetProductModal();
}

function showAssignToModal() {
    const selectedItemIds = getSelectedItems();
    if (selectedItemIds.length === 0) {
        alert('请选择要指派的条目');
        return;
    }
    
    document.getElementById('assignToModal').classList.add('show');
}

function hideAssignToModal() {
    document.getElementById('assignToModal').classList.remove('show');
}

function confirmAssignTo() {
    const assignee = document.getElementById('assigneeInput').value.trim();
    if (!assignee) {
        alert('请输入指派人');
        return;
    }
    
    const selectedItemIds = getSelectedItems();
    
    // 批量指派
    selectedItemIds.forEach(itemId => {
        const item = globalData.items.find(i => i.id == itemId);
        if (item) {
            item.assignee = assignee;
            item.updateTime = new Date().toLocaleString('zh-CN');
        }
    });
    
    // 重新渲染列表
    if (globalData.currentTask) {
        renderItemList(globalData.currentTask.id);
    }
    
    hideAssignToModal();
    alert(`已将 ${selectedItemIds.length} 个条目指派给 ${assignee}`);
}

function showExportModal() {
    const selectedItemIds = getSelectedItems();
    if (selectedItemIds.length === 0) {
        alert('请选择要导出的条目');
        return;
    }
    
    document.getElementById('exportModal').classList.add('show');
}

function hideExportModal() {
    document.getElementById('exportModal').classList.remove('show');
}

function toggleExportAll() {
    const exportAll = document.getElementById('exportAll');
    const productCheckboxes = document.querySelectorAll('#exportProductList input[type="checkbox"]:not(#exportAll)');
    
    productCheckboxes.forEach(cb => {
        cb.checked = exportAll.checked;
    });
}

function confirmExport() {
    const selectedProducts = [];
    const productCheckboxes = document.querySelectorAll('#exportProductList input[type="checkbox"]:not(#exportAll):checked');
    productCheckboxes.forEach(cb => {
        selectedProducts.push(cb.labels[0].textContent);
    });
    
    if (selectedProducts.length === 0) {
        alert('请选择要导出的产品');
        return;
    }
    
    // 模拟导出
    hideExportModal();
    alert(`正在导出 ${selectedProducts.join('、')} 产品的数据...`);
}

// 列筛选功能
let columnFilterVisible = false;

// 默认显示的列
const defaultVisibleColumns = [
    'select', '序号', '编号', '条目描述', '产品', '应答状态', 
    '应答', '应答说明', '应答来源', '索引', '操作'
];

// 列筛选按钮点击
function toggleColumnFilter() {
    const dropdown = document.getElementById('columnFilterDropdown');
    const btn = document.getElementById('columnFilterBtn');
    const toggleIcon = btn.querySelector('.toggle-icon');
    
    columnFilterVisible = !columnFilterVisible;
    
    if (columnFilterVisible) {
        dropdown.classList.add('show');
        toggleIcon.textContent = '▲';
    } else {
        dropdown.classList.remove('show');
        toggleIcon.textContent = '▼';
    }
}

// 重置列筛选
function resetColumnFilter() {
    // 取消所有选中
    const checkboxes = document.querySelectorAll('#columnFilterDropdown input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    
    // 设置默认选中状态
    defaultVisibleColumns.forEach(col => {
        const checkbox = document.getElementById(`col-${col}`);
        if (checkbox) {
            checkbox.checked = true;
        }
    });
    
    updateColumnCount();
}

// 应用列筛选
function applyColumnFilter() {
    const table = document.querySelector('.table');
    if (!table) {
        console.log('❌ 找不到表格元素');
        return;
    }
    
    const checkboxes = document.querySelectorAll('#columnFilterDropdown input[type="checkbox"]');
    const visibleColumns = [];
    
    checkboxes.forEach(checkbox => {
        if (checkbox.checked) {
            const columnName = checkbox.id.replace('col-', '');
            visibleColumns.push(columnName);
        }
    });
    
    // 获取列索引映射
    const columnIndexMap = {
        'select': 0,
        '序号': 1,
        '编号': 2,
        '条目描述': 3,
        '产品': 4,
        '标签': 5,
        '应答状态': 6,
        '应答': 7,
        '指派给': 8,
        '应答方式': 9,
        '应答说明': 10,
        '应答来源': 11,
        '索引': 12,
        '备注': 13,
        '最后更新人': 14,
        '最后更新时间': 15,
        '操作': 16
    };
    
    // 隐藏/显示表头
    const headerCells = table.querySelectorAll('thead th');
    headerCells.forEach((cell, index) => {
        const columnName = Object.keys(columnIndexMap).find(key => columnIndexMap[key] === index);
        if (columnName && !visibleColumns.includes(columnName)) {
            cell.style.display = 'none';
        } else {
            cell.style.display = '';
        }
    });
    
    // 应用当前列筛选
    applyCurrentColumnFilter();
    
    // 关闭下拉菜单
    toggleColumnFilter();
    
    console.log('✅ 应用列筛选，可见列:', visibleColumns);
}

// 应用当前列筛选（不关闭下拉菜单）
function applyCurrentColumnFilter() {
    const table = document.querySelector('.table');
    if (!table) {
        console.log('❌ 找不到表格元素');
        return;
    }
    
    const checkboxes = document.querySelectorAll('#columnFilterDropdown input[type="checkbox"]');
    const visibleColumns = [];
    
    checkboxes.forEach(checkbox => {
        if (checkbox.checked) {
            const columnName = checkbox.id.replace('col-', '');
            visibleColumns.push(columnName);
        }
    });
    
    // 获取列索引映射
    const columnIndexMap = {
        'select': 0,
        '序号': 1,
        '编号': 2,
        '条目描述': 3,
        '产品': 4,
        '标签': 5,
        '应答状态': 6,
        '应答': 7,
        '指派给': 8,
        '应答方式': 9,
        '应答说明': 10,
        '应答来源': 11,
        '索引': 12,
        '备注': 13,
        '最后更新人': 14,
        '最后更新时间': 15,
        '操作': 16
    };
    
    // 隐藏/显示表头
    const headerCells = table.querySelectorAll('thead th');
    headerCells.forEach((cell, index) => {
        const columnName = Object.keys(columnIndexMap).find(key => columnIndexMap[key] === index);
        if (columnName && !visibleColumns.includes(columnName)) {
            cell.style.display = 'none';
        } else {
            cell.style.display = '';
        }
    });
    
    // 简化的表格数据处理 - 直接按列索引处理
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        
        // 为每个单元格标记列索引（如果没有的话）
        if (!row.hasAttribute('data-column-processed')) {
            let currentColumnIndex = 0;
            cells.forEach(cell => {
                // 检查是否有跨列属性
                const colspan = parseInt(cell.getAttribute('colspan') || '1');
                cell.setAttribute('data-column-index', currentColumnIndex);
                currentColumnIndex += colspan;
            });
            row.setAttribute('data-column-processed', 'true');
        }
        
        // 根据列索引隐藏/显示单元格
        cells.forEach(cell => {
            const columnIndex = parseInt(cell.getAttribute('data-column-index') || '0');
            const columnName = Object.keys(columnIndexMap).find(key => columnIndexMap[key] === columnIndex);
            
            if (columnName && !visibleColumns.includes(columnName)) {
                cell.style.display = 'none';
            } else {
                cell.style.display = '';
            }
        });
    });
    
    console.log('✅ 自动应用列筛选，可见列:', visibleColumns);
}

// 更新选中列数量显示
function updateColumnCount() {
    const checkboxes = document.querySelectorAll('#columnFilterDropdown input[type="checkbox"]');
    const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
    const totalCount = checkboxes.length;
    
    const countDisplay = document.getElementById('selectedColumnCount');
    if (countDisplay) {
        countDisplay.textContent = `${checkedCount}/${totalCount}`;
    }
}

// 点击页面其他地方关闭列筛选下拉菜单
document.addEventListener('click', function(event) {
    const columnFilter = document.querySelector('.column-filter');
    const dropdown = document.getElementById('columnFilterDropdown');
    
    if (columnFilter && !columnFilter.contains(event.target) && dropdown && dropdown.classList.contains('show')) {
        toggleColumnFilter();
    }
});

// 监听列筛选复选框变化
document.addEventListener('change', function(event) {
    if (event.target.matches('#columnFilterDropdown input[type="checkbox"]')) {
        updateColumnCount();
        // 立即应用列筛选效果
        applyCurrentColumnFilter();
    }
});

// 初始化列筛选
document.addEventListener('DOMContentLoaded', function() {
    resetColumnFilter();
    updateColumnCount();
});

// 初始化列筛选函数
function initColumnFilter() {
    resetColumnFilter();
    updateColumnCount();
}

// 数据分析功能
function filterAnalysisData() {
    // 获取当前选中的指派人筛选条件
    const assigneeSelect = document.querySelector('#data-analysis select[onchange="filterAnalysisData()"]');
    const selectedAssignee = assigneeSelect ? assigneeSelect.value : '';
    
    console.log('数据分析筛选 - 选中的指派人:', selectedAssignee);
    
    // 应用筛选条件渲染统计数据
    renderAnalysisStats(selectedAssignee);
    renderProductStats(selectedAssignee);
}

function renderAnalysisStats(assigneeFilter = '') {
    if (!globalData.currentTask) return;
    
    let taskItems = globalData.items.filter(item => item.taskId === globalData.currentTask.id);
    
    // 根据指派人筛选
    if (assigneeFilter) {
        taskItems = taskItems.filter(item => {
            return item.assignee && item.assignee.includes(assigneeFilter);
        });
    }
    
    const completedItems = taskItems.filter(item => item.status === '已应答');
    const processingItems = taskItems.filter(item => item.status === '应答中');
    const pendingItems = taskItems.filter(item => item.status === '未应答');
    const fcItems = completedItems.filter(item => item.satisfaction === 'FC');
    const pcItems = completedItems.filter(item => item.satisfaction === 'PC');
    const ncItems = completedItems.filter(item => item.satisfaction === 'NC');
    
    // 计算完成率和满足度
    const completionRate = taskItems.length > 0 ? Math.round((completedItems.length / taskItems.length) * 100) : 0;
    const satisfactionRate = completedItems.length > 0 ? Math.round((fcItems.length / completedItems.length) * 100) : 0;
    
    const statsGrid = document.getElementById('statsGrid');
    if (statsGrid) {
        statsGrid.innerHTML = `
            <div class="stat-item">
                <div class="stat-value">${taskItems.length}</div>
                <div class="stat-label">总条目数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${completedItems.length}</div>
                <div class="stat-label">已应答</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${processingItems.length}</div>
                <div class="stat-label">应答中</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${pendingItems.length}</div>
                <div class="stat-label">未应答</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${fcItems.length}</div>
                <div class="stat-label">FC</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${pcItems.length}</div>
                <div class="stat-label">PC</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${ncItems.length}</div>
                <div class="stat-label">NC</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${completionRate}%</div>
                <div class="stat-label">完成率</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${satisfactionRate}%</div>
                <div class="stat-label">满足度</div>
            </div>
        `;
    }
}

function renderProductStats(assigneeFilter = '') {
    if (!globalData.currentTask) return;
    
    let taskItems = globalData.items.filter(item => item.taskId === globalData.currentTask.id);
    
    // 根据指派人筛选
    if (assigneeFilter) {
        taskItems = taskItems.filter(item => {
            return item.assignee && item.assignee.includes(assigneeFilter);
        });
    }
    
    const products = [...new Set(taskItems.map(item => item.product))];
    
    const productStatsBody = document.getElementById('productStatsBody');
    if (productStatsBody) {
        productStatsBody.innerHTML = products.map(product => {
            const productItems = taskItems.filter(item => item.product === product);
            const completedItems = productItems.filter(item => item.status === '已应答');
            const fcItems = completedItems.filter(item => item.satisfaction === 'FC');
            const pcItems = completedItems.filter(item => item.satisfaction === 'PC');
            const ncItems = completedItems.filter(item => item.satisfaction === 'NC');
            const completionRate = productItems.length > 0 ? Math.round((completedItems.length / productItems.length) * 100) : 0;
            const satisfactionRate = completedItems.length > 0 ? Math.round((fcItems.length / completedItems.length) * 100) : 0;
            
            return `
                <tr>
                    <td>${product}</td>
                    <td>${productItems.length}</td>
                    <td>${completedItems.length}</td>
                    <td>${fcItems.length}</td>
                    <td>${pcItems.length}</td>
                    <td>${ncItems.length}</td>
                    <td>${completionRate}%</td>
                    <td>${satisfactionRate}%</td>
                </tr>
            `;
        }).join('');
    }
}

// 更新指派人筛选选项
function updateAnalysisAssigneeOptions() {
    if (!globalData.currentTask) return;
    
    const taskItems = globalData.items.filter(item => item.taskId === globalData.currentTask.id);
    const assignees = [...new Set(taskItems.map(item => item.assignee).filter(assignee => assignee))];
    
    const assigneeSelect = document.querySelector('#data-analysis select[onchange="filterAnalysisData()"]');
    if (assigneeSelect) {
        const currentValue = assigneeSelect.value;
        
        assigneeSelect.innerHTML = `
            <option value="">全部</option>
            ${assignees.map(assignee => {
                // 提取工号用于value，显示完整姓名（工号）
                const match = assignee.match(/（(\d+)）/);
                const value = match ? match[1] : assignee;
                return `<option value="${value}" ${currentValue === value ? 'selected' : ''}>${assignee}</option>`;
            }).join('')}
        `;
    }
}

// 渲染条目列表
function renderItemList(taskId = null) {
    const tbody = document.getElementById('itemTableBody');
    const paginationInfo = document.getElementById('itemPaginationInfo');
    
    if (!tbody) {
        console.error('❌ 找不到itemTableBody元素');
        return;
    }
    
    // 如果有条目正在编辑，保存编辑状态
    let editingRowData = null;
    if (editingItemId) {
        const editingRow = document.querySelector(`tr[data-item-id="${editingItemId}"]`);
        if (editingRow) {
            // 保存编辑中的数据
            const satisfactionSelect = document.getElementById(`edit-satisfaction-${editingItemId}`);
            const responseTextarea = document.getElementById(`edit-response-${editingItemId}`);
            const remarkTextarea = document.getElementById(`edit-remark-${editingItemId}`);
            
            editingRowData = {
                itemId: editingItemId,
                satisfaction: satisfactionSelect ? satisfactionSelect.value : '',
                responseDescription: responseTextarea ? responseTextarea.value : '',
                remark: remarkTextarea ? remarkTextarea.value : ''
            };
        }
    }
    
    let items = globalData.items;
    if (taskId) {
        // 确保数据类型一致进行比较
        const targetTaskId = parseInt(taskId);
        items = items.filter(item => item.taskId === targetTaskId);
    }
    
    tbody.innerHTML = '';
    
    if (items.length === 0) {
        tbody.innerHTML = '<tr><td colspan="17" style="text-align: center; padding: 40px; color: #999;">暂无条目数据</td></tr>';
        if (paginationInfo) {
            paginationInfo.textContent = '共 0 条记录，第 1/1 页';
        }
        return;
    }
    
    // 按编号分组
    const groupedItems = {};
    items.forEach(item => {
        if (!groupedItems[item.code]) {
            groupedItems[item.code] = [];
        }
        groupedItems[item.code].push(item);
    });
    
    let rowIndex = 1;
    Object.keys(groupedItems).forEach(code => {
        const codeItems = groupedItems[code];
        codeItems.forEach((item, index) => {
            const row = document.createElement('tr');
            row.className = index === 0 ? 'item-group first-row' : 'item-group';
            row.setAttribute('data-item', code);
            row.setAttribute('data-item-id', item.id);
            
            row.innerHTML = `
                <td><input type="checkbox" name="itemSelect" value="${item.id}" ${item.status === '应答中' ? 'disabled' : ''}></td>
                <td>${rowIndex}</td>
                ${index === 0 ? `<td rowspan="${codeItems.length}">${code}</td>` : ''}
                ${index === 0 ? `<td rowspan="${codeItems.length}">${item.description}</td>` : ''}
                <td>${item.product}</td>
                <td>${item.tags ? item.tags.map(tag => `<span class="tag primary">${tag}</span>`).join('') : ''}</td>
                <td><span class="status-tag ${getStatusClass(item.status)}">${item.status}</span></td>
                <td class="satisfaction-cell">${item.satisfaction ? `<span class="satisfaction-tag ${item.satisfaction.toLowerCase()}">${item.satisfaction}</span>` : '-'}</td>
                <td>${item.assignee || '-'}</td>
                <td>${item.responseMethod || '-'}</td>
                <td class="response-description-cell">${item.responseDescription ? truncateText(item.responseDescription, 30) : '-'}</td>
                <td>${item.source || '-'}</td>
                <td>${item.index ? `<a href="#" target="_blank">${item.index}</a>` : '-'}</td>
                <td class="remark-cell">${item.remark || '-'}</td>
                <td>${item.updateUser || '-'}</td>
                <td>${item.updateTime || '-'}</td>
                <td class="action-cell">
                    <div class="table-actions">
                        ${getItemActionButtons(item)}
                    </div>
                </td>
            `;
            tbody.appendChild(row);
            rowIndex++;
        });
    });
    
    if (paginationInfo) {
        paginationInfo.textContent = `共 ${items.length} 条记录，第 1/1 页`;
    }
    
    // 表格渲染完成后，自动应用当前的列筛选设置
    setTimeout(() => {
        // 清除之前的列索引标记，确保重新计算
        const rows = document.querySelectorAll('#itemTableBody tr');
        rows.forEach(row => {
            row.removeAttribute('data-column-processed');
            const cells = row.querySelectorAll('td');
            cells.forEach(cell => {
                cell.removeAttribute('data-column-index');
            });
        });
        
        applyCurrentColumnFilter();
        
        // 恢复编辑状态
        if (editingRowData) {
            const editingRow = document.querySelector(`tr[data-item-id="${editingRowData.itemId}"]`);
            if (editingRow) {
                // 重新进入编辑状态
                editingRow.classList.add('editing');
                
                // 恢复应答列
                const satisfactionCell = editingRow.querySelector('.satisfaction-cell');
                if (satisfactionCell) {
                    satisfactionCell.innerHTML = `
                        <select class="editable-select" id="edit-satisfaction-${editingRowData.itemId}">
                            <option value="">请选择</option>
                            <option value="FC" ${editingRowData.satisfaction === 'FC' ? 'selected' : ''}>FC - 完全满足</option>
                            <option value="PC" ${editingRowData.satisfaction === 'PC' ? 'selected' : ''}>PC - 部分满足</option>
                            <option value="NC" ${editingRowData.satisfaction === 'NC' ? 'selected' : ''}>NC - 不满足</option>
                        </select>
                    `;
                }
                
                // 恢复应答说明列
                const responseCell = editingRow.querySelector('.response-description-cell');
                if (responseCell) {
                    responseCell.innerHTML = `
                        <textarea class="editable-textarea" id="edit-response-${editingRowData.itemId}" placeholder="请输入应答说明">${editingRowData.responseDescription}</textarea>
                    `;
                }
                
                // 恢复备注列
                const remarkCell = editingRow.querySelector('.remark-cell');
                if (remarkCell) {
                    remarkCell.innerHTML = `
                        <textarea class="editable-textarea" id="edit-remark-${editingRowData.itemId}" placeholder="请输入备注" style="min-height: 40px;">${editingRowData.remark}</textarea>
                    `;
                }
                
                // 恢复操作列
                const actionCell = editingRow.querySelector('.action-cell');
                if (actionCell) {
                    actionCell.innerHTML = `
                        <button class="btn btn-sm btn-primary" onclick="saveEditItem(${editingRowData.itemId})">保存</button>
                        <button class="btn btn-sm" onclick="cancelEditItem()" style="margin-left: 4px;">取消</button>
                    `;
                }
            }
        }
    }, 50); // 增加延迟确保DOM完全更新
}

// 文本截断
function truncateText(text, maxLength) {
    if (text && text.length > maxLength) {
        return text.substring(0, maxLength) + '...';
    }
    return text || '';
}

// 查询功能
function queryItems() {
    console.log('查询条目');
    
    const filters = {
        code: document.getElementById('queryCode').value,
        description: document.getElementById('queryDescription').value,
        product: document.getElementById('queryProduct').value,
        status: document.getElementById('queryStatus').value,
        tag: document.getElementById('queryTag').value,
        satisfaction: document.getElementById('querySatisfaction').value,
        assignee: document.getElementById('queryAssignee').value
    };
    
    // 应用筛选逻辑
    let filteredItems = globalData.items.filter(item => {
        if (globalData.currentTask && item.taskId !== globalData.currentTask.id) return false;
        if (filters.code && !item.code.includes(filters.code)) return false;
        if (filters.description && !item.description.includes(filters.description)) return false;
        if (filters.product && item.product !== filters.product) return false;
        if (filters.status && item.status !== filters.status) return false;
        if (filters.tag && !item.tags.some(tag => tag.includes(filters.tag))) return false;
        if (filters.satisfaction && item.satisfaction !== filters.satisfaction) return false;
        if (filters.assignee && (!item.assignee || !item.assignee.includes(filters.assignee))) return false;
        return true;
    });
    
    // 重新渲染筛选后的结果
    renderFilteredItems(filteredItems);
}

function renderFilteredItems(items) {
    const tbody = document.getElementById('itemTableBody');
    const paginationInfo = document.getElementById('itemPaginationInfo');
    
    tbody.innerHTML = '';
    
    if (items.length === 0) {
        tbody.innerHTML = '<tr><td colspan="17" style="text-align: center; padding: 40px; color: #999;">没有符合条件的数据</td></tr>';
        if (paginationInfo) {
            paginationInfo.textContent = '共 0 条记录，第 1/1 页';
        }
        return;
    }
    
    // 按编号分组
    const groupedItems = {};
    items.forEach(item => {
        if (!groupedItems[item.code]) {
            groupedItems[item.code] = [];
        }
        groupedItems[item.code].push(item);
    });
    
    let rowIndex = 1;
    Object.keys(groupedItems).forEach(code => {
        const codeItems = groupedItems[code];
        codeItems.forEach((item, index) => {
            const row = document.createElement('tr');
            row.className = index === 0 ? 'item-group first-row' : 'item-group';
            row.setAttribute('data-item', code);
            
            row.innerHTML = `
                <td><input type="checkbox" name="itemSelect" value="${item.id}" ${item.status === '应答中' ? 'disabled' : ''}></td>
                <td>${rowIndex}</td>
                ${index === 0 ? `<td rowspan="${codeItems.length}">${code}</td>` : ''}
                ${index === 0 ? `<td rowspan="${codeItems.length}">${item.description}</td>` : ''}
                <td>${item.product}</td>
                <td>${item.tags ? item.tags.map(tag => `<span class="tag primary">${tag}</span>`).join('') : ''}</td>
                <td><span class="status-tag ${getStatusClass(item.status)}">${item.status}</span></td>
                <td class="satisfaction-cell">${item.satisfaction ? `<span class="satisfaction-tag ${item.satisfaction.toLowerCase()}">${item.satisfaction}</span>` : '-'}</td>
                <td>${item.assignee || '-'}</td>
                <td>${item.responseMethod || '-'}</td>
                <td class="response-description-cell">${item.responseDescription ? truncateText(item.responseDescription, 30) : '-'}</td>
                <td>${item.source || '-'}</td>
                <td>${item.index ? `<a href="#" target="_blank">${item.index}</a>` : '-'}</td>
                <td class="remark-cell">${item.remark || '-'}</td>
                <td>${item.updateUser || '-'}</td>
                <td>${item.updateTime || '-'}</td>
                <td class="action-cell">
                    <div class="table-actions">
                        ${getItemActionButtons(item)}
                    </div>
                </td>
            `;
            tbody.appendChild(row);
            rowIndex++;
        });
    });
    
    if (paginationInfo) {
        paginationInfo.textContent = `共 ${items.length} 条记录，第 1/1 页`;
    }
    
    // 渲染完成后，应用当前的列筛选设置
    setTimeout(() => {
        // 清除之前的列索引标记，确保重新计算
        const rows = document.querySelectorAll('#itemTableBody tr');
        rows.forEach(row => {
            row.removeAttribute('data-column-processed');
            const cells = row.querySelectorAll('td');
            cells.forEach(cell => {
                cell.removeAttribute('data-column-index');
            });
        });
        
        applyCurrentColumnFilter();
    }, 50); // 增加延迟确保DOM完全更新
}

function resetQuery() {
    document.getElementById('queryCode').value = '';
    document.getElementById('queryDescription').value = '';
    document.getElementById('queryProduct').value = '';
    document.getElementById('queryStatus').value = '';
    document.getElementById('queryTag').value = '';
    document.getElementById('querySatisfaction').value = '';
    document.getElementById('queryAssignee').value = '';
    
    // 重新渲染原始数据
    if (globalData.currentTask) {
        renderItemList(globalData.currentTask.id);
    }
}

// 批量操作功能
function startResponse() {
    const selectedItems = getSelectedItems();
    const taskItems = globalData.currentTask ? globalData.items.filter(item => item.taskId === globalData.currentTask.id) : [];
    // 只处理未应答的条目
    const availableItems = taskItems.filter(item => item.status === '未应答');
    
    if (selectedItems.length === 0) {
        if (availableItems.length === 0) {
            alert('没有可操作的条目（排除应答中的条目）');
            return;
        }
        if (confirm(`是否启动所有未应答的 ${availableItems.length} 个条目的应答？`)) {
            console.log('启动所有未应答条目应答');
            
            // 更新状态为应答中
            availableItems.forEach(item => {
                item.status = '应答中';
                item.responseMethod = 'AI应答';
                item.updateTime = new Date().toLocaleString('zh-CN');
            });
            
            renderItemList(globalData.currentTask.id);
            updateTaskProgress(globalData.currentTask.id);
            renderTaskList();
            
            alert(`开始应答 ${availableItems.length} 个条目...`);
        }
    } else {
        console.log('启动选中条目应答:', selectedItems);
        
        // 只处理选中的且状态为未应答的条目
        let updatedCount = 0;
        selectedItems.forEach(itemId => {
            const item = globalData.items.find(i => i.id == itemId);
            if (item && item.status === '未应答') {
                item.status = '应答中';
                item.responseMethod = 'AI应答';
                item.updateTime = new Date().toLocaleString('zh-CN');
                updatedCount++;
            }
        });
        
        if (updatedCount === 0) {
            alert('选中的条目中没有可开始应答的条目（只能对未应答状态的条目开始应答）');
            return;
        }
        
        if (globalData.currentTask) {
            renderItemList(globalData.currentTask.id);
            updateTaskProgress(globalData.currentTask.id);
            renderTaskList();
        }
        
        alert(`开始应答选中的 ${updatedCount} 个条目...`);
    }
}

function batchDelete() {
    const selectedItems = getSelectedItems();
    const taskItems = globalData.currentTask ? globalData.items.filter(item => item.taskId === globalData.currentTask.id) : [];
    
    if (selectedItems.length === 0) {
        if (taskItems.length === 0) {
            alert('没有可删除的条目');
            return;
        }
        const message = `是否要删除所有 ${taskItems.length} 个条目？`;
        if (confirm(message)) {
            console.log('批量删除所有条目');
            
            // 删除所有条目
            globalData.items = globalData.items.filter(item => item.taskId !== globalData.currentTask.id);
            updateTaskItemCount(globalData.currentTask.id);
            renderItemList(globalData.currentTask.id);
            renderTaskList();
            
            alert('所有条目删除成功！');
        }
    } else {
        const message = `是否要删除选中的 ${selectedItems.length} 个条目？`;
        if (confirm(message)) {
            console.log('批量删除选中条目:', selectedItems);
            
            // 删除选中的条目
            selectedItems.forEach(itemId => {
                const index = globalData.items.findIndex(i => i.id == itemId);
                if (index > -1) {
                    globalData.items.splice(index, 1);
                }
            });
            
            if (globalData.currentTask) {
                updateTaskItemCount(globalData.currentTask.id);
                renderItemList(globalData.currentTask.id);
                renderTaskList();
            }
            
            alert(`${selectedItems.length} 个条目删除成功！`);
        }
    }
}

function batchAddTag() {
    const selectedItems = getSelectedItems();
    if (selectedItems.length === 0 && globalData.currentTask) {
        const taskItems = globalData.items.filter(item => item.taskId === globalData.currentTask.id);
        if (taskItems.length === 0) {
            alert('没有可操作的条目');
            return;
        }
    }
    
    showBatchAddTagModal();
}

function batchRemoveTag() {
    const selectedItems = getSelectedItems();
    if (selectedItems.length === 0) {
        alert('请选择要移除标签的条目');
        return;
    }
    
    showBatchRemoveTagModal();
}

function setProduct() {
    showSetProductModal();
}

function assignTo() {
    const selectedItems = getSelectedItems();
    if (selectedItems.length === 0) {
        alert('请选择要指派的条目');
        return;
    }
    
    showAssignToModal();
}

function exportItems() {
    const selectedItems = getSelectedItems();
    if (selectedItems.length === 0) {
        alert('请选择要导出的条目');
        return;
    }
    
    showExportModal();
}

function batchManualResponse() {
    const selectedItems = getSelectedItems();
    if (selectedItems.length === 0) {
        alert('请选择要进行手工应答的条目');
        return;
    }
    
    // 批量手工应答逻辑
    console.log('批量手工应答:', selectedItems);
    alert('批量手工应答功能开发中...');
}

function batchAiResponse() {
    const selectedItems = getSelectedItems();
    if (selectedItems.length === 0) {
        alert('请选择要进行AI应答的条目');
        return;
    }
    
    // 批量AI应答逻辑
    console.log('批量AI应答:', selectedItems);
    alert('批量AI应答功能开发中...');
}

// 权限管理相关变量
let selectedPersonnel = [];
let currentPermissionType = '';
let readonlyPersonnelToRemove = []; // 待删除的只读人员
let originalReadonlyPersonnel = []; // 原始只读人员列表

// 显示权限管理弹窗
function showPermissionManagementModal() {
    const selectedItems = getSelectedItems();
    const modal = document.getElementById('permissionManagementModal');
    const selectedItemsInfo = document.getElementById('selectedItemsInfo');
    const selectedItemsCount = document.getElementById('selectedItemsCount');
    
    // 显示已选条目信息
    if (selectedItems.length > 0) {
        selectedItemsInfo.style.display = 'block';
        selectedItemsCount.textContent = selectedItems.length;
    } else {
        selectedItemsInfo.style.display = 'none';
    }
    
    // 重置表单
    resetPermissionForm();
    
    modal.classList.add('show');
    modal.style.display = 'flex';
}

// 隐藏权限管理弹窗
function hidePermissionManagementModal() {
    const modal = document.getElementById('permissionManagementModal');
    modal.classList.remove('show');
    modal.style.display = 'none';
    resetPermissionForm();
}

// 重置权限管理表单
function resetPermissionForm() {
    // 重置单选按钮
    document.getElementById('permissionReadOnly').checked = false;
    document.getElementById('permissionAssign').checked = false;
    
    // 重置人员选择
    document.getElementById('personnelSearchInput').value = '';
    document.getElementById('personnelDropdown').style.display = 'none';
    
    // 重置已选人员
    selectedPersonnel = [];
    updateSelectedPersonnelDisplay();
    
    // 重置待删除的只读人员
    readonlyPersonnelToRemove = [];
    resetReadonlyPersonnelDisplay();
    
    // 隐藏只读人员列表
    document.getElementById('readonlyPersonnelSection').style.display = 'none';
    
    currentPermissionType = '';
    updateConfirmButtonState();
}

// 权限类型变更处理
function onPermissionTypeChange() {
    const readOnlyRadio = document.getElementById('permissionReadOnly');
    const assignRadio = document.getElementById('permissionAssign');
    const readonlySection = document.getElementById('readonlyPersonnelSection');
    
    if (readOnlyRadio.checked) {
        currentPermissionType = 'readonly';
        readonlySection.style.display = 'block';
        // 初始化原始只读人员列表
        initializeOriginalReadonlyPersonnel();
    } else if (assignRadio.checked) {
        currentPermissionType = 'assign';
        readonlySection.style.display = 'none';
    }
    updateConfirmButtonState();
}

// 初始化原始只读人员列表
function initializeOriginalReadonlyPersonnel() {
    originalReadonlyPersonnel = [
        { id: '123456', name: '张三', displayName: '张三（123456）' },
        { id: '789012', name: '李四', displayName: '李四（789012）' }
    ];
}

// 搜索人员
function searchPersonnel() {
    const input = document.getElementById('personnelSearchInput');
    const dropdown = document.getElementById('personnelDropdown');
    
    if (input.value.trim()) {
        dropdown.style.display = 'block';
        // 这里可以根据输入内容过滤人员列表
        filterPersonnelDropdown(input.value);
    } else {
        dropdown.style.display = 'none';
    }
}

// 过滤人员下拉列表
function filterPersonnelDropdown(searchText) {
    const dropdown = document.getElementById('personnelDropdown');
    const items = dropdown.querySelectorAll('.personnel-item');
    
    items.forEach(item => {
        const text = item.textContent.toLowerCase();
        const search = searchText.toLowerCase();
        if (text.includes(search)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
}

// 选择人员
function selectPersonnel(name, id) {
    const person = { name, id, displayName: `${name}（${id}）` };
    
    // 检查是否已经选择
    if (!selectedPersonnel.find(p => p.id === id)) {
        selectedPersonnel.push(person);
        updateSelectedPersonnelDisplay();
        updateConfirmButtonState();
    }
    
    // 清空搜索框并隐藏下拉列表
    document.getElementById('personnelSearchInput').value = '';
    document.getElementById('personnelDropdown').style.display = 'none';
}

// 移除已选人员
function removeSelectedPersonnel(id) {
    selectedPersonnel = selectedPersonnel.filter(p => p.id !== id);
    updateSelectedPersonnelDisplay();
    updateConfirmButtonState();
}

// 更新已选人员显示
function updateSelectedPersonnelDisplay() {
    const container = document.getElementById('selectedPersonnelList');
    
    if (selectedPersonnel.length === 0) {
        container.innerHTML = '<div style="color: #999; font-size: 12px; padding: 8px;">暂无选择人员</div>';
        return;
    }
    
    container.innerHTML = selectedPersonnel.map(person => `
        <div class="personnel-tag">
            <span>${person.displayName}</span>
            <button type="button" class="remove-btn" onclick="removeSelectedPersonnel('${person.id}')">&times;</button>
        </div>
    `).join('');
}

// 标记只读人员为待删除
function markReadonlyPersonnelForRemoval(id) {
    const personnelTag = document.querySelector(`#readonlyPersonnelList .personnel-tag[data-id="${id}"]`);
    if (personnelTag) {
        if (readonlyPersonnelToRemove.includes(id)) {
            // 取消删除标记
            readonlyPersonnelToRemove = readonlyPersonnelToRemove.filter(pid => pid !== id);
            personnelTag.classList.remove('to-remove');
        } else {
            // 添加删除标记
            readonlyPersonnelToRemove.push(id);
            personnelTag.classList.add('to-remove');
        }
        updateConfirmButtonState();
    }
}

// 重置只读人员显示状态
function resetReadonlyPersonnelDisplay() {
    const personnelTags = document.querySelectorAll('#readonlyPersonnelList .personnel-tag');
    personnelTags.forEach(tag => {
        tag.classList.remove('to-remove');
    });
}

// 更新确认按钮状态
function updateConfirmButtonState() {
    const confirmBtn = document.querySelector('#permissionManagementModal .btn-primary');
    if (!confirmBtn) return;
    
    // 检查是否有变更
    const hasNewPersonnel = selectedPersonnel.length > 0;
    const hasRemovedPersonnel = readonlyPersonnelToRemove.length > 0;
    const hasChanges = hasNewPersonnel || hasRemovedPersonnel;
    
    if (!currentPermissionType || !hasChanges) {
        confirmBtn.disabled = true;
    } else {
        confirmBtn.disabled = false;
    }
}

// 确认权限管理操作
function confirmPermissionManagement() {
    if (!currentPermissionType) {
        alert('请选择权限类型');
        return;
    }
    
    const hasNewPersonnel = selectedPersonnel.length > 0;
    const hasRemovedPersonnel = readonlyPersonnelToRemove.length > 0;
    
    if (!hasNewPersonnel && !hasRemovedPersonnel) {
        alert('没有需要处理的变更');
        return;
    }
    
    const selectedItems = getSelectedItems();
    
    if (currentPermissionType === 'assign') {
        // 指派应答逻辑
        if (selectedItems.length === 0 && hasNewPersonnel) {
            // 未勾选条目，提示确认是否对所有条目进行操作
            const personnelNames = selectedPersonnel.map(p => p.name).join('、');
            if (confirm(`请确认是否将所有条目指派给 ${personnelNames}？`)) {
                performAssignOperation(selectedPersonnel, 'all');
            } else {
                return;
            }
        } else if (hasNewPersonnel) {
            // 已勾选条目，直接执行操作
            performAssignOperation(selectedPersonnel, selectedItems);
        }
    } else if (currentPermissionType === 'readonly') {
        // 只读权限逻辑 - 直接执行操作
        performReadonlyOperation(selectedPersonnel, readonlyPersonnelToRemove);
    }
}

// 执行指派操作
function performAssignOperation(personnel, items) {
    const personnelNames = personnel.map(p => p.name).join('、');
    const itemCount = items === 'all' ? '所有' : items.length;
    
    console.log('执行指派操作:', { personnel, items });
    
    // 这里添加实际的指派逻辑
    alert(`已将 ${itemCount} 个条目指派给：${personnelNames}`);
    
    // 关闭弹窗并刷新列表
    hidePermissionManagementModal();
    refreshItemList();
}

// 执行只读权限操作
function performReadonlyOperation(newPersonnel, removedPersonnelIds) {
    let message = '';
    
    if (newPersonnel.length > 0) {
        const personnelNames = newPersonnel.map(p => p.name).join('、');
        message += `已为 ${personnelNames} 设置只读权限`;
    }
    
    if (removedPersonnelIds.length > 0) {
        const removedNames = removedPersonnelIds.map(id => {
            const person = originalReadonlyPersonnel.find(p => p.id === id);
            return person ? person.name : id;
        }).join('、');
        if (message) message += '；';
        message += `已移除 ${removedNames} 的只读权限`;
    }
    
    console.log('执行只读权限操作:', { newPersonnel, removedPersonnelIds });
    
    // 这里添加实际的只读权限设置逻辑
    alert(message);
    
    // 关闭弹窗并刷新列表
    hidePermissionManagementModal();
    refreshItemList();
}

// 点击外部关闭下拉列表
document.addEventListener('click', function(event) {
    const dropdown = document.getElementById('personnelDropdown');
    const input = document.getElementById('personnelSearchInput');
    
    if (dropdown && input && !input.contains(event.target) && !dropdown.contains(event.target)) {
        dropdown.style.display = 'none';
    }
});

// 保留原有的assignTo函数以防其他地方调用
function assignTo() {
    showPermissionManagementModal();
}

function hideAssignToModal() {
    hidePermissionManagementModal();
}

function confirmAssignTo() {
    // 兼容原有调用，设置为指派应答模式
    document.getElementById('permissionAssign').checked = true;
    currentPermissionType = 'assign';
    onPermissionTypeChange();
}

function showAssignToModal() {
    showPermissionManagementModal();
}

// 条目编辑相关变量
let editingItemId = null;
let originalItemData = null;

// 开始编辑条目
function startEditItem(itemId) {
    // 如果已有正在编辑的条目，先取消编辑
    if (editingItemId && editingItemId !== itemId) {
        cancelEditItem();
    }
    
    editingItemId = itemId;
    const item = globalData.items.find(i => i.id == itemId);
    if (!item) return;
    
    // 保存原始数据
    originalItemData = {
        satisfaction: item.satisfaction || '',
        responseDescription: item.responseDescription || '',
        remark: item.remark || ''
    };
    
    // 获取表格行
    const row = document.querySelector(`tr[data-item-id="${itemId}"]`);
    if (!row) return;
    
    // 添加编辑状态样式
    row.classList.add('editing');
    
    // 更新应答列
    const satisfactionCell = row.querySelector('.satisfaction-cell');
    if (satisfactionCell) {
        const currentValue = item.satisfaction || '';
        satisfactionCell.innerHTML = `
            <select class="editable-select" id="edit-satisfaction-${itemId}">
                <option value="">请选择</option>
                <option value="FC" ${currentValue === 'FC' ? 'selected' : ''}>FC - 完全满足</option>
                <option value="PC" ${currentValue === 'PC' ? 'selected' : ''}>PC - 部分满足</option>
                <option value="NC" ${currentValue === 'NC' ? 'selected' : ''}>NC - 不满足</option>
            </select>
        `;
    }
    
    // 更新应答说明列
    const responseCell = row.querySelector('.response-description-cell');
    if (responseCell) {
        const currentValue = item.responseDescription || '';
        responseCell.innerHTML = `
            <textarea class="editable-textarea" id="edit-response-${itemId}" placeholder="请输入应答说明">${currentValue}</textarea>
        `;
    }
    
    // 更新备注列
    const remarkCell = row.querySelector('.remark-cell');
    if (remarkCell) {
        const currentValue = item.remark || '';
        remarkCell.innerHTML = `
            <textarea class="editable-textarea" id="edit-remark-${itemId}" placeholder="请输入备注" style="min-height: 40px;">${currentValue}</textarea>
        `;
    }
    
    // 更新操作列
    const actionCell = row.querySelector('.action-cell');
    if (actionCell) {
        actionCell.innerHTML = `
            <button class="btn btn-sm btn-primary" onclick="saveEditItem(${itemId})">保存</button>
            <button class="btn btn-sm" onclick="cancelEditItem()" style="margin-left: 4px;">取消</button>
        `;
    }
}

// 保存编辑的条目
function saveEditItem(itemId) {
    const item = globalData.items.find(i => i.id == itemId);
    if (!item) return;
    
    // 获取编辑后的值
    const satisfactionSelect = document.getElementById(`edit-satisfaction-${itemId}`);
    const responseTextarea = document.getElementById(`edit-response-${itemId}`);
    const remarkTextarea = document.getElementById(`edit-remark-${itemId}`);
    
    if (satisfactionSelect) {
        item.satisfaction = satisfactionSelect.value;
    }
    if (responseTextarea) {
        item.responseDescription = responseTextarea.value.trim();
    }
    if (remarkTextarea) {
        item.remark = remarkTextarea.value.trim();
    }
    
    // 更新最后更新时间和更新人
    item.updateTime = new Date().toLocaleString('zh-CN');
    item.updateUser = '张三（123456）'; // 当前用户
    
    // 取消编辑状态
    editingItemId = null;
    originalItemData = null;
    
    // 重新渲染列表
    if (globalData.currentTask) {
        renderItemList(globalData.currentTask.id);
    }
    
    alert('保存成功');
}

// 取消编辑条目
function cancelEditItem() {
    if (!editingItemId) return;
    
    // 恢复原始数据
    if (originalItemData) {
        const item = globalData.items.find(i => i.id == editingItemId);
        if (item) {
            item.satisfaction = originalItemData.satisfaction;
            item.responseDescription = originalItemData.responseDescription;
            item.remark = originalItemData.remark;
        }
    }
    
    // 取消编辑状态
    editingItemId = null;
    originalItemData = null;
    
    // 重新渲染列表
    if (globalData.currentTask) {
        renderItemList(globalData.currentTask.id);
    }
}

function showAssignToModal() {
    showPermissionManagementModal();
}