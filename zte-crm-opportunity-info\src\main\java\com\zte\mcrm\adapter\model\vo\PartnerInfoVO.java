package com.zte.mcrm.adapter.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
public class PartnerInfoVO {

    @ApiModelProperty(value = "经销商名称（合作伙伴名称）")
    private String name;

    @ApiModelProperty(value = "经销商名称（合作伙伴名称）英文名称")
    private String nameEn;

    @ApiModelProperty(value = "受限制主体（GTS标签）")
    private String gtsFlag;

    @ApiModelProperty(value = "受限制主体中文名称（GTS标签名称）")
    private String gtsFlagName;

    @ApiModelProperty(value = "渠道商客户编码")
    private String crmCustomerCode;

}
