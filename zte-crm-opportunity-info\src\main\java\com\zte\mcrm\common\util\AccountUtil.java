package com.zte.mcrm.common.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationConfig;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.account.access.vo.Account;
import com.zte.mcrm.adapter.model.dto.CustomerDetailInfoDTO;
import com.zte.mcrm.adapter.model.vo.CustomerDetailInfoVO;
import com.zte.mcrm.clues.common.CluesSysConst;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.common.model.HttpResultData;
import com.zte.springbootframe.common.model.RetCodeCopy;
import com.zte.springbootframe.common.serviceregister.MicroServiceWithConfigUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.string.StringHelper;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: ********
 * @Date: 2019/12/4
 * @Description: 从客户接口获取客户信息工具类
 */
public class AccountUtil {

    /**
     * 从客户接口批量获取客户信息
     *
     * @param accountIds
     * @return
     * @throws BusiException
     */
    public static List<Account> getAccountListByids(Collection<String> accountIds) throws BusiException {
        if(CollectionUtils.isEmpty(accountIds)){
            return Collections.emptyList();
        }
        List<Account> accountList = new ArrayList<>();
        try {
            Map<String, String> paramMap = new HashMap<>(16);
            Map<String, String> headerParamsMap = RequestMessage.getHeader("SIEBEL_ACCOUNT");
            String retAccountIds = accountIds.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
            paramMap.put("accountCode", retAccountIds);
            HttpResultData searchHttpResult = MicroServiceWithConfigUtil.invokeServiceWithGetMethod(
                    "5", "/noPermisonAccountBatch", paramMap, headerParamsMap);
            if (searchHttpResult != null && searchHttpResult.getBo() != null) {
                String accounts = JacksonJsonConverUtil.beanToJson(searchHttpResult.getBo());
                accountList = JacksonJsonConverUtil.jsonToListBean(accounts, new TypeReference<List<Account>>() {});
            }
        } catch (RouteException e) {
            throw new BusiException(RetCodeCopy.BUSINESSERROR_CODE, "");
        }

        return accountList;
    }
    /**
     * 根据客户名称/客户编码批量查询客户信息
     *
     * @param codes
     * @return
     * @throws BusiException
     */
    public static List<CustomerDetailInfoVO> getCustomerByCode(Collection<String> codes) throws BusiException {
        if(CollectionUtils.isEmpty(codes)){
            return new ArrayList<>();
        }
        List<CustomerDetailInfoVO> list = null;
        try {
            Map<String, Object> paramMap = new HashMap<>(16);
            Map<String, String> headerParamsMap = RequestMessage.getHeader("SIEBEL_ACCOUNT");
            paramMap.put("customerCodeList", codes);
            paramMap.put("pageNo",1);
            paramMap.put("pageSize",10);
            paramMap.put("filterMerge",false);
            paramMap.put("filterFrozen",false);
            paramMap.put("onlyEffect",false);
            HttpResultData searchHttpResult = MicroServiceWithConfigUtil.invokeServiceWithPostMethod(
                    "5", "/api/customer/pageQueryDetail", paramMap, headerParamsMap);
            if (searchHttpResult != null && searchHttpResult.getBo() != null) {
                String bo = JacksonJsonConverUtil.beanToJson(searchHttpResult.getBo());
                JSONObject resultJson = JSONObject.parseObject(bo);
                String row = resultJson.getString("rows");

                ObjectMapper mapper = JacksonJsonConverUtil.getMapperInstance();
                mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                try {
                    list = mapper.readValue(row, new TypeReference<List<CustomerDetailInfoVO>>() {});
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }

            }
        } catch (RouteException e) {
            throw new BusiException(RetCodeCopy.BUSINESSERROR_CODE, "");
        }

        return list;
    }

}
