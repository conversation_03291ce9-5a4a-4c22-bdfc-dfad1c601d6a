/**
 *  Copyright  2018 赵世光 All rights reserved.
 *  @author: 6011000197 
 *  @date: 2018年6月29日 下午3:54:22 
 */
package com.zte.mcrm.dynamic.business.service;

/**  
 * <p>Title: DynamicInfoService</p>  
 * <p>Description: </p>  
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date 2018年6月29日  
 */
public interface DynamicInfoService {
	/**
	 * 发送公开动态消息，提供消息头，消息体，以及关联对象的ID，code码。动态消息所需其他属性对内封装。
	 * @param messageHeader
	 * @param message
	 * @param relationObjId
	 * @param relationObjCode  
	 * <AUTHOR> Zhao<PERSON>hi<PERSON>uang
	 * @date 2018年6月29日
	 */
	public void sendMessage(String messageHeader,String message,String relationObjId,String relationObjCode);

}
