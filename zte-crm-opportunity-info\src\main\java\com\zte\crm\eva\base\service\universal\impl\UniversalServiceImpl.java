package com.zte.crm.eva.base.service.universal.impl;

import com.zte.crm.eva.base.common.constant.universal.FrontEndUniveralConsts;
import com.zte.crm.eva.base.common.utils.PageRowsUtil;
import com.zte.crm.eva.base.domain.universal.CommonTableMap;
import com.zte.crm.eva.base.domain.universal.TableField;
import com.zte.crm.eva.base.infrastructure.access.dao.UniversalDao;
import com.zte.crm.eva.base.domain.universal.WhereBO;
import com.zte.crm.eva.base.infrastructure.adapter.IchannelBaseAdapter;
import com.zte.crm.eva.base.service.universal.UniversalService;


import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.common.util.FormDataHelpUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.*;

import static com.zte.crm.eva.base.domain.universal.WhereBO.buildWhere;

/**
 * @ClassName: UniversalServiceImpl
 * @Description:
 * <AUTHOR>
 * @date 2023-04-13
 **/
@Service
public class UniversalServiceImpl implements UniversalService {

    @Autowired
    private UniversalDao universalDao;

    @Autowired
    private IchannelBaseAdapter ichannelBaseAdapter;

    /**
     * 保存记录
     * @param map
     * @param fileName
     */
    @Override
    public boolean insertUniversal(Map<String, Object> map, String fileName) throws Exception {
        if (map.isEmpty()) {
            throw new IllegalArgumentException("param can't be empty when you insert record");
        }
        CommonTableMap tableMap = ichannelBaseAdapter.getTableMap(fileName);
        universalDao.saveRecord(tableMap, map);
        return true;
    }

    /**
     * 更新记录
     * @param map
     * @param fileName
     * @return
     */
    @Override
    public boolean updateUniversal(Map<String, Object> map,  String fileName) throws Exception {
        Object idValueObject = map.get(FrontEndUniveralConsts.COMMON_ID_NAME);
        if (idValueObject == null) {
            throw new IllegalArgumentException("id can't be null when you update record");
        }
        CommonTableMap tableMap = ichannelBaseAdapter.getTableMap(fileName);
        universalDao.updateRecord(tableMap, map);
        return true;
    }

    /**
     * 查询记录
     * @param form
     * @param fileName
     * @return
     */
    @Override
    public PageRows<Map<String, Object>> selectCommonByPage(FormData<Map<String, Object>> form,  String fileName) throws Exception {
        CommonTableMap commonTableMap = ichannelBaseAdapter.getTableMap(fileName);
        Map<String, Object> queryParam = buildQueryParam(form, commonTableMap);
        List<TableField> tableFields = commonTableMap.getTableFields();
        String tableRealName = commonTableMap.getTableRealName();
        WhereBO whereDO = buildWhere(queryParam, tableFields);
        long page = (long) queryParam.getOrDefault("page", 1L);
        //查询总条数
        Long count = universalDao.countRecord(tableRealName, whereDO);
        if (count < 1) {
            return PageRowsUtil.buildEmptyPage(page);
        }
        String fields = commonTableMap.buildSelectSql();
        //查询列表
        List<Map<String, Object>> mapList = universalDao.findRecordByPage(fields, tableRealName, whereDO);
        return PageRowsUtil.buildPageRow(page, count, mapList);
    }

    /**
     * 构建查询参数
     * @param form
     * @param commonTableMap
     * @return
     */
    private Map<String, Object> buildQueryParam(FormData<Map<String, Object>> form, CommonTableMap commonTableMap) {
        long pageSize = FormDataHelpUtil.getPageSize(form);
        long page = FormDataHelpUtil.getPageNum(form);
        long startRow = (page - 1) * pageSize;

        Map<String, Object> queryParam = commonTableMap.getTransferParam(form.getBo());
        Optional<TableField> sortOptional = commonTableMap.getTableFieldAccordingToNickName(form.getSort());
        if (sortOptional.isPresent()) {
            queryParam.put("sort", sortOptional.get().getFieldRealName());
        }
        String order = form.getOrder();
        if (!FrontEndUniveralConsts.ORDER_ASC.equalsIgnoreCase(order)) {
            order = FrontEndUniveralConsts.ORDER_DESC;
        }
        queryParam.put("order", order);
        queryParam.put("rowSize", pageSize);
        queryParam.put("startRow", startRow);
        queryParam.put("page", page);
        return queryParam;
    }

    /**
     * 软删除记录
     * @param map
     * @param fileName
     * @return
     */
    @Override
    public boolean deleteUniversal(Map<String, Object> map, String fileName) throws Exception {
        Object id = map.get(FrontEndUniveralConsts.COMMON_ID_NAME);
        if (id == null) {
            throw new IllegalArgumentException("id can't be null when you delete record");
        }
        CommonTableMap tableMap = ichannelBaseAdapter.getTableMap(fileName);
        universalDao.deleteRecord(tableMap, map);
        return true;
    }





}