package com.zte.aiagent.domain.shared.event;

import java.time.LocalDateTime;

/**
 * 领域事件基类
 * 所有领域事件的基础接口
 */
public interface DomainEvent {
    
    /**
     * 获取事件ID
     */
    String getEventId();
    
    /**
     * 获取聚合根ID
     */
    String getAggregateId();
    
    /**
     * 获取事件发生时间
     */
    LocalDateTime getOccurredOn();
    
    /**
     * 获取事件类型
     */
    String getEventType();
} 