package com.zte.leadinfo.leadinfo.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.zte.leadinfo.leadinfo.entity.SOptyXDO;
import com.zte.leadinfo.leadinfo.mapper.SOptyXMapper;
import com.zte.leadinfo.leadinfo.service.SOptyXOldService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务接口实现
 *
 * <AUTHOR>
 * @description 由 Mybatisplus Code Generator 创建
 * @since 2024-06-04 10:52:16
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SOptyXOldServiceImpl extends ServiceImpl<SOptyXMapper, SOptyXDO> implements SOptyXOldService {

    @Override
    public List<SOptyXDO> listByIds(List<String> optyRowIds) {
        if (CollectionUtils.isEmpty(optyRowIds)) {
            return Lists.newArrayList();
        }

        optyRowIds = optyRowIds.stream().filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        return this.getBaseMapper().selectBatchIds(optyRowIds);
    }
}