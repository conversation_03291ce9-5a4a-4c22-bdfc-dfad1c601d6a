package com.zte.mcrm.clues.common;
/**
 * 定义线索常量
 * <AUTHOR>
 *
 */
public class CluesSysConst {
	/**关闭原因*/
	public static final String ZTE_LEAD_CLOSED_REASON = "ZTE_LEAD_CLOSED_REASON";
	/** ZTE_LEAD_REFUSED_REASON*/
	public  static final String ZTE_LEAD_REFUSED_REASON="ZTE_LEAD_REFUSED_REASON";
	/**状态*/
	public static final String ZTE_LEAD_STATUS = "ZTE_LEAD_STATUS";
	/**业务范围*/
	public static final String ZTE_OPPTY_BUS_TYPE = "ZTE_OPPTY_BUS_TYPE";
	/**销售模式*/
	public static final String ZTE_ORDER_SALES = "ZTE_ORDER_SALES";
	/**销售模式*/
	public static final String ZTE_OPTY_SALES = "ZTE_OPTY_SALES";
	/**线索客户类型*/
	public static final String ZTE_OPPTY_TYPE = "ZTE_OPPTY_TYPE";
	/** 子行业 */
	public static final String ZTE_CHILD_TRADE = "ZTE_CHILD_TRADE";
	/** 行业 */
	public static final String ZTE_PARENT_TRADE = "ZTE_PARENT_TRADE";
	/** 市场类型*/
	public static final String ZTE_MARKET_TYPE = "ZTE_MARKET_TYPE";
	/**商机范围（国内国际）*/
	public static final String ZTE_ACC_MARKET_SCOPE = "ZTE_ACC_MARKET_SCOPE";
	/**有效标识*/
	public static final String FLAG_Y = "Y";
	/**参数*/
	public static final String PARAM_ACCOUNTID = "accountId";
	/**服务等级标识ID*/
	public static final String MIROSERVICES_FIVE = "5";
	/**无权限客户url*/
	public static final String URL_NOPERMISONACCOUNT = "/noPermisonAccount";
	/**参数*/
	public static final String PARAM_LOVTYPE = "lovType";
	/**值列表url*/
	public static final String URL_LOVALL = "/lovall";
	/** 商机-根据评审对象ID(例：商机头表ID)查询评审人(approveEmpId)-PC*/
	public static final String GET_APPROVE_USERNO_BY_OBJECTID_LIST = "/approveLines/getApproveUserNoByObjectIdList";
	/**提交或保存*/
	public static final String POST_TYPE_SUBMIT = "submit";
	/**待审批状态*/
	public static final String STATUS_CODE_APPROVE = "Approving";
	/**已发起转商机*/
	public static final String STATUS_CODE_OPPTY = "Oppty";
	/**商机培育*/
	public static final String STATUS_CODE_RENEWING = "Renewing";
	/**已关闭*/
	public static final String STATUS_CODE_CLOSED = "Closed";
	/**草稿*/
	public static final String STATUS_CODE_DRAFT = "Draft";
	/**待分配*/
	public static final String STATUS_CODE_ASSIGNING = "Assigning";
	/**被退回**/
	public static final String STATUS_CODE_REFUSED = "Refused";
	
	/**线索来源:新的跟商机保持一致*/
	public static final String ZTE_OPPORTUNITY_SOURCE = "ZTE_OPPORTUNITY_SOURCE";
	
	/** 客户类型：运营商 **/
	public static final String ACCT_TYPE_OPERATOR = "Operator";
	/**数据源*/
	public static final String MOBILE = "mobile";
	/**值列表:是否*/
	public static final String ZTE_BOOLEAN_STATUS = "ZTE_BOOLEAN_STATUS";
	/**值列表:客户属性*/
	public static final String ZTE_ACCOUNT_ATTRIBUTE = "ZTE_ACCOUNT_ATTRIBUTE";
	/**值列表:潜在融资模式*/
	public static final String ZTE_POTENTIAL_FOUND_MODEL = "ZTE_POTENTIAL_FOUND_MODEL";

	/**中文xLangId*/
	public static final String ZH_CN = "zh_CN";
	/**chs*/
	public static final String CHS = "chs";
	/**en_US*/
	public static final String EN_US = "en_US";
	/**US*/
	public static final String US = "US";
	/**ENU*/
	public static final String ENU = "ENU";
	/**siebel*/
	public static final String SIEBEL = "siebel";

	/**是否是禁运国*/
	public static final String IS_FORBIDDON ="embargo";

	/**是禁运国，是受制裁主体*/
	public static final String EMBARGO_SACTIONED="true";

	/**
	 * 权限平台授权码
	 */
	public static final String CLUE_AUTH_CODE="QueryClue";

    /** 我的所有线索*/
    public static final String ALL_CLUES = "01";
}
