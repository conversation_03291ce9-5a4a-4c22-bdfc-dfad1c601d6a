package com.zte.aiagent.domain.enums;

import lombok.Getter;

/**
 * 文件格式枚举类
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Getter
public enum FileFormatEnum {
    
    /**
     * Excel格式
     */
    EXCEL("excel", ".xlsx", "Excel文件格式"),
    
    /**
     * Word格式
     */
    WORD("word", ".docx", "Word文件格式");
    
    /**
     * 格式标识
     */
    private final String code;
    
    /**
     * 文件后缀
     */
    private final String suffix;
    
    /**
     * 格式描述
     */
    private final String description;
    
    FileFormatEnum(String code, String suffix, String description) {
        this.code = code;
        this.suffix = suffix;
        this.description = description;
    }
    
    /**
     * 根据格式标识获取枚举
     * @param code 格式标识
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static FileFormatEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (FileFormatEnum format : values()) {
            if (format.getCode().equalsIgnoreCase(code)) {
                return format;
            }
        }
        return null;
    }
    
    /**
     * 根据文件后缀获取枚举
     * @param suffix 文件后缀
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static FileFormatEnum getBySuffix(String suffix) {
        if (suffix == null) {
            return null;
        }
        for (FileFormatEnum format : values()) {
            if (format.getSuffix().equalsIgnoreCase(suffix)) {
                return format;
            }
        }
        return null;
    }

} 