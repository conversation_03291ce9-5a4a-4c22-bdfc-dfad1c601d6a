package com.zte.crm.eva.base.infrastructure.client;

import com.zte.crm.eva.base.domain.pseudocode.model.PseudocodeRule;
import com.zte.crm.eva.base.infrastructure.client.model.ComMsgForwardDTO;
import com.zte.crm.eva.base.infrastructure.client.model.PrmQuickCodeValue;
import com.zte.itp.msa.core.model.IDNResponseData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.model.dto.AccountDetailQueryDTO;
import com.zte.mcrm.adapter.model.dto.UcsUserInfoDTO;
import com.zte.mcrm.adapter.model.vo.AccountDetail;
import com.zte.mcrm.adapter.model.vo.BulkCodeValuesByOneTypeVo;
import com.zte.mcrm.common.errorcode.model.ComExceptionLog;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import com.zte.itp.msa.core.model.FormData;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-04-20
 */
@FeignClient(name = "zte-crm-ichannel-base")
public interface IchannelBaseFeign {

    @GetMapping(value = "/zte-crm-ichannel-base/common/module/interface/getJsonConfiguration")
    ServiceData<String> getJsonConfiguration(@RequestParam(name = "fileName") String fileName);

    @ApiOperation("通过规则名称和模块名称获取规则 -Post方式")
    @PostMapping(value="/zte-crm-ichannel-base/pseudocode/pseudocoderule/getByNameAndModule")
    ServiceData<List<PseudocodeRule>>  getByNameAndModule(@RequestBody PseudocodeRule pseudocodeRule);

    @ApiOperation("异步推送一条消息-POST方式")
    @PostMapping(value="/zte-crm-ichannel-base/msgforwardcenter/async/push",produces ="application/json; charset=utf-8")
    ServiceData<Boolean>  asyncPush(@RequestBody ComMsgForwardDTO comMsgForwardDTO);

    @ApiOperation("根据系统参数类型获取所有系统参数值数据 -树形结构输出|已加缓存")
    @GetMapping(value = "/zte-crm-ichannel-base/prmquickcodevalue/{codetype}/codevalues")
    ServiceData<List<PrmQuickCodeValue>> getByCodeType(@PathVariable("codetype") String codeType);

    @ApiOperation("根据系统参数类型获取所有系统参数值数据")
    @PostMapping(value = "/zte-crm-ichannel-base/prmquickcodevalue/codeTypes/effectiveCodeValues")
    ServiceData<List<BulkCodeValuesByOneTypeVo>> getByCodeTypeList(@RequestBody List<String> codeTypes);

    @ApiOperation("异步插入日志记录")
    @PostMapping(value = "/zte-crm-ichannel-base/log/exception/async/add",produces ="application/json; charset=utf-8")
    ServiceData<Boolean> asyncAddErrorRecord(@RequestBody ComExceptionLog comExceptionLog);


    @ApiOperation("分页查询")
    @PostMapping(value = "/zte-crm-ichannel-base/foundation/qu/fundation/agencyUpdate",produces ="application/json; charset=utf-8")
    ServiceData<PageRows<Map<String, Object>>> getFoundationUpdate(@RequestBody FormData<Map<String, Object>> formData);

    @ApiOperation("更新记录")

    @PostMapping(value = "/zte-crm-ichannel-base/foundation/in/fundation/agencyRecord",produces ="application/json; charset=utf-8")
    ServiceData<Boolean>  recordFoundation(@RequestBody  Map<String, Object> queryParam);


    @ApiOperation("修改组织任务")
    @PostMapping(value = "/zte-crm-ichannel-base/foundation/mo/fundation/agencyUpdate",produces ="application/json; charset=utf-8")
    ServiceData<Boolean> updateFoundation(@RequestBody  Map<String, Object> queryParam);

    @ApiOperation("查询渠道商账号信息")
    @PostMapping(value = "/zte-crm-ichannel-base/uac/query/account/detail",produces ="application/json; charset=utf-8")
    IDNResponseData<AccountDetail> queryAccountDetail(@RequestBody AccountDetailQueryDTO accountDetailQueryDTO);

    @ApiOperation("查询人员信息")
    @PostMapping(value = "/zte-crm-ichannel-base/ucs/account/queryall",produces ="application/json; charset=utf-8")
    ServiceData<List<UcsUserInfoDTO>> queryAll(@RequestBody List<String> accountList);
}
