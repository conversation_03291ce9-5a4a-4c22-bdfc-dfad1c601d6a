package com.zte.mcrm.channel.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 描述：
 * 创建时间：2021/10/13
 *
 * @author：王丹凤6396000572
 */
@Data
public class CreateCustomerParamVO {

    @ApiModelProperty(value = "单据ID")
    private String rowId;
    @ApiModelProperty(value = "最终客户名称")
    private String lastAccName;
    @ApiModelProperty(value = "中兴业务经理工号")
    private String businessManagerId;
    @ApiModelProperty(value = "中兴业务经理姓名")
    private String businessManagerName;
    @ApiModelProperty(value = "商机所属部门")
    private String deptNo;
}
