package com.zte.mcrm.clues.access.dao;

/****
 *
 * <AUTHOR> @date 2021/2/10
 **/
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.zte.mcrm.clues.access.vo.BusinessClues;
import com.zte.mcrm.opportunity.access.vo.BusinessOppPositionVo;
import com.zte.springbootframe.util.page.PageQuery;
@Repository
public interface BusinessCluesDao {

	/**
	 * 查询线索详情
	 * @param businessClues
	 * @return
	 * @throws Exception
	 */
	BusinessClues selectBaseInfo(BusinessClues businessClues) ;
	/**
	 *  分页查询线索
	 * @param pageQuery
	 * @return
	 * @throws Exception
	 */
	List<BusinessClues> getCluesWithAuth(PageQuery<BusinessClues> pageQuery) throws Exception;
	
	/**
	 *  客户关联线索
	 * @param pageQuery
	 * @return
	 * @throws Exception
	 */
	List<BusinessClues> getAccountClues(PageQuery<BusinessClues> pageQuery) throws Exception;
	
	/**
	 *  计数客户关联线索条数
	 * @param pageQuery
	 * @return
	 * @throws Exception
	 */
	int accountCluesCount(PageQuery<BusinessClues> pageQuery) throws Exception;
	/**
	 *  计数线索条数
	 * @param pageQuery
	 * @return
	 * @throws Exception
	 */
	int countClues(PageQuery<BusinessClues> pageQuery) throws Exception;

	/**
	 * 获取线索版本
	 * @param id
	 * @return
	 * @throws Exception
	 */
	String selectClueVsersion(String id) throws Exception;
	
	/**
	 * 检查是否有查看线索权限，是的话返回值>1,否的话返回值=0
	 * @param empId
	 * @param clueId
	 * @return
	 */
	int checkAuthWithEmpIdAndClueId(@Param("empId") String empId, @Param("clueId") String clueId);
	
	/**
	 * 线索状态是否为待客户经理更新
	 * @param clue
	 * @return
	 */
	int isLeadUpdate(BusinessClues clue);

	/**
	 * 高级查询
	 * @param pageQuery
	 * @return
	 */
	List<BusinessClues> advanceQuery(PageQuery pageQuery);

	/**
	 * 高级查询计数
	 * @param pageQuery
	 * @return
	 */
	int advanceQueryCount(PageQuery pageQuery);
}
