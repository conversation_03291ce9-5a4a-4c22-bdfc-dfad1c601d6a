package com.zte.mcrm.channel.model.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 * 商机邮件实体类
 */
@ToString
@Setter
@Getter
public class OpportunityMailEntity {
    /** 主键id */
    private String rowId;
    /** 渠道商名称 */
    private String customerName;
    /** 商机编号 */
    private String optyCd;
    /** 商机名称 */
    private String optyName;
    /** 投资方所在地 */
    private String deptName;
    /** 投资方所在地部门编码 */
    private String deptNo;
    /** 最终用户名称 */
    private String lastAccName;
    /** 最终用户行业 */
    private String finalCustomerTradeName;
    /** 最终用户行业 */
    private String finalCustomerParentTrade;
    /** 最终用户行业 */
    private String finalCustomerChildTrade;
    /** 产品 */
    private String products;
    /** 预计签单金额（元）*/
    private String totalAmount;
    /** 提交时间：*/
    private String submitDate;
    /** 商机来源 */
    private String dataSource;
    /** 商机状态 */
    private String statusCd;
    /** 商机状态名称 */
    private String statusCdName;
    /** 链接地址-中文 */
    private String linkUrlCn;
    /** 链接地址-英文 */
    private String linkUrlEn;
    /** 模板编码 */
    private String modelCode;
    /** 模板类型：reminder催办邮件;notification知会邮件;tobe待办邮件 */
    private String modelType;
    /** 收件人 */
    private String mailTo;
    /** 邮件抄送人列表 */
    private String mailCc;
    /**
     * 链接请求域名
     */
    private String ssHttpHead;
    /**
    * 审批意见
    */
    private String opinion;
    /**
     * 接收人编号
     */
    private String receiverIds;
}
