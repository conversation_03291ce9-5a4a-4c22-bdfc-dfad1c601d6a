package com.zte.mcrm.channel.service.channel;

import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.channel.model.dto.CrmCustomerDertimineParamDTO;
import com.zte.mcrm.channel.model.dto.OpportunityStatusDTO;
import com.zte.mcrm.channel.model.entity.ExternalOpportunityDetail;
import com.zte.mcrm.channel.model.vo.CrmCustomerDetermineResultVO;
import com.zte.mcrm.channel.model.vo.OpportunityDataVO;
import com.zte.mcrm.channel.model.vo.OpportunityQueryParamVO;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * 描述：
 * 创建时间：2021/9/22
 *
 * @author：王丹凤6396000572
 */
public interface IOpportunityQueryService {

    /**
     * 分页查询商机列表
     * @param paramVO
     * @return
     * @throws Exception
     */
    public PageRows<OpportunityDataVO> getOpportunityList(OpportunityQueryParamVO paramVO) throws Exception;


    /**
     * 商机详情查询（对外）
     *
     * @param optyCd 商机编号
     * @return ExternalOpportunityDetail
     * <AUTHOR>
     * @date 2021/9/24
     */
    ExternalOpportunityDetail getExternalOpportunityDetail(String optyCd) throws Exception;

    /**
     * 根据渠道商编码及年份获取是否存在有效商机，存在则返回1
     * @param crmCustomerDertimineParamDTO
     * @return
     */
    List<CrmCustomerDetermineResultVO> determineIntransitsAndValidOptys(CrmCustomerDertimineParamDTO crmCustomerDertimineParamDTO);

    /**
     * [项目与交易统一查询接口]根据渠道商编码及年份获取是否存在有效商机，存在则返回1
     * @param crmCustomerDertimineParamDTO
     * @return
     */
    List<CrmCustomerDetermineResultVO> determineIntransitsAndValidDocuments(CrmCustomerDertimineParamDTO crmCustomerDertimineParamDTO) throws ExecutionException, InterruptedException;

    /**
     * 根据商机编码更新商机状态信息
     * @param opportunityStatusDTO
     * @return
     */
    Boolean updateOpportunityStatus(OpportunityStatusDTO opportunityStatusDTO);
}
