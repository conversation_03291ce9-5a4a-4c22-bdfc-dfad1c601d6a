<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zte.mcrm.channel.dao.OpportunityMonthReportDao" >
    <!-- 屏蔽mybatis的2级缓存  <cache />  -->

    <!-- 记录和实体映射配置 -->
    <resultMap id="BaseMap" type="com.zte.mcrm.channel.model.entity.OpportunityMonthReport" >
        <id column="id" property="id" jdbcType="VARCHAR" />
		<result column="opty_id" property="optyId" jdbcType="VARCHAR" />
		<result column="report_month" property="reportMonth" jdbcType="VARCHAR" />
		<result column="report_status" property="reportStatus" jdbcType="VARCHAR" />
		<result column="opty_current_status" property="optyCurrentStatus" jdbcType="VARCHAR" />
		<result column="reason_code" property="reasonCode" jdbcType="VARCHAR" />
		<result column="report_info" property="reportInfo" jdbcType="VARCHAR" />
		<result column="created_by" property="createdBy" jdbcType="VARCHAR" />
		<result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
		<result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR" />
		<result column="last_updated_date" property="lastUpdatedDate" jdbcType="TIMESTAMP" />
		<result column="enabled_flag" property="enabledFlag" jdbcType="CHAR" />
		<result column="memo" property="memo" jdbcType="VARCHAR" />
        <result column="business_type" property="businessType" jdbcType="VARCHAR" />
        <result column="month_report_detail" property="monthReportDetailJson" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="MonthReportReminderMap" type="com.zte.mcrm.channel.model.entity.OpportunityKeyInfoEntity" >
        <id column="id" property="rowId" jdbcType="VARCHAR" />
        <result column="opty_cd" property="optyCd" jdbcType="VARCHAR" />
        <result column="status_cd" property="statusCd" jdbcType="VARCHAR"/>
        <result column="current_status" property="currentStatus" jdbcType="VARCHAR"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
        <result column="created" property="created" jdbcType="TIMESTAMP" />
        <result column="business_manager_id" property="businessManagerId" jdbcType="VARCHAR" />
        <result column="business_manager_name" property="businessManagerName" jdbcType="VARCHAR" />
        <result column="success_date" property="successDate" jdbcType="TIMESTAMP" />
        <result column="last_upd_by" property="lastUpdBy" jdbcType="VARCHAR" />
        <result column="last_upd" property="lastUpd" jdbcType="TIMESTAMP" />
        <result column="X_LAST_ACC_ID" property="lastAccId" jdbcType="VARCHAR"/>
        <result column="x_last_acc_name" property="lastAccName" jdbcType="VARCHAR"/>
        <result column="ATTRIB_46" property="attrib46" jdbcType="VARCHAR"/>
        <result column="customer_name" property="customerName" jdbcType="VARCHAR"/>
        <result column="crm_customer_code" property="crmCustomerCode" jdbcType="VARCHAR"/>
        <result column="work_flow_instance_id" property="flowInstanceId" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="base_column">
        t.id ,
        t.opty_id ,
        t.report_month ,
        t.report_status ,
        t.opty_current_status ,
        t.reason_code ,
        t.report_info ,
        t.created_by ,
        t.created_date ,
        t.last_updated_by ,
        t.last_updated_date ,
        t.enabled_flag ,
        t.memo ,
        t.business_type ,
        t.month_report_detail
    </sql>

    <sql id="base_where">
        <if test="id != null and id != ''"> and t.id = #{id}</if>
        <if test="optyId != null and optyId != ''"> and t.opty_id = #{optyId}</if>
        <if test="reportMonth != null and reportMonth != ''"> and t.report_month = #{reportMonth}</if>
        <if test="reportStatus != null and reportStatus != ''"> and t.report_status = #{reportStatus}</if>
        <if test="optyCurrentStatus != null and optyCurrentStatus != ''"> and t.opty_current_status = #{optyCurrentStatus}</if>
        <if test="reasonCode != null and reasonCode != ''"> and t.reason_code = #{reasonCode}</if>
        <if test="reportInfo != null and reportInfo != ''"> and t.report_info = #{reportInfo}</if>
        <if test="createdBy != null and createdBy != ''"> and t.created_by = #{createdBy}</if>
        <if test="createdDate != null"> and t.created_date = #{createdDate}</if>
        <if test="lastUpdatedBy != null and lastUpdatedBy != ''"> and t.last_updated_by = #{lastUpdatedBy}</if>
        <if test="lastUpdatedDate != null"> and t.last_updated_date = #{lastUpdatedDate}</if>
        <if test="enabledFlag != null and enabledFlag != ''"> and t.enabled_flag = #{enabledFlag}</if>
        <if test="memo != null and memo != ''"> and t.memo = #{memo}</if>
        <if test="businessType != null and businessType != ''"> and t.business_type = #{businessType}</if>
    and t.tenant_id = #{headerTenantId}
    </sql>

    <!-- 获取符合条件的记录列表 -->
    <select id="getList" parameterType="java.util.Map" resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM s_month_report t
        WHERE 1=1
        <include refid="base_where"/>
    </select>

    <!-- 获取符合条件的记录列表 -->
    <select id="getMonthReportList" resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM s_month_report t
        WHERE t.opty_id = #{optyId, jdbcType=VARCHAR}
        and t.business_type = 'monthReport'
        and t.enabled_flag = 'Y'
        order by report_month desc
    </select>

    <!-- 软删除一条记录 -->
    <update id="disAbleCurrentMonthReport" >
        UPDATE s_month_report
        SET enabled_flag = 'N',
        last_updated_by = #{empNo, jdbcType=VARCHAR},
        last_updated_date = SYSDATE()
        WHERE
        opty_id = #{opportunityMonthReport.optyId, jdbcType=VARCHAR}
        and report_month = #{opportunityMonthReport.reportMonth, jdbcType=VARCHAR}
        and business_type = #{opportunityMonthReport.businessType, jdbcType=VARCHAR}
        and enabled_flag = 'Y'
    </update>

    <select id="getMonthReport" resultMap="BaseMap">
        select <include refid="base_column"/>
        from s_month_report t
        WHERE
        t.opty_id = #{optyId, jdbcType=VARCHAR}
        and t.report_month = #{reportMonth, jdbcType=VARCHAR}
        and t.business_type = #{businessType, jdbcType=VARCHAR}
        and t.enabled_flag = 'Y'
    </select>

    <!--添加一条记录 -->
    <insert id="insert" parameterType="com.zte.mcrm.channel.model.entity.OpportunityMonthReport" >
        INSERT INTO s_month_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
		    <if test="id != null">id ,</if>
		    <if test="optyId != null">opty_id ,</if>
		    <if test="reportMonth != null">report_month ,</if>
		    <if test="reportStatus != null">report_status ,</if>
		    <if test="optyCurrentStatus != null">opty_current_status ,</if>
		    <if test="reasonCode != null">reason_code ,</if>
		    <if test="reportInfo != null">report_info ,</if>
		    <if test="createdBy != null">created_by ,</if>
		    created_date ,
		    <if test="lastUpdatedBy != null">last_updated_by ,</if>
		    last_updated_date ,
		    enabled_flag ,
		    <if test="memo != null">memo ,</if>
            <if test="businessType != null">business_type ,</if>
            <if test="monthReportDetailJson != null">month_report_detail ,</if>
    		tenant_id
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
    	    <if test="id != null">#{id, jdbcType=VARCHAR} ,</if>
    	    <if test="optyId != null">#{optyId, jdbcType=VARCHAR} ,</if>
    	    <if test="reportMonth != null">#{reportMonth, jdbcType=VARCHAR} ,</if>
    	    <if test="reportStatus != null">#{reportStatus, jdbcType=VARCHAR} ,</if>
    	    <if test="optyCurrentStatus != null">#{optyCurrentStatus, jdbcType=VARCHAR} ,</if>
    	    <if test="reasonCode != null">#{reasonCode, jdbcType=VARCHAR} ,</if>
    	    <if test="reportInfo != null">#{reportInfo, jdbcType=VARCHAR} ,</if>
    	    <if test="createdBy != null">#{createdBy, jdbcType=VARCHAR} ,</if>
            SYSDATE() ,
    	    <if test="lastUpdatedBy != null">#{lastUpdatedBy, jdbcType=VARCHAR} ,</if>
            SYSDATE() ,
    	    'Y' ,
    	    <if test="memo != null">#{memo, jdbcType=VARCHAR} ,</if>
            <if test="businessType != null">#{businessType, jdbcType=VARCHAR} ,</if>
            <if test="monthReportDetailJson != null">#{monthReportDetailJson, jdbcType=VARCHAR} ,</if>
			#{headerTenantId, jdbcType=BIGINT}
        </trim>
    </insert>

    <!-- 更新一条记录 -->
    <update id="update" parameterType="com.zte.mcrm.channel.model.entity.OpportunityMonthReport" >
        UPDATE s_month_report
        <set>
		    <if test="optyId != null">opty_id=#{optyId, jdbcType=VARCHAR} ,</if>
		    <if test="reportMonth != null">report_month=#{reportMonth, jdbcType=VARCHAR} ,</if>
		    <if test="reportStatus != null">report_status=#{reportStatus, jdbcType=VARCHAR} ,</if>
		    <if test="optyCurrentStatus != null">opty_current_status=#{optyCurrentStatus, jdbcType=VARCHAR} ,</if>
		    <if test="reasonCode != null">reason_code=#{reasonCode, jdbcType=VARCHAR} ,</if>
		    <if test="reportInfo != null">report_info=#{reportInfo, jdbcType=VARCHAR} ,</if>
		    <if test="createdBy != null">created_by=#{createdBy, jdbcType=VARCHAR} ,</if>
		    <if test="createdDate != null">created_date=#{createdDate, jdbcType=TIMESTAMP} ,</if>
		    <if test="lastUpdatedBy != null">last_updated_by=#{lastUpdatedBy, jdbcType=VARCHAR} ,</if>
		    <if test="lastUpdatedDate != null">last_updated_date=#{lastUpdatedDate, jdbcType=TIMESTAMP} ,</if>
		    <if test="enabledFlag != null">enabled_flag=#{enabledFlag, jdbcType=CHAR} ,</if>
		    <if test="memo != null">memo=#{memo, jdbcType=VARCHAR} ,</if>
            <if test="businessType != null">business_type=#{businessType, jdbcType=VARCHAR} ,</if>
            <if test="monthReportDetailJson != null">month_report_detail=#{monthReportDetailJson, jdbcType=VARCHAR} ,</if>
	    </set>
        WHERE
        id=#{id, jdbcType=VARCHAR}
    </update>

    <!-- 翻页函数:获取符合条件的记录数 -->
    <select id="getCount" parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT count(*) FROM s_month_report t
        WHERE 1=1
        <include refid="base_where"/>
    </select>

    <select id="queryByOpportunityAndReportMonth"  resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM s_month_report t
        WHERE t.opty_id = #{opportunity, jdbcType=VARCHAR}
        AND t.report_month = #{reportMonth, jdbcType=VARCHAR}
    </select>

    <select id="getMonthReportReminders" resultMap="MonthReportReminderMap">
        select s.id,
        s.opty_code as opty_cd,
        s.create_by as created_by,
        s.opty_name as ATTRIB_46,
        JSON_VALUE(t.employee, '$[0].empUIID') as business_manager_id,
        JSON_UNQUOTE(JSON_VALUE(t.employee, '$[0].empName')) as business_manager_name,
        s.create_time as created,
        s.success_date,
        s.current_status
        from s_opty s
        left join s_opty_team t on s.id = t.p_id
        and t.employee_type = 1
        and JSON_VALUE(t.core_info_flag, '$[0].value') = 'Y'
        left join s_month_report m on s.id = m.opty_id and m.business_type = 'monthReport' and m.enabled_flag = 'Y'
        and m.report_month = #{reportMonth, jdbcType=VARCHAR}
        left join s_opty_x x on s.id = x.id
        where
            <![CDATA[ date_format(s.success_date, '%Y-%m-%d') < date_format(DATE_ADD(curdate(),interval -day(curdate())+1 day), '%Y-%m-%d') ]]>
            and s.is_deleted = 0
            and m.month_report_detail is null and s.data_source = 'iChannel' and s.opty_code like 'XS%'
            and s.opty_status = 'Renewing'
            and (s.current_status is null or s.current_status in ('','underway'))
    </select>

    <select id="getReNewStatusOptys" resultMap="MonthReportReminderMap">
        select
        s.id,
        s.opty_code as opty_cd,
        app.work_flow_instance_id,
        s.opty_status as status_cd,
        s.create_time as created,
        s.create_by as created_by,
        s.last_modified_time as last_upd,
        s.last_modified_by as last_upd_by,
        s.success_date
        from
        s_opty s join com_approval_record app on
        s.id = app.business_id
        where s.data_source in ('iChannel','PRM')
        and s.is_deleted = 0 and app.enabled_flag = 'Y'
        <if test="type == 'successDate'">
            and s.opty_status  not in ('Draft', 'reportedApprovaling', 'arbitration')
            and (s.success_date is null or s.success_date = '')
        </if>
        <if test="type == 'submitDate'">
            and s.opty_status != 'Draft'
            and (s.submit_date is null or s.submit_date = '')
        </if>
        <if test="limit > 0">
            limit #{limit}
        </if>
    </select>

    <select id="getOldAccDraftProcessOptys" resultMap="MonthReportReminderMap">
        select
        s.id,
        s.opty_code as opty_cd,
        r.work_flow_instance_id,
        s.create_by as created,
        x.customer as X_LAST_ACC_ID,
        x.x_last_acc_name,
        x.crm_customer_code,
        x.customer_name,
        s.create_by as created_by,
        s.last_modified_time as last_upd,
        s.last_modified_by as last_upd_by,
        JSON_VALUE(t.employee, '$[0].empUIID') as business_manager_id,
        JSON_UNQUOTE(JSON_VALUE(t.employee, '$[0].empName')) as business_manager_name,
        s.opty_name as ATTRIB_46
        from
        s_opty s
        left join s_opty_team t on s.id = t.p_id
        and t.employee_type = 1
        and JSON_VALUE(t.core_info_flag, '$[0].value') = 'Y'
        left join s_opty_x x on
        x.id = s.id left join com_approval_record r on
        r.business_id = s.id
        and r.enabled_flag = 'Y'
        where s.opty_status = 'reportedApprovaling'
            and s.data_source in ('iChannel','PRM')
            and (x.last_acc_status = 2 or x.last_acc_status = 1)
            and x.enabled_flag = 'Y' and s.is_deleted = 0 and r.work_flow_instance_id is not null
    </select>

    <select id="getMonthlyInvalidOptys"
            resultMap="MonthReportReminderMap">
        select s.id,
        s.opty_code as opty_cd,
        s.create_by as created_by,
        s.opty_name as ATTRIB_46,
        JSON_VALUE(t.employee, '$[0].empUIID') as business_manager_id,
        JSON_UNQUOTE(JSON_VALUE(t.employee, '$[0].empName')) as business_manager_name,
        s.create_time as created,
        s.success_date,
        app.work_flow_instance_id,
        s.current_status
        from s_opty s
        left join s_opty_team t on s.id = t.p_id
        and t.employee_type = 1
        and JSON_VALUE(t.core_info_flag, '$[0].value') = 'Y'
        left join s_month_report m on s.id = m.opty_id and m.business_type = 'monthReport' and m.enabled_flag = 'Y'
        and m.report_month = #{reportMonth, jdbcType=VARCHAR}
        left join s_opty_x x on s.id = x.id
        left join com_approval_record app on s.id = app.business_id and app.enabled_flag = 'Y'
        where
        <![CDATA[ date_format(s.success_date, '%Y-%m-%d') < date_format(date_sub(DATE_ADD(curdate(),interval -day(curdate())+1 day), interval 1 month), '%Y-%m-%d') ]]>
        and s.is_deleted = 0
        and m.month_report_detail is null and s.data_source in ('iChannel') and s.opty_code like 'XS%'
        and s.opty_status = 'Renewing'
        and (s.current_status is null or s.current_status in ('','underway'))
    </select>

</mapper>
