package com.zte.mcrm.channel.controller.channel;

import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.authorization.model.RoleInfo;
import com.zte.mcrm.adapter.authorization.service.RoleService;
import com.zte.mcrm.channel.constant.SourceOfOpportunityEnum;
import com.zte.mcrm.channel.model.dto.AuthorizationRoleInfoDTO;
import com.zte.mcrm.channel.model.dto.OpportunityInfoDTO;
import com.zte.mcrm.channel.model.entity.Opportunity;
import com.zte.mcrm.channel.model.entity.OpportunityDetail;
import com.zte.mcrm.channel.model.mapper.OpportunityInfoVOMapper;
import com.zte.mcrm.channel.model.vo.OpportunityAddVO;
import com.zte.mcrm.channel.model.vo.OpportunityInfoVO;
import com.zte.mcrm.channel.model.vo.OpportunityProductVO;
import com.zte.mcrm.channel.service.channel.IOpportunityInfoService;
import com.zte.mcrm.channel.service.channel.IOpportunityService;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.model.SysGlobalConstVo;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.mcrm.common.util.ValidationGroups;
import com.zte.springbootframe.util.local.LocalMessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.Assert;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;


/**
 * 新建商机相关 MVC 控制类
 * <AUTHOR>
 * @date 2021/09/15
 */
@Api(tags = "新建商机相关API")
@RestController
@RequestMapping("/channel/newOpportunity")
public class OpportunityAddController {
    /** 日志对象 */
    private static final Logger log = LoggerFactory.getLogger(OpportunityAddController.class);

    /** 服务对象，SPRING自动装配 */
    @Autowired
    IOpportunityService opportunityServiceImpl ;
    @Autowired
    RoleService roleService;
    @Autowired
    IOpportunityInfoService opportunityInfoService;
    @Autowired
    MessageSource messageSource;

    @Resource(name="sendRestrictedEntityMainThread")
    ThreadPoolTaskExecutor taskExecutor;
    /** 最终用户行业: 商业="71" , 分销="94" */
    @Value("${channel.new.business.finalCustomerParentTradeList:71,94}")
    private List<String> newBusinessFinalCustomerParentTradeList;

    @ApiOperation("iChannel侧-提交商机")
    @PostMapping(value = "/submit")
    public ServiceData<OpportunityInfoVO> submitOpportunityInfo(@Validated(ValidationGroups.Submit.class) @RequestBody OpportunityInfoVO opportunityInfoVO,
                                                                BindingResult bindingResult) throws Exception {
        // 数据校验
        if (bindingResult != null && bindingResult.hasErrors()) {
            throw new com.zte.springbootframe.common.exception.ValidationException(bindingResult);
        }
        ServiceData<OpportunityInfoVO> sd = new ServiceData<>();
        OpportunityAddVO opportunityAddVO = opportunityInfoVO.getOpportunity();
        Assert.notNull(opportunityAddVO, LocalMessageUtils.getMessage("opportunityNull"));
        opportunityAddVO.setDataSource(SourceOfOpportunityEnum.CHANNEL_FILING.getValue());
        OpportunityInfoDTO opportunityInfo = OpportunityInfoVOMapper.transOpportunityInfoVOToOpportunityInfoDTO(opportunityInfoVO);

        // 对外提交时，最终用户行业: 商业="71" or 分销="94" , 则为新业务
        OpportunityDetail opportunityDetail = opportunityInfo.getOpportunityDetail();
        Optional.ofNullable(opportunityDetail).ifPresent(e -> {
            String finalCustomerParentTrade = e.getFinalCustomerParentTrade();
            Opportunity opportunity = opportunityInfo.getOpportunity();
            if (newBusinessFinalCustomerParentTradeList.contains(finalCustomerParentTrade)) {
                opportunity.setIsNewBusiness(CommonConst.Y);
            }else {
                opportunity.setIsNewBusiness(CommonConst.N);
            }
        });

        ServiceData<OpportunityInfoDTO> opportunityInfoResult = opportunityInfoService.submitOpportunity(opportunityInfo);
        sd.setCode(opportunityInfoResult.getCode());
        OpportunityInfoVO result = OpportunityInfoVOMapper.transOpportunityInfoDTOToOpportunityInfoVO(opportunityInfoResult.getBo());
        sd.setBo(result);
        if (opportunityInfoResult.getCode() != null && RetCode.SUCCESS_CODE.equals(opportunityInfoResult.getCode().getCode())){
            // 提交成功后发送邮件
            try {
                opportunityServiceImpl.sendRestrictedEntityMail(opportunityAddVO.getRowId());
            } catch (Exception e) {
                log.error("send mail error:{}", e);
            }
        }
        return sd;
    }


    @ApiOperation("iChannel侧-暂存商机")
    @PostMapping(value = "/storage")
    public ServiceData<OpportunityInfoVO> storageOpportunityInfo(@Validated(ValidationGroups.Storage.class) @RequestBody OpportunityInfoVO opportunityInfoVO,
                                                                 BindingResult bindingResult) throws Exception {
        // 数据校验
        if (bindingResult != null && bindingResult.hasErrors()) {
            throw new com.zte.springbootframe.common.exception.ValidationException(bindingResult);
        }
        OpportunityAddVO opportunityAddVO = opportunityInfoVO.getOpportunity();
        Assert.notNull(opportunityAddVO, LocalMessageUtils.getMessage("opportunityNull"));
        opportunityAddVO.setDataSource(SourceOfOpportunityEnum.CHANNEL_FILING.getValue());
        OpportunityInfoDTO opportunityInfo = OpportunityInfoVOMapper.transOpportunityInfoVOToOpportunityInfoDTO(opportunityInfoVO);
        ServiceData<OpportunityInfoVO> sd = new ServiceData<>();
        // 对外保存时，最终用户行业: 商业="71" or 分销="94" , 则为新业务
        OpportunityDetail opportunityDetail = opportunityInfo.getOpportunityDetail();
        Optional.ofNullable(opportunityDetail).ifPresent(e -> {
            String finalCustomerParentTrade = e.getFinalCustomerParentTrade();
            Opportunity opportunity = opportunityInfo.getOpportunity();
            if (newBusinessFinalCustomerParentTradeList.contains(finalCustomerParentTrade)) {
                opportunity.setIsNewBusiness(CommonConst.Y);
            }else {
                opportunity.setIsNewBusiness(CommonConst.N);
            }
        });
        OpportunityInfoDTO opportunityInfoResult = opportunityInfoService.storageOpportunityInfo(opportunityInfo);
        OpportunityInfoVO result = OpportunityInfoVOMapper.transOpportunityInfoDTOToOpportunityInfoVO(opportunityInfoResult);
        sd.setBo(result);
        return sd;
    }

    @ApiOperation("依据角色、组织、子行业从权限平台找人")
    @PostMapping("/queryRoleInfoWithConstraint")
    public ServiceData<List<RoleInfo>> queryRoleInfoWithConstraint(@RequestBody AuthorizationRoleInfoDTO authorizationRoleInfoDTO) throws Exception {
        ServiceData<List<RoleInfo>> result = new ServiceData<>();
        result.setBo(opportunityInfoService.queryRoleInfoWithConstraint(authorizationRoleInfoDTO));
        return result;
    }

    @ApiOperation("维护商机主产品信息")
    @PostMapping("/maintainMainProds")
    public ServiceData<Boolean> queryRoleInfoWithConstraint(@Validated @RequestBody List<OpportunityProductVO> maintainMainProdsParams) throws Exception {
        ServiceData<Boolean> result = new ServiceData<>();
        result.setBo(opportunityInfoService.maintainMainProds(maintainMainProdsParams));
        return result;
    }

}
