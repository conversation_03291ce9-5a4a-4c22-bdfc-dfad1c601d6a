package com.zte.mcrm.adapter.mail.service;

import com.zte.mcrm.adapter.mail.domain.ZmailModel;
import com.zte.mcrm.channel.model.entity.OpportunityMailEntity;

/**
 * <AUTHOR>
 */
public interface SendMailService
{

    /**
     * 发送邮件
     * @param zmailModel
     */
    void send(ZmailModel zmailModel);

    /**
     * 发送商机邮件
     * @param opportunityMailEntity
     */
    void sendOptyMail(OpportunityMailEntity opportunityMailEntity);
}
