package com.zte.leadinfo.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 
 * <AUTHOR>
 * @TableName cx_appr_op_head
 */
@TableName(value ="cx_appr_op_head")
@Data
public class CxApprOpHead implements Serializable {
    /**
     * 
     */
    @TableId(value = "ROW_ID")
    private String rowId;

    /**
     * 
     */
    @TableField(value = "CREATED")
    private Date created;

    /**
     * 
     */
    @TableField(value = "CREATED_BY")
    private String createdBy;

    /**
     * 
     */
    @TableField(value = "LAST_UPD")
    private Date lastUpd;

    /**
     * 
     */
    @TableField(value = "LAST_UPD_BY")
    private String lastUpdBy;

    /**
     * 当前审批节点顺序
     */
    @TableField(value = "CUR_APPR_NODE_ID")
    private String curApprNodeId;

    /**
     * 描述
     */
    @TableField(value = "DESC_TEXT")
    private String descText;

    /**
     * 审批对象Id
     */
    @TableField(value = "NAME")
    private String name;

    /**
     * 提交人Id
     */
    @TableField(value = "SUBMIT_USER_ID")
    private String submitUserId;

    /**
     * 审批树Id
     */
    @TableField(value = "APPROVE_TREE_ID")
    private String approveTreeId;

    /**
     * 商机负责人Id项目经理Id
     */
    @TableField(value = "PROJ_MANAGER_ID")
    private String projManagerId;

    /**
     * 审批对象Id
     */
    @TableField(value = "OBJECT_ID")
    private String objectId;

    /**
     * 
     */
    @TableField(value = "DEPT_FULL_PATH")
    private String deptFullPath;

    /**
     * 审批发起人所在的行政部门
     */
    @TableField(value = "DEPT_ID")
    private String deptId;

    /**
     * 审批发起人所在的行政部门
     */
    @TableField(value = "DEPT_NAME")
    private String deptName;

    /**
     * 审批类型-ZTE_APPROVE_OBJECT
     */
    @TableField(value = "APPROVE_OBJECT")
    private String approveObject;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /** 审批单明细 */
    @TableField(exist = false)
    private List<CxApprOppLn> cxApprOppLnList;
}