package com.zte.mcrm.channel.model.dto;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.zte.mcrm.adapter.constant.ProjectAuthStatusEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ProjectAuthInfoDto {
    /** 授权次数 */
    private Long numberOfAuthorizations;

    /** 授权编号&授权状态 */
    private String authIdAndStatus;

    /** 授权状态 */
    private ProjectAuthStatusEnum statusAuth;

}
