package com.zte.mcrm.adapter.approval.model;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @description: 待审批参数
 * <AUTHOR>
 * @date: 2021/6/23 下午4:51
 */
@Data
public class ApprovingParameter {
    /** 业务id */
    private String businessId;
    /** 流程实例id */
    private String flowInstanceId;
    /** 应用编码 */
    private String appCode;
    /** 流程节点 */
    private String flowCode;
    /** 员工工号 */
    @NotBlank
    private String empNo;
    /** 页号 */
    private Integer pageNo;
    /** 页数 */
    private Integer pageSize;
}