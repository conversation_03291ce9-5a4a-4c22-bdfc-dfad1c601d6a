package com.zte.mcrm.channel.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 渠道商和最终用户受限制主体实体类
 */
@ToString
@Getter
@Setter
@Builder
public class RestrictedPartyVO {

    @ApiModelProperty(value = "最终用户名称")
    private String endUserName;
    @ApiModelProperty(value = "最终用户受限制主体名称")
    private String endUserRestrictedPartyName;
    @ApiModelProperty(value = "最终用户受限制主体编码")
    private String endUserRestrictedPartyCode;

    @ApiModelProperty(value = "渠道商名称")
    private String customerName;
    @ApiModelProperty(value = "渠道商受限制主体名称")
    private String customerRestrictedPartyName;
    @ApiModelProperty(value = "渠道商受限制主体编码")
    private String customerRestrictedPartyCode;
}
