package com.zte.mcrm.channel.model.mapper;

import com.zte.mcrm.adapter.model.vo.PartnerInfoVO;
import com.zte.mcrm.adapter.model.vo.PartnerListVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2021/11/19
 */
@Mapper
public interface PartnerMapper {
    PartnerMapper INSTANCE = Mappers.getMapper(PartnerMapper.class);

    /**
     * PartnerListVo 转 PartnerInfoVO
     * @param partnerListVO
     * @return
     */
    @Mapping(target = "name", source = "name")
    @Mapping(target = "nameEn", source = "nameEn")
    @Mapping(target = "gtsFlag", source = "gtsFlag")
    @Mapping(target = "gtsFlagName", source = "gtsFlagName")
    @Mapping(target = "crmCustomerCode", source = "partnerBigCategoryId")
    PartnerInfoVO transPartnerListVoToPartnerInfoVO(PartnerListVO partnerListVO);
}
