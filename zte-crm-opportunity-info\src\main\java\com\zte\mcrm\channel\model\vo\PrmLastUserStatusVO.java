package com.zte.mcrm.channel.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
public class PrmLastUserStatusVO {

    @ApiModelProperty(value = "最终用户状态 0-无状态；1-待创建客户草稿；2-已创建客户草稿；3-创建客户草稿后生效的客户；4-客户系统本身已存在生效客户")
    private Integer lastAccStatus;

    @ApiModelProperty(value = "中兴业务经理id")
    private String businessManagerId;

    @ApiModelProperty(value = "中兴业务经理名字")
    private String businessManagerName;

    @ApiModelProperty(value = "最终用户名称")
    private String lastAccName;
}
