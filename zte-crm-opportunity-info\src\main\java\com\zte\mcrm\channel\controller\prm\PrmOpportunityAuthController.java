package com.zte.mcrm.channel.controller.prm;

import com.zte.itp.authorityclient.entity.input.CommonModuleIdEntity;
import com.zte.itp.authorityclient.entity.output.RoleVO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.util.web.HttpHeaderUtil;
import com.zte.mcrm.adapter.authorization.service.UppAuthorityService;
import com.zte.mcrm.channel.service.prm.IPrmOpportunityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 */
@Api(tags = "鉴权相关API")
@RestController
@RequestMapping("/prm/auth")
public class PrmOpportunityAuthController {
    /** 日志对象 */
    private static final Logger log = LoggerFactory.getLogger(PrmOpportunityAuthController.class);

    @Autowired
    UppAuthorityService uppAuthorityService;
    @Autowired
    IPrmOpportunityService prmOpportunityService;

    @ApiOperation("prm侧，查询我的角色Map")
    @GetMapping(value = "/roleMap")
    public ServiceData<Map<String, RoleVO>> getMyRoleMap(@RequestHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO) String empNo){
        ServiceData<Map<String, RoleVO>> sd = new ServiceData<>();
        CommonModuleIdEntity entity = new CommonModuleIdEntity();
        entity.setEmpidui(empNo);
        entity.setToken(HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE));
        sd.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        sd.setBo(uppAuthorityService.getRoleMap(entity));
        return sd;
    }

    @ApiOperation("prm侧，校验详情查看权限")
    @GetMapping(value = "/checkDetailsViewPermissions")
    public ServiceData<Boolean> checkDetailsViewPermissions(@RequestHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO) String empNo,
                                                            @RequestParam(name = "rowId") @ApiParam(value = "主键id", required = true) String rowId) throws ExecutionException, InterruptedException {
        ServiceData<Boolean> sd = new ServiceData<>();
        sd.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        sd.setBo(prmOpportunityService.checkDetailReviewPermission(empNo, rowId));
        return sd;
    }
}
