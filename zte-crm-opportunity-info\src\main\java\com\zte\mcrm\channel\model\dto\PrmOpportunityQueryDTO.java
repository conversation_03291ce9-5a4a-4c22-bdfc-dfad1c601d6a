package com.zte.mcrm.channel.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class PrmOpportunityQueryDTO {

    @ApiModelProperty(value = "关键字")
    private String keyWordForQuery;

    @ApiModelProperty(value = "报备时间_起始时间")
    private String minCreateDate;

    @ApiModelProperty(value = "报备时间_结束时间")
    private String maxCreateDate;

    @ApiModelProperty(value = "预计发标时间_起始时间")
    private String minTimeForBidIssuance;

    @ApiModelProperty(value = "预计发标时间_结束时间")
    private String maxTimeForBidIssuance;

    @ApiModelProperty(value = "招标类型编码")
    private String tenderTypeCode;

    @ApiModelProperty(value = "项目当前阶段编码")
    private String projectPhasesCode;

    @ApiModelProperty(value = "投资方所在地（商机所属部门）")
    private String deptNo;

    @ApiModelProperty(value = "最终用户行业编码")
    private String finalCustomerTrade;

    @ApiModelProperty(value = "最终用户子行业编码")
    private String finalCustomerChildTrade;

    @ApiModelProperty(value = "商机状态")
    private List<String> statusCd;

    @ApiModelProperty(value = "商机来源")
    private List<String> dataSource;

    @ApiModelProperty(value = "授权状态")
    private List<String> statusAuth;

    @ApiModelProperty(value = "月报状态")
    private List<String> statusReport;
}
