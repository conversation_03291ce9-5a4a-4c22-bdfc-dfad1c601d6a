<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.aiagent.infrastruction.access.mapper.BidParseItemMapper">
    <!-- 基础结果集映射 -->
    <resultMap id="BaseResultMap" type="com.zte.aiagent.infrastruction.access.po.BidParseItemPO">
        <id column="row_id" property="rowId" jdbcType="VARCHAR"/>
        <result column="parse_record_id" property="parseRecordId" jdbcType="VARCHAR"/>
        <result column="item_code" property="itemCode" jdbcType="VARCHAR"/>
        <result column="item_name" property="itemName" jdbcType="VARCHAR"/>
        <result column="item_value" property="itemValue" jdbcType="VARCHAR"/>
        <result column="source_text" property="sourceText" jdbcType="VARCHAR"/>
        <result column="data_level" property="dataLevel" jdbcType="INTEGER"/>
        <result column="parent_item_code" property="parentItemCode" jdbcType="VARCHAR"/>
        <result column="order_value" property="orderValue" jdbcType="INTEGER"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_date" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR"/>
        <result column="last_updated_date" property="lastUpdatedDate" jdbcType="TIMESTAMP"/>
        <result column="enabled_flag" property="enabledFlag" jdbcType="VARCHAR"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 插入解析条目记录 -->
    <insert id="insert" parameterType="com.zte.aiagent.infrastruction.access.po.BidParseItemPO">
        INSERT INTO bid_parse_item (
            row_id,
            parse_record_id,
            item_code,
            item_name,
            item_value,
            source_text,
            data_level,
            parent_item_code,
            order_value,
            created_by,
            created_date,
            last_updated_by,
            last_updated_date,
            enabled_flag,
            tenant_id
        ) VALUES (
                     #{rowId,jdbcType=VARCHAR},
                     #{parseRecordId,jdbcType=VARCHAR},
                     #{itemCode,jdbcType=VARCHAR},
                     #{itemName,jdbcType=VARCHAR},
                     #{itemValue,jdbcType=VARCHAR},
                     #{sourceText,jdbcType=VARCHAR},
                     #{dataLevel,jdbcType=INTEGER},
                     #{parentItemCode,jdbcType=VARCHAR},
                     #{orderValue,jdbcType=INTEGER},
                     #{createdBy,jdbcType=VARCHAR},
                     #{createdDate,jdbcType=TIMESTAMP},
                     #{lastUpdatedBy,jdbcType=VARCHAR},
                     #{lastUpdatedDate,jdbcType=TIMESTAMP},
                     #{enabledFlag,jdbcType=VARCHAR},
                     #{tenantId,jdbcType=BIGINT}
                 )
    </insert>

    <!-- 批量插入解析条目记录 -->
    <insert id="batchInsert" parameterType="java.util.Map">
        INSERT INTO bid_parse_item (
        row_id,
        parse_record_id,
        item_code,
        item_name,
        item_value,
        source_text,
        data_level,
        parent_item_code,
        order_value,
        created_by,
        created_date,
        last_updated_by,
        last_updated_date,
        enabled_flag,
        tenant_id
        ) VALUES
        <foreach collection="items" item="item" separator=",">
            (
            #{item.rowId,jdbcType=VARCHAR},
            #{item.parseRecordId,jdbcType=VARCHAR},
            #{item.itemCode,jdbcType=VARCHAR},
            #{item.itemName,jdbcType=VARCHAR},
            #{item.itemValue,jdbcType=VARCHAR},
            #{item.sourceText,jdbcType=VARCHAR},
            #{item.dataLevel,jdbcType=INTEGER},
            #{item.parentItemCode,jdbcType=VARCHAR},
            #{item.orderValue,jdbcType=INTEGER},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdDate,jdbcType=TIMESTAMP},
            #{item.lastUpdatedBy,jdbcType=VARCHAR},
            #{item.lastUpdatedDate,jdbcType=TIMESTAMP},
            #{item.enabledFlag,jdbcType=VARCHAR},
            #{item.tenantId,jdbcType=BIGINT}
            )
        </foreach>
    </insert>

    <!-- 根据ID查询解析条目 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
            row_id,
            parse_record_id,
            item_code,
            item_name,
            item_value,
            source_text,
            data_level,
            parent_item_code,
            order_value,
            created_by,
            created_date,
            last_updated_by,
            last_updated_date,
            enabled_flag,
            tenant_id
        FROM bid_parse_item
        WHERE row_id = #{rowId,jdbcType=VARCHAR}
    </select>

    <!-- 根据解析记录ID查询条目列表 -->
    <select id="selectByParseRecordId" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
            row_id,
            parse_record_id,
            item_code,
            item_name,
            item_value,
            source_text,
            data_level,
            parent_item_code,
            order_value,
            created_by,
            created_date,
            last_updated_by,
            last_updated_date,
            enabled_flag,
            tenant_id
        FROM bid_parse_item
        WHERE parse_record_id = #{parseRecordId,jdbcType=VARCHAR}
          AND enabled_flag = 'Y'
        ORDER BY order_value ASC, data_level ASC
    </select>

    <!-- 根据解析记录ID和条目编码查询条目 -->
    <select id="selectByRecordIdAndItemCode" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
            row_id,
            parse_record_id,
            item_code,
            item_name,
            item_value,
            source_text,
            data_level,
            parent_item_code,
            order_value,
            created_by,
            created_date,
            last_updated_by,
            last_updated_date,
            enabled_flag,
            tenant_id
        FROM bid_parse_item
        WHERE parse_record_id = #{parseRecordId,jdbcType=VARCHAR}
          AND item_code = #{itemCode,jdbcType=VARCHAR}
          AND enabled_flag = 'Y'
    </select>

    <!-- 根据ID更新解析条目 -->
    <update id="updateByPrimaryKey" parameterType="com.zte.aiagent.infrastruction.access.po.BidParseItemPO">
        UPDATE bid_parse_item
        SET
            parse_record_id = #{parseRecordId,jdbcType=VARCHAR},
            item_code = #{itemCode,jdbcType=VARCHAR},
            item_name = #{itemName,jdbcType=VARCHAR},
            item_value = #{itemValue,jdbcType=VARCHAR},
            source_text = #{sourceText,jdbcType=VARCHAR},
            data_level = #{dataLevel,jdbcType=INTEGER},
            parent_item_code = #{parentItemCode,jdbcType=VARCHAR},
            order_value = #{orderValue,jdbcType=INTEGER},
            created_by = #{createdBy,jdbcType=VARCHAR},
            created_date = #{createdDate,jdbcType=TIMESTAMP},
            last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            last_updated_date = #{lastUpdatedDate,jdbcType=TIMESTAMP},
            enabled_flag = #{enabledFlag,jdbcType=VARCHAR},
            tenant_id = #{tenantId,jdbcType=BIGINT}
        WHERE row_id = #{rowId,jdbcType=VARCHAR}
    </update>

    <!-- 根据ID删除解析条目 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        DELETE FROM bid_parse_item
        WHERE row_id = #{rowId,jdbcType=VARCHAR}
    </delete>

    <!-- 根据解析记录ID删除所有关联条目 -->
    <delete id="deleteByParseRecordId" parameterType="java.lang.String">
        DELETE FROM bid_parse_item
        WHERE parse_record_id = #{parseRecordId,jdbcType=VARCHAR}
    </delete>
</mapper>
