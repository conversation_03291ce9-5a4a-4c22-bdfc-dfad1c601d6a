## 1. 项目概述

### 1.1 项目背景

招标文件解析功能是为了解决传统招标文件人工审核效率低、易出错的问题。通过AI智能解析技术，自动提取招标文件中的关键信息，提高招标业务处理效率，降低人工成本。

### 1.2 功能目标

- **智能解析**: 自动提取招标文件中的关键条目
- **灵活配置**: 支持解析条目的动态配置和管理
- **结果展示**: 提供清晰的解析结果展示和导出功能
- **审核流程**: 支持解析结果的人工审核和修正

### 1.3 技术选型

| 组件 | 技术栈 | 版本 | 说明 |
|------|--------|------|------|
| 后端框架 | Spring Boot | 2.7.x | 微服务架构主框架 |
| ORM框架 | MyBatis-Plus | 3.5.x | 数据库操作框架 |
| 数据库 | MySQL | 8.0.x | 关系型数据库 |
| 缓存 | Redis | 6.2.x | 缓存和会话管理 |
| 消息队列 | Kafka | 3.x | 异步消息处理 |
| 文件存储 | 文档云服务 | - | 文件上传和预览 |
| AI服务 | AI应用平台 | - | 文档智能解析 |
| 前端框架 | Vue.js | 3.x | 用户界面 |

---

## 2. 架构设计

### 2.1 整体架构

```mermaid
graph TB
    subgraph "前端层"
        A[Vue.js前端应用]
        B[文件上传组件]
        C[解析结果展示]
        D[配置管理界面]
    end
    
    subgraph "应用层"
        E[招标文件Controller]
        F[解析配置Controller]
        G[文件上传Service]
        H[解析引擎Service]
        I[结果处理Service]
    end
    
    subgraph "业务层"
        J[文档管理模块]
        K[解析配置模块]
        L[文档解析模块]
        M[结果导出模块]
    end
    
    subgraph "数据层"
        N[MySQL数据库]
        O[Redis缓存]
        P[文档云存储]
    end
    
    subgraph "外部服务"
        Q[AI应用平台]
        R[文档云服务]
    end
    
    A --> E
    B --> G
    C --> I
    D --> F
    
    E --> J
    F --> K
    G --> L
    H --> M
    I --> J
    
    J --> N
    M --> N
    K --> N
    L --> Q
    M --> P
    
    G --> R
    H --> Q
    
    K --> O
```

### 2.2 模块设计

#### 2.2.1 文档管理模块
- **职责**: 招标文件的上传、存储、查询、删除
- **核心类**: `BidDocumentService`, `BidDocumentMapper`
- **数据表**: `bid_document`

#### 2.2.2 解析引擎模块
- **职责**: 文档解析任务的调度、执行、监控
- **核心类**: `ParseEngineService`, `ParseTaskExecutor`
- **数据表**: `bid_parse_record`, `bid_parse_item`

#### 2.2.3 配置管理模块
- **职责**: 解析条目配置的管理和维护
- **核心类**: `ConfigService`, `ConfigMapper`
- **数据表**: `sys_conf_type`, `sys_conf_value`

---

## 2.3 DDD领域划分设计

### 2.3.1 限界上下文

**招标文件解析上下文 (BidDocumentParseContext)**
- **核心职责**: 招标文件的智能解析和数据提取
- **业务边界**: 从文件上传到解析结果导出的完整流程
- **统一语言**: 文档、解析、模板、条目、聚合等业务概念

### 2.3.2 子域划分

```mermaid
graph TB
    subgraph "招标文件解析领域"
        subgraph "核心域 (Core Domain)"
            A[解析执行子域<br/>ParseExecution]
        end
        
        subgraph "支撑域 (Supporting Domain)"
            B[文档管理子域<br/>DocumentManagement]
            C[配置管理子域<br/>ConfigManagement]
        end
        
        subgraph "通用域 (Generic Domain)"
            D[数据存储子域<br/>DataStorage]
            E[文件处理子域<br/>FileProcessing]
        end
    end
    
    A -.-> B
    A -.-> C
    A -.-> D
    B -.-> E
    C -.-> D
```

#### 2.3.3 聚合设计

| 子域 | 聚合根 | 实体 | 值对象 | 主要职责 |
|------|--------|------|--------|----------|
| **文档管理** | BidDocument | - | DocumentInfo, ParseStatus, FileMetadata | 文档生命周期管理 |
| **解析执行** | ParseRecord | ParseItem | ParseResult, ItemValue | 解析过程管理 |
| **配置管理** | ParseConfig | ConfigItem | ConfigValue, AIPrompt | 解析规则配置 |
| **数据存储** | ParseData | DataChunk | StorageInfo, ChunkMetadata | 大数据分块存储 |

---

## 2.4 项目结构规范

### 2.4.1 整体架构

```
com.zte.mcrm.bid.parse/
├── interfaces/                          # 接口层 - 用户界面交互
├── application/                         # 应用层 - 用例编排
├── domain/                              # 领域层 - 业务逻辑核心
└── infrastructure/                      # 基础设施层 - 技术实现
```

### 2.4.2 详细包结构

```
src/main/java/com/zte/mcrm/bid/parse/
├── interfaces/                          # 接口层
│   ├── ui/                             # Web接口
│   │   ├── controller/                  # 控制器
│   │   │   ├── BidDocumentController.java
│   │   │   ├── ParseConfigController.java
│   │   │   └── SystemConfigController.java
│   │   ├── dto/                         # 数据传输对象
│   │   │   ├── request/                 # 请求DTO
│   │   │   │   ├── DocumentUploadRequest.java
│   │   │   │   ├── DocumentQueryRequest.java
│   │   │   │   ├── ParseResultRequest.java
│   │   │   │   ├── ConfigSaveRequest.java
│   │   │   │   └── ExportRequest.java
│   │   │   ├── response/                # 响应DTO
│   │   │   │   ├── DocumentListResponse.java
│   │   │   │   ├── DocumentDetailResponse.java
│   │   │   │   ├── ParseResultResponse.java
│   │   │   │   ├── ConfigTreeResponse.java
│   │   │   │   └── ExportResponse.java
│   │   │   └── vo/                      # 视图对象
│   │   │       ├── BidDocumentVO.java
│   │   │       ├── ParseItemVO.java
│   │   │       ├── ConfigItemVO.java
│   │   │       └── ParseProgressVO.java
│   │   └── assembler/                   # 对象装配器
│   │       ├── BidDocumentAssembler.java
│   │       ├── ParseResultAssembler.java
│   │       └── ConfigAssembler.java
│   └── facade/                          # 门面接口
│       └── BidParseServiceFacade.java
│
├── application/                         # 应用层
│   ├── service/                         # 应用服务
│   │   ├── BidDocumentAppService.java
│   │   ├── ParseExecutionAppService.java
│   │   ├── ConfigManagementAppService.java
│   │   └── DataExportAppService.java
│   ├── command/                         # 命令对象
│   │   ├── CreateDocumentCommand.java
│   │   ├── StartParseCommand.java
│   │   ├── RetryParseCommand.java
│   │   ├── SaveConfigCommand.java
│   │   └── ExportResultCommand.java
│   ├── query/                           # 查询对象
│   │   ├── DocumentListQuery.java
│   │   ├── ParseResultQuery.java
│   │   ├── ConfigTreeQuery.java
│   │   └── ParseProgressQuery.java
│   ├── event/                           # 应用事件处理
│   │   ├── handler/
│   │   │   ├── DocumentEventHandler.java
│   │   │   ├── ParseEventHandler.java
│   │   │   └── ConfigEventHandler.java
│   │   └── publisher/
│   │       └── ApplicationEventPublisher.java
│   └── task/                            # 异步任务
│       ├── ParseTaskExecutor.java
│       ├── ParseTaskQueue.java
│       └── ScheduledTasks.java
│
├── domain/                              # 领域层
│   ├── document/                        # 文档管理聚合
│   │   ├── aggregate/
│   │   │   └── BidDocument.java         # 聚合根
│   │   ├── entity/
│   │   │   └── (无独立实体)
│   │   ├── valueobject/
│   │   │   ├── DocumentInfo.java
│   │   │   ├── ParseStatus.java
│   │   │   ├── FileMetadata.java
│   │   │   └── DocumentSummary.java
│   │   ├── repository/
│   │   │   └── BidDocumentRepository.java
│   │   ├── service/
│   │   │   ├── DocumentValidationService.java
│   │   │   └── DocumentLifecycleService.java
│   │   └── event/
│   │       ├── DocumentCreatedEvent.java
│   │       ├── ParseStartedEvent.java
│   │       ├── ParseCompletedEvent.java
│   │       └── ParseFailedEvent.java
│   │
│   ├── parse/                           # 解析执行聚合
│   │   ├── aggregate/
│   │   │   └── ParseRecord.java         # 聚合根
│   │   ├── entity/
│   │   │   └── ParseItem.java
│   │   ├── valueobject/
│   │   │   ├── ParseResult.java
│   │   │   ├── ItemValue.java
│   │   │   ├── ParseDuration.java
│   │   │   └── ExtractionContext.java
│   │   ├── repository/
│   │   │   └── ParseRecordRepository.java
│   │   ├── service/
│   │   │   ├── ParseExecutionService.java
│   │   │   ├── AIParseService.java
│   │   │   └── ResultValidationService.java
│   │   └── specification/
│   │       ├── ParseCompletionSpec.java
│   │       └── ItemExtractionSpec.java
│   │
│   ├── config/                          # 配置管理聚合
│   │   ├── aggregate/
│   │   │   └── ParseConfig.java         # 聚合根
│   │   ├── entity/
│   │   │   └── ConfigItem.java
│   │   ├── valueobject/
│   │   │   ├── ConfigValue.java
│   │   │   ├── TreeNode.java
│   │   │   └── TemplateInfo.java
│   │   ├── repository/
│   │   │   └── ParseConfigRepository.java
│   │   ├── service/
│   │   │   ├── ConfigValidationService.java
│   │   │   ├── TemplateManagementService.java
│   │   │   └── ConfigTreeBuildingService.java
│   │   └── factory/
│   │       └── ConfigItemFactory.java
│   │
│   ├── storage/                         # 数据日志聚合
│   │   ├── aggregate/
│   │   │   └── ParseData.java           # 聚合根
│   │   ├── valueobject/
│   │   │   └── StorageInfo.java
│   │   ├── repository/
│   │   │   └── ParseDataRepository.java
│   │   └── service/
│   │       ├── ContentChunkingService.java
│   │       ├── DataIntegrityService.java
│   │       └── StorageOptimizationService.java
│   │
│   └── shared/                          # 共享领域对象/服务
│       ├── valueobject/
│       │   ├── UserInfo.java
│       │   └── AuditInfo.java
│       ├── exception/
│       │   ├── DomainException.java
│       │   ├── ParseException.java
│       │   ├── ConfigException.java
│       │   └── StorageException.java
│       ├── specification/
│       │   ├── TenantAccessSpec.java
│       │   └── DataValidationSpec.java
│       └── event/
│           └── DomainEvent.java
│
└── infrastructure/                      # 基础设施层
    ├── access/                     # 持久化实现
    │   ├── mapper/                 # MyBatis映射器
    │   │   ├── BidDocumentMapper.java
    │   │   ├── ParseRecordMapper.java
    │   │   ├── ParseItemMapper.java
    │   │   ├── ParseDataMapper.java
    │   │   ├── SysConfTypeMapper.java
    │   │   ├── SysConfValueMapper.java
    │   │   ├── BidDocumentMapper.xml
    │   │   ├── ParseRecordMapper.xml
    │   │   ├── ParseItemMapper.xml
    │   │   ├── ParseDataMapper.xml
    │   │   ├── SysConfTypeMapper.xml
    │   │   └── SysConfValueMapper.xml
    │   ├── repository/                  # 仓储实现
    │   │   ├── BidDocumentRepositoryImpl.java
    │   │   ├── ParseRecordRepositoryImpl.java
    │   │   ├── ParseConfigRepositoryImpl.java
    │   │   └── ParseDataRepositoryImpl.java
    │   ├── po/                          # 持久化对象
    │   │   ├── BidDocumentPO.java
    │   │   ├── ParseRecordPO.java
    │   │   ├── ParseItemPO.java
    │   │   ├── ParseDataPO.java
    │   │   ├── SysConfTypePO.java
    │   │   └── SysConfValuePO.java
    │   └── converter/                   # 对象转换器
    │       ├── BidDocumentConverter.java
    │       ├── ParseRecordConverter.java
    │       ├── ParseConfigConverter.java
    │       └── ParseDataConverter.java
    │
    ├── external/                        # 外部服务适配
    │   ├── ai/                          # AI服务适配
    │   │   ├── AIServiceClient.java
    │   │   ├── AIRequestAdapter.java
    │   │   ├── AIResponseProcessor.java
    │   │   └── dto/
    │   │       ├── AIParseRequest.java
    │   │       └── AIParseResponse.java
    │   ├── document/                    # 文档云服务适配
    │   │   ├── DocumentCloudClient.java
    │   │   ├── DocumentUploadAdapter.java
    │   │   ├── DocumentPreviewAdapter.java
    │   │   └── dto/
    │   │       ├── FileUploadRequest.java
    │   │       └── FileUploadResponse.java
    │   └── cache/                       # 缓存服务适配
    │       ├── RedisCacheService.java
    │       └── CacheKeyGenerator.java
    │
    ├── messaging/                       # 消息处理
    │   ├── kafka/
    │   │   ├── producer/
    │   │   │   ├── KafkaMessageProducer.java
    │   │   │   └── ParseEventPublisher.java
    │   │   ├── consumer/
    │   │   │   ├── KafkaMessageConsumer.java
    │   │   │   └── ParseTaskConsumer.java
    │   │   └── config/
    │   │       └── KafkaConfiguration.java
    │   └── event/
    │       ├── DomainEventPublisher.java
    │       └── EventStore.java
    │
    ├── export/                          # 导出功能实现
    │   ├── ExcelExportService.java
    │   ├── PdfExportService.java
    │   ├── template/
    │   │   ├── ExcelTemplateProcessor.java
    │   │   └── PdfTemplateProcessor.java
    │   └── formatter/
    │       └── DataFormatter.java
    │
    └── config/                          # 配置类
        └── AsyncConfig.java
```
