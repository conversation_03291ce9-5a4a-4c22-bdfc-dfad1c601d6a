package com.zte.mcrm.common.framework.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "systemauthverify")
@PropertySource(value = "classpath:${spring.application.name}.properties", ignoreResourceNotFound = true)
@Data
public class SystemAuthVerifyProperty {

    /**
     * 权限校验开关
     * 默认为false，即不开启权限校验
     */
    boolean enable = false;

    /**
     * 密钥Map，key为accessKey，当前与调用服务名一致
     */
    Map<String, String> systemSecretKeyMap = new HashMap<>();

}
