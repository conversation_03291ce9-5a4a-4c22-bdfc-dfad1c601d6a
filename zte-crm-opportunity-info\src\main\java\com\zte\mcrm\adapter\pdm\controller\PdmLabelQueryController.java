package com.zte.mcrm.adapter.pdm.controller;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import com.zte.mcrm.adapter.pdm.model.PdmProductTreeVO;
import com.zte.mcrm.adapter.pdm.service.IPdmLabelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags ="PDM信息查询API")
@RestController
@RequestMapping("/pdm")
public class PdmLabelQueryController {

    @Resource
    private IPdmLabelService pdmLabelService;

    @ApiOperation("pdm产品分类树")
    @GetMapping(value = "/getProductTree")
    public ServiceData<List<PdmProductTreeVO>> getProductTree() {
        List<PdmProductTreeVO> result = pdmLabelService.queryProductClassTree(false);
        return ServiceResultUtil.success(result);
    }

    @ApiOperation("商机转立项产品分类树-五级")
    @GetMapping(value = "/getProductTreeFilter")
    public ServiceData<List<PdmProductTreeVO>> getProductTreeFilter() {
        List<PdmProductTreeVO> result = pdmLabelService.queryProductClassTreeFilterForProject();
        return ServiceResultUtil.success(result);
    }
}
