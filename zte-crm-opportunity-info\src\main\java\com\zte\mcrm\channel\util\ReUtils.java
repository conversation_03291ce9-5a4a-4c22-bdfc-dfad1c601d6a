package com.zte.mcrm.channel.util;

import cn.hutool.core.util.ReUtil;
import com.google.common.collect.Lists;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ReUtils {

    /**
     * 是否内部工号
     * @param innerId
     * @return
     */
    public static boolean isInnerId(String innerId) {
        return ReUtil.isMatch(OpportunityConstant.EMP_NO_REGEX, innerId);
    }

    /**
     * 过滤非内部工号
     * @param ids
     * @return
     */
    public static List<String> filterAbnormalEmpId(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return ids.stream().filter(id -> isInnerId(id)).collect(Collectors.toList());
    }

}
