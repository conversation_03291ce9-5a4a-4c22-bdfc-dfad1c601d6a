package com.zte.mcrm.channel.dao;

import java.util.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;
import com.zte.mcrm.channel.model.entity.*;


/**
 * 商机月报 数据访问接口类 
 * <AUTHOR>
 * @date 2021/10/20 
 */
@Mapper
public interface SMonthReportDao{
    /**
     * 根据主键查询
     * <AUTHOR>
     * @param id 主键
     * @date 2021/10/20 
     * @return 实体
     */
	SMonthReport get(@Param("id")String id);
	
    /**
     * 查询列表
     * <AUTHOR>
     * @param map 查询条件
     * @date 2021/10/20 
     * @return 实体集合
     */
	List<SMonthReport> getList(Map<String, Object> map);
	
	/**
     * 软删除，enabled_flag字段更新为N
     * <AUTHOR>
     * @param id 主键
     * @date 2021/10/20 
     * @return 删除总数
     */	
	int softDelete(@Param("id")String id);
	
    /**
     * 删除
     * <AUTHOR>
     * @param id 主键
     * @date 2021/10/20 
     * @return 删除总数
     */	
	int delete(@Param("id")String id);

    /**
     * 动态新增
     * <AUTHOR>
     * @param entity 新增实体
     * @date 2021/10/20 
     * @return 新增总数
     */	
	int insert(SMonthReport entity);

    /**
     * 批量新增
     * <AUTHOR>
     * @param list 新增实体集合
     * @date 2021/10/20 
     * @return 新增总数
     */	
	int insertByBatch(List<SMonthReport> list);

    /**
     * 更新
     * <AUTHOR>
     * @param entity 更新条件
     * @date 2021/10/20 
     * @return 更新影响总数
     */		
	int update(SMonthReport entity);

    /**
     * 统计
     * <AUTHOR>
     * @param map 查询条件
     * @date 2021/10/20 
     * @return 统计总数
     */	
	long getCount(Map<String, Object> map);

    /**
     * 分页查询
     * <AUTHOR>
     * @param map 查询条件
     * @date 2021/10/20 
     * @return 实体集合
     */	
	List<SMonthReport> getPage(Map<String, Object> map);
}