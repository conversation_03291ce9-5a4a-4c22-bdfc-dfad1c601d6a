package com.zte.mcrm.channel.service.channel;

import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.channel.model.entity.OpportunityDetail;
import com.zte.mcrm.channel.model.entity.OpportunityProduct;
import com.zte.opty.model.bo.SOptyProductBO;

import java.util.List;
import java.util.Map;


/**
 *  服务接口类
 * <AUTHOR>
 * @date 2021/09/14  
 */
public interface IOpportunityProductService {
    /**
     * 根据ID查询
     * @param rowId 主键ID
     * @return 实体
     * <AUTHOR>
     * @date 2021/09/14
     */
	OpportunityProduct get(String rowId);

    /**
     * 查询列表
     * @param map 参数集合
     * @return 实体集合
     * <AUTHOR>
     * @date 2021/09/14
     */
	List<OpportunityProduct> getList(Map<String, Object> map);

    /**
     * 软删除
     * @param rowId 主键ID
     * @return 删除记录个数
     * <AUTHOR>
     * @date 2021/09/14
     */
//	int softDelete(String rowId);

    /**
     * 批量软删除指定记录
     * @param rowIds
     * @return
     */
    int batchSoftDelete(List<String> rowIds);

    /**
     * 删除
     * @param rowId 主键ID
     * @return 删除记录个数
     * <AUTHOR>
     * @date 2021/09/14
     */
	int delete(String rowId);

    /**
     * 新增
     * @param entity 实体对象
     * @return 新增的记录对象,注意是提交数据库之前的实体对象
     * <AUTHOR>
     * @date 2021/09/14
     */
	OpportunityProduct insert(OpportunityProduct entity);

    /**
     * 更新
     * @param entity 实体对象
     * @return 修改的记录对象,注意是提交数据库之前的实体对象
     * <AUTHOR>
     * @date 2021/09/14
     */
	OpportunityProduct update(OpportunityProduct entity);

    /**
     * 批量插入商机产品信息
     *
     * @param opportunityProductList 产品信息列表
     * @param opptyId                商机id
     * @param businessType           业务类型
     * @param opportunityDetail
     * @return
     */
    List<SOptyProductBO> insertOrUpdateProductList(List<OpportunityProduct> opportunityProductList, String opptyId, String businessType,
                                                   OpportunityDetail opportunityDetail);

    /**
     * 统计
     * @param map 参数集合
     * @return 统计总数
     * <AUTHOR>
     * @date 2021/09/14
     */
	long getCount(Map<String, Object> map);

   /**
    * 分页查询
    * @param map 参数集合
    * @return 实体集合
    * <AUTHOR>
    * @date 2021/09/14
    */
	List<OpportunityProduct> getPage(Map<String, Object> map);

    /**
    * 分页查询
    * @param form 参数集合
    * @return 实体集合
    * <AUTHOR>
    * @date 2021/09/14
    */
    PageRows<OpportunityProduct> getPageRows(FormData<OpportunityProduct> form);
}
