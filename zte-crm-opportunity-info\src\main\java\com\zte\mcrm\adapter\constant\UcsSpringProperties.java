package com.zte.mcrm.adapter.constant;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/16
 */
@Component
@ConfigurationProperties(prefix = "ucs")
@PropertySource(value = "classpath:${spring.application.name}.properties", ignoreResourceNotFound = true)
@Data
public class UcsSpringProperties {
    /**
     * 调用方access key
     */
    private String accessKey;

    /**
     * 调用方id
     */
    private String appId;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * secretKey
     */
    private String secretKey;
    /**
     * 邮箱手机号解密
     */
    private String secretKeySixteen;
    /**
     * 邮箱手机号解密iv
     */
    private String iv;
    /**
     * 忽略列表
     */
    private List<String> ignorePath = new ArrayList<>();

    /**
     * 当请求头相关信息为空时，是否通过验证，开发时可设置为 false
     */
    private boolean tokenEnabled = true;
}
