package com.zte.mcrm.account.access.vo;

import com.zte.itp.msa.core.model.HeaderData;
import io.swagger.annotations.ApiModelProperty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;


/**
 * 
 * <AUTHOR>
 * 客户 关键字查询实体
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Account extends HeaderData {
	/**客户ID*/
	private String custId;
	/**客户编号*/
	private String custNo;
	/**客户名称*/
	private String custName;
	/**信息ID*/
	private String infoId;
	/**信息名称*/
	private String infoName;
	/**主要所属部门Id*/
	private String deptId;
	/**权限SQL*/
	private String authSql;
	/**客户id*/
	private String id;
	/**客户编码*/
	private String accountNum;
	/**客户名字*/
	private String accountName;
	/**国家*/
	private String localName;
	/**国家编码*/
	private String localNum;
	/**通讯地址*/
	private String loc;
	@ApiModelProperty("客户受制裁主体")
	private String sanctionedPatry;
    @ApiModelProperty("客户受制裁主体编码")
    private String sanctionedPatryCode;
	@ApiModelProperty("客户类型编码")
	private String accntTypeCd;
	@ApiModelProperty("客户类型显示值")
	private String accntType;
	/**子客户类型编码*/
	private String subAccntTypeCd;
	private String subAccntType;
	/**主管部门id*/
	private String buId;
	/**主管部门名字*/
	private String buName;
	/**主管部门编码*/
	private String buNum;
	/**综合信用评级*/
	@ApiModelProperty("totalRating")
	private String totalRating;
	/**业务客户分类名称*/
	private String businessAccntTypeName;
	/**mto名称*/
	@ApiModelProperty("集团客户简称")
	private String mtoName;
	/** 集团客户简称，也就是mto属性*/
	@ApiModelProperty("集团客户简称独立源代码")
	private String mtoNameCode;
	/**团队主要负责人职位ID*/
	private String prPostnId;
	/**客户eccId*/
	private String acctEccId;
	/**运营商类型*/
	private String operatorType;
	/**运营商类型*/
	private String operatorTypeCode;
	/**客户级别,也称运营商级别*/
	private String accountLevel;
	/**可用信用额度*/
	@ApiModelProperty("可用信用额度")
	private String available;
	@ApiModelProperty("客户信用风险提醒")
	/**客户信用风险提醒*/
	private String accntRiskTip;
	/**组织名称**/
	private String orgName;
	/**用户ID**/
	private String empId;
	/**客户范围*/
	private String custRange;
	@ApiModelProperty("客户范围独立语言代码")
	private String custRangeCode;

    public String getSanctionedPatryCode() {
        return sanctionedPatryCode;
    }

    public void setSanctionedPatryCode(String sanctionedPatryCode) {
        this.sanctionedPatryCode = sanctionedPatryCode;
    }

    public String getSubAccntType() {
		return subAccntType;
	}
	public void setSubAccntType(String subAccntType) {
		this.subAccntType = subAccntType;
	}
	public String getCustRangeCode() {
		return custRangeCode;
	}
	public void setCustRangeCode(String custRangeCode) {
		this.custRangeCode = custRangeCode;
	}
	public String getMtoNameCode() {
		return mtoNameCode;
	}
	public void setMtoNameCode(String mtoNameCode) {
		this.mtoNameCode = mtoNameCode;
	}
	public String getCustRange() {
		return custRange;
	}
	public void setCustRange(String custRange) {
		this.custRange = custRange;
	}
	public String getAccountLevel() {
		return accountLevel;
	}
	public void setAccountLevel(String accountLevel) {
		this.accountLevel = accountLevel;
	}
	public String getOperatorType() {
		return operatorType;
	}
	public void setOperatorType(String operatorType) {
		this.operatorType = operatorType;
	}
	public String getOperatorTypeCode() {
		return operatorTypeCode;
	}
	public void setOperatorTypeCode(String operatorTypeCode) {
		this.operatorTypeCode = operatorTypeCode;
	}
	public String getEmpId() {
		return empId;
	}
	public void setEmpId(String empId) {
		this.empId = empId;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getInfoId() {
		return infoId;
	}
	public void setInfoId(String infoId) {
		this.infoId = infoId;
	}
	public String getInfoName() {
		return infoName;
	}
	public void setInfoName(String infoName) {
		this.infoName = infoName;
	}
	public String getDeptId() {
		return deptId;
	}
	public void setDeptId(String deptId) {
		this.deptId = deptId;
	}
	public String getAccountNum() {
		return accountNum;
	}
	public void setAccountNum(String accountNum) {
		this.accountNum = accountNum;
	}
	public String getAuthSql() {
		return authSql;
	}
	public void setAuthSql(String authSql) {
		this.authSql = authSql;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getAccountName() {
		return accountName;
	}
	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}
	public String getLocalName() {
		return localName;
	}
	public void setLocalName(String localName) {
		this.localName = localName;
	}
	public String getLoc() {
		return loc;
	}
	public void setLoc(String loc) {
		this.loc = loc;
	}
	public String getSanctionedPatry() {
		return sanctionedPatry;
	}
	public void setSanctionedPatry(String sanctionedPatry) {
		this.sanctionedPatry = sanctionedPatry;
	}
	public String getAccntTypeCd() {
		return accntTypeCd;
	}
	public void setAccntTypeCd(String accntTypeCd) {
		this.accntTypeCd = accntTypeCd;
	}
	public String getSubAccntTypeCd() {
		return subAccntTypeCd;
	}
	public void setSubAccntTypeCd(String subAccntTypeCd) {
		this.subAccntTypeCd = subAccntTypeCd;
	}
	public String getBuId() {
		return buId;
	}
	public void setBuId(String buId) {
		this.buId = buId;
	}
	public String getBuName() {
		return buName;
	}
	public void setBuName(String buName) {
		this.buName = buName;
	}
	public String getBuNum() {
		return buNum;
	}
	public void setBuNum(String buNum) {
		this.buNum = buNum;
	}
	public String getTotalRating() {
		return totalRating;
	}
	public void setTotalRating(String totalRating) {
		this.totalRating = totalRating;
	}
	public String getBusinessAccntTypeName() {
		return businessAccntTypeName;
	}
	public void setBusinessAccntTypeName(String businessAccntTypeName) {
		this.businessAccntTypeName = businessAccntTypeName;
	}
	public String getMtoName() {
		return mtoName;
	}
	public void setMtoName(String mtoName) {
		this.mtoName = mtoName;
	}
	public String getPrPostnId() {
		return prPostnId;
	}
	public void setPrPostnId(String prPostnId) {
		this.prPostnId = prPostnId;
	}
	public String getAcctEccId() {
		return acctEccId;
	}
	public void setAcctEccId(String acctEccId) {
		this.acctEccId = acctEccId;
	}
	public String getCustId() {
		return custId;
	}
	public void setCustId(String custId) {
		this.custId = custId;
	}
	public String getCustNo() {
		return custNo;
	}
	public void setCustNo(String custNo) {
		this.custNo = custNo;
	}
	public String getCustName() {
		return custName;
	}
	public void setCustName(String custName) {
		this.custName = custName;
	}
	public String getAccntType() {
		return accntType;
	}
	public void setAccntType(String accntType) {
		this.accntType = accntType;
	}
	public String getAvailable() {
		return available;
	}
	public void setAvailable(String available) {
		this.available = available;
	}
	public String getAccntRiskTip() {
		return accntRiskTip;
	}
	public void setAccntRiskTip(String accntRiskTip) {
		this.accntRiskTip = accntRiskTip;
	}
	public String getLocalNum() {
		return localNum;
	}
	public void setLocalNum(String localNum) {
		this.localNum = localNum;
	}

    @Override
    public String toString() {
        return "Account{" +
                "custId='" + custId + '\'' +
                ", custNo='" + custNo + '\'' +
                ", custName='" + custName + '\'' +
                ", infoId='" + infoId + '\'' +
                ", infoName='" + infoName + '\'' +
                ", deptId='" + deptId + '\'' +
                ", authSql='" + authSql + '\'' +
                ", id='" + id + '\'' +
                ", accountNum='" + accountNum + '\'' +
                ", accountName='" + accountName + '\'' +
                ", localName='" + localName + '\'' +
                ", localNum='" + localNum + '\'' +
                ", loc='" + loc + '\'' +
                ", sanctionedPatry='" + sanctionedPatry + '\'' +
                ", sanctionedPatryCode='" + sanctionedPatryCode + '\'' +
                ", accntTypeCd='" + accntTypeCd + '\'' +
                ", accntType='" + accntType + '\'' +
                ", subAccntTypeCd='" + subAccntTypeCd + '\'' +
                ", subAccntType='" + subAccntType + '\'' +
                ", buId='" + buId + '\'' +
                ", buName='" + buName + '\'' +
                ", buNum='" + buNum + '\'' +
                ", totalRating='" + totalRating + '\'' +
                ", businessAccntTypeName='" + businessAccntTypeName + '\'' +
                ", mtoName='" + mtoName + '\'' +
                ", mtoNameCode='" + mtoNameCode + '\'' +
                ", prPostnId='" + prPostnId + '\'' +
                ", acctEccId='" + acctEccId + '\'' +
                ", operatorType='" + operatorType + '\'' +
                ", operatorTypeCode='" + operatorTypeCode + '\'' +
                ", accountLevel='" + accountLevel + '\'' +
                ", available='" + available + '\'' +
                ", accntRiskTip='" + accntRiskTip + '\'' +
                ", orgName='" + orgName + '\'' +
                ", empId='" + empId + '\'' +
                ", custRange='" + custRange + '\'' +
                ", custRangeCode='" + custRangeCode + '\'' +
                '}';
    }
}
