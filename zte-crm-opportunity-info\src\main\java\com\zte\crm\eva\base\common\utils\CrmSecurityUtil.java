package com.zte.crm.eva.base.common.utils;

import com.zte.crm.eva.base.infrastructure.adapter.impl.IchannelBaseAdapterImpl;
import com.zte.itp.security.ZteSecurity;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;


/**
 * 加解密类
 * <AUTHOR>
 * @date 2023-04-18
 */
public class CrmSecurityUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(IchannelBaseAdapterImpl.class);

    private static final String GCM256ALGORITHM = "AES/GCM/PKCS5Padding";

    /**
     * 自定义securityKey的AES加密
     * @param content 加密内容
     * @return 加密结果
     */
    public static String encryptUsingCustomerKey(String content, String secretKey) throws Exception {
        if (StringUtils.isBlank(content)) {
            return "";
        }
        try {
            Cipher cipher = Cipher.getInstance("AES/GCM/PKCS5Padding");
            byte[] raw = secretKey.getBytes();
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            cipher.init(1, skeySpec);

            byte[] iv = cipher.getIV();

            byte[] encryptData = cipher.doFinal(content.getBytes());
            byte[] message = new byte[12 + content.getBytes().length + 16];
            System.arraycopy(iv, 0, message, 0, 12);
            System.arraycopy(encryptData, 0, message, 12, encryptData.length);
            return Base64.encodeBase64String(message);
        } catch (Exception e) {
            LOGGER.error("decrypt content:{} using key: {}, error: {}", content, secretKey, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 自定义securityKey的AES解密
     * @param content 解密内容
     * @return 解密结果
     */
    public static  String decryptUsingCustomerKey(String content, String secretKey) throws Exception {
        if (StringUtils.isBlank(content)) {
            return "";
        }
        try {
            byte[] raw = secretKey.getBytes("ASCII");
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            byte[] message = Base64.decodeBase64(content);
            GCMParameterSpec params = new GCMParameterSpec(128, message, 0, 12);
            Cipher cipher = Cipher.getInstance("AES/GCM/PKCS5Padding");
            cipher.init(2, skeySpec,params);

            byte[] decryptData = cipher.doFinal(message, 12, message.length - 12);
            return new String(decryptData,"utf-8");
        } catch (Exception e) {
            LOGGER.error("decrypt content:{} using key: {}, error: {}", content, secretKey, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 渠道自定义加密，对zte解密结果加密后传递给前端
     *
     * @param plaintext 明文
     * @return 密文
     */
    public static String ichannelEncrypt(String plaintext, String key) {
        if (StringUtils.isBlank(plaintext)) {
            return StringUtils.EMPTY;
        }
        try {
            SecureRandom secureRandom = new SecureRandom();
            byte[] iv = new byte[12];
            secureRandom.nextBytes(iv);
            Cipher cipher = Cipher.getInstance(GCM256ALGORITHM);
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(128, iv);
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, gcmParameterSpec);
            byte[] encrypted = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
            byte[] ivAndEncrypted = new byte[iv.length + encrypted.length];
            System.arraycopy(iv, 0, ivAndEncrypted, 0, iv.length);
            System.arraycopy(encrypted, 0, ivAndEncrypted, iv.length, encrypted.length);
            return ZteSecurity.replaceBlank((new BASE64Encoder()).encode(ivAndEncrypted));
        } catch (Exception e) {
            LOGGER.info("ichannelEncrypt encrypt failed:", e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * 渠道自定义解密，对前端传入结果解密后使用zte加密
     *
     * @param ciphertext 解密内容
     * @return 解密结果
     */
    public static String ichannelDecrypt(String ciphertext, String key) {
        if (StringUtils.isBlank(ciphertext)) {
            return StringUtils.EMPTY;
        }
        try {
            byte[] ivAndEncrypted = (new BASE64Decoder()).decodeBuffer(ZteSecurity.replaceBlank(ciphertext));
            byte[] iv = new byte[12];
            byte[] encrypted = new byte[ivAndEncrypted.length - 12];
            System.arraycopy(ivAndEncrypted, 0, iv, 0, 12);
            System.arraycopy(ivAndEncrypted, 12, encrypted, 0, ivAndEncrypted.length - 12);
            Cipher cipher = Cipher.getInstance(GCM256ALGORITHM);
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(128, iv);
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, gcmParameterSpec);
            byte[] decrypted = cipher.doFinal(encrypted);
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            LOGGER.info("ichannelDecrypt decrypt failed:", e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * 数据使用 zte 解密，然后 ichannel 加密后传前端
     *
     * @param zteCiphertext
     * @param key
     * @return
     */
    public static String zteDecryptAndIchannelEncrypt(String zteCiphertext, String key) {
        if (StringUtils.isBlank(zteCiphertext)) {
            return StringUtils.EMPTY;
        }
        try {
            return CrmSecurityUtil.ichannelEncrypt(ZteSecurity.getInstance().decrypt(zteCiphertext), key);
        } catch (Exception e) {
            LOGGER.info("zteDecryptAndIchannelEncrypt failed:", e);
        }
        return StringUtils.EMPTY;
    }
}
