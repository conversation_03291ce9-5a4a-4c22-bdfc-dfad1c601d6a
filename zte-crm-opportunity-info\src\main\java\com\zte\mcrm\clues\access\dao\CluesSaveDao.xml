<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.mcrm.clues.access.dao.CluesSaveDao">


	<insert id="saveInfoInMainTable" parameterType="com.zte.mcrm.clues.access.vo.BusinessClues">
	insert into S_LEAD 
	(
		row_id,
		STATUS_CD,
		Lead_Num,
		BU_ID,
		org_code_path,
		accnt_id,
		CREATED_BY,
		LAST_UPD_BY,
		CREATED,
		LAST_UPD,
		DATA_SOURCE,
		VISIT_ID
	)
	 values 
	 (
	 	#{id},
	 	#{statusCode},
	 	#{clueNum},
	 	#{deptId},
	 	#{orgCodePath,jdbcType=VARCHAR},
	 	#{acctId},
	 	#{empId},
	 	#{empId},
	 	SY<PERSON>AT<PERSON>(),
	 	SY<PERSON><PERSON><PERSON>(),
	 	#{datasource},
	 	#{visitId}
	 )
	</insert>
	
	<insert id="saveInfoInExTable" parameterType="com.zte.mcrm.clues.access.vo.BusinessClues">
	INSERT INTO CX_LEAD_X 
    (
    	row_id
    	,LEAD_NAME
    	,BUS_TYPE 
        ,Lead_Type
        ,Lead_Source
        ,Currency_Id
        ,CREATED_BY
        ,LAST_UPD_BY
        ,CREATED
        ,LAST_UPD
        ,DATA_SOURCE
        ,PAR_ROW_ID
        <if test="ownerMgr!=null">,OWNER_ID</if>
        <if test="null!=investmentScaleOfAcct">,Lead_Amount</if>
        <if test="null!=predictSignAmt">,CONTRACT_PRICE</if>
        <if test="null!=predictSignDate">,DATE_2</if>
        <if test="null!=saleModelCode">,SALES_TYPE</if>
        <if test="null!=marketTypeCode">,MARKET_TYPE</if>
        <if test="null!=mulDivisionFlg">,MUL_DIVISION_FLG</if>
        <if test="null!=background">,NOTES_2</if>
        <if test="null!=techMgrId">,tech_mgr_id</if>
		<if test="null!=prodSystemId">,PROD_SYSTEM</if>
		<if test="null!=bigProdcutLineId">,PROD_LINE_BIG</if>
		<if test="null!=prodSystem">,PROD_SYSTEM_NAME</if>
		<if test="null!=bigProductLine">,PROD_LINE_BIG_NAME</if>
        <if test="null!=foundFlgCode">,FUND_FLG</if>
        <if test="null!=parentTradeCode">,PARENT_TRADE</if>
        <if test="null!=childTradeCode">,CHILD_TRADE</if>
        <if test="null!=lastAcctId">,LAST_ACC_ID</if>
     )
     values 
     (
     	#{id}
     	,#{clueName}
     	,#{businessTypeCode}
        ,#{acctTypeCode}
        ,#{clueSourceCode}
        ,#{currency}
        ,#{empId}
        ,#{empId}
        ,SYSDATE()
        ,SYSDATE()
        ,#{datasource}
        ,#{id}
         <if test="ownerMgr!=null">,#{ownerMgr}</if>
         <if test="null!=investmentScaleOfAcct">,#{investmentScaleOfAcct}</if>
         <if test="null!=predictSignAmt">,#{predictSignAmt}</if>
         <if test="null!=predictSignDate">,#{predictSignDate}</if>
         <if test="null!=saleModelCode">,#{saleModelCode}</if>
         <if test="null!=marketTypeCode">,#{marketTypeCode}</if>
         <if test="null!=mulDivisionFlg">,#{mulDivisionFlg}</if>
         <if test="null!=background">,#{background}</if>
         <if test="null!=techMgrId">,#{techMgrId}</if>
         <if test="null!=prodSystemId">,#{prodSystemId}</if>
         <if test="null!=bigProdcutLineId">,#{bigProdcutLineId}</if>
		 <if test="null!=prodSystem">,#{prodSystem}</if>
		 <if test="null!=bigProductLine">,#{bigProductLine}</if>
         <if test="null!=foundFlgCode">,#{foundFlgCode}</if>
         <if test="null!=parentTradeCode">,#{parentTradeCode}</if>
         <if test="null!=childTradeCode">,#{childTradeCode}</if>
         <if test="null!=lastAcctId">,#{lastAcctId}</if>
         )
	</insert>
	<!-- 更新线索主表 -->
	<update id="updateInfoInMainTable" parameterType="com.zte.mcrm.clues.access.vo.BusinessClues" >
    	update S_LEAD
	    <set >
	      <if test="deptId != null and deptId != ''" >
	        BU_ID = #{deptId},
	      </if>
	      <if test="orgCodePath != null and orgCodePath != ''" >
              org_code_path = #{orgCodePath,jdbcType=VARCHAR},
	      </if>
	      <if test="clueNum != null and clueNum != ''" >
	        LEAD_NUM = #{clueNum},
	      </if>
	      <if test="statusCode != null and statusCode != ''" >
	        STATUS_CD = #{statusCode},
	      </if>
	      <if test="acctId != null and acctId != ''" >
	        ACCNT_ID = #{acctId},
	      </if>
	      <if test="empId != null and empId != ''" >
	        LAST_UPD_BY = #{empId},
	      </if>
	      LAST_UPD = SYSDATE()
	    </set>
	    where ROW_ID = #{id}
	    <if test="clueNum != null and clueNum != ''" >
	        or LEAD_NUM = #{clueNum}
	    </if>
  </update>
	<!-- 更新线索拓展表 -->
	<update id="updateInfoInExTable" parameterType="com.zte.mcrm.clues.access.vo.BusinessClues" >
    	update CX_LEAD_X
	    <set >
	       SALES_TYPE = #{saleModelCode},
	       POTENTIAL_MODEL = #{potentialModelCode},
	       ACCOUNT_ATTRIBUTE = #{accountAttributeCode},
	       PARENT_TRADE = #{parentTradeCode},
	       CHILD_TRADE = #{childTradeCode},
	       last_acc_id = #{lastAcctId},
	      <if test="clueName != null and clueName != ''" >
	        LEAD_NAME = #{clueName},
	      </if>
	      <if test="businessTypeCode != null and businessTypeCode != ''" >
	        BUS_TYPE = #{businessTypeCode},
	      </if>
	      <if test="acctTypeCode != null and acctTypeCode != ''" >
	        Lead_Type = #{acctTypeCode},
	      </if>
	      <if test="clueSourceCode != null and clueSourceCode != ''" >
	        Lead_Source = #{clueSourceCode},
	      </if>
	      <if test="currency != null and currency != ''" >
	        Currency_Id = #{currency},
	      </if>
	      <if test="empId != null and empId != ''" >
	        LAST_UPD_BY = #{empId},
	      </if>
	        OWNER_ID = #{ownerMgr},
	      <if test="investmentScaleOfAcct != null and investmentScaleOfAcct != ''" >
	        Lead_Amount = #{investmentScaleOfAcct},
	      </if>
	      <if test="predictSignAmt != null and predictSignAmt != ''" >
	        CONTRACT_PRICE = #{predictSignAmt},
	      </if>
	      <if test="predictSignDate != null" >
	        DATE_2 = #{predictSignDate},
	      </if>
	      <if test="marketTypeCode != null and marketTypeCode != ''" >
	        MARKET_TYPE = #{marketTypeCode},
	      </if>
	      <if test="mulDivisionFlg != null and mulDivisionFlg != ''" >
	        MUL_DIVISION_FLG = #{mulDivisionFlg},
	      </if>
	      <if test="background != null and background != ''" >
	        NOTES_2 = #{background},
	      </if>
	      <if test="techMgrId != null and techMgrId != ''" >
	        tech_mgr_id = #{techMgrId},
	      </if>
	        CLOSER_ID = #{backPerson},
	        REFUSE_REASON = #{backReasonCode},
	      <if test="prodSystemId != null and prodSystemId != ''" >
	        PROD_SYSTEM = #{prodSystemId},
	      </if>
		  <if test="prodSystem != null and prodSystem != ''" >
			  PROD_SYSTEM_NAME = #{prodSystem},
		  </if>
		  <if test="bigProductLine != null and bigProductLine != ''" >
			  PROD_LINE_BIG_NAME = #{bigProductLine},
		  </if>
		  <if test="bigProdcutLineId != null and bigProdcutLineId != ''" >
			PROD_LINE_BIG = #{bigProdcutLineId},
		  </if>
	      <if test="reasonCode != null and reasonCode != ''" >
	        NOTES_4 = #{reasonCode},
	      </if>
	      <if test="foundFlgCode != null and foundFlgCode != ''" >
	        FUND_FLG = #{foundFlgCode},
	      </if>
	      LAST_UPD = SYSDATE(),
	      last_upd_by = #{empId}
	    </set>
	    where ROW_ID = #{id}
  </update>
  <!-- 更新金额 -->
  <update id="updateAmountInExTable" parameterType="com.zte.mcrm.clues.access.vo.BusinessClues" >
    	update CX_LEAD_X
	    <set>
	        Lead_Amount = #{investmentScaleOfAcct},
	        CONTRACT_PRICE = #{predictSignAmt}
	    </set>
	    where ROW_ID = #{id}
  </update>
</mapper>