package com.zte.mcrm.channel.controller.channel;

import com.zte.itp.authorityclient.entity.output.RoleVO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.mcrm.channel.service.channel.IOpportunityInfoService;
import com.zte.mcrm.channel.service.channel.IOpportunityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Api(tags = "鉴权相关API")
@RestController
@RequestMapping("/channel/auth")
public class ChannelOpportunityAuthController {
    /** 日志对象 */
    private static final Logger log = LoggerFactory.getLogger(ChannelOpportunityAuthController.class);

    @Autowired
    IOpportunityInfoService opportunityInfoService;

    @Autowired
    IOpportunityService opportunityService;

    @ApiOperation("iChannel侧，查询我的角色Map")
    @GetMapping(value = "/roleMap")
    public ServiceData<Map<String, RoleVO>> getMyRoleMap(@RequestHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO) String empNo){
        ServiceData<Map<String, RoleVO>> sd = new ServiceData<>();
        sd.setBo(opportunityInfoService.getChannelRoleMap(empNo));
        return sd;
    }

    @ApiOperation("iChannel侧，校验详情查看权限")
    @GetMapping(value = "/checkDetailsViewPermissions")
    public ServiceData<Boolean> checkDetailsViewPermissions(@RequestHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO) String empNo,
                                                            @RequestParam(name = "rowId") @ApiParam(value = "主键id", required = true) String rowId){
        ServiceData<Boolean> sd = new ServiceData<>();
        sd.setBo(opportunityService.checkDetailReviewPermission(rowId));
        return sd;
    }
}
