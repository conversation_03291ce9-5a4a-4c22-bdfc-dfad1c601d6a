package com.zte.mcrm.channel.controller.channel;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import com.zte.mcrm.adapter.approval.model.ApprovalResponseBO;
import com.zte.mcrm.adapter.clouddisk.util.ServiceDataUtil;
import com.zte.mcrm.channel.cron.MonthlyOpportunityInvalidTask;
import com.zte.mcrm.channel.cron.MonthlyReportUpdateReminderTask;
import com.zte.mcrm.channel.cron.StartFlowTask;
import com.zte.mcrm.channel.model.entity.OpportunityKeyInfoEntity;
import com.zte.mcrm.channel.service.channel.IOpportunityMigrationService;
import com.zte.mcrm.common.annotation.SystemAuthVerify;
import com.zte.mcrm.common.util.AccountUtil;
import com.zte.springbootframe.common.exception.BusiException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.http.entity.mime.content.ByteArrayBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;


@Api(tags = "商机数据迁移接口")
@RestController
@RequestMapping("/migration")
public class OpporyinutyMigrationController {
    /** 日志对象 */
    @Autowired
    IOpportunityMigrationService opporyinutyMigrationService;

    @Autowired
    MonthlyReportUpdateReminderTask monthlyReportUpdateReminderTask;

    @Autowired
    MonthlyOpportunityInvalidTask monthlyOpportunityInvalidTask;

    @Autowired
    StartFlowTask startFlowTask;

    @ApiOperation("商机迁移-失效特定单据")
    @PostMapping(value = "/invalid")
    @SystemAuthVerify(serviceNamesWhitelist = {"zte-crm"})
    public ServiceData<String> invalid(@RequestBody List<String> rowIds){
        return ServiceDataUtil.success(opporyinutyMigrationService.invalid(rowIds));
    }

    @ApiOperation(value = "刷新异常的渠道商客户编码", notes = "入参type只能使用以下两个：\n" +
            "1. new: 针对新商机单据；\n" +
            "2. old: 针对历史遗留的老商机单据。\n" +
            "\n如果入参不为以上两个则只默认查询新商机中异常渠道商客户编码的单据数目，不做处理\n" +
            "\n出参为处理中发生异常的商机列表及异常信息。")
    @GetMapping("/refreshCrmCustomerCode")
    public ServiceData<List<String>> updateCrmCustomerCode(String type) throws BusiException {
        return ServiceResultUtil.success(opporyinutyMigrationService.refreshCrmCustomerCode(type));
    }

    @GetMapping("/updateLastAccNameByOldOpp")
    public ServiceData<Integer> updateLastAccNameByOldOpp() throws BusiException {
        return ServiceResultUtil.success(opporyinutyMigrationService.updateLastAccNameByOldOpp());
    }

    @ApiOperation(value = "手动触发月报提醒发送任务一次")
    @GetMapping("/monthlyReportUpdateReminderTask")
    public ServiceData monthlyReportUpdateReminderTask() throws BusiException {
        monthlyReportUpdateReminderTask.monthReportReminder();
        return new ServiceData();
    }

    @ApiOperation(value = "手动触发刷新客户状态并启动流程的任务一次")
    @GetMapping("/refreshCustomerAndStartFlowTask")
    public ServiceData startFlowTaskTask() throws BusiException {
        startFlowTask.updateLastAccStatusBatch();
        return new ServiceData();
    }

    @ApiOperation(value = "手动触发月报未更新商机失效任务一次")
    @GetMapping("/monthlyOpportunityInvalidTask")
    public ServiceData monthlyOpportunityInvalidTask() throws BusiException {
        monthlyOpportunityInvalidTask.monthlyOpportunityInvalidAndReminder();
        return new ServiceData();
    }

    @ApiOperation(value = "更新评审相关时间到数据库", notes = "入参type只能使用以下两个：\n" +
            "1. successDate: 更新流程结束时间；\n" +
            "2. submitDate: 更新流程提交时间，以审批节点启动的时间为准。\n" +
            "\n 入参limit为每次刷新的单据数，通常大于0，如果为0则更新所有符合条件的单据\n" +
            "\n出参第一行为符合条件的所有商机及流程实例编码，后面的为异常商机的列表。")
    @GetMapping("/updateApproveDate")
    public ServiceData<List<String>> updateApproveDate(@RequestParam(name = "type") String type,@RequestParam(name = "limit") Integer limit) throws BusiException {
        return ServiceResultUtil.success(opporyinutyMigrationService.updateApproveDate(type, limit));
    }

    @GetMapping("/updateBusinessManager")
    public ServiceData<String> updateBusinessManager() throws BusiException {
        return ServiceResultUtil.success(opporyinutyMigrationService.updateBusinessManager());
    }

    @GetMapping("/adaptToTheNewProcess")
    public ServiceData<List<OpportunityKeyInfoEntity>> adaptToTheNewProcess() throws Exception {
        return ServiceResultUtil.success(opporyinutyMigrationService.adaptToTheNewProcess());
    }

    @PostMapping("/createCustomerAndStartFlow")
    public ServiceData<OpportunityKeyInfoEntity> createCustomerAndStartFlow(@RequestBody OpportunityKeyInfoEntity entity) throws Exception {
        opporyinutyMigrationService.createCustomerAndStartFlow(entity);
        return ServiceResultUtil.success(entity);
    }

    @PostMapping(value = "/sendApprovalCenterExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ServiceData<ApprovalResponseBO> sendApprovalCenterExcel(@ApiParam @RequestPart MultipartFile file,
                                                     @ApiParam("类型：ins_flow/ins_task/ins_reassign") @RequestParam String type) throws Exception {
        return opporyinutyMigrationService.sendApprovalCenterExcel(file, type);
    }

    @GetMapping(value = "/getFailLogAndRollBack")
    public void getFailLogAndRollBack(HttpServletResponse httpServletResponse,
                                      @ApiParam @RequestParam String serialNumber,
                                      @ApiParam @RequestParam(required = false) String type)throws IOException {
        ByteArrayBody resultBody = opporyinutyMigrationService.getFailLogAndRollBack(serialNumber, type);

        ServletOutputStream responseOutputStream = httpServletResponse.getOutputStream();
        if (null != resultBody) {
            httpServletResponse.setCharacterEncoding("UTF-8");
            httpServletResponse.setHeader("Content-type", "application/octet-stream");
            httpServletResponse.setHeader("Content-Disposition", "attachment;filename=" + resultBody.getFilename());
            httpServletResponse.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            resultBody.writeTo(responseOutputStream);
        }
        responseOutputStream.flush();
        responseOutputStream.close();
    }

}
