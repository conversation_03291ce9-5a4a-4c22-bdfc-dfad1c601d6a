package com.zte.mcrm.common.util;

import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.approval.model.dto.ApprovalRecordsDTO;
import com.zte.mcrm.adapter.approval.model.dto.ApprovedTask;
import com.zte.mcrm.common.consts.HttpMethodEnum;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

public class ApprovedTaskUtils {

    public static void order( List<ApprovalRecordsDTO> records){

        if (CollectionUtils.isEmpty(records)) {
            return ;
        }
        List<ApprovedTask> newTasks = Lists.newArrayList();
        String active = "ACTIVE";
        // 排序
        for (ApprovedTask task : records.get(0).getApprovalTaskRecordList()) {
            if(active.equals(task.getTaskStatus())){
                newTasks.add(0,task);
            }else{
                newTasks.add(task);
            }
        }
        records.get(0).setApprovalTaskRecordList(newTasks);

    }
}
