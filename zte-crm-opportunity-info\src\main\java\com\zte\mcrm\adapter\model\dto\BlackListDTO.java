package com.zte.mcrm.adapter.model.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class BlackListDTO {

    /** 黑名单ID*/
    private String id;

    /** 企业名称 */
    private String orgName;

    /** 黑名单层级 详见黑名单相关快码Blacklist-Related Quick Code */
    private String level;

    /** 黑名单类型 详见黑名单相关快码Blacklist-Related Quick Code */
    private String listType;

    /** 处理原因分类(三商入股，黑名单关联公司、假单、合规原因、超期未缴纳罚款、互联网违规销售、非我司经销商违规、贿赂、其他) */
    private String reasonCategory;

    /** 处理原因 */
    private String reason;

    /** 被调查日期（时间戳） */
    private Long investigationDate;

    /**  开始限制日期	 */
    private Long startDate;

    /** 结束限制日期 */
    private Long endDate;

    /** 国家/地区 */
    private String location;

    /** 被限制领域 */
    private String forbiddenArea;

    /** 违规编号 */
    private String violationBillNo;

    /** 状态 */
    private String status;
}
