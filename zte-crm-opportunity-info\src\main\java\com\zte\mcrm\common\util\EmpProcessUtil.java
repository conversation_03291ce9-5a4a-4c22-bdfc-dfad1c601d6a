package com.zte.mcrm.common.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.mcrm.common.annotation.EmpName;
import org.apache.commons.lang3.StringUtils;
import org.glassfish.jersey.internal.guava.Sets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Auther: 10244932
 * @Date: 2021/6/11 15:15
 * @Description:
 */
public  class EmpProcessUtil{


    private static List<String> DEFUALT_FIELD_NAME = Lists.newArrayList("lastUpdatedBy",  "createdBy");


    public static <T> List<T>  replaceEmpNo(List<T> objects) throws Exception {
        EmpProcess<T> empProcess = new EmpProcess<T>(objects);
        empProcess.replaceValue();
        return objects;
    }


    public static <T> List<T>  replaceEmpNoWithName(List<T> objects) throws Exception {
        EmpProcess<T> empProcess = new EmpProcess<T>(objects){
            @Override
            public Object getReplaceValue(T obj, Object value) {
                Object replaceValue = super.getReplaceValue(obj, value);
                if ( replaceValue instanceof String && value instanceof String) {
                    replaceValue = ((String) replaceValue).replace( (String) value, "");
                }
                return replaceValue;
            }
        };
        empProcess.replaceValue();
        return objects;
    }


    public static class EmpProcess<T> extends ObjectProcess<T> {

        private static final Logger logger = LoggerFactory.getLogger(EmpProcess.class);

        private Map<String,String> empInfo = Maps.newHashMap();

        public EmpProcess(List<T> objects) throws Exception {
            super(objects, EmpName.class);
        }

        @Override
        public List<Field> additionalFields(Class clazz) throws Exception {
            Field[] declaredFields = clazz.getDeclaredFields();
            List<Field> fields = Lists.newArrayList();
            for (Field field : declaredFields) {
                if (DEFUALT_FIELD_NAME.contains(field.getName())) {
                    fields.add(field);
                }
            }
            return fields;
        }

        @Override
        public Object getReplaceValue(T obj, Object value) {
            if (value instanceof String && empInfo.get(value)!=null) {
                return (empInfo.get(value));
            }
            return value;
        }

        @Override
        public void preReplace() throws Exception {
            Set<String> empNos = Sets.newHashSet();
            List<T> objects = getValues();
            List<Field> fields = getFields();
            String fieldValue = null;
            for (T obj : objects) {
                for (Field field : fields) {
                    if (field.getType() == String.class && obj!=null
                            && StringUtils.isNotEmpty(fieldValue=(String)field.get(obj))) {
                        empNos.add(fieldValue);
                    }
                }
            }
            // 微服务查询人事接口
            try {
                empInfo.putAll(EmpInfoUtils.getEmpNameMap(Lists.newArrayList(empNos)));
            } catch (Exception e) {
                logger.info("call /ucs/account/queryall  fail:{}",e.getMessage());
            }

        }






    }
}
