<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.leadinfo.leadinfo.mapper.CxOpptyProdMapper">

    <resultMap id="BaseResultMap" type="com.zte.leadinfo.leadinfo.entity.CxOpptyProdDO">
        <id property="rowId" column="ROW_ID" jdbcType="VARCHAR"/>
        <result property="createdBy" column="CREATED_BY" jdbcType="VARCHAR"/>
        <result property="lastUpdBy" column="LAST_UPD_BY" jdbcType="VARCHAR"/>
        <result property="created" column="CREATED" jdbcType="TIMESTAMP"/>
        <result property="lastUpd" column="LAST_UPD" jdbcType="TIMESTAMP"/>
        <result property="prodLv1Id" column="PROD_LV1_ID" jdbcType="VARCHAR"/>
        <result property="prodLv1Name" column="PROD_LV1_NAME" jdbcType="VARCHAR"/>
        <result property="prodLv2Id" column="PROD_LV2_ID" jdbcType="VARCHAR"/>
        <result property="prodLv2Name" column="PROD_LV2_NAME" jdbcType="VARCHAR"/>
        <result property="prodLv21Id" column="PROD_LV2_1_ID" jdbcType="VARCHAR"/>
        <result property="prodLv21Name" column="PROD_LV2_1_NAME" jdbcType="VARCHAR"/>
        <result property="prodLv3Id" column="PROD_LV3_ID" jdbcType="VARCHAR"/>
        <result property="prodLv3Name" column="PROD_LV3_NAME" jdbcType="VARCHAR"/>
        <result property="prodLv4Id" column="PROD_LV4_ID" jdbcType="VARCHAR"/>
        <result property="prodLv4Name" column="PROD_LV4_NAME" jdbcType="VARCHAR"/>
        <result property="opptyId" column="OPPTY_ID" jdbcType="VARCHAR"/>
        <result property="zteMainProduct" column="ZTE_MAIN_PRODUCT" jdbcType="CHAR"/>
        <result property="activeFlg" column="ACTIVE_FLG" jdbcType="VARCHAR"/>
        <result property="productAmount" column="PRODUCT_AMOUNT" jdbcType="DECIMAL"/>
        <result property="forSignDate" column="FOR_SIGN_DATE" jdbcType="DATE"/>
        <result property="dataSource" column="DATA_SOURCE" jdbcType="VARCHAR"/>
        <result property="businessType" column="business_type" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ROW_ID,CREATED_BY,LAST_UPD_BY,
        CREATED,LAST_UPD,PROD_LV1_ID,
        PROD_LV1_NAME,PROD_LV2_ID,PROD_LV2_NAME,
        PROD_LV2_1_ID,PROD_LV2_1_NAME,PROD_LV3_ID,
        PROD_LV3_NAME,PROD_LV4_ID,PROD_LV4_NAME,
        OPPTY_ID,ZTE_MAIN_PRODUCT,ACTIVE_FLG,
        PRODUCT_AMOUNT,FOR_SIGN_DATE,DATA_SOURCE,
        business_type
    </sql>

</mapper>