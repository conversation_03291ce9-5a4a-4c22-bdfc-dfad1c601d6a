package com.zte.mcrm.common.business.service;

import com.zte.mcrm.common.access.vo.LockVO;

import java.util.List;

/****
 *
 * <AUTHOR> @date 2021/2/10
 **/
public interface LockService {
	/**
	 * 加锁
	 * @param lock
	 */
	void addLock(Lock<PERSON> lock);
	/**
	 * 释放锁
	 * @param methodName
	 */
	void deleteLock(String methodName);

	/**
		* 获取审批人
		* @param approveId
		* @return
		*/
	List<String> getApprovedBy(String approveId);
}
