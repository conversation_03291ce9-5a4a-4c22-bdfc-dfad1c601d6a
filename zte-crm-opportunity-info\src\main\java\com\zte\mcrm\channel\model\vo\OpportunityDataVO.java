package com.zte.mcrm.channel.model.vo;

import lombok.Data;

/**
 * 描述：
 * 创建时间：2021/9/22
 *
 * @author：王丹凤6396000572
 */
@Data
public class OpportunityDataVO {

    /**
     * 商机编号
     */
    private String optyCd;
    /**
     * 商机名称
     */
    private String optyName;
    /**
     * 渠道商名称
     */
    private String channelName;
    /**
     * 渠道商客户编码
     */
    private String crmCustomerCode;
    /**
     * 商机状态编码
     */
    private String statusCd;
    /**
     * 商机状态名称
     */
    private String statusCdName;
    /**
     * 项目所属部门编号
     */
    private String deptNo;
    /**
     * 项目所属部门名称
     */
    private String deptName;
    /**
     * 商机概况
     */
    private String projectDesc;
    /**
     * 预计签单金额（元）
     */
    private String expectSignMoney;
    /**
     * 最终用户编号
     */
    private String finalCustomerId;

    /**
     * 最终用户名称
     */
    private String finalCustomerName;
    /**
     * 最终用户行业
     */
    private String finalCustomerParentTradeName;
    /**
     * 最终用户行业id
     */
    private String finalCustomerParentTrade;
    /**
     * 产品范围:产品名称，多个之间以，隔开
     */
    private String prodName;
    /**
     * 报备时间
     */
    private String createdTime;
    /**
     * 商机来源
     */
    private String dataSourceName;
    /**
     * 商机id
     */
    private String rowId;
    /**
     * 最终用户子行业编码
     */
    private String finalCustomerChildTrade;
    /**
     * 最终用户子行业名称
     */
    private String finalCustomerChildTradeName;

    /**
     * 老销售机会渠道商客户id
     */
    private String oldOppCustomerId;

}
