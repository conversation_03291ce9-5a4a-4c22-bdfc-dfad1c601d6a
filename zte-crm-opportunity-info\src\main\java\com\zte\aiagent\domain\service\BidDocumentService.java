package com.zte.aiagent.domain.service;

import com.zte.aiagent.app.command.CreateDocumentCommand;
import com.zte.aiagent.ui.dto.request.BidDocumentPageQueryDTO;
import com.zte.aiagent.ui.dto.vo.BidDocumentVO;
import com.zte.aiagent.ui.dto.vo.BidParseRecordVO;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;

import java.util.List;

public interface BidDocumentService {

    /**
     * 上传文档用例
     * 业务流程：文件上传 -> 创建聚合 -> 发布事件
     *
     * @param command 创建文档命令
     * @return 文档ID
     */
    String uploadDocument(CreateDocumentCommand command);

    /**
     * 按格式导出解析文档
     * @param documentId 文件ID
     * @param format 导出文件格式
     * @return 下载链接
     */
    String exportParsedFile(String documentId, String format);

    /**
     * 根据文档ID查询解析记录列表
     * @param documentId 文档ID
     * @return 解析记录列表
     * @throws Exception 业务异常
     */
    List<BidParseRecordVO> queryParseRecords(String documentId) throws Exception;

    /**
     * 分页查询招标文件列表(增强版 - 包含解析进度和解析记录)
     * @param request 分页查询请求
     * @return 分页查询结果
     * @throws Exception 业务异常
     */
    PageRows<BidDocumentVO> queryPageWithProgress(FormData<BidDocumentPageQueryDTO> request) throws Exception;
}
