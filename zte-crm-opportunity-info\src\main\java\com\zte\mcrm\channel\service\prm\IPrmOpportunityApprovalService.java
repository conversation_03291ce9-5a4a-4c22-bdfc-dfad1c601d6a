package com.zte.mcrm.channel.service.prm;

import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.adapter.approval.model.dto.ApprovedTask;
import com.zte.mcrm.adapter.approval.model.dto.ReassignDTO;
import com.zte.mcrm.adapter.authorization.model.RoleInfo;
import com.zte.mcrm.channel.model.dto.ApprovalCallBackMsgInfo;
import com.zte.mcrm.channel.model.dto.AuthorizationRoleInfoDTO;
import com.zte.mcrm.channel.model.dto.OpportunityOpinionDTO;
import com.zte.mcrm.channel.model.dto.SimilarOpportunityQueryDTO;
import com.zte.mcrm.channel.model.entity.*;
import com.zte.mcrm.channel.model.vo.PrmLastUserStatusVO;
import com.zte.mcrm.channel.model.vo.RestrictedPartyVO;
import com.zte.springbootframe.common.exception.BusiException;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * prm商机审批页面相关接口 服务
 * <AUTHOR>
 * @date 2021/9/30
 */
public interface IPrmOpportunityApprovalService {

    /**
     * 查询类似商机
     * <AUTHOR>
     * @date 2021/9/30
     */
    PageRows<SimilarOpportunity> getSimilarOpportunity(FormData<SimilarOpportunityQueryDTO> formParam) throws Exception;

    /**
     * 查询类似商机
     * <AUTHOR>
     * @date 2021/9/30
     */
    PageRows<SimilarOpportunity> getSimilarOpportunityApprovaling(FormData<SimilarOpportunityQueryDTO> formParam) throws Exception;


    /**
     * 根据 row-id 查询类似商机
     * @param param
     * @return
     */
    List<SimilarOpportunity> querySimilarOpportunityByRowId(SimilarOpportunityQueryDTO param);

    /**
     * 依据角色、组织、子行业从权限平台找人
     * @param authorizationRoleInfoDTO
     * @return
     */
    List<RoleInfo> queryRoleInfoWithConstraint(AuthorizationRoleInfoDTO authorizationRoleInfoDTO);

    /**
     * 修改中兴业务经理
     * @param businessManager
     * @return
     */
    Integer modifyBusinessManager(BusinessManagerBO businessManager);


    boolean reassignApproval(ReassignDTO reassignDTO);

    /**
     * 节点审批
     * @param opinion
     * @return
     */
    String approve(OpportunityOpinionDTO opinion) throws Exception;
    /**
     * 仲裁审批
     * @param opinion
     * @return
     * @throws Exception
     */
    String arbitrationApprove(OpportunityOpinionDTO opinion) throws Exception;
    /**
     * 审批中心评审校验回调接口
     * @param approvalVerificationCallback
     * @return
     */
    ApprovalVerificationResult approveVerificationCallBack(ApprovalVerificationCallback approvalVerificationCallback) throws Exception;

    /**
     * 获取仲裁审批人
     * @param callBackMsgInfo
     * @return
     */
    String getArbitrationApprover(ApprovalCallBackMsgInfo callBackMsgInfo);

    /**
     * 异步方式获取商机审批人
     * @param opportunityDetail
     * @param roleCode
     * @return
     */
    CompletableFuture<List<RoleInfo>> getApprovalPersons(OpportunityDetail opportunityDetail, String roleCode);

    /**
     * 更新流程参数
     * @param flowInstanceId
     * @param approvalStartParamsBO
     * @param affixParams
     * @throws com.zte.springbootframe.common.exception.BusiException
     */
    String modifyApprovalCenterProcessVariables(String flowInstanceId,
                                              ApprovalStartParamsBO approvalStartParamsBO,
                                              Map<String, Object> affixParams)
            throws com.zte.springbootframe.common.exception.BusiException;


    List<OpptyStatus> queryOpptyProgress(String rowId) throws Exception;

    /**
     * 最终用户状态查询
      * @param rowId
     */
    PrmLastUserStatusVO queryLastUserStatus(String rowId) throws BusiException;

    List<ApprovedTask> queryApprovedTasks(String rowId) throws Exception;
    public List<ApprovedTask> getApprovedTasks(String workFlowInstanceId) throws Exception;

    List<ApprovalNode> queryApprovalNodes(String rowId) throws Exception;

    List<ApprovalDetail> queryApprovalDetail(String rowId) throws Exception;

    /**
     * 校验是否受限制主体
     * @param deptNo
     * @param lastAccId
     * @param orgUnifiedCode
     */
    Boolean checkRestrictedParty(String deptNo,String lastAccId, String orgUnifiedCode) throws Exception;

    RestrictedPartyVO getRestrictedParty(String lastAccId, String crmCustomerCode) throws Exception;

    RestrictedPartyVO getRestrictedPartyV2(String lastAccId, String crmCustomerCode,String organizationId) throws Exception;
}
