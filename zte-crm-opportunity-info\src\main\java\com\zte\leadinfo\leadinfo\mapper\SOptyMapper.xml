<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.leadinfo.leadinfo.mapper.SOptyMapper">

    <resultMap id="BaseResultMap" type="com.zte.leadinfo.leadinfo.entity.SOptyDO">
        <id property="rowId" column="row_id" jdbcType="VARCHAR"/>
        <result property="lastUpd" column="last_upd" jdbcType="TIMESTAMP"/>
        <result property="lastUpdBy" column="last_upd_by" jdbcType="VARCHAR"/>
        <result property="created" column="created" jdbcType="TIMESTAMP"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="optyCd" column="opty_cd" jdbcType="VARCHAR"/>
        <result property="buId" column="BU_ID" jdbcType="VARCHAR"/>
        <result property="oldBuId" column="old_bu_id" jdbcType="VARCHAR"/>
        <result property="orgNamePath" column="org_name_path" jdbcType="VARCHAR"/>
        <result property="statusCd" column="status_cd" jdbcType="VARCHAR"/>
        <result property="prDeptOuId" column="PR_DEPT_OU_ID" jdbcType="VARCHAR"/>
        <result property="businessTypeCd" column="BUSINESS_TYPE_CD" jdbcType="VARCHAR"/>
        <result property="xMktId" column="X_MKT_ID" jdbcType="VARCHAR"/>
        <result property="parOptyId" column="PAR_OPTY_ID" jdbcType="VARCHAR"/>
        <result property="dataSource" column="data_source" jdbcType="VARCHAR"/>
        <result property="name" column="NAME" jdbcType="VARCHAR"/>
        <result property="spaceId" column="SPACE_ID" jdbcType="VARCHAR"/>
        <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
        <result property="enabledFlag" column="enabled_flag" jdbcType="CHAR"/>
        <result property="reportStatus" column="report_status" jdbcType="VARCHAR"/>
        <result property="submitDate" column="submit_date" jdbcType="TIMESTAMP"/>
        <result property="expiryDate" column="expiry_date" jdbcType="TIMESTAMP"/>
        <result property="successDate" column="success_date" jdbcType="TIMESTAMP"/>
        <result property="currentStatus" column="current_status" jdbcType="VARCHAR"/>
        <result property="migrationStatus" column="migration_status" jdbcType="CHAR"/>
        <result property="migrationTime" column="migration_time" jdbcType="TIMESTAMP"/>
        <result property="migrationMessage" column="migration_message" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        row_id,last_upd,last_upd_by,
        created,created_by,opty_cd,
        BU_ID,old_bu_id,org_name_path,
        status_cd,PR_DEPT_OU_ID,BUSINESS_TYPE_CD,
        X_MKT_ID,PAR_OPTY_ID,data_source,
        NAME,SPACE_ID,tenant_id,
        enabled_flag,report_status,submit_date,
        expiry_date,success_date,current_status,
        migration_status,migration_time,migrationMessage
    </sql>

    <select id="selectIdsByConditions" parameterType="com.zte.opty.sync.domain.dto.OptySyncQueryDTO" resultType="java.lang.String">
        select row_id from S_OPTY where 1=1
        <if test="buIdLike != null and buIdLike != ''">
            and bu_id like CONCAT(#{buIdLike},'%')
        </if>
        <if test="orgNamePathLike != null and orgNamePathLike != ''">
            and org_name_path like CONCAT(#{orgNamePathLike},'%')
        </if>
        <if test="buIdIn !=null and buIdIn.size()>0">
            and bu_id in
            <foreach collection="buIdIn" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="statusCdIn !=null and statusCdIn.size()>0">
            and status_cd in
            <foreach collection="statusCdIn" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="migrationStatusIn !=null and migrationStatusIn.size()>0">
            and migration_status in
            <foreach collection="migrationStatusIn" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="optyCdIn !=null and optyCdIn.size()>0">
            and opty_cd in
            <foreach collection="optyCdIn" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dataSourceIn !=null and dataSourceIn.size()>0">
            and data_source in
            <foreach collection="dataSourceIn" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null">
            and created <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and created <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="optyIdIn !=null and optyIdIn.size()>0">
            and row_id in
            <foreach collection="optyIdIn" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="loadUndoOptyIds" resultType="java.lang.String">
        select row_id from s_opty so where so.migration_status = 'N'
        order by so.org_name_path, so.created
        limit ${batchSize}
    </select>
    <select id="selectAll" resultType="com.zte.leadinfo.leadinfo.entity.CustomerInfoDO">
        select so.row_id as id,so.PR_DEPT_OU_ID as prDeptOuId,
        sx.X_LAST_ACC_ID as xLastAccId,
        sx.crm_customer_code as crmCustomerCode
        from s_opty so left join s_opty_x sx on so.row_id = sx.row_id
        where so.data_source in ('prm', 'ichannel')
    </select>
</mapper>