<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.aiagent.infrastruction.access.mapper.SysConfTypeMapper">
    <!-- 基础结果集映射，定义表字段与PO类属性的映射关系 -->
    <resultMap id="BaseResultMap" type="com.zte.aiagent.infrastruction.access.po.SysConfTypePO">
        <id column="row_id" property="rowId" jdbcType="VARCHAR"/>
        <result column="code_type" property="codeType" jdbcType="VARCHAR"/>
        <result column="code_type_cn_name" property="codeTypeCnName" jdbcType="VARCHAR"/>
        <result column="code_type_en_name" property="codeTypeEnName" jdbcType="VARCHAR"/>
        <result column="tree_flag" property="treeFlag" jdbcType="VARCHAR"/>
        <result column="memo" property="memo" jdbcType="VARCHAR"/>
        <result column="last_updated_date" property="lastUpdatedDate" jdbcType="TIMESTAMP"/>
        <result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR"/>
        <result column="created_date" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="enabled_flag" property="enabledFlag" jdbcType="VARCHAR"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 插入系统参数配置类型记录 -->
    <insert id="insert" parameterType="com.zte.aiagent.infrastruction.access.po.SysConfTypePO">
        INSERT INTO sys_conf_type (
            row_id,
            code_type,
            code_type_cn_name,
            code_type_en_name,
            tree_flag,
            memo,
            last_updated_date,
            last_updated_by,
            created_date,
            created_by,
            enabled_flag,
            tenant_id
        ) VALUES (
                     #{rowId,jdbcType=VARCHAR},
                     #{codeType,jdbcType=VARCHAR},
                     #{codeTypeCnName,jdbcType=VARCHAR},
                     #{codeTypeEnName,jdbcType=VARCHAR},
                     #{treeFlag,jdbcType=VARCHAR},
                     #{memo,jdbcType=VARCHAR},
                     #{lastUpdatedDate,jdbcType=TIMESTAMP},
                     #{lastUpdatedBy,jdbcType=VARCHAR},
                     #{createdDate,jdbcType=TIMESTAMP},
                     #{createdBy,jdbcType=VARCHAR},
                     #{enabledFlag,jdbcType=VARCHAR},
                     #{tenantId,jdbcType=BIGINT}
                 )
    </insert>

    <!-- 根据ID查询系统参数配置类型 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
            row_id,
            code_type,
            code_type_cn_name,
            code_type_en_name,
            tree_flag,
            memo,
            last_updated_date,
            last_updated_by,
            created_date,
            created_by,
            enabled_flag,
            tenant_id
        FROM sys_conf_type
        WHERE row_id = #{rowId,jdbcType=VARCHAR}
    </select>

    <!-- 根据编码类型和租户ID查询系统参数配置类型 -->
    <select id="selectByCodeTypeAndTenantId" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
            row_id,
            code_type,
            code_type_cn_name,
            code_type_en_name,
            tree_flag,
            memo,
            last_updated_date,
            last_updated_by,
            created_date,
            created_by,
            enabled_flag,
            tenant_id
        FROM sys_conf_type
        WHERE code_type = #{codeType,jdbcType=VARCHAR}
          AND tenant_id = #{tenantId,jdbcType=BIGINT}
          AND enabled_flag = 'Y'
    </select>

    <!-- 查询所有有效系统参数配置类型 -->
    <select id="selectAllValid" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT
            row_id,
            code_type,
            code_type_cn_name,
            code_type_en_name,
            tree_flag,
            memo,
            last_updated_date,
            last_updated_by,
            created_date,
            created_by,
            enabled_flag,
            tenant_id
        FROM sys_conf_type
        WHERE enabled_flag = 'Y'
          AND tenant_id = #{tenantId,jdbcType=BIGINT}
        ORDER BY code_type ASC
    </select>

    <!-- 根据ID更新系统参数配置类型 -->
    <update id="updateByPrimaryKey" parameterType="com.zte.aiagent.infrastruction.access.po.SysConfTypePO">
        UPDATE sys_conf_type
        SET
            code_type = #{codeType,jdbcType=VARCHAR},
            code_type_cn_name = #{codeTypeCnName,jdbcType=VARCHAR},
            code_type_en_name = #{codeTypeEnName,jdbcType=VARCHAR},
            tree_flag = #{treeFlag,jdbcType=VARCHAR},
            memo = #{memo,jdbcType=VARCHAR},
            last_updated_date = #{lastUpdatedDate,jdbcType=TIMESTAMP},
            last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            created_date = #{createdDate,jdbcType=TIMESTAMP},
            enabled_flag = #{enabledFlag,jdbcType=VARCHAR},
            tenant_id = #{tenantId,jdbcType=BIGINT}
        WHERE row_id = #{rowId,jdbcType=VARCHAR}
    </update>

    <!-- 逻辑删除系统参数配置类型（将enabled_flag设为N） -->
    <update id="logicDelete" parameterType="java.util.Map">
        UPDATE sys_conf_type
        SET
            enabled_flag = 'N',
            last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            last_updated_date = #{lastUpdatedDate,jdbcType=TIMESTAMP}
        WHERE row_id = #{rowId,jdbcType=VARCHAR}
    </update>
</mapper>
