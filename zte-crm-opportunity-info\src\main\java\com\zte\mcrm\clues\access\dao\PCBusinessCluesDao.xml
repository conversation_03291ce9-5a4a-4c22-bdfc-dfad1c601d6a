<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.mcrm.clues.access.dao.PCBusinessCluesDao">


    <sql id="baseSql">
        sl.row_id as "id"
        ,sl.Lead_Num as "clueNum"
        ,sl.STATUS_CD as
        "statusCode"
        ,x.LEAD_NAME as "clueName"
        ,x.BUS_TYPE as
        "businessTypeCode"
        ,sl.BU_ID as "deptId"
        ,x.OWNER_ID as "ownerMgr"
        ,x.Currency_Id as "currency"
        ,x.Lead_Amount as
        "investmentScaleOfAcct"
        ,x.CONTRACT_PRICE as "predictSignAmt"
        ,x.DATE_2 as "predictSignDate"
        ,sl.Accnt_Id as "acctId"
        ,sl.CREATED as "created"
        ,sl.LAST_UPD as "lastUpdate"
        ,x.NOTES_4 as "reason"
        ,x.SALES_TYPE
        as "saleModelCode"
        ,x.Lead_Source as "clueSourceCode"
        ,x.NOTES_2 as "background"
        ,sl.CREATED_BY as "createdBy"
        ,x.Lead_Type as "acctTypeCode"
        <!--,st.LAST_NAME as "backPerson"-->
        ,x.PROD_SYSTEM as "prodSystemId"
        ,x.PROD_LINE_BIG as "bigProdcutLineId"
        ,x.REFUSE_REASON as "backReasonCode"
        ,x.MUL_DIVISION_FLG as "mulDivisionFlg"
        ,x.MARKET_TYPE as "marketTypeCode"
        ,x.tech_mgr_id techMgrId
        ,x.FUND_FLG foundFlgCode
        ,x.ACCOUNT_ATTRIBUTE accountAttributeCode
        ,x.POTENTIAL_MODEL potentialModelCode
    </sql>
    <resultMap id="BusinessClues" type="com.zte.mcrm.clues.access.vo.BusinessClues"></resultMap>

    <select id="getCluesWithAuth" parameterType="com.zte.springbootframe.util.page.PageQuery" resultMap="BusinessClues">
        select tb.* from (
        <if test="'02'!=entity.clueType and '03'!=entity.clueType">
            SELECT
            <include refid="baseSql"></include>
            FROM
            s_lead sl
            INNER JOIN (
            SELECT '' as row_id FROM s_lead
            <if test="entity.auth.subOrgs !=null and entity.auth.subOrgs.size()>0">
                union
                SELECT
                t.row_id
                FROM
                s_lead t
                left join s_org_ext_from_project org ON t.bu_id=org.name
                left join S_PARTY_RPT_REL p52 ON org.row_id=p52.sub_party_id
                where 1=1
                and p52.`party_id` IN
                <foreach collection="entity.auth.subOrgs" item="org"
                         open="(" close=")" separator=",">
                    #{org}
                </foreach>
            </if>
            <if test="'01'==entity.clueType or '04'==entity.clueType or '05'==entity.clueType">
                UNION
                SELECT
                t.row_id
                FROM
                s_lead t
                WHERE
                t.ENABLE_FLAG = 'Y'
                AND t.created_by = #{entity.empId}
            </if>
            <if test="'01'==entity.clueType or '05'==entity.clueType">
                UNION
                SELECT
                clx.ROW_ID
                FROM
                cx_lead_x clx
                WHERE
                clx.owner_id = #{entity.empId}
            </if>
            ) AS filterResult ON filterResult.row_id = sl.row_id
            LEFT JOIN cx_lead_x x ON filterResult.row_id = x.row_id
            <!--LEFT JOIN s_contact st ON st.row_id = x.owner_id-->
            <!--left join s_user su on su.row_id = st.row_Id-->
            LEFT JOIN s_org_ext_x soex on sl.bu_id = soex.row_id  
            WHERE sl.ENABLE_FLAG = 'Y'
            <if test="'05'==entity.clueType">
                and sl.STATUS_CD = 'Closed'
            </if>
            <if test="'04'==entity.clueType">
                and sl.STATUS_CD in ('Assigning','Refused')
            </if>
            <if test="null!=entity.clueMsg">
                AND (
                upper(x.lead_name) LIKE upper(concat('%',#{entity.clueMsg},'%'))
                OR upper(sl.lead_num) LIKE upper(concat('%',#{entity.clueMsg},'%'))
                <!--OR upper(sl.last_name) LIKE upper('%${entity.clueMsg}%')-->
                <!--OR upper(su.login) LIKE upper('%${entity.clueMsg}%')-->
                )
            </if>
            order by sl.LAST_UPD desc
        </if>

        <if test="'02'==entity.clueType">
            SELECT
            <include refid="baseSql"></include>
            FROM
            s_lead sl
            INNER JOIN (
            SELECT
            t.row_id
            FROM
            s_lead t
            WHERE
            1 = 1
            AND t.created_by = #{entity.empId}
            ) AS filterResult ON filterResult.row_id = sl.row_id
            LEFT JOIN cx_lead_x x ON filterResult.row_id = x.row_id
            <!--LEFT JOIN s_contact st ON st.row_id = x.owner_id-->
            <!--left join s_user su on su.row_id = st.row_Id-->
            WHERE sl.ENABLE_FLAG = 'Y'
            <if test="null!=entity.clueMsg">
                AND (
                upper(x.lead_name) LIKE  upper(concat('%',#{entity.clueMsg},'%'))
                OR upper(sl.lead_num) LIKE  upper(concat('%',#{entity.clueMsg},'%'))
                <!--OR upper(sl.last_name) LIKE upper('%${entity.clueMsg}%')-->
                <!--OR upper(su.login) LIKE upper('%${entity.clueMsg}%')-->
                )
            </if>
            order by sl.LAST_UPD desc
        </if>

        <if test="'03'==entity.clueType">
            SELECT
            <include refid="baseSql"></include>
            FROM
            s_lead sl
            INNER JOIN (
            SELECT
            clx.ROW_ID
            FROM
            cx_lead_x clx
            WHERE
            clx.owner_id = #{entity.empId}
            ) AS filterResult ON filterResult.row_id = sl.row_id
            LEFT JOIN cx_lead_x x ON filterResult.row_id = x.row_id
            <!--LEFT JOIN s_contact st ON st.row_id = x.owner_id-->
            <!--left join s_user su on su.row_id = st.row_Id-->
            WHERE sl.ENABLE_FLAG = 'Y'
            <if test="null!=entity.clueMsg">
                AND (
                upper(x.lead_name) LIKE  upper(concat('%',#{entity.clueMsg},'%'))
                OR upper(sl.lead_num) LIKE  upper(concat('%',#{entity.clueMsg},'%'))
                <!--OR upper(sl.last_name) LIKE upper('%${entity.clueMsg}%')-->
                <!--OR upper(su.login) LIKE upper('%${entity.clueMsg}%')-->
                )
            </if>
            order by sl.LAST_UPD desc
        </if>
        ) tb
        limit #{start},#{pageSize}
    </select>
 
    <select id="countClues" parameterType="com.zte.springbootframe.util.page.PageQuery" resultType="int">
        select count(1) from (
        <if test="'02'!=entity.clueType and '03'!=entity.clueType">
            SELECT sl.ROW_ID
            FROM
            s_lead sl
            INNER JOIN (
            SELECT '' as row_id FROM s_lead
            <if test="entity.auth.subOrgs !=null and entity.auth.subOrgs.size()>0">
                union
                SELECT
                t.row_id
                FROM
                s_lead t
                left join s_org_ext_from_project org ON t.bu_id=org.name
                left join S_PARTY_RPT_REL p52 ON org.row_id=p52.sub_party_id
                where 1=1
                and p52.`party_id` IN
                <foreach collection="entity.auth.subOrgs" item="org"
                         open="(" close=")" separator=",">
                    #{org}
                </foreach>
            </if>
            <if test="'01'==entity.clueType or '04'==entity.clueType or '05'==entity.clueType">
                UNION
                SELECT
                t.row_id
                FROM
                s_lead t
                WHERE
                t.ENABLE_FLAG = 'Y'
                AND t.created_by = #{entity.empId}
            </if>
            <if test="'01'==entity.clueType or '05'==entity.clueType">
                UNION
                SELECT
                clx.ROW_ID
                FROM
                cx_lead_x clx
                WHERE
                clx.owner_id = #{entity.empId}
            </if>
            ) AS filterResult ON filterResult.row_id = sl.row_id
            LEFT JOIN cx_lead_x x ON filterResult.row_id = x.row_id
            <!--LEFT JOIN s_contact st ON st.row_id = x.owner_id-->
            <!--left join s_user su on su.row_id = st.row_Id-->
            WHERE sl.ENABLE_FLAG = 'Y'
            <if test="'05'==entity.clueType">
                and sl.STATUS_CD = 'Closed'
            </if>
            <if test="'04'==entity.clueType">
                and sl.STATUS_CD in ('Assigning','Refused')
            </if>
            <if test="null!=entity.clueMsg">
                AND (
                upper(x.lead_name) LIKE upper(concat('%',#{entity.clueMsg},'%'))
                OR upper(sl.lead_num) LIKE upper(concat('%',#{entity.clueMsg},'%'))
                <!--OR upper(sl.last_name) LIKE upper('%${entity.clueMsg}%')-->
                <!--OR upper(su.login) LIKE upper('%${entity.clueMsg}%')-->
                )
            </if>
        </if>

        <if test="'02'==entity.clueType">
            SELECT sl.ROW_ID
            FROM
            s_lead sl
            INNER JOIN (
            SELECT
            t.row_id
            FROM
            s_lead t
            WHERE
            1 = 1
            AND t.created_by = #{entity.empId}
            ) AS filterResult ON filterResult.row_id = sl.row_id
            LEFT JOIN cx_lead_x x ON filterResult.row_id = x.row_id
            <!--LEFT JOIN s_contact st ON st.row_id = x.owner_id-->
            <!--left join s_user su on su.row_id = st.row_Id-->
            WHERE sl.ENABLE_FLAG = 'Y'
            <if test="null!=entity.clueMsg">
                AND (
                upper(x.lead_name) LIKE upper(concat('%',#{entity.clueMsg},'%'))
                OR upper(sl.lead_num) LIKE upper(concat('%',#{entity.clueMsg},'%'))
                <!--OR upper(sl.last_name) LIKE upper('%${entity.clueMsg}%')-->
                <!--OR upper(su.login) LIKE upper('%${entity.clueMsg}%')-->
                )
            </if>
        </if>

        <if test="'03'==entity.clueType">
            SELECT sl.ROW_ID
            FROM
            s_lead sl
            INNER JOIN (
            SELECT
            clx.ROW_ID
            FROM
            cx_lead_x clx
            WHERE
            clx.owner_id = #{entity.empId}
            ) AS filterResult ON filterResult.row_id = sl.row_id
            LEFT JOIN cx_lead_x x ON filterResult.row_id = x.row_id
            <!--LEFT JOIN s_contact st ON st.row_id = x.owner_id-->
            <!--left join s_user su on su.row_id = st.row_Id-->
            WHERE sl.ENABLE_FLAG = 'Y'
            <if test="null!=entity.clueMsg">
                AND (
                upper(x.lead_name) LIKE upper(concat('%',#{entity.clueMsg},'%'))
                OR upper(sl.lead_num) LIKE upper(concat('%',#{entity.clueMsg},'%'))
                <!--OR upper(sl.last_name) LIKE upper('%${entity.clueMsg}%')-->
                <!--OR upper(su.login) LIKE upper('%${entity.clueMsg}%')-->
                )
            </if>
        </if>
        ) tb
    </select>
    <select id="getRecentCluesWithAuth" parameterType="com.zte.springbootframe.util.page.PageQuery"
            resultMap="BusinessClues">
        select ts.* from (
        select tt.* from (
        SELECT
        <include refid="baseSql"></include>
        FROM
        s_lead sl
        INNER JOIN (
        SELECT '' as row_id FROM s_lead
        <if test="entity.auth.subOrgs !=null and entity.auth.subOrgs.size()>0">
            union
            SELECT
            t.row_id
            FROM
            s_lead t
            left join s_org_ext_from_project org ON t.bu_id=org.name
            left join S_PARTY_RPT_REL p52 ON org.row_id=p52.sub_party_id
            where 1=1
            and p52.`party_id` IN
            <foreach collection="entity.auth.subOrgs" item="org"
                     open="(" close=")" separator=",">
                #{org}
            </foreach>
        </if>
        UNION
        SELECT
        t.row_id
        FROM
        s_lead t
        WHERE
        1 = 1
        AND t.created_by = #{entity.empId}
        UNION
        SELECT
        clx.ROW_ID
        FROM
        cx_lead_x clx
        WHERE
        clx.owner_id = #{entity.empId}
        ) AS filterResult ON filterResult.row_id = sl.row_id
        LEFT JOIN cx_lead_x x ON filterResult.row_id = x.row_id
        <!--LEFT JOIN s_contact st ON st.row_id = x.owner_id-->
        WHERE sl.ENABLE_FLAG = 'Y'
        <if test="''!=filterInitVo.zteOpptyCode and null !=filterInitVo.zteOpptyCode">
            and sl.lead_num in
            <foreach collection="filterInitVo.opptyCodeArr" item="filterInitVo.opptyCodeArr"
                     open="(" close=")" separator=",">
                #{filterInitVo.opptyCodeArr}
            </foreach>
            order by field(SL.lead_num,
            <foreach collection="filterInitVo.opptyCodeArr" item="filterInitVo.opptyCodeArr"
                     open="" close="" separator=",">
                #{filterInitVo.opptyCodeArr}
            </foreach>
            )
        </if>
        )tt
        )ts
        limit #{start},#{pageSize}
    </select>
    <select id="selectBaseInfo" parameterType="com.zte.mcrm.clues.access.vo.BusinessClues"
            resultType="com.zte.mcrm.clues.access.vo.BusinessClues">
        SELECT
        <include refid="baseSql"></include>
        ,x.PROD_SYSTEM_NAME as "prodSystem"
        ,x.PROD_LINE_BIG_NAME as "bigProductLine"
        ,x.CLOSER_ID backPersonId
        ,x.NOTES_4 reasonCode
        ,x.LAST_ACC_ID lastAcctId
        <!--,t4.name lastAcctName-->
        ,x.PARENT_TRADE parentTradeCode
        ,x.CHILD_TRADE childTradeCode
        <!--,t3.X_HR_ORG_PATH fullDepetName-->
        FROM S_LEAD sl
        LEFT JOIN CX_LEAD_X x ON sl.ROW_ID = x.ROW_ID
        <!--LEFT JOIN S_ORG_EXT_X T3 ON sl.BU_ID = T3.ROW_ID-->
        <!--LEFT JOIN S_CONTACT st ON x.CLOSER_ID = st.ROW_ID-->
        <!--LEFT JOIN S_ORG_EXT T4 ON X.LAST_ACC_ID = T4.row_id-->
        WHERE sl.ENABLE_FLAG = 'Y'
        <if test="null!=clueNum">
            and sl.Lead_Num = #{clueNum}
        </if>
        <if test="null!=id">
            and sl.row_id = #{id}
        </if>
    </select>

    <select id="selectBusinessOppUserPosition" parameterType="com.zte.mcrm.clues.access.vo.BusinessClues"
            resultType="com.zte.mcrm.opportunity.access.vo.BusinessOppPositionVo">
        SELECT T3.OPPTY_SECT post,
        t4.bu_id buId,
        T3.row_id id,
        T3.POSITION_TYPE positionType
        FROM
        S_PARTY_PER T1,
        CX_POST_SECT T3,
        S_POSTN T4
        WHERE T1.PERSON_ID = #{empId} AND
        T1.PARTY_ID = T4.ROW_ID AND
        T4.POSTN_TYPE_CD = T3.POSITION_TYPE
    </select>
    <!-- 线索状态是否为"待客户经理更新"-->
    <select id="isLeadUpdate" parameterType="com.zte.mcrm.clues.access.vo.BusinessClues" resultType="int">
        select count(*)
        from s_lead t
        where t.ENABLE_FLAG = 'Y' AND t.status_cd = 'Renewing' and
        t.row_id = #{id}
    </select>

    <select id="isLeadUpdateByNum" parameterType="com.zte.mcrm.clues.access.vo.BusinessClues" resultType="int">
        select count(*)
        from s_lead t
        where t.ENABLE_FLAG = 'Y' AND t.status_cd = 'Renewing' and
        t.Lead_Num = #{clueNum}
    </select>

    <!-- 删除线索 -->
    <update id="deleteClue" parameterType="com.zte.mcrm.clues.access.vo.BusinessClues">
        UPDATE S_LEAD SET ENABLE_FLAG = 'N' WHERE ROW_ID = #{id} OR LEAD_NUM = #{id}
    </update>
    <!-- 根据线索ID获取线索详情 -->
    <select id="selectBaseInfoByClueId" parameterType="com.zte.mcrm.clues.access.vo.BusinessClues"
            resultType="com.zte.mcrm.clues.access.vo.BusinessClues">
        SELECT
        ROW_ID id,
        BU_ID deptId,
        LEAD_NUM clueNum,
        STATUS_CD statusCode,
        ACCNT_ID acctId,
        CREATED_BY createdBy
        FROM S_LEAD WHERE ROW_ID = #{id} OR LEAD_NUM = #{id} LIMIT 1
    </select>

    <select id="getMyClueCount" parameterType="java.lang.String" resultType="int">
      select count(*)
      from cx_lead_x
      where OWNER_ID = #{empNo}
    </select>

    <!-- 根据部门ORG编码、线索编码查询商机 -->
    <select id="getCluesListByOrgCodeClueNum" resultMap="BusinessClues">
        SELECT
            sl.row_id as id
            ,sl.Lead_Num as "clueNum"
            ,sl.STATUS_CD as "statusCode"
            ,sl.BU_ID as deptId
        FROM s_lead sl
        where sl.ENABLE_FLAG = 'Y'
        <if test="null != clueNum and '' != clueNum">
            and sl.Lead_Num = #{clueNum,jdbcType=VARCHAR}
        </if>
        <if test="null != orgCode and '' != orgCode">
            and locate(#{orgCode, jdbcType=VARCHAR}, sl.org_code_path)
        </if>
    </select>

    <!-- 批量更新部门ORG编码全路径 -->
    <update id="updateOrgCodePathList" parameterType="com.zte.mcrm.clues.access.vo.BusinessClues" >
        update s_lead
        set org_code_path =
        <foreach collection="list" item="item" index="index" separator=" " open="CASE ROW_ID" close="END">
            WHEN #{item.id,jdbcType=VARCHAR} THEN #{item.orgCodePath,jdbcType=VARCHAR}
        </foreach>
        where ROW_ID in
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>

    <!-- 我的所有线索总数量查询 -->
    <select id="countAllTypeClue" parameterType="com.zte.springbootframe.util.page.PageQuery" resultType="int">
        select count(sl.row_id)
        from s_lead sl
        LEFT JOIN cx_lead_x x ON sl.row_id = x.row_id
        where sl.ENABLE_FLAG = 'Y'
        <if test="null!=entity.clueMsg">
            and (locate(#{entity.clueMsg,jdbcType=VARCHAR}, sl.lead_num) or locate(#{entity.clueMsg,jdbcType=VARCHAR}, x.lead_name))
        </if>
        and (
            x.owner_id = #{entity.empId,jdbcType=VARCHAR}
            or sl.created_by = #{entity.empId,jdbcType=VARCHAR}
            <if test="entity.auth != null and entity.auth.subOrgPathSet != null and entity.auth.subOrgPathSet.size() > 0">
                or
                <foreach collection="entity.auth.subOrgPathSet" item="orgNo" open=" " close=" " separator="OR">
                    sl.org_code_path LIKE concat(#{orgNo,jdbcType=VARCHAR}, '%')
                </foreach>
            </if>
        )
    </select>

    <!-- 我的所有线索查询 -->
    <select id="getAllTypeClueList" parameterType="com.zte.springbootframe.util.page.PageQuery" resultMap="BusinessClues">
        select
          <include refid="baseSql"></include>
        from s_lead sl
        LEFT JOIN cx_lead_x x ON sl.row_id = x.row_id
        where sl.ENABLE_FLAG = 'Y'
        <if test="null != entity.clueMsg">
            and (locate(#{entity.clueMsg,jdbcType=VARCHAR}, sl.lead_num) or locate(#{entity.clueMsg,jdbcType=VARCHAR}, x.lead_name))
        </if>
        and (
            x.owner_id = #{entity.empId,jdbcType=VARCHAR}
            or sl.created_by = #{entity.empId,jdbcType=VARCHAR}
            <if test="entity.auth != null and entity.auth.subOrgPathSet != null and entity.auth.subOrgPathSet.size() > 0">
                or
                <foreach collection="entity.auth.subOrgPathSet" item="orgNo" open=" " close=" " separator="OR">
                    sl.org_code_path LIKE concat(#{orgNo,jdbcType=VARCHAR}, '%')
                </foreach>
            </if>
        )
        order by sl.LAST_UPD desc
        limit #{start},#{pageSize}
    </select>

</mapper>
