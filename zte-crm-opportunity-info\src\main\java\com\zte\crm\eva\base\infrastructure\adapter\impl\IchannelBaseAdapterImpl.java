package com.zte.crm.eva.base.infrastructure.adapter.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.zte.crm.eva.base.common.utils.CrmSecurityUtil;
import com.zte.crm.eva.base.common.utils.PageRowsUtil;
import com.zte.crm.eva.base.domain.pseudocode.model.PseudocodeRule;
import com.zte.crm.eva.base.domain.universal.CommonTableMap;
import com.zte.crm.eva.base.domain.universal.DynamicJsonContent;
import com.zte.crm.eva.base.infrastructure.adapter.IchannelBaseAdapter;
import com.zte.crm.eva.base.infrastructure.client.IchannelBaseFeign;
import com.zte.crm.eva.base.infrastructure.client.model.ComMsgForwardDTO;
import com.zte.crm.eva.base.infrastructure.client.model.PrmQuickCodeValue;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.clouddisk.util.ServiceDataUtil;
import com.zte.mcrm.adapter.model.vo.BulkCodeValuesByOneTypeVo;
import com.zte.mcrm.common.framework.exception.ErrorCode;
import com.zte.mcrm.common.framework.exception.ErrorCodeException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * ichannel接口封装
 * <AUTHOR>
 * @date 2023-04-14
 */
@Component(value = "ichannelBaseAdapter")
public class IchannelBaseAdapterImpl implements IchannelBaseAdapter {

    private static final Logger LOGGER = LoggerFactory.getLogger(IchannelBaseAdapterImpl.class);
    /**
     * 消息类型
     */
    private static final int NOTICE_TYPE = 1;
    /**
     * 邮件类型
     */
    private static final int EMAIL_TYPE = 2;

    @Autowired
    private IchannelBaseFeign ichannelBaseFeign;

    @Value("${ichannel.encrypt.decrypt.key}")
    private String ichannelKey;

    /**
     * 根据文件名获取前后端字段的映射关系
     * @param fileName
     * @return
     * @throws Exception
     */
    @Override
    public CommonTableMap getTableMap(String fileName) throws Exception {
        try {
            ServiceData<String> jsonConfiguration = ichannelBaseFeign.getJsonConfiguration(fileName);
            boolean success = ServiceDataUtil.isSuccess(jsonConfiguration);
            if (!success) {
                throw new IllegalStateException("json configuration can't be found ");
            }
            String bo = jsonConfiguration.getBo();
            if (StringUtils.isBlank(bo)) {
                throw new IllegalStateException("json configuration can't be found ");
            }
            String jsonContent = CrmSecurityUtil.decryptUsingCustomerKey(bo, ichannelKey);
            DynamicJsonContent dynamicJsonContent = JSON.parseObject(jsonContent, new TypeReference<DynamicJsonContent>() {
            });
            if (dynamicJsonContent == null || dynamicJsonContent.getTable() == null) {
                throw new IllegalStateException("the table of json configuration is null");
            }
            CommonTableMap tableMap = dynamicJsonContent.getTable();
            tableMap.checkAttributes();
            return tableMap;
        } catch (Exception e) {
            LOGGER.error("IchannelBaseAdapterImpl-getTableMap failed, fileName:{}, error:{}", fileName, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据模块和规则名称获取drools脚本
     * @param module
     * @param ruleName
     * @return
     */
    @Override
    public List<PseudocodeRule> getPseudocodeRule(String module, String ruleName) {
        PseudocodeRule param = new PseudocodeRule();
        param.setModule(module);
        param.setRuleName(ruleName);
        ServiceData<List<PseudocodeRule>> pseudocodeRuleData = ichannelBaseFeign.getByNameAndModule(param);
        boolean success = ServiceDataUtil.isSuccess(pseudocodeRuleData);
        if (!success) {
            throw new IllegalStateException("querying pseudocodeRule is failure,  module:" + module + " ruleName:" + ruleName);
        }
        List<PseudocodeRule> ruleList = pseudocodeRuleData.getBo();
        if (CollectionUtils.isEmpty(ruleList)) {
            return Lists.newArrayList();
        }
        return ruleList;
    }

    /**
     * 根据类型查询系统参数配置
     * @param codeType
     * @return
     */
    @Override
    public List<PrmQuickCodeValue> getCodeValuesByCodeType(String codeType) {
        ServiceData<List<PrmQuickCodeValue>> prmQuickCodeValueData = ichannelBaseFeign.getByCodeType(codeType);
        boolean success = ServiceDataUtil.isSuccess(prmQuickCodeValueData);
        if (!success) {
            throw new IllegalStateException("querying PrmQuickCodeValue is failure, codeType:" + codeType);
        }
        List<PrmQuickCodeValue> prmQuickCodeValues = prmQuickCodeValueData.getBo();
        if (CollectionUtils.isEmpty(prmQuickCodeValues)) {
            return Lists.newArrayList();
        }
        return prmQuickCodeValues;
    }

    /**
     * 根据类型批量查询有效系统参数配置
     * @param codeTypeList
     * @return
     */
    @Override
    public List<BulkCodeValuesByOneTypeVo> getCodeValuesByCodeTypeList(List<String> codeTypeList) {
        ServiceData<List<BulkCodeValuesByOneTypeVo>> codeTypeValueList = ichannelBaseFeign.getByCodeTypeList(codeTypeList);
        boolean success = ServiceDataUtil.isSuccess(codeTypeValueList);
        if (!success) {
            throw new IllegalStateException("querying PrmQuickCodeValue is failure, codeType:" + codeTypeList);
        }
        List<BulkCodeValuesByOneTypeVo> prmQuickCodeValues = codeTypeValueList.getBo();
        if (CollectionUtils.isEmpty(prmQuickCodeValues)) {
            return Lists.newArrayList();
        }
        return prmQuickCodeValues;
    }

    /**
     * 通用发送邮件模板
     * @param msgId 模板编号
     * @param argMap 占位符字段map
     * @param toPersonList 收件人list（支持工号、邮箱）
     * @date 2022/05/20
     */
    @Override
    public void sendEmail(String msgId, Map<String, String> argMap, List<String> toPersonList) {
        ComMsgForwardDTO msgDTO = new ComMsgForwardDTO();
        msgDTO.setArgs(argMap);
        msgDTO.setMsgId(msgId);
        msgDTO.setTo(toPersonList);
        msgDTO.setType(EMAIL_TYPE);
        try {
            ichannelBaseFeign.asyncPush(msgDTO);
        } catch (Exception e) {
            LOGGER.error("Send email failure, msgId: {}, error:{}",msgId, e.getMessage(), e);
        }
    }

    /**
     * 通用发送工作通知模板
     * @param msgId 模板编号
     * @param argMap 占位符字段map
     * @param toPersonList 收件人list（支持工号、邮箱）
     * @date 2022/11/24
     */
    @Override
    public boolean sendNotice(String msgId, Map<String, String> argMap, List<String> toPersonList) {
        ComMsgForwardDTO msgDTO = new ComMsgForwardDTO();
        msgDTO.setArgs(argMap);
        msgDTO.setMsgId(msgId);
        msgDTO.setTo(toPersonList);
        msgDTO.setType(NOTICE_TYPE);
        try {
            ichannelBaseFeign.asyncPush(msgDTO);
            return true;
        } catch (Exception e) {
            LOGGER.error("Send notice failure, msgId: {}, error:{}",msgId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public PageRows<Map<String, Object>> getFoundationUpdate(FormData<Map<String, Object>> formData) {
        try {
            ServiceData<PageRows<Map<String, Object>>> foundationUpdate = ichannelBaseFeign.getFoundationUpdate(formData);
            if (ServiceDataUtil.isSuccess(foundationUpdate)) {
                return foundationUpdate.getBo();
            } else {
                LOGGER.error("获取任务列表失败, form:{}, response:{}",
                        JSON.toJSONString(formData),
                        JSON.toJSONString(foundationUpdate));
                return PageRowsUtil.buildEmptyPage(1L);
            }
        } catch (Exception e) {
            LOGGER.error(" 获取任务列表失败: ", e);
            throw new ErrorCodeException(ErrorCode.UniversalErrorEnum.OU3103, formData);
        }
    }

    /**
     * 更新记录
     *
     * @param queryParam
     * @return
     */
    public Boolean recordFoundation(Map<String, Object> queryParam) {
        try {
            ServiceData<Boolean> recordFoundationResponse = ichannelBaseFeign.recordFoundation(queryParam);
            if (ServiceDataUtil.isSuccess(recordFoundationResponse)) {
                return recordFoundationResponse.getBo();
            } else {
                return false;
            }
        } catch (Exception e) {
            LOGGER.error("记录保存异常 : ", e);
            throw new ErrorCodeException(ErrorCode.UniversalErrorEnum.OU3133, queryParam);
        }
    }

    /**
     * 修改组织任务
     *
     * @param queryParam 参数
     * @return Boolean
     */
    public Boolean updateFoundation(Map<String, Object> queryParam) {
        try {
            ServiceData<Boolean> updateFoundationResponse = ichannelBaseFeign.updateFoundation(queryParam);
            if (ServiceDataUtil.isSuccess(updateFoundationResponse)) {
                return updateFoundationResponse.getBo();
            } else {
                return false;
            }
        } catch (Exception e) {
            LOGGER.error("记录更新异常 : ", e);
            throw new ErrorCodeException(ErrorCode.UniversalErrorEnum.OU3143, queryParam);
        }
    }
}
