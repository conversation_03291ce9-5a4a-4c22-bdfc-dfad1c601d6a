package com.zte.mcrm.common.upload.controller;

import com.zte.itp.msa.core.exception.ValidationException;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import com.zte.mcrm.common.upload.model.entity.ComUploadFile;
import com.zte.mcrm.common.upload.model.entity.QueryUploadFileInfo;
import com.zte.mcrm.common.upload.service.UploadFileService;
import com.zte.mcrm.common.util.FormDataHelpUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 *  spring mvc控制类
 * <AUTHOR>
 * @date 2021/06/05
 */
@Api(tags = "附件管理-界面管理接口")
@RestController
@RequestMapping("/uploadfile")
public class UploadFileController {

    /** 日志对象 */
    private static final Logger log = LoggerFactory.getLogger(UploadFileController.class);

    /** 服务对象，SPRING自动装配 */
    @Autowired
    UploadFileService uploadFileBaseServiceImpl ;

    @ApiOperation("根据主键获取实体对象 -Get方式")
    @GetMapping(value="/{id}")
    public ServiceData<ComUploadFile> get(@PathVariable("id") Long id) {
        //业务操作可以不捕获异常,由统一的异常处理方法处理
        ComUploadFile uploadFile = uploadFileBaseServiceImpl.get(id);

        //返回统一的服务端数据
        return ServiceResultUtil.success(uploadFile);
    }

    @ApiOperation("获取符合条件的实体列表,按指定属性排序 -Post方式")
    @PostMapping(value="/getlist")
    public ServiceData<List<ComUploadFile>>  getList(@RequestBody FormData<ComUploadFile>  form) {

        List<ComUploadFile> list = uploadFileBaseServiceImpl.getList(form.getBo());

        //返回统一的服务端数据
        return ServiceResultUtil.success(list);
    }

    @ApiOperation("删除指定记录-delete方式")
    @DeleteMapping(value="/{id}",produces ="application/json; charset=utf-8")
    public ServiceData<Integer>  delete(@PathVariable("id") Long id) {

        int count = uploadFileBaseServiceImpl.delete(id);

        //返回统一的服务端数据
        return ServiceResultUtil.success(count);
    }

    @ApiOperation("新增业务实体-POST方式")
    @PostMapping(value="/add",produces ="application/json; charset=utf-8")
    public ServiceData<Integer>  add(@Validated @RequestBody ComUploadFile uploadFile,BindingResult result) throws ValidationException {

        //检查数据效验结果,如果有验证错误,抛出数据验证异常
        if(result != null && result.hasErrors()){
            throw new ValidationException(result);
        }

        //业务操作可以不捕获异常,由统一的异常处理方法处理
        int count = uploadFileBaseServiceImpl.insert(uploadFile);

        //返回统一的服务端数据
        return ServiceResultUtil.success(count);
    }

    @ApiOperation("修改业务实体-PUT方式")
    @PutMapping(value="/edit",produces ="application/json; charset=utf-8")
    public ServiceData<Integer> edit(@Validated @RequestBody ComUploadFile uploadFile,BindingResult result) throws ValidationException  {

        //检查数据效验结果,如果有验证错误,抛出数据验证异常
        if(result != null && result.hasErrors()){
            throw new ValidationException(result);
        }

        //业务操作可以不捕获异常,由统一的异常处理方法处理
        int count = uploadFileBaseServiceImpl.update(uploadFile);

        //返回统一的服务端数据
        return ServiceResultUtil.success(count);
    }

    @ApiOperation("获取符合条件的分页记录列表，包括当页数据/记录总数,按指定属性排序 -Post方式")
    @PostMapping(value="/getpage")
    public ServiceData<PageRows<ComUploadFile>>  getPage(@RequestBody FormData<ComUploadFile>  form) {
        Map<String,Object> map = FormDataHelpUtil.getPageQueryMap(form);

        long total = uploadFileBaseServiceImpl.getCount(map);
        List<ComUploadFile> list = uploadFileBaseServiceImpl.getPage(map);

        PageRows<ComUploadFile> page = new PageRows<>();
        page.setCurrent(form.getPage());
        page.setTotal(total);
        page.setRows(list);

        //返回统一的服务端数据
        return ServiceResultUtil.success(page);
    }
    @ApiOperation("模糊查询获取符合条件的分页记录列表，包括当页数据/记录总数,按指定属性排序 -Post方式")
    @PostMapping(value="/getFuzzyPage")
    public ServiceData<PageRows<ComUploadFile>> getFuzzyPage(@RequestBody  FormData<QueryUploadFileInfo>  form) throws Exception {
        Map<String,Object> map = FormDataHelpUtil.getPageQueryMap(form);

        long total = uploadFileBaseServiceImpl.getFuzzyCount(map);
        List<ComUploadFile> list=null;
        if(total >=1 ){
            list = uploadFileBaseServiceImpl.getFuzzyPage(map);
        }
        PageRows<ComUploadFile> page = new PageRows<>();
        page.setCurrent(form.getPage());
        page.setTotal(total);
        page.setRows(list);

        //返回统一的服务端数据
        return ServiceResultUtil.success(page);
    }

}