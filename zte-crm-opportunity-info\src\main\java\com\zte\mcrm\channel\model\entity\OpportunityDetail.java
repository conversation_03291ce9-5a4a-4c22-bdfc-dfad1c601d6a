package com.zte.mcrm.channel.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.mcrm.adapter.model.vo.CustomerDetailInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
*  实体类
* <AUTHOR>
* @date 2021/09/14
*/

@Setter @Getter @ToString
@ApiModel(description="")
public class OpportunityDetail implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private String rowId;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "")
    private Date created;
    @ApiModelProperty(value = "")
    private String createdBy;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "")
    private Date lastUpd;
    @ApiModelProperty(value = "")
    private String lastUpdBy;
    @ApiModelProperty(value = "商机名称")
    private String attrib46;
    @ApiModelProperty(value = "")
    private String nationalAreaId;
    @ApiModelProperty(value = "")
    private String nationalAreaProvinceId;
    @ApiModelProperty(value = "")
    private String nationalAreaName;
    @ApiModelProperty(value = "")
    private String nationalAreaCityId;
    @ApiModelProperty(value = "预计签单金额")
    private BigDecimal totalAmount;
    @ApiModelProperty(value = "币种Id")
    private String currencyId;
    @ApiModelProperty(value = "币种code")
    private String currencyCode;
    @ApiModelProperty(value = "客户类型")
    private String optyType;
    @ApiModelProperty(value = "销售模式")
    private String salesType;
    @ApiModelProperty(value = "最终用途")
    private String finalUsage;
    @ApiModelProperty(value = "最终用户类型")
    private String endUserType;
    @ApiModelProperty(value = "最终用户的最终用途")
    private String enduseOfEnduser;
    @ApiModelProperty(value = "具体客户描述")
    private String specificCustomerDesc;
    @ApiModelProperty(value = "具体用途描述")
    private String specificUsageDesc;
    @ApiModelProperty(value = "国内国际")
    private String area;
    @ApiModelProperty(value = "子行业")
    private String childTrade;
    @ApiModelProperty(value = "行业")
    private String parentTrade;
    @ApiModelProperty(value = "市场类型")
    private String marketType;
    @ApiModelProperty(value = "服务属性")
    private String projectLabel;
    @ApiModelProperty(value = "")
    private String groupFlg;
    @ApiModelProperty(value = "客户对服务销售的接受程度")
    private String serAccept;
    @ApiModelProperty(value = "产品&方案竞争能力-运营商系统")
    private String prodAbility;
    @ApiModelProperty(value = "产品&方案竞争能力-运营商服务")
    private String prodAbility2;
    @ApiModelProperty(value = "产品&方案竞争能力-政企网系统")
    private String prodAbility3;
    @ApiModelProperty(value = "产品&方案竞争能力-政企网服务")
    private String prodAbility4;
    @ApiModelProperty(value = "客户关系-运营商系统")
    private String accntRelation;
    @ApiModelProperty(value = "客户关系-运营商服务")
    private String accntRelation2;
    @ApiModelProperty(value = "客户关系-政企网系统")
    private String accntRelation3;
    @ApiModelProperty(value = "客户关系-政企网服务")
    private String accntRelation4;
    @ApiModelProperty(value = "竞争难度-运营商系统")
    private String hard1;
    @ApiModelProperty(value = "竞争难度-运营商服务")
    private String hard2;
    @ApiModelProperty(value = "竞争难度-政企网系统")
    private String hard3;
    @ApiModelProperty(value = "竞争难度-政企网服务")
    private String hard4;
    @ApiModelProperty(value = "预计毛利率-运营商系统")
    private BigDecimal rate;
    @ApiModelProperty(value = "预计毛利率-运营商服务")
    private BigDecimal rate2;
    @ApiModelProperty(value = "预计毛利率-政企网系统")
    private BigDecimal rate3;
    @ApiModelProperty(value = "预计毛利率-政企网服务")
    private BigDecimal rate4;
    @ApiModelProperty(value = "运营商系统-成功概率")
    private BigDecimal attrib19;
    @ApiModelProperty(value = "运营商服务-成功概率")
    private BigDecimal attrib20;
    @ApiModelProperty(value = "政企网系统-成功概率")
    private BigDecimal attrib21;
    @ApiModelProperty(value = "政企网服务-成功概率")
    private BigDecimal attrib22;
    @ApiModelProperty(value = "")
    private String mulDivisionFlg;
    @ApiModelProperty(value = "商机等级-手工")
    private String opptyLevelManual;
    @ApiModelProperty(value = "商机等级-系统")
    private String opptyLevelSystem;
    @ApiModelProperty(value = "客户投资规模")
    private BigDecimal custInvestAmount;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "预计签单时间")
    private Date date2;
    @ApiModelProperty(value = "商机背景")
    private String notes2;
    @ApiModelProperty(value = "")
    private String isFromPrm;
    @JsonFormat(pattern ="yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "预计发标/议标时间")
    private Date date1;
    @ApiModelProperty(value = "商机来源")
    private String opptySource;
    @ApiModelProperty(value = "是否融资项目")
    private String fundFlg;
    @ApiModelProperty(value = "扩容/新建")
    private String netType;
    @ApiModelProperty(value = "MTO联调属性")
    private String mtoUnion;
    @ApiModelProperty(value = "成功概率-运营商系统")
    private BigDecimal succProb;
    @ApiModelProperty(value = " 成功概率-运营商服务")
    private BigDecimal succProb2;
    @ApiModelProperty(value = " 成功概率- 政企网系统")
    private BigDecimal succProb3;
    @ApiModelProperty(value = " 成功概率-政企网服务")
    private BigDecimal succProb4;
    @ApiModelProperty(value = "商机阶段-OPTY_PHASE")
    private String optyPhase;
    @ApiModelProperty(value = "商机排名")
    private BigDecimal opptyRange;
    @ApiModelProperty(value = "商机排名意见")
    private String opptyRecom;
    @ApiModelProperty(value = "线索Id")
    private String leadId;
    @ApiModelProperty(value = "招标类型-ZTE_INVITE_TYPE")
    private String tendType;
    @ApiModelProperty(value = "关联商机，关闭商机需要选择商机")
    private String optyId;
    @ApiModelProperty(value = "关闭原因ZTE_OPPTY_CLOSED_REASON")
    private String notes4;
    @ApiModelProperty(value = "")
    private String dataSource;
    @ApiModelProperty(value = "值内容等同于row_id，用于oracle odi")
    private String parRowId;
    @ApiModelProperty(value = "")
    private String projectType;
    @ApiModelProperty(value = "项目执行地")
    private String buId2;
    @ApiModelProperty(value = "商机等级Siebel系统使用")
    private String opptyLevel;
    @ApiModelProperty(value = "运营商系统签单金额")
    private BigDecimal amt;
    @ApiModelProperty(value = "运营商服务签单金额")
    private BigDecimal amt2;
    @ApiModelProperty(value = "政企网系统签单金额")
    private BigDecimal amt3;
    @ApiModelProperty(value = "政企网服务签单金额")
    private BigDecimal amt4;
    @ApiModelProperty(value = "客户属性")
    private String accountAttribute;
    @ApiModelProperty(value = "潜在融资模式")
    private String potentialModel;
    @ApiModelProperty(value = "二级经销商")
    private String secondDealerId;
    @ApiModelProperty(value = "最终用户Id")
    private String lastAccId;
    @ApiModelProperty(value = "最终用户编码")
    private String lastAccCode;
    @ApiModelProperty(value = "最终用户名称")
    private String lastAccName;
    @ApiModelProperty(value = "最终用户状态")
    private Integer lastAccStatus;
    @ApiModelProperty(value = "最终用户地址")
    private String finalCustomerAddress;
    @ApiModelProperty(value = "最终用户行业编码")
    private String finalCustomerParentTrade;
    @ApiModelProperty(value = "最终用户子行业编码")
    private String finalCustomerChildTrade;
    @ApiModelProperty(value = "最终用户联系人姓名")
    private String finalCustomerContactName;
    @ApiModelProperty(value = "最终用户联系人电话")
    private String finalCustomerContactPhone;
    @ApiModelProperty(value = "最终用户联系人邮箱")
    private String finalCustomerContactEmail;
    @ApiModelProperty(value = "投资方所在地（商机所属部门）")
    private String deptNo;
    @ApiModelProperty(value = "项目当前阶段编码")
    private String projectPhasesCode;
    @ApiModelProperty(value = "赢率")
    private String winRate;
    @ApiModelProperty(value = "招标类型编码")
    private String tenderTypeCode;
    @ApiModelProperty(value = "招标方全称")
    private String bidProviderName;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "竞标截止日期")
    private Date biddingDeadline;
    @ApiModelProperty(value = "报备人姓名")
    private String agencyName;
    @ApiModelProperty(value = "报备人电话")
    private String agencyPhone;
    @ApiModelProperty(value = "报备人邮箱")
    private String agencyEmail;
    @ApiModelProperty(value = "中兴业务经理id")
    private String businessManagerId;
    @ApiModelProperty(value = "中兴业务经理名字")
    private String businessManagerName;
    @ApiModelProperty(value = "项目指委会主任")
    private String directorOfPsc;
    @ApiModelProperty(value = "商机概况")
    private String projectDesc;
    @ApiModelProperty(value = "商机属性")
    private String optyAttribute;
    @ApiModelProperty(value = "确认设备自用")
    private String selfUseFlag;
    @ApiModelProperty(value = "渠道商客户编码")
    private String crmCustomerCode;
    @ApiModelProperty(value = "渠道商名称")
    private String customerName;
    @ApiModelProperty(value = "原渠道商名称")
    private String sourceCustomerName;
    @ApiModelProperty(value = "渠道商客户编码")
    private String sourceCrmCustomerCode;
    @ApiModelProperty(value = "报备代理商级别")
    private String agencyLevelName;
    @ApiModelProperty(value = "代理商级别编码")
    private String agencyLevelCode;
    @ApiModelProperty(value = "预留字段1")
    private String reservedField1;
    @ApiModelProperty(value = "预留字段2")
    private String reservedField2;
    @ApiModelProperty(value = "预留字段3")
    private String reservedField3;
    @ApiModelProperty(value = "预留字段4")
    private String reservedField4;
    @ApiModelProperty(value = "预留字段5")
    private String reservedField5;
    @ApiModelProperty(value = "最终用户是否受限制主体")
    private String finalCustomerRestrictionFlag;
    @ApiModelProperty(value = "渠道商是否受限制主体")
    private String agencyRestrictionFlag;
    @ApiModelProperty(value = "TS审批单号")
    private String tsApprovalNumber;
    @ApiModelProperty(value = "激活次数")
    private Integer activeCount;
    @ApiModelProperty(value = "是否属于激活报备(Y/N)")
    private String fromActiveFlag;
    @ApiModelProperty(value = "从哪个商机激活的(记录商机id)")
    private String fromActiveOpty;
    @ApiModelProperty(value = "有效标记")
    private String enabledFlag;
    @ApiModelProperty(value = "租户id")
    private Integer tenantId;
    @ApiModelProperty(value = "客户映射关系")
    private Map<String, CustomerDetailInfoVO> customerCodeIdMap;
    /**
     * 商机激活人
     */
    @ApiModelProperty(value = "商机激活人")
    private String activatedBy;

    /**
     * 渠道商信息
     */
    @ApiModelProperty(value = "渠道商信息")
    private String crmCustomer;

    public List<String> buildCustomerCodeList() {
        List<String> customerList = new ArrayList<>();
        customerList.add(this.getCrmCustomerCode());
        customerList.add(this.getSourceCrmCustomerCode());
        customerList.add(this.getLastAccId());
        return customerList.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
    }
}
