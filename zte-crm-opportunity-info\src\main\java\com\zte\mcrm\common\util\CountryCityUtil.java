package com.zte.mcrm.common.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zte.mcrm.common.model.CountryAndCityVo;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.common.model.HttpResultData;
import com.zte.springbootframe.common.serviceregister.MicroServiceWithConfigUtil;
import com.zte.springbootframe.util.string.StringHelper;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @Author: 10261899
 * @Date: 2019/12/3
 * @Description:
 */
public class CountryCityUtil {

    public static CountryAndCityVo getCountryCityInfo(String countryOrCityNo) throws BusiException {

        JSONObject param=new JSONObject();

        param.put("countryOrCityNo",countryOrCityNo);

        HttpResultData httpResultData = MicroServiceWithConfigUtil.invokeServiceWithGetMethod("4", "/country/getInfoById", param, RequestMessage.getHeader("pcOpportunity"));

        if(httpResultData!=null&&httpResultData.getBo()!=null){
            String result= JSONObject.toJSONString(httpResultData.getBo());
            return JSONObject.parseObject(result,CountryAndCityVo.class);
        }

        return null;

    }

    public static List<CountryAndCityVo> getCountryCityInfos(Set<String> countryOrCityNos) throws BusiException {

        List<CountryAndCityVo> result=new ArrayList<>();
        if(StringHelper.isEmpty(countryOrCityNos)){
            return result;
        }
        HttpResultData httpResultData = MicroServiceWithConfigUtil.invokeServiceWithPostMethod("4", "/country/getInfoByIds", countryOrCityNos, RequestMessage.getHeader("pcOpportunity"));

        if(httpResultData!=null&&httpResultData.getBo()!=null){
            String resultString= JSONObject.toJSONString(httpResultData.getBo());
            result=JSONArray.parseArray(resultString,CountryAndCityVo.class);
        }

        return result;

    }

}
