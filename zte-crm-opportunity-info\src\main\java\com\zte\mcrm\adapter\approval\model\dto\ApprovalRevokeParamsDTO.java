package com.zte.mcrm.adapter.approval.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName ApprovalRevokeParamsDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/5/18 16:03
 */
@Data
public class ApprovalRevokeParamsDTO implements Serializable {
    private static final long serialVersionUID = 229942709562172711L;
    /**
     * 应用编码
     */
    @ApiModelProperty("应用编码")
    private String appCode;
    /**
     * 流程id
     */
    @ApiModelProperty("流程id")
    private String flowCode;
    /**
     * 业务id
     */
    @ApiModelProperty("业务id")
    private String businessId;
    /**
     * 流程实例id
     */
    @ApiModelProperty("流程实例id")
    private String flowInstanceId;
    /**
     * 撤销原因描述
     */
    @ApiModelProperty("撤销原因描述")
    private String opinion;
    /**
     * 撤销扩展原因描述
     */
    @ApiModelProperty("撤销扩展原因描述")
    private String extOpinion;
}

