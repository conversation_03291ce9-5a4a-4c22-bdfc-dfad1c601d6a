package com.zte.mcrm.channel.dao;

import java.util.*;

import com.zte.mcrm.channel.model.entity.OpportunityKeyInfoEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;
import com.zte.mcrm.channel.model.entity.*;


/**
 * 商机月报 数据访问接口类 
 * <AUTHOR>
 * @date 2021/10/20 
 */
@Mapper
public interface OpportunityMonthReportDao {
	
    /**
     * 查询列表
     * <AUTHOR>
     * @param map 查询条件
     * @date 2021/10/20 
     * @return 实体集合
     */
	List<OpportunityMonthReport> getList(Map<String, Object> map);

	/**
	 * 查询月报归属期列表
	 * <AUTHOR>
	 * @date 2021/10/20
	 * @return 实体集合
	 */
	List<OpportunityMonthReport> getMonthReportList(@Param("optyId") String optyId);

	/**
	 *
	 * @param map
	 * @return
	 */
	int disAbleCurrentMonthReport(Map<String, Object> map);

	/**
	 *
	 * @param map
	 * @return
	 */
	OpportunityMonthReport getMonthReport(Map<String, Object> map);


    /**
     * 动态新增
     * <AUTHOR>
     * @param entity 新增实体
     * @date 2021/10/20 
     * @return 新增总数
     */	
	int insert(OpportunityMonthReport entity);

    /**
     * 更新
     * <AUTHOR>
     * @param entity 更新条件
     * @date 2021/10/20 
     * @return 更新影响总数
     */		
	int update(OpportunityMonthReport entity);

    /**
     * 统计
     * <AUTHOR>
     * @param map 查询条件
     * @date 2021/10/20 
     * @return 统计总数
     */	
	long getCount(Map<String, Object> map);

	/**
	 * 根据 当前商机，当前归属期 查找唯一一条数据
	 * <AUTHOR>
	 * @date 2021/10/21
	 * @return 实体集合
	 */
	OpportunityMonthReport queryByOpportunityAndReportMonth(@Param("opportunity")String opportunity, @Param("reportMonth")String reportMonth);

	/**
	 * 获取需要发送月报提醒邮件的商机列表
	 * @param reportMonth
	 * @return
	 */
	List<OpportunityKeyInfoEntity> getMonthReportReminders(@Param("reportMonth") String reportMonth);

	/**
	 * 获取需要发送月报提醒邮件的商机列表
	 * @param reportMonth
	 * @return
	 */
	List<OpportunityKeyInfoEntity> getMonthlyInvalidOptys(@Param("reportMonth") String reportMonth);

	/**
	 * 获取报备成功的商机及其对应的流程实例列表（注意：没有流程实例的不会查询）
	 * @return
	 */
	List<OpportunityKeyInfoEntity> getReNewStatusOptys(@Param("type")String type, @Param("limit") int limit);

	/**
	 * 获取老流程中创建客户草稿的在途商机列表
	 * @return
	 */
	List<OpportunityKeyInfoEntity> getOldAccDraftProcessOptys();
}
