package com.zte.leadinfo.common.mapper;

import com.zte.leadinfo.common.entity.CxApprOppLn;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cx_appr_opp_ln】的数据库操作Mapper
* @createDate 2024-07-12 23:29:27
* @Entity entity.CxApprOppLn
*/
public interface CxApprOppLnMapper extends BaseMapper<CxApprOppLn> {

    List<CxApprOppLn> listByOpptyIds(@Param("optyIds") List<String> optyIds, @Param("approveObjects") List<String> approveObjects);
}




