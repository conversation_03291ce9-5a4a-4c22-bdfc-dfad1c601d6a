package com.zte.mcrm.adapter.constant;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;

/**
 * @Author: <EMAIL>
 * @Date: 2021/07/26
 * @Description:
 */
public class Sha256EncryptUtil {
    /**
     * SHA-256加密后转成16进制
     * @param content
     * @return
     */
    public static String sha256Hex(String content) {
        byte[] bytes;
        try {
            Class<?> clazz = Class.forName("java.security.MessageDigest");
            Method method = clazz.getMethod("getInstance", String.class);
            // messageDigest = MessageDigest.getInstance("SHA-256")
            MessageDigest messageDigest = (MessageDigest) method.invoke(null, "SHA-256");
            // messageDigest.update(content.getBytes(StandardCharsets.UTF_8))
            method = clazz.getMethod("update", byte[].class, int.class, int.class);
            byte[] contentBytes = content.getBytes(StandardCharsets.UTF_8);
            method.invoke(messageDigest, contentBytes, 0, contentBytes.length);
            // bytes = messageDigest.digest()
            method = clazz.getMethod("digest");
            bytes = (byte[]) method.invoke(messageDigest);
        } catch (ClassNotFoundException | NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            e.printStackTrace();
            return null;
        }

        return byte2Hex(bytes);
    }
    /**
     * 将byte转为16进制
     * @param bytes 字节数组
     * @return 转为16进制内容
     */
    public static String byte2Hex(byte[] bytes) {
        StringBuilder stringBuilder = new StringBuilder();
        String temp;
        for (byte aByte : bytes) {
            temp = Integer.toHexString(aByte & 0xFF);
            if (temp.length() == 1) {
                //1得到一位的进行补0操作
                stringBuilder.append("0");
            }
            stringBuilder.append(temp);
        }
        return stringBuilder.toString();
    }
}
