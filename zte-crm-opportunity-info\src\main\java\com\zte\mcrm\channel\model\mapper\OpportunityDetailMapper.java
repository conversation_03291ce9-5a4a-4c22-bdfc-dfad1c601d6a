package com.zte.mcrm.channel.model.mapper;

import com.zte.mcrm.channel.model.dto.OpportunityTransferredProjectDTO;
import com.zte.mcrm.channel.model.entity.OpportunityDetail;
import com.zte.mcrm.channel.model.vo.OpportunityDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2021/10/19
 */
@Mapper
public interface OpportunityDetailMapper {
    OpportunityDetailMapper INSTANCE = Mappers.getMapper(OpportunityDetailMapper.class);

    /**
     * PrmOpportunityDetailVO 转 OpportunityDetail
     * @param opportunityDetailVO
     * @return
     */
    OpportunityDetail transOpportunityDetailVOToOpportunityDetail(OpportunityDetailVO opportunityDetailVO);


    /**
     * OpportunityDetail 转 PrmOpportunityDetailVO
     * @param opportunityDetail
     * @return
     */
    OpportunityDetailVO transOpportunityDetailToOpportunityDetailVO(OpportunityDetail opportunityDetail);

    /**
     * 转换转立项实体类到OpportunityDetail
     * @param opportunityTransferredProject 转立项实体类
     * @return
     */
    @Mapping(target = "crmCustomerCode", source = "customerCode")
    OpportunityDetail transTransferredProjectToOpportunityDetail(OpportunityTransferredProjectDTO opportunityTransferredProject);

}
