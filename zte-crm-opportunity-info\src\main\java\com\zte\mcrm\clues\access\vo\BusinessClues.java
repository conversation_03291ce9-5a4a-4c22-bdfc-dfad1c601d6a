package com.zte.mcrm.clues.access.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.zte.itp.msa.core.model.HeaderData;
import com.zte.mcrm.opportunity.access.vo.Auth;
import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.mcrm.lov.access.vo.ListOfValue;

import io.swagger.annotations.ApiModelProperty;

/****
 *
 * <AUTHOR> @date 2021/2/3
 **/
public class BusinessClues extends HeaderData implements Serializable {

	private static final long serialVersionUID = 1269450920399837081L;

    @ApiModelProperty("关闭原因值列表")
    private List<ListOfValue> reasonList;

    @ApiModelProperty("线索id")
    private String id;
    @ApiModelProperty("线索编号")
    private String clueNum;
    @NotBlank(message="{clueName.empty}")
    @ApiModelProperty("线索名称")
    private String clueName;
    @ApiModelProperty("状态码")
    private String statusCode;
    @ApiModelProperty("状态")
    private String status;
    @ApiModelProperty("业务范围")
    private String businessType;
    @ApiModelProperty("业务范围语言代码")
    private String businessTypeCode;
    @NotBlank(message="{businessTypeId.empty}")
    @ApiModelProperty("业务范围语言ID")
    private String businessTypeId;
    @NotBlank(message="{deptId.empty}")
    @ApiModelProperty("代表处id")
    private String deptId;
    @ApiModelProperty("办事处")
    private String deptName;
    private String fullDepetName;
    @ApiModelProperty("归属客户经理")
    private String ownerName;
    @ApiModelProperty("归属客户经理工号")
    private String ownerNo;
    @ApiModelProperty("归属客户经理ID")
    private String ownerMgr;
    @NotBlank(message="{currency.empty}")
    @ApiModelProperty("币种")
    private String currency;
    @ApiModelProperty("客户投资规模")
    private Double investmentScaleOfAcct;
    @ApiModelProperty("预计签单金额")
    private Double predictSignAmt;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("预计签单时间")
    private Date predictSignDate;
    @ApiModelProperty("预计签单时间开始,用于高级查询")
    private String predictSignDateFrom;
    @ApiModelProperty("预计签单时间结束,用于高级查询")
    private String predictSignDateTo;
    @ApiModelProperty("客户id")
    private String acctId;
    @ApiModelProperty("客户编码")
    private String acctNum;
    @ApiModelProperty("客户名称")
    private String acctName;
    @ApiModelProperty("客户类型")
    private String acctType;
    @ApiModelProperty("客户类型语言代码")
    private String acctTypeCode;
    @NotBlank(message="{acctTypeId.empty}")
    @ApiModelProperty("客户类型语言ID")
    private String acctTypeId;
    @ApiModelProperty("销售模式语言代码")
    private String saleModelCode;
    @ApiModelProperty("销售模式语言ID")
    private String saleModelId;
    @ApiModelProperty("销售模式")
    private String saleMode;
    @ApiModelProperty("线索来源")
    private String clueSource;
    @ApiModelProperty(" 线索来源语言代码")
    private String clueSourceCode;
    @ApiModelProperty("线索来源语言ID")
    private String clueSourceId;
    @ApiModelProperty("线索背景")
    private String background;
    @ApiModelProperty("创建人")
    private String createdBy;
    @ApiModelProperty("员工工号")
    private String empNo;
    @ApiModelProperty("员工ID")
    private String empId;
    @ApiModelProperty("员工姓名")
    private String lastName;
    @ApiModelProperty("模糊查询条件")
    private String clueMsg;
    @ApiModelProperty("子行业ID")
    private String childTradeId;
    @ApiModelProperty("子行业")
    private String childTrade;
    @ApiModelProperty("子行业独立语言代码")
    private String childTradeCode;
    @ApiModelProperty("行业Id")
    private String parentTradeId;
    @ApiModelProperty("行业")
    private String parentTrade;
    @ApiModelProperty("行业独立语言代码")
    private String parentTradeCode;
    @ApiModelProperty("最终客户ID")
    private String lastAcctId;
    @ApiModelProperty("最终客户名字")
    private String lastAcctName;
    @ApiModelProperty("国内国际")
    private String area;
    @ApiModelProperty("国内国际独立语言代码")
    private String areaCode;
    @ApiModelProperty("国内国际id")
    private String areaId;
    @ApiModelProperty("关闭原因ID")
    private String reason;
    @ApiModelProperty("关闭原因Code")
    private String reasonCode;
    @ApiModelProperty("关闭原因ID")
    private String reasonId;
    @ApiModelProperty("提交保存方式")
    private String postType;
    @ApiModelProperty("当前职位是否可以转商机")
    private String isTransfer="N";
    @ApiModelProperty("当前线索是否可以分配")
    private String isAssigned="N";
    @ApiModelProperty("是否可关闭")
    private String isBack = "N";
    @ApiModelProperty("退回原因值列表 ")
    private List<ListOfValue> backReason;
    @ApiModelProperty("退回原因Id")
    private String backReasonId;
    @ApiModelProperty("退回原因")
    private String backReasonVal;
    @ApiModelProperty("退回原因code")
    private String backReasonCode;
    @ApiModelProperty("版本号")
    private String version;
	@ApiModelProperty("市场类型独立源代码")
	private String marketTypeCode;
	@ApiModelProperty("市场类型")
	private String marketType;
	@ApiModelProperty("是否跨事业部运作")
	private String mulDivisionFlg;
	@ApiModelProperty("技术经理ID")
	private String techMgrId;
	@ApiModelProperty("技术经理名字")
	private String techMgrName;
	@ApiModelProperty("技术经理工号")
	private String techMgrNo;
	@ApiModelProperty("业务类型")
	private String clueType;
	@ApiModelProperty("创建人姓名")
	private String createName;
	@ApiModelProperty("创建人工号")
	private String createNo;
	@ApiModelProperty("ZTE业务客户分类")
	private String zteBusiAccountCategory;
	@ApiModelProperty("运营商级别")
	private String operatorLevel;
	@ApiModelProperty("运营商类型")
	private String operatorType;
	@ApiModelProperty("集团客户简称")
	private String mtoName;
	@ApiModelProperty("关联线索")
	private String relationClue;
	@ApiModelProperty("退回人")
	private String backPerson;
	@ApiModelProperty("退回人")
	private String backPersonId;
	@ApiModelProperty("退回人工号")
	private String backPersonNum;
	@ApiModelProperty("是否可转商机")
	private String isRenewing = "N";
	@ApiModelProperty("大产品线")
	private String bigProductLine;
	@ApiModelProperty("大产品线ID")
	private String bigProdcutLineId;
	@ApiModelProperty("产品体系分类")
	private String prodSystem;
	@ApiModelProperty("产品体系分类ID")
	private String prodSystemId;
	@ApiModelProperty("是否有关闭权限")
	private String isClosedAuth="N";
	@ApiModelProperty("是否有还原权限")
	private String isRestoreAuth="N";
	@ApiModelProperty("数据来源")
	private String datasource;
	@ApiModelProperty("拓展活动ID")
	private String visitId;
	@ApiModelProperty("是否融资")
	private String foundFlg;
	private String foundFlgCode;
	@ApiModelProperty("客户属性")
	private String accountAttribute;
	private String accountAttributeCode;
	@ApiModelProperty("潜在融资模式")
	private String potentialModel;
	private String potentialModelCode;
	@ApiModelProperty("创建时间")
	private String created;
	@ApiModelProperty("最后更新时间")
	private String lastUpdate;
    @ApiModelProperty("部门ORG编码")
    private String orgCode;
    @ApiModelProperty("部门ORG编码全路径")
    private String orgCodePath;
    @ApiModelProperty("海外")
    private Boolean overseasArea;

	public Boolean getOverseasArea() {
		return overseasArea;
	}

	public void setOverseasArea(Boolean overseasArea) {
		this.overseasArea = overseasArea;
	}

	public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgCodePath() {
        return orgCodePath;
    }

    public void setOrgCodePath(String orgCodePath) {
        this.orgCodePath = orgCodePath;
    }

    public String getCreated() {
		return created;
	}

	public void setCreated(String created) {
		this.created = created;
	}

	public String getLastUpdate() {
		return lastUpdate;
	}

	public void setLastUpdate(String lastUpdate) {
		this.lastUpdate = lastUpdate;
	}

	private Auth auth;

	public Auth getAuth() {
		return auth;
	}

	public void setAuth(Auth auth) {
		this.auth = auth;
	}

	public static long getSerialVersionUID() {
		return serialVersionUID;
	}

	public String getPredictSignDateFrom() {
		return predictSignDateFrom;
	}

	public void setPredictSignDateFrom(String predictSignDateFrom) {
		this.predictSignDateFrom = predictSignDateFrom;
	}

	public String getPredictSignDateTo() {
		return predictSignDateTo;
	}

	public void setPredictSignDateTo(String predictSignDateTo) {
		this.predictSignDateTo = predictSignDateTo;
	}

	public String getFullDepetName() {
		return fullDepetName;
	}

	public void setFullDepetName(String fullDepetName) {
		this.fullDepetName = fullDepetName;
	}

	public String getFoundFlgCode() {
		return foundFlgCode;
	}

	public void setFoundFlgCode(String foundFlgCode) {
		this.foundFlgCode = foundFlgCode;
	}

	public String getAccountAttributeCode() {
		return accountAttributeCode;
	}

	public void setAccountAttributeCode(String accountAttributeCode) {
		this.accountAttributeCode = accountAttributeCode;
	}

	public String getPotentialModelCode() {
		return potentialModelCode;
	}

	public void setPotentialModelCode(String potentialModelCode) {
		this.potentialModelCode = potentialModelCode;
	}

	public String getFoundFlg() {
		return foundFlg;
	}

	public void setFoundFlg(String foundFlg) {
		this.foundFlg = foundFlg;
	}

	public String getAccountAttribute() {
		return accountAttribute;
	}

	public void setAccountAttribute(String accountAttribute) {
		this.accountAttribute = accountAttribute;
	}

	public String getPotentialModel() {
		return potentialModel;
	}

	public void setPotentialModel(String potentialModel) {
		this.potentialModel = potentialModel;
	}

	public String getVisitId() {
		return visitId;
	}

	public void setVisitId(String visitId) {
		this.visitId = visitId;
	}

	public String getDatasource() {
		return datasource;
	}

	public void setDatasource(String datasource) {
		this.datasource = datasource;
	}

	public String getIsClosedAuth() {
		return isClosedAuth;
	}

	public void setIsClosedAuth(String isClosedAuth) {
		this.isClosedAuth = isClosedAuth;
	}

	public String getIsRestoreAuth() {
		return isRestoreAuth;
	}

	public void setIsRestoreAuth(String isRestoreAuth) {
		this.isRestoreAuth = isRestoreAuth;
	}

	public String getBackPersonNum() {
		return backPersonNum;
	}

	public void setBackPersonNum(String backPersonNum) {
		this.backPersonNum = backPersonNum;
	}

	public String getBackPersonId() {
		return backPersonId;
	}

	public void setBackPersonId(String backPersonId) {
		this.backPersonId = backPersonId;
	}

	public String getBackReasonVal() {
		return backReasonVal;
	}

	public void setBackReasonVal(String backReasonVal) {
		this.backReasonVal = backReasonVal;
	}

	public String getBackReasonCode() {
		return backReasonCode;
	}

	public void setBackReasonCode(String backReasonCode) {
		this.backReasonCode = backReasonCode;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getBigProductLine() {
		return bigProductLine;
	}

	public void setBigProductLine(String bigProductLine) {
		this.bigProductLine = bigProductLine;
	}

	public String getBigProdcutLineId() {
		return bigProdcutLineId;
	}

	public void setBigProdcutLineId(String bigProdcutLineId) {
		this.bigProdcutLineId = bigProdcutLineId;
	}

	public String getProdSystem() {
		return prodSystem;
	}

	public void setProdSystem(String prodSystem) {
		this.prodSystem = prodSystem;
	}

	public String getProdSystemId() {
		return prodSystemId;
	}

	public void setProdSystemId(String prodSystemId) {
		this.prodSystemId = prodSystemId;
	}

	public String getIsRenewing() {
		return isRenewing;
	}

	public void setIsRenewing(String isRenewing) {
		this.isRenewing = isRenewing;
	}

	public String getCreateNo() {
		return createNo;
	}

	public void setCreateNo(String createNo) {
		this.createNo = createNo;
	}
	public String getRelationClue() {
		return relationClue;
	}

	public void setRelationClue(String relationClue) {
		this.relationClue = relationClue;
	}

	public String getBackPerson() {
		return backPerson;
	}

	public void setBackPerson(String backPerson) {
		this.backPerson = backPerson;
	}

	public String getClueType() {
		return clueType;
	}

	public void setClueType(String clueType) {
		this.clueType = clueType;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}
	
	public String getZteBusiAccountCategory() {
		return zteBusiAccountCategory;
	}

	public void setZteBusiAccountCategory(String zteBusiAccountCategory) {
		this.zteBusiAccountCategory = zteBusiAccountCategory;
	}

	public String getOperatorLevel() {
		return operatorLevel;
	}

	public void setOperatorLevel(String operatorLevel) {
		this.operatorLevel = operatorLevel;
	}

	public String getOperatorType() {
		return operatorType;
	}

	public void setOperatorType(String operatorType) {
		this.operatorType = operatorType;
	}

	public String getMtoName() {
		return mtoName;
	}

	public void setMtoName(String mtoName) {
		this.mtoName = mtoName;
	}

	public String getMulDivisionFlg() {
		return mulDivisionFlg;
	}

	public void setMulDivisionFlg(String mulDivisionFlg) {
		this.mulDivisionFlg = mulDivisionFlg;
	}

	public String getMarketTypeCode() {
		return marketTypeCode;
	}

	public void setMarketTypeCode(String marketTypeCode) {
		this.marketTypeCode = marketTypeCode;
	}

	public String getMarketType() {
		return marketType;
	}

	public void setMarketType(String marketType) {
		this.marketType = marketType;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public List<ListOfValue> getBackReason() {
		return backReason;
	}

	public void setBackReason(List<ListOfValue> backReason) {
		this.backReason = backReason;
	}

	public String getIsBack() {
		return isBack;
	}

	public void setIsBack(String isBack) {
		this.isBack = isBack;
	}

	public String getBackReasonId() {
		return backReasonId;
	}

	public void setBackReasonId(String backReasonId) {
		this.backReasonId = backReasonId;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public String getOwnerName() {
		return ownerName;
	}

	public void setOwnerName(String ownerName) {
		this.ownerName = ownerName;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public Double getPredictSignAmt() {
		return predictSignAmt;
	}

	public void setPredictSignAmt(Double predictSignAmt) {
		this.predictSignAmt = predictSignAmt;
	}

	public Date getPredictSignDate() {
		return predictSignDate;
	}

	public void setPredictSignDate(Date predictSignDate) {
		this.predictSignDate = predictSignDate;
	}

	public Double getInvestmentScaleOfAcct() {
		return investmentScaleOfAcct;
	}

	public void setInvestmentScaleOfAcct(Double investmentScaleOfAcct) {
		this.investmentScaleOfAcct = investmentScaleOfAcct;
	}

	public String getAcctName() {
		return acctName;
	}

	public void setAcctName(String acctName) {
		this.acctName = acctName;
	}
	
	public String getAcctNum() {
		return acctNum;
	}

	public void setAcctNum(String acctNum) {
		this.acctNum = acctNum;
	}

	public String getAcctType() {
		return acctType;
	}

	public void setAcctType(String acctType) {
		this.acctType = acctType;
	}

	public String getSaleMode() {
		return saleMode;
	}

	public void setSaleMode(String saleMode) {
		this.saleMode = saleMode;
	}

	public String getClueSource() {
		return clueSource;
	}

	public void setClueSource(String clueSource) {
		this.clueSource = clueSource;
	}

	public String getBackground() {
		return background;
	}

	public void setBackground(String background) {
		this.background = background;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public String getClueNum() {
		return clueNum;
	}

	public void setClueNum(String clueNum) {
		this.clueNum = clueNum;
	}

	public String getClueName() {
		return clueName;
	}

	public void setClueName(String clueName) {
		this.clueName = clueName;
	}

	public String getStatusCode() {
		return statusCode;
	}

	public void setStatusCode(String statusCode) {
		this.statusCode = statusCode;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getBusinessTypeCode() {
		return businessTypeCode;
	}

	public void setBusinessTypeCode(String businessTypeCode) {
		this.businessTypeCode = businessTypeCode;
	}

	public String getDeptId() {
		return deptId;
	}

	public void setDeptId(String deptId) {
		this.deptId = deptId;
	}

	public String getOwnerMgr() {
		return ownerMgr;
	}

	public void setOwnerMgr(String ownerMgr) {
		this.ownerMgr = ownerMgr;
	}

	public String getAcctId() {
		return acctId;
	}

	public void setAcctId(String acctId) {
		this.acctId = acctId;
	}

	public String getAcctTypeCode() {
		return acctTypeCode;
	}

	public void setAcctTypeCode(String acctTypeCode) {
		this.acctTypeCode = acctTypeCode;
	}

	public String getSaleModelCode() {
		return saleModelCode;
	}

	public void setSaleModelCode(String saleModelCode) {
		this.saleModelCode = saleModelCode;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getClueSourceCode() {
		return clueSourceCode;
	}

	public void setClueSourceCode(String clueSourceCode) {
		this.clueSourceCode = clueSourceCode;

	}

	public String getEmpNo() {
		return empNo;
	}

	public void setEmpNo(String empNo) {
		this.empNo = empNo;
	}

	public String getEmpId() {
		return empId;
	}

	public void setEmpId(String empId) {
		this.empId = empId;
	}

	public String getClueMsg() {
		return clueMsg;
	}

	public void setClueMsg(String clueMsg) {
		this.clueMsg = clueMsg;
	}

	public String getOwnerNo() {
		return ownerNo;
	}

	public void setOwnerNo(String ownerNo) {
		this.ownerNo = ownerNo;
	}

	public String getPostType() {
		return postType;
	}

	public void setPostType(String postType) {
		this.postType = postType;
	}

	public String getBusinessTypeId() {
		return businessTypeId;
	}

	public void setBusinessTypeId(String businessTypeId) {
		this.businessTypeId = businessTypeId;
	}

	public String getAcctTypeId() {
		return acctTypeId;
	}

	public void setAcctTypeId(String acctTypeId) {
		this.acctTypeId = acctTypeId;
	}

	public String getSaleModelId() {
		return saleModelId;
	}

	public void setSaleModelId(String saleModelId) {
		this.saleModelId = saleModelId;
	}

	public String getClueSourceId() {
		return clueSourceId;
	}

	public void setClueSourceId(String clueSourceId) {
		this.clueSourceId = clueSourceId;
	}

	public String getChildTrade() {
		return childTrade;
	}

	public void setChildTrade(String childTrade) {
		this.childTrade = childTrade;
	}

	public String getChildTradeCode() {
		return childTradeCode;
	}

	public void setChildTradeCode(String childTradeCode) {
		this.childTradeCode = childTradeCode;
	}

	public String getParentTrade() {
		return parentTrade;
	}

	public void setParentTrade(String parentTrade) {
		this.parentTrade = parentTrade;
	}

	public String getParentTradeCode() {
		return parentTradeCode;
	}

	public void setParentTradeCode(String parentTradeCode) {
		this.parentTradeCode = parentTradeCode;
	}

	public String getLastAcctId() {
		return lastAcctId;
	}

	public void setLastAcctId(String lastAcctId) {
		this.lastAcctId = lastAcctId;
	}

	public String getLastAcctName() {
		return lastAcctName;
	}

	public void setLastAcctName(String lastAcctName) {
		this.lastAcctName = lastAcctName;
	}

	public String getChildTradeId() {
		return childTradeId;
	}

	public void setChildTradeId(String childTradeId) {
		this.childTradeId = childTradeId;
	}

	public String getParentTradeId() {
		return parentTradeId;
	}

	public void setParentTradeId(String parentTradeId) {
		this.parentTradeId = parentTradeId;
	}

	public String getIsTransfer() {
		return isTransfer;
	}

	public void setIsTransfer(String isTransfer) {
		this.isTransfer = isTransfer;
	}

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public String getAreaId() {
		return areaId;
	}

	public void setAreaId(String areaId) {
		this.areaId = areaId;
	}

	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	public String getIsAssigned() {
		return isAssigned;
	}

	public void setIsAssigned(String isAssigned) {
		this.isAssigned = isAssigned;
	}

	public String getReasonId() {
		return reasonId;
	}

	public void setReasonId(String reasonId) {
		this.reasonId = reasonId;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public String getReasonCode() {
		return reasonCode;
	}

	public void setReasonCode(String reasonCode) {
		this.reasonCode = reasonCode;
	}

	public List<ListOfValue> getReasonList() {
		return reasonList;
	}

	public void setReasonList(List<ListOfValue> reasonList) {
		this.reasonList = reasonList;
	}

	public String getTechMgrId() {
		return techMgrId;
	}

	public void setTechMgrId(String techMgrId) {
		this.techMgrId = techMgrId;
	}


	public String getTechMgrName() {
		return techMgrName;
	}

	public void setTechMgrName(String techMgrName) {
		this.techMgrName = techMgrName;
	}

	public String getTechMgrNo() {
		return techMgrNo;
	}

	public void setTechMgrNo(String techMgrNo) {
		this.techMgrNo = techMgrNo;
	}

	
	
}
