package com.zte.mcrm.channel.constant;

public enum OpportunityNotifyMsgEnum {
    /**
     * 商机创建最终用户生效
     */
    OPPORTUNITY_EMAIL_FOR_CUSTOMER_EFFECTED_REMINDER_EXTERNAL("channel.opportunity.edit.url"),
    /**
     * 商机创建最终用户不通过提醒
     */
    OPPORTUNITY_EMAIL_FOR_CUSTOMER_FAIL_REMINDER(""),
    /**
     * 商机创建最终用户待提交
     */
    OPPORTUNITY_EMAIL_FOR_CUSTOMER_SUBMITTING_REMINDER(""),
    /**
     * 商机创建最终用户驳回提醒
     */
    OPPORTUNITY_EMAIL_FOR_CUSTOMER_REJECT_REMINDER(""),
    /**
     * 商机创建最终用户失效提醒
     */
    OPPORTUNITY_EMAIL_FOR_CUSTOMER_INVALID_REMINDER("");

    final String mailUrl;

    public String getMailUrl() {
        return mailUrl;
    }

    OpportunityNotifyMsgEnum(String mailUrl) {
        this.mailUrl = mailUrl;
    }
}
