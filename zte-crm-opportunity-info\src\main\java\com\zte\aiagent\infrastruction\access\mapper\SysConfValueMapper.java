package com.zte.aiagent.infrastruction.access.mapper;

import com.zte.aiagent.infrastruction.access.po.SysConfValuePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 系统参数配置值表Mapper接口
 * 提供对系统参数值的数据库操作，删除操作采用逻辑删除
 */
@Mapper
public interface SysConfValueMapper {
    /**
     * 插入系统参数配置值记录
     * @param sysConfValue 系统参数配置值PO对象
     * @return 影响的行数
     */
    int insert(SysConfValuePO sysConfValue);

    /**
     * 批量插入系统参数配置值记录
     * @param items 系统参数配置值列表
     * @return 影响的行数
     */
    int batchInsert(@Param("items") List<SysConfValuePO> items);

    /**
     * 根据ID查询系统参数配置值
     * @param rowId 主键ID
     * @return 系统参数配置值PO对象
     */
    SysConfValuePO selectByPrimaryKey(String rowId);

    /**
     * 根据编码类型ID查询参数值列表
     * @param codeTypeId 编码类型ID
     * @param tenantId 租户ID
     * @return 系统参数配置值列表
     */
    List<SysConfValuePO> selectByCodeTypeId(
            @Param("codeTypeId") String codeTypeId,
            @Param("tenantId") Long tenantId);

    /**
     * 根据编码类型ID、代码和租户ID查询参数值
     * @param codeTypeId 编码类型ID
     * @param code 代码
     * @param tenantId 租户ID
     * @return 系统参数配置值PO对象
     */
    SysConfValuePO selectByTypeIdCodeAndTenant(
            @Param("codeTypeId") String codeTypeId,
            @Param("code") String code,
            @Param("tenantId") Long tenantId);

    /**
     * 根据父值ID查询子参数列表
     * @param parentId 父值ID
     * @param tenantId 租户ID
     * @return 系统参数配置值列表
     */
    List<SysConfValuePO> selectByParentId(
            @Param("parentId") String parentId,
            @Param("tenantId") Long tenantId);

    /**
     * 根据ID更新系统参数配置值
     * @param sysConfValue 系统参数配置值PO对象
     * @return 影响的行数
     */
    int updateByPrimaryKey(SysConfValuePO sysConfValue);

    /**
     * 逻辑删除系统参数配置值（将enabled_flag设为N）
     * @param rowId 主键ID
     * @param lastUpdatedBy 最后更新人
     * @param lastUpdatedDate 最后更新时间
     * @return 影响的行数
     */
    int logicDelete(
            @Param("rowId") String rowId,
            @Param("lastUpdatedBy") String lastUpdatedBy,
            @Param("lastUpdatedDate") Date lastUpdatedDate);

    /**
     * 批量逻辑删除系统参数配置值
     * @param rowIds 主键ID列表
     * @param lastUpdatedBy 最后更新人
     * @param lastUpdatedDate 最后更新时间
     * @return 影响的行数
     */
    int batchLogicDelete(
            @Param("rowIds") List<String> rowIds,
            @Param("lastUpdatedBy") String lastUpdatedBy,
            @Param("lastUpdatedDate") Date lastUpdatedDate);
}
