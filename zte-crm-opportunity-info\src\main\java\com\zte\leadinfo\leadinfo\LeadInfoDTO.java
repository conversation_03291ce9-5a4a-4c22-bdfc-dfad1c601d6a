package com.zte.leadinfo.leadinfo;

import com.zte.leadinfo.common.entity.CxApprOpHead;
import com.zte.leadinfo.common.entity.CxDocItemDO;
import com.zte.leadinfo.common.entity.CxDocumentDO;
import com.zte.leadinfo.leadinfo.entity.CxOpptyProdDO;
import com.zte.leadinfo.leadinfo.entity.CxOptyEmpDO;
import com.zte.leadinfo.leadinfo.entity.SOptyDO;
import com.zte.leadinfo.leadinfo.entity.SOptyXDO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * <AUTHOR> suntugui
 * @date 2024/6/4 10:26
 */
@Data
public class LeadInfoDTO {

    /**
     * 商机数据迁移老商机主表
     */
    private SOptyDO sOpty;

    /**
     * 商机数据迁移老商机扩展表
     */
    private SOptyXDO sOptyX;

    /**
     * 商机数据迁移老产品信息表
     */
    private List<CxOpptyProdDO> cxOpptyProds;

    /**
     * 商机数据迁移老团队成员表
     */
    private List<CxOptyEmpDO> cxOptyEmps;

    /**
     * 商机数据迁移老附件头表
     */
    private CxDocumentDO cxDocument;

    /**
     * 商机数据迁移老附件明细表
     */
    private List<CxDocItemDO> cxDocItems;

    /** 审批单据头，内含审批单明细 */
    private List<CxApprOpHead> cxApprOpHeads;

    /**
     * 获取客户ID
     * @return
     */
    public String getCustomerId() {
        return Optional.ofNullable(this.getSOpty()).orElse(new SOptyDO()).getPrDeptOuId();
    }

    /**
     * 获取最终客户ID
     * @return
     */
    public String getFinalCustomerId() {
        return Optional.ofNullable(this.sOptyX).orElse(new SOptyXDO()).getXLastAccId();
    }

    /**
     * 获取商机来源
     * @return
     */
    public String getOptSource() {
        return Optional.ofNullable(this.sOptyX).orElse(new SOptyXDO()).getOpptySource();
    }

    public Set<String> allEmpNoSet() {
        Set<String> allEmpNo = new HashSet<>();
        for (CxOptyEmpDO cxOptyEmpDO : this.getCxOptyEmps()) {
            allEmpNo.add(cxOptyEmpDO.getEmpId());
        }
        for (CxDocItemDO cxDocItemDO : this.getCxDocItems()) {
            allEmpNo.add(cxDocItemDO.getCreatedBy());
        }
        allEmpNo.removeIf(Objects::isNull);
        allEmpNo.removeIf(String::isEmpty);
        return allEmpNo;
    }
    public Set<String> allInnerEmp() {
        Set<String> allEmpNo = new HashSet<>();
        SOptyXDO sOptyXDO = Optional.ofNullable(this.getSOptyX()).orElse(new SOptyXDO());
        SOptyDO sOptyDO = Optional.ofNullable(this.getSOpty()).orElse(new SOptyDO());
        allEmpNo.add(sOptyDO.getCreatedBy());
        allEmpNo.add(sOptyXDO.getBusinessManagerId());
        allEmpNo.add(sOptyXDO.getDirectorOfPsc());
        allEmpNo.removeIf(Objects::isNull);
        allEmpNo.removeIf(String::isEmpty);
        return allEmpNo;
    }

    public Set<String> allCustomerNoSet() {
        Set<String> allCustomerId = new HashSet<>();
        allCustomerId.add(this.getCustomerId());
        allCustomerId.add(this.getFinalCustomerId());
        allCustomerId.removeIf(Objects::isNull);
        allCustomerId.removeIf(String::isEmpty);
        return allCustomerId;
    }

    // 如果主表bu_id不为空，就取bu_id；否则取值扩展表的dept_no
    public String getTree() {
        SOptyDO sOptyDO = this.getSOpty();
        SOptyXDO sOptyXDO = this.getSOptyX();
        if (StringUtils.isEmpty(sOptyDO.getBuId())) {
            return sOptyXDO.getDeptNo();
        } else {
            return sOptyDO.getBuId();
        }
    }

    /**
     * 获取所有的渠道商编码和ID，需要进行转换
     * @return
     */
    public Set<String> allCustomerCodeId() {
        Set<String> allCustomerId = new HashSet<>();
        allCustomerId.add(Optional.ofNullable(this.getSOpty()).orElse(new SOptyDO()).getPrDeptOuId());
        allCustomerId.add(Optional.ofNullable(this.getSOptyX()).orElse(new SOptyXDO()).getXLastAccId());
        allCustomerId.add(Optional.ofNullable(this.getSOptyX()).orElse(new SOptyXDO()).getCrmCustomerCode());
        allCustomerId.removeIf(Objects::isNull);
        allCustomerId.removeIf(String::isEmpty);
        return allCustomerId;
    }


}
