package com.zte.mcrm.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.client.util.TestFallbackUtils;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.mcrm.adapter.approval.service.ApprovalHeaderBuilder;
import com.zte.mcrm.adapter.constant.HeaderNameConst;
import com.zte.mcrm.common.consts.CommonConstant;
import com.zte.mcrm.common.consts.HttpMethodEnum;
import com.zte.mcrm.common.exception.BusinessRuntimeException;
import com.zte.mcrm.common.exception.RemoteServiceException;
import com.zte.mcrm.common.framework.exception.PrmRetCode;
import com.zte.mcrm.common.model.SysGlobalConstVo;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName: MicroServiceInvokeUtil
 * @Description: TODO
 * @Author: 朱正梁10266679
 * @Date: 2021/6/1 9:59
 **/
public class MicroServiceInvokeUtil {

    private static final Logger logger = LoggerFactory.getLogger(MicroServiceInvokeUtil.class);

    public static final String SERVICE_NAME_APPROVAL = "zte-iss-approval-manage";
    public static final String APPROVAL_VERSION = "v1";

    /** 服务调用成功返回码 */
    public static final String SUCCESS_CODE = "0000";

    private MicroServiceInvokeUtil() {
        // 禁止初始化
    }

    /**
     * 调用审批中心微服务
     * 服务返回数据类型为ServiceData, 且本方法返回ServiceData.bo
     *
     * @param url 操作URL(Class的RequestMapping + Method的RequestMapping)
     * @param methodType 服务请求类型
     * @param params 请求参数:可序列化为JSON格式的对象，或者基本类型
     * @param resultType 返回结果的数据类型
     * @param <T>
     * @return
     * @throws RouteException
     */
    public static <T> T invokeApprovalService(String url, HttpMethodEnum methodType, Object params,
                                              TypeReference<ServiceData<T>> resultType) {
        ApprovalHeaderBuilder approvalHeaderBuilder =
                (ApprovalHeaderBuilder) SpringContextUtil.getBean("ApprovalHeaderBuilder");
        Map<String, String> headerParams = new HashMap<>();
        if (null != approvalHeaderBuilder) {
            headerParams = approvalHeaderBuilder.getHeaderParams(headerParams());
        }
        Map<String, String> paramString = new HashMap<>(4);
        paramString.put("serviceName", SERVICE_NAME_APPROVAL);
        paramString.put("version", APPROVAL_VERSION);
        paramString.put("url", url);
        return invokeServiceAndReturnBO(paramString, methodType, params, resultType, headerParams);
    }

    /**
     * 是否为全路径
     * @param url
     * @return
     */
    private static boolean isAbsolutePath(String url) {
        return url.startsWith(CommonConstant.HTTP);
    }

    /**
     * 微服务调用<br/>
     * 服务返回数据类型为ServiceData, 且本方法返回ServiceData.bo
     * @param paramString 包含以下两个
     *  serviceName 服务名
     *  version 版本号
     *  url 操作URL(Class的RequestMapping + Method的RequestMapping)
     * @param methodType 服务请求类型
     * @param params 请求参数:可序列化为JSON格式的对象，或者基本类型
     * @param resultType 返回结果的数据类型
     * @return
     * @throws RouteException 返回结果非成功状态（“0000”）时抛此异常
     */
    public static <T> T invokeServiceAndReturnBO(Map<String, String> paramString,
                                                 HttpMethodEnum methodType, Object params, TypeReference<ServiceData<T>> resultType,Map<String, String> headerParams) {
        String serviceName = paramString.get("serviceName");
        String version = paramString.get("version");
        String url = paramString.get("url");
        String bodyParams = JSON.toJSONString(params);

        if (logger.isInfoEnabled()) {
            logger.info("MSB call [serviceName:{}, version:{}, url:{}, method:{}, bodyParams:{}, headerParams:{} ",
                    serviceName, version, url, methodType.getKey(), bodyParams, JSON.toJSONString(headerParams));
        }
        String response = MicroServiceRestUtil.invokeService(serviceName, version, methodType.getKey(), url, bodyParams, headerParams);

        logger.info("MSB call result:{} ", response);

        // 先用String类型取ServiceData.bo, 判断ServiceData.RetCode是否是成功的。
        ServiceData<String> rawData = JSON.parseObject(response, new TypeReference<ServiceData<String>>() {});

        RetCode retCode = rawData.getCode();

        if (!SUCCESS_CODE.equals(retCode.getCode())) {
            throw new RemoteServiceException(
                    retCode.getCode(), retCode.getMsgId(), retCode.getMsg(), rawData.getBo());
        }
        // 再用泛型类型解析BO数据
        ServiceData<T> serviceData = JSON.parseObject(response, resultType);

        return serviceData.getBo();
    }

    /**
     * url调用方式调用接口
     * 服务返回数据类型为ServiceData, 且本方法返回ServiceData.bo
     *
     * @param paramString 包含以下两个
     *  version 版本号
     *  url 操作URL(Class的RequestMapping + Method的RequestMapping)
     * @param methodType 服务请求类型
     * @param params 请求参数:可序列化为JSON格式的对象，或者基本类型
     * @param resultType 返回结果的数据类型
     * @return
     * @throws RouteException
     * @throws RemoteServiceException 返回结果非成功状态（“0000”）时抛此异常
     */
    public static <T> T invokeServiceAndReturnBoWithUrl(Map<String, String> paramString,
                                                        HttpMethodEnum methodType, Object params, TypeReference<ServiceData<T>> resultType) {
        String version = paramString.get("version");
        Map<String, String> headerParams = headerParams();
        Objects.requireNonNull(version, "version  can't be empty");
        Objects.requireNonNull(methodType, "sendType  can't be empty");
        headerParams.put(SysGlobalConst.HTTP_HEADER_X_TARGET_SERVICE_VERSION, version);

        return invokeServiceAndReturnBoWithUrl(paramString, methodType, params, resultType, headerParams);
    }

    /**
     * url调用方式调用接口
     * 服务返回数据类型为ServiceData, 且本方法返回ServiceData.bo
     *
     * @param paramString 包含以下两个
     *  version 版本号
     *  url 操作URL(Class的RequestMapping + Method的RequestMapping)
     * @param methodType 服务请求类型
     * @param params 请求参数:可序列化为JSON格式的对象，或者基本类型
     * @param resultType 返回结果的数据类型
     * @param headerParams 请求头上下文
     * @param <T>
     * @return
     */
    public static <T> T invokeServiceAndReturnBoWithUrl(Map<String, String> paramString, HttpMethodEnum methodType, Object params,
                                                        TypeReference<ServiceData<T>> resultType, Map<String, String> headerParams) {
        String result = "";
        String version = paramString.get("version");
        String url = paramString.get("url");

        try {
            result = getResult(url, methodType, params, headerParams);
        } catch (Exception e) {
            if (TestFallbackUtils.isProdEnv()) {
                throw new BusinessRuntimeException(e, PrmRetCode.EXTERNAL_SERVERERROR_CODE);
            } else {
                StringBuilder sb = new StringBuilder().append(url).append("_")
                        .append(version).append("_").append(StringUtils.upperCase(methodType.getKey()));
                String fallbackValue = TestFallbackUtils.getFallbackValue(sb.toString());
                if (!StringUtils.isEmpty(fallbackValue)) {
                    result = fallbackValue;
                } else {
                    throw new BusinessRuntimeException(e, PrmRetCode.EXTERNAL_SERVERERROR_CODE);
                }
            }
        }

        // 先用String类型取ServiceData.bo, 判断ServiceData.RetCode是否是成功的。
        ServiceData<String> rawData = JSON.parseObject(result, new TypeReference<ServiceData<String>>() {});

        RetCode retCode = rawData.getCode();

        if (!SUCCESS_CODE.equals(retCode.getCode())) {
            //            throw new RemoteServiceException(
            //                    retCode.getCode(), retCode.getMsgId(), retCode.getMsg(), rawData.getBo());
            throw new BusinessRuntimeException(PrmRetCode.EXTERNAL_SERVERERROR_CODE, rawData.getBo());
        }

        ServiceData<T> serviceData = JSON.parseObject(result, resultType);

        return serviceData.getBo();
    }

    public static String getResult(String url, HttpMethodEnum methodType, Object params, Map<String, String> headerParams) {
        if (HttpMethodEnum.POST.name().equals(StringUtils.upperCase(methodType.getKey()))) {
            return HttpClientUtil.httpPostWithJSON(url, JSON.toJSONString(params), headerParams);
        }
        if (HttpMethodEnum.PUT.name().equals(StringUtils.upperCase(methodType.name()))) {
            return HttpClientUtil.httpPutWithJSON(url, JSON.toJSONString(params), headerParams);
        }
        if (HttpMethodEnum.GET.name().equals(StringUtils.upperCase(methodType.name()))) {
            System.out.println(JSON.toJSONString(params));
            if (StringUtils.isBlank(JSON.toJSONString(params))) {
                params = "{}";
            }
            return HttpClientUtil.httpGet(url, (Map) JacksonJsonConverUtil.jsonToBean(JSON.toJSONString(params), Map.class),
                    headerParams);
        }
        if (HttpMethodEnum.DELETE.name().equals(StringUtils.upperCase(methodType.name()))) {
            return HttpClientUtil.httpDeleteWithJson(url, JSON.toJSONString(params), headerParams);
        }
        return "";
    }

    /**
     * 获取Header参数
     */
    public static Map<String, String> headerParams() {
        Map<String, String> result = Maps.newHashMap();
        SysGlobalConstVo clientHeader = CommonUtils.getSysGlobalConstVo();
        if (null == clientHeader) {
            return result;
        }
        result.put("X-Emp-No", StringUtils.isNotBlank(clientHeader.getxEmpNo())? clientHeader.getxEmpNo() :
                HeaderNameConst.DEFAULT_X_EMP_NO);
        result.put("X-Auth-Value", StringUtils.isNotBlank(clientHeader.getxAuthValue())? clientHeader.getxAuthValue()
                : HeaderNameConst.DEFAULT_X_AUTH_VALUE);
        result.put("X-Lang-Id", clientHeader.getxLangId());
        result.put("X-Tenant-Id", StringUtils.isNotBlank(clientHeader.getxTenantId())? clientHeader.getxTenantId()
                : HeaderNameConst.DEFAULT_X_TENANT_ID);
        result.put("X-Org-Id", StringUtils.isNotBlank(clientHeader.getxOrgId())? clientHeader.getxOrgId()
                : HeaderNameConst.DEFAULT_X_ORG_ID);
        result.put("X-Origin-ServiceName", clientHeader.getxOriginServiceName());
        result.put("X-Itp-Value", clientHeader.getxItpValue());
        return result;
    }
}
