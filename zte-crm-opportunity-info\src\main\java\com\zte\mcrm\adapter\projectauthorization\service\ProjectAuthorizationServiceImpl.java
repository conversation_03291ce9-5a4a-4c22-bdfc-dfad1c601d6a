package com.zte.mcrm.adapter.projectauthorization.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.mcrm.adapter.constant.HeaderNameConst;
import com.zte.mcrm.adapter.projectauthorization.dto.OpportunityAuthDto;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.mcrm.common.util.RequestMessage;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import com.zte.springbootframe.common.model.HttpResultData;
import com.zte.springbootframe.common.serviceregister.MicroServiceWithConfigUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ProjectAuthorizationServiceImpl implements ProjectAuthorizationService{

    static private Logger logger = LoggerFactory.getLogger(ProjectAuthorizationServiceImpl.class);

    private static final String AUTH_REQUEST_URL = "/app/APP1037381255928217600/custom/opportunity/list";

    /**
     * 根据商机编号获取项目授权信息
     *
     * @param optyCds 商机编号列表
     * @return
     */
    @Override
    public List<OpportunityAuthDto> getProjAuthInfosByOptyCd(List<String> optyCds) throws Exception{

        HttpResultData httpResult = MicroServiceWithConfigUtil.invokeServiceWithPostMethod("12", AUTH_REQUEST_URL, optyCds, getHeaderMap());

        if( httpResult.getBo()!=null) {
            String rtnStr = JacksonJsonConverUtil.beanToJson(httpResult.getBo());
            logger.info("getProjAuthInfosByOptyCd,return:{}",rtnStr);
            return JacksonJsonConverUtil.jsonToListBeanOther(rtnStr, new TypeReference<List<OpportunityAuthDto>>() {
            });
        }
        return Collections.emptyList();
    }

    private Map<String, String> getHeaderMap() {
        Map<String, String> headerMap = new HashMap<>(8);
        String tenantId = CommonUtils.getTenantId();
        if(StringUtils.isBlank(tenantId)){
            tenantId = HeaderNameConst.DEFAULT_X_TENANT_ID;
        }
        headerMap.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO, CommonUtils.getEmpNo());
        headerMap.put(SysGlobalConst.HTTP_HEADER_X_ACCOUNT_ID, CommonUtils.getEmpNo());
        headerMap.put(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE, RequestMessage.getToken());
        headerMap.put(SysGlobalConst.HTTP_HEADER_X_TENANT_ID, tenantId);
        return headerMap;
    }
}
