package com.zte.aiagent.domain.valueobject;

import lombok.Value;
import org.apache.commons.lang3.StringUtils;

/**
 * 文档信息值对象
 * 封装文档的基本信息
 */
@Value
public class DocumentInfo {

    /** 文件名 */
    String fileName;

    /** 文件大小（字节） */
    Long fileSize;

    /** 文件类型 */
    String fileType;

    /** 文档云fileKey */
    String fileKey;

    /**
     * 工厂方法
     */
    public static DocumentInfo of(String fileName, Long fileSize, String fileType, String fileKey) {
        if (StringUtils.isBlank(fileName)) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        if (fileSize == null || fileSize < 0) {
            throw new IllegalArgumentException("文件大小不能为空或负数");
        }
        if (StringUtils.isBlank(fileKey)) {
            throw new IllegalArgumentException("文档云fileKey不能为空");
        }

        return new DocumentInfo(fileName, fileSize, fileType, fileKey);
    }
}
