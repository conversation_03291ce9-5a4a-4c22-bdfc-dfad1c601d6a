package com.zte.mcrm.common.framework.exception;

/**
 * 错误码接口类<br>
 * 约束:实现类只允许是枚举类<br>
 * 分模块-功能用枚举类命名区分
 * 错误码定义规则: <href> https://i.zte.com.cn/#/space/4d5adfe966fb498da6c9406c4f22e23e/wiki/page/0f0690b7c33d42b79b2e3d185ed1107a/view</href><br>
 * 1、总体规范<br>
 * 错误码为字母数字类型，共6位，分成5个部分：第一位标识模块+ 第二位标识功能 + 第三位标识错误产生来源 + 第四五位错误编号 + 第六位错误级别<br>
 * 前端展示时截取前5位展示，第六位错误级别不展示。<br>
 * 第一二位模块功能标识：<br>
 * 各模块功能编码维护<br>
 * 错误产生来源：<br>
 * 2 表示错误来源于用户（客户端），比如输入参数错误，无权限、访问被拦截等问题;<br>
 * 3 表示错误来源于当前系统，往往是业务逻辑出错，或程序健壮性差等问题; <br>
 * 4 表示错误来源于第三方服务，比如 第三方服务出错、第三方系统超时、通知服务出错等问题;<br>
 * 第四五位是对应模块功能下的唯一错误编号，二位数字编号从 10 到 99。<br>
 * 错误级别：目前约定取值：1 INFO，2 WARN，3 ERROR，4 FATAL <br>
 * 2、具体示例<br>
 * 错误码： RA3103<br>
 * 其中第一位R 标识注册模块，第二位A 表示渠道商注册功能，第三位“3”表示当前系统业务逻辑错误，第四五位10表示错误场景： 渠道商注册，营业执照key为空 。组合起来RA3103作为一个错误码。
 *
 * @Author: <EMAIL>
 * @Date: 2021/12/28
 * @Description: 错误码接口类
 */
public interface ErrorCode {
    /**
     * 获取枚举对象
     *
     * @return 枚举对象
     */
    Enum get();

    /**
     * 返回枚举值的name
     *
     * @return 返回枚举值的name(错误码)
     */
    String code();

    /**
     * 同一模块下公共功能错误码
     */
    enum ComFuncitonErrorEnum implements ErrorCode {
        /**
         * 不区分功能的框架@validator注解校验错误码
         */
        OV2042,
        /**
         *数据库运行时异常SQLException
         */
        OQ3063,
        /**
         *不区分功能的业务运行时异常BusinessRuntimeException
         */
        OY3053,
        /**
         *不区分功能的程序异常Exception
         */
        OY3014;


        /**
         * 获取枚举对象
         *
         * @return 枚举对象
         */
        @Override
        public Enum get() {
            return this;
        }

        /**
         * @return 返回枚举值的name(错误码)
         */
        @Override
        public String code() {
            return this.name();
        }
    }

    /**
     * 通用模块
     */
    enum UniversalErrorEnum implements ErrorCode {
        /**
         * 组织调整任务
         */
        OU3103,
        /**
         * 组织调整任务逐行执行
         */
        OU3113,

        /**
         * 获取任务列表失败
         */
        OU3123,

        /**
         * 记录保存异常
         */
        OU3133,

        /**
         * 记录更新异常
         */
        OU3143,

        /**
         * 创建客户失败
         */
        OU3153,

        /**
         * 查询城市编码失败
         */
        OU3163,

        /**
         * 查询不到公司:{0}的信息
         */
        OU3173,

        /**
         * 查询不到匹配的客户系统行业-子行业:{0}-{1}
         */
        OU3183,

        /**
         * 查询客户信息失败
         */
        OU3193,

        /**
         * 创建客户异常:{0}
         */
        OU3012;

        @Override
        public Enum get() {
            return this;
        }

        @Override
        public String code() {
            return this.name();
        }
    }

}
