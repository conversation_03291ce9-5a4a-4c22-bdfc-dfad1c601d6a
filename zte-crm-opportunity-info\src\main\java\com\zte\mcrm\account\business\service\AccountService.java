/**
 *  Copyright  2018 赵世光 All rights reserved.
 *  @author: ********** 
 *  @date: 2018年4月8日 下午3:23:41 
 */
package com.zte.mcrm.account.business.service;

import com.zte.mcrm.account.access.vo.Account;
import com.zte.mcrm.account.access.vo.CreditVO;

/**  
 * <p>Title: AccountService</p>  
 * <p>Description: </p>  
 * <AUTHOR> <PERSON><PERSON>hi<PERSON>uang
 * @date 2018年4月8日  
 */
public interface AccountService {
	/**
	 * <p>根据客户ID调用Http服务获取客户</p> 
	 * <p>Description: </p>  
	 * @param accountId
	 * @return  
	 * <AUTHOR> ZhaoShiGuang
	 * @date 2018年4月8日
	 */
	public Account getAccount(String accountId);
	/**
	 * 根据客户ID调用客户服务获取信用评级
	 * @param accountId
	 * @return  
	 * <AUTHOR> ZhaoShiGuang
	 * @date 2018年4月8日
	 */
	public CreditVO getAccountCredit(String accountId);

}
