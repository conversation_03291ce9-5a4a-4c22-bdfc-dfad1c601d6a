package com.zte.mcrm.common.errorcode.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
* 公共异常日志 实体类
* <AUTHOR> 10269210
* @date 2022/01/06
*/

@Setter @Getter @ToString
@ApiModel(description="公共异常日志")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ComExceptionLog implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    private Long id;
    @ApiModelProperty(value = "服务")
    private String service;
    @ApiModelProperty(value = "模块")
    private String module;
    @ApiModelProperty(value = "功能")
    private String features;
    @ApiModelProperty(value = "请求服务名")
    private String reqServiceName;
    @ApiModelProperty(value = "请求方法")
    private String reqMethod;
    @ApiModelProperty(value = "请求路径")
    private String reqUrl;
    @ApiModelProperty(value = "请求路径参数")
    private String queryParams;
    @ApiModelProperty(value = "请求头")
    private String reqHeaders;
    @ApiModelProperty(value = "请求body")
    private String reqBody;
    @ApiModelProperty(value = "请求响应")
    private String response;
    @ApiModelProperty(value = "错误码")
    private String errorCode;
    @ApiModelProperty(value = "错误消息")
    private String errorMessage;
    @ApiModelProperty(value = "重试标识")
    private String retryFlag;
    @ApiModelProperty(value = "重试次数")
    private Long retryLimitTimes;
    @ApiModelProperty(value = "异常信息")
    private String exceptionContent;
    @ApiModelProperty(value = "扩展字段1")
    private String expand1;
    @ApiModelProperty(value = "扩展字段2")
    private String expand2;
    @ApiModelProperty(value = "扩展字段3")
    private String expand3;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createdDate;
    @ApiModelProperty(value = "创建人")
    private String createdBy;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpdatedDate;
    @ApiModelProperty(value = "最后更新人")
    private String lastUpdatedBy;
    @ApiModelProperty(value = "是否有效")
    private String enabledFlag;

}