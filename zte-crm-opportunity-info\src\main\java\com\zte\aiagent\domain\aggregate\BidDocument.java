package com.zte.aiagent.domain.aggregate;

import com.zte.aiagent.common.util.IdUtils;
import com.zte.aiagent.domain.event.DocumentCreatedEvent;
import com.zte.aiagent.domain.valueobject.DocumentInfo;
import com.zte.aiagent.domain.enums.ParseStatusEnum;
import com.zte.aiagent.domain.shared.event.DomainEvent;
import com.zte.aiagent.domain.shared.valueobject.AuditInfo;
import com.zte.aiagent.domain.shared.valueobject.Tenant;
import com.zte.aiagent.domain.valueobject.ExportParsedFileKey;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 招标文档聚合根
 * 核心职责：文档生命周期管理，解析状态控制，业务规则封装
 */
@Getter
public class BidDocument {

    /**
     * 聚合根标识
     */
    private final String documentId;

    /**
     * 核心业务属性
     */
    private final DocumentInfo documentInfo;
    private ParseStatusEnum parseStatus;
    private final String parseTemplateCode;
    private final String parseTemplateId;

    /**
     * 解析相关属性
     */
    private LocalDateTime parseStartTime;
    private LocalDateTime parseEndTime;

    /**
     * 导出解析结果的文件key
     */
    private final ExportParsedFileKey exportParsedFileKey;

    /**
     * 通用属性
     */
    private AuditInfo auditInfo;
    private final Tenant tenantId;

    /**
     * 领域事件
     */
    private final List<DomainEvent> domainEvents = new ArrayList<>();

    /**
     * 私有构造函数，用于创建新的聚合根
     */
    public BidDocument(String documentId,
                       DocumentInfo documentInfo,
                       String parseTemplateCode,
                       String parseTemplateId,
                       ParseStatusEnum parseStatus,
                       LocalDateTime parseStartTime,
                       LocalDateTime parseEndTime,
                       AuditInfo auditInfo,
                       Tenant tenantId,
                       ExportParsedFileKey exportParsedFileKey) {
        this.documentId = documentId;
        this.documentInfo = documentInfo;
        this.parseTemplateCode = parseTemplateCode;
        this.parseTemplateId = parseTemplateId;
        this.parseStatus = parseStatus;
        this.parseStartTime = parseStartTime;
        this.parseEndTime = parseEndTime;
        this.auditInfo = auditInfo;
        this.tenantId = tenantId;
        this.exportParsedFileKey = exportParsedFileKey;
    }

    /**
     * 工厂方法 - 创建新文档
     */
    public static BidDocument create(DocumentInfo documentInfo,
                                     String parseTemplateCode,
                                     String parseTemplateId,
                                     Tenant tenantId,
                                     String operator) {

        String documentId = IdUtils.generateNewId();
        BidDocument document = new BidDocument(
                documentId,
                documentInfo,
                parseTemplateCode,
                parseTemplateId,
                ParseStatusEnum.PENDING,
                null,
                null,
                AuditInfo.create(operator),
                tenantId,
                null
        );

        // 发布创建事件
        document.addDomainEvent(new DocumentCreatedEvent(
                documentId,
                documentInfo,
                parseTemplateCode,
                tenantId.getValue()
        ));

        return document;
    }

    /**
     * 开始解析
     */
    public void startParse(String operator) {
        if (!this.parseStatus.canStartParse()) {
            throw new IllegalStateException("当前状态不允许开始解析");
        }

        this.parseStatus = ParseStatusEnum.PARSING;
        this.parseStartTime = LocalDateTime.now();
        this.auditInfo = this.auditInfo.updateBy(operator);

        // TODO: 发布解析开始事件
    }

    /**
     * 完成解析
     */
    public void completeParse(int extractedCount, String operator) {
        if (!this.parseStatus.isParsing()) {
            throw new IllegalStateException("当前状态不允许完成解析");
        }

        this.parseStatus = ParseStatusEnum.SUCCESS;
        this.parseEndTime = LocalDateTime.now();
        this.auditInfo = this.auditInfo.updateBy(operator);

        // TODO: 发布解析完成事件
    }

    /**
     * 解析失败
     */
    public void failParse(String errorMessage, String operator) {
        if (!this.parseStatus.isParsing()) {
            throw new IllegalStateException("当前状态不允许标记为失败");
        }

        this.parseStatus = ParseStatusEnum.FAILED;
        this.parseEndTime = LocalDateTime.now();
        this.auditInfo = this.auditInfo.updateBy(operator);

        // TODO: 发布解析失败事件
    }

    /**
     * 获取领域事件并清空
     */
    public List<DomainEvent> getDomainEventsAndClear() {
        List<DomainEvent> events = new ArrayList<>(this.domainEvents);
        this.domainEvents.clear();
        return events;
    }

    /**
     * 添加领域事件
     */
    private void addDomainEvent(DomainEvent event) {
        this.domainEvents.add(event);
    }
}
