package com.zte.mcrm.adapter.model.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * 法人实体
 *
 * <AUTHOR>
 * @date 2025.03.22 下午2:34
 */
@Getter
@Setter
public class BizLegalEntityVO {
    /**
     * 租户ID
     */
    private  Long tenantId;
    /**
     *法人名称
     */
    private String legalPersonName;
    /**
     * 法人英文名称
     */
    private String legalPersonEnglishName;
    /**
     * 法人编码
     */
    private String legalCode;
    /**
     * 法人唯一ID
     */
    private String legalId;
    /**
     *  企业统一信用代码
     */
    private String creditCode;
    /**
     * 中文简称
     */
    private String chineseShortName;
    /**
     * 英文简称
     */
    private String englishShortName;
    /**
     *
     */
    private String legalStatus;
    /**
     *
     */
    private String enabledFlag;
    /**
     * 扩展字段1 公司ID
     */
    private String attributes1;
    private String attributes2;
    private String attributes3;
}
