package com.zte.leadinfo.leadinfo.controller;


import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import com.zte.leadinfo.leadinfo.LeadInfoRepository;
import com.zte.mcrm.account.access.vo.Account;
import com.zte.mcrm.common.util.AccountUtil;
import com.zte.springbootframe.common.exception.BusiException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(value = "客户信息存储", tags = "客户信息存储")
@RestController
@RequestMapping("/customer")
public class CustomerSaveController {

    @Autowired
    LeadInfoRepository leadInfoRepository;

    @ApiOperation("保存客户信息")
    @GetMapping("/saveCustomer")
    public ServiceData<Boolean> saveCustomer() throws BusiException, InterruptedException {
        Boolean result = leadInfoRepository.saveBatchCustomer();
        return ServiceResultUtil.success(result);
    }

    @ApiOperation("查询缓存")
    @GetMapping("/queryRedis")
    public ServiceData<String> queryRedis(@RequestParam("key") String key) {
        String result = leadInfoRepository.queryRedis(key);
        return ServiceResultUtil.success(result);
    }

    @ApiOperation("批量处理客户同步，指定ID，小于10个参数")
    @PostMapping("/saveCustomerByIdList")
    public ServiceData<Boolean> saveCustomerByIdList(@RequestBody List<String> idList) {
        Boolean result = leadInfoRepository.saveCustomerByIdList(idList);
        return ServiceResultUtil.success(result);
    }

    @ApiOperation("查询客户信息")
    @PostMapping("/queryCustomerList")
    public ServiceData<List<Account>> queryCustomerList(@RequestBody List<String> idList) throws BusiException {
        List<Account> accountList = AccountUtil.getAccountListByids(idList);
        return ServiceResultUtil.success(accountList);
    }
}
