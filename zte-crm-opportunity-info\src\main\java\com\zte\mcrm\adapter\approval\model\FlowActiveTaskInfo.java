package com.zte.mcrm.adapter.approval.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description: 流程活跃任务信息
 * @author: 10243305
 * @date: 2021/7/23 下午4:10
 */
@Data
public class FlowActiveTaskInfo {
    @ApiModelProperty(value = "流程编码")
    private String flowCode;

    @ApiModelProperty(value = "流程实例ID")
    private String flowInstanceId;

    @ApiModelProperty(value = "关联业务ID")
    private String businessId;

    @ApiModelProperty(value = "节点类型")
    private String nodeType;

    @ApiModelProperty(value = "节点名称")
    private String nodeName;

    @ApiModelProperty(value = "流程当前活跃的任务")
    private List<ApprovalTaskInfo> activeTasks;
}
