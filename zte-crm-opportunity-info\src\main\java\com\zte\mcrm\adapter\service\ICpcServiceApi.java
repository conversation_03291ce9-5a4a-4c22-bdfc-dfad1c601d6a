package com.zte.mcrm.adapter.service;

import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.adapter.model.dto.*;
import com.zte.mcrm.adapter.model.vo.CompanySnapshotVO;
import com.zte.mcrm.adapter.model.vo.PartnerListVO;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 描述：合作伙伴API
 * 创建时间：2021/9/16
 *
 * @author：王丹凤6396000572
 */
public interface ICpcServiceApi {

    /**
     * 法人模糊查询服务
     * @param form
     * @return
     * @throws BusiException
     * @throws RouteException
     */
    PageRows<CompanyInfoDTO> queryCompanyInfoList(@RequestBody FormData<CompanyInfoSearchDTO> form) throws BusiException, RouteException;

    CompanyOrgInfoDTO queryOrgInfoByKeyword(CompanyInfoSearchDTO companyInfoSearch) throws BusiException, RouteException;

    CompanySnapshotVO querySnapshot(CompanyInfoSearchDTO companyInfoSearch) throws BusiException, RouteException;

        /**
         * 法人基本信息查询服务
         * @param orgUnifiedCode
         * @return
         * @throws BusiException
         * @throws RouteException
         */
    CompanyInfoDTO searchCompanyInfo(String orgUnifiedCode) throws BusiException, RouteException;

    /**
     * 批量黑黄名单查询服务
     * @param entity
     * @return
     * @throws Exception
     */
    PageRows<BlackListDTO> getBlackListBatch(CompanyBatchInfoDTO entity) throws Exception;

    /**
     * 合作伙伴列表查询服务
     * @param formData
     * @return
     * @throws BusiException
     * @throws RouteException
     */
    PageRows<PartnerListVO> partnerObscureQuery(FormData<PartnerObscureQueryDTO> formData) throws RouteException, BusiException;

}
