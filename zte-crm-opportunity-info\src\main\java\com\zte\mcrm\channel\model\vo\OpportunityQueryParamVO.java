package com.zte.mcrm.channel.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 描述：
 * 创建时间：2021/9/22
 *
 * @author：王丹凤6396000572
 */
@Data
public class OpportunityQueryParamVO {

    /**
     * 中兴业务经理工号
     */
    private String shortNo;
    /**
     * 渠道商客户编码
     */
    private String crmCustomerCode;
    /**
     * 渠道商名称（非必填）,模糊匹配
     */
    private String channelName;
    /**
     * 商机编号（非必填）,模糊匹配
     */
    private String optyCd;
    /**
     * 商机名称（非必填）,模糊匹配
     */
    private String optyName;
    /**
     * 当前页
     */
    private int currentPage = 1;
    /**
     * 页大小
     */
    private int pageSize = 10;

    private int startIndex = 0;

    @ApiModelProperty(value = "商机状态")
    private List<String> statusCd;

    @ApiModelProperty(value = "项目所属部门")
    private String deptNo;

    @ApiModelProperty(value = "最终用户行业")
    private String finalCustomerChildTrade;

    @ApiModelProperty(value = "报备开始时间")
    private String createdTimeStart;

    @ApiModelProperty(value = "报备结束时间")
    private String createdTimeEnd;

    @ApiModelProperty(value = "商机编号")
    private List<String> optyCdList;

    @ApiModelProperty(value = "商机来源")
    private String dataSource;


}
