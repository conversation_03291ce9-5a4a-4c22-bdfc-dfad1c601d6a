package com.zte.crm.eva.base.domain.pseudocode.model;

import java.util.Map;

/**
 * <AUTHOR>
 * 传值给drools规则引擎
 */
public class MapBO {
    private Map<String, Object> param;

    public Map<String, Object> getParam() {
        return param;
    }

    @Override
    public String toString() {
        return "MapBO{" +
                "param=" + param +
                ", result=" + result +
                '}';
    }

    public void setParam(Map<String, Object> param) {
        this.param = param;
    }

    public RuleResultBO getResult() {
        return result;
    }

    public void setResult(RuleResultBO result) {
        this.result = result;
    }

    private RuleResultBO result;

    public MapBO() {}

    public MapBO(Map<String, Object> param, RuleResultBO result) {
        this.param = param;
        this.result = result;
    }
}
