
package com.zte.mcrm.adapter.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ChannelCustomerRes {
    
    @ApiModelProperty(value = "业务系统申请成为合作伙伴的编码")
    private String beid;
    @ApiModelProperty(value = "CRM客户编码")
    private String crmCustomerCode;
    @ApiModelProperty(value = "CRM客户ID")
    private String crmCustomerId;
    @ApiModelProperty(value = "公司注册表主键ID")
    private String customerId;
    @ApiModelProperty(value = "公司名称")
    private String customerName;
    @ApiModelProperty(value = "合作")
    private String customerType;
    @ApiModelProperty(value = "有效标记")
    private String enabledFlag;
    @ApiModelProperty(value = "1国内/2国际")
    private String isNational;
    @ApiModelProperty(value = "合作伙伴中心生成的企业统一代码,国内以CN开头，国际以DNS开头")
    private String orgUnifiedCode;
    @ApiModelProperty(value = "合作伙伴中心黑名单标签")
    private String partnerBlackFlag;
    @ApiModelProperty(value = "合作伙伴中心生成的唯一编码,以1开头的10位流水号")
    private String partnerCode;
    @ApiModelProperty(value = "合作伙伴中心受限制主体标签")
    private String partnerGtsFlag;
    @ApiModelProperty(value = "注册日期")
    private String registedDate;
    @ApiModelProperty(value = "员工总人数")
    private String staffNum;
    @ApiModelProperty(value = "单据状态 2、审批中3、审批不通过4、审批通过 9、已失效")
    private String status;
}
