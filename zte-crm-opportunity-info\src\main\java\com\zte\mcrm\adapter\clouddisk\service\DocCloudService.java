package com.zte.mcrm.adapter.clouddisk.service;

import org.apache.http.entity.mime.content.ByteArrayBody;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;


/**
 * api主要接口示例service
 * <AUTHOR>
 */
public interface DocCloudService {

	/**
	 * 文件上传（包含4个流程）
	 * @param userId 用户名
	 * @param file
	 * @return 文件Key
	 * @throws Exception
	 */
	String upload(String userId, MultipartFile file) throws Exception;


    /**
     * 服务器端分片上传
     * @param fileMd5 文件上传标识
     * @param filePath 文件路径
     * @param userId 工号
     * @throws Exception
     */
     void uploadFile(String fileMd5, String filePath, String userId)throws Exception;

	/**
	 * 获取下载文档云文件URL
	 * @param docKey 文档云文件key值
	 * @param userId 用户Id
	 * @param downloadNum
	 * @param httpsFlag 是否返回https
	 * @return 文档云文件下载URL
	 * @throws Exception
	 */
	String getDownloadLink(String docKey, String userId, String downloadNum, boolean httpsFlag) throws Exception;

	/**
	 * 批量获取附件一次性下载链接-返回Map<K,V> K-dmeKey;V-一次性下载链接
	 * @param dmeKeys 文档云文件key值列表
	 * @param userId 用户Id
	 * @param onceTime
	 * @return 文档云文件下载URL
	 * @throws Exception
	 */
	Map<String, String> getDownloadLinks(List<String> dmeKeys, String userId, String onceTime) throws Exception;

	/**
	 * 下载文档云文件
	 * @param docKey 文档云文件key值
	 * @param userId 用户Id
	 * @return 浏览器下载
	 * @throws Exception
	 */
	ByteArrayBody  download(String docKey, String userId) throws Exception;

	/**
	 * 文件预览
	 *
	 * @param docKey 文档云key
	 * @param fileName 文件名
	 * @return
	 * <AUTHOR>
	 * @date 2021/7/29
	 */
	String previewFile(String docKey, String fileName);
}
