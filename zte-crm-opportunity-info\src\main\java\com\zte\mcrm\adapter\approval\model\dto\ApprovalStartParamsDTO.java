package com.zte.mcrm.adapter.approval.model.dto;

import lombok.Data;

import java.util.Map;

/**
 * @description: 流程启动参数
 * @author: 10243305
 * @date: 2021/6/23 下午4:51
 */
@Data
public class ApprovalStartParamsDTO {
    /** 应用编码 */
    private String appCode;
    /** 业务id */
    private String businessId;
    /** 流程发布id */
    private String flowInstanceId;
    /** 流程编码 */
    private String flowCode;
    /** 流程启动参数 */
    private Map<String, Object> params;
    /** 版本 */
    private String version;
}
