package com.zte.mcrm.common.util;

import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.mcrm.user.access.vo.SiebelUser;
import com.zte.mcrm.user.business.service.SiebelUserService;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import com.zte.springbootframe.common.exception.BusiException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> zhaoshiguang 2017年8月28日 上午11:31:28
 */
public class RequestMessage {
    /**
     * 公共服务用户语言简体中文识别标志
     */
    public static final String ZH_CN = "zh_CN";

    public static String getCurrentEmpNo() {
        return CommonUtils.getEmpNo();
    }

    public static String getUserLang() {
        return CommonUtils.getxLangId();
    }

    /**
     * 获取当前请求的用户工号
     *
     * @return
     * <AUTHOR> ZhaoShiGuang
     * @date 2018年9月19日
     */
    public static String getEmpNo() {
        return CommonUtils.getEmpNo();
    }

    public static String getToken() {
        return CommonUtils.getAuthValue();
    }

    /**
     * 获取当前请求人的ID
     *
     * @return
     * @throws BusiException
     * <AUTHOR> ZhaoShiGuang
     * @date 2018年4月19日
     */
    public static String getCurrentUserId() throws BusiException {
        String empNo = getEmpNo();
        SiebelUserService siebelUserService = (SiebelUserService) SpringContextUtil.getBean("siebelUserService");
        SiebelUser user;
        try {
            user = siebelUserService.findOneByLogin(empNo);
        } catch (Exception e) {
            throw new BusiException("", e.getMessage());
        }
        return user.getId();
    }

    public static Map<String, String> getHeader(String tenantId) {
        tenantId = tenantId == null ? "pcOpportunity" : tenantId;
        //请求头
        Map<String, String> headerParamsMap = new HashMap<>(4);
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_TENANT_ID, tenantId);
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_LANG_ID, CommonUtils.getxLangId());
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO, CommonUtils.getEmpNo());
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE, CommonUtils.getAuthValue());
        return headerParamsMap;
    }

    public static Map<String, String> getHeaderExt(String tenantId) {
        tenantId = tenantId == null ? CommonUtils.getTenantId() : tenantId;
        //请求头
        Map<String, String> headerParamsMap = new HashMap<>(4);
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_TENANT_ID, tenantId);
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_LANG_ID, CommonUtils.getxLangId());
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO, CommonUtils.getEmpNo());
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE, CommonUtils.getAuthValue());
        return headerParamsMap;
    }

}
