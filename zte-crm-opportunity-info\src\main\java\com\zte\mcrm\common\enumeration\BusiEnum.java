package com.zte.mcrm.common.enumeration;

import com.zte.springbootframe.util.local.LocalMessageUtils;

/**
 * 
 * <AUTHOR> 2017年8月22日 下午5:29:25
 *
 */
public enum BusiEnum {

	/**状态不允许新增*/
	StatusNotAllowAdd("statusError",LocalMessageUtils.getMessage("oppertunity.do.not.add")),

	/**当前状态无法转立项*/
	toPrjStatusNotAllowAdd("toPrjStatusError",LocalMessageUtils.getMessage("oppertunity.can.not.conversion.project")),

	/**您的权限不足*/
	anthNotAllowAdd("anthNotAllowAdd",LocalMessageUtils.getMessage("oppertunity.auto.can.enough"));
	
	private  BusiEnum(String code,String msg){
		this.code = code;
		this.msg = msg;
	}
	private String code;
	private String msg;
	
	public String getCode() {
		return code;
	}
	public String getMsg() {
		return msg;
	}
	
	

}
