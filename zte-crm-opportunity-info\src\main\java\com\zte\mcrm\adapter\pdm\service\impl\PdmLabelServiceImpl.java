package com.zte.mcrm.adapter.pdm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.TypeReference;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.pdm.constant.PdmConstant;
import com.zte.mcrm.adapter.pdm.model.PdmProductTreeDataDto;
import com.zte.mcrm.adapter.pdm.model.PdmProductTreeVO;
import com.zte.mcrm.adapter.pdm.service.IPdmLabelService;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.common.access.vo.ComDictionaryMaintainVO;
import com.zte.mcrm.common.business.service.IComDictionaryMaintainService;
import com.zte.mcrm.common.consts.HttpMethodEnum;
import com.zte.mcrm.common.util.MsaUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PdmLabelServiceImpl implements IPdmLabelService {

    @Resource
    IComDictionaryMaintainService dictionary;

    @Value("${product.id.zte:0}")
    String zteProductId;

    @Value("${product.id.system:20}")
    String zteSystemProductId;



    @Override
    public List<PdmProductTreeVO> queryProductClassTree(boolean includeDisabled) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("includeDisabled", includeDisabled);
        List<PdmProductTreeVO> pdmProductTreeVOS = new ArrayList<>();
        try {
            Map<String, String> paramString = new HashMap<>(3);
            paramString.put("serviceName", PdmConstant.PDM_LABEL_SERVICE_NAME);
            paramString.put("version", "v1");
            paramString.put("url", PdmConstant.QUERY_PRODUCT_CLASS_TREE_URL);
            List<PdmProductTreeDataDto> pdmProductTreeDataDto = MsaUtils.invokeService(paramString, HttpMethodEnum.GET,
                    params,
                    new TypeReference<ServiceData<List<PdmProductTreeDataDto>>>() {
                    });
            if (CollectionUtil.isNotEmpty(pdmProductTreeDataDto)) {
                pdmProductTreeVOS = pdmProductTreeDataDto.get(0).getData();
            }
            return pdmProductTreeVOS;
        } catch (Exception e) {
            log.error("PdmLabelServiceImpl queryProductClassTree error:", e);
            return pdmProductTreeVOS;
        }
    }

    @Override
    public List<PdmProductTreeVO> queryProductClassTreeFilterByInnerClass(List<String> innerClassNames) {
        List<PdmProductTreeVO> pdmProductTreeVOS = queryProductClassTree(false);
        if (CollectionUtil.isEmpty(pdmProductTreeVOS) || CollectionUtil.isEmpty(innerClassNames)) {
            return pdmProductTreeVOS;
        }
        // 过滤中兴系统产品列表
        List<PdmProductTreeVO> zteSystemProductTree = pdmProductTreeVOS.stream()
                .filter(vo -> zteProductId.equals(vo.getId()))
                .map(PdmProductTreeVO::getChildren)
                .flatMap(Collection::stream)
                .filter(vo -> zteSystemProductId.equals(vo.getId()))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(zteSystemProductTree)) {
            return zteSystemProductTree;
        }
        List<PdmProductTreeVO> zteSystemProductSubTree = zteSystemProductTree.get(0).getChildren();
        if (CollectionUtil.isEmpty(zteSystemProductSubTree)) {
            return zteSystemProductSubTree;
        }
        return filterProductListByInnerClassName(zteSystemProductSubTree, innerClassNames);
    }

    private List<PdmProductTreeVO> filterProductListByInnerClassName(List<PdmProductTreeVO> pdmProductTreeVOS, List<String> innerClassNames) {
        List<PdmProductTreeVO> result = new ArrayList<>();
        for (PdmProductTreeVO pdmProductTreeVO : pdmProductTreeVOS) {
            if (innerClassNames.contains(pdmProductTreeVO.getName())) {
                result.add(pdmProductTreeVO);
            }
        }
        return result;
    }

    @Override
    public List<PdmProductTreeVO> queryProductClassTreeFilterForProject() {
        List<ComDictionaryMaintainVO> innerClass = dictionary.queryByType(OpportunityConstant.ZTE_OPTY_INNER_CLASS);
        List<String> innerClassNames = innerClass.stream()
                .filter(Objects::nonNull).distinct().map(ComDictionaryMaintainVO::getChineseName)
                .collect(Collectors.toList());
        return queryProductClassTreeFilterByInnerClass(innerClassNames);
    }

}
