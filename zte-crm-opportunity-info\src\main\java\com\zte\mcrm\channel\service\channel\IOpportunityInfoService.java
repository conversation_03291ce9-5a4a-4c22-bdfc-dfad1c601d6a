package com.zte.mcrm.channel.service.channel;

import com.zte.itp.authorityclient.entity.output.RoleVO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.approval.model.dto.ApprovalCallBackDTO;
import com.zte.mcrm.adapter.approval.model.dto.ApprovalStartParamsDTO;
import com.zte.mcrm.adapter.authorization.model.RoleInfo;
import com.zte.mcrm.adapter.model.dto.AccountInfo;
import com.zte.mcrm.channel.model.dto.AuthorizationRoleInfoDTO;
import com.zte.mcrm.channel.model.dto.OpportunityInfoDTO;
import com.zte.mcrm.channel.model.entity.OpportunityDetail;
import com.zte.mcrm.channel.model.vo.CreateCustomerParamVO;
import com.zte.mcrm.channel.model.vo.OpportunityDetailVO;
import com.zte.mcrm.channel.model.vo.OpportunityProductVO;

import java.util.List;
import java.util.Map;

public interface IOpportunityInfoService {

    /**
     * 提交商机报备
     * @param opportunityInfo 商机报备信息
     * @return
     * @throws Exception
     */
    ServiceData<OpportunityInfoDTO> submitOpportunity(OpportunityInfoDTO opportunityInfo) throws Exception;

    /**
     * prm侧提交商机报备
     * @param opportunityInfo
     * @return
     */
    OpportunityInfoDTO submitPrmOpportunity(OpportunityInfoDTO opportunityInfo) throws Exception;

    /**
     * 提交商机报备审批
     * @param opportunityInfo
     * @return
     * @throws Exception
     */
    boolean submitOpportunityProcess(OpportunityInfoDTO opportunityInfo) throws Exception;


    /**
     * 启动流程
     * @param rowId
     * @throws Exception
     */
    boolean startFlowTask(String rowId) throws Exception;

    boolean startFlowTask(ApprovalStartParamsDTO startParamsDTO);

    /**
     * 获取启动流程参数
     * @param rowId
     * @return
     * @throws Exception
     */
    ApprovalStartParamsDTO getStartProcessParams (String rowId) throws Exception;

    /**
     * 暂存商机报备信息
     * @param opportunityInfo
     * @return
     * @throws Exception 异常
     */
    OpportunityInfoDTO storageOpportunityInfo(OpportunityInfoDTO opportunityInfo) throws Exception;

    /**
     * 依据角色、组织、子行业从权限平台找人
     * @param authorizationRoleInfoDTO
     * @return
     * @throws Exception
     */
    List<RoleInfo> queryRoleInfoWithConstraint(AuthorizationRoleInfoDTO authorizationRoleInfoDTO) throws Exception;

    /**
     *
     * @param vo
     */
   void sendCreateCustomerDraftEmail(CreateCustomerParamVO vo);
    /**
     * 从客户系统查询生效的客户编号
     * @param lastAccName
     * @return
     * @throws com.zte.springbootframe.common.exception.BusiException
     */
    AccountInfo queryActivatedLastAccByName(String lastAccName) throws com.zte.springbootframe.common.exception.BusiException;

    /**
     * 获取审批中心流程变量
     * @param opportunityInfo
     * @return
     */
    ApprovalStartParamsDTO setStartParams(OpportunityInfoDTO opportunityInfo) throws Exception;

    /**
     * 插入或更新商机
     * @param opportunityInfo
     */
    void insertOrUpdateTheOpportunity(OpportunityInfoDTO opportunityInfo) throws Exception;

    /**
     * 刷新
     * @param opportunityDetail
     * @return
     * @throws Exception
     */
    String refresh(OpportunityDetail opportunityDetail) throws Exception;

    /**
     * 创建客户草稿
     * @param paramVO
     * @return
     * @throws Exception
     */
    void doCreateCustomerDraft(CreateCustomerParamVO paramVO)throws Exception;

    void creatCustomer(OpportunityDetail opportunityDetail) throws Exception;

    /**
     * 创建客户草稿
     * @param finalCustomerChildTrade 最终用户id
     * @return deptNo  商机所属部门
     * @throws Exception
     */
    public String queryApprover(String finalCustomerChildTrade, String deptNo) ;

    /**
     * 获取iChannel侧角色Map
     * @param empNo
     * @return
     */
    Map<String, RoleVO> getChannelRoleMap(String empNo);

    /**
     * 处理审批结果
     * 具体包括：1. 更新商机状态；2. 发送消息提醒及邮件
     *
     * @param callBackDTO
     * @return String
     * <AUTHOR>
     * @date 2021/11/2
     */
    String handleApprovalCallBack(ApprovalCallBackDTO callBackDTO);

    /**
     * 发送系统消息
     * @param callBackDTO
     */
    void sendMessage(ApprovalCallBackDTO callBackDTO);

    /**
     * 根据审批中心流程状态刷新商机状态 -> 系统异常时处理用
     * @param rowId
     * @return
     */
    List<String> refreshStatusByApproveCenter(String rowId, boolean doUpdate);

    /**
     * 商机维护主产品
     * @param maintainMainProdsParams
     * @return
     */
    Boolean maintainMainProds(List<OpportunityProductVO> maintainMainProdsParams);

    /**
     * 商机变更渠道商
     * @param record
     * @return
     */
    Boolean modifyChannelPartner(String record);

    /**
     * 修改特殊字段使用
     * @param detailVO
     * @return
     */
    Boolean updateOptyAttribute(OpportunityDetailVO detailVO);

    /**
     * 判断修改权限
     * @return
     */
    Boolean verifyOperatorAuth(String rowId);
}
