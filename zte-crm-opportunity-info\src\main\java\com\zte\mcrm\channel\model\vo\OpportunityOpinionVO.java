package com.zte.mcrm.channel.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 审批意见VO：意见+类似商机+不通过原因
 *
 * <AUTHOR>
 * @date 2021/11/3
 */
@Data
public class OpportunityOpinionVO {
    @ApiModelProperty(value = "类似商机")
    private String similarOptyCd;
    @ApiModelProperty(value = "不通过原因，1：已有报备；2：不同意此报备；3：是受限制主体")
    private Integer failureReason;
    @ApiModelProperty(value = "TS审批单号")
    private String tsApprovalNumber;
    @ApiModelProperty(value = "中兴业务经理")
    private String businessManagerName;
}
