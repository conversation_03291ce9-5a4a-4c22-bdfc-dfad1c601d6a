package com.zte.aiagent.infrastruction.access.mapper;

import com.zte.aiagent.infrastruction.access.po.SysConfTypePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 系统参数配置类型表Mapper接口
 * 提供对系统参数类型的数据库操作，删除操作采用逻辑删除
 */
@Mapper
public interface SysConfTypeMapper {
    /**
     * 插入系统参数配置类型记录
     * @param sysConfType 系统参数配置类型PO对象
     * @return 影响的行数
     */
    int insert(SysConfTypePO sysConfType);

    /**
     * 根据ID查询系统参数配置类型
     * @param rowId 主键ID
     * @return 系统参数配置类型PO对象
     */
    SysConfTypePO selectByPrimaryKey(String rowId);

    /**
     * 根据编码类型和租户ID查询系统参数配置类型
     * @param codeType 编码类型
     * @param tenantId 租户ID
     * @return 系统参数配置类型PO对象
     */
    SysConfTypePO selectByCodeTypeAndTenantId(
            @Param("codeType") String codeType,
            @Param("tenantId") Long tenantId);

    /**
     * 查询所有有效系统参数配置类型
     * @param tenantId 租户ID
     * @return 系统参数配置类型列表
     */
    List<SysConfTypePO> selectAllValid(Long tenantId);

    /**
     * 根据ID更新系统参数配置类型
     * @param sysConfType 系统参数配置类型PO对象
     * @return 影响的行数
     */
    int updateByPrimaryKey(SysConfTypePO sysConfType);

    /**
     * 逻辑删除系统参数配置类型（将enabled_flag设为N）
     * @param rowId 主键ID
     * @param lastUpdatedBy 最后更新人
     * @param lastUpdatedDate 最后更新时间
     * @return 影响的行数
     */
    int logicDelete(
            @Param("rowId") String rowId,
            @Param("lastUpdatedBy") String lastUpdatedBy,
            @Param("lastUpdatedDate") Date lastUpdatedDate);
}
