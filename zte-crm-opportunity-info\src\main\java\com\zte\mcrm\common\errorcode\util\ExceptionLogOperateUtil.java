package com.zte.mcrm.common.errorcode.util;

import com.alibaba.fastjson.JSON;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.security.adaptor.IsoaZteSecurityAdaptor;
import com.zte.itp.security.ZteSecurity;
import com.zte.mcrm.common.errorcode.constant.ErrorCodeConstant;
import com.zte.mcrm.common.errorcode.enums.ErrorLevelEnum;
import com.zte.mcrm.common.errorcode.enums.ModuleEnum;
import com.zte.mcrm.common.errorcode.model.ComExceptionLog;
import com.zte.mcrm.common.errorcode.service.ComExceptionLogService;
import com.zte.mcrm.common.framework.exception.ErrorCodeException;
import com.zte.mcrm.common.framework.exception.ErrorCodeRemoteException;
import com.zte.mcrm.common.framework.http.BodyCachingReqWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.PropertyKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.UnsupportedEncodingException;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2022/04/29
 * @Description:
 */
@Slf4j
@Component
public class ExceptionLogOperateUtil {
    public static final int MAX_BODY_LENGTH = 2048;
    private static ComExceptionLogService comExceptionLogService;
    private static String serviceName;

    /**
     * ErrorCodeRemoteException 异常日志保存
     *
     * @param req
     * @param e
     */
    public static void saveExceptionLog(HttpServletRequest req, ErrorCodeRemoteException e) {
        String exceptionContent = StringUtils.substring(getTrace(e), 0, ErrorCodeConstant.EXCEPTION_LOG_MAX_LENGTH);
        saveExceptionLogBase(req, exceptionContent, e.getErrorCode(), e.getErrorCodeMessage(), e.getRemoteErrorResponse());
    }

    /**
     * ErrorCodeException 异常日志保存
     *
     * @param req
     * @param e
     */
    public static void saveExceptionLog(HttpServletRequest req, ErrorCodeException e) {
        String exceptionContent = StringUtils.substring(getTrace(e), 0, ErrorCodeConstant.EXCEPTION_LOG_MAX_LENGTH);
        saveExceptionLogBase(req, exceptionContent, e.getErrorCode(), e.getErrorCodeMessage(), "");
    }

    /**
     * 异常日志保存
     *
     * @param req
     * @param e
     */
    public static void saveExceptionLog(HttpServletRequest req, Exception e, @PropertyKey(resourceBundle = ErrorCodeMessageUtil.ERRORCODEMESSAGES_BUNDLE) String errorCode, Object... params) {
        saveExceptionLogWithErrorCodeMsg(req, e, errorCode, ErrorCodeMessageUtil.getErrorCodeMessage(errorCode, params));
    }

    /**
     * 为兼容老异常类的日志保存，新增的异常都用 {@link ErrorCodeRemoteException,ErrorCodeException}
     *
     * @param req
     * @param e
     * @param errorCode
     * @param errorCodeMessage
     */
    public static void saveExceptionLogWithErrorCodeMsg(HttpServletRequest req, Exception e, @PropertyKey(resourceBundle = ErrorCodeMessageUtil.ERRORCODEMESSAGES_BUNDLE) String errorCode, String errorCodeMessage) {
        String exceptionContent = StringUtils.substring(getTrace(e), 0, ErrorCodeConstant.EXCEPTION_LOG_MAX_LENGTH);
        saveExceptionLogBase(req, exceptionContent, errorCode, errorCodeMessage, "");
    }

    private static void saveExceptionLogBase(HttpServletRequest req, String exceptionContent, String errorCode, String errorCodeMessage, String remoteErrorResponse) {
        try {
            ComExceptionLog comExceptionLog = ComExceptionLog.builder()
                    .service(getServiceName()).module(ModuleEnum.O.name()).features("")
                    .reqServiceName(req.getContextPath())
                    .reqMethod(req.getMethod())
                    .reqUrl(req.getRequestURL().toString())
                    .reqHeaders(JSON.toJSONString(getEncryptHeaderMap(req)))
                    .reqBody(getBodyData(req))
                    .queryParams(req.getQueryString())
                    .createdBy(req.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO))
                    .lastUpdatedBy(req.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO))
                    .errorCode(errorCode)
                    .errorMessage(errorCodeMessage)
                    .response(remoteErrorResponse)
                    .exceptionContent(exceptionContent)
                    .build();
            // 只有错误级别是错误（3）和致命（4）的才需要写入base服务
            if (StringUtils.equals(getErrorLevel(errorCode), ErrorLevelEnum.ERROR.getCode())
                    || StringUtils.equals(getErrorLevel(errorCode), ErrorLevelEnum.FATAL.getCode())) {
                comExceptionLogService.asyncAdd(comExceptionLog);
            } else {
                log.info(comExceptionLog.toString());
            }
        } catch (Exception e2) {
            log.error("保存异常日志失败", e2);
        }
    }

    public ExceptionLogOperateUtil(@Autowired ComExceptionLogService comExceptionLogService) {
        ExceptionLogOperateUtil.comExceptionLogService = comExceptionLogService;
    }

    private static String getServiceName() {
        if (null == serviceName) {
            ExceptionLogOperateUtil.serviceName = SpringContextUtil.getEnvironment().getProperty("spring.application.name");
        }
        return serviceName;
    }

    private static Map<String, String> getEncryptHeaderMap(HttpServletRequest req) throws Exception {
        Map<String, String> requestHeaderMap = getRequestHeaderMap(req);
        String xAuthValue = requestHeaderMap.get(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE);
        requestHeaderMap.put(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE,
                             SpringContextUtil.getBean(IsoaZteSecurityAdaptor.class).encryptAesGcm(xAuthValue));
        return requestHeaderMap;
    }

    /**中兴AES加密工具
     * @param sSrc
     * @return
     */
    private static String encrypt(String sSrc) {
        try {
            return StringUtils.isNotEmpty(sSrc)? ZteSecurity.getInstance().encrypt(sSrc):"";
        } catch (Exception e) {
            log.info("ZteSecurity encrypt failed: {}", e);
        }
        return "";
    }

    private static Map<String, String> getRequestHeaderMap(HttpServletRequest request) {
        Map<String, String> headerMap = new HashMap<>(16);
        headerMap.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO, request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO));
        headerMap.put(SysGlobalConst.HTTP_HEADER_X_LANG_ID, request.getHeader(SysGlobalConst.HTTP_HEADER_X_LANG_ID));
        headerMap.put(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE, request.getHeader(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE));
        headerMap.put(SysGlobalConst.HTTP_HEADER_X_ORG_ID, request.getHeader(SysGlobalConst.HTTP_HEADER_X_ORG_ID));
        headerMap.put(SysGlobalConst.HTTP_HEADER_X_ITP_VALUE, request.getHeader(SysGlobalConst.HTTP_HEADER_X_ITP_VALUE));
        headerMap.put(SysGlobalConst.HTTP_HEADER_X_TENANT_ID, request.getHeader(SysGlobalConst.HTTP_HEADER_X_TENANT_ID));
        headerMap.put(SysGlobalConst.HTTP_HEADER_X_ORIGIN_SERVICE_NAME, request.getHeader(SysGlobalConst.HTTP_HEADER_X_ORIGIN_SERVICE_NAME));
        return headerMap;
    }

    private static String getBodyData(HttpServletRequest req) {
        try {
            if (req instanceof BodyCachingReqWrapper) {

                byte[] body = ((BodyCachingReqWrapper) req).getBody();
                int len = Math.min(body.length, MAX_BODY_LENGTH);
                return new String(body, 0, len, req.getCharacterEncoding());
            }
        } catch (UnsupportedEncodingException e) {
            log.error("getBodyData UnsupportedEncodingException:", e);
        }
        return MessageFormat.format(ErrorCodeConstant.MULTIPART_BODY_FORMAT, req.getContentType(), req.getContentLength());
    }

    /**
     * 获取异常信息字符串
     *
     * @param t
     * @return
     */
    /* Started by AICoder, pid:t4406n5be646b9914a810986f00d6d12e400cfd8 */
    public static String getTrace(Throwable t) {
        try (StringWriter stringWriter = new StringWriter();
             PrintWriter writer = new PrintWriter(stringWriter)) {
            t.printStackTrace(writer);
            return stringWriter.toString();
        } catch (Exception e) {
            log.error("getTrace:", e);
        }
        return null;
    }
    /* Ended by AICoder, pid:t4406n5be646b9914a810986f00d6d12e400cfd8 */

    private static String getErrorLevel(String errorCode) {
        if (StringUtils.isBlank(errorCode)) {
            return Strings.EMPTY;
        }
        return String.valueOf(errorCode.charAt(errorCode.length() - 1));
    }
}
