package com.zte.mcrm.adapter.authorization.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.zte.itp.authorityclient.client.AuthorityClient;
import com.zte.itp.authorityclient.entity.input.CommonModuleIdEntity;
import com.zte.itp.authorityclient.entity.input.CommonRoleEntity;
import com.zte.itp.authorityclient.entity.output.ServiceData;
import com.zte.mcrm.adapter.authorization.service.SalesOpportunitiesAuthService;
import com.zte.mcrm.adapter.model.dto.RoleDTO;
import com.zte.mcrm.common.util.CommonUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 描述：商机产品-销售机会 模块下的权限信息service
 * 创建时间：2021/10/8
 *
 * @author：王丹凤6396000572
 */
@Service
public class SalesOpportunitiesAuthServiceImpl implements SalesOpportunitiesAuthService {

    /**
     * 销售机会产品ID
     **/
    @Value("${upp.auth.productId}")
    private Long salesProductId;
    /**
     * 销售机会模块ID
     **/
    @Value("${upp.auth.moduleId}")
    private String salesModuleId;
    /**
     * 销售机会租户ID
     **/
    @Value("${upp.auth.tenantId}")
    private Long salesTenantId;
    /**
     * 销售机会产品密钥key
     **/
    @Value("${upp.auth.productSecretKey}")
    private String salesProductSecretKey;

    /**
     * 查询当前登录人的所有角色信息
     *
     * @return
     */
    @Override
    public ServiceData getRoleList() {
        // 权限平台需要的参数对象
        CommonRoleEntity entity = new CommonRoleEntity();
        addSalesConstraints(entity);
        //当前用户
        entity.setEmpidui(CommonUtils.getEmpNo());
        //token
        entity.setToken(CommonUtils.getAuthValue());
        // 调用权限平台接口
        return AuthorityClient.getRoleList(entity);
    }

    /**
     * 查询当前登录人的角色编码集合
     */
    @Override
    public Set<String> getRoleCodes() {
        ServiceData ret = this.getRoleList();
        String str = JSONObject.toJSONString(ret.getBo());
        List<RoleDTO> roleDTOS = JSONObject.parseObject(str, new TypeReference<List<RoleDTO>>() {});
        Set<String> roleCodes = roleDTOS.stream().map(RoleDTO::getRoleCode).collect(Collectors.toSet());
        return roleCodes;
    }

    /**
     * 添加销售机会侧约束条件
     */
    private void addSalesConstraints(CommonModuleIdEntity entity) {
        //产品Id，该值保持固定，需要变动联系权限平台
        entity.setProductId(salesProductId);
        //模块Id，该值请咨询权限平台
        entity.setModuleId(salesModuleId);
        //租户Id
        entity.setTenantId(salesTenantId);
        //产品密钥key
        entity.setSecretKey(salesProductSecretKey);
    }

}
