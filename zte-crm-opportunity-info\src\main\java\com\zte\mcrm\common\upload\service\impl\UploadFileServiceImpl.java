package com.zte.mcrm.common.upload.service.impl;

import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.adapter.clouddisk.service.DocCloudService;
import com.zte.mcrm.common.business.service.IKeyIdService;
import com.zte.mcrm.common.consts.CommonConstant;
import com.zte.mcrm.common.upload.constants.UploadConst;
import com.zte.mcrm.common.upload.dao.base.ComUploadFileDao;
import com.zte.mcrm.common.upload.enums.FileTypeEnum;
import com.zte.mcrm.common.upload.model.entity.ComUploadFile;
import com.zte.mcrm.common.upload.service.UploadFileService;
import com.zte.mcrm.common.upload.service.base.impl.UploadFileBaseServiceImpl;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.springbootframe.common.model.RetCodeCopy;
import com.zte.springbootframe.util.local.LocalMessageUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.FileUploadException;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.Part;
import java.util.*;

/**
 *  服务类 
 * <AUTHOR>
 * @date 2021/06/05
 */
@Service
public class UploadFileServiceImpl extends UploadFileBaseServiceImpl implements UploadFileService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UploadFileServiceImpl.class);
    public static final int ONE = 1;

    @Value("${fileSuffix.notAllowed}")
    private String fileSuffixNotAllowed;

    @Autowired
    private DocCloudService docCloudService;

    @Autowired
    private IKeyIdService iKeyIdService;

    @Autowired
    private ComUploadFileDao uploadFileDao ;

    @Override
    public void uploadFile(ComUploadFile uploadFile, MultipartFile file, HttpServletRequest request) throws Exception {

        String userId = CommonUtils.getEmpNo();
        String path = file.getOriginalFilename();
        String fileName = getFileName(request);
        if (StringUtils.isBlank(fileName) && StringUtils.isNotBlank(path)){
            // 得到去除路径的文件名
            fileName = path.substring(path.lastIndexOf('\\') + ONE);
        }
        if (StringUtils.isBlank(fileName)){
            throw new BusiException(RetCodeCopy.VALIDATIONERROR_MSGID, LocalMessageUtils.getMessage("file.name.null"));
        }
        String fileSuffix = getFileSuffix(fileName);
        if (judgeFileSuffixNotAllowed(fileSuffix)){
            throw new BusiException(RetCodeCopy.VALIDATIONERROR_MSGID, LocalMessageUtils.getMessage("fileSuffix.notAllowed"));
        }
        String dmeKey = docCloudService.upload(userId, file);
        uploadFile.setDmeKey(dmeKey);
        uploadFile.setDocName(fileName);
        //判断fileType是否存在，如果不存在，根据文件后缀识别
        if(StringUtils.isEmpty(uploadFile.getFileType())) {
            uploadFile.setFileType(getFileTypeBySuffix(fileName));
        }
        if(Objects.equals(null,uploadFile.getSecretLevel())){
            uploadFile.setSecretLevel(UploadConst.SECRET_LEVEL_1);
        }
        insert(uploadFile);
    }

    //从content-disposition中获取filename
    private String getFileName(HttpServletRequest request) {
        String filename = null;
        try {
            Collection<Part> parts = request.getParts();
            for (Part part : parts) {
                String headerValue = part.getHeader("content-disposition");
                LOGGER.error("headerValue：" + headerValue);
                System.out.println("headerValue：" + headerValue);
                String[] contentDisposition = headerValue != null ? headerValue.split(";") : new String[]{};
                for (String value : contentDisposition) {
                    if (value.trim().startsWith("filename")) {
                        filename = value.substring(value.indexOf('=') + 1).trim().replace("\"", "");
                        return filename;
                    }
                }
            }
        }catch (Exception e){
            LOGGER.error("getFileNameError", e);
        }
        return null;
    }

    private boolean judgeFileSuffixNotAllowed(String fileSuffix){
        String[] fileSuffixNotAllowedList = fileSuffixNotAllowed.split(",");
        for (String s : fileSuffixNotAllowedList) {
            if (s.equalsIgnoreCase(fileSuffix)){
                return true;
            }
        }
        return false;
    }

    /**
     * 根据文件名获取文件类型
     * @param fileName 文件名
     * @return 文件类型
     * <AUTHOR>
     * @date 2021/8/31
     */
    private String getFileTypeBySuffix(String fileName) {
        String fileSuffix = getFileSuffix(fileName);
        return FileTypeEnum.getEnumNameForValues(fileSuffix);
    }

    private String getFileSuffix(String fileName){
        return fileName.substring(fileName.lastIndexOf(".") + ONE).toUpperCase();
    }


    @Override
    public Boolean deleteFileByDmeKey(String dmeKey ) {
        Boolean result = Boolean.FALSE;
        ComUploadFile uploadFile = new ComUploadFile();
        uploadFile.setDmeKey(dmeKey);
        uploadFile.setEnabledFlag("N");
        try {
            int update = deleteByDmeKey(uploadFile);
            result = update >= ONE ? Boolean.TRUE : Boolean.FALSE;
        } catch (Exception e) {
            LOGGER.error("delete file by oid: {} error:{}", dmeKey, e.getMessage(), e);
        }
        return result;
    }


    @Override
    public Long countFileNum(String uploadType, Long billOid) {
        Map<String, Object> param = new HashMap<>(16);
        param.put("billOid", billOid);
        param.put("uploadType",  uploadType);
        param.put("enabledFlag", "Y");
        return getCount(param);
    }

    @Override
    public List<ComUploadFile> findUploadFiles(ComUploadFile uploadFile) {
        if(StringUtils.isBlank(uploadFile.getBillId())){
            throw new BusiException(RetCode.BUSINESSERROR_CODE, "billId.null");
        }

        List<ComUploadFile> uploadFiles = getList(uploadFile);
        return uploadFiles;
    }

    @Override
    public Boolean copy(String uploadType, String oid, String newBillOid) {
        final ComUploadFile entity = new ComUploadFile();
        entity.setBillId(oid);
        entity.setUploadType(uploadType);
        final List<ComUploadFile> list = getList(entity);
        boolean response = true;
        for (ComUploadFile uploadFile : list) {
            uploadFile.setId(iKeyIdService.getKeyLongId());
            uploadFile.setBillId(newBillOid);
            if (insert(uploadFile) < ONE) {
                response = false;
            }
        }
        return response;
    }


    /**
     * 查询附件密级
     * @param dmeKey
     * @return
     */
    private Integer getSecretLevel(String dmeKey) {
        return uploadFileDao.getSecretLevel(dmeKey);
    }

    /**
     * 根据dmeKey从数据库中查询文件类型
     * @param dmeKey
     * @return
     */
    private String getFileType(String dmeKey) {
        ComUploadFile file = uploadFileDao.getFileType(dmeKey);
        if(Objects.isNull(file)) {
            return null;
        }
        String fileType = file.getFileType();
        if(StringUtils.isEmpty(fileType)) {
            String docName = file.getDocName();
            if(StringUtils.isEmpty(docName)) {
                return null;
            }
            //根据文件名返回文件类型
            return getFileTypeBySuffix(docName);
        }
        return fileType;
    }


    /**
     * 判断是否是公开图片
     * @param dmeKey
     * @return
     * <AUTHOR>
     * @date 2021/9/1
     */
    @Override
    public Boolean isPublicImg(String dmeKey) {
        if(!isPublicFile(dmeKey)) {
            return false;
        }
        String[] dmeKeys = dmeKey.split(CommonConstant.COMMA);
        for (String key : dmeKeys) {
            String fileType = getFileType(key);
            if(!Objects.equals(UploadConst.FILE_TYPE_2, fileType)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断是否是公共附件
     * @param dmeKey
     * @return
     */
    @Override
    public Boolean isPublicFile(String dmeKey) {
        String[] dmeKeys = dmeKey.split(CommonConstant.COMMA);
        for (String key : dmeKeys) {
            Integer secretLevel = getSecretLevel(key);
            if(Objects.isNull(secretLevel)) {
                continue;
            }
            if(!Objects.equals(UploadConst.SECRET_LEVEL_0, secretLevel)) {
                return false;
            }
        }
        return true;
    }
}
