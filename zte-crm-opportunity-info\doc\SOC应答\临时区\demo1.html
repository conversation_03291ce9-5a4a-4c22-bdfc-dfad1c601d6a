<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <title>SOC应答系统 DEMO</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <style>
    body {
      margin: 0;
      font-family: 'Segoe UI', 'PingFang SC', Arial, sans-serif;
      background: #ffffff;
    }

    .soc-navbar {
      height: 48px;
      background: #ffffff;
      color: #7c7c7c;
      display: flex;
      align-items: center;
      padding: 0 32px;
      font-size: 16px;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
      z-index: 10;
    }

    .soc-navbar-logo {
      font-weight: bold;
      font-size: 20px;
      margin-right: 32px;
      letter-spacing: 1px;
    }

    .soc-navbar-menu {
      flex: 1;
      display: flex;
      align-items: center;
    }

    .soc-navbar-menu>div {
      margin-right: 24px;
      cursor: pointer;
    }

    .soc-navbar-search {
      background: #fff;
      border-radius: 4px;
      padding: 2px 8px;
      margin-right: 24px;
      color: #333;
      display: flex;
      align-items: center;
    }

    .soc-navbar-search input {
      border: none;
      outline: none;
      background: transparent;
      font-size: 14px;
      width: 120px;
    }

    .soc-navbar-user {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .soc-navbar-avatar {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: #e6eaf2;
      display: inline-block;
    }

    .soc-layout {
      display: flex;
      height: calc(100vh - 48px);
    }

    .soc-sidebar,
    .soc-chatbar {
      background: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      z-index: 2;
    }

    .soc-sidebar {
      width: 220px;
      min-width: 180px;
      max-width: 260px;
      border-right: 1px solid #e8eaec;
      display: flex;
      flex-direction: column;
    }

    .soc-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-width: 0;
    }

    .soc-header {
      height: 48px;
      background: #fff;
      border-bottom: 1px solid #e8eaec;
      display: flex;
      align-items: center;
      padding: 0 24px;
      font-size: 18px;
      font-weight: 500;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    }

    .soc-main {
      flex: 1;
      overflow: auto;
      padding: 16px;
      background: #ffffff;
    }

    /* .soc-table-card 已废弃，表格平铺内容区 */
    #answerTableCard {
      background: none;
      border-radius: 0;
      box-shadow: none;
      margin: 0;
      overflow-x: auto;
      padding: 0;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      margin: 8px 0;
      position: relative;
      /* 新增，确保sticky基于此容器 */
    }

    table.soc-table {
      border-collapse: separate;
      border-spacing: 0;
      width: 100%;
      min-width: 1100px;
      /* 降低最小宽度，便于小屏展示 */
      font-size: 13px;
      background: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      border-radius: 6px;
      border: 1px solid #e8eaec;
      overflow: hidden;
    }

    table.soc-table th,
    table.soc-table td {
      border-bottom: 1px solid #e8eaec;
      border-right: 1px solid #e8eaec;
      padding: 8px 8px;
      /* 紧凑布局 */
      text-align: left;
      vertical-align: middle;
      font-size: 13px;
      /* 字体缩小 */
    }

    table.soc-table th:last-child,
    table.soc-table td:last-child {
      border-right: none;
    }

    table.soc-table td {
      word-wrap: break-word;
      overflow-wrap: break-word;
    }

    table.soc-table th {
      background: #EEEEEE;
      font-weight: 500;
      color: #717171;
      border-top: 1px solid #d6d6d6;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    table.soc-table tr:last-child td {
      border-bottom: none;
    }

    table.soc-table tbody tr:hover {
      background-color: #f5f7fa;
    }

    table.soc-table tbody tr {
      transition: background-color 0.2s ease;
    }

    /* 新增：合并单元格样式 */
    table.soc-table td[rowspan] {
      vertical-align: top;
      background-color: #fafafa;
    }

    /* 新增：产品列样式 */
    table.soc-table td:nth-child(4) {
      font-weight: 500;
      color: #1765d5;
    }

    /* 新增：AnswerType标签样式 */
    table.soc-table td:nth-child(5) span {
      display: inline-block;
      font-size: 12px;
      border-radius: 4px;
      padding: 2px 8px;
    }

    /* 新增：应答下拉框样式 */
    table.soc-table td:nth-child(6) select {
      width: 20px;
      min-width: 20px;
      max-width: 20px;
      text-align: left;
      padding: 0 8px;
      box-sizing: border-box;
      background-position: right 6px center;
      appearance: none;
    }

    /* 新增：应答说明文本框样式 */
    table.soc-table td:nth-child(7) textarea {
      min-height: 40px;
      resize: vertical;
      border: none;
      outline: none;
      background: transparent;
      width: 100%;
      font-family: inherit;
      font-size: inherit;
      padding: 4px;
      box-sizing: border-box;
    }

    /* 新增：备注输入框样式 - 改为应答说明类似的文本框 */
    table.soc-table td:nth-child(9) textarea {
      min-height: 40px;
      resize: vertical;
      border: none;
      outline: none;
      background: transparent;
      width: 100%;
      font-family: inherit;
      font-size: inherit;
      padding: 4px;
      box-sizing: border-box;
    }

    /* 表格内输入框通用样式 */
    table.soc-table input[type="text"],
    table.soc-table textarea {
      border: none;
      outline: none;
      background: transparent;
      width: 100%;
      font-family: inherit;
      font-size: inherit;
      padding: 4px;
      box-sizing: border-box;
    }

    /* 表格内输入框聚焦时的样式 */
    table.soc-table input[type="text"]:focus,
    table.soc-table textarea:focus {
      background: #f8f9fa;
      border-radius: 4px;
    }

    /* 表格内select下拉框样式 */
    table.soc-table select {
      border: none;
      outline: none;
      background: transparent;
      width: 100%;
      font-family: inherit;
      font-size: inherit;
      padding: 4px;
      box-sizing: border-box;
    }

    /* 表格内select下拉框聚焦时的样式 */
    table.soc-table select:focus {
      background: #f8f9fa;
      border-radius: 4px;
    }

    /* 表格内select下拉框悬停时的样式 */
    table.soc-table select:hover {
      background: #f8f9fa;
    }

    /* 表格头部检索框聚焦时的样式 */
    table.soc-table th input[type="text"]:focus,
    table.soc-table th select:focus {
      background: #f8f9fa;
      border-radius: 4px;
    }

    /* 表格头部检索框悬停时的样式 */
    table.soc-table th input[type="text"]:hover,
    table.soc-table th select:hover {
      background: #f8f9fa;
    }

    /* 表格内输入框悬停时的样式 */
    table.soc-table input[type="text"]:hover,
    table.soc-table textarea:hover {
      background: #f8f9fa;
    }

    .soc-table-img {
      max-width: 80px;
      max-height: 40px;
      border-radius: 4px;
      border: 1px solid #eee;
    }

    .soc-table-action {
      color: #1890ff;
      cursor: pointer;
    }

    .soc-chatbar {
      width: 300px;
      min-width: 180px;
      max-width: 340px;
      border-left: 1px solid #e8eaec;
      display: flex;
      flex-direction: column;
    }

    .soc-hide-btn {
      position: absolute;
      top: 12px;
      right: -12px;
      background: #fff;
      border: 1px solid #ececec;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      cursor: pointer;
      box-shadow: 0 2px 8px #eee;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .soc-sidebar,
    .soc-chatbar {
      position: relative;
    }

    .soc-sidebar .soc-header,
    .soc-chatbar .soc-header {
      font-size: 15px;
      font-weight: 500;
      border-bottom: 1px solid #f0f0f0;
    }

    .soc-sidebar>div,
    .soc-chatbar>div {
      padding: 12px 16px;
    }

    .soc-sidebar>div>div,
    .soc-chatbar>div>div {
      margin-bottom: 12px;
      color: #555;
    }

    @media (max-width: 1200px) {
      .soc-sidebar {
        width: 140px;
      }

      .soc-chatbar {
        width: 160px;
      }
    }

    @media (max-width: 900px) {

      .soc-sidebar,
      .soc-chatbar {
        display: none;
      }

      .soc-content {
        flex: 1 1 100%;
      }
    }

    .soc-btn {
      height: 32px;
      padding: 0 16px;
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      background: #fff;
      color: #666;
      font-size: 14px;
      cursor: pointer;
      transition: all .2s ease;
      margin-right: 0;
      outline: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
      display: inline-flex;
      align-items: center;
      gap: 4px;
      font-weight: 400;
      line-height: 1;
    }

    .soc-btn:hover {
      background: #fff;
      color: #1890ff;
      border-color: #1890ff;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.12);
    }

    .soc-btn-green {
      background: #52c41a;
      color: #fff;
      border: 1px solid #52c41a;
      box-shadow: 0 2px 4px rgba(82, 196, 26, 0.2);
    }

    .soc-btn-green:hover {
      background: #73d13d;
      border-color: #73d13d;
      color: #fff;
      box-shadow: 0 4px 8px rgba(82, 196, 26, 0.3);
    }

    .soc-btn-orange {
      background: #fa8c16;
      color: #fff;
      border: 1px solid #fa8c16;
      box-shadow: 0 2px 4px rgba(250, 140, 22, 0.2);
    }

    .soc-btn-orange:hover {
      background: #ffa940;
      border-color: #ffa940;
      color: #fff;
      box-shadow: 0 4px 8px rgba(250, 140, 22, 0.3);
    }

    .soc-btn-yellow {
      background: #fadb14;
      color: #fff;
      border: 1px solid #fadb14;
    }

    .soc-btn-yellow:hover {
      background: #ffec3d;
      border-color: #ffec3d;
      color: #fff;
    }

    .soc-btn-blue {
      background: #0077ff;
      color: #fff;
      border: 1px solid #1871D6;
    }

    .soc-btn-blue:hover {
      background: #40a9ff;
      border-color: #40a9ff;
      color: #fff;
    }

    .soc-btn-red {
      background: #f5222d;
      border: 1px solid #f5222d;
      color: #fff;
    }

    .soc-btn-red:hover {
      background: #ff4d4f;
      border-color: #1973D6;
      color: #fff;
    }


    .soc-btn-2 {
      background: #ffffff;
      border: 1px solid #1871D6;
      color: #0077ff;
    }

    .soc-btn-3 {
      background: #ffffff;
      border: 1px solid #D8D8D8;
      color: #0077ff;
    }

    .soc-btn-4 {
      background: #ffffff;
      border: 1px solid #D8D8D8;
      color: #9f9f9f;
    }

    /* 次要按钮样式 - 白色背景蓝色边框 */
    .soc-btn-secondary {
      background: #fff;
      color: #1890ff;
      border: 1px solid #1890ff;
      box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
    }

    .soc-btn-secondary:hover {
      background: #e6f7ff;
      color: #1890ff;
      border-color: #1890ff;
      box-shadow: 0 4px 8px rgba(24, 144, 255, 0.15);
    }

    /* 输入框通用样式 */
    input[type="text"],
    input[type="search"],
    select {
      border: 1px solid #d4d7db;
      border-radius: 4px;
      padding: 4px 8px;
      font-size: 13px;
      transition: border-color 0.2s ease, box-shadow 0.2s ease;
      background: #fff;
    }

    input[type="text"]:focus,
    input[type="search"]:focus,
    select:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      outline: none;
    }

    /* 智能提示模板样式 */
    .smart-prompt-btn {
      background: #e6f7ff !important;
      color: #1890ff !important;
      border: 1px solid #91d5ff !important;
      border-radius: 6px !important;
      padding: 4px 8px !important;
      font-size: 12px !important;
      cursor: pointer !important;
      transition: all 0.2s !important;
      margin: 2px !important;
    }

    .smart-prompt-btn:hover {
      background: #e6f7ff !important;
      border-color: #91d5ff !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 2px 4px rgba(23, 101, 213, 0.1) !important;
    }

    /* 快捷指令按钮样式 */
    .quick-cmd-btn {
      background: #f6ffed;
      color: #389e0d;
      border: 1px solid #b7eb8f;
      border-radius: 6px;
      padding: 4px 8px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s;
      margin: 2px;
    }

    .quick-cmd-btn:hover {
      background: #d9f7be;
      border-color: #95de64;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(56, 158, 13, 0.1);
    }

    /* 主菜单项样式 */
    .main-action-item {
      transition: background 0.2s;
    }

    .main-action-item:hover {
      background: #e6f7ff !important;
      color: #1890ff !important;
    }

    .soc-toolbar input.search-input {
      height: 32px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      padding: 0 8px;
      font-size: 15px;
      outline: none;
      transition: border .2s;
    }

    .soc-toolbar input.search-input:focus {
      border-color: #1765d5;
    }

    .soc-toolbar label {
      font-size: 15px;
      color: #333;
    }

    .soc-toolbar select {
      height: 32px;
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      font-size: 15px;
      padding: 0 8px;
      margin-left: 2px;
    }

    .soc-search-row th {
      background: #f7f9fb;
      padding: 6px 8px;
      border-bottom: 1px solid #f0f0f0;
    }

    .soc-search-row input.search-input {
      width: 100%;
      height: 30px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      padding: 0 8px;
      font-size: 14px;
      outline: none;
      background: #fff;
      transition: border .2s, box-shadow .2s;
      box-sizing: border-box;
    }

    .soc-search-row input.search-input:focus {
      border-color: #1765d5;
      box-shadow: 0 0 0 2px #e6f7ff;
    }

    .soc-search-row select {
      height: 30px;
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      font-size: 14px;
      padding: 0 8px;
      background: #fff;
    }

    /* 素材准备区样式 */
    .material-upload-area {
      border: 2px dashed #d9d9d9;
      border-radius: 8px;
      padding: 16px;
      text-align: center;
      transition: all 0.3s;
      cursor: pointer;
    }

    .material-upload-area:hover {
      border-color: #1765d5;
      background-color: #f0f5ff;
    }

    .material-upload-area.dragover {
      border-color: #1765d5;
      background-color: #e6f7ff;
    }

    .material-select-area {
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      padding: 16px;
      background: #fafafa;
    }

    .material-tag {
      display: inline-flex;
      align-items: center;
      border-radius: 4px;
      padding: 4px 8px;
      font-size: 13px;
      margin: 2px;
      transition: all 0.2s;
    }

    .material-tag:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .material-tag .doc-name {
      margin-right: 6px;
      font-weight: 500;
    }

    .material-tag .doc-type {
      font-size: 11px;
      color: #666;
      margin-right: 6px;
    }

    .material-tag .doc-action {
      cursor: pointer;
      margin-right: 4px;
      opacity: 0.7;
      transition: opacity 0.2s;
    }

    .material-tag .doc-action:hover {
      opacity: 1;
    }

    .material-status {
      font-size: 12px;
      padding: 4px 8px;
      border-radius: 4px;
      background: #f5f5f5;
    }

    .material-status.complete {
      background: #f6ffed;
      color: #52c41a;
    }

    .material-status.incomplete {
      background: #fffbe6;
      color: #faad14;
    }

    /* 详情弹窗样式增强 */
    .layer-nav-btn:hover {
      color: #1765d5 !important;
      border-bottom-color: #1765d5 !important;
    }

    .source-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    }

    .fold-content .expand-content,
    .fold-content .collapse-content {
      color: #1765d5;
      text-decoration: none;
      font-size: 13px;
      margin-left: 8px;
    }

    .fold-content .expand-content:hover,
    .fold-content .collapse-content:hover {
      text-decoration: underline;
    }

    /* 推荐应答下拉列表样式 */
    #answerSelect {
      transition: all 0.3s ease;
      font-weight: 500;
    }

    #answerSelect:focus {
      box-shadow: 0 0 0 2px rgba(23, 101, 213, 0.2);
      transform: translateY(-1px);
    }

    #answerSelect option {
      padding: 8px 12px;
      font-weight: 500;
    }

    #answerSelect option[value="FC"] {
      background: #f6ffed;
      color: #52c41a;
    }

    #answerSelect option[value="PC"] {
      background: #fff7e6;
      color: #faad14;
    }

    #answerSelect option[value="NC"] {
      background: #fff2f0;
      color: #ff4d4f;
    }

    #answerSelect option[value="N/A"] {
      background: #f5f5f5;
      color: #666;
    }

    #answerSelect option[value=""] {
      background: #f5f5f5;
      color: #666;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .layer-nav-btn {
        padding: 6px 12px !important;
        font-size: 14px !important;
      }

      .source-card {
        min-width: 100% !important;
      }

      #answerSelect {
        min-width: 100px !important;
        font-size: 13px !important;
      }
    }

    /* 产品应答卡片样式 */
    .product-card {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 8px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      margin-bottom: 8px;
    }

    .product-card-row {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
    }

    .product-card-row span {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
    }

    .product-card-row span:hover {
      text-decoration: underline;
      cursor: pointer;
    }

    .product-card-row .product-name {
      font-weight: 500;
      color: #333;
    }

    .product-card-row .answer-type {
      background: #e6f7ff;
      color: #1765d5;
      border: 1px solid #91d5ff;
      border-radius: 4px;
      padding: 2px 6px;
      font-size: 12px;
    }

    .product-card-row .answer-type.ai {
      background: #fffbe6;
      color: #ad8b00;
      border: 1px solid #ffe58f;
    }

    .product-card-row .answer-explain {
      background: #f5f5f5;
      border-radius: 4px;
      padding: 2px 6px;
      font-size: 12px;
      color: #666;
    }

    .product-card-row .index {
      background: #f5f5f5;
      border-radius: 4px;
      padding: 2px 6px;
      font-size: 12px;
      color: #666;
    }

    .product-card-row .remark {
      background: #f5f5f5;
      border-radius: 4px;
      padding: 2px 6px;
      font-size: 12px;
      color: #666;
    }

    /* 操作列样式 */
    .action-col {
      display: flex;
      align-items: center;
      gap: 8px;
      padding-right: 8px;
      justify-content: flex-end;
    }

    .action-col .soc-btn {
      font-size: 13px;
      padding: 4px 12px;
      height: 28px;
    }

    .action-col .soc-btn-blue {
      background: #1890ff;
      color: #fff;
      border: 1px solid #1890ff;
    }

    .action-col .soc-btn-blue:hover {
      background: #40a9ff;
      border-color: #40a9ff;
      color: #fff;
    }

    /* 新增：应答下拉框和手工输入最小宽度 */
    table.soc-table td:nth-child(6) select,
    table.soc-table td:nth-child(6) input,
    table.soc-table td:nth-child(6) textarea {
      min-width: 80px;
      max-width: 180px;
      text-align: left;
    }

    /* 新增：AnswerType列宽度缩小，内容左对齐 */
    table.soc-table th:nth-child(5),
    table.soc-table td:nth-child(5) {
      min-width: 60px;
      max-width: 80px;
      width: 1%;
      text-align: left;
      /* 左对齐 */
      white-space: nowrap;
    }

    table.soc-table td:nth-child(5) span {
      margin: 0;
      display: inline-block;
    }

    /* 操作列冻结在右侧 */
    table.soc-table th:last-child,
    table.soc-table td:last-child {
      position: sticky;
      right: 0;
      z-index: 3;
      background: #fff;
      min-width: 120px;
      /* 增加操作列宽度，防止内容被遮挡 */
      max-width: 180px;
      box-shadow: -4px 0 12px #f0f0f0;
      border-left: 1px solid #e8eaec;
    }

    /* 备注和索引可滚动查看 */
    table.soc-table td:nth-child(8),
    table.soc-table td:nth-child(9) {
      max-width: 220px;
      overflow-x: auto;
      white-space: nowrap;
    }

    @media (max-width: 1200px) {
      #answerTableCard {
        overflow-x: auto;
      }

      table.soc-table {
        min-width: 1200px;
      }

      table.soc-table th:last-child,
      table.soc-table td:last-child {
        min-width: 100px;
        max-width: 140px;
      }
    }
  </style>
</head>

<body>
  <div class="soc-layout">
    <!-- 任务区/侧边栏 -->
    <aside class="soc-sidebar" id="socSidebar">
      <div class="soc-header" style="display:flex;align-items:center;justify-content:space-between;">
        <span>任务区</span>
        <button id="taskManageBtn"
          style="background:#e6f7ff;color:#1765d5;border:1px solid #91d5ff;border-radius:4px;padding:2px 10px;cursor:pointer;font-size:13px;line-height:1;">📋
          任务管理</button>
      </div>
      <div style="padding:0;">
        <div id="taskTabBar" style="display:flex;">
          <button class="soc-tab-btn" data-tab="todo">待办任务</button>
          <button class="soc-tab-btn" data-tab="history">历史归档</button>
          <button class="soc-tab-btn" data-tab="template">模板管理</button>
        </div>
        <div id="taskTabContent" style="padding:12px 8px 0 8px;"></div>
      </div>
      <button class="soc-hide-btn" title="收起侧边栏" id="hideSidebarBtn">&lt;</button>
    </aside>
    <!-- 内容主区 -->
    <main class="soc-content" id="socContent">
      <div class="soc-header">SOC应答任务</div>
      <div class="soc-main">
        <!-- 表格卡片 -->
        <div id="answerTableCard">
          <div class="soc-toolbar" style="display:flex;align-items:center;gap:0;padding:0 0 12px 0;">
            <!-- 操作按钮区 -->
            <div style="display:flex;gap:10px;">
              <button id="importExcelBtn" class="soc-btn soc-btn-blue">导入</button>
              <button id="addSingleEntryBtn" class="soc-btn soc-btn-2">新增单条</button>
              <input type="file" id="importExcelInput" accept=".xlsx,.xls" style="display:none;" />
              <button id="exportBtn" class="soc-btn soc-btn-2">导出</button>
              <button id="batchReAnswerBtn" class="soc-btn soc-btn-2">开始应答</button>
              <div style="position:relative;">
                <button id="mainActionBtn" class="soc-btn soc-btn-3">更多 ▾</button>
                <div id="mainActionMenu"
                  style="display:none;position:absolute;left:0;top:36px;z-index:20;background:#fff;border:1px solid #ececec;box-shadow:0 2px 8px #eee;border-radius:6px;min-width:160px;overflow:hidden;">
                  <div id="exportSettingBtn" class="main-action-item" data-act="exportSetting" tabindex="0"
                    style="padding:8px 16px;cursor:pointer;transition:background .2s;">⚙️ 导出设置</div>
                  <div id="openParamSettingBtn" class="main-action-item" data-act="openParamSetting" tabindex="0"
                    style="padding:8px 16px;cursor:pointer;transition:background .2s;">⚙️ 参数设置</div>
                  <div id="openReferenceDocBtn" class="main-action-item" data-act="openReferenceDoc" tabindex="0"
                    style="padding:8px 16px;cursor:pointer;transition:background .2s;">📑 参考文档</div>
                  <div id="priorityConfigBtn" class="main-action-item" data-act="priorityConfig" tabindex="0"
                    style="padding:8px 16px;cursor:pointer;transition:background .2s;">🔧 优先级配置</div>
                  <div id="satisfactionCalcBtn" class="main-action-item" data-act="satisfactionCalc" tabindex="0"
                    style="padding:8px 16px;cursor:pointer;transition:background .2s;">📊 满足度计算</div>
                  <div id="viewSimilarBtn" class="main-action-item" data-act="viewSimilar" tabindex="0"
                    style="padding:8px 16px;cursor:pointer;transition:background .2s;">🔍 查看相似条目</div>
                  <div id="historyRecordBtn" class="main-action-item" data-act="historyRecord" tabindex="0"
                    style="padding:8px 16px;cursor:pointer;transition:background .2s;">🕓 历史记录</div>
                  <div style="border-top:1px solid #f0f0f0;margin:4px 0;"></div>
                  <div id="clearAllBtn" class="main-action-item" data-act="clearAll" tabindex="0"
                    style="padding:8px 16px;cursor:pointer;transition:background .2s;color:#ff4d4f;">🗑️ 清空全部条目</div>
                </div>
                <button id="batchDeleteBtn" class="soc-btn soc-btn-4">删除</button>
              </div>
            </div>
            <span style="flex:1"></span>
          </div>
          <div style="display:flex;align-items:center;gap:12px;margin-bottom:8px;">
            <span id="similarTip" style="color:#faad14;display:none;">发现<span id="similarCount"></span>组相似条目，<a href="#"
                id="showSimilarBtn">查看</a></span>
            <span id="productFilterTip" style="color:#1765d5;font-size:13px;display:none;">当前筛选产品：<span
                id="currentFilterProduct"></span></span>
          </div>
          <table class="soc-table" id="answerTable">
            <thead>
              <tr>
                <th><input type="checkbox" id="selectAllRow" /></th>
                <th style="width:60px;">
                  编号
                  <input id="searchNo" type="text" placeholder="筛选"
                    style="width:40px;font-size:12px;margin-left:2px;" />
                </th>
                <th style="min-width:120px;width:180px;max-width:220px;">
                  条目描述
                  <input id="searchDesc" type="text" placeholder="筛选"
                    style="width:80px;font-size:12px;margin-left:2px;" />
                </th>
                <th style="min-width:50px;">
                  产品
                  <div style="margin-top:4px;">
                    <select id="searchProduct"
                      style="height:28px;border:none;outline:none;background:transparent;font-size:13px;padding:4px;width:100%;box-sizing:border-box;border-radius:0;">
                      <option value="">全部</option>
                      <option value="5GC">5GC</option>
                      <option value="VoLTE">VoLTE</option>
                      <option value="IMS">IMS</option>
                    </select>
                  </div>
                </th>
                <th style="min-width:30px;">
                  方式
                  <div style="margin-top:4px;">
                    <select id="searchAnswerType"
                      style="height:28px;border:none;outline:none;background:transparent;font-size:13px;padding:4px;width:100%;box-sizing:border-box;border-radius:0;">
                      <option value="">全部</option>
                      <option value="AI">AI</option>
                      <option value="人工">人工</option>
                    </select>
                  </div>
                </th>
                <th style="min-width:30px;">
                  应答
                  <div style="margin-top:4px;">
                    <select id="searchAnswer"
                      style="height:28px;border:none;outline:none;background:transparent;font-size:13px;padding:4px;width:100%;box-sizing:border-box;border-radius:0;">
                      <option value="">全部</option>
                      <option value="FC">FC</option>
                      <option value="PC">PC</option>
                      <option value="NC">NC</option>
                      <option value="N/A">N/A</option>
                      <option value="未应答">未应答</option>
                    </select>
                  </div>
                </th>
                <th style="min-width:150px;">
                  应答说明
                  <div style="margin-top:4px;">
                    <input type="text" id="searchExplain" placeholder="应答说明"
                      style="height:28px;border:none;outline:none;background:transparent;font-size:13px;padding:4px;width:100%;box-sizing:border-box;border-radius:0;" />
                  </div>
                </th>
                <th style="min-width:120px;">索引</th>
                <th style="min-width:60px;">
                  备注
                  <div style="margin-top:4px;">
                    <input type="text" id="searchRemark" placeholder="备注"
                      style="height:28px;border:none;outline:none;background:transparent;font-size:13px;padding:4px;width:100%;box-sizing:border-box;border-radius:0;" />
                  </div>
                </th>
                <th style="min-width:40px;padding-left:8px;">操作</th>
              </tr>
            </thead>
            <tbody id="answerTableBody"></tbody>
          </table>
          <!-- 分页条 -->
          <div id="tablePagination"
            style="display:flex;align-items:center;justify-content:flex-end;gap:12px;margin:12px 0 0 0;">
            <span>每页</span>
            <select id="pageSizeSelect" style="width:60px;height:28px;">
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
            <span>条</span>
            <button id="prevPageBtn" style="padding:2px 10px;">上一页</button>
            <span id="pageInfo"></span>
            <button id="nextPageBtn" style="padding:2px 10px;">下一页</button>
          </div>
        </div>
        <!-- 详情弹窗容器 -->
        <div id="answerDetailDialog" style="display:none;"></div>
        <!-- 其它内容区块可继续补充 -->
      </div>
    </main>
    <!-- 对话区/右侧栏 -->
    <aside class="soc-chatbar" id="socChatbar">
      <div class="soc-header">投标Agent</div>
      <div style="padding:0;display:flex;flex-direction:column;height:calc(100% - 48px);position:relative;">
        <!-- 其它内容区 -->
        <div style="flex:1;display:flex;flex-direction:column;overflow:hidden;">

          <!-- 对话历史区 -->
          <div style="padding:8px 12px 0 12px;flex:1;overflow:auto;" id="luiHistoryWrap">
            <div id="luiHistory" style="font-size:13px;line-height:1.7;display:flex;flex-direction:column;gap:8px;">
            </div>
          </div>
        </div>
        <!-- 快捷指令栏 -->
        <div style="padding:8px 12px 0 12px; min-height:60px;">
          <div id="quickCmdHeader"
            style="font-weight:500;margin-bottom:4px;cursor:pointer;display:flex;justify-content:space-between;align-items:center;user-select:none;background:#fff;position:relative;">
            <span>快捷指令</span>
            <span id="quickCmdToggle" style="transition: transform 0.2s; display:inline-block;">▼</span>
          </div>
          <div id="quickCmdBar" style="display:flex;flex-wrap:wrap;gap:6px 8px;transition: all 0.3s ease;"></div>
        </div>
        <!-- LUI输入区，固定底部 -->
        <form id="luiForm"
          style="display:flex;gap:4px;padding:10px 12px 10px 12px;background:#fff;box-shadow:0 -2px 8px #f0f0f0;position:absolute;left:0;right:0;bottom:0;">
          <input id="luiInput" type="text"
            placeholder="请输入自然语言描述，如'帮我设置项目参数：产品5GC和VoLTE，国家泰国，运营商AIS'或'上传招标文件.pdf，再分析5GC安全要求'"
            style="flex:1;padding:8px 12px;border-radius:8px;border:1px solid #d9d9d9;font-size:14px;outline:none;transition:border .2s;" />
          <button type="submit"
            style="background:#1765d5;color:#fff;border:none;border-radius:8px;padding:8px 16px;cursor:pointer;font-size:14px;">发送</button>
        </form>

        <!-- 智能提示模板区域 -->
        <div id="smartPromptArea"
          style="position:absolute;left:0;right:0;bottom:60px;background:#fff;border-top:1px solid #f0f0f0;padding:8px 12px;display:none;">
          <div style="font-size:12px;color: #fbfbfb;;margin-bottom:6px;">💡 智能推荐：</div>
          <div id="smartPrompts" style="display:flex;flex-wrap:wrap;gap:6px;"></div>
        </div>
      </div>
      <button class="soc-hide-btn" title="收起对话区" id="hideChatbarBtn">&gt;</button>
    </aside>
  </div>
  <div id="outline-float" style="position:fixed;top:80px;left:0;z-index:9999;transition:all .2s;">
    <div id="outline-panel"
      style="width:260px;background:#fff;border-radius:0 12px 12px 0;box-shadow:2px 0 12px #eee;padding:12px 8px 12px 0;overflow-y:auto;max-height:80vh;display:block;">
      <div style="display:flex;align-items:center;justify-content:space-between;margin-bottom:8px;padding-left:8px;">
        <span style="font-weight:600;font-size:16px;color:#333;">大纲</span>
        <button id="outline-collapse"
          style="background:none;border:none;font-size:18px;cursor:pointer;color:#666;padding:4px;">&laquo;</button>
      </div>
      <div id="outline-tree" style="padding-left:8px;"></div>
    </div>
    <button id="outline-expand"
      style="display:none;width:36px;height:36px;border-radius:50%;background:#1765d5;color:#fff;border:none;box-shadow:0 2px 8px #eee;cursor:pointer;font-size:18px;">&#9776;</button>
  </div>
  <script>
    // ====== 分页状态 ======
    let tablePage = { page: 1, pageSize: 10 };
    // === 参数设置弹窗 ===
    function showParamSettingDialog() {
      let html = `<div style='width:520px;max-width:96vw;'>
    <div style='font-size:18px;font-weight:500;margin-bottom:12px;'>参数设置</div>
    <div id='tabParamContent' style='padding:18px 8px 8px 8px;'>
      <form id="paramFormDialog" style="display:flex;flex-wrap:wrap;gap:16px 32px;align-items:flex-end;">
        <div>
          <label>产品选择 <span style="color:#ff4d4f;">*</span></label><br/>
          <div class="tree-select-container" style="position:relative;min-width:200px;width:220px;">
            <div class="tree-select-input" style="border:1px solid #d9d9d9;border-radius:4px;padding:8px 12px;min-height:36px;background:#fff;cursor:pointer;display:flex;flex-wrap:wrap;gap:4px;align-items:center;" onclick="toggleProductTree()">
              <span class="tree-select-placeholder" style="color:#999;">请选择产品</span>
            </div>
            <div class="tree-select-dropdown" style="position:absolute;top:100%;left:0;right:0;background:#fff;border:1px solid #d9d9d9;border-radius:4px;box-shadow:0 2px 8px rgba(0,0,0,0.15);z-index:1000;max-height:300px;overflow-y:auto;display:none;">
              <div class="tree-select-search" style="padding:8px;border-bottom:1px solid #f0f0f0;">
                <input type="text" placeholder="搜索产品..." style="width:100%;padding:6px 8px;border:1px solid #d9d9d9;border-radius:4px;font-size:12px;" onkeyup="filterProductTree(this.value)">
              </div>
              <div class="tree-select-options">
                <div class="tree-node" data-value="5GC" data-type="工程">
                  <span class="tree-node-toggle" onclick="toggleTreeNode(this)">▶</span>
                  <span class="tree-node-label">5GC</span>
                  <span class="tree-node-type">(工程)</span>
                </div>
                <div class="tree-node" data-value="VoLTE" data-type="售前">
                  <span class="tree-node-toggle" onclick="toggleTreeNode(this)">▶</span>
                  <span class="tree-node-label">VoLTE</span>
                  <span class="tree-node-type">(售前)</span>
                </div>
                <div class="tree-node" data-value="IMS" data-type="售前">
                  <span class="tree-node-toggle" onclick="toggleTreeNode(this)">▶</span>
                  <span class="tree-node-label">IMS</span>
                  <span class="tree-node-type">(售前)</span>
                </div>
                <div class="tree-node" data-value="核心网" data-type="工程">
                  <span class="tree-node-toggle" onclick="toggleTreeNode(this)">▶</span>
                  <span class="tree-node-label">核心网</span>
                  <span class="tree-node-type">(工程)</span>
                </div>
                <div class="tree-node" data-value="接入网" data-type="工程">
                  <span class="tree-node-toggle" onclick="toggleTreeNode(this)">▶</span>
                  <span class="tree-node-label">接入网</span>
                  <span class="tree-node-type">(工程)</span>
                </div>
              </div>
            </div>
            <input type="hidden" id="productSelectDialog" value="">
          </div>
        </div>
        <div>
          <label>国家</label><br/>
          <div class="searchable-select-container" style="position:relative;min-width:120px;width:140px;">
            <input type="text" id="countrySelectDialog" placeholder="请选择或输入国家" style="width:100%;padding:8px 12px;border:1px solid #d9d9d9;border-radius:4px;font-size:14px;" onfocus="showCountryDropdown()" onblur="hideCountryDropdown()" onkeyup="filterCountryOptions(this.value)">
            <div class="searchable-dropdown" style="position:absolute;top:100%;left:0;right:0;background:#fff;border:1px solid #d9d9d9;border-radius:4px;box-shadow:0 2px 8px rgba(0,0,0,0.15);z-index:1000;max-height:200px;overflow-y:auto;display:none;">
              <div class="dropdown-option" data-value="泰国">泰国</div>
              <div class="dropdown-option" data-value="越南">越南</div>
              <div class="dropdown-option" data-value="印尼">印尼</div>
              <div class="dropdown-option" data-value="马来西亚">马来西亚</div>
              <div class="dropdown-option" data-value="新加坡">新加坡</div>
              <div class="dropdown-option" data-value="菲律宾">菲律宾</div>
            </div>
          </div>
        </div>
        <div>
          <label>客户</label><br/>
          <div class="searchable-select-container" style="position:relative;min-width:120px;width:140px;">
            <input type="text" id="operatorSelectDialog" placeholder="请选择或输入客户" style="width:100%;padding:8px 12px;border:1px solid #d9d9d9;border-radius:4px;font-size:14px;" onfocus="showOperatorDropdown()" onblur="hideOperatorDropdown()" onkeyup="filterOperatorOptions(this.value)">
            <div class="searchable-dropdown" style="position:absolute;top:100%;left:0;right:0;background:#fff;border:1px solid #d9d9d9;border-radius:4px;box-shadow:0 2px 8px rgba(0,0,0,0.15);z-index:1000;max-height:200px;overflow-y:auto;display:none;">
              <div class="dropdown-option" data-value="AIS">AIS</div>
              <div class="dropdown-option" data-value="Viettel">Viettel</div>
              <div class="dropdown-option" data-value="Telkomsel">Telkomsel</div>
              <div class="dropdown-option" data-value="Maxis">Maxis</div>
              <div class="dropdown-option" data-value="Singtel">Singtel</div>
              <div class="dropdown-option" data-value="PLDT">PLDT</div>
            </div>
          </div>
        </div>
        <div>
          <label>项目名称</label><br/>
          <input id="projectInputDialog" type="text" style="width:160px;" placeholder="请输入项目名称" />
        </div>
        <div>
          <label>参数模板</label><br/>
          <select id="templateSelectDialog" style="min-width:120px;width:160px;"></select>
        </div>
        <div style="display:flex;gap:8px;">
          <button type="button" id="saveTemplateBtnDialog">保存为模板</button>
          <button type="button" id="restoreDefaultBtnDialog">恢复默认</button>
          <button type="button" id="clearParamBtnDialog">清空参数</button>
        </div>
      </form>
      <div id="paramChangedBarDialog" style="display:none;margin-top:10px;padding:8px 16px;background:#fffbe6;border:1px solid #ffe58f;border-radius:4px;color:#ad8b00;">
        参数已变更，建议 <button id="reAnswerBtnDialog" style="color:#1765d5;background:none;border:none;cursor:pointer;">全部重新应答</button>
      </div>
    </div>
    <div style='text-align:right;margin-top:18px;display:flex;justify-content:flex-end;gap:12px;'>
      <button id='confirmParamSettingDlg' style='background:#1765d5;color:#fff;border:none;border-radius:4px;padding:8px 24px;cursor:pointer;font-size:14px;transition:all 0.3s;'>确定</button>
      <button id='cancelParamSettingDlg' style='background:#f5f5f5;color:#666;border:1px solid #d9d9d9;border-radius:4px;padding:8px 24px;cursor:pointer;font-size:14px;transition:all 0.3s;'>取消</button>
    </div>
  </div>`;
      const dlg = document.createElement('div');
      dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.18);display:flex;align-items:center;justify-content:center;'><div style='background:#fff;padding:0 0 0 0;border-radius:12px;min-width:340px;box-shadow:0 4px 24px #aaa;'>${html}</div></div>`;
      document.body.appendChild(dlg);
      // 初始化树形选择器和搜索下拉框
      initTreeSelect(dlg);
      initSearchableSelects(dlg);
      // 取消按钮 - 直接关闭对话框，不保存任何更改
      const cancelBtn = dlg.querySelector('#cancelParamSettingDlg');
      cancelBtn.onclick = function () {
        document.body.removeChild(dlg);
      };

      // 取消按钮悬停效果
      cancelBtn.onmouseenter = function () {
        this.style.background = '#e6e6e6';
        this.style.borderColor = '#bfbfbf';
      };
      cancelBtn.onmouseleave = function () {
        this.style.background = '#f5f5f5';
        this.style.borderColor = '#d9d9d9';
      };

      // 确定按钮 - 保存参数设置并关闭对话框
      const confirmBtn = dlg.querySelector('#confirmParamSettingDlg');
      confirmBtn.onclick = function () {
        // 获取表单数据
        const productSelect = dlg.querySelector('#productSelectDialog');
        const countrySelect = dlg.querySelector('#countrySelectDialog');
        const operatorSelect = dlg.querySelector('#operatorSelectDialog');
        const projectInput = dlg.querySelector('#projectInputDialog');

        // 收集选中的产品（从隐藏输入框获取）
        const selectedProducts = productSelect.value ? productSelect.value.split(',') : [];

        // 基本验证 - 只有产品是必填参数
        if (selectedProducts.length === 0) {
          alert('请至少选择一个产品（必填）');
          dlg.querySelector('.tree-select-input').focus();
          return;
        }

        // 保存参数到本地存储或全局变量
        const paramData = {
          products: selectedProducts,
          country: countrySelect.value,
          operator: operatorSelect.value,
          projectName: projectInput.value.trim(),
          timestamp: new Date().toISOString()
        };

        // 保存参数（这里可以根据实际需求调用相应的保存函数）
        localStorage.setItem('currentParams', JSON.stringify(paramData));

        // 显示保存成功提示
        const productNames = selectedProducts.join(', ');
        const successMsg = `参数设置已保存\n产品: ${productNames}${paramData.country ? '\n国家: ' + paramData.country : ''}${paramData.operator ? '\n运营商: ' + paramData.operator : ''}${paramData.projectName ? '\n项目: ' + paramData.projectName : ''}`;
        alert(successMsg);

        // 关闭对话框
        document.body.removeChild(dlg);

        // 如果需要，可以触发页面刷新或其他更新操作
        // renderAnswerTable && renderAnswerTable();
      };

      // 确定按钮悬停效果
      confirmBtn.onmouseenter = function () {
        this.style.background = '#1454c0';
      };
      confirmBtn.onmouseleave = function () {
        this.style.background = '#1765d5';
      };

      // 添加键盘事件支持
      document.addEventListener('keydown', function handleKeydown(e) {
        if (e.key === 'Escape') {
          cancelBtn.click();
          document.removeEventListener('keydown', handleKeydown);
        } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
          confirmBtn.click();
          document.removeEventListener('keydown', handleKeydown);
        }
      });
      // 复用原有参数表单事件绑定和数据填充逻辑
      bindDialogParamEvents && bindDialogParamEvents();
      renderTemplateSelect && renderTemplateSelect();
      // 若有默认模板自动填充
      const def = getDefaultTemplate && getDefaultTemplate();
      if (def) setParamForm && setParamForm(def);
    }

    // === 参考文档弹窗 ===
    function showReferenceDocDialog() {
      let html = `<div style='width:520px;max-width:96vw;'>
    <div style='font-size:18px;font-weight:500;margin-bottom:12px;'>参考文档区</div>
    <div id='tabMaterialContent' style='padding:18px 8px 8px 8px;'>
      <!-- 文档上传区域 -->
      <div style="margin-bottom:20px;">
        <div style="font-weight:500;margin-bottom:12px;color:#333;">📁 文档上传</div>
        <div style="display:grid;grid-template-columns:1fr 1fr;gap:12px;">
          <div style="border:2px dashed #d9d9d9;border-radius:8px;padding:16px;text-align:center;transition:all 0.3s;">
            <div style="font-size:24px;margin-bottom:8px;">📄</div>
            <div style="font-weight:500;margin-bottom:4px;">项目文档</div>
            <div style="font-size:12px;color:#666;margin-bottom:12px;">招标文件、技术协议等</div>
            <button type="button" id="uploadProjectDocBtnDialog2" class="soc-btn soc-btn-blue" style="width:100%;">选择文件</button>
            <input type="file" id="uploadProjectDocInputDialog2" accept=".pdf,.doc,.docx,.xlsx,.xls" style="display:none;" />
          </div>
          <div style="border:2px dashed #d9d9d9;border-radius:8px;padding:16px;text-align:center;transition:all 0.3s;">
            <div style="font-size:24px;margin-bottom:8px;">📋</div>
            <div style="font-weight:500;margin-bottom:4px;">历史SOC文档</div>
            <div style="font-size:12px;color:#666;margin-bottom:12px;">历史应答文档、经验总结</div>
            <button type="button" id="uploadSocDocBtnDialog2" class="soc-btn soc-btn-blue" style="width:100%;">选择文件</button>
            <input type="file" id="uploadSocDocInputDialog2" accept=".pdf,.doc,.docx,.xlsx,.xls" style="display:none;" />
          </div>
        </div>
      </div>
      <!-- 文档选择区域 -->
      <div style="margin-bottom:20px;">
        <div style="font-weight:500;margin-bottom:12px;color:#333;">📚 文档选择</div>
        <div style="display:grid;grid-template-columns:1fr 1fr;gap:12px;">
          <div style="border:1px solid #e8e8e8;border-radius:8px;padding:16px;background:#fafafa;">
            <div style="font-weight:500;margin-bottom:8px;">📖 文档库</div>
            <div style="font-size:12px;color:#666;margin-bottom:12px;">从Studio知识库选择参考文档</div>
            <button type="button" id="selectLibDocBtnDialog2" class="soc-btn" style="width:100%;">选择参考文档</button>
          </div>
          <div style="border:1px solid #e8e8e8;border-radius:8px;padding:16px;background:#fafafa;">
            <div style="font-weight:500;margin-bottom:8px;">🕓 历史文档</div>
            <div style="font-size:12px;color:#666;margin-bottom:12px;">复用历史上传的文档</div>
            <button type="button" id="selectHistoryDocBtnDialog2" class="soc-btn" style="width:100%;">选择历史文档</button>
          </div>
        </div>
      </div>
      <!-- 已选文档展示区域 -->
      <div style="margin-bottom:20px;">
        <div style="font-weight:500;margin-bottom:12px;color:#333;display:flex;align-items:center;justify-content:space-between;">
          <span>📎 已选文档</span>
          <span id="materialCountDialog2" style="font-size:12px;color:#666;">0个文档</span>
        </div>
        <div id="materialTagsDialog2" style="min-height:60px;border:1px solid #e8e8e8;border-radius:6px;padding:12px;background:#fafafa;display:flex;flex-wrap:wrap;gap:8px;align-items:flex-start;">
          <span style="color:#aaa;font-size:13px;align-self:center;">暂无已选文档</span>
        </div>
      </div>
      <!-- 操作按钮区域 -->
      <div style="display:flex;gap:12px;justify-content:space-between;align-items:center;">
        <div style="display:flex;gap:8px;">
          <button type="button" id="checkMaterialBtnDialog2" class="soc-btn soc-btn-yellow">✓ 校验准备项</button>
          <button type="button" id="saveMaterialTemplateBtnDialog2" class="soc-btn">💾 保存为模板</button>
        </div>
        <div style="font-size:12px;">
          <span id="materialStatusDialog2" class="material-status incomplete">准备状态：未完成</span>
        </div>
      </div>
    </div>
    <div style='text-align:right;margin-top:18px;'><button id='closeReferenceDocDlg' style='background:#1765d5;color:#fff;border:none;border-radius:4px;padding:6px 24px;cursor:pointer;'>关闭</button></div>
  </div>`;
      const dlg = document.createElement('div');
      dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.18);display:flex;align-items:center;justify-content:center;'><div style='background:#fff;padding:0 0 0 0;border-radius:12px;min-width:340px;box-shadow:0 4px 24px #aaa;'>${html}</div></div>`;
      document.body.appendChild(dlg);
      dlg.querySelector('#closeReferenceDocDlg').onclick = function () {
        document.body.removeChild(dlg);
      };
      // 复用原有素材区事件绑定和渲染逻辑
      // 上传、选择、标签、校验、保存模板等逻辑同前
      // ...（可复用前述 showReferenceDocDialog 里的事件绑定代码）...
      // 上传项目文档
      const uploadProjectDocBtnDialog2 = dlg.querySelector('#uploadProjectDocBtnDialog2');
      const uploadProjectDocInputDialog2 = dlg.querySelector('#uploadProjectDocInputDialog2');
      if (uploadProjectDocBtnDialog2) uploadProjectDocBtnDialog2.onclick = () => uploadProjectDocInputDialog2 && uploadProjectDocInputDialog2.click();
      if (uploadProjectDocInputDialog2) uploadProjectDocInputDialog2.onchange = function () {
        if (!this.files[0]) return;
        const file = this.files[0];
        const list = getMaterialList();
        list.push({ name: file.name, type: '项目文档', time: Date.now(), id: 'p' + Date.now() });
        saveMaterialList(list);
        renderMaterialTagsDialog2();
        alert('上传成功（模拟）');
        this.value = '';
      };
      // 上传历史SOC文档
      const uploadSocDocBtnDialog2 = dlg.querySelector('#uploadSocDocBtnDialog2');
      const uploadSocDocInputDialog2 = dlg.querySelector('#uploadSocDocInputDialog2');
      if (uploadSocDocBtnDialog2) uploadSocDocBtnDialog2.onclick = () => uploadSocDocInputDialog2 && uploadSocDocInputDialog2.click();
      if (uploadSocDocInputDialog2) uploadSocDocInputDialog2.onchange = function () {
        if (!this.files[0]) return;
        const file = this.files[0];
        const list = getMaterialList();
        list.push({ name: file.name, type: '历史SOC文档', time: Date.now(), id: 's' + Date.now() });
        saveMaterialList(list);
        renderMaterialTagsDialog2();
        alert('上传成功（模拟）');
        this.value = '';
      };
      // 文档库选择
      const selectLibDocBtnDialog2 = dlg.querySelector('#selectLibDocBtnDialog2');
      if (selectLibDocBtnDialog2) selectLibDocBtnDialog2.onclick = function () {
        const libDocs = [
          { name: '5GC技术白皮书', type: '文档库', id: 'lib1' },
          { name: 'VoLTE解决方案', type: '文档库', id: 'lib2' },
          { name: 'IMS部署指南', type: '文档库', id: 'lib3' }
        ];
        const sel = prompt('请输入要选择的文档编号（用,分隔）：\n1. 5GC技术白皮书\n2. VoLTE解决方案\n3. IMS部署指南');
        if (!sel) return;
        const idxs = sel.split(',').map(s => parseInt(s.trim()) - 1).filter(i => i >= 0 && i < libDocs.length);
        if (idxs.length === 0) return alert('未选择');
        const list = getMaterialList();
        idxs.forEach(i => {
          if (!list.some(d => d.id === libDocs[i].id)) list.push({ ...libDocs[i], time: Date.now() });
        });
        saveMaterialList(list);
        renderMaterialTagsDialog2();
        alert('已添加文档库文档');
      };
      // 历史文档选择
      const selectHistoryDocBtnDialog2 = dlg.querySelector('#selectHistoryDocBtnDialog2');
      if (selectHistoryDocBtnDialog2) selectHistoryDocBtnDialog2.onclick = function () {
        const all = JSON.parse(localStorage.getItem('socHistoryDocs') || '[]');
        if (all.length === 0) return alert('暂无历史上传文档');
        let html = '<div style="max-height:300px;overflow:auto;">';
        html += all.map((d, i) =>
          `<div style='margin-bottom:6px;'><input type='checkbox' id='histdoc2${i}' data-idx='${i}' />
      <span>${d.name} <span style='color:#aaa;font-size:12px;'>(${d.type})</span></span>
      <button data-act='preview' data-idx='${i}'>预览</button>
      <button data-act='del' data-idx='${i}'>删除</button></div>`
        ).join('');
        html += '</div><div style="margin-top:8px;text-align:right;"><button id="histDocBatchAdd2">批量关联</button> <button id="histDocClose2">关闭</button></div>';
        const dlg2 = document.createElement('div');
        dlg2.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:1001;background:rgba(0,0,0,0.15);display:flex;align-items:center;justify-content:center;'><div style='background:#fff;padding:24px 32px;border-radius:8px;min-width:320px;box-shadow:0 2px 8px #aaa;'>${html}</div></div>`;
        document.body.appendChild(dlg2);
        dlg2.onclick = function (e) {
          if (e.target.id === 'histDocClose2') document.body.removeChild(dlg2);
          if (e.target.id === 'histDocBatchAdd2') {
            const sel = Array.from(dlg2.querySelectorAll('input[type=checkbox]:checked')).map(c => parseInt(c.dataset.idx));
            if (sel.length === 0) return alert('未选择');
            const list = getMaterialList();
            sel.forEach(i => { if (!list.some(d => d.id === all[i].id)) list.push(all[i]); });
            saveMaterialList(list);
            renderMaterialTagsDialog2();
            alert('已批量关联');
            document.body.removeChild(dlg2);
          }
          if (e.target.dataset.act === 'preview') alert('预览文档：' + all[e.target.dataset.idx].name + '（模拟）');
          if (e.target.dataset.act === 'del') {
            if (confirm('确定删除？')) {
              all.splice(e.target.dataset.idx, 1);
              localStorage.setItem('socHistoryDocs', JSON.stringify(all));
              document.body.removeChild(dlg2);
              if (selectHistoryDocBtnDialog2) selectHistoryDocBtnDialog2.onclick();
            }
          }
        };
      };
      // 已选文档标签渲染
      function renderMaterialTagsDialog2() {
        const list = getMaterialList();
        const materialTagsDialog2 = dlg.querySelector('#materialTagsDialog2');
        const materialCountDialog2 = dlg.querySelector('#materialCountDialog2');
        if (!materialTagsDialog2) return;
        if (list.length === 0) materialTagsDialog2.innerHTML = '<span style="color:#aaa;">暂无已选文档</span>';
        else materialTagsDialog2.innerHTML = list.map((d, i) =>
          `<span class='material-tag' style='background:${getDocTypeColor(d.type)};border:1px solid ${getDocTypeBorderColor(d.type)};'>
        <span class='doc-name'>${d.name}</span>
        <span class='doc-type'>${d.type}</span>
        <span class='doc-action' data-act='preview' data-idx='${i}' title='预览文档'>👁️</span>
        <span class='doc-action' data-act='del' data-idx='${i}' title='删除文档'>✖️</span>
      </span>`
        ).join('');
        if (materialCountDialog2) materialCountDialog2.textContent = list.length + '个文档';
      }
      renderMaterialTagsDialog2();
      // 标签区事件
      const materialTagsDialog2 = dlg.querySelector('#materialTagsDialog2');
      if (materialTagsDialog2) materialTagsDialog2.onclick = function (e) {
        if (e.target.dataset.act === 'preview') alert('预览文档：' + getMaterialList()[e.target.dataset.idx].name + '（模拟）');
        if (e.target.dataset.act === 'del') {
          const list = getMaterialList();
          list.splice(e.target.dataset.idx, 1);
          saveMaterialList(list);
          renderMaterialTagsDialog2();
        }
      };
      // 校验按钮
      const checkMaterialBtnDialog2 = dlg.querySelector('#checkMaterialBtnDialog2');
      if (checkMaterialBtnDialog2) checkMaterialBtnDialog2.onclick = function () {
        const param = getParamForm();
        const mat = getMaterialList();
        let msg = '';
        if (!param.products.length) msg += '产品未选择\n';
        if (!param.country) msg += '国家未选择\n';
        if (!param.operator) msg += '运营商未选择\n';
        if (!param.project) msg += '项目名称未填写\n';
        if (mat.length === 0) msg += '未上传/选择任何文档\n';
        if (msg) alert('请完善以下项：\n' + msg);
        else alert('校验通过，准备项完整！');
      };
      // 保存为模板按钮
      const saveMaterialTemplateBtnDialog2 = dlg.querySelector('#saveMaterialTemplateBtnDialog2');
      if (saveMaterialTemplateBtnDialog2) saveMaterialTemplateBtnDialog2.onclick = function () {
        const name = prompt('请输入准备模板名称');
        if (!name) return;
        let list = getAllTemplates();
        if (list.some(t => t.name === name)) { alert('模板名已存在'); return; }
        const tmpl = getParamForm();
        tmpl.id = 't' + Date.now();
        tmpl.name = name;
        tmpl.isDefault = false;
        tmpl.materials = getMaterialList();
        list.push(tmpl);
        saveAllTemplates(list);
        renderTemplateSelect();
        alert('准备模板已保存（含素材）');
        // 可选：renderTaskTab('template');
      };
    }
    // ... existing code ...
    // 侧边栏/对话区收起展开逻辑 + 状态记忆
    const sidebar = document.getElementById('socSidebar');
    const chatbar = document.getElementById('socChatbar');
    const content = document.getElementById('socContent');
    const hideSidebarBtn = document.getElementById('hideSidebarBtn');
    const hideChatbarBtn = document.getElementById('hideChatbarBtn');
    // 状态key
    const SIDEBAR_KEY = 'socSidebarHidden';
    const CHATBAR_KEY = 'socChatbarHidden';
    // 初始化
    function applySidebarState() {
      const hidden = localStorage.getItem(SIDEBAR_KEY) === '1';
      sidebar.style.display = hidden ? 'none' : '';
      if (hidden) {
        content.style.marginLeft = '0';
      } else {
        content.style.marginLeft = '';
      }
    }
    function applyChatbarState() {
      const hidden = localStorage.getItem(CHATBAR_KEY) === '1';
      chatbar.style.display = hidden ? 'none' : '';
      if (hidden) {
        content.style.marginRight = '0';
      } else {
        content.style.marginRight = '';
      }
    }
    if (hideSidebarBtn) hideSidebarBtn.onclick = function () {
      localStorage.setItem(SIDEBAR_KEY, '1');
      applySidebarState();
    };
    if (hideChatbarBtn) hideChatbarBtn.onclick = function () {
      localStorage.setItem(CHATBAR_KEY, '1');
      applyChatbarState();
    };
    // 双击内容区左/右边缘可展开
    content.ondblclick = function (e) {
      if (e.clientX < 40) {
        localStorage.setItem(SIDEBAR_KEY, '0');
        applySidebarState();
      }
      if (window.innerWidth - e.clientX < 40) {
        localStorage.setItem(CHATBAR_KEY, '0');
        applyChatbarState();
      }
    };
    // 页面加载时应用状态
    applySidebarState();
    applyChatbarState();
    // 响应式布局优化（移动端自动收起两侧栏）
    function handleResize() {
      if (window.innerWidth < 900) {
        sidebar.style.display = 'none';
        chatbar.style.display = 'none';
      } else {
        applySidebarState();
        applyChatbarState();
      }
    }
    window.addEventListener('resize', handleResize);
    handleResize();

    // 任务区tab切换
    const taskTabBar = document.getElementById('taskTabBar');
    const taskTabContent = document.getElementById('taskTabContent');
    let currentTab = 'todo';
    function renderTaskTab(tab) {
      currentTab = tab;
      Array.from(taskTabBar.querySelectorAll('button')).forEach(btn => {
        btn.style.background = btn.dataset.tab === tab ? '#e6eaf2' : '';
      });
      if (tab === 'todo') {
        taskTabContent.innerHTML = '<div>暂无待办任务</div>';
      } else if (tab === 'history') {
        taskTabContent.innerHTML = '<div>暂无历史归档</div>';
      } else if (tab === 'template') {
        renderTemplateManage();
      }
    }
    if (taskTabBar) taskTabBar.onclick = function (e) {
      if (e.target.tagName === 'BUTTON') renderTaskTab(e.target.dataset.tab);
    };
    renderTaskTab('todo');
    // 模板管理区渲染
    function renderTemplateManage() {
      const list = getAllTemplates();
      let html = '<div style="font-weight:500;margin-bottom:8px;">模板列表</div>';
      if (list.length === 0) html += '<div>暂无模板</div>';
      else html += '<ul style="padding-left:0;list-style:none;">' + list.map(t =>
        `<li style='margin-bottom:6px;'><span>${t.name}${t.isDefault ? ' <span style="color:#1765d5;font-size:12px;">(默认)</span>' : ''}</span>
        <button data-act="use" data-id="${t.id}" style="margin-left:8px;">使用</button>
        <button data-act="rename" data-id="${t.id}">重命名</button>
        <button data-act="del" data-id="${t.id}">删除</button>
        <button data-act="setDefault" data-id="${t.id}">设为默认</button>
        <button data-act="preview" data-id="${t.id}">预览</button></li>`
      ).join('') + '</ul>';
      taskTabContent.innerHTML = html;
    }
    // 模板数据结构与本地存储
    function getAllTemplates() {
      return JSON.parse(localStorage.getItem('socParamTemplates') || '[]');
    }
    function saveAllTemplates(list) {
      localStorage.setItem('socParamTemplates', JSON.stringify(list));
    }
    function getDefaultTemplate() {
      const list = getAllTemplates();
      return list.find(t => t.isDefault) || list[0];
    }
    // 参数表单与模板联动
    const productSelectDialog = document.getElementById('productSelectDialog');
    const countrySelectDialog = document.getElementById('countrySelectDialog');
    const operatorSelectDialog = document.getElementById('operatorSelectDialog');
    const projectInputDialog = document.getElementById('projectInputDialog');
    const templateSelectDialog = document.getElementById('templateSelectDialog');
    const saveTemplateBtn = document.getElementById('saveTemplateBtn');
    const restoreDefaultBtn = document.getElementById('restoreDefaultBtn');
    const clearParamBtn = document.getElementById('clearParamBtn');
    const paramChangedBar = document.getElementById('paramChangedBar');
    const reAnswerBtn = document.getElementById('reAnswerBtn');
    // 渲染模板下拉
    function renderTemplateSelect() {
      const list = getAllTemplates();
      if (!templateSelectDialog) return;
      templateSelectDialog.innerHTML = list.length === 0 ? '<option value="">无模板</option>' : list.map(t => `<option value="${t.id}"${t.isDefault ? " selected" : ""}>${t.name}${t.isDefault ? "(默认)" : ""}</option>`).join('');
    }
    // 表单赋值
    function setParamForm(tmpl) {
      if (!tmpl) return;
      if (productSelectDialog) Array.from(productSelectDialog.options).forEach(opt => { opt.selected = (tmpl.products || []).includes(opt.value) });
      if (countrySelectDialog) countrySelectDialog.value = tmpl.country || '';
      if (operatorSelectDialog) operatorSelectDialog.value = tmpl.operator || '';
      if (projectInputDialog) projectInputDialog.value = tmpl.project || '';
    }
    // 获取表单数据
    function getParamForm() {
      // 主参数表单空值保护
      if (!productSelectDialog || !countrySelectDialog || !operatorSelectDialog || !projectInputDialog) {
        return {
          products: [],
          country: '',
          operator: '',
          project: ''
        };
      }
      return {
        products: Array.from(productSelectDialog.selectedOptions).map(o => o.value),
        country: countrySelectDialog.value,
        operator: operatorSelectDialog.value,
        project: projectInputDialog.value
      };
    }

    // 获取已配置的产品列表
    function getConfiguredProducts() {
      // 优先从localStorage获取当前参数
      const currentParams = localStorage.getItem('currentParams');
      if (currentParams) {
        try {
          const params = JSON.parse(currentParams);
          return params.products || [];
        } catch (e) {
          console.error('解析当前参数失败:', e);
        }
      }
      
      // 如果localStorage中没有，则从表单获取
      const paramForm = getParamForm();
      return paramForm.products || [];
    }
    // 检查参数是否变更
    let lastParam = JSON.stringify(getParamForm());
    function checkParamChanged() {
      const now = JSON.stringify(getParamForm());
      if (now !== lastParam) {
        if (paramChangedBar) paramChangedBar.style.display = '';
      } else {
        if (paramChangedBar) paramChangedBar.style.display = 'none';
      }
    }
    // 监听表单变更
    [productSelectDialog, countrySelectDialog, operatorSelectDialog, projectInputDialog].forEach(el => {
      if (el) {
        el.onchange = checkParamChanged;
        el.oninput = checkParamChanged;
      }
    });
    // 弹窗参数表单事件绑定（如有）
    function bindDialogParamEvents() {
      const productSelectDialog = document.getElementById('productSelectDialog');
      const countrySelectDialog = document.getElementById('countrySelectDialog');
      const operatorSelectDialog = document.getElementById('operatorSelectDialog');
      const projectInputDialog = document.getElementById('projectInputDialog');
      [productSelectDialog, countrySelectDialog, operatorSelectDialog, projectInputDialog].forEach(el => {
        if (el) {
          el.onchange = checkParamChanged;
          el.oninput = checkParamChanged;
        }
      });
    }
    // 保存为模板
    if (saveTemplateBtn) {
      saveTemplateBtn.onclick = function () {
        const name = prompt('请输入模板名称');
        if (!name) return;
        let list = getAllTemplates();
        if (list.some(t => t.name === name)) { alert('模板名已存在'); return; }
        const tmpl = getParamForm();
        tmpl.id = 't' + Date.now();
        tmpl.name = name;
        tmpl.isDefault = false;
        list.push(tmpl);
        saveAllTemplates(list);
        renderTemplateSelect();
        alert('模板已保存');
        renderTaskTab('template');
      };
    }
    // 恢复默认
    if (restoreDefaultBtn) {
      restoreDefaultBtn.onclick = function () {
        const def = getDefaultTemplate();
        if (def) setParamForm(def);
        checkParamChanged();
      };
    }
    // 清空参数
    if (clearParamBtn) {
      clearParamBtn.onclick = function () {
        Array.from(productSelectDialog.options).forEach(opt => { opt.selected = false; });
        countrySelectDialog.value = '';
        operatorSelectDialog.value = '';
        projectInputDialog.value = '';
        checkParamChanged();
      };
    }
    // 模板切换
    if (templateSelectDialog) {
      templateSelectDialog.onchange = function () {
        const id = templateSelectDialog.value;
        const list = getAllTemplates();
        const t = list.find(t => t.id === id);
        if (t) setParamForm(t);
        checkParamChanged();
      };
    }
    // 重新应答按钮
    if (reAnswerBtn) {
      reAnswerBtn.onclick = function () {
        lastParam = JSON.stringify(getParamForm());
        paramChangedBar.style.display = 'none';
        alert('已触发全部重新应答（DEMO仅提示）');
      };
    }
    // 首次加载模板
    renderTemplateSelect();
    // 若有默认模板自动填充
    const def = getDefaultTemplate();
    if (def) setParamForm(def);
    lastParam = JSON.stringify(getParamForm());
    // 模板管理区按钮事件
    if (taskTabContent) taskTabContent.onclick = function (e) {
      if (!e.target.dataset.act) return;
      const id = e.target.dataset.id;
      let list = getAllTemplates();
      const idx = list.findIndex(t => t.id === id);
      if (idx === -1) return;
      if (e.target.dataset.act === 'use') {
        setParamForm(list[idx]);
        checkParamChanged();
        alert('已切换到该模板');
      } else if (e.target.dataset.act === 'rename') {
        const newName = prompt('请输入新名称', list[idx].name);
        if (!newName) return;
        if (list.some(t => t.name === newName)) { alert('模板名已存在'); return; }
        list[idx].name = newName;
        saveAllTemplates(list);
        renderTemplateManage();
        renderTemplateSelect();
      } else if (e.target.dataset.act === 'del') {
        if (confirm('确定删除该模板？')) {
          list.splice(idx, 1);
          saveAllTemplates(list);
          renderTemplateManage();
          renderTemplateSelect();
        }
      } else if (e.target.dataset.act === 'setDefault') {
        list.forEach((t, i) => t.isDefault = (i === idx));
        saveAllTemplates(list);
        renderTemplateManage();
        renderTemplateSelect();
      } else if (e.target.dataset.act === 'preview') {
        alert(JSON.stringify(list[idx], null, 2));
      }
    };

    // ===== 素材准备区逻辑 =====
    const uploadProjectDocBtn = document.getElementById('uploadProjectDocBtn');
    const uploadProjectDocInput = document.getElementById('uploadProjectDocInput');
    const uploadSocDocBtn = document.getElementById('uploadSocDocBtn');
    const uploadSocDocInput = document.getElementById('uploadSocDocInput');
    const selectLibDocBtn = document.getElementById('selectLibDocBtn');
    const selectHistoryDocBtn = document.getElementById('selectHistoryDocBtn');
    const materialTags = document.getElementById('materialTags');
    const checkMaterialBtn = document.getElementById('checkMaterialBtn');
    const saveMaterialTemplateBtn = document.getElementById('saveMaterialTemplateBtn');
    // 素材数据结构
    function getMaterialList() {
      return JSON.parse(localStorage.getItem('socMaterialList') || '[]');
    }
    function saveMaterialList(list) {
      localStorage.setItem('socMaterialList', JSON.stringify(list));
    }
    // 渲染素材标签
    function renderMaterialTags() {
      const list = getMaterialList();
      if (!materialTags) return;
      if (list.length === 0) materialTags.innerHTML = '<span style="color:#aaa;">暂无已选文档</span>';
      else materialTags.innerHTML = list.map((d, i) =>
        `<span class="material-tag" style="background:${getDocTypeColor(d.type)};border:1px solid ${getDocTypeBorderColor(d.type)};">
          <span class="doc-name">${d.name}</span>
          <span class="doc-type">${d.type}</span>
          <span class="doc-action" data-act="preview" data-idx="${i}" title="预览文档">👁️</span>
          <span class="doc-action" data-act="del" data-idx="${i}" title="删除文档">✖️</span>
        </span>`
      ).join('');
    }

    // 渲染弹窗中的素材标签
    function renderMaterialTagsDialog() {
      const list = getMaterialList();
      const materialTagsDialog = document.getElementById('materialTagsDialog');
      const materialCountDialog = document.getElementById('materialCountDialog');
      const materialStatusDialog = document.getElementById('materialStatusDialog');

      if (!materialTagsDialog) return;

      // 更新文档数量
      if (materialCountDialog) {
        materialCountDialog.textContent = `${list.length}个文档`;
      }

      // 更新准备状态
      if (materialStatusDialog) {
        const param = getParamForm();
        const isComplete = param.products.length > 0 && param.country && param.operator && param.project && list.length > 0;
        materialStatusDialog.textContent = `准备状态：${isComplete ? '已完成' : '未完成'}`;
        materialStatusDialog.className = `material-status ${isComplete ? 'complete' : 'incomplete'}`;
      }

      if (list.length === 0) {
        materialTagsDialog.innerHTML = '<span style="color:#aaa;font-size:13px;align-self:center;">暂无已选文档</span>';
      } else {
        materialTagsDialog.innerHTML = list.map((d, i) =>
          `<span class="material-tag" style="background:${getDocTypeColor(d.type)};border:1px solid ${getDocTypeBorderColor(d.type)};">
            <span class="doc-name">${d.name}</span>
            <span class="doc-type">${d.type}</span>
            <span class="doc-action" data-act="preview" data-idx="${i}" title="预览文档">👁️</span>
            <span class="doc-action" data-act="del" data-idx="${i}" title="删除文档">✖️</span>
          </span>`
        ).join('');
      }
    }

    // 获取文档类型对应的颜色
    function getDocTypeColor(type) {
      const colorMap = {
        '项目文档': '#e6f7ff',
        '历史SOC文档': '#f6ffed',
        '文档库': '#fff7e6',
        '文档库文档': '#fff7e6'
      };
      return colorMap[type] || '#f5f5f5';
    }

    // 获取文档类型对应的边框颜色
    function getDocTypeBorderColor(type) {
      const colorMap = {
        '项目文档': '#91d5ff',
        '历史SOC文档': '#b7eb8f',
        '文档库': '#ffd591',
        '文档库文档': '#ffd591'
      };
      return colorMap[type] || '#d9d9d9';
    }
    // 初始化一些演示数据
    if (getMaterialList().length === 0) {
      const demoMaterials = [
        { name: '泰国AIS招标文件.pdf', type: '项目文档', time: Date.now() - 86400000, id: 'p1' },
        { name: '5GC技术白皮书.pdf', type: '文档库', time: Date.now() - 172800000, id: 'lib1' },
        { name: '2023年SOC应答经验.xlsx', type: '历史SOC文档', time: Date.now() - 259200000, id: 's1' }
      ];
      saveMaterialList(demoMaterials);
    }

    // 初始化历史文档演示数据
    if (JSON.parse(localStorage.getItem('socHistoryDocs') || '[]').length === 0) {
      const demoHistoryDocs = [
        { name: '越南Viettel技术协议.pdf', type: '项目文档', time: Date.now() - 345600000, id: 'p2' },
        { name: 'VoLTE解决方案文档.pdf', type: '文档库', time: Date.now() - 432000000, id: 'lib2' },
        { name: '2022年SOC应答模板.xlsx', type: '历史SOC文档', time: Date.now() - 518400000, id: 's2' },
        { name: '网络安全要求规范.pdf', type: '项目文档', time: Date.now() - 604800000, id: 'p3' },
        { name: 'IMS部署最佳实践.pdf', type: '文档库', time: Date.now() - 691200000, id: 'lib3' }
      ];
      localStorage.setItem('socHistoryDocs', JSON.stringify(demoHistoryDocs));
    }

    renderMaterialTags();
    // 上传项目文档
    if (uploadProjectDocBtn) uploadProjectDocBtn.onclick = () => uploadProjectDocInput && uploadProjectDocInput.click();
    if (uploadProjectDocInput) uploadProjectDocInput.onchange = function () {
      if (!this.files[0]) return;
      const file = this.files[0];
      const list = getMaterialList();
      list.push({ name: file.name, type: '项目文档', time: Date.now(), id: 'p' + Date.now() });
      saveMaterialList(list);
      renderMaterialTags();
      alert('上传成功（模拟）');
      this.value = '';
    };
    // 上传历史SOC文档
    if (uploadSocDocBtn) uploadSocDocBtn.onclick = () => uploadSocDocInput && uploadSocDocInput.click();
    if (uploadSocDocInput) uploadSocDocInput.onchange = function () {
      if (!this.files[0]) return;
      const file = this.files[0];
      const list = getMaterialList();
      list.push({ name: file.name, type: '历史SOC文档', time: Date.now(), id: 's' + Date.now() });
      saveMaterialList(list);
      renderMaterialTags();
      alert('上传成功（模拟）');
      this.value = '';
    };
    // 从文档库多选
    if (selectLibDocBtn) selectLibDocBtn.onclick = function () {
      const libDocs = [
        { name: '5GC技术白皮书', type: '文档库', id: 'lib1' },
        { name: 'VoLTE解决方案', type: '文档库', id: 'lib2' },
        { name: 'IMS部署指南', type: '文档库', id: 'lib3' }
      ];
      const sel = prompt('请输入要选择的文档编号（用,分隔）：\n1. 5GC技术白皮书\n2. VoLTE解决方案\n3. IMS部署指南');
      if (!sel) return;
      const idxs = sel.split(',').map(s => parseInt(s.trim()) - 1).filter(i => i >= 0 && i < libDocs.length);
      if (idxs.length === 0) return alert('未选择');
      const list = getMaterialList();
      idxs.forEach(i => {
        if (!list.some(d => d.id === libDocs[i].id)) list.push({ ...libDocs[i], time: Date.now() });
      });
      saveMaterialList(list);
      renderMaterialTags();
      alert('已添加文档库文档');
    };
    // 历史文档区弹窗
    if (selectHistoryDocBtn) selectHistoryDocBtn.onclick = function () {
      const all = JSON.parse(localStorage.getItem('socHistoryDocs') || '[]');
      if (all.length === 0) return alert('暂无历史上传文档');
      let html = '<div style="max-height:300px;overflow:auto;">';
      html += all.map((d, i) =>
        `<div style='margin-bottom:6px;'><input type='checkbox' id='histdoc${i}' data-idx='${i}' />
        <span>${d.name} <span style='color:#aaa;font-size:12px;'>(${d.type})</span></span>
        <button data-act='preview' data-idx='${i}'>预览</button>
        <button data-act='del' data-idx='${i}'>删除</button></div>`
      ).join('');
      html += '</div><div style="margin-top:8px;text-align:right;"><button id="histDocBatchAdd">批量关联</button> <button id="histDocClose">关闭</button></div>';
      const dlg = document.createElement('div');
      dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.15);display:flex;align-items:center;justify-content:center;'><div style='background:#fff;padding:24px 32px;border-radius:8px;min-width:320px;box-shadow:0 2px 8px #aaa;'>${html}</div></div>`;
      document.body.appendChild(dlg);
      dlg.onclick = function (e) {
        if (e.target.id === 'histDocClose') document.body.removeChild(dlg);
        if (e.target.id === 'histDocBatchAdd') {
          const sel = Array.from(dlg.querySelectorAll('input[type=checkbox]:checked')).map(c => parseInt(c.dataset.idx));
          if (sel.length === 0) return alert('未选择');
          const list = getMaterialList();
          sel.forEach(i => { if (!list.some(d => d.id === all[i].id)) list.push(all[i]); });
          saveMaterialList(list);
          renderMaterialTags();
          alert('已批量关联');
          document.body.removeChild(dlg);
        }
        if (e.target.dataset.act === 'preview') alert('预览文档：' + all[e.target.dataset.idx].name + '（模拟）');
        if (e.target.dataset.act === 'del') {
          if (confirm('确定删除？')) {
            all.splice(e.target.dataset.idx, 1);
            localStorage.setItem('socHistoryDocs', JSON.stringify(all));
            document.body.removeChild(dlg);
            if (selectHistoryDocBtn) selectHistoryDocBtn.onclick();
          }
        }
      };
    };
    // 素材标签区事件
    if (materialTags) materialTags.onclick = function (e) {
      if (e.target.dataset.act === 'preview') alert('预览文档：' + getMaterialList()[e.target.dataset.idx].name + '（模拟）');
      if (e.target.dataset.act === 'del') {
        const list = getMaterialList();
        list.splice(e.target.dataset.idx, 1);
        saveMaterialList(list);
        renderMaterialTags();
      }
    };
    // 上传/选择的文档自动加入历史文档区
    function syncMaterialToHistory() {
      const all = JSON.parse(localStorage.getItem('socHistoryDocs') || '[]');
      const list = getMaterialList();
      let changed = false;
      list.forEach(d => {
        if (!all.some(h => h.id === d.id)) { all.push({ ...d }); changed = true; }
      });
      if (changed) localStorage.setItem('socHistoryDocs', JSON.stringify(all));
    }
    // 每次素材变更后同步
    const origSaveMaterialList = saveMaterialList;
    saveMaterialList = function (list) {
      origSaveMaterialList(list);
      syncMaterialToHistory();
    };
    // 校验准备项
    if (checkMaterialBtn) checkMaterialBtn.onclick = function () {
      const param = getParamForm();
      const mat = getMaterialList();
      let msg = '';
      if (!param.products.length) msg += '产品未选择\n';
      if (!param.country) msg += '国家未选择\n';
      if (!param.operator) msg += '运营商未选择\n';
      if (!param.project) msg += '项目名称未填写\n';
      if (mat.length === 0) msg += '未上传/选择任何文档\n';
      if (msg) alert('请完善以下项：\n' + msg);
      else alert('校验通过，准备项完整！');
    };
    // 保存当前准备为模板（含素材）
    if (saveMaterialTemplateBtn) saveMaterialTemplateBtn.onclick = function () {
      const name = prompt('请输入准备模板名称');
      if (!name) return;
      let list = getAllTemplates();
      if (list.some(t => t.name === name)) { alert('模板名已存在'); return; }
      const tmpl = getParamForm();
      tmpl.id = 't' + Date.now();
      tmpl.name = name;
      tmpl.isDefault = false;
      tmpl.materials = getMaterialList();
      list.push(tmpl);
      saveAllTemplates(list);
      renderTemplateSelect();
      alert('准备模板已保存（含素材）');
      renderTaskTab('template');
    };
    // 模板切换时自动填充素材
    const origSetParamForm = setParamForm;
    setParamForm = function (tmpl) {
      origSetParamForm(tmpl);
      if (tmpl && tmpl.materials) {
        saveMaterialList(tmpl.materials);
        renderMaterialTags();
      }
    };

    // ===== 应答结果表格逻辑 =====
    const answerTableBody = document.getElementById('answerTableBody');
    const importExcelBtn = document.getElementById('importExcelBtn');
    const importExcelInput = document.getElementById('importExcelInput');
    const exportBtn = document.getElementById('exportBtn');
    const batchReAnswerBtn = document.getElementById('batchReAnswerBtn');
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    const filterAnswer = document.getElementById('filterAnswer');
    const selectAllRow = document.getElementById('selectAllRow');
    const answerDetailDialog = document.getElementById('answerDetailDialog');
    const similarTip = document.getElementById('similarTip');
    const similarCount = document.getElementById('similarCount');
    const showSimilarBtn = document.getElementById('showSimilarBtn');
    // 应答条目数据结构
    function getAnswerList() {
      return JSON.parse(localStorage.getItem('socAnswerList') || '[]');
    }
    function saveAnswerList(list) {
      localStorage.setItem('socAnswerList', JSON.stringify(list));
    }
    // 搜索和排序状态
    let tableSort = { col: '', asc: true };
    let tableSearch = { no: '', desc: '', answer: '', explain: '', index: '', remark: '' };
    // 渲染表格
    function renderAnswerTable() {
      let list = getAnswerList();
      // 复合检索条件
      const searchProduct = document.getElementById('searchProduct')?.value || '';
      const searchAnswer = document.getElementById('searchAnswer')?.value || '';
      const searchExplain = document.getElementById('searchExplain')?.value.trim() || '';
      const searchRemark = document.getElementById('searchRemark')?.value.trim() || '';
      const searchAnswerType = document.getElementById('searchAnswerType')?.value || '';
      // 其它搜索条件
      const searchNo = tableSearch.no || '';
      const searchDesc = tableSearch.desc || '';
      // 过滤
      list = list.filter(row => {
        const products = Array.isArray(row.products) ? row.products : [];
        // 兼容老数据
        if (products.length === 0) return false;
        // 产品筛选
        let displayProducts = products;
        if (searchProduct) displayProducts = products.filter(p => p === searchProduct);
        if (displayProducts.length === 0) return false;
        // 只要有一个产品满足应答/说明/备注筛选即可
        let hasMatch = false;
        for (const product of displayProducts) {
          const pdata = (row.productData && row.productData[product]) || {};
          // 应答方式筛选
          if (searchAnswerType && pdata.answerType !== searchAnswerType) continue;
          if (searchAnswer && pdata.answer !== searchAnswer) continue;
          if (searchExplain && (!pdata.explain || pdata.explain.indexOf(searchExplain) === -1)) continue;
          if (searchRemark && (!pdata.remark || pdata.remark.indexOf(searchRemark) === -1)) continue;
          hasMatch = true;
          break;
        }
        if (!hasMatch) return false;
        if (searchNo && String(row.no || '').indexOf(searchNo) === -1) return false;
        if (searchDesc && (!row.desc || row.desc.indexOf(searchDesc) === -1)) return false;
        return true;
      });
      // 排序（如有）
      if (tableSort.col) {
        list = list.slice().sort((a, b) => {
          let va = a[tableSort.col] || '';
          let vb = b[tableSort.col] || '';
          if (typeof va === 'number' && typeof vb === 'number') return tableSort.asc ? va - vb : vb - va;
          return tableSort.asc ? String(va).localeCompare(String(vb)) : String(vb).localeCompare(String(va));
        });
      }
      // ====== 分页处理 ======
      const pageSize = tablePage.pageSize;
      const page = tablePage.page;
      const total = list.length;
      const pageCount = Math.max(1, Math.ceil(total / pageSize));
      if (page > pageCount) tablePage.page = pageCount;
      const start = (tablePage.page - 1) * pageSize;
      const end = start + pageSize;
      const pageList = list.slice(start, end);
      let html = '';
      let rowIndex = 0;
      pageList.forEach((row, i) => {
        const products = Array.isArray(row.products) ? row.products : [];
        let displayProducts = products;
        if (searchProduct) displayProducts = products.filter(p => p === searchProduct);

        // 为每个产品创建一行
        displayProducts.forEach((product, productIndex) => {
          const pdata = (row.productData && row.productData[product]) || { answer: '', explain: '', remark: '', index: '', imgList: [] };
          let indexArr = [];
          if (Array.isArray(pdata.index)) indexArr = pdata.index;
          else if (typeof pdata.index === 'string') indexArr = pdata.index.split(/[;；\n]/).map(s => s.trim()).filter(Boolean);
          let remarkVal = pdata.remark || '';
          let explainVal = pdata.explain || '';
          let answerType = pdata.answerType || 'AI';
          let tagColor = answerType === 'AI' ? '#e6f7ff' : '#fffbe6';
          let tagTextColor = answerType === 'AI' ? '#1765d5' : '#faad14';

          // 编号和描述只在第一行显示，其他行合并单元格
          const isFirstProduct = productIndex === 0;
          const rowspan = isFirstProduct ? displayProducts.length : 0;

          html += `<tr id="outline-item-${i}-${product}" data-row-idx="${i}" data-product="${product}">
            <td><input type="checkbox" class="rowCheck" data-idx="${i}" data-product="${product}" /></td>
            ${isFirstProduct ? `<td rowspan="${rowspan}">${row.no || i + 1}</td>` : ''}
            ${isFirstProduct ? `<td rowspan="${rowspan}">${row.desc || ''}</td>` : ''}
            <td style="font-weight:500;color:#1765d5;">${product}</td>
            <td><span style="background:${tagColor};color:${tagTextColor};font-size:12px;border-radius:4px;padding:2px 8px;">${answerType}</span></td>
            <td>
              <select class="answer-select" data-idx="${i}" data-product="${product}" style="height:26px;font-size:13px;background:${pdata.answer === 'FC' ? '#f6ffed' : pdata.answer === 'PC' ? '#fff7e6' : pdata.answer === 'NC' ? '#fff2f0' : '#f5f5f5'};color:${pdata.answer === 'FC' ? '#52c41a' : pdata.answer === 'PC' ? '#faad14' : pdata.answer === 'NC' ? '#ff4d4f' : '#666'};">
                <option value="">未应答</option>
                <option value="FC" ${pdata.answer === 'FC' ? 'selected' : ''}>FC</option>
                <option value="PC" ${pdata.answer === 'PC' ? 'selected' : ''}>PC</option>
                <option value="NC" ${pdata.answer === 'NC' ? 'selected' : ''}>NC</option>
                <option value="N/A" ${pdata.answer === 'N/A' ? 'selected' : ''}>N/A</option>
              </select>
            </td>
            <td>
              <textarea class="explain-input" data-idx="${i}" data-product="${product}" maxlength="1000" rows="2" style="font-size:13px;resize:vertical;width:100%;" title="${explainVal.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;')}">${explainVal}</textarea>
            </td>
            <td title="${indexArr.join('; ')}">${indexArr.length ? indexArr.join('; ') : '--'}</td>
            <td>
              <textarea class="remark-input" data-idx="${i}" data-product="${product}" maxlength="500" rows="2" style="font-size:13px;resize:vertical;width:100%;" title="${remarkVal.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;')}">${remarkVal}</textarea>
            </td>
            <td style="text-align:right;">
              <span class="soc-table-action" data-act="detail" data-idx="${i}" data-product="${product}">详情</span> |
              <span class="soc-table-action" data-act="reanswer" data-idx="${i}" data-product="${product}">AI应答</span> |
              <span class="soc-table-action" data-act="delete" data-idx="${i}" data-product="${product}" style="color:#ff4d4f;">删除</span>
            </td>
          </tr>`;
          rowIndex++;
        });
      });
      if (answerTableBody) answerTableBody.innerHTML = html;
      // 事件绑定：应答下拉、说明、备注编辑
      document.querySelectorAll('.answer-select').forEach(sel => {
        sel.onchange = function () {
          const idx = parseInt(sel.dataset.idx);
          const product = sel.dataset.product;
          const list = getAnswerList();
          if (list[idx] && list[idx].productData && list[idx].productData[product]) {
            list[idx].productData[product].answer = sel.value;
            // 只要用户手动修改即标记为人工
            list[idx].productData[product].answerType = '人工';
            saveAnswerList(list);
            renderAnswerTable();
          }
        };
      });
      document.querySelectorAll('.remark-input').forEach(inp => {
        inp.onblur = function () {
          const idx = parseInt(inp.dataset.idx);
          const product = inp.dataset.product;
          const list = getAnswerList();
          let val = inp.value.slice(0, 500);
          if (list[idx] && list[idx].productData && list[idx].productData[product]) {
            list[idx].productData[product].remark = val;
            // 只要用户手动修改即标记为人工
            list[idx].productData[product].answerType = '人工';
            saveAnswerList(list);
          }
        };
      });
      document.querySelectorAll('.explain-input').forEach(inp => {
        inp.onblur = function () {
          const idx = parseInt(inp.dataset.idx);
          const product = inp.dataset.product;
          const list = getAnswerList();
          let val = inp.value;
          if (list[idx] && list[idx].productData && list[idx].productData[product]) {
            list[idx].productData[product].explain = val;
            // 只要用户手动修改即标记为人工
            list[idx].productData[product].answerType = '人工';
            saveAnswerList(list);
          }
        };
        // 粘贴图片URL自动识别
        inp.onpaste = function (e) {
          const idx = parseInt(inp.dataset.idx);
          const product = inp.dataset.product;
          const list = getAnswerList();
          let items = (e.clipboardData || window.clipboardData).items;
          for (let i = 0; i < items.length; i++) {
            let item = items[i];
            if (item.kind === 'file' && item.type.indexOf('image') !== -1) {
              let file = item.getAsFile();
              let reader = new FileReader();
              reader.onload = function (evt) {
                if (list[idx] && list[idx].productData && list[idx].productData[product]) {
                  if (!Array.isArray(list[idx].productData[product].imgList)) list[idx].productData[product].imgList = [];
                  list[idx].productData[product].imgList.push(evt.target.result);
                  saveAnswerList(list);
                  renderAnswerTable();
                }
              };
              reader.readAsDataURL(file);
            } else if (item.kind === 'string' && item.type === 'text/plain') {
              item.getAsString(function (str) {
                if (/^https?:\/\/.+\.(jpg|jpeg|png|webp|gif)$/i.test(str)) {
                  if (list[idx] && list[idx].productData && list[idx].productData[product]) {
                    if (!Array.isArray(list[idx].productData[product].imgList)) list[idx].productData[product].imgList = [];
                    list[idx].productData[product].imgList.push(str);
                    saveAnswerList(list);
                    renderAnswerTable();
                  }
                }
              });
            }
          }
        };
      });
      // ====== 相似条目一致性高亮与提示 ======
      // 检测相似分组
      const groups = findSimilarGroups(list);
      // 记录每个条目属于哪个分组
      const idxToGroup = {};
      groups.forEach((g, gi) => g.forEach(idx => { idxToGroup[idx] = gi; }));
      // 高亮相似条目 - 修改为基于data-row-idx属性
      Array.from(answerTableBody.querySelectorAll('tr')).forEach((tr) => {
        const rowIdx = tr.getAttribute('data-row-idx');
        if (rowIdx !== null && idxToGroup[parseInt(rowIdx)] !== undefined) {
          tr.style.background = '#fffbe6';
          tr.setAttribute('data-similar-group', idxToGroup[parseInt(rowIdx)]);
          tr.title = `与分组${idxToGroup[parseInt(rowIdx)] + 1}的其他条目相似`;
        } else {
          tr.style.background = '';
          tr.removeAttribute('data-similar-group');
          tr.title = '';
        }
      });
      // 显示顶部提示
      if (similarTip && similarCount) {
        if (groups.length > 0) {
          similarTip.style.display = '';
          similarCount.textContent = groups.length;
        } else {
          similarTip.style.display = 'none';
        }
      }
      // 鼠标悬停高亮同组 - 修改为基于data-row-idx属性
      Array.from(answerTableBody.querySelectorAll('tr')).forEach((tr) => {
        tr.onmouseenter = function () {
          const g = tr.getAttribute('data-similar-group');
          if (g !== null) {
            Array.from(answerTableBody.querySelectorAll('tr')).forEach((t) => {
              const tRowIdx = t.getAttribute('data-row-idx');
              if (tRowIdx !== null && idxToGroup[parseInt(tRowIdx)] == g) {
                t.style.outline = '2px solid #faad14';
              }
            });
          }
        };
        tr.onmouseleave = function () {
          Array.from(answerTableBody.querySelectorAll('tr')).forEach(t => t.style.outline = '');
        };
      });
      // 更新大纲树
      updateOutline();
      // ====== 分页条渲染 ======
      const pageInfo = document.getElementById('pageInfo');
      if (pageInfo) pageInfo.textContent = `${tablePage.page} / ${pageCount}`;
      document.getElementById('prevPageBtn').disabled = tablePage.page <= 1;
      document.getElementById('nextPageBtn').disabled = tablePage.page >= pageCount;
    }
    // 检索事件绑定
    document.getElementById('searchProduct').onchange = renderAnswerTable;
    document.getElementById('searchAnswer').onchange = renderAnswerTable;
    document.getElementById('searchExplain').oninput = renderAnswerTable;
    document.getElementById('searchRemark').oninput = renderAnswerTable;
    document.getElementById('searchAnswerType').onchange = renderAnswerTable;
    // 排序按钮事件
    document.querySelectorAll('.sort-btn').forEach(btn => {
      btn.onclick = function () {
        const col = btn.dataset.col;
        if (tableSort.col === col) tableSort.asc = !tableSort.asc;
        else { tableSort.col = col; tableSort.asc = true; }
        renderAnswerTable();
      };
    });
    // 搜索输入事件
    document.querySelectorAll('.search-input').forEach(inp => {
      inp.oninput = function () {
        tableSearch[inp.dataset.col] = inp.value.trim();
        renderAnswerTable();
      };
    });
    // 备注编辑事件（允许手工修改并保存）
    if (answerTableBody) answerTableBody.addEventListener('input', function (e) {
      if (e.target.classList.contains('remark-input')) {
        const idx = parseInt(e.target.dataset.idx);
        const product = e.target.dataset.product;
        let list = getAnswerList();
        if (product && product !== 'all' && list[idx] && list[idx].productData && list[idx].productData[product]) {
          // 多产品结构：更新指定产品的备注
          list[idx].productData[product].remark = e.target.value;
          saveAnswerList(list);
        } else if (list[idx]) {
          // 单产品结构：更新条目备注
          list[idx].remark = e.target.value;
          saveAnswerList(list);
        }
      }
    });
    // 批量导入Excel（模拟）
    if (importExcelBtn) importExcelBtn.onclick = function () {
      // 模拟Excel数据，不依赖文件上传
      const simulatedFileName = `模拟Excel文件_${new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }).replace(/[\/\s:]/g, '')}.xlsx`;

      // 模拟Excel问题数据
      const excelQuestions = [
        { no: 1, desc: '系统支持多语言界面' },
        { no: '1.1', desc: '是否支持自定义词库' },
        { no: '1.2', desc: '系统支持多语言功能' },
        { no: 2, desc: '是否支持批量导入' },
        { no: '2.1', desc: '系统是否支持自定义词库' },
        { no: '2.2', desc: '是否支持云部署' },
        { no: '2.2.1', desc: '支持哪些云平台' },
        { no: '2.2.2', desc: '是否支持容器化部署' },
        { no: 3, desc: '系统安全性如何' },
        { no: 4, desc: '是否支持高可用部署' },
        { no: 5, desc: '数据备份与恢复功能' },
        { no: 6, desc: '系统监控告警能力' },
        { no: 7, desc: '用户权限管理机制' },
        { no: 8, desc: 'API接口开放能力' },
        { no: 9, desc: '第三方系统集成' },
        { no: 10, desc: '系统升级维护方式' }
      ];

      // 检查是否已配置产品参数
      const configuredProducts = getConfiguredProducts();

      if (configuredProducts.length === 0) {
        // 未配置产品，显示参数设置弹窗，设置完成后自动继续导入流程
        showParamSettingDialogWithCallback(function () {
          // 参数设置完成后的回调，重新获取配置的产品并继续导入流程
          const newConfiguredProducts = getConfiguredProducts();
          if (newConfiguredProducts.length > 0) {
            showBatchImportProductDialog(newConfiguredProducts, excelQuestions, simulatedFileName);
          }
        });
      } else {
        // 已配置产品，直接显示产品选择弹窗
        showBatchImportProductDialog(configuredProducts, excelQuestions, simulatedFileName);
      }
    };

    // 移除文件输入监听
    if (importExcelInput) importExcelInput.onchange = null;

    // 显示批量导入产品选择弹窗（类似AI应答按钮的逻辑）
    function showBatchImportProductDialog(configuredProducts, questions, fileName) {
      let html = `<div style='width:480px;max-width:96vw;background:#fff;border-radius:12px;box-shadow:0 4px 24px #aaa;padding:24px;'>
        <div style='font-size:18px;font-weight:600;margin-bottom:16px;text-align:center;'>
          <span style='color:#1765d5;'>📄</span> 批量应答处理
        </div>
        <div style='background:#f5f5f5;border-radius:8px;padding:16px;margin-bottom:16px;'>
          <div style='font-size:14px;color:#666;margin-bottom:8px;'>文件信息</div>
          <div style='font-size:15px;font-weight:500;'>${fileName}</div>
          <div style='font-size:13px;color:#666;margin-top:4px;'>已解析 ${questions.length} 个条目</div>
        </div>
        <div style='margin-bottom:16px;'>
          <div style='font-size:15px;font-weight:500;margin-bottom:8px;'>请选择待应答的产品</div>
          <div style='font-size:13px;color:#666;margin-bottom:12px;'>从已配置的产品中选择（可多选）</div>
          <div style='display:flex;flex-wrap:wrap;gap:8px;'>`;

      configuredProducts.forEach(product => {
        html += `<label style='display:flex;align-items:center;padding:8px 12px;border:1px solid #d9d9d9;border-radius:6px;cursor:pointer;transition:all 0.2s;' 
                   onmouseover='this.style.borderColor="#1765d5";this.style.backgroundColor="#f6f9ff"' 
                   onmouseout='this.style.borderColor="#d9d9d9";this.style.backgroundColor="transparent"'>
                   <input type='checkbox' class='batch-product-checkbox' value='${product}' style='margin-right:6px;' checked/>
                   <span style='font-size:14px;'>${product}</span>
                 </label>`;
      });

      html += `</div>
        </div>
        <div style='background:#e6f7ff;border-radius:6px;padding:12px;margin-bottom:20px;'>
          <div style='font-size:13px;color:#1765d5;'>
            <span style='margin-right:4px;'>💡</span>
            系统将为每个选中的产品生成独立的应答结果
          </div>
          <div style='font-size:12px;color:#666;margin-top:6px;'>
              Ctrl+Enter 确定  |  ESC 取消 
          </div>
        </div>
        <div style='text-align:center;display:flex;justify-content:center;gap:16px;'>
          <button id='confirmBatchProcessBtn' style='background:#1765d5;color:#fff;border:none;border-radius:8px;padding:10px 24px;font-size:15px;cursor:pointer;box-shadow:0 2px 8px #e6f7ff;transition:all 0.2s;' onmouseover='this.style.backgroundColor="#0d47a1";this.style.transform="translateY(-1px)"' onmouseout='this.style.backgroundColor="#1765d5";this.style.transform="translateY(0)"'>确定生成应答</button>
          <button id='cancelBatchProcessBtn' style='background:#f5f6fa;color:#333;border:none;border-radius:8px;padding:10px 24px;font-size:15px;cursor:pointer;transition:all 0.2s;' onmouseover='this.style.backgroundColor="#e8e8e8"' onmouseout='this.style.backgroundColor="#f5f6fa"'>取消</button>
        </div>
      </div>`;

      const dlg = document.createElement('div');
      dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.3);display:flex;align-items:center;justify-content:center;'>${html}</div>`;
      document.body.appendChild(dlg);

      // 键盘事件支持
      dlg.addEventListener('keydown', function (e) {
        if (e.key === 'Escape') {
          document.body.removeChild(dlg);
        } else if (e.key === 'Enter' && e.ctrlKey) {
          dlg.querySelector('#confirmBatchProcessBtn').click();
        }
      });

      // 取消按钮
      dlg.querySelector('#cancelBatchProcessBtn').onclick = function () {
        document.body.removeChild(dlg);
      };

      // 确定按钮
      dlg.querySelector('#confirmBatchProcessBtn').onclick = function () {
        console.log('确定按钮被点击');
        const selectedProducts = Array.from(dlg.querySelectorAll('.batch-product-checkbox:checked')).map(cb => cb.value);
        console.log('选中的产品:', selectedProducts);

        if (selectedProducts.length === 0) {
          alert('请至少选择一个产品');
          return;
        }

        // 生成批量应答数据
        console.log('开始生成批量应答数据');
        generateBatchAnswers(questions, selectedProducts, fileName);
        document.body.removeChild(dlg);
      };
    }

    // 显示带回调的参数设置对话框（用于批量导入时参数未配置的情况）
    function showParamSettingDialogWithCallback(callback) {
      let html = `<div style='width:520px;max-width:96vw;'>
    <div style='font-size:18px;font-weight:500;margin-bottom:12px;'>参数设置</div>
    <div style='color:#ff7a00;background:#fff7e6;border:1px solid #ffd591;border-radius:6px;padding:12px;margin-bottom:12px;'>
      <span style='margin-right:4px;'>⚠️</span>
      批量导入需要先配置产品参数，设置完成后将自动继续导入流程。
    </div>
    <div id='tabParamContent' style='padding:18px 8px 8px 8px;'>
      <form id="paramFormDialog" style="display:flex;flex-wrap:wrap;gap:16px 32px;align-items:flex-end;">
        <div>
          <label>产品选择</label><br/>
          <div class="tree-select-container" style="position:relative;min-width:200px;width:220px;">
            <div class="tree-select-input" style="border:1px solid #d9d9d9;border-radius:4px;padding:8px 12px;min-height:36px;background:#fff;cursor:pointer;display:flex;flex-wrap:wrap;gap:4px;align-items:center;" onclick="toggleProductTree()">
              <span class="tree-select-placeholder" style="color:#999;">请选择产品</span>
            </div>
            <div class="tree-select-dropdown" style="position:absolute;top:100%;left:0;right:0;background:#fff;border:1px solid #d9d9d9;border-radius:4px;box-shadow:0 2px 8px rgba(0,0,0,0.15);z-index:1000;max-height:300px;overflow-y:auto;display:none;">
              <div class="tree-select-search" style="padding:8px;border-bottom:1px solid #f0f0f0;">
                <input type="text" placeholder="搜索产品..." style="width:100%;padding:6px 8px;border:1px solid #d9d9d9;border-radius:4px;font-size:12px;" onkeyup="filterProductTree(this.value)">
              </div>
              <div class="tree-select-options">
                <div class="tree-node" data-value="5GC" data-type="工程">
                  <span class="tree-node-toggle" onclick="toggleTreeNode(this)">▶</span>
                  <span class="tree-node-label">5GC</span>
                  <span class="tree-node-type">(工程)</span>
                </div>
                <div class="tree-node" data-value="VoLTE" data-type="售前">
                  <span class="tree-node-toggle" onclick="toggleTreeNode(this)">▶</span>
                  <span class="tree-node-label">VoLTE</span>
                  <span class="tree-node-type">(售前)</span>
                </div>
                <div class="tree-node" data-value="IMS" data-type="售前">
                  <span class="tree-node-toggle" onclick="toggleTreeNode(this)">▶</span>
                  <span class="tree-node-label">IMS</span>
                  <span class="tree-node-type">(售前)</span>
                </div>
                <div class="tree-node" data-value="核心网" data-type="工程">
                  <span class="tree-node-toggle" onclick="toggleTreeNode(this)">▶</span>
                  <span class="tree-node-label">核心网</span>
                  <span class="tree-node-type">(工程)</span>
                </div>
                <div class="tree-node" data-value="接入网" data-type="工程">
                  <span class="tree-node-toggle" onclick="toggleTreeNode(this)">▶</span>
                  <span class="tree-node-label">接入网</span>
                  <span class="tree-node-type">(工程)</span>
                </div>
              </div>
            </div>
            <input type="hidden" id="productSelectDialog" value="">
          </div>
        </div>
        <div>
          <label>国家</label><br/>
          <div class="searchable-select-container" style="position:relative;min-width:120px;width:140px;">
            <input type="text" id="countrySelectDialog" placeholder="请选择或输入国家" style="width:100%;padding:8px 12px;border:1px solid #d9d9d9;border-radius:4px;font-size:14px;" onfocus="showCountryDropdown()" onblur="hideCountryDropdown()" onkeyup="filterCountryOptions(this.value)">
            <div class="searchable-dropdown" style="position:absolute;top:100%;left:0;right:0;background:#fff;border:1px solid #d9d9d9;border-radius:4px;box-shadow:0 2px 8px rgba(0,0,0,0.15);z-index:1000;max-height:200px;overflow-y:auto;display:none;">
              <div class="dropdown-option" data-value="泰国">泰国</div>
              <div class="dropdown-option" data-value="越南">越南</div>
              <div class="dropdown-option" data-value="印尼">印尼</div>
              <div class="dropdown-option" data-value="马来西亚">马来西亚</div>
              <div class="dropdown-option" data-value="新加坡">新加坡</div>
              <div class="dropdown-option" data-value="菲律宾">菲律宾</div>
            </div>
          </div>
        </div>
        <div>
          <label>客户</label><br/>
          <div class="searchable-select-container" style="position:relative;min-width:120px;width:140px;">
            <input type="text" id="operatorSelectDialog" placeholder="请选择或输入客户" style="width:100%;padding:8px 12px;border:1px solid #d9d9d9;border-radius:4px;font-size:14px;" onfocus="showOperatorDropdown()" onblur="hideOperatorDropdown()" onkeyup="filterOperatorOptions(this.value)">
            <div class="searchable-dropdown" style="position:absolute;top:100%;left:0;right:0;background:#fff;border:1px solid #d9d9d9;border-radius:4px;box-shadow:0 2px 8px rgba(0,0,0,0.15);z-index:1000;max-height:200px;overflow-y:auto;display:none;">
              <div class="dropdown-option" data-value="AIS">AIS</div>
              <div class="dropdown-option" data-value="Viettel">Viettel</div>
              <div class="dropdown-option" data-value="Telkomsel">Telkomsel</div>
              <div class="dropdown-option" data-value="Maxis">Maxis</div>
              <div class="dropdown-option" data-value="Singtel">Singtel</div>
              <div class="dropdown-option" data-value="PLDT">PLDT</div>
            </div>
          </div>
        </div>
        <div>
          <label>项目名称</label><br/>
          <input id="projectInputDialog" type="text" style="width:160px;" placeholder="请输入项目名称" />
        </div>
        <div>
          <label>参数模板</label><br/>
          <select id="templateSelectDialog" style="min-width:120px;width:160px;"></select>
        </div>
        <div style="display:flex;gap:8px;">
          <button type="button" id="saveTemplateBtnDialog">保存为模板</button>
          <button type="button" id="restoreDefaultBtnDialog">恢复默认</button>
          <button type="button" id="clearParamBtnDialog">清空参数</button>
        </div>
      </form>
    </div>
    <div style='text-align:right;margin-top:18px;display:flex;justify-content:flex-end;gap:12px;'>
      <button id='confirmParamSettingDlg' style='background:#1765d5;color:#fff;border:none;border-radius:4px;padding:8px 24px;cursor:pointer;font-size:14px;transition:all 0.3s;'>确定并继续导入</button>
      <button id='cancelParamSettingDlg' style='background:#f5f5f5;color:#666;border:1px solid #d9d9d9;border-radius:4px;padding:8px 24px;cursor:pointer;font-size:14px;transition:all 0.3s;'>取消</button>
    </div>
  </div>`;
      const dlg = document.createElement('div');
      dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.18);display:flex;align-items:center;justify-content:center;'><div style='background:#fff;padding:0 0 0 0;border-radius:12px;min-width:340px;box-shadow:0 4px 24px #aaa;'>${html}</div></div>`;
      document.body.appendChild(dlg);

      // 初始化树形选择器和搜索下拉框
      initTreeSelect(dlg);
      initSearchableSelects(dlg);

      // 取消按钮
      const cancelBtn = dlg.querySelector('#cancelParamSettingDlg');
      cancelBtn.onclick = function () {
        document.body.removeChild(dlg);
      };

      // 确定按钮 - 修改为支持回调
      const confirmBtn = dlg.querySelector('#confirmParamSettingDlg');
      confirmBtn.onclick = function () {
        console.log('确定并继续导入按钮被点击');
        
        // 获取表单数据
        const productSelect = dlg.querySelector('#productSelectDialog');
        const countrySelect = dlg.querySelector('#countrySelectDialog');
        const operatorSelect = dlg.querySelector('#operatorSelectDialog');
        const projectInput = dlg.querySelector('#projectInputDialog');

        // 收集选中的产品（从隐藏输入框获取）
        const selectedProducts = productSelect.value ? productSelect.value.split(',') : [];
        console.log('选中的产品:', selectedProducts);

        // 基本验证 - 只有产品是必填参数
        if (selectedProducts.length === 0) {
          alert('请至少选择一个产品（必填）');
          dlg.querySelector('.tree-select-input').focus();
          return;
        }

        // 保存参数到本地存储
        const paramData = {
          products: selectedProducts,
          country: countrySelect.value,
          operator: operatorSelect.value,
          projectName: projectInput.value.trim(),
          timestamp: new Date().toISOString()
        };

        localStorage.setItem('currentParams', JSON.stringify(paramData));
        console.log('参数已保存到localStorage:', paramData);

        // 显示保存成功提示
        const productNames = selectedProducts.join(', ');
        const successMsg = `参数设置已保存\n产品: ${productNames}${paramData.country ? '\n国家: ' + paramData.country : ''}${paramData.operator ? '\n运营商: ' + paramData.operator : ''}${paramData.projectName ? '\n项目: ' + paramData.projectName : ''}`;
        alert(successMsg);

        // 关闭对话框
        document.body.removeChild(dlg);

        // 执行回调函数
        if (typeof callback === 'function') {
          console.log('执行回调函数');
          callback();
        } else {
          console.log('没有回调函数');
        }
      };

      // 按钮悬停效果
      confirmBtn.onmouseenter = function () {
        this.style.background = '#1454c0';
      };
      confirmBtn.onmouseleave = function () {
        this.style.background = '#1765d5';
      };

      cancelBtn.onmouseenter = function () {
        this.style.background = '#e6e6e6';
        this.style.borderColor = '#bfbfbf';
      };
      cancelBtn.onmouseleave = function () {
        this.style.background = '#f5f5f5';
        this.style.borderColor = '#d9d9d9';
      };

      // 键盘事件支持
      document.addEventListener('keydown', function handleKeydown(e) {
        if (e.key === 'Escape') {
          cancelBtn.click();
          document.removeEventListener('keydown', handleKeydown);
        } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
          confirmBtn.click();
          document.removeEventListener('keydown', handleKeydown);
        }
      });

      // 复用原有参数表单事件绑定和数据填充逻辑
      bindDialogParamEvents && bindDialogParamEvents();
      renderTemplateSelect && renderTemplateSelect();
      // 若有默认模板自动填充
      const def = getDefaultTemplate && getDefaultTemplate();
      if (def) setParamForm && setParamForm(def);
    }

    // 生成批量应答数据
    function generateBatchAnswers(questions, selectedProducts, fileName) {
      console.log('generateBatchAnswers 函数被调用');
      console.log('问题数量:', questions.length);
      console.log('选中产品:', selectedProducts);
      console.log('文件名:', fileName);

      const list = getAnswerList();
      const startIndex = list.length;

      // 显示加载提示
      const loadingDiv = document.createElement('div');
      loadingDiv.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:9999;background:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;'>
        <div style='background:#fff;border-radius:12px;padding:32px;text-align:center;min-width:300px;'>
          <div style='font-size:18px;font-weight:600;margin-bottom:16px;'>正在生成应答...</div>
          <div style='font-size:14px;color:#666;margin-bottom:16px;'>为 ${questions.length} 个条目 × ${selectedProducts.length} 个产品生成应答</div>
          <div style='width:100%;height:4px;background:#f0f0f0;border-radius:2px;overflow:hidden;'>
            <div id='batchProgress' style='width:0%;height:100%;background:#1765d5;transition:width 0.3s;'></div>
          </div>
        </div>
      </div>`;
      document.body.appendChild(loadingDiv);

      // 模拟批量生成过程
      let processedCount = 0;
      const totalCount = questions.length;

      questions.forEach((question, index) => {
        setTimeout(() => {
          // 为每个产品生成独立的应答数据
          const productData = {};
          selectedProducts.forEach(product => {
            productData[product] = {
              answer: ['FC', 'PC', 'NC', 'N/A'][Math.floor(Math.random() * 4)],
              explain: `${product}系统${['完全支持', '部分支持', '暂不支持', '不适用'][Math.floor(Math.random() * 4)]}该功能（批量导入）`,
              supplement: 'AI生成的补充说明',
              remark: `来源：${fileName}`,
              index: `${product}文档-${Math.floor(Math.random() * 10) + 1}.${Math.floor(Math.random() * 10) + 1}`,
              source: '文档库',
              imgList: [],
              answerType: 'AI'
            };
          });

          list.push({
            no: question.no,
            desc: question.desc,
            products: [...selectedProducts],
            productData: productData,
            img: ''
          });

          processedCount++;
          const progress = (processedCount / totalCount) * 100;
          const progressBar = document.getElementById('batchProgress');
          if (progressBar) {
            progressBar.style.width = progress + '%';
          }

          // 最后一个条目处理完成
          if (processedCount === totalCount) {
            setTimeout(() => {
              document.body.removeChild(loadingDiv);
              saveAnswerList(list);
              renderAnswerTable();

              // 显示成功提示
              const successMsg = `批量应答生成完成！\n\n` +
                `📄 文件：${fileName}\n` +
                `📝 条目数：${questions.length}\n` +
                `🏷️ 产品数：${selectedProducts.length}\n` +
                `📊 总应答数：${questions.length * selectedProducts.length}\n` +
                `🎯 选中产品：${selectedProducts.join('、')}`;

              alert(successMsg);

              // 更新大纲树
              if (typeof updateOutline === 'function') {
                updateOutline();
              }
            }, 500);
          }
        }, index * 100); // 每个条目间隔100ms处理，模拟真实处理时间
      });
    }
    // 新增应答条目弹窗
    const addSingleEntryBtn = document.getElementById('addSingleEntryBtn');
    if (addSingleEntryBtn) addSingleEntryBtn.onclick = function () {
      let productOptions = ['5GC', 'VoLTE', 'IMS', '核心网', '接入网'];
      let html = `<div style='width:400px;max-width:96vw;background:#fff;border-radius:12px;box-shadow:0 4px 24px #aaa;padding:24px 32px;'>
          <div style='font-size:20px;font-weight:600;margin-bottom:18px;text-align:center;'>新增应答条目</div>
          <div style='margin-bottom:16px;'><label style='font-size:15px;font-weight:500;'>编号</label><br/><input id='singleNo' style='width:100%;height:38px;border-radius:8px;border:1px solid #d9d9d9;padding:0 12px;font-size:15px;outline:none;transition:border .2s;' placeholder='自动编号或自定义' onfocus="this.style.borderColor='#1765d5'" onblur="this.style.borderColor='#d9d9d9'" /></div>
          <div style='margin-bottom:16px;'><label style='font-size:15px;font-weight:500;'>条目描述 <span style="color:#ff4d4f;">*</span></label><br/><textarea id='singleDesc' style='width:100%;height:60px;border-radius:8px;border:1px solid #d9d9d9;padding:8px 12px;font-size:15px;outline:none;resize:vertical;transition:border .2s;' placeholder='请输入条目描述' onfocus="this.style.borderColor='#1765d5'" onblur="this.style.borderColor='#d9d9d9'" required></textarea></div>
          <div style='margin-bottom:20px;'><label style='font-size:15px;font-weight:500;'>备注</label><br/><input id='singleRemark' style='width:100%;height:38px;border-radius:8px;border:1px solid #d9d9d9;padding:0 12px;font-size:15px;outline:none;transition:border .2s;' placeholder='可填写备注' onfocus="this.style.borderColor='#1765d5'" onblur="this.style.borderColor='#d9d9d9'" /></div>
          <div style='text-align:center;margin-top:24px;display:flex;justify-content:center;gap:16px;'>
    <button id='doAddSingleEntry' style='background:#1765d5;color:#fff;border:none;border-radius:8px;padding:10px 40px;font-size:16px;cursor:pointer;box-shadow:0 2px 8px #e6f7ff;transition:background .2s;'>确定</button>
    <button id='cancelAddSingleEntry' style='background:#f5f6fa;color:#333;border-radius:8px;padding:10px 32px;font-size:16px;border:none;cursor:pointer;'>取消</button>
  </div>
        </div>`;
      const dlg = document.createElement('div');
      dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.18);display:flex;align-items:center;justify-content:center;'>${html}</div>`;
      document.body.appendChild(dlg);
      // 初始化树形选择器和搜索下拉框
      initTreeSelect(dlg);
      initSearchableSelects(dlg);
      dlg.querySelector('#cancelAddSingleEntry').onclick = function () { document.body.removeChild(dlg); };
      dlg.querySelector('#doAddSingleEntry').onclick = function () {
        const no = dlg.querySelector('#singleNo').value.trim();
        const desc = dlg.querySelector('#singleDesc').value.trim();
        const remark = dlg.querySelector('#singleRemark').value.trim();
        const searchProduct = document.getElementById('searchProduct')?.value || '';
        let products = [];
        if (searchProduct && searchProduct.value) {
          products = [searchProduct.value];
        }
        // 不再校验必选产品
        if (!desc) { dlg.querySelector('#singleDesc').style.borderColor = '#ff4d4f'; dlg.querySelector('#singleDesc').focus(); return alert('条目描述不能为空'); }
        const list = getAnswerList();
        // 多产品结构，每个产品有独立应答、说明、补充、备注等
        const productData = {};
        products.forEach(p => {
          productData[p] = {
            answer: '',
            explain: '',
            supplement: '',
            remark: remark || '',
            index: '',
            source: '',
            // 可扩展更多字段
          };
        });
        list.push({
          no: no || (list.length + 1),
          products,
          desc,
          productData,
          img: ''
        });
        saveAnswerList(list);
        renderAnswerTable();
        document.body.removeChild(dlg);
      };
    };
    // 筛选
    if (filterAnswer) filterAnswer.onchange = renderAnswerTable;
    // 产品筛选
    const productFilter = document.getElementById('productFilter');
    if (productFilter) productFilter.onchange = renderAnswerTable;
    // 全选
    if (selectAllRow) selectAllRow.onclick = function () {
      Array.from(document.querySelectorAll('.rowCheck')).forEach(c => c.checked = selectAllRow.checked);
    };
    // 单条/批量重新应答
    if (answerTableBody) answerTableBody.addEventListener('click', function (e) {
      if (e.target.classList.contains('soc-table-action')) {
        const act = e.target.dataset.act;
        const idx = parseInt(e.target.dataset.idx);
        const product = e.target.dataset.product;
        if (act === 'detail') {
          showAnswerDetail(idx, product);
        } else if (act === 'reanswer') {
          reAnswerRows([idx], product);
        } else if (act === 'delete') {
          // 删除指定产品的应答
          const list = getAnswerList();
          if (list[idx] && list[idx].productData && list[idx].productData[product]) {
            // 只删除该产品的应答数据
            delete list[idx].productData[product];
            // 同时从products数组中移除该产品
            if (list[idx].products && Array.isArray(list[idx].products)) {
              const productIndex = list[idx].products.indexOf(product);
              if (productIndex > -1) {
                list[idx].products.splice(productIndex, 1);
              }
            }
            // 如果该条目所有产品都被删光，则删除整条
            if (Object.keys(list[idx].productData).length === 0) {
              list.splice(idx, 1);
            }
            saveAnswerList(list);
            renderAnswerTable();
          }
        }
      }
    });
    if (batchReAnswerBtn) batchReAnswerBtn.onclick = function () {
      const idxs = Array.from(document.querySelectorAll('.rowCheck')).map((c, i) => c.checked ? i : null).filter(i => i !== null);
      let configuredProducts = typeof getConfiguredProducts === 'function' ? getConfiguredProducts() : ['5GC', 'VoLTE', 'IMS'];
      if (!Array.isArray(configuredProducts) || configuredProducts.length === 0) configuredProducts = ['5GC', 'VoLTE', 'IMS'];
      const searchProduct = document.getElementById('searchProduct')?.value || '';
      function showProductSelectDialog(onConfirm) {
        let html = `<div style='width:320px;background:#fff;border-radius:10px;box-shadow:0 2px 12px #aaa;padding:24px;'>
      <div style='font-size:16px;font-weight:600;margin-bottom:12px;'>请选择要重新应答的产品</div>
      <div style='margin-bottom:16px;'>`;
        configuredProducts.forEach(p => {
          html += `<label style='margin-right:16px;margin-bottom:8px;display:inline-block;cursor:pointer;user-select:none;'><input type='checkbox' class='reanswer-product-checkbox' value='${p}' ${!searchProduct || searchProduct === p ? 'checked' : ''} style='margin-right:6px;width:16px;height:16px;cursor:pointer;'/> ${p}</label>`;
        });
        html += `</div>
      <div style='text-align:right;'>
        <button id='confirmReAnswerProductBtn' style='background:#1765d5;color:#fff;border:none;border-radius:6px;padding:6px 18px;cursor:pointer;margin-right:12px;'>确定</button>
        <button id='cancelReAnswerProductBtn'>取消</button>
      </div>
    </div>`;
        const dlg = document.createElement('div');
        dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.18);display:flex;align-items:center;justify-content:center;'>${html}</div>`;
        document.body.appendChild(dlg);
        dlg.querySelector('#cancelReAnswerProductBtn').onclick = function () { document.body.removeChild(dlg); };
        dlg.querySelector('#confirmReAnswerProductBtn').onclick = function () {
          const products = Array.from(dlg.querySelectorAll('.reanswer-product-checkbox:checked')).map(cb => cb.value);
          if (products.length === 0) return alert('请至少选择一个产品');
          document.body.removeChild(dlg);
          onConfirm(products);
        };
      }
      function batchReAnswer(idxs, products) {
        let changed = 0;
        idxs.forEach(idx => {
          products.forEach(p => {
            reAnswerRows([idx], p, false); // 不弹窗
            changed++;
          });
        });
        alert('已重新应答' + changed + '条（模拟）');
      }
      if (idxs.length > 0) {
        showProductSelectDialog(function (products) {
          batchReAnswer(idxs, products);
        });
      } else {
        let dlg = document.createElement('div');
        dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.18);display:flex;align-items:center;justify-content:center;'>
      <div style='background:#fff;padding:32px 36px;border-radius:10px;box-shadow:0 2px 12px #aaa;'>
        <div style='font-size:16px;font-weight:600;margin-bottom:18px;'>未选中条目，是否对所有条目重新应答？</div>
        <div style='text-align:right;'>
          <button id='confirmReAnswerAllBtn' style='background:#1765d5;color:#fff;border:none;border-radius:6px;padding:6px 18px;cursor:pointer;margin-right:16px;'>是，选择产品</button>
          <button id='cancelReAnswerAllBtn'>取消</button>
        </div>
      </div>
    </div>`;
        document.body.appendChild(dlg);
        dlg.querySelector('#cancelReAnswerAllBtn').onclick = function () { document.body.removeChild(dlg); };
        dlg.querySelector('#confirmReAnswerAllBtn').onclick = function () {
          document.body.removeChild(dlg);
          showProductSelectDialog(function (products) {
            const allIdxs = Array.from(document.querySelectorAll('.rowCheck')).map((c, i) => i);
            batchReAnswer(allIdxs, products);
          });
        };
      }
    };
    // 批量删除按钮点击事件
    if (batchDeleteBtn) batchDeleteBtn.onclick = function () {
      confirmBatchDelete();
    };
    // 重新应答逻辑
    function reAnswerRows(idxs, product, showAlert = true) {
      const list = getAnswerList();
      idxs.forEach(i => {
        if (product && list[i] && list[i].productData && list[i].productData[product]) {
          list[i].productData[product].answer = ['FC', 'PC', 'NC', 'N/A'][Math.floor(Math.random() * 4)];
          list[i].productData[product].explain = `${product}系统AI重新生成的应答说明（模拟）`;
          list[i].productData[product].supplement = 'AI生成的补充信息';
          list[i].productData[product].index = `${product}文档-${Math.floor(Math.random() * 10) + 1}.${Math.floor(Math.random() * 10) + 1}`;
          list[i].productData[product].answerType = 'AI';
        } else if (list[i] && list[i].products && list[i].products.length > 0) {
          list[i].products.forEach(p => {
            if (list[i].productData && list[i].productData[p]) {
              list[i].productData[p].answer = ['FC', 'PC', 'NC', 'N/A'][Math.floor(Math.random() * 4)];
              list[i].productData[p].explain = `${p}系统AI重新生成的应答说明（模拟）`;
              list[i].productData[p].supplement = 'AI生成的补充信息';
              list[i].productData[p].index = `${p}文档-${Math.floor(Math.random() * 10) + 1}.${Math.floor(Math.random() * 10) + 1}`;
              list[i].productData[p].answerType = 'AI';
            }
          });
        } else {
          list[i].answer = ['FC', 'PC', 'NC', 'N/A'][Math.floor(Math.random() * 4)];
          list[i].explain = 'AI重新生成的应答说明（模拟）';
          list[i].productData[0].answerType = 'AI';
        }
      });
      saveAnswerList(list);
      renderAnswerTable();
      if (showAlert) alert('已重新应答' + idxs.length + '条（模拟）');
    }
    // 详情弹窗
    function showAnswerDetail(idx, product) {
      const list = getAnswerList();
      const row = list[idx];
      // 当前产品Tab
      const productFilter = document.getElementById('productFilter')?.value || '';
      window._currentProductTab = product || (productFilter && row.products && row.products.includes(productFilter) ? productFilter : (row.products && row.products[0])) || '5GC';
      let pdata = {};
      if (row.productData && row.productData[window._currentProductTab]) {
        // 多产品结构：获取指定产品的数据
        pdata = row.productData[window._currentProductTab];
      } else if (row.products && row.products.length === 0) {
        // 兼容老数据：单产品结构
        pdata = {
          answer: row.answer || '',
          explain: row.explain || '',
          supplement: row.supplement || '',
          remark: row.remark || '',
          index: row.index || '',
          source: row.source || '',
          tags: [],
          opMatch: 0,
          matchAlgo: '',
          opMatchStar: 0
        };
      } else {
        // 默认空数据
        pdata = { answer: '', explain: '', supplement: '', remark: '', index: '', source: '', tags: [], opMatch: 0, matchAlgo: '', opMatchStar: 0 };
      }
      // 统计区
      const stat = { FC: 0, PC: 0, NC: 0 };
      if (row.productData && Object.keys(row.productData).length > 0) {
        // 多产品结构：统计所有产品的应答状态
        Object.values(row.productData).forEach(d => {
          if (d.answer === 'FC') stat.FC++;
          if (d.answer === 'PC') stat.PC++;
          if (d.answer === 'NC') stat.NC++;
        });
      } else {
        // 兼容老数据：单产品结构
        if (row.answer === 'FC') stat.FC++;
        if (row.answer === 'PC') stat.PC++;
        if (row.answer === 'NC') stat.NC++;
      }
      // 分源模拟数据
      const sourceCards = [
        {
          source: '项目文档',
          summary: '描述类条目：拆解子问题，推导过程清晰。指标类条目：展示指标推导。资质类条目：列出资质文件。',
          cards: [
            {
              tags: ['描述类', '高优先级'],
              match: 92,
              desc: '【项目文档】条目描述示例1（高亮）',
              status: 'FC',
              explain: '应答说明示例1',
              index: '项目文档-A-1.1',
              sourceMark: '',
              supplement: '补充信息示例',
              remark: '备注示例',
              matchAlgo: 'BM25+SBERT',
              opMatch: 88,
              opMatchStar: 4
            }
          ]
        },
        {
          source: '文档库',
          summary: '指标类条目：XX产品Y指标推导。',
          cards: [
            {
              tags: ['指标类', '文档库'],
              match: 85,
              desc: '【文档库】条目描述示例2',
              status: 'PC',
              explain: '应答说明示例2',
              index: '文档库-B-2.1',
              sourceMark: '',
              supplement: '',
              remark: '',
              matchAlgo: 'LDA',
              opMatch: 70,
              opMatchStar: 3
            }
          ]
        },
        {
          source: '历史应答',
          summary: '推荐结果：FC 2条，PC 1条，支持分类筛选。',
          cards: [
            {
              tags: ['历史', '高匹配'],
              match: 95,
              desc: '【历史应答】条目描述示例3',
              status: 'FC',
              explain: '应答说明示例3',
              index: '历史-3.1',
              sourceMark: '',
              supplement: '',
              remark: '',
              matchAlgo: '结构相似度',
              opMatch: 90,
              opMatchStar: 5
            }
          ]
        },
        {
          source: 'GBBS',
          summary: '按应答分类/策略统计，支持多算法匹配度、相似度过滤、标签筛选。',
          cards: [
            {
              tags: ['策略', '国家:泰国', '通用性'],
              match: 89,
              desc: '【GBBS】条目描述示例4',
              status: 'PC',
              explain: '应答说明示例4',
              index: 'GBBS-4.1',
              sourceMark: 'GBBS-ID-4',
              supplement: '',
              remark: '',
              matchAlgo: 'BM25+SBERT+LDA',
              opMatch: 80,
              opMatchStar: 4
            }
          ]
        }
      ];
      // 推荐结果区推导过程
      const deduction = '推导过程：系统根据多源检索结果，优先级排序，结合AI语义分析与人工补充，最终生成推荐应答。';
      // 详情区超2000字符折叠
      function foldContent(content) {
        if (!content || content.length <= 2000) return content;
        return `<div class='fold-content'><div class='fold-preview'>${content.slice(0, 2000)}...<a href='#' class='expand-content'>展开</a></div><div class='fold-full' style='display:none;'>${content}<a href='#' class='collapse-content'>收起</a></div></div>`;
      }
      let html = `<div style='background:#fff;padding:0;border-radius:12px;min-width:1000px;max-width:98vw;min-height:700px;max-height:90vh;box-shadow:0 4px 24px #aaa;position:relative;display:flex;flex-direction:column;'>
        <!-- 头部区域 -->
        <div style='padding:18px 32px 0 32px;background:#fafafa;border-radius:12px 12px 0 0;'>
          <div style='display:flex;justify-content:space-between;align-items:center;margin-bottom:12px;'>
            <div style='font-size:22px;font-weight:600;'>应答详情</div>
            <div style='display:flex;gap:8px;align-items:center;'>
              <span style='font-size:14px;color:#666;'>当前产品：</span>
              <span style='font-weight:500;color:#1765d5;' id="currentProductName">${window._currentProductTab}</span>
            </div>
          </div>
          <!-- 产品Tab切换 -->
          <div style='display:flex;gap:16px;margin-bottom:12px;'>
            ${(() => {
          const productFilter = document.getElementById('productFilter')?.value || '';
          const displayProducts = productFilter ?
            (row.products && row.products.includes(productFilter) ? [productFilter] : []) :
            (row.products && row.products.length > 0 ? row.products : ['5GC']);
          return displayProducts.map(p => `<button class='product-tab-btn' data-p='${p}' style='padding:6px 18px;border-radius:6px;border:none;background:${p === window._currentProductTab ? '#1765d5' : '#f0f5ff'};color:${p === window._currentProductTab ? '#fff' : '#1765d5'};font-weight:500;cursor:pointer;transition:all 0.2s;'>${p}</button>`).join('');
        })()}
          </div>
          <!-- 三层导航 -->
          <div style='display:flex;gap:24px;border-bottom:1px solid #e8e8e8;'>
            <button class='layer-nav-btn active' data-layer='recommend' style='padding:8px 16px;border:none;background:none;color:#1765d5;font-weight:500;border-bottom:2px solid #1765d5;cursor:pointer;'>推荐结果</button>
            <button class='layer-nav-btn' data-layer='summary' style='padding:8px 16px;border:none;background:none;color:#666;font-weight:500;border-bottom:2px solid transparent;cursor:pointer;'>总结</button>
            <button class='layer-nav-btn' data-layer='source' style='padding:8px 16px;border:none;background:none;color:#666;font-weight:500;border-bottom:2px solid transparent;cursor:pointer;'>分源详情</button>
        </div>
        </div>
        
        <!-- 内容区域 -->
        <div style='flex:1;overflow:auto;padding:0 32px 24px 32px;'>
          <!-- 推荐结果层 -->
          <div id='recommend-layer' class='layer-content active' style='display:block;'>
            <div style='margin-top:20px;'>
              <div style='font-size:18px;font-weight:600;margin-bottom:16px;color:#262626;'>推荐结果</div>
              
              <!-- 条目描述（锁定） -->
              <div style='background:#f8f9fa;border:1px solid #e9ecef;border-radius:8px;padding:16px;margin-bottom:16px;'>
                <div style='font-weight:500;margin-bottom:8px;color:#495057;'>条目描述 <span style='color:#6c757d;font-size:13px;'>(锁定)</span></div>
                <div style='color:#212529;line-height:1.6;'>${foldContent(row.desc)}</div>
              </div>
              
              <!-- 补充信息 -->
              <div style='margin-bottom:16px;'>
                <div style='font-weight:500;margin-bottom:8px;color:#495057;'>补充信息</div>
                <div style='display:flex;gap:8px;align-items:flex-start;'>
                  <input id='supplementInput' style='flex:1;padding:8px 12px;border-radius:6px;border:1px solid #d9d9d9;font-size:14px;' placeholder='可补充说明，辅助AI推理' value='${pdata.supplement || ''}' />
                  <button id='reAnswerBtn' style='background:#52c41a;color:#fff;border:none;border-radius:6px;padding:8px 16px;font-size:13px;cursor:pointer;white-space:nowrap;'>AI应答</button>
            </div>
                <div style='font-size:12px;color:#666;margin-top:4px;'>补充信息将作为AI推理的额外输入，帮助生成更准确的应答</div>
          </div>
              
              <!-- 推荐应答 -->
              <div style='margin-bottom:16px;'>
                <div style='font-weight:500;margin-bottom:8px;color:#495057;'>推荐应答</div>
                <div style='background:#fff;border:1px solid #d9d9d9;border-radius:6px;padding:12px;'>
                  <select id='answerSelect' style='background:${pdata.answer === 'FC' ? '#f6ffed' : pdata.answer === 'PC' ? '#fff7e6' : pdata.answer === 'NC' ? '#fff2f0' : '#f5f5f5'};color:${pdata.answer === 'FC' ? '#52c41a' : pdata.answer === 'PC' ? '#faad14' : pdata.answer === 'NC' ? '#ff4d4f' : '#666'};border:1px solid ${pdata.answer === 'FC' ? '#b7eb8f' : pdata.answer === 'PC' ? '#ffd591' : pdata.answer === 'NC' ? '#ffccc7' : '#d9d9d9'};border-radius:4px;padding:6px 12px;font-size:14px;font-weight:500;outline:none;cursor:pointer;min-width:120px;'>
                    <option value='' ${!pdata.answer ? 'selected' : ''}>未应答</option>
                    <option value='FC' ${pdata.answer === 'FC' ? 'selected' : ''}>FC - 完全满足</option>
                    <option value='PC' ${pdata.answer === 'PC' ? 'selected' : ''}>PC - 部分满足</option>
                    <option value='NC' ${pdata.answer === 'NC' ? 'selected' : ''}>NC - 不满足</option>
                    <option value='N/A' ${pdata.answer === 'N/A' ? 'selected' : ''}>N/A - 不适用</option>
                  </select>
          </div>
          </div>
              
              <!-- 应答说明 -->
              <div style='margin-bottom:16px;'>
                <div style='font-weight:500;margin-bottom:8px;color:#495057;'>应答说明</div>
                <div id='explainView' style='background:#fff;border:1px solid #d9d9d9;border-radius:6px;padding:12px;min-height:40px;'>${foldContent(pdata.explain || '')}</div>
                <div id='explainEditWrap' style='display:none;margin-top:8px;'>
                  <textarea id='explainEdit' style='width:100%;height:80px;padding:8px;border-radius:6px;border:1px solid #d9d9d9;resize:vertical;'>${pdata.explain || ''}</textarea>
                  <div style='margin-top:8px;'>
                    <button id='saveExplainBtn' style='background:#1765d5;color:#fff;border:none;border-radius:4px;padding:6px 16px;margin-right:8px;cursor:pointer;'>保存</button>
                    <button id='cancelExplainBtn' style='background:#f5f5f5;color:#666;border:none;border-radius:4px;padding:6px 16px;cursor:pointer;'>取消</button>
                  </div>
                </div>
                <div style='margin-top:8px;'>
                  <button id='editExplainBtn' style='background:#f0f5ff;color:#1765d5;border:1px solid #d6e4ff;border-radius:4px;padding:6px 12px;margin-right:8px;cursor:pointer;font-size:13px;'>编辑</button>
                  <button id='polishExplainBtn' style='background:#fff7e6;color:#fa8c16;border:1px solid #ffd591;border-radius:4px;padding:6px 12px;margin-right:8px;cursor:pointer;font-size:13px;'>AI润色</button>
                  <button id='translateExplainBtn' style='background:#f6ffed;color:#52c41a;border:1px solid #b7eb8f;border-radius:4px;padding:6px 12px;cursor:pointer;font-size:13px;'>AI翻译</button>
                </div>
              </div>
              
              <!-- 索引 -->
              <div style='margin-bottom:16px;'>
                <div style='font-weight:500;margin-bottom:8px;color:#495057;'>索引</div>
                <div id='indexView' style='color:#666;'>
                  ${pdata.index ? parseIndexToLinks(pdata.index) : '暂无索引'}
                </div>
                <div id='indexEditWrap' style='display:none;margin-top:8px;'>
                  <div style='display:flex;gap:8px;margin-bottom:8px;'>
                    <input id='indexFileName' style='flex:1;padding:6px 8px;border-radius:4px;border:1px solid #d9d9d9;font-size:13px;' placeholder='文件名' value='${parseIndexFileName(pdata.index)}' />
                    <button id='selectDocBtn' style='background:#f0f5ff;color:#1765d5;border:1px solid #d6e4ff;border-radius:4px;padding:6px 12px;cursor:pointer;font-size:12px;'>选择文档</button>
                  </div>
                  <div style='display:flex;gap:8px;margin-bottom:8px;'>
                    <input id='indexChapter' style='flex:1;padding:6px 8px;border-radius:4px;border:1px solid #d9d9d9;font-size:13px;' placeholder='章节号，如3.2.1' value='${parseIndexChapter(pdata.index)}' />
                    <input id='indexPage' style='flex:1;padding:6px 8px;border-radius:4px;border:1px solid #d9d9d9;font-size:13px;' placeholder='页码，如15' value='${parseIndexPage(pdata.index)}' />
                  </div>
                  <div style='margin-top:8px;'>
                    <button id='saveIndexBtn' style='background:#1765d5;color:#fff;border:none;border-radius:4px;padding:6px 16px;margin-right:8px;cursor:pointer;'>保存</button>
                    <button id='cancelIndexBtn' style='background:#f5f5f5;color:#666;border:none;border-radius:4px;padding:6px 16px;cursor:pointer;'>取消</button>
                  </div>
                </div>
                <div style='margin-top:8px;'>
                  <button id='editIndexBtn' style='background:#f0f5ff;color:#1765d5;border:1px solid #d6e4ff;border-radius:4px;padding:6px 12px;margin-right:8px;cursor:pointer;font-size:13px;'>编辑索引</button>
                  <button id='addIndexBtn' style='background:#f6ffed;color:#52c41a;border:1px solid #b7eb8f;border-radius:4px;padding:6px 12px;cursor:pointer;font-size:13px;'>添加索引</button>
                </div>
              </div>
              
              <!-- 来源 -->
              <div style='margin-bottom:16px;'>
                <div style='font-weight:500;margin-bottom:8px;color:#495057;'>来源</div>
                <div style='color:#666;'>${pdata.source || '项目文档/文档库/GBBS/历史SOC文档'}</div>
              </div>
              
              <!-- 备注 -->
              <div style='margin-bottom:16px;'>
                <div style='font-weight:500;margin-bottom:8px;color:#495057;'>备注</div>
                <input id='remarkInput' style='width:100%;padding:8px 12px;border-radius:6px;border:1px solid #d9d9d9;font-size:14px;' value='${pdata.remark || ''}' placeholder='可添加备注信息' />
              </div>
            </div>
          </div>
          
          <!-- 总结层 -->
          <div id='summary-layer' class='layer-content' style='display:none;'>
            <div style='margin-top:20px;'>
              <div style='font-size:18px;font-weight:600;margin-bottom:16px;color:#262626;'>总结分析</div>
              
              <!-- 概要分析 -->
              <div style='background:#f8f9fa;border:1px solid #e9ecef;border-radius:8px;padding:16px;margin-bottom:20px;'>
                <div style='font-weight:500;margin-bottom:12px;color:#495057;'>概要分析</div>
                <div style='line-height:1.6;color:#212529;'>
                  <p>系统根据多源检索结果，优先级排序，结合AI语义分析与人工补充，最终生成推荐应答。</p>
                  <p>本次检索涉及4个数据源：项目文档、文档库、GBBS、历史SOC文档，各源贡献度如下：</p>
                </div>
              </div>
              
              <!-- 数据源统计 -->
              <div style='margin-bottom:20px;'>
                <div style='font-weight:500;margin-bottom:12px;color:#495057;'>数据源检索情况</div>
                <div style='display:grid;grid-template-columns:repeat(auto-fit, minmax(200px, 1fr));gap:16px;'>
                  <div style='background:#fff;border:1px solid #e9ecef;border-radius:8px;padding:16px;text-align:center;'>
                    <div style='font-size:24px;font-weight:600;color:#1765d5;margin-bottom:4px;'>4</div>
                    <div style='color:#6c757d;font-size:14px;'>数据源总数</div>
                  </div>
                  <div style='background:#fff;border:1px solid #e9ecef;border-radius:8px;padding:16px;text-align:center;'>
                    <div style='font-size:24px;font-weight:600;color:#52c41a;margin-bottom:4px;'>${stat.FC}</div>
                    <div style='color:#6c757d;font-size:14px;'>完全满足(FC)</div>
                  </div>
                  <div style='background:#fff;border:1px solid #e9ecef;border-radius:8px;padding:16px;text-align:center;'>
                    <div style='font-size:24px;font-weight:600;color:#faad14;margin-bottom:4px;'>${stat.PC}</div>
                    <div style='color:#6c757d;font-size:14px;'>部分满足(PC)</div>
                  </div>
                  <div style='background:#fff;border:1px solid #e9ecef;border-radius:8px;padding:16px;text-align:center;'>
                    <div style='font-size:24px;font-weight:600;color:#ff4d4f;margin-bottom:4px;'>${stat.NC}</div>
                    <div style='color:#6c757d;font-size:14px;'>不满足(NC)</div>
                  </div>
                </div>
              </div>
              
              <!-- 优先级规则 -->
              <div style='margin-bottom:20px;'>
                <div style='font-weight:500;margin-bottom:12px;color:#495057;'>优先级规则</div>
                <div style='background:#fff;border:1px solid #e9ecef;border-radius:8px;padding:16px;'>
                  <div style='display:flex;align-items:center;margin-bottom:8px;'>
                    <span style='background:#1765d5;color:#fff;border-radius:50%;width:20px;height:20px;display:flex;align-items:center;justify-content:center;font-size:12px;margin-right:8px;'>1</span>
                    <span>项目文档</span>
                  </div>
                  <div style='display:flex;align-items:center;margin-bottom:8px;'>
                    <span style='background:#52c41a;color:#fff;border-radius:50%;width:20px;height:20px;display:flex;align-items:center;justify-content:center;font-size:12px;margin-right:8px;'>2</span>
                    <span>文档库选定文档</span>
                  </div>
                  <div style='display:flex;align-items:center;margin-bottom:8px;'>
                    <span style='background:#faad14;color:#fff;border-radius:50%;width:20px;height:20px;display:flex;align-items:center;justify-content:center;font-size:12px;margin-right:8px;'>3</span>
                    <span>历史SOC文档</span>
                  </div>
                  <div style='display:flex;align-items:center;'>
                    <span style='background:#ff4d4f;color:#fff;border-radius:50%;width:20px;height:20px;display:flex;align-items:center;justify-content:center;font-size:12px;margin-right:8px;'>4</span>
                    <span>GBBS系统数据</span>
                  </div>
                </div>
              </div>
              
              <!-- 推导过程 -->
          <div>
                <div style='font-weight:500;margin-bottom:12px;color:#495057;'>推导过程</div>
                <div style='background:#fff;border:1px solid #e9ecef;border-radius:8px;padding:16px;line-height:1.6;color:#212529;'>
                  ${deduction}
                </div>
              </div>
            </div>
          </div>
          
          <!-- 分源详情层 -->
          <div id='source-layer' class='layer-content' style='display:none;'>
            <div style='margin-top:20px;'>
              <div style='font-size:18px;font-weight:600;margin-bottom:16px;color:#262626;'>分源详情</div>
              
              <!-- 筛选工具栏 -->
              <div style='background:#f8f9fa;border:1px solid #e9ecef;border-radius:8px;padding:12px;margin-bottom:16px;'>
                <div style='display:flex;gap:16px;align-items:center;flex-wrap:wrap;'>
                  <div style='display:flex;align-items:center;gap:8px;'>
                    <span style='font-size:14px;color:#495057;'>应答状态：</span>
                    <select id='statusFilter' style='padding:4px 8px;border-radius:4px;border:1px solid #d9d9d9;'>
                      <option value=''>全部</option>
                      <option value='FC'>FC</option>
                      <option value='PC'>PC</option>
                      <option value='NC'>NC</option>
                      <option value='N/A'>N/A</option>
                    </select>
                  </div>
                  <div style='display:flex;align-items:center;gap:8px;'>
                    <span style='font-size:14px;color:#495057;'>相似度：</span>
                    <select id='similarityFilter' style='padding:4px 8px;border-radius:4px;border:1px solid #d9d9d9;'>
                      <option value=''>全部</option>
                      <option value='90'>≥90%</option>
                      <option value='80'>≥80%</option>
                      <option value='70'>≥70%</option>
                    </select>
                  </div>
                  <div style='display:flex;align-items:center;gap:8px;'>
                    <span style='font-size:14px;color:#495057;'>数据源：</span>
                    <select id='sourceFilter' style='padding:4px 8px;border-radius:4px;border:1px solid #d9d9d9;'>
                      <option value=''>全部</option>
                      <option value='项目文档'>项目文档</option>
                      <option value='文档库'>文档库</option>
                      <option value='历史应答'>历史应答</option>
                      <option value='GBBS'>GBBS</option>
                    </select>
                  </div>
                  <button id='clearFilterBtn' style='background:#f5f5f5;color:#666;border:1px solid #d9d9d9;border-radius:4px;padding:4px 12px;cursor:pointer;font-size:13px;'>清除筛选</button>
                </div>
              </div>
              
              <!-- 分源卡片 -->
            ${sourceCards.map(src => `
                <div class='source-group' data-source='${src.source}' style='margin-bottom:24px;border:1px solid #e9ecef;border-radius:12px;overflow:hidden;'>
                  <div style='background:#f8f9fa;padding:16px;border-bottom:1px solid #e9ecef;'>
                    <div style='display:flex;justify-content:space-between;align-items:center;'>
                      <div style='font-weight:600;font-size:16px;color:#262626;'>${src.source}</div>
                      <div style='color:#6c757d;font-size:14px;'>${src.cards.length}条记录</div>
                    </div>
                    <div style='color:#6c757d;font-size:13px;margin-top:4px;'>${src.summary}</div>
                  </div>
                  <div style='padding:16px;'>
                    <div style='display:grid;grid-template-columns:repeat(auto-fit, minmax(300px, 1fr));gap:16px;'>
                  ${src.cards.map(card => `
                        <div class='source-card' data-status='${card.status}' data-match='${card.match}' data-source='${src.source}' style='background:#fff;border:1px solid #e9ecef;border-radius:8px;padding:16px;position:relative;transition:all 0.2s;hover:box-shadow:0 2px 8px rgba(0,0,0,0.1);'>
                          <!-- 标签区域 -->
                          <div style='margin-bottom:12px;'>
                            ${card.tags.map(t => `<span style='background:#e6f7ff;color:#1765d5;border-radius:4px;padding:2px 8px;font-size:12px;margin-right:6px;margin-bottom:4px;display:inline-block;'>${t}</span>`).join('')}
                          </div>
                          
                          <!-- 匹配度信息 -->
                          <div style='display:flex;justify-content:space-between;align-items:center;margin-bottom:12px;'>
                            <div style='display:flex;align-items:center;gap:8px;'>
                              <span style='font-size:14px;color:#495057;'>匹配度：</span>
                              <span style='font-weight:600;color:#faad14;'>${card.match}%</span>
                              <span style='font-size:12px;color:#6c757d;'>(${card.matchAlgo || ''})</span>
                            </div>
                            ${card.opMatch ? `<div style='display:flex;align-items:center;gap:4px;'>
                              <span style='font-size:14px;color:#495057;'>运营商：</span>
                              <span style='font-weight:600;color:#faad14;'>${card.opMatch}%</span>
                              <span style='color:#faad14;'>${'★'.repeat(card.opMatchStar || 0)}</span>
                            </div>` : ''}
                          </div>
                          
                          <!-- 条目描述 -->
                          <div style='margin-bottom:12px;'>
                            <div style='font-weight:500;margin-bottom:6px;color:#495057;'>条目描述</div>
                            <div style='color:#212529;line-height:1.5;font-size:14px;'>${foldContent(card.desc)}</div>
                          </div>
                          
                          <!-- 应答状态 -->
                          <div style='margin-bottom:12px;'>
                            <div style='font-weight:500;margin-bottom:6px;color:#495057;'>应答状态</div>
                            <span style='background:${card.status === 'FC' ? '#f6ffed' : card.status === 'PC' ? '#fff7e6' : '#fff2f0'};color:${card.status === 'FC' ? '#52c41a' : card.status === 'PC' ? '#faad14' : '#ff4d4f'};border:1px solid ${card.status === 'FC' ? '#b7eb8f' : card.status === 'PC' ? '#ffd591' : '#ffccc7'};border-radius:4px;padding:2px 8px;font-size:12px;font-weight:500;'>${card.status}</span>
                          </div>
                          
                          <!-- 应答说明 -->
                          <div style='margin-bottom:12px;'>
                            <div style='font-weight:500;margin-bottom:6px;color:#495057;'>应答说明</div>
                            <div style='color:#212529;line-height:1.5;font-size:14px;'>${foldContent(card.explain)}</div>
                          </div>
                          
                          <!-- 索引和来源标记 -->
                          <div style='margin-bottom:12px;'>
                            <div style='font-weight:500;margin-bottom:6px;color:#495057;'>索引</div>
                            <div style='color:#1765d5;font-size:14px;cursor:pointer;text-decoration:underline;'>${card.index}</div>
                          </div>
                          
                          ${card.sourceMark ? `<div style='margin-bottom:12px;'>
                            <div style='font-weight:500;margin-bottom:6px;color:#495057;'>来源标记</div>
                            <div style='color:#1765d5;font-size:14px;cursor:pointer;text-decoration:underline;'>${card.sourceMark}</div>
                          </div>` : ''}
                          
                          <!-- 操作按钮 -->
                          <button class='apply-card-btn' style='position:absolute;right:12px;bottom:12px;background:#1765d5;color:#fff;border:none;border-radius:6px;padding:6px 16px;cursor:pointer;font-size:13px;transition:all 0.2s;hover:background:#0958d9;'>应用此结果</button>
                    </div>
                  `).join('')}
                    </div>
                </div>
              </div>
            `).join('')}
          </div>
        </div>
        </div>
        
        <!-- 关闭按钮 -->
        <span style='position:absolute;top:16px;right:24px;cursor:pointer;font-size:24px;color:#666;transition:color 0.2s;hover:color:#333;' onclick='document.getElementById("answerDetailDialog").style.display="none"'>&times;</span>
      </div>`;
      answerDetailDialog.innerHTML = html;
      answerDetailDialog.style.display = '';

      // 三层导航切换
      Array.from(answerDetailDialog.querySelectorAll('.layer-nav-btn')).forEach(btn => {
        btn.onclick = function () {
          // 更新导航按钮状态
          Array.from(answerDetailDialog.querySelectorAll('.layer-nav-btn')).forEach(b => {
            b.style.color = '#666';
            b.style.borderBottomColor = 'transparent';
          });
          btn.style.color = '#1765d5';
          btn.style.borderBottomColor = '#1765d5';

          // 切换内容层
          const layer = btn.dataset.layer;
          Array.from(answerDetailDialog.querySelectorAll('.layer-content')).forEach(content => {
            content.style.display = 'none';
          });
          document.getElementById(layer + '-layer').style.display = 'block';
        };
      });

      // 产品Tab切换
      Array.from(answerDetailDialog.querySelectorAll('.product-tab-btn')).forEach(btn => {
        btn.onclick = function () {
          setCurrentProductTab(btn.dataset.p);
          answerDetailDialog.style.display = 'none';
          setTimeout(() => showAnswerDetail(idx, btn.dataset.p), 100);
        };
      });
      // 推荐应答下拉列表编辑
      const answerSelect = answerDetailDialog.querySelector('#answerSelect');
      if (answerSelect) {
        answerSelect.onchange = function () {
          const newAnswer = answerSelect.value;

          // 更新下拉列表样式
          const colors = {
            'FC': { bg: '#f6ffed', color: '#52c41a', border: '#b7eb8f' },
            'PC': { bg: '#fff7e6', color: '#faad14', border: '#ffd591' },
            'NC': { bg: '#fff2f0', color: '#ff4d4f', border: '#ffccc7' },
            'N/A': { bg: '#f5f5f5', color: '#666', border: '#d9d9d9' },
            '': { bg: '#f5f5f5', color: '#666', border: '#d9d9d9' }
          };

          const color = colors[newAnswer] || colors[''];
          answerSelect.style.background = color.bg;
          answerSelect.style.color = color.color;
          answerSelect.style.borderColor = color.border;

          // 保存到数据
          if (!row.productData) row.productData = {};
          if (!row.productData[window._currentProductTab]) row.productData[window._currentProductTab] = {};
          row.productData[window._currentProductTab].answer = newAnswer;
          saveAnswerList(list);

          // 刷新表格显示
          renderAnswerTable();

          // 显示保存成功提示
          const originalText = answerSelect.options[answerSelect.selectedIndex].text;
          const tempText = answerSelect.options[answerSelect.selectedIndex].text + ' ✓';
          answerSelect.options[answerSelect.selectedIndex].text = tempText;
          setTimeout(() => {
            answerSelect.options[answerSelect.selectedIndex].text = originalText;
          }, 1000);
        };
      }

      // 补充信息、备注编辑
      const supplementInput = answerDetailDialog.querySelector('#supplementInput');
      if (supplementInput) supplementInput.onchange = function () {
        if (!row.productData) row.productData = {};
        if (!row.productData[window._currentProductTab]) row.productData[window._currentProductTab] = {};
        row.productData[window._currentProductTab].supplement = supplementInput.value;
        saveAnswerList(list);
      };
      const remarkInput = answerDetailDialog.querySelector('#remarkInput');
      if (remarkInput) remarkInput.onchange = function () {
        if (!row.productData) row.productData = {};
        if (!row.productData[window._currentProductTab]) row.productData[window._currentProductTab] = {};
        row.productData[window._currentProductTab].remark = remarkInput.value;
        saveAnswerList(list);
      };

      // 重新应答按钮事件
      const reAnswerBtn = answerDetailDialog.querySelector('#reAnswerBtn');
      if (reAnswerBtn) {
        reAnswerBtn.onclick = function () {
          const supplementInput = answerDetailDialog.querySelector('#supplementInput');
          const supplement = supplementInput ? supplementInput.value.trim() : '';

          // 保存补充信息
          if (!row.productData) row.productData = {};
          if (!row.productData[window._currentProductTab]) row.productData[window._currentProductTab] = {};
          row.productData[window._currentProductTab].supplement = supplement;
          saveAnswerList(list);

          // 显示重新应答确认弹窗
          const confirmHtml = `
            <div style='width:400px;max-width:98vw;'>
              <div style='font-size:16px;font-weight:500;margin-bottom:12px;'>重新应答确认</div>
              <div style='margin-bottom:16px;color:#666;line-height:1.5;'>
                <p>即将对以下条目进行重新应答：</p>
                <p><strong>条目编号：</strong>${row.no || (idx + 1)}</p>
                <p><strong>产品：</strong>${window._currentProductTab}</p>
                <p><strong>补充信息：</strong>${supplement || '无'}</p>
              </div>
              <div style='background:#f6ffed;border:1px solid #b7eb8f;border-radius:6px;padding:12px;margin-bottom:16px;'>
                <div style='font-size:14px;color:#52c41a;margin-bottom:4px;'>✓ 将使用最新补充信息</div>
                <div style='font-size:12px;color:#666;'>系统将基于补充信息重新生成应答，覆盖当前结果</div>
              </div>
          <div style='text-align:right;'>
                <button id='cancelReAnswerBtn' style='background:#f5f5f5;color:#666;border:none;border-radius:4px;padding:6px 16px;margin-right:8px;cursor:pointer;'>取消</button>
                <button id='confirmReAnswerBtn' style='background:#52c41a;color:#fff;border:none;border-radius:4px;padding:6px 16px;cursor:pointer;'>确认重新应答</button>
          </div>
            </div>
          `;

          const dlg = document.createElement('div');
          dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.18);display:flex;align-items:center;justify-content:center;'><div style='background:#fff;padding:20px;border-radius:12px;box-shadow:0 4px 24px #aaa;'>${confirmHtml}</div></div>`;
          document.body.appendChild(dlg);

          // 取消重新应答
          dlg.querySelector('#cancelReAnswerBtn').onclick = function () {
            document.body.removeChild(dlg);
          };

          // 确认重新应答
          dlg.querySelector('#confirmReAnswerBtn').onclick = function () {
            // 显示处理中状态
            dlg.querySelector('#confirmReAnswerBtn').textContent = '处理中...';
            dlg.querySelector('#confirmReAnswerBtn').disabled = true;
            dlg.querySelector('#cancelReAnswerBtn').disabled = true;

            // 模拟重新应答处理
            setTimeout(() => {
              // 更新应答结果（模拟AI重新生成）
              const newAnswer = supplement.includes('安全') ? 'FC' : supplement.includes('部分') ? 'PC' : 'FC';
              const newExplain = supplement ?
                `基于补充信息"${supplement}"重新生成的应答：${row.desc}${supplement.includes('安全') ? '，完全满足安全要求。' : supplement.includes('部分') ? '，部分满足相关要求。' : '，满足相关要求。'}` :
                row.desc;

              if (!row.productData) row.productData = {};
              if (!row.productData[window._currentProductTab]) row.productData[window._currentProductTab] = {};
              row.productData[window._currentProductTab].answer = newAnswer;
              row.productData[window._currentProductTab].explain = newExplain;
              row.productData[window._currentProductTab].index = supplement.includes('文档') ? '技术文档-3.2.1' : (pdata.index || '');
              row.productData[window._currentProductTab].answerType = 'AI';
              saveAnswerList(list);

              // 关闭确认弹窗
              document.body.removeChild(dlg);

              // 刷新详情弹窗
              showAnswerDetail(idx, window._currentProductTab);

              // 刷新主表格
              renderAnswerTable();

              // 显示成功提示
              alert(`条目${row.no || (idx + 1)}的${window._currentProductTab}产品重新应答完成！`);
            }, 2000);
          };
        };
      }
      // 编辑/AI润色/翻译
      const editBtn = answerDetailDialog.querySelector('#editExplainBtn');
      const explainView = answerDetailDialog.querySelector('#explainView');
      const explainEditWrap = answerDetailDialog.querySelector('#explainEditWrap');
      if (editBtn) editBtn.onclick = function () {
        explainView.style.display = 'none';
        explainEditWrap.style.display = '';
      };
      const cancelBtn = answerDetailDialog.querySelector('#cancelExplainBtn');
      if (cancelBtn) cancelBtn.onclick = function () {
        explainEditWrap.style.display = 'none';
        explainView.style.display = '';
      };
      const saveBtn = answerDetailDialog.querySelector('#saveExplainBtn');
      if (saveBtn) saveBtn.onclick = function () {
        const val = answerDetailDialog.value.querySelector('#explainEdit').value;
        if (!row.productData) row.productData = {};
        if (!row.productData[window._currentProductTab]) row.productData[window._currentProductTab] = {};
        row.productData[window._currentProductTab].explain = val;
        saveAnswerList(list);
        showAnswerDetail(idx, window._currentProductTab);
      };
      const polishBtn = answerDetailDialog.querySelector('#polishExplainBtn');
      if (polishBtn) polishBtn.onclick = function () {
        const old = pdata.explain || '';
        const polished = old + '（AI润色：表达更正式）';
        if (!row.productData) row.productData = {};
        if (!row.productData[window._currentProductTab]) row.productData[window._currentProductTab] = {};
        row.productData[window._currentProductTab].explain = polished;
        saveAnswerList(list);
        showAnswerDetail(idx, window._currentProductTab);
      };
      const translateBtn = answerDetailDialog.querySelector('#translateExplainBtn');
      if (translateBtn) translateBtn.onclick = function () {
        const old = pdata.explain || '';
        const translated = old + ' (AI翻译: This is the English version.)';
        if (!row.productData) row.productData = {};
        if (!row.productData[window._currentProductTab]) row.productData[window._currentProductTab] = {};
        row.productData[window._currentProductTab].explain = translated;
        saveAnswerList(list);
        showAnswerDetail(idx, window._currentProductTab);
      };
      // 筛选功能
      const statusFilter = answerDetailDialog.querySelector('#statusFilter');
      const similarityFilter = answerDetailDialog.querySelector('#similarityFilter');
      const sourceFilter = answerDetailDialog.querySelector('#sourceFilter');
      const clearFilterBtn = answerDetailDialog.querySelector('#clearFilterBtn');

      function applyFilters() {
        const status = statusFilter ? statusFilter.value : '';
        const similarity = similarityFilter ? similarityFilter.value : '';
        const source = sourceFilter ? sourceFilter.value : '';

        Array.from(answerDetailDialog.querySelectorAll('.source-card')).forEach(card => {
          let show = true;

          // 状态筛选
          if (status && card.dataset.status !== status) {
            show = false;
          }

          // 相似度筛选
          if (similarity && parseInt(card.dataset.match) < parseInt(similarity)) {
            show = false;
          }

          // 数据源筛选
          if (source && card.dataset.source !== source) {
            show = false;
          }

          card.style.display = show ? 'block' : 'none';
        });

        // 隐藏空的源组
        Array.from(answerDetailDialog.querySelectorAll('.source-group')).forEach(group => {
          const visibleCards = group.querySelectorAll('.source-card[style*="display: block"]').length;
          if (visibleCards === 0) {
            group.style.display = 'none';
          } else {
            group.style.display = 'block';
          }
        });
      }

      if (statusFilter) statusFilter.onchange = applyFilters;
      if (similarityFilter) similarityFilter.onchange = applyFilters;
      if (sourceFilter) sourceFilter.onchange = applyFilters;

      if (clearFilterBtn) clearFilterBtn.onclick = function () {
        if (statusFilter) statusFilter.value = '';
        if (similarityFilter) similarityFilter.value = '';
        if (sourceFilter) sourceFilter.value = '';

        Array.from(answerDetailDialog.querySelectorAll('.source-card')).forEach(card => {
          card.style.display = 'block';
        });
        Array.from(answerDetailDialog.querySelectorAll('.source-group')).forEach(group => {
          group.style.display = 'block';
        });
      };

      // 应用此结果按钮
      Array.from(answerDetailDialog.querySelectorAll('.apply-card-btn')).forEach(btn => {
        btn.onclick = function () {
          alert('已应用此结果到当前产品（模拟）');
        };
      });
      // 统计区筛选（模拟）
      Array.from(answerDetailDialog.querySelectorAll('.stat-num')).forEach(btn => {
        btn.onclick = function () {
          alert('已筛选' + btn.dataset.type + '类型卡片（模拟）');
        };
      });
      // 折叠/展开交互
      Array.from(answerDetailDialog.querySelectorAll('.expand-content')).forEach(a => {
        a.onclick = function (e) {
          e.preventDefault();
          const fold = a.closest('.fold-content');
          if (fold) {
            fold.querySelector('.fold-preview').style.display = 'none';
            fold.querySelector('.fold-full').style.display = '';
          }
        };
      });
      Array.from(answerDetailDialog.querySelectorAll('.collapse-content')).forEach(a => {
        a.onclick = function (e) {
          e.preventDefault();
          const fold = a.closest('.fold-content');
          if (fold) {
            fold.querySelector('.fold-preview').style.display = '';
            fold.querySelector('.fold-full').style.display = 'none';
          }
        };
      });

      // 索引编辑事件绑定
      const editIndexBtn = answerDetailDialog.querySelector('#editIndexBtn');
      const addIndexBtn = answerDetailDialog.querySelector('#addIndexBtn');
      const saveIndexBtn = answerDetailDialog.querySelector('#saveIndexBtn');
      const cancelIndexBtn = answerDetailDialog.querySelector('#cancelIndexBtn');
      const selectDocBtn = answerDetailDialog.querySelector('#selectDocBtn');

      if (editIndexBtn) {
        editIndexBtn.onclick = function () {
          document.getElementById('indexView').style.display = 'none';
          document.getElementById('indexEditWrap').style.display = 'block';
          this.style.display = 'none';
          addIndexBtn.style.display = 'none';
        };
      }

      if (addIndexBtn) {
        addIndexBtn.onclick = function () {
          document.getElementById('indexView').style.display = 'none';
          document.getElementById('indexEditWrap').style.display = 'block';
          // 清空输入框
          document.getElementById('indexFileName').value = '';
          document.getElementById('indexChapter').value = '';
          document.getElementById('indexPage').value = '';
          this.style.display = 'none';
          editIndexBtn.style.display = 'none';
        };
      }

      if (saveIndexBtn) {
        saveIndexBtn.onclick = function () {
          const fileName = document.getElementById('indexFileName').value.trim();
          const chapter = document.getElementById('indexChapter').value.trim();
          const page = document.getElementById('indexPage').value.trim();

          if (!fileName) {
            alert('请输入文件名');
            return;
          }

          if (!chapter) {
            alert('请输入章节号');
            return;
          }

          // 构建索引字符串
          let indexStr = fileName + '-' + chapter;
          if (page) indexStr += '-' + page;

          // 更新显示
          document.getElementById('indexView').innerHTML = parseIndexToLinks(indexStr);
          document.getElementById('indexView').style.display = 'block';
          document.getElementById('indexEditWrap').style.display = 'none';
          editIndexBtn.style.display = 'inline-block';
          addIndexBtn.style.display = 'inline-block';

          // 更新数据（这里需要根据实际数据结构更新）
          if (window._currentDetailData) {
            window._currentDetailData.index = indexStr;
          }
        };
      }

      if (cancelIndexBtn) {
        cancelIndexBtn.onclick = function () {
          document.getElementById('indexView').style.display = 'block';
          document.getElementById('indexEditWrap').style.display = 'none';
          editIndexBtn.style.display = 'inline-block';
          addIndexBtn.style.display = 'inline-block';
        };
      }

      if (selectDocBtn) {
        selectDocBtn.onclick = function () {
          showDocumentSelector(function (selectedDoc) {
            document.getElementById('indexFileName').value = selectedDoc;
          });
        };
      }
    }
    // 批量导出（模拟）
    if (exportBtn) exportBtn.onclick = function () {
      // 弹窗选择导出类型和方式
      const types = [
        { key: 'FC', label: '满足条目' },
        { key: 'PC', label: '部分满足条目' },
        { key: 'NC', label: '不满足条目' },
        { key: 'N/A', label: 'N/A条目' },
        { key: '', label: '未应答条目' }
      ];
      let html = '<div style="padding:8px 0;">';
      html += '<div style="font-weight:500;margin-bottom:6px;">导出类型</div>';
      html += types.map((t, i) => `<label style='margin-right:12px;'><input type='checkbox' class='expType' value='${t.key}' ${i < 4 ? 'checked' : ''}/> ${t.label}</label>`).join('');
      html += '</div><div style="margin:10px 0 6px 0;font-weight:500;">导出方式</div>';
      html += `<label><input type='radio' name='expMode' value='single' checked/> 单文件单sheet页</label>
        <label style='margin-left:16px;'><input type='radio' name='expMode' value='multiSheet'/> 单文件多sheet页</label>
        <label style='margin-left:16px;'><input type='radio' name='expMode' value='multiFile'/> 多文件单sheet页</label>`;
      html += '<div style="margin-top:16px;text-align:right;"><button id="doExportBtn">导出</button> <button id="cancelExportBtn">取消</button></div>';
      const dlg = document.createElement('div');
      dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.15);display:flex;align-items:center;justify-content:center;'><div style='background:#fff;padding:24px 32px;border-radius:8px;min-width:340px;box-shadow:0 2px 8px #aaa;'>${html}</div></div>`;
      document.body.appendChild(dlg);
      dlg.onclick = function (e) {
        if (e.target.id === 'cancelExportBtn') document.body.removeChild(dlg);
        if (e.target.id === 'doExportBtn') {
          // 获取选项
          const checkedTypes = Array.from(dlg.querySelectorAll('.expType:checked')).map(c => c.value);
          const mode = dlg.querySelector('input[name=expMode]:checked').value;
          if (checkedTypes.length === 0) return alert('请选择导出类型');
          doExport(checkedTypes, mode);
          document.body.removeChild(dlg);
        }
      };
    };
    // 导出主逻辑（模拟xlsx，实际csv）
    function doExport(typeArr, mode) {
      const all = getAnswerList();
      // 按类型分组
      const group = {};
      typeArr.forEach(t => group[t] = []);
      all.forEach(row => {
        let key = row.answer || '';
        if (!typeArr.includes(key)) return;
        group[key].push(row);
      });
      // 字段顺序：编号、条目描述、应答、应答说明（含图片）、索引
      const fields = ['no', 'desc', 'answer', 'explain', 'index'];
      const headers = ['编号', '条目描述', '应答', '应答说明', '索引'];
      // 文件名规则
      const taskName = 'SOC应答导出';
      const now = new Date();
      const timeStr = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}_${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}`;
      // 应答类型映射
      const answerMap = JSON.parse(localStorage.getItem('socAnswerMap') || '{"FC":"Fully Compliant","PC":"Partially Compliant","NC":"Non-Compliant","N/A":"Not Applicable","":"Not Applicable"}');
      // 索引格式
      const indexFormat = localStorage.getItem('socIndexFormat') || '$DOC$-$PAGE$';
      // 索引格式变量替换
      function formatIndex(row) {
        let fmt = indexFormat;
        const vars = {
          '$DOC$': row.doc || '',
          '$PAGE$': row.page || '',
          '$MAJORCHAPTER$': row.majorChapter || '',
          '$SUBCHAPTER$': row.subChapter || '',
          '$YEAR$': row.year || '',
          '$AUTHOR$': row.author || '',
          '$CUSTOM1$': row.custom1 || '',
          '$CUSTOM2$': row.custom2 || '',
          '$CUSTOM3$': row.custom3 || '',
          '$CUSTOM4$': row.custom4 || '',
          '$CUSTOM5$': row.custom5 || ''
        };
        Object.keys(vars).forEach(k => { fmt = fmt.replaceAll(k, vars[k]); });
        return fmt;
      }
      function rowToArr(row) {
        return [
          row.no || '',
          row.desc || '',
          answerMap[row.answer || ''] || row.answer || '',
          (row.explain || '') + (row.img ? ` [图片:${row.img}]` : ''),
          formatIndex(row),
          row.remark || ''
        ];
      }
      // ...后续导出逻辑保持不变... 
    }
    // 下载文件
    function downloadFile(filename, content) {
      const blob = new Blob([content], { type: 'text/csv' });
      const a = document.createElement('a');
      a.href = URL.createObjectURL(blob);
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      setTimeout(() => { document.body.removeChild(a); }, 100);
    }
    // 相似条目检测（简单文本相似度模拟）
    function findSimilarGroups(list) {
      let groups = [];
      let used = new Set();
      for (let i = 0; i < list.length; i++) {
        if (used.has(i)) continue;
        let group = [i];
        for (let j = i + 1; j < list.length; j++) {
          if (used.has(j)) continue;
          if (list[i].desc && list[j].desc && list[i].desc.replace(/模拟/g, '') === list[j].desc.replace(/模拟/g, '')) {
            group.push(j);
            used.add(j);
          }
        }
        if (group.length > 1) groups.push(group);
      }
      return groups;
    }
    // 相似条目查看功能
    function showSimilarItems() {
      const list = getAnswerList();
      const groups = findSimilarGroups(list);
      if (groups.length === 0) return alert('无相似条目');

      let html = '<div style="max-height:320px;overflow:auto;">';
      groups.forEach((g, gi) => {
        html += `<div style='margin-bottom:10px;'><b>分组${gi + 1}：</b>`;
        g.forEach(idx => {
          html += `<div style='margin-left:16px;'>[${list[idx].no || idx + 1}] ${list[idx].desc} <button data-idx='${idx}' data-act='batchApply'>批量应用应答</button></div>`;
        });
        html += `<button data-act='ignoreGroup' data-gi='${gi}' style='margin-left:16px;color:#aaa;font-size:12px;'>忽略本组提示</button>`;
        html += '</div>';
      });
      html += '</div><div style="margin-top:8px;text-align:right;"><button id="closeSimDlg">关闭</button></div>';

      const dlg = document.createElement('div');
      dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.15);display:flex;align-items:center;justify-content:center;'><div style='background:#fff;padding:24px 32px;border-radius:8px;min-width:320px;box-shadow:0 2px 8px #aaa;'><h3 style="margin-top:0;">相似条目分组</h3>${html}</div></div>`;
      document.body.appendChild(dlg);

      dlg.onclick = function (e) {
        if (e.target.id === 'closeSimDlg') document.body.removeChild(dlg);
        if (e.target.dataset.act === 'batchApply') {
          const idx = parseInt(e.target.dataset.idx);
          const group = groups.find(g => g.includes(idx));
          if (!group) return;
          const list = getAnswerList();
          const ref = list[idx];
          group.forEach(i => {
            list[i].answer = ref.answer;
            list[i].explain = ref.explain + '（批量应用）';
          });
          saveAnswerList(list);
          renderAnswerTable();
          alert('已批量应用该应答到本组所有条目');
          document.body.removeChild(dlg);
        }
        if (e.target.dataset.act === 'ignoreGroup') {
          const gi = parseInt(e.target.dataset.gi);
          let ignored = JSON.parse(localStorage.getItem('socIgnoredSimGroups') || '[]');
          if (!ignored.includes(gi)) ignored.push(gi);
          localStorage.setItem('socIgnoredSimGroups', JSON.stringify(ignored));
          document.body.removeChild(dlg);
          renderAnswerTable();
        }
      };
    }

    // 相似条目分组弹窗
    if (showSimilarBtn) showSimilarBtn.onclick = function (e) {
      e.preventDefault();
      showSimilarItems();
    };

    // ===== 自然语言对话区逻辑 =====
    const luiForm = document.getElementById('luiForm');
    const luiInput = document.getElementById('luiInput');
    const luiHistory = document.getElementById('luiHistory');
    const quickCmdHeader = document.getElementById('quickCmdHeader');
    const quickCmdBar = document.getElementById('quickCmdBar');
    const quickCmdToggle = document.getElementById('quickCmdToggle');

    const docRefBar = document.getElementById('docRefBar');
    const smartPromptArea = document.getElementById('smartPromptArea');
    const smartPrompts = document.getElementById('smartPrompts');
    if (quickCmdHeader && quickCmdBar && quickCmdToggle) {
      // 初始状态为展开
      quickCmdBar.style.display = 'flex';
      quickCmdToggle.style.transform = 'rotate(0deg)';
      quickCmdHeader.onclick = function () {
        const isHidden = quickCmdBar.style.display === 'none';
        if (isHidden) {
          quickCmdBar.style.display = 'flex';
          quickCmdToggle.style.transform = 'rotate(0deg)';
        } else {
          quickCmdBar.style.display = 'none';
          quickCmdToggle.style.transform = 'rotate(180deg)';
        }
      };
    }

    // LUI历史本地存储
    function getLuiHistory() {
      return JSON.parse(localStorage.getItem('socLuiHistory') || '[]');
    }
    function saveLuiHistory(list) {
      localStorage.setItem('socLuiHistory', JSON.stringify(list));
    }

    // 智能提示模板系统
    const promptTemplates = {
      // 参数设置模板
      paramSetting: [
        { text: "设置项目参数：产品[5GC,VoLTE] 国家[泰国] 客户[AIS]", type: "param" },
        { text: "配置项目：产品5GC 国家泰国 运营商AIS 项目泰国AIS5GC", type: "param" },
        { text: "设置产品为5GC和VoLTE，目标国家泰国", type: "param" }
      ],
      // 文档管理模板
      docManagement: [
        { text: "上传项目文档招标文件.pdf", type: "doc" },
        { text: "从文档库选择5GC技术白皮书", type: "doc" },
        { text: "引用文档：招标文件.pdf作为项目文档", type: "doc" }
      ],
      // 应答生成模板
      answerGeneration: [
        { text: "分析5GC产品的安全要求", type: "answer" },
        { text: "生成VoLTE系统的多语言支持应答", type: "answer" },
        { text: "分析IMS产品的云部署能力", type: "answer" }
      ],
      // 批量操作模板
      batchOperation: [
        { text: "重新应答选中的条目", type: "batch" },
        { text: "批量优化所有PC状态的应答说明", type: "batch" },
        { text: "导出满足条件的条目", type: "batch" }
      ],
      // 查询模板
      query: [
        { text: "查看当前项目的满足度统计", type: "query" },
        { text: "搜索包含安全要求的相关条目", type: "query" },
        { text: "显示历史应答记录", type: "query" },
        { text: "查看相似条目", type: "query" }
      ]
    };

    // 智能推荐提示模板
    function recommendPrompts(input) {
      if (!input || input.length < 2) {
        smartPromptArea.style.display = 'none';
        return;
      }

      const recommendations = [];
      const keywords = input.toLowerCase();

      // 根据关键词推荐模板
      if (keywords.includes('设置') || keywords.includes('配置') || keywords.includes('参数')) {
        recommendations.push(...promptTemplates.paramSetting);
      }
      if (keywords.includes('上传') || keywords.includes('文档') || keywords.includes('选择')) {
        recommendations.push(...promptTemplates.docManagement);
      }
      if (keywords.includes('分析') || keywords.includes('生成') || keywords.includes('应答')) {
        recommendations.push(...promptTemplates.answerGeneration);
      }
      if (keywords.includes('批量') || keywords.includes('重新') || keywords.includes('导出')) {
        recommendations.push(...promptTemplates.batchOperation);
      }
      if (keywords.includes('查看') || keywords.includes('搜索') || keywords.includes('统计')) {
        recommendations.push(...promptTemplates.query);
      }

      // 如果没有匹配的关键词，推荐常用模板
      if (recommendations.length === 0) {
        recommendations.push(
          promptTemplates.paramSetting[0],
          promptTemplates.answerGeneration[0],
          promptTemplates.query[0]
        );
      }

      // 去重并限制数量
      const uniqueRecs = recommendations.filter((rec, index, self) =>
        index === self.findIndex(r => r.text === rec.text)
      ).slice(0, 3);

      renderSmartPrompts(uniqueRecs);
    }

    // 渲染智能提示
    function renderSmartPrompts(prompts) {
      if (prompts.length === 0) {
        smartPromptArea.style.display = 'none';
        return;
      }

      smartPrompts.innerHTML = prompts.map(prompt =>
        `<button class="smart-prompt-btn" data-text="${prompt.text}" style="background:#f0f5ff;color:#1765d5;border:1px solid #d6e4ff;border-radius:6px;padding:4px 8px;font-size:12px;cursor:pointer;transition:all 0.2s;">${prompt.text}</button>`
      ).join('');

      smartPromptArea.style.display = 'block';
    }
    // ===== 对话区历史左右气泡排布 =====
    function renderLuiHistory() {
      const list = getLuiHistory();
      if (!luiHistory) return;
      luiHistory.innerHTML = list.map(item =>
        `<div style='display:flex;${item.role === "user" ? "justify-content:flex-end;" : "justify-content:flex-start;"}'>
          <div style='max-width:70%;padding:8px 14px;border-radius:16px;box-shadow:0 1px 4px #eee;${item.role === "user" ? "background:#c2dfff;color:#1a1a1a;margin-left:30px;text-align:right;" : "background:#fafbfc;color:#1a1a1a;margin-right:30px;text-align:left;"}'>
            ${item.text}
          </div>
        </div>`
      ).join('');
      luiHistory.scrollTop = luiHistory.scrollHeight;
    }
    // 修改对话存储结构，区分role
    function addLuiHistory(role, text) {
      const list = getLuiHistory();
      list.push({ role, text });
      saveLuiHistory(list);
    }
    // 修改LUI表单提交逻辑
    luiForm.onsubmit = function (e) {
      e.preventDefault();
      const q = luiInput.value.trim();
      if (!q) return;
      addLuiHistory('user', q);
      renderLuiHistory();
      luiInput.value = '';
      // 取最近3轮上下文
      const context = luiContext.slice(-3).map(item => item.q + item.a).join('\n');
      const a = simulateAIAnswer(q, context);
      addLuiHistory('ai', a);
      renderLuiHistory();
      luiContext.push({ q, a });
    };
    // 首次渲染历史
    renderLuiHistory();
    // 自然语言意图识别与实体提取
    function parseNaturalLanguage(input) {
      const intent = {
        type: '',
        entities: {},
        actions: []
      };

      const text = input.toLowerCase();

      // 参数设置意图识别
      if (text.includes('设置') || text.includes('配置') || text.includes('参数')) {
        intent.type = 'param_setting';

        // 提取产品参数
        const productMatch = text.match(/产品[：:]\s*([^，,]+)/) || text.match(/产品\s*([^，,]+)/);
        if (productMatch) {
          intent.entities.products = productMatch[1].split(/[,，]/).map(p => p.trim());
        }

        // 提取国家参数
        const countryMatch = text.match(/国家[：:]\s*([^，,]+)/) || text.match(/国家\s*([^，,]+)/);
        if (countryMatch) {
          intent.entities.country = countryMatch[1].trim();
        }

        // 提取运营商参数
        const operatorMatch = text.match(/客户[：:]\s*([^，,]+)/) || text.match(/客户\s*([^，,]+)/);
        if (operatorMatch) {
          intent.entities.operator = operatorMatch[1].trim();
        }

        // 提取项目名称
        const projectMatch = text.match(/项目[：:]\s*([^，,]+)/) || text.match(/项目\s*([^，,]+)/);
        if (projectMatch) {
          intent.entities.project = projectMatch[1].trim();
        }
      }

      // 文档管理意图识别
      else if (text.includes('上传') || text.includes('选择') || text.includes('引用')) {
        intent.type = 'doc_management';

        if (text.includes('上传')) {
          intent.actions.push('upload');
          const fileMatch = text.match(/上传[^，,]*?([^，,]+\.(pdf|doc|docx|xlsx|xls))/);
          if (fileMatch) {
            intent.entities.filename = fileMatch[1];
          }
        }

        if (text.includes('选择') || text.includes('引用')) {
          intent.actions.push('select');
        }
      }

      // 应答生成意图识别
      else if (text.includes('分析') || text.includes('生成') || text.includes('应答')) {
        intent.type = 'answer_generation';

        const productMatch = text.match(/(5gc|volte|ims|核心网|接入网)/);
        if (productMatch) {
          intent.entities.product = productMatch[1];
        }

        const requirementMatch = text.match(/(安全|多语言|云部署|性能|可靠性)/);
        if (requirementMatch) {
          intent.entities.requirement = requirementMatch[1];
        }
      }

      // 批量操作意图识别
      else if (text.includes('批量') || text.includes('重新') || text.includes('导出')) {
        intent.type = 'batch_operation';

        if (text.includes('AI应答')) {
          intent.actions.push('regenerate');
        }

        if (text.includes('导出')) {
          intent.actions.push('export');
        }

        const statusMatch = text.match(/(fc|pc|nc|n\/a)/);
        if (statusMatch) {
          intent.entities.status = statusMatch[1].toUpperCase();
        }
      }

      // 查询意图识别
      else if (text.includes('查看') || text.includes('搜索') || text.includes('统计')) {
        intent.type = 'query';

        if (text.includes('满足度') || text.includes('统计')) {
          intent.actions.push('statistics');
        }

        if (text.includes('搜索')) {
          intent.actions.push('search');
        }
      }

      // 优先级配置意图识别
      else if (text.includes('优先级') || text.includes('配置') && (text.includes('数据源') || text.includes('规则'))) {
        intent.type = 'priority_config';

        if (text.includes('设置') || text.includes('配置')) {
          intent.actions.push('config');
        }

        if (text.includes('查看')) {
          intent.actions.push('view');
        }

        if (text.includes('重置')) {
          intent.actions.push('reset');
        }
      }

      // 满足度计算意图识别
      else if (text.includes('满足度') && (text.includes('计算') || text.includes('统计') || text.includes('分析'))) {
        intent.type = 'satisfaction_calc';
        intent.actions.push('calculate');
      }

      // 删除意图识别
      else if (text.includes('删除') || text.includes('清空') || text.includes('移除')) {
        intent.type = 'delete';

        if (text.includes('选中') || text.includes('批量')) {
          intent.actions.push('batch_delete');
        }
        if (text.includes('全部') || text.includes('所有') || text.includes('清空')) {
          intent.actions.push('clear_all');
        }
        if (text.includes('条目') && !text.includes('选中') && !text.includes('全部')) {
          intent.actions.push('single_delete');
        }
      }

      return intent;
    }

    // 执行自然语言指令
    function executeNaturalLanguageCommand(intent) {
      let response = '';

      switch (intent.type) {
        case 'param_setting':
          response = '【参数设置】';
          if (intent.entities.products) {
            response += `已设置产品：${intent.entities.products.join('、')}。`;
            // 联动GUI表单
            if (productSelectDialog) {
              Array.from(productSelectDialog.options).forEach(opt => {
                opt.selected = intent.entities.products.includes(opt.value);
              });
            }
          }
          if (intent.entities.country) {
            response += `已设置国家：${intent.entities.country}。`;
            if (countrySelectDialog) countrySelectDialog.value = intent.entities.country;
          }
          if (intent.entities.operator) {
            response += `已设置运营商：${intent.entities.operator}。`;
            if (operatorSelectDialog) operatorSelectDialog.value = intent.entities.operator;
          }
          if (intent.entities.project) {
            response += `已设置项目：${intent.entities.project}。`;
            if (projectInputDialog) projectInputDialog.value = intent.entities.project;
          }
          response += '参数设置完成，是否开始生成应答？';
          break;

        case 'doc_management':
          response = '【文档管理】';
          if (intent.actions.includes('upload')) {
            response += `已上传文档：${intent.entities.filename || '文档'}。`;
          }
          if (intent.actions.includes('select')) {
            response += '已选择参考文档。';
          }
          response += '文档准备完成。';
          break;

        case 'answer_generation':
          response = '【应答生成】';
          if (intent.entities.product && intent.entities.requirement) {
            response += `正在分析${intent.entities.product}的${intent.entities.requirement}要求...`;

            // 模拟生成应答条目
            const list = getAnswerList();
            const newRow = {
              no: list.length + 1,
              desc: `分析${intent.entities.product}的${intent.entities.requirement}要求`,
              answer: ['FC', 'PC', 'NC', 'N/A'][Math.floor(Math.random() * 4)],
              explain: `AI自动生成的${intent.entities.product} ${intent.entities.requirement}应答内容（模拟）`,
              img: '',
              index: 'AI-1'
            };
            list.push(newRow);
            saveAnswerList(list);
            renderAnswerTable();
          }
          response += '应答生成完成，请在内容区查看结果。';
          break;

        case 'batch_operation':
          response = '【批量操作】';
          if (intent.actions.includes('regenerate')) {
            response += `正在重新应答${intent.entities.status ? intent.entities.status + '状态' : '选中'}的条目...`;
          }
          if (intent.actions.includes('export')) {
            response += '正在导出满足条件的条目...';
          }
          response += '批量操作完成。';
          break;

        case 'query':
          response = '【查询结果】';
          if (intent.actions.includes('statistics')) {
            const list = getAnswerList();
            const fcCount = list.filter(item => item.answer === 'FC').length;
            const pcCount = list.filter(item => item.answer === 'PC').length;
            const ncCount = list.filter(item => item.answer === 'NC').length;
            response += `当前项目满足度统计：FC ${fcCount}条，PC ${pcCount}条，NC ${ncCount}条。`;
          }
          if (intent.actions.includes('search')) {
            response += '搜索结果已显示在内容区。';
          }
          break;

        case 'priority_config':
          response = '【优先级配置】';
          if (intent.actions.includes('config')) {
            response += '正在打开优先级配置界面...';
            showPriorityConfigDialog();
          } else if (intent.actions.includes('view')) {
            const currentPriority = JSON.parse(localStorage.getItem('priorityConfig') || '["项目文档", "文档库选定文档", "文档库全量文档", "上传历史应标文档", "GBBS系统数据"]');
            response += '当前优先级配置：' + currentPriority.map((item, index) => (index + 1) + '. ' + item).join('；');
          } else if (intent.actions.includes('reset')) {
            const defaultPriority = ["项目文档", "文档库选定文档", "文档库全量文档", "上传历史应标文档", "GBBS系统数据"];
            localStorage.setItem('priorityConfig', JSON.stringify(defaultPriority));
            response += '优先级配置已重置为默认设置。';
          } else {
            response += '正在打开优先级配置界面...';
            showPriorityConfigDialog();
          }
          break;

        case 'satisfaction_calc':
          response = '【满足度计算】正在计算当前项目的满足度指标...';
          calculateSatisfaction();
          return; // 直接返回，不添加到对话历史，因为calculateSatisfaction会自己添加结果

        case 'delete':
          response = '【删除操作】';
          if (intent.actions.includes('batch_delete')) {
            response += '正在执行批量删除操作...';
            confirmBatchDelete();
          } else if (intent.actions.includes('clear_all')) {
            response += '正在执行清空全部操作...';
            confirmClearAllItems();
          } else {
            response += '请选择要删除的条目，然后点击删除按钮或使用"删除选中条目"指令。';
          }
          break;

        default:
          response = '我理解您的需求，但需要更多信息来执行操作。请尝试使用更具体的描述，如"设置项目参数：产品5GC 国家泰国"。';
      }

      return response;
    }

    // LUI指令解析与AI应答模拟（增强）
    function simulateAIAnswer(cmd, context) {
      // 首先尝试自然语言理解
      const intent = parseNaturalLanguage(cmd);
      if (intent.type) {
        return executeNaturalLanguageCommand(intent);
      }

      // 1. 识别为应答问题
      if (/分析|请问|如何|怎么|能否|是否|优势|劣势|对比|说明|解释|举例|请给出|请列举|请描述/.test(cmd)) {
        // 模拟AI生成应答条目
        const answer = `【AI应答】针对"${cmd}"，系统已为您生成推荐应答，请在内容区查看。`;
        // 联动内容区：表格新增一条应答
        const list = getAnswerList();
        const newRow = {
          no: list.length + 1,
          desc: cmd,
          answer: ['FC', 'PC', 'NC', 'N/A'][Math.floor(Math.random() * 4)],
          explain: 'AI自动生成的应答内容（模拟）',
          img: '',
          index: 'AI-1'
        };
        list.push(newRow);
        saveAnswerList(list);
        renderAnswerTable();
        return answer;
      }
      // 2. 批量处理指令
      if (cmd.match(/批量处理.*文件|批量导入|导入.*excel/i)) {
        // 直接触发批量导入按钮，使用模拟数据
        const importBtn = document.getElementById('importExcelBtn');
        if (importBtn) {
          setTimeout(() => importBtn.click(), 100);
        }

        return '【批量处理】正在启动批量导入流程，系统将使用模拟Excel数据进行演示...';
      }

      // 3. 其它指令（复用原有模拟）
      if (cmd.match(/^上传项目文档/)) return '已上传项目文档（模拟）';
      if (cmd.match(/^上传历史应答文档/)) return '已上传历史SOC文档（模拟）';
      if (cmd.match(/^从文档库选择参考文档/)) return '已选择文档库文档（模拟）';
      if (cmd.match(/^选择历史文档/)) return '已选择历史文档（模拟）';
      if (cmd.match(/^校验准备项/)) return '准备项校验通过（模拟）';
      if (cmd.match(/^保存当前准备为模板/)) return '已保存准备模板（模拟）';
      if (cmd.match(/^全部重新应答/)) return '已触发全部重新应答（模拟）';
      if (cmd.match(/^AI应答/)) return '已触发重新应答（模拟）';
      if (cmd.match(/^导出/)) return '已导出（模拟）';
      if (cmd.match(/^查找相似条目/) || cmd.match(/^查看相似条目/) || cmd.match(/^显示相似条目/)) {
        showSimilarItems();
        return '已打开相似条目查看窗口';
      }
      if (cmd.match(/^润色/)) return '已触发AI润色（模拟）';
      if (cmd.match(/^翻译/)) return '已触发AI翻译（模拟）';
      return '暂不支持该指令（模拟）';
    }
    // 简单上下文记忆
    let luiContext = [];
    // 智能提示模板事件监听
    if (smartPrompts) {
      smartPrompts.onclick = function (e) {
        if (e.target.classList.contains('smart-prompt-btn')) {
          luiInput.value = e.target.dataset.text;
          luiInput.focus();
          smartPromptArea.style.display = 'none';
        }
      };
    }

    // 输入框监听，实时推荐提示模板
    if (luiInput) {
      luiInput.addEventListener('input', function () {
        recommendPrompts(this.value);
      });

      // 点击输入框外部时隐藏提示区域
      document.addEventListener('click', function (e) {
        if (!luiInput.contains(e.target) && !smartPromptArea.contains(e.target)) {
          smartPromptArea.style.display = 'none';
        }
      });
    }

    // 快捷指令栏
    const quickCmds = [
      '设置项目参数：产品5GC和VoLTE，国家泰国，运营商AIS',
      '查看当前项目的满足度统计',
      '重新应答PC状态的条目',
      '导出满足条件的条目',
      '设置优先级配置',
      '查看相似条目'
    ];
    if (quickCmdBar) quickCmdBar.innerHTML = quickCmds.map(cmd => `<button type='button' class='quick-cmd-btn' style="background:#fafbfc;color:#1a1a1a;border:1px solid #fafbfc;border-radius:6px;padding:4px 8px;font-size:12px;cursor:pointer;transition:all 0.2s;margin:2px;">${cmd}</button>`).join('');
    if (quickCmdBar) quickCmdBar.onclick = function (e) {
      if (e.target.classList.contains('quick-cmd-btn')) {
        luiInput.value = e.target.textContent;
        luiInput.focus();
      }
    };
    // 文档引用区
    function renderDocRefBar() {
      const list = getMaterialList();
      if (!docRefBar) return;
      docRefBar.innerHTML = list.length === 0 ? '<span style="color:#aaa;">暂无文档</span>' : list.map(d =>
        `<span style='background:#f6ffed;border:1px solid #b7eb8f;border-radius:4px;padding:2px 8px;font-size:13px;'>${d.name}</span>`
      ).join('');
    }
    renderDocRefBar();
    // 素材变更时同步引用区和弹窗
    const origRenderMaterialTags = renderMaterialTags;
    renderMaterialTags = function () {
      origRenderMaterialTags();
      renderDocRefBar();
      renderMaterialTagsDialog();
    };

    // ===== 参数/素材弹窗逻辑 =====
    const openParamMaterialBtn = document.getElementById('openParamMaterialBtn');
    function showParamMaterialDialog() {
      let html = `<div style='width:520px;max-width:96vw;'>
        <div style='display:flex;border-bottom:1px solid #ececec;'>
          <div id='tabParam' style='flex:1;padding:12px 0;text-align:center;cursor:pointer;font-weight:500;border-bottom:2px solid #1765d5;color:#1765d5;'>参数设置</div>
          <div id='tabMaterial' style='flex:1;padding:12px 0;text-align:center;cursor:pointer;font-weight:500;border-bottom:2px solid transparent;'>素材准备</div>
        </div>
        <div id='tabParamContent' style='padding:18px 8px 8px 8px;'>
          <form id="paramFormDialog" style="display:flex;flex-wrap:wrap;gap:16px 32px;align-items:flex-end;">
            <div>
              <label>产品选择</label><br/>
              <div id="productSelectDialog" class="tree-select" style="min-width:120px;width:160px;"></div>
            </div>
            <div>
              <label>国家</label><br/>
              <div id="countrySelectDialog" class="searchable-select" style="min-width:100px;width:120px;"></div>
            </div>
            <div>
              <label>客户</label><br/>
              <div id="operatorSelectDialog" class="searchable-select" style="min-width:100px;width:120px;"></div>
            </div>
            <div>
              <label>项目名称</label><br/>
              <input id="projectInputDialog" type="text" style="width:160px;" placeholder="请输入项目名称" />
            </div>
            <div>
              <label>参数模板</label><br/>
              <select id="templateSelectDialog" style="min-width:120px;width:160px;"></select>
            </div>
            <div style="display:flex;gap:8px;">
              <button type="button" id="saveTemplateBtnDialog">保存为模板</button>
              <button type="button" id="restoreDefaultBtnDialog">恢复默认</button>
              <button type="button" id="clearParamBtnDialog">清空参数</button>
            </div>
          </form>
          <div id="paramChangedBarDialog" style="display:none;margin-top:10px;padding:8px 16px;background:#fffbe6;border:1px solid #ffe58f;border-radius:4px;color:#ad8b00;">
            参数已变更，建议 <button id="reAnswerBtnDialog" style="color:#1765d5;background:none;border:none;cursor:pointer;">全部重新应答</button>
          </div>
        </div>
        <div id='tabMaterialContent' style='display:none;padding:18px 8px 8px 8px;'>
          <!-- 文档上传区域 -->
          <div style="margin-bottom:20px;">
            <div style="font-weight:500;margin-bottom:12px;color:#333;">📁 文档上传</div>
            <div style="display:grid;grid-template-columns:1fr 1fr;gap:12px;">
              <div style="border:2px dashed #d9d9d9;border-radius:8px;padding:16px;text-align:center;transition:all 0.3s;" 
                   onmouseover="this.style.borderColor='#1765d5';this.style.backgroundColor='#f0f5ff';" 
                   onmouseout="this.style.borderColor='#d9d9d9';this.style.backgroundColor='transparent';">
                <div style="font-size:24px;margin-bottom:8px;">📄</div>
                <div style="font-weight:500;margin-bottom:4px;">项目文档</div>
                <div style="font-size:12px;color:#666;margin-bottom:12px;">招标文件、技术协议等</div>
                <button type="button" id="uploadProjectDocBtnDialog" class="soc-btn soc-btn-blue" style="width:100%;">选择文件</button>
                <input type="file" id="uploadProjectDocInputDialog" accept=".pdf,.doc,.docx,.xlsx,.xls" style="display:none;" />
            </div>
              <div style="border:2px dashed #d9d9d9;border-radius:8px;padding:16px;text-align:center;transition:all 0.3s;" 
                   onmouseover="this.style.borderColor='#1765d5';this.style.backgroundColor='#f0f5ff';" 
                   onmouseout="this.style.borderColor='#d9d9d9';this.style.backgroundColor='transparent';">
                <div style="font-size:24px;margin-bottom:8px;">📋</div>
                <div style="font-weight:500;margin-bottom:4px;">历史SOC文档</div>
                <div style="font-size:12px;color:#666;margin-bottom:12px;">历史应答文档、经验总结</div>
                <button type="button" id="uploadSocDocBtnDialog" class="soc-btn soc-btn-blue" style="width:100%;">选择文件</button>
                <input type="file" id="uploadSocDocInputDialog" accept=".pdf,.doc,.docx,.xlsx,.xls" style="display:none;" />
            </div>
            </div>
            </div>

          <!-- 文档选择区域 -->
          <div style="margin-bottom:20px;">
            <div style="font-weight:500;margin-bottom:12px;color:#333;">📚 文档选择</div>
            <div style="display:grid;grid-template-columns:1fr 1fr;gap:12px;">
              <div style="border:1px solid #e8e8e8;border-radius:8px;padding:16px;background:#fafafa;">
                <div style="font-weight:500;margin-bottom:8px;">📖 文档库</div>
                <div style="font-size:12px;color:#666;margin-bottom:12px;">从Studio知识库选择参考文档</div>
                <button type="button" id="selectLibDocBtnDialog" class="soc-btn" style="width:100%;">选择参考文档</button>
            </div>
              <div style="border:1px solid #e8e8e8;border-radius:8px;padding:16px;background:#fafafa;">
                <div style="font-weight:500;margin-bottom:8px;">🕓 历史文档</div>
                <div style="font-size:12px;color:#666;margin-bottom:12px;">复用历史上传的文档</div>
                <button type="button" id="selectHistoryDocBtnDialog" class="soc-btn" style="width:100%;">选择历史文档</button>
            </div>
          </div>
          </div>

          <!-- 已选文档展示区域 -->
          <div style="margin-bottom:20px;">
            <div style="font-weight:500;margin-bottom:12px;color:#333;display:flex;align-items:center;justify-content:space-between;">
              <span>📎 已选文档</span>
              <span id="materialCountDialog" style="font-size:12px;color:#666;">0个文档</span>
            </div>
            <div id="materialTagsDialog" style="min-height:60px;border:1px solid #e8e8e8;border-radius:6px;padding:12px;background:#fafafa;display:flex;flex-wrap:wrap;gap:8px;align-items:flex-start;">
              <span style="color:#aaa;font-size:13px;align-self:center;">暂无已选文档</span>
            </div>
          </div>

          <!-- 操作按钮区域 -->
          <div style="display:flex;gap:12px;justify-content:space-between;align-items:center;">
            <div style="display:flex;gap:8px;">
              <button type="button" id="checkMaterialBtnDialog" class="soc-btn soc-btn-yellow">✓ 校验准备项</button>
              <button type="button" id="saveMaterialTemplateBtnDialog" class="soc-btn">💾 保存为模板</button>
            </div>
            <div style="font-size:12px;">
              <span id="materialStatusDialog" class="material-status incomplete">准备状态：未完成</span>
            </div>
          </div>
        </div>
        <div style='text-align:right;margin-top:18px;'><button id='closeParamMaterialDlg' style='background:#1765d5;color:#fff;border:none;border-radius:4px;padding:6px 24px;cursor:pointer;'>关闭</button></div>
        </div>`;
      const dlg = document.createElement('div');
      dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.18);display:flex;align-items:center;justify-content:center;'><div style='background:#fff;padding:0 0 0 0;border-radius:12px;min-width:340px;box-shadow:0 4px 24px #aaa;'>${html}</div></div>`;
      document.body.appendChild(dlg);
      // tab切换
      const tabParam = dlg.querySelector('#tabParam');
      const tabMaterial = dlg.querySelector('#tabMaterial');
      const tabParamContent = dlg.querySelector('#tabParamContent');
      const tabMaterialContent = dlg.querySelector('#tabMaterialContent');
      tabParam.onclick = function () {
        tabParam.style.color = '#1765d5';
        tabParam.style.borderBottom = '2px solid #1765d5';
        tabMaterial.style.color = '#333';
        tabMaterial.style.borderBottom = '2px solid transparent';
        tabParamContent.style.display = '';
        tabMaterialContent.style.display = 'none';
      };
      tabMaterial.onclick = function () {
        tabMaterial.style.color = '#1765d5';
        tabMaterial.style.borderBottom = '2px solid #1765d5';
        tabParam.style.color = '#333';
        tabParam.style.borderBottom = '2px solid transparent';
        tabParamContent.style.display = 'none';
        tabMaterialContent.style.display = '';
      };
      // 默认tab
      tabParam.onclick();
      // 关闭
      dlg.querySelector('#closeParamMaterialDlg').onclick = function () {
        document.body.removeChild(dlg);
      };
      // 参数/素材表单内容填充与事件复用
      // 初始化弹窗中的素材标签
      renderMaterialTagsDialog();

      // 弹窗中的素材标签事件处理
      dlg.addEventListener('click', function (e) {
        if (e.target.dataset.act === 'preview') {
          const idx = parseInt(e.target.dataset.idx);
          const list = getMaterialList();
          if (list[idx]) {
            alert(`预览文档：${list[idx].name}（模拟）`);
          }
        }
        if (e.target.dataset.act === 'del') {
          const idx = parseInt(e.target.dataset.idx);
          if (confirm('确定删除此文档？')) {
            const list = getMaterialList();
            list.splice(idx, 1);
            saveMaterialList(list);
            renderMaterialTags();
          }
        }
      });

      // 弹窗中的上传按钮事件
      const uploadProjectDocBtnDialog = dlg.querySelector('#uploadProjectDocBtnDialog');
      const uploadProjectDocInputDialog = dlg.querySelector('#uploadProjectDocInputDialog');
      const uploadSocDocBtnDialog = dlg.querySelector('#uploadSocDocBtnDialog');
      const uploadSocDocInputDialog = dlg.querySelector('#uploadSocDocInputDialog');
      const selectLibDocBtnDialog = dlg.querySelector('#selectLibDocBtnDialog');
      const selectHistoryDocBtnDialog = dlg.querySelector('#selectHistoryDocBtnDialog');
      const checkMaterialBtnDialog = dlg.querySelector('#checkMaterialBtnDialog');
      const saveMaterialTemplateBtnDialog = dlg.querySelector('#saveMaterialTemplateBtnDialog');

      // 上传项目文档
      if (uploadProjectDocBtnDialog) uploadProjectDocBtnDialog.onclick = () => uploadProjectDocInputDialog && uploadProjectDocInputDialog.click();
      if (uploadProjectDocInputDialog) uploadProjectDocInputDialog.onchange = function () {
        if (!this.files[0]) return;
        const file = this.files[0];

        // 文件格式验证
        const allowedTypes = ['.pdf', '.doc', '.docx', '.xlsx', '.xls'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        if (!allowedTypes.includes(fileExtension)) {
          alert('不支持的文件格式，请选择PDF、Word或Excel文件');
          this.value = '';
          return;
        }

        // 文件大小验证（50MB）
        if (file.size > 50 * 1024 * 1024) {
          alert('文件大小不能超过50MB');
          this.value = '';
          return;
        }

        const list = getMaterialList();
        list.push({ name: file.name, type: '项目文档', time: Date.now(), id: 'p' + Date.now() });
        saveMaterialList(list);
        renderMaterialTags();
        alert('上传成功（模拟）');
        this.value = '';
      };

      // 添加拖拽上传功能
      const projectUploadArea = dlg.querySelector('#uploadProjectDocBtnDialog').parentElement;
      const socUploadArea = dlg.querySelector('#uploadSocDocBtnDialog').parentElement;

      [projectUploadArea, socUploadArea].forEach(area => {
        area.addEventListener('dragover', function (e) {
          e.preventDefault();
          this.classList.add('dragover');
        });

        area.addEventListener('dragleave', function (e) {
          e.preventDefault();
          this.classList.remove('dragover');
        });

        area.addEventListener('drop', function (e) {
          e.preventDefault();
          this.classList.remove('dragover');

          const files = e.dataTransfer.files;
          if (files.length > 0) {
            const file = files[0];

            // 文件格式验证
            const allowedTypes = ['.pdf', '.doc', '.docx', '.xlsx', '.xls'];
            const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
            if (!allowedTypes.includes(fileExtension)) {
              alert('不支持的文件格式，请选择PDF、Word或Excel文件');
              return;
            }

            // 文件大小验证（50MB）
            if (file.size > 50 * 1024 * 1024) {
              alert('文件大小不能超过50MB');
              return;
            }

            const list = getMaterialList();
            const docType = this === projectUploadArea ? '项目文档' : '历史SOC文档';
            list.push({ name: file.name, type: docType, time: Date.now(), id: (docType === '项目文档' ? 'p' : 's') + Date.now() });
            saveMaterialList(list);
            renderMaterialTags();
            alert('拖拽上传成功（模拟）');
          }
        });
      });

      // 上传历史SOC文档
      if (uploadSocDocBtnDialog) uploadSocDocBtnDialog.onclick = () => uploadSocDocInputDialog && uploadSocDocInputDialog.click();
      if (uploadSocDocInputDialog) uploadSocDocInputDialog.onchange = function () {
        if (!this.files[0]) return;
        const file = this.files[0];

        // 文件格式验证
        const allowedTypes = ['.pdf', '.doc', '.docx', '.xlsx', '.xls'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        if (!allowedTypes.includes(fileExtension)) {
          alert('不支持的文件格式，请选择PDF、Word或Excel文件');
          this.value = '';
          return;
        }

        // 文件大小验证（50MB）
        if (file.size > 50 * 1024 * 1024) {
          alert('文件大小不能超过50MB');
          this.value = '';
          return;
        }

        const list = getMaterialList();
        list.push({ name: file.name, type: '历史SOC文档', time: Date.now(), id: 's' + Date.now() });
        saveMaterialList(list);
        renderMaterialTags();
        alert('上传成功（模拟）');
        this.value = '';
      };

      // 从文档库选择
      if (selectLibDocBtnDialog) selectLibDocBtnDialog.onclick = function () {
        const libDocs = [
          { name: '5GC技术白皮书', type: '文档库', id: 'lib1' },
          { name: 'VoLTE解决方案', type: '文档库', id: 'lib2' },
          { name: 'IMS部署指南', type: '文档库', id: 'lib3' },
          { name: '网络安全规范', type: '文档库', id: 'lib4' },
          { name: '性能测试报告', type: '文档库', id: 'lib5' }
        ];
        const sel = prompt('请输入要选择的文档编号（用,分隔）：\n' + libDocs.map((doc, i) => `${i + 1}. ${doc.name}`).join('\n'));
        if (!sel) return;
        const idxs = sel.split(',').map(s => parseInt(s.trim()) - 1).filter(i => i >= 0 && i < libDocs.length);
        if (idxs.length === 0) return alert('未选择');
        const list = getMaterialList();
        idxs.forEach(i => {
          if (!list.some(d => d.id === libDocs[i].id)) list.push({ ...libDocs[i], time: Date.now() });
        });
        saveMaterialList(list);
        renderMaterialTags();
        alert('已添加文档库文档');
      };

      // 选择历史文档
      if (selectHistoryDocBtnDialog) selectHistoryDocBtnDialog.onclick = function () {
        const all = JSON.parse(localStorage.getItem('socHistoryDocs') || '[]');
        if (all.length === 0) return alert('暂无历史上传文档');
        let html = '<div style="max-height:300px;overflow:auto;">';
        html += all.map((d, i) =>
          `<div style='margin-bottom:6px;'><input type='checkbox' id='histdoc${i}' data-idx='${i}' />
          <span>${d.name} <span style='color:#aaa;font-size:12px;'>(${d.type})</span></span>
          <button data-act='preview' data-idx='${i}'>预览</button>
          <button data-act='del' data-idx='${i}'>删除</button></div>`
        ).join('');
        html += '</div><div style="margin-top:8px;text-align:right;"><button id="histDocBatchAdd">批量关联</button> <button id="histDocClose">关闭</button></div>';
        const histDlg = document.createElement('div');
        histDlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:1000;background:rgba(0,0,0,0.15);display:flex;align-items:center;justify-content:center;'><div style='background:#fff;padding:24px 32px;border-radius:8px;min-width:320px;box-shadow:0 2px 8px #aaa;'>${html}</div></div>`;
        document.body.appendChild(histDlg);
        histDlg.onclick = function (e) {
          if (e.target.id === 'histDocClose') document.body.removeChild(histDlg);
          if (e.target.id === 'histDocBatchAdd') {
            const sel = Array.from(histDlg.querySelectorAll('input[type=checkbox]:checked')).map(c => parseInt(c.dataset.idx));
            if (sel.length === 0) return alert('未选择');
            const list = getMaterialList();
            sel.forEach(i => { if (!list.some(d => d.id === all[i].id)) list.push(all[i]); });
            saveMaterialList(list);
            renderMaterialTags();
            alert('已批量关联');
            document.body.removeChild(histDlg);
          }
          if (e.target.dataset.act === 'preview') alert('预览文档：' + all[e.target.dataset.idx].name + '（模拟）');
          if (e.target.dataset.act === 'del') {
            if (confirm('确定删除？')) {
              all.splice(e.target.dataset.idx, 1);
              localStorage.setItem('socHistoryDocs', JSON.stringify(all));
              document.body.removeChild(histDlg);
              if (selectHistoryDocBtnDialog) selectHistoryDocBtnDialog.onclick();
            }
          }
        };
      };

      // 校验准备项
      if (checkMaterialBtnDialog) checkMaterialBtnDialog.onclick = function () {
        const param = getParamForm();
        const mat = getMaterialList();
        let msg = '';
        if (!param.products.length) msg += '产品未选择\n';
        if (!param.country) msg += '国家未选择\n';
        if (!param.operator) msg += '运营商未选择\n';
        if (!param.project) msg += '项目名称未填写\n';
        if (mat.length === 0) msg += '未上传/选择任何文档\n';
        if (msg) alert('请完善以下项：\n' + msg);
        else alert('校验通过，准备项完整！');
      };

      // 保存当前准备为模板
      if (saveMaterialTemplateBtnDialog) saveMaterialTemplateBtnDialog.onclick = function () {
        const name = prompt('请输入准备模板名称');
        if (!name) return;
        let list = getAllTemplates();
        if (list.some(t => t.name === name)) { alert('模板名已存在'); return; }
        const tmpl = getParamForm();
        tmpl.id = 't' + Date.now();
        tmpl.name = name;
        tmpl.isDefault = false;
        tmpl.materials = getMaterialList();
        list.push(tmpl);
        saveAllTemplates(list);
        renderTemplateSelect();
        alert('准备模板已保存（含素材）');
        renderTaskTab('template');
      };
    }
    // ===== 导出设置弹窗逻辑 =====
    const exportSettingBtn = document.getElementById('exportSettingBtn');
    let answerMap = JSON.parse(localStorage.getItem('socAnswerMap') || '{"FC":"Fully Compliant","PC":"Partially Compliant","NC":"Non-Compliant","N/A":"Not Applicable","":"Not Applicable"}');
    let indexFormat = localStorage.getItem('socIndexFormat') || '$DOC$-$PAGE$';
    if (exportSettingBtn) exportSettingBtn.onclick = function () {
      let answerPlaceholders = {
        'FC': 'Fully Compliant',
        'PC': 'Partially Compliant',
        'NC': 'Non-Compliant',
        'N/A': 'Not Applicable',
        '': '（空）'
      };
      let html = `<div style='width:440px;max-width:96vw;'>
        <div style='font-size:20px;font-weight:600;margin-bottom:18px;letter-spacing:1px;'>导出设置</div>
        <div style='margin-bottom:22px;padding:18px 18px 14px 18px;background:#f8fafb;border-radius:10px;border:1px solid #f0f0f0;'>
          <b style='font-size:15px;'>应答类型映射：</b>
          <table style='width:100%;font-size:14px;margin-top:10px;border-collapse:separate;border-spacing:0 6px;'>
            <tr style='background:#f7f9fb;'><th style="width:32%;font-weight:500;padding:6px 0;text-align:center;">系统类型</th><th style="font-weight:500;padding:6px 0;text-align:center;">客户描述</th></tr>
            ${['FC', 'PC', 'NC', 'N/A', ''].map(k =>
        `<tr>
                <td style="text-align:center;color:#888;background:#fff;border-radius:6px 0 0 6px;border:1px solid #e5e7eb;border-right:none;">${k || '空'}</td>
                <td style="background:#fff;border-radius:0 6px 6px 0;border:1px solid #e5e7eb;padding:0 8px;">
                  <input type='text' id='map_${k || 'empty'}' value='' placeholder='${answerPlaceholders[k]}' style='width:98%;padding:5px 8px;border:1px solid #d9d9d9;border-radius:4px;font-size:14px;background:#fcfcfc;' autocomplete='off' />
                </td>
              </tr>`
      ).join('')}
          </table>
        </div>
        <div style='margin-bottom:18px;padding:16px 18px 10px 18px;background:#f8fafb;border-radius:10px;border:1px solid #f0f0f0;'>
          <b style='font-size:15px;'>索引格式：</b><br/>
          <input id='indexFormatInput' style='width:100%;margin-top:8px;padding:6px 10px;border:1px solid #d9d9d9;border-radius:4px;font-size:14px;background:#fcfcfc;' value='${indexFormat}' placeholder='$DOC$-$PAGE$' autocomplete='off' />
          <div style='font-size:12px;color:#888;margin-top:6px;line-height:1.6;'>支持变量：$DOC$、$PAGE$、$MAJORCHAPTER$、$SUBCHAPTER$、$YEAR$、$AUTHOR$、$CUSTOM1$~$CUSTOM5$</div>
        </div>
        <div style='text-align:right;margin-top:22px;padding:0 6px 8px 0;'>
          <button id='saveExportSettingBtn' style='background:#52c41a;color:#fff;border:none;border-radius:20px;padding:7px 32px;font-size:15px;cursor:pointer;box-shadow:0 2px 8px #e6f7e6;transition:background .2s;'>保存</button>
          <button id='cancelExportSettingBtn' style='margin-left:12px;background:#fff;color:#666;border:1px solid #d9d9d9;border-radius:20px;padding:7px 28px;font-size:15px;cursor:pointer;transition:background .2s;'>取消</button>
        </div>
      </div>`;
      const dlg = document.createElement('div');
      dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.18);display:flex;align-items:center;justify-content:center;'><div style='background:#fff;padding:0 0 0 0;border-radius:16px;min-width:340px;box-shadow:0 6px 32px #bbb;'>${html}</div></div>`;
      document.body.appendChild(dlg);
      dlg.querySelector('#cancelExportSettingBtn').onclick = function () { document.body.removeChild(dlg); };
      dlg.querySelector('#saveExportSettingBtn').onclick = function () {
        // 保存映射
        ['FC', 'PC', 'NC', 'N/A', ''].forEach(k => {
          const v = dlg.querySelector(`#map_${k || 'empty'}`).value.trim();
          answerMap[k] = v;
        });
        localStorage.setItem('socAnswerMap', JSON.stringify(answerMap));
        // 保存索引格式
        const fmt = dlg.querySelector('#indexFormatInput').value.trim();
        if (fmt.length > 200) return alert('索引格式过长');
        indexFormat = fmt;
        localStorage.setItem('socIndexFormat', indexFormat);
        document.body.removeChild(dlg);
        alert('导出设置已保存');
      };
    };
    // 导出时应用映射和索引格式
    function doExport(typeArr, mode) {
      const all = getAnswerList();
      // 按类型分组
      const group = {};
      typeArr.forEach(t => group[t] = []);
      all.forEach(row => {
        let key = row.answer || '';
        if (!typeArr.includes(key)) return;
        group[key].push(row);
      });
      // 字段顺序：编号、条目描述、应答、应答说明（含图片）、索引
      const fields = ['no', 'desc', 'answer', 'explain', 'index'];
      const headers = ['编号', '条目描述', '应答', '应答说明', '索引'];
      // 文件名规则
      const taskName = 'SOC应答导出';
      const now = new Date();
      const timeStr = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}_${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}`;
      // 应答类型映射
      const answerMap = JSON.parse(localStorage.getItem('socAnswerMap') || '{"FC":"Fully Compliant","PC":"Partially Compliant","NC":"Non-Compliant","N/A":"Not Applicable","":"Not Applicable"}');
      // 索引格式
      const indexFormat = localStorage.getItem('socIndexFormat') || '$DOC$-$PAGE$';
      // 索引格式变量替换
      function formatIndex(row) {
        let fmt = indexFormat;
        const vars = {
          '$DOC$': row.doc || '',
          '$PAGE$': row.page || '',
          '$MAJORCHAPTER$': row.majorChapter || '',
          '$SUBCHAPTER$': row.subChapter || '',
          '$YEAR$': row.year || '',
          '$AUTHOR$': row.author || '',
          '$CUSTOM1$': row.custom1 || '',
          '$CUSTOM2$': row.custom2 || '',
          '$CUSTOM3$': row.custom3 || '',
          '$CUSTOM4$': row.custom4 || '',
          '$CUSTOM5$': row.custom5 || ''
        };
        Object.keys(vars).forEach(k => { fmt = fmt.replaceAll(k, vars[k]); });
        return fmt;
      }
      function rowToArr(row) {
        return [
          row.no || '',
          row.desc || '',
          answerMap[row.answer || ''] || row.answer || '',
          (row.explain || '') + (row.img ? ` [图片:${row.img}]` : ''),
          formatIndex(row),
          row.remark || ''
        ];
      }
      // ...后续导出逻辑保持不变... 
    }
    // ===== 批量/单条重新应答弹窗逻辑 =====
    const batchReAnswerDialogBtn = document.getElementById('batchReAnswerDialogBtn');
    if (batchReAnswerDialogBtn) batchReAnswerDialogBtn.onclick = function () {
      // 获取所有条目和产品（模拟产品）
      const all = getAnswerList();
      const products = ['5GC', 'VoLTE', 'IMS', '核心网', '接入网'];
      let html = `<div style='width:420px;max-width:96vw;'>
        <div style='font-size:18px;font-weight:500;margin-bottom:12px;'>批量/单条重新应答</div>
        <div style='margin-bottom:10px;'><b>请选择应答范围：</b></div>
        <div style='margin-bottom:10px;'><label><input type='radio' name='reType' value='selectedRows' checked/> 选中条目全部产品</label></div>
        <div style='margin-bottom:10px;'><label><input type='radio' name='reType' value='selectedRowsProducts'/> 选中条目的指定产品</label></div>
        <div style='margin-bottom:10px;'><label><input type='radio' name='reType' value='selectedProducts'/> 选中产品的全部条目</label></div>
        <div id='productMultiSel' style='margin-left:24px;display:none;'>
          <b>选择产品：</b><br/>
          ${products.map(p => `<label style='margin-right:8px;'><input type='checkbox' class='reProductChk' value='${p}'/> ${p}</label>`).join('')}
        </div>
        <div style='text-align:right;margin-top:18px;'><button id='doBatchReAnswerBtn' style='background:#fa541c;color:#fff;border:none;border-radius:4px;padding:6px 24px;cursor:pointer;'>确定</button> <button id='cancelBatchReAnswerBtn' style='margin-left:8px;'>取消</button></div>
      </div>`;
      const dlg = document.createElement('div');
      dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.18);display:flex;align-items:center;justify-content:center;'><div style='background:#fff;padding:0 0 0 0;border-radius:12px;min-width:340px;box-shadow:0 4px 24px #aaa;'>${html}</div></div>`;
      document.body.appendChild(dlg);
      // 切换产品多选显示
      dlg.querySelectorAll('input[name=reType]').forEach(radio => {
        radio.onchange = function () {
          dlg.querySelector('#productMultiSel').style.display = radio.value === 'selectedRowsProducts' || radio.value === 'selectedProducts' ? '' : 'none';
        };
      });
      // 取消
      dlg.querySelector('#cancelBatchReAnswerBtn').onclick = function () { document.body.removeChild(dlg); };
      // 确定
      dlg.querySelector('#doBatchReAnswerBtn').onclick = function () {
        const type = dlg.querySelector('input[name=reType]:checked').value;
        let selectedIdxs = Array.from(document.querySelectorAll('.rowCheck')).map((c, i) => c.checked ? i : null).filter(i => i !== null);
        let selectedProducts = Array.from(dlg.querySelectorAll('.reProductChk:checked')).map(c => c.value);
        if (type === 'selectedRows') {
          if (selectedIdxs.length === 0) return alert('请先勾选条目');
          reAnswerRows(selectedIdxs);
        } else if (type === 'selectedRowsProducts') {
          if (selectedIdxs.length === 0) return alert('请先勾选条目');
          if (selectedProducts.length === 0) return alert('请先选择产品');
          // 模拟：对选中条目+产品组合重新应答
          const list = getAnswerList();
          selectedIdxs.forEach(i => {
            list[i].answer = selectedProducts[Math.floor(Math.random() * selectedProducts.length)];
            list[i].explain = 'AI重新应答（条目+产品组合）';
          });
          saveAnswerList(list);
          renderAnswerTable();
          alert('已对选中条目+产品组合重新应答（模拟）');
        } else if (type === 'selectedProducts') {
          if (selectedProducts.length === 0) return alert('请先选择产品');
          // 模拟：对所有条目中属于选中产品的重新应答
          const list = getAnswerList();
          list.forEach((row, i) => {
            if (selectedProducts.includes(row.answer)) {
              row.answer = selectedProducts[Math.floor(Math.random() * selectedProducts.length)];
              row.explain = 'AI重新应答（产品维度）';
            }
          });
          saveAnswerList(list);
          renderAnswerTable();
          alert('已对选中产品的全部条目重新应答（模拟）');
        }
        document.body.removeChild(dlg);
      };
    };
    // ===== 历史记录管理弹窗 =====
    function showHistoryDialog() {
      // 历史数据模拟：直接用本地 socAnswerList 作为历史（可扩展为多任务/归档）
      const all = getAnswerList();
      let html = `<div style='width:600px;max-width:98vw;'>
        <div style='font-size:18px;font-weight:500;margin-bottom:12px;'>历史应答记录</div>
        <div style='margin-bottom:10px;'>
          <label>筛选应答：<select id='historyFilterAnswer'><option value=''>全部</option><option value='FC'>FC</option><option value='PC'>PC</option><option value='NC'>NC</option><option value='N/A'>N/A</option></select></label>
          <input id='historySearchInput' placeholder='搜索条目描述' style='margin-left:12px;width:180px;' />
        </div>
        <table style='width:100%;font-size:14px;background:#fff;border-radius:4px;box-shadow:0 1px 4px #eee;'>
          <thead><tr style='background:#f7f9fb;'><th>编号</th><th>条目描述</th><th>应答</th><th>应答说明</th><th>操作</th></tr></thead>
          <tbody id='historyTableBody'></tbody>
        </table>
        <div style='text-align:right;margin-top:16px;'><button id='closeHistoryBtn'>关闭</button></div>
      </div>`;
      const dlg = document.createElement('div');
      dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.18);display:flex;align-items:center;justify-content:center;'><div style='background:#fff;padding:0 0 0 0;border-radius:12px;min-width:340px;box-shadow:0 4px 24px #aaa;'>${html}</div></div>`;
      document.body.appendChild(dlg);
      // 渲染表格
      function renderHistoryTable() {
        const filter = dlg.querySelector('#historyFilterAnswer').value;
        const kw = dlg.querySelector('#historySearchInput').value.trim();
        let rows = all.filter(row => {
          if (filter && row.answer !== filter) return false;
          if (kw && (!row.desc || row.desc.indexOf(kw) === -1)) return false;
          return true;
        });
        dlg.querySelector('#historyTableBody').innerHTML = rows.map((row, i) =>
          `<tr><td>${row.no || i + 1}</td><td>${row.desc || ''}</td><td>${row.answer || ''}</td><td>${row.explain || ''}</td><td><button data-idx='${i}' class='historyDetailBtn'>详情</button> <button data-idx='${i}' class='historyExportBtn'>导出</button> <button data-idx='${i}' class='historyReuseBtn'>复用文档</button></td></tr>`
        ).join('');
      }
      renderHistoryTable();
      dlg.querySelector('#historyFilterAnswer').onchange = renderHistoryTable;
      dlg.querySelector('#historySearchInput').oninput = renderHistoryTable;
      // 关闭
      dlg.querySelector('#closeHistoryBtn').onclick = function () { document.body.removeChild(dlg); };
      // 操作按钮
      dlg.querySelector('#historyTableBody').onclick = function (e) {
        if (e.target.classList.contains('historyDetailBtn')) {
          showAnswerDetail(e.target.dataset.idx);
        }
        if (e.target.classList.contains('historyExportBtn')) {
          alert('已导出该条历史（模拟）');
        }
        if (e.target.classList.contains('historyReuseBtn')) {
          alert('已复用该条文档（模拟）');
        }
      };
    }

    const historyRecordBtn = document.getElementById('historyRecordBtn');
    if (historyRecordBtn) historyRecordBtn.onclick = function () {
      showHistoryDialog();
    };

    // ===== 角色切换逻辑 =====
    const roleSelect = document.getElementById('roleSelect');
    let socRole = localStorage.getItem('socRole') || 'admin';
    if (roleSelect) {
      roleSelect.value = socRole;
      roleSelect.onchange = function () {
        socRole = roleSelect.value;
        localStorage.setItem('socRole', socRole);
        applyRolePermission();
      };
    }
    function applyRolePermission() {
      // 模拟：不同角色可见产品、数据范围不同
      const productOptions = [
        { v: '5GC', t: '工程' },
        { v: 'VoLTE', t: '售前' },
        { v: 'IMS', t: '售前' },
        { v: '核心网', t: '工程' },
        { v: '接入网', t: '工程' }
      ];
      let allowed = [];
      if (socRole === 'admin') allowed = productOptions;
      if (socRole === 'tech') allowed = productOptions.filter(o => o.t === '售前');
      if (socRole === 'delivery') allowed = productOptions.filter(o => o.t === '工程');
      // 主参数区
      const productSelect = document.getElementById('productSelect');
      if (productSelect) {
        productSelect.innerHTML = allowed.map(o => `<option value="${o.v}">${o.v}</option>`).join('');
      }
      // 弹窗参数区
      const productSelectDialog = document.getElementById('productSelectDialog');
      if (productSelectDialog) {
        productSelectDialog.innerHTML = allowed.map(o => `<option value="${o.v}">${o.v}</option>`).join('');
      }
      // 内容区表格数据过滤（仅模拟，实际应后端过滤）
      renderAnswerTable();
    }
    // 初始化演示数据（多产品结构）
    if (getAnswerList().length === 0) {
      const demoData = [
        {
          no: 1,
          desc: '系统支持多语言',
          products: ['5GC', 'VoLTE', 'IMS'],
          productData: {
            '5GC': {
              answer: 'FC',
              explain: '5GC系统完全支持中英文双语切换，用户界面、系统提示、错误信息等均可根据用户偏好进行语言设置。',
              supplement: '支持动态语言切换，无需重启系统',
              remark: '重要功能',
              index: '5GC技术白皮书-3.2.1',
              source: '文档库'
            },
            'VoLTE': {
              answer: 'PC',
              explain: 'VoLTE系统支持中文和英文两种语言，但部分高级功能提示信息仍为英文。',
              supplement: '基础功能支持，高级功能待完善',
              remark: '需优化',
              index: 'VoLTE解决方案-2.1.3',
              source: '文档库'
            },
            'IMS': {
              answer: 'FC',
              explain: 'IMS系统完全支持多语言，包括中文、英文、日文等多种语言。',
              supplement: '多语言支持完善',
              remark: '功能完整',
              index: 'IMS技术规范-4.1.2',
              source: '文档库'
            }
          },
          img: 'https://via.placeholder.com/80x40?text=多语言'
        },
        {
          no: 2,
          desc: '支持5G网络切片',
          products: ['5GC', 'VoLTE', 'IMS'],
          productData: {
            '5GC': {
              answer: 'FC',
              explain: '5GC系统完全支持5G网络切片技术，可为不同业务场景提供独立的网络资源。',
              supplement: '支持端到端切片管理',
              remark: '核心功能',
              index: '5GC切片白皮书-2.3.1',
              source: '文档库'
            },
            'VoLTE': {
              answer: 'N/A',
              explain: 'VoLTE系统不涉及5G网络切片技术。',
              supplement: '4G技术不包含切片',
              remark: '不适用',
              index: 'VoLTE技术规范-1.0.0',
              source: '文档库'
            },
            'IMS': {
              answer: 'PC',
              explain: 'IMS系统部分支持网络切片概念，但实现方式与5G切片不同。',
              supplement: '基于IMS的切片实现',
              remark: '部分支持',
              index: 'IMS切片方案-3.1.5',
              source: '文档库'
            }
          },
          img: 'https://via.placeholder.com/80x40?text=网络切片'
        },
        {
          no: 3,
          desc: '保障系统安全性',
          products: ['5GC', 'VoLTE', 'IMS'],
          productData: {
            '5GC': {
              answer: 'FC',
              explain: '5GC系统采用多层次安全防护，包括接入安全、传输安全、数据安全等。',
              supplement: '符合3GPP安全标准',
              remark: '安全等级高',
              index: '5GC安全规范-5.2.1',
              source: '文档库'
            },
            'VoLTE': {
              answer: 'FC',
              explain: 'VoLTE系统具备完善的安全机制，支持加密通信和身份认证。',
              supplement: '基于IMS安全架构',
              remark: '安全可靠',
              index: 'VoLTE安全指南-2.4.3',
              source: '文档库'
            },
            'IMS': {
              answer: 'FC',
              explain: 'IMS系统提供全面的安全保护，包括SIP安全、媒体安全等。',
              supplement: '端到端安全防护',
              remark: '安全体系完善',
              index: 'IMS安全标准-4.2.1',
              source: '文档库'
            }
          },
          img: 'https://via.placeholder.com/80x40?text=安全'
        },
        {
          no: '1.1',
          desc: '支持自定义词库',
          products: ['5GC', 'IMS'],
          productData: {
            '5GC': {
              answer: 'FC',
              explain: '5GC系统提供完整的自定义词库功能，支持用户导入、编辑、管理专业术语词典。',
              supplement: '支持批量导入，最大容量10万词条',
              remark: '核心功能',
              index: '5GC技术白皮书-4.1.2',
              source: '文档库'
            },
            'IMS': {
              answer: 'NC',
              explain: 'IMS系统暂不支持自定义词库功能，使用系统预设的标准化术语。',
              supplement: '计划在下一版本中支持',
              remark: '待开发',
              index: 'IMS部署指南-1.3.5',
              source: '文档库'
            }
          },
          img: 'https://via.placeholder.com/80x40?text=词库'
        },
        {
          no: '1.2',
          desc: '是否支持批量导入？',
          products: ['5GC', 'VoLTE', '核心网'],
          productData: {
            '5GC': {
              answer: 'FC',
              explain: '5GC系统完全支持批量导入功能，支持Excel、CSV等多种格式，单次可导入10万条记录。',
              supplement: '提供导入模板和校验功能',
              remark: '高效工具',
              index: '5GC技术白皮书-5.2.1',
              source: '文档库'
            },
            'VoLTE': {
              answer: 'PC',
              explain: 'VoLTE系统支持批量导入，但格式要求较严格，单次最多导入5万条记录。',
              supplement: '需要严格按照模板格式',
              remark: '需注意格式',
              index: 'VoLTE解决方案-3.1.4',
              source: '文档库'
            },
            '核心网': {
              answer: 'FC',
              explain: '核心网系统完全支持批量导入，支持多种数据格式，并提供数据校验和回滚功能。',
              supplement: '支持增量导入和全量导入',
              remark: '功能完善',
              index: '核心网技术规范-2.3.1',
              source: '文档库'
            }
          },
          img: 'https://via.placeholder.com/80x40?text=批量导入'
        },
        {
          no: '2.1',
          desc: '系统安全性如何？',
          products: ['5GC', '接入网'],
          productData: {
            '5GC': {
              answer: 'FC',
              explain: '5GC系统采用多层安全架构，包括身份认证、数据加密、访问控制等，符合国际安全标准。',
              supplement: '通过ISO27001认证',
              remark: '安全可靠',
              index: '5GC安全白皮书-1.1.1',
              source: '文档库'
            },
            '接入网': {
              answer: 'PC',
              explain: '接入网系统具备基础安全防护能力，但在高级安全功能方面还需加强。',
              supplement: '基础安全功能完善',
              remark: '需加强',
              index: '接入网安全规范-2.1.2',
              source: '文档库'
            }
          },
          img: 'https://via.placeholder.com/80x40?text=安全性'
        },
        {
          no: '2.2',
          desc: '是否支持云部署？',
          products: ['5GC', 'IMS', '核心网'],
          productData: {
            '5GC': {
              answer: 'FC',
              explain: '5GC系统完全支持云部署，支持公有云、私有云、混合云等多种部署模式。',
              supplement: '支持容器化部署',
              remark: '云原生',
              index: '5GC云部署指南-1.1.1',
              source: '文档库'
            },
            'IMS': {
              answer: 'FC',
              explain: 'IMS系统支持云部署，提供完整的云原生解决方案。',
              supplement: '支持微服务架构',
              remark: '云就绪',
              index: 'IMS云化方案-2.1.1',
              source: '文档库'
            },
            '核心网': {
              answer: 'PC',
              explain: '核心网系统支持部分云部署功能，但某些核心组件仍需传统部署。',
              supplement: '混合部署模式',
              remark: '逐步云化',
              index: '核心网云化策略-3.1.1',
              source: '文档库'
            }
          },
          img: 'https://via.placeholder.com/80x40?text=云部署'
        },
        {
          no: '2.2.1',
          desc: '支持云平台',
          products: ['5GC', 'IMS'],
          productData: {
            '5GC': {
              answer: 'FC',
              explain: '5GC系统支持AWS、Azure、阿里云等主流云平台。',
              supplement: '支持多云部署',
              remark: '平台兼容性好',
              index: '5GC云平台支持-1.1.1',
              source: '文档库'
            },
            'IMS': {
              answer: 'PC',
              explain: 'IMS系统支持AWS和Azure，阿里云支持正在开发中。',
              supplement: '部分云平台支持',
              remark: '待完善',
              index: 'IMS云平台兼容-2.1.1',
              source: '文档库'
            }
          },
          img: 'https://via.placeholder.com/80x40?text=云平台'
        },
        {
          no: '2.2.2',
          desc: '是否支持容器化部署？',
          products: ['5GC', 'IMS'],
          productData: {
            '5GC': {
              answer: 'FC',
              explain: '5GC系统完全支持Docker和Kubernetes容器化部署。',
              supplement: '云原生架构',
              remark: '现代化部署',
              index: '5GC容器化指南-1.2.1',
              source: '文档库'
            },
            'IMS': {
              answer: 'FC',
              explain: 'IMS系统支持容器化部署，提供完整的K8s部署方案。',
              supplement: '微服务架构',
              remark: '容器就绪',
              index: 'IMS容器化方案-2.2.1',
              source: '文档库'
            }
          },
          img: 'https://via.placeholder.com/80x40?text=容器化'
        }
      ];
      saveAnswerList(demoData);
    }

    applyRolePermission();
    const taskManageBtn = document.getElementById('taskManageBtn');
    if (taskManageBtn) taskManageBtn.onclick = function () {
      // 任务数据结构：本地存储 socTaskList，每个任务含id、name、status、percent、answerList
      let taskList = JSON.parse(localStorage.getItem('socTaskList') || '[]');
      let currentTaskId = localStorage.getItem('socCurrentTaskId') || '';
      function saveTaskList(list) { localStorage.setItem('socTaskList', JSON.stringify(list)); }
      function setCurrentTask(id) { localStorage.setItem('socCurrentTaskId', id); }
      let html = `<div style='width:600px;max-width:98vw;'>
        <div style='font-size:18px;font-weight:500;margin-bottom:12px;'>任务管理</div>
        <div style='margin-bottom:10px;'>
          <button id='createTaskBtn' style='background:#52c41a;color:#fff;border:none;border-radius:4px;padding:2px 16px;'>新建任务</button>
        </div>
        <table style='width:100%;font-size:14px;background:#fff;border-radius:4px;box-shadow:0 1px 4px #eee;'>
          <thead><tr style='background:#f7f9fb;'><th>任务名</th><th>状态</th><th>进度</th><th>操作</th></tr></thead>
          <tbody id='taskTableBody'></tbody>
        </table>
        <div style='text-align:right;margin-top:16px;'><button id='closeTaskBtn'>关闭</button></div>
      </div>`;
      const dlg = document.createElement('div');
      dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.18);display:flex;align-items:center;justify-content:center;'><div style='background:#fff;padding:0 0 0 0;border-radius:12px;min-width:340px;box-shadow:0 4px 24px #aaa;'>${html}</div></div>`;
      document.body.appendChild(dlg);
      // 渲染表格
      function renderTaskTable() {
        dlg.querySelector('#taskTableBody').innerHTML = taskList.map((t, i) =>
          `<tr${t.id === currentTaskId ? ' style="background:#e6f7ff;"' : ''}><td>${t.name}</td><td>${t.status || '进行中'}</td><td>${t.percent || '0%'}</td><td><button data-idx='${i}' class='taskSwitchBtn'>切换</button> <button data-idx='${i}' class='taskArchiveBtn'>归档</button> <button data-idx='${i}' class='taskDelBtn'>删除</button></td></tr>`
        ).join('');
      }
      renderTaskTable();
      // 新建任务
      dlg.querySelector('#createTaskBtn').onclick = function () {
        const name = prompt('请输入任务名称');
        if (!name) return;
        const id = 'task' + Date.now();
        taskList.push({ id, name, status: '进行中', percent: '0%', answerList: [] });
        saveTaskList(taskList);
        renderTaskTable();
      };
      // 关闭
      dlg.querySelector('#closeTaskBtn').onclick = function () { document.body.removeChild(dlg); };
      // 操作按钮
      dlg.querySelector('#taskTableBody').onclick = function (e) {
        if (e.target.classList.contains('taskSwitchBtn')) {
          const t = taskList[e.target.dataset.idx];
          setCurrentTask(t.id);
          alert('已切换到任务：' + t.name + '（模拟，需刷新页面数据）');
          document.body.removeChild(dlg);
        }
        if (e.target.classList.contains('taskArchiveBtn')) {
          taskList[e.target.dataset.idx].status = '已归档';
          saveTaskList(taskList);
          renderTaskTable();
        }
        if (e.target.classList.contains('taskDelBtn')) {
          if (confirm('确定删除该任务？')) {
            taskList.splice(e.target.dataset.idx, 1);
            saveTaskList(taskList);
            renderTaskTable();
          }
        }
      };
    };

    // ===== 索引相关功能 =====
    // 解析索引为超链接
    function parseIndexToLinks(indexStr) {
      if (!indexStr) return '暂无索引';
      // 格式：文件名-章节号，如"5GC云部署指南-1.1.1"
      const parts = indexStr.split('-');
      if (parts.length < 2) return indexStr;

      const fileName = parts[0];
      const chapter = parts[1];
      const page = parts[2] || '';

      // 判断文档权限和预览链接
      const isUserUpload = fileName.includes('用户上传');
      const previewUrl = isUserUpload ?
        `javascript:previewDocument('${fileName}', 'user')` :
        `javascript:previewDocument('${fileName}', 'library')`;

      return `<a href="${previewUrl}" style="color:#1765d5;text-decoration:none;border-bottom:1px dotted #1765d5;" title="点击预览文档">${fileName}</a>-${chapter}${page ? '-' + page : ''}`;
    }

    // 解析索引中的文件名
    function parseIndexFileName(indexStr) {
      if (!indexStr) return '';
      const parts = indexStr.split('-');
      return parts[0] || '';
    }

    // 解析索引中的章节号
    function parseIndexChapter(indexStr) {
      if (!indexStr) return '';
      const parts = indexStr.split('-');
      return parts[1] || '';
    }

    // 解析索引中的页码
    function parseIndexPage(indexStr) {
      if (!indexStr) return '';
      const parts = indexStr.split('-');
      return parts[2] || '';
    }

    // 预览文档
    function previewDocument(fileName, type) {
      if (type === 'user') {
        // 用户上传文档，直接预览
        alert(`预览用户上传文档：${fileName}`);
      } else {
        // 文档库文档，需要权限判断
        const hasPermission = checkDocumentPermission(fileName);
        if (hasPermission) {
          alert(`预览文档库文档：${fileName}`);
        } else {
          alert(`无权限预览文档：${fileName}，请联系管理员`);
        }
      }
    }

    // 检查文档权限
    function checkDocumentPermission(fileName) {
      // 模拟权限检查逻辑
      const userPermissions = JSON.parse(localStorage.getItem('userDocumentPermissions') || '[]');
      return userPermissions.includes(fileName) || fileName.includes('公开');
    }

    // 获取已关联文档列表
    function getAssociatedDocuments() {
      return [
        '5GC云部署指南',
        'IMS云化方案',
        '核心网云化策略',
        '用户上传-技术规范V1.0',
        '用户上传-部署手册',
        'TSM系统文档-公开版',
        'GBBS系统手册'
      ];
    }

    // 显示文档选择弹窗
    function showDocumentSelector(callback) {
      const docs = getAssociatedDocuments();
      let html = `<div style='width:500px;max-width:98vw;'>
        <div style='font-size:16px;font-weight:500;margin-bottom:12px;'>选择文档</div>
        <div style='margin-bottom:12px;'>
          <input id='docSearchInput' style='width:100%;padding:6px 8px;border-radius:4px;border:1px solid #d9d9d9;' placeholder='搜索文档名称...' />
        </div>
        <div style='max-height:300px;overflow:auto;border:1px solid #e9ecef;border-radius:4px;'>`;

      docs.forEach(doc => {
        const isUserUpload = doc.includes('用户上传');
        const icon = isUserUpload ? '📁' : '📄';
        html += `<div class='doc-item' data-doc='${doc}' style='padding:8px 12px;cursor:pointer;border-bottom:1px solid #f0f0f0;display:flex;align-items:center;gap:8px;'>
          <span>${icon}</span>
          <span style='flex:1;'>${doc}</span>
          <span style='font-size:12px;color:#666;'>${isUserUpload ? '用户上传' : '文档库'}</span>
        </div>`;
      });

      html += `</div>
        <div style='text-align:right;margin-top:16px;'>
          <button id='cancelDocSelectBtn' style='background:#f5f5f5;color:#666;border:none;border-radius:4px;padding:6px 16px;margin-right:8px;cursor:pointer;'>取消</button>
          <button id='confirmDocSelectBtn' style='background:#1765d5;color:#fff;border:none;border-radius:4px;padding:6px 16px;cursor:pointer;'>确定</button>
        </div>
      </div>`;

      const dlg = document.createElement('div');
      dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.18);display:flex;align-items:center;justify-content:center;'><div style='background:#fff;padding:20px;border-radius:12px;box-shadow:0 4px 24px #aaa;'>${html}</div></div>`;
      document.body.appendChild(dlg);

      // 搜索功能
      const searchInput = dlg.querySelector('#docSearchInput');
      const docItems = dlg.querySelectorAll('.doc-item');
      searchInput.oninput = function () {
        const keyword = this.value.toLowerCase();
        docItems.forEach(item => {
          const docName = item.dataset.doc.toLowerCase();
          item.style.display = docName.includes(keyword) ? 'flex' : 'none';
        });
      };

      // 选择文档
      docItems.forEach(item => {
        item.onclick = function () {
          docItems.forEach(i => i.style.background = '');
          this.style.background = '#e6f7ff';
          dlg.selectedDoc = this.dataset.doc;
        };
      });

      // 确定选择
      dlg.querySelector('#confirmDocSelectBtn').onclick = function () {
        if (dlg.selectedDoc && callback) {
          callback(dlg.selectedDoc);
        }
        document.body.removeChild(dlg);
      };

      // 取消
      dlg.querySelector('#cancelDocSelectBtn').onclick = function () {
        document.body.removeChild(dlg);
      };
    }

    // 绑定索引编辑事件
    function bindIndexEvents() {
      const editBtn = document.getElementById('editIndexBtn');
      const addBtn = document.getElementById('addIndexBtn');
      const saveBtn = document.getElementById('saveIndexBtn');
      const cancelBtn = document.getElementById('cancelIndexBtn');
      const selectDocBtn = document.getElementById('selectDocBtn');

      if (editBtn) {
        editBtn.onclick = function () {
          document.getElementById('indexView').style.display = 'none';
          document.getElementById('indexEditWrap').style.display = 'block';
          this.style.display = 'none';
          addBtn.style.display = 'none';
        };
      }

      if (addBtn) {
        addBtn.onclick = function () {
          document.getElementById('indexView').style.display = 'none';
          document.getElementById('indexEditWrap').style.display = 'block';
          // 清空输入框
          document.getElementById('indexFileName').value = '';
          document.getElementById('indexChapter').value = '';
          document.getElementById('indexPage').value = '';
          this.style.display = 'none';
          editBtn.style.display = 'none';
        };
      }

      if (saveBtn) {
        saveBtn.onclick = function () {
          const fileName = document.getElementById('indexFileName').value.trim();
          const chapter = document.getElementById('indexChapter').value.trim();
          const page = document.getElementById('indexPage').value.trim();

          if (!fileName) {
            alert('请输入文件名');
            return;
          }

          if (!chapter) {
            alert('请输入章节号');
            return;
          }

          // 构建索引字符串
          let indexStr = fileName + '-' + chapter;
          if (page) indexStr += '-' + page;

          // 更新显示
          document.getElementById('indexView').innerHTML = parseIndexToLinks(indexStr);
          document.getElementById('indexView').style.display = 'block';
          document.getElementById('indexEditWrap').style.display = 'none';
          editIndexBtn.style.display = 'inline-block';
          addIndexBtn.style.display = 'inline-block';

          // 更新数据（这里需要根据实际数据结构更新）
          if (window._currentDetailData) {
            window._currentDetailData.index = indexStr;
          }
        };
      }

      if (cancelBtn) {
        cancelBtn.onclick = function () {
          document.getElementById('indexView').style.display = 'block';
          document.getElementById('indexEditWrap').style.display = 'none';
          editIndexBtn.style.display = 'inline-block';
          addIndexBtn.style.display = 'inline-block';
        };
      }

      if (selectDocBtn) {
        selectDocBtn.onclick = function () {
          showDocumentSelector(function (selectedDoc) {
            document.getElementById('indexFileName').value = selectedDoc;
          });
        };
      }
    }

    // ===== 埋点日志功能 =====
    function logEvent(event, detail) {
      let logs = JSON.parse(localStorage.getItem('socEventLogs') || '[]');
      logs.push({ time: new Date().toLocaleString(), event, detail });
      localStorage.setItem('socEventLogs', JSON.stringify(logs));
    }
    // 关键行为埋点
    [
      ['addSingleEntryBtn', 'click', '新增应答条目'],
      ['importExcelBtn', 'click', '批量导入Excel'],
      ['exportBtn', 'click', '导出'],
      ['batchReAnswerBtn', 'click', '全部重新应答'],
      ['batchReAnswerDialogBtn', 'click', '批量/单条重新应答'],
      ['historyRecordBtn', 'click', '历史记录'],
      ['taskManageBtn', 'click', '任务管理'],
      ['openParamSettingBtn', 'click', '参数设置'],
      ['openReferenceDocBtn', 'click', '参考文档'],
      ['exportSettingBtn', 'click', '导出设置']
    ].forEach(([id, evt, desc]) => {
      const el = document.getElementById(id);
      if (el) el.addEventListener(evt, () => logEvent(desc, ''));
    });
    // 详情弹窗埋点
    window.showAnswerDetail = (function (orig) {
      return function (idx) {
        logEvent('查看详情', 'idx=' + idx);
        return orig(idx);
      }
    })(window.showAnswerDetail || showAnswerDetail);
    // LUI指令埋点
    if (luiForm) luiForm.addEventListener('submit', () => logEvent('LUI指令', luiInput.value));
    // 埋点日志查看入口
    const navbar = document.querySelector('.soc-navbar-user');
    if (navbar) {
      const btn = document.createElement('button');
      btn.textContent = '埋点日志';
      btn.style.marginLeft = '16px';
      btn.style.background = '#fff1b8';
      btn.style.color = '#ad6800';
      btn.style.border = '1px solid #ffe58f';
      btn.style.borderRadius = '4px';
      btn.style.padding = '2px 12px';
      btn.style.cursor = 'pointer';
      btn.onclick = function () {
        let logs = JSON.parse(localStorage.getItem('socEventLogs') || '[]');
        let html = `<div style='width:520px;max-width:98vw;'><div style='font-size:18px;font-weight:500;margin-bottom:12px;'>埋点日志</div><div style='max-height:320px;overflow:auto;font-size:13px;'>`;
        if (logs.length === 0) html += '<div style="color:#aaa;">暂无埋点</div>';
        else html += logs.map(l => `<div>[${l.time}] ${l.event} ${l.detail ? ('(' + l.detail + ')') : ''}</div>`).reverse().join('');
        html += '</div><div style="text-align:right;margin-top:16px;"><button id="closeLogBtn">关闭</button></div></div>';
        const dlg = document.createElement('div');
        dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.18);display:flex;align-items:center;justify-content:center;'><div style='background:#fff;padding:0 0 0 0;border-radius:12px;min-width:340px;box-shadow:0 4px 24px #aaa;'>${html}</div></div>`;
        document.body.appendChild(dlg);
        const closeBtn = dlg.querySelector('#closeLogBtn');
        if (closeBtn) closeBtn.onclick = function () { document.body.removeChild(dlg); };
      };
      navbar.appendChild(btn);
    }

    // ===== 主操作按钮与二级菜单逻辑 =====
    const mainActionBtn = document.getElementById('mainActionBtn');
    const mainActionMenu = document.getElementById('mainActionMenu');
    if (mainActionBtn && mainActionMenu) {
      mainActionBtn.onclick = function (e) {
        e.stopPropagation();
        mainActionMenu.style.display = mainActionMenu.style.display === 'none' ? 'block' : 'none';
      };
      document.body.addEventListener('click', function (e) {
        // 如果点击在菜单或按钮内，不关闭
        if (mainActionMenu.contains(e.target) || mainActionBtn.contains(e.target)) return;
        mainActionMenu.style.display = 'none';
      });
      mainActionMenu.onclick = function (e) {
        console.log(e.target);
        if (!e.target.classList.contains('main-action-item')) return;
        e.stopPropagation(); // 阻止冒泡，防止body提前关闭菜单
        const act = e.target.dataset.act;
        if (act === 'addSingleEntry') {
          const btn = document.getElementById('addSingleEntryBtn');
          if (btn) btn.click();
        }
        if (act === 'importExcel') {
          const btn = document.getElementById('importExcelBtn');
          if (btn) btn.click();
        }
        if (act === 'export') {
          const btn = document.getElementById('exportBtn');
          if (btn) btn.click();
        }
        if (act === 'batchReAnswer') {
          const btn = document.getElementById('batchReAnswerBtn');
          if (btn) btn.click();
        }
        if (act === 'batchReAnswerDialog') {
          const btn = document.getElementById('batchReAnswerDialogBtn');
          if (btn) btn.click();
        }
        if (act === 'exportSetting') {
          const btn = document.getElementById('exportSettingBtn');
          if (btn) btn.click();
        }
        if (act === 'openParamSetting') {
          const btn = document.getElementById('openParamSettingBtn');
          if (btn) btn.click();
        }
        if (act === 'openReferenceDoc') {
          const btn = document.getElementById('openReferenceDocBtn');
          if (btn) btn.click();
        }
        if (act === 'priorityConfig') {
          showPriorityConfigDialog();
        }
        if (act === 'satisfactionCalc') {
          calculateSatisfaction();
        }
        if (act === 'viewSimilar') {
          showSimilarItems();
        }
        if (act === 'historyRecord') {
          showHistoryDialog();
        }
        if (act === 'clearAll') {
          confirmClearAllItems();
        }
        mainActionMenu.style.display = 'none';
      };
    }
    // 所有主操作区按钮事件绑定已在前文加空值保护，无需重复声明和绑定，避免变量重复。

    // ... existing code ...
    // 统一所有条目的产品为['5GC','VoLTE','IMS']
    (function unifyAllRowsProducts() {
      const list = getAnswerList();
      const unifiedProducts = ['5GC', 'VoLTE', 'IMS'];
      list.forEach(row => {
        row.products = unifiedProducts.slice();
        // 补全productData
        if (!row.productData) row.productData = {};
        unifiedProducts.forEach(p => {
          if (!row.productData[p]) {
            row.productData[p] = {
              answer: '',
              explain: '',
              supplement: '',
              remark: '',
              index: '',
              source: ''
            };
          }
        });
        // 移除多余的productData
        Object.keys(row.productData).forEach(p => {
          if (!unifiedProducts.includes(p)) delete row.productData[p];
        });
      });
      saveAnswerList(list);
      renderAnswerTable();
    })();
    // ... existing code ...

    if (openParamMaterialBtn) openParamMaterialBtn.onclick = showParamMaterialDialog;
    const openParamSettingBtn = document.getElementById('openParamSettingBtn');
    if (openParamSettingBtn) openParamSettingBtn.onclick = function (e) {
      e.stopPropagation();
      showParamSettingDialog && showParamSettingDialog();
    };
    const openReferenceDocBtn = document.getElementById('openReferenceDocBtn');
    if (openReferenceDocBtn) openReferenceDocBtn.onclick = function (e) {
      e.stopPropagation();
      showReferenceDocDialog && showReferenceDocDialog();
    };

    // === 优先级规则配置功能 ===
    function showPriorityConfigDialog() {
      // 获取当前优先级配置
      const currentPriority = JSON.parse(localStorage.getItem('priorityConfig') || '["项目文档", "文档库选定文档", "文档库全量文档", "上传历史应标文档", "GBBS系统数据"]');

      let html = `<div style='width:620px;max-width:96vw;'>
        <div style='font-size:18px;font-weight:500;margin-bottom:12px;'>🔧 优先级规则配置</div>
        <div style='padding:18px 8px 8px 8px;'>
          <div style='margin-bottom:16px;color:#666;font-size:14px;'>
            通过拖拽调整数据源的优先级顺序，优先级高的数据源在应答推荐时优先被引用。
          </div>
          <div style='border:1px solid #d9d9d9;border-radius:6px;padding:12px;background:#fafafa;'>
            <div style='font-weight:500;margin-bottom:8px;'>数据源优先级（拖拽调整顺序）：</div>
            <div id='priorityList' style='display:flex;flex-direction:column;gap:6px;'>
              ${currentPriority.map((item, index) => `
                <div class='priority-item' data-source='${item}' style='background:#fff;border:1px solid #d9d9d9;border-radius:4px;padding:10px 12px;cursor:move;display:flex;align-items:center;gap:8px;transition:all 0.2s;'>
                  <span style='font-size:16px;color:#666;'>≡</span>
                  <span style='font-weight:500;color:#1765d5;'>${index + 1}.</span>
                  <span style='flex:1;'>${item}</span>
                  <span style='font-size:12px;color:#999;'>${index === 0 ? '最高优先级' : index === currentPriority.length - 1 ? '最低优先级' : ''}</span>
                </div>
              `).join('')}
            </div>
          </div>
          <div style='margin-top:16px;color:#666;font-size:13px;'>
            💡 应答生成时，系统会按照此优先级顺序融合多来源结果。如果高优先级数据源无法回答，将自动使用次优先级数据源的结果。
          </div>
          <div style='margin-top:16px;display:flex;gap:8px;'>
            <button id='savePriorityTemplate' style='background:#52c41a;color:#fff;border:none;border-radius:4px;padding:6px 12px;cursor:pointer;font-size:13px;'>保存为模板</button>
            <button id='loadPriorityTemplate' style='background:#1890ff;color:#fff;border:none;border-radius:4px;padding:6px 12px;cursor:pointer;font-size:13px;'>加载模板</button>
            <button id='resetPriority' style='background:#faad14;color:#fff;border:none;border-radius:4px;padding:6px 12px;cursor:pointer;font-size:13px;'>重置默认</button>
          </div>
        </div>
        <div style='text-align:right;margin-top:18px;display:flex;justify-content:flex-end;gap:12px;'>
          <button id='confirmPriorityDlg' style='background:#1765d5;color:#fff;border:none;border-radius:4px;padding:8px 24px;cursor:pointer;font-size:14px;'>确定</button>
          <button id='cancelPriorityDlg' style='background:#f5f5f5;color:#666;border:1px solid #d9d9d9;border-radius:4px;padding:8px 24px;cursor:pointer;font-size:14px;'>取消</button>
        </div>
      </div>`;

      const dlg = document.createElement('div');
      dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.18);display:flex;align-items:center;justify-content:center;'><div style='background:#fff;padding:0 0 0 0;border-radius:12px;min-width:340px;box-shadow:0 4px 24px #aaa;'>${html}</div></div>`;
      document.body.appendChild(dlg);

      // 实现拖拽功能
      const priorityList = dlg.querySelector('#priorityList');
      let draggedElement = null;

      priorityList.addEventListener('dragstart', function (e) {
        if (e.target.classList.contains('priority-item')) {
          draggedElement = e.target;
          e.target.style.opacity = '0.5';
        }
      });

      priorityList.addEventListener('dragend', function (e) {
        if (e.target.classList.contains('priority-item')) {
          e.target.style.opacity = '';
          draggedElement = null;
        }
      });

      priorityList.addEventListener('dragover', function (e) {
        e.preventDefault();
      });

      priorityList.addEventListener('drop', function (e) {
        e.preventDefault();
        if (draggedElement && e.target.classList.contains('priority-item')) {
          const afterElement = getDragAfterElement(priorityList, e.clientY);
          if (afterElement == null) {
            priorityList.appendChild(draggedElement);
          } else {
            priorityList.insertBefore(draggedElement, afterElement);
          }
          updatePriorityNumbers();
        }
      });

      // 为所有优先级项添加draggable属性
      dlg.querySelectorAll('.priority-item').forEach(item => {
        item.draggable = true;
        item.addEventListener('dragover', function (e) {
          e.preventDefault();
        });
      });

      function getDragAfterElement(container, y) {
        const draggableElements = [...container.querySelectorAll('.priority-item:not(.dragging)')];
        return draggableElements.reduce((closest, child) => {
          const box = child.getBoundingClientRect();
          const offset = y - box.top - box.height / 2;
          if (offset < 0 && offset > closest.offset) {
            return { offset: offset, element: child };
          } else {
            return closest;
          }
        }, { offset: Number.NEGATIVE_INFINITY }).element;
      }

      function updatePriorityNumbers() {
        const items = dlg.querySelectorAll('.priority-item');
        items.forEach((item, index) => {
          const numberSpan = item.querySelector('span:nth-child(2)');
          const statusSpan = item.querySelector('span:nth-child(4)');
          numberSpan.textContent = (index + 1) + '.';
          if (index === 0) {
            statusSpan.textContent = '最高优先级';
          } else if (index === items.length - 1) {
            statusSpan.textContent = '最低优先级';
          } else {
            statusSpan.textContent = '';
          }
        });
      }

      // 按钮事件处理
      dlg.querySelector('#cancelPriorityDlg').onclick = function () {
        document.body.removeChild(dlg);
      };

      dlg.querySelector('#confirmPriorityDlg').onclick = function () {
        const items = dlg.querySelectorAll('.priority-item');
        const newPriority = Array.from(items).map(item => item.dataset.source);
        localStorage.setItem('priorityConfig', JSON.stringify(newPriority));
        addLuiHistory('system', '优先级规则配置已保存。新的优先级顺序：' + newPriority.map((item, index) => (index + 1) + '. ' + item).join('；'));
        renderLuiHistory();
        document.body.removeChild(dlg);
      };

      dlg.querySelector('#resetPriority').onclick = function () {
        const defaultPriority = ["项目文档", "文档库选定文档", "文档库全量文档", "上传历史应标文档", "GBBS系统数据"];
        localStorage.setItem('priorityConfig', JSON.stringify(defaultPriority));
        document.body.removeChild(dlg);
        showPriorityConfigDialog(); // 重新打开对话框显示重置后的配置
      };

      dlg.querySelector('#savePriorityTemplate').onclick = function () {
        const items = dlg.querySelectorAll('.priority-item');
        const currentPriority = Array.from(items).map(item => item.dataset.source);
        const templateName = prompt('请输入模板名称：');
        if (templateName) {
          const templates = JSON.parse(localStorage.getItem('priorityTemplates') || '{}');
          templates[templateName] = currentPriority;
          localStorage.setItem('priorityTemplates', JSON.stringify(templates));
          addLuiHistory('system', `优先级模板"${templateName}"已保存`);
          renderLuiHistory();
        }
      };

      dlg.querySelector('#loadPriorityTemplate').onclick = function () {
        const templates = JSON.parse(localStorage.getItem('priorityTemplates') || '{}');
        const templateNames = Object.keys(templates);
        if (templateNames.length === 0) {
          alert('暂无保存的优先级模板');
          return;
        }

        const templateName = prompt('请选择要加载的模板：\n' + templateNames.map((name, index) => (index + 1) + '. ' + name).join('\n'));
        if (templateName && templates[templateName]) {
          localStorage.setItem('priorityConfig', JSON.stringify(templates[templateName]));
          document.body.removeChild(dlg);
          showPriorityConfigDialog(); // 重新打开对话框显示加载后的配置
        }
      };
    }

    // === 删除功能实现 ===
    // 删除单个条目的确认对话框
    function confirmDeleteItem(idx) {
      const list = getAnswerList();
      const item = list[idx];
      if (!item) return;

      const html = `<div style='width:420px;max-width:96vw;'>
        <div style='font-size:18px;font-weight:500;margin-bottom:12px;color:#ff4d4f;'>⚠️ 确认删除</div>
        <div style='padding:16px;background:#fff2f0;border:1px solid #ffccc7;border-radius:6px;margin-bottom:16px;'>
          <div style='font-weight:500;margin-bottom:8px;'>即将删除以下条目：</div>
          <div style='color:#666;'>编号：${item.no || idx + 1}</div>
          <div style='color:#666;margin-top:4px;'>描述：${item.desc || '无描述'}</div>
        </div>
        <div style='color:#999;font-size:13px;margin-bottom:16px;'>
          删除后将无法恢复，请确认是否继续？
        </div>
        <div style='text-align:right;display:flex;justify-content:flex-end;gap:12px;'>
          <button id='confirmDeleteItem' style='background:#ff4d4f;color:#fff;border:none;border-radius:4px;padding:8px 24px;cursor:pointer;'>确认删除</button>
          <button id='cancelDeleteItem' style='background:#f5f5f5;color:#666;border:1px solid #d9d9d9;border-radius:4px;padding:8px 24px;cursor:pointer;'>取消</button>
        </div>
      </div>`;

      const dlg = document.createElement('div');
      dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.3);display:flex;align-items:center;justify-content:center;'><div style='background:#fff;padding:24px;border-radius:12px;box-shadow:0 4px 24px #aaa;'>${html}</div></div>`;
      document.body.appendChild(dlg);

      dlg.querySelector('#cancelDeleteItem').onclick = function () {
        document.body.removeChild(dlg);
      };

      dlg.querySelector('#confirmDeleteItem').onclick = function () {
        deleteItems([idx]);
        document.body.removeChild(dlg);
        addLuiHistory('system', `已删除条目 "${item.desc || '无描述'}"`);
        renderLuiHistory();
      };
    }

    // 批量删除选中条目的确认对话框
    function confirmBatchDelete() {
      const checkedIndexes = Array.from(document.querySelectorAll('.rowCheck:checked')).map(cb => parseInt(cb.dataset.idx));

      if (checkedIndexes.length === 0) {
        alert('请先选择要删除的条目');
        return;
      }

      const list = getAnswerList();
      const selectedItems = checkedIndexes.map(idx => list[idx]).filter(Boolean);

      const html = `<div style='width:520px;max-width:96vw;'>
        <div style='font-size:18px;font-weight:500;margin-bottom:12px;color:#ff4d4f;'>⚠️ 批量删除确认</div>
        <div style='padding:16px;background:#fff2f0;border:1px solid #ffccc7;border-radius:6px;margin-bottom:16px;'>
          <div style='font-weight:500;margin-bottom:8px;'>即将删除 ${selectedItems.length} 个条目：</div>
          <div style='max-height:200px;overflow-y:auto;'>
            ${selectedItems.map((item, i) => `
              <div style='color:#666;margin:4px 0;padding:4px 8px;background:#fafafa;border-radius:4px;'>
                ${item.no || checkedIndexes[i] + 1}. ${item.desc || '无描述'}
              </div>
            `).join('')}
          </div>
        </div>
        <div style='color:#999;font-size:13px;margin-bottom:16px;'>
          删除后将无法恢复，请确认是否继续？
        </div>
        <div style='text-align:right;display:flex;justify-content:flex-end;gap:12px;'>
          <button id='confirmBatchDelete' style='background:#ff4d4f;color:#fff;border:none;border-radius:4px;padding:8px 24px;cursor:pointer;'>确认删除</button>
          <button id='cancelBatchDelete' style='background:#f5f5f5;color:#666;border:1px solid #d9d9d9;border-radius:4px;padding:8px 24px;cursor:pointer;'>取消</button>
        </div>
      </div>`;

      const dlg = document.createElement('div');
      dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.3);display:flex;align-items:center;justify-content:center;'><div style='background:#fff;padding:24px;border-radius:12px;box-shadow:0 4px 24px #aaa;'>${html}</div></div>`;
      document.body.appendChild(dlg);

      dlg.querySelector('#cancelBatchDelete').onclick = function () {
        document.body.removeChild(dlg);
      };

      dlg.querySelector('#confirmBatchDelete').onclick = function () {
        deleteItems(checkedIndexes);
        document.body.removeChild(dlg);
        addLuiHistory('system', `已批量删除 ${selectedItems.length} 个条目`);
        renderLuiHistory();
      };
    }

    // 清空全部条目的确认对话框
    function confirmClearAllItems() {
      const list = getAnswerList();

      if (list.length === 0) {
        alert('当前没有条目可以清空');
        return;
      }

      const html = `<div style='width:420px;max-width:96vw;'>
        <div style='font-size:18px;font-weight:500;margin-bottom:12px;color:#ff4d4f;'>⚠️ 清空全部确认</div>
        <div style='padding:16px;background:#fff2f0;border:1px solid #ffccc7;border-radius:6px;margin-bottom:16px;'>
          <div style='font-weight:500;margin-bottom:8px;'>即将清空全部条目：</div>
          <div style='color:#666;'>当前共有 <strong>${list.length}</strong> 个条目</div>
        </div>
        <div style='color:#999;font-size:13px;margin-bottom:16px;'>
          <strong style='color:#ff4d4f;'>此操作将删除所有条目，且无法恢复！</strong><br/>
          请确认是否继续？
        </div>
        <div style='text-align:right;display:flex;justify-content:flex-end;gap:12px;'>
          <button id='confirmClearAll' style='background:#ff4d4f;color:#fff;border:none;border-radius:4px;padding:8px 24px;cursor:pointer;'>确认清空</button>
          <button id='cancelClearAll' style='background:#f5f5f5;color:#666;border:1px solid #d9d9d9;border-radius:4px;padding:8px 24px;cursor:pointer;'>取消</button>
        </div>
      </div>`;

      const dlg = document.createElement('div');
      dlg.innerHTML = `<div style='position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:rgba(0,0,0,0.3);display:flex;align-items:center;justify-content:center;'><div style='background:#fff;padding:24px;border-radius:12px;box-shadow:0 4px 24px #aaa;'>${html}</div></div>`;
      document.body.appendChild(dlg);

      dlg.querySelector('#cancelClearAll').onclick = function () {
        document.body.removeChild(dlg);
      };

      dlg.querySelector('#confirmClearAll').onclick = function () {
        clearAllItems();
        document.body.removeChild(dlg);
        addLuiHistory('system', `已清空全部 ${list.length} 个条目`);
        renderLuiHistory();
      };
    }

    // 执行删除操作的核心函数
    function deleteItems(indexes) {
      const list = getAnswerList();
      // 按索引倒序删除，避免索引错位
      const sortedIndexes = indexes.sort((a, b) => b - a);

      sortedIndexes.forEach(idx => {
        if (idx >= 0 && idx < list.length) {
          list.splice(idx, 1);
        }
      });

      saveAnswerList(list);
      renderAnswerTable();
      updateOutline(); // 更新大纲树

      // 取消所有选中状态
      document.querySelectorAll('.rowCheck').forEach(cb => cb.checked = false);
      const selectAllCheckbox = document.getElementById('selectAllRow');
      if (selectAllCheckbox) selectAllCheckbox.checked = false;
    }

    // 清空全部条目
    function clearAllItems() {
      saveAnswerList([]);
      renderAnswerTable();
      updateOutline(); // 更新大纲树

      // 取消所有选中状态
      const selectAllCheckbox = document.getElementById('selectAllRow');
      if (selectAllCheckbox) selectAllCheckbox.checked = false;
    }

    // === 智能满足度指标计算功能 ===
    function calculateSatisfaction() {
      const list = getAnswerList();

      // 统计各种状态的数量
      let totalItems = 0;
      let totalProducts = 0;
      let fcCount = 0;
      let pcCount = 0;
      let ncCount = 0;
      let naCount = 0;
      let unansweredCount = 0;

      list.forEach(row => {
        if (row.products && row.products.length > 0) {
          row.products.forEach(product => {
            totalItems++;
            totalProducts++;
            const productData = row.productData?.[product];
            if (productData) {
              const answer = productData.answer || '';
              switch (answer.toUpperCase()) {
                case 'FC':
                  fcCount++;
                  break;
                case 'PC':
                  pcCount++;
                  break;
                case 'NC':
                  ncCount++;
                  break;
                case 'N/A':
                  naCount++;
                  break;
                default:
                  unansweredCount++;
                  break;
              }
            } else {
              unansweredCount++;
            }
          });
        }
      });

      // 计算满足度和不满足度
      const satisfaction = totalItems > 0 ? ((fcCount + pcCount) / totalItems * 100).toFixed(1) : 0;
      const unsatisfaction = totalItems > 0 ? (ncCount / totalItems * 100).toFixed(1) : 0;

      // 展示计算结果
      const resultHtml = `
        <div style="background:#f6ffed;border:1px solid #b7eb8f;border-radius:6px;padding:16px;margin:8px 0;">
          <div style="font-size:16px;font-weight:500;color:#389e0d;margin-bottom:12px;">📊 满足度指标计算结果</div>
          <div style="display:grid;grid-template-columns:1fr 1fr;gap:16px;margin-bottom:12px;">
            <div style="text-align:center;">
              <div style="font-size:28px;font-weight:bold;color:#52c41a;">${satisfaction}%</div>
              <div style="color:#666;font-size:14px;">满足度</div>
              <div style="color:#999;font-size:12px;">(FC + PC) / 总条目</div>
            </div>
            <div style="text-align:center;">
              <div style="font-size:28px;font-weight:bold;color:#ff4d4f;">${unsatisfaction}%</div>
              <div style="color:#666;font-size:14px;">不满足度</div>
              <div style="color:#999;font-size:12px;">NC / 总条目</div>
            </div>
          </div>
          <div style="border-top:1px solid #d9f7be;padding-top:12px;">
            <div style="display:grid;grid-template-columns:repeat(3,1fr);gap:8px;font-size:13px;">
              <div>总条目数：<strong>${list.length}</strong></div>
              <div>总应答数：<strong>${totalItems}</strong></div>
              <div>产品数量：<strong>${new Set(list.flatMap(row => row.products || [])).size}</strong></div>
            </div>
            <div style="display:grid;grid-template-columns:repeat(5,1fr);gap:8px;margin-top:8px;font-size:13px;">
              <div style="color:#52c41a;">FC：<strong>${fcCount}</strong></div>
              <div style="color:#faad14;">PC：<strong>${pcCount}</strong></div>
              <div style="color:#ff4d4f;">NC：<strong>${ncCount}</strong></div>
              <div style="color:#666;">N/A：<strong>${naCount}</strong></div>
              <div style="color:#999;">未应答：<strong>${unansweredCount}</strong></div>
            </div>
          </div>
        </div>
      `;

      // 添加到对话历史
      addLuiHistory('system', resultHtml);
      renderLuiHistory();

      // 同时在控制台输出详细信息用于调试
      console.log('满足度计算详情：', {
        totalItems,
        totalProducts,
        fcCount,
        pcCount,
        ncCount,
        naCount,
        unansweredCount,
        satisfaction: satisfaction + '%',
        unsatisfaction: unsatisfaction + '%'
      });
    }

    // --- 大纲树相关 ---
    function buildOutlineTree(list) {
      const root = [];
      const nodeMap = {};

      // 第一步：创建所有节点
      list.forEach((row, idx) => {
        const no = String(row.no || idx + 1);
        const desc = row.desc || '';
        const node = { no, desc, children: [], idx };
        nodeMap[no] = node;
      });

      // 第二步：建立父子关系
      list.forEach((row, idx) => {
        const no = String(row.no || idx + 1);
        const node = nodeMap[no];
        const parts = no.split('.');

        if (parts.length === 1) {
          // 一级节点
          root.push(node);
        } else {
          // 子节点，找到父节点
          const parentNo = parts.slice(0, -1).join('.');
          const parentNode = nodeMap[parentNo];
          if (parentNode) {
            parentNode.children.push(node);
          } else {
            // 如果找不到父节点，作为根节点
            root.push(node);
          }
        }
      });

      return root;
    }
    function renderOutlineTree(tree, parentUl) {
      tree.forEach(node => {
        const li = document.createElement('li');
        li.style.listStyle = 'none';
        li.style.margin = '2px 0 2px 0';
        li.style.fontSize = '14px';
        li.style.cursor = 'pointer';
        li.innerHTML = `<span class="outline-node" data-idx="${node.idx}" data-no="${node.no}" style="padding:2px 4px;border-radius:4px;display:inline-block;max-width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;" title="${node.desc}">${node.no}. ${node.desc}</span>`;
        if (node.children && node.children.length > 0) {
          const toggle = document.createElement('span');
          toggle.textContent = '▼';
          toggle.style.fontSize = '12px';
          toggle.style.marginRight = '4px';
          toggle.style.cursor = 'pointer';
          toggle.style.color = '#666';
          toggle.style.userSelect = 'none';

          const childUl = document.createElement('ul');
          childUl.style.marginLeft = '18px';
          childUl.style.paddingLeft = '8px';
          childUl.style.display = ''; // 默认展开

          toggle.onclick = function (e) {
            e.stopPropagation();
            if (childUl.style.display === 'none') {
              childUl.style.display = '';
              toggle.textContent = '▼';
            } else {
              childUl.style.display = 'none';
              toggle.textContent = '▶';
            }
          };

          li.prepend(toggle);
          renderOutlineTree(node.children, childUl);
          li.appendChild(childUl);
        }
        parentUl.appendChild(li);
      });
    }
    function updateOutline() {
      const list = getAnswerList();
      const tree = buildOutlineTree(list);
      const treeRoot = document.createElement('ul');
      treeRoot.style.margin = '0';
      treeRoot.style.padding = '0';
      renderOutlineTree(tree, treeRoot);
      const treeDiv = document.getElementById('outline-tree');
      treeDiv.innerHTML = '';
      treeDiv.appendChild(treeRoot);
      treeDiv.querySelectorAll('.outline-node').forEach(node => {
        node.onclick = function (e) {
          e.stopPropagation();
          const idx = node.dataset.idx;
          const anchor = document.getElementById('outline-item-' + idx);
          if (anchor) {
            anchor.scrollIntoView({ behavior: 'smooth', block: 'center' });
            anchor.style.background = '#e6f7ff';
            setTimeout(() => { anchor.style.background = ''; }, 1200);
          }
        };

        // 添加悬停效果
        node.onmouseenter = function () {
          this.style.background = '#f0f0f0';
        };

        node.onmouseleave = function () {
          this.style.background = '';
        };
      });
    }
    const outlinePanel = document.getElementById('outline-panel');
    const outlineExpand = document.getElementById('outline-expand');
    const outlineCollapse = document.getElementById('outline-collapse');
    function showOutlinePanel(show) {
      if (show) {
        outlinePanel.style.display = '';
        outlineExpand.style.display = 'none';
        localStorage.setItem('outlineCollapsed', '0');
      } else {
        outlinePanel.style.display = 'none';
        outlineExpand.style.display = '';
        localStorage.setItem('outlineCollapsed', '1');
      }
    }
    outlineExpand.onclick = () => showOutlinePanel(true);
    outlineCollapse.onclick = () => showOutlinePanel(false);
    if (localStorage.getItem('outlineCollapsed') === '1') {
      showOutlinePanel(false);
    } else {
      showOutlinePanel(true);
    }
    // --- end 大纲树 ---

    // === 树形选择器和搜索下拉框相关函数 ===
    
    // 初始化树形选择器
    function initTreeSelect(dlg) {
      const treeSelectInput = dlg.querySelector('.tree-select-input');
      const treeSelectDropdown = dlg.querySelector('.tree-select-dropdown');
      const productSelectHidden = dlg.querySelector('#productSelectDialog');
      const placeholder = dlg.querySelector('.tree-select-placeholder');
      
      // 点击输入框显示/隐藏下拉框
      treeSelectInput.onclick = function(e) {
        e.stopPropagation();
        const isVisible = treeSelectDropdown.style.display === 'block';
        treeSelectDropdown.style.display = isVisible ? 'none' : 'block';
      };
      
      // 点击其他地方隐藏下拉框
      document.addEventListener('click', function(e) {
        if (!treeSelectInput.contains(e.target) && !treeSelectDropdown.contains(e.target)) {
          treeSelectDropdown.style.display = 'none';
        }
      });
      
      // 选择产品节点
      dlg.querySelectorAll('.tree-node').forEach(node => {
        node.onclick = function(e) {
          e.stopPropagation();
          const value = this.dataset.value;
          const label = this.querySelector('.tree-node-label').textContent;
          const type = this.dataset.type;
          
          // 切换选中状态
          this.classList.toggle('selected');
          
          // 更新隐藏输入框的值
          const selectedNodes = dlg.querySelectorAll('.tree-node.selected');
          const selectedValues = Array.from(selectedNodes).map(n => n.dataset.value);
          productSelectHidden.value = selectedValues.join(',');
          
          console.log('产品选择更新:', selectedValues);
          console.log('隐藏输入框值:', productSelectHidden.value);
          
          // 更新显示
          if (selectedValues.length > 0) {
            const selectedLabels = Array.from(selectedNodes).map(n => n.querySelector('.tree-node-label').textContent);
            placeholder.textContent = selectedLabels.join(', ');
            placeholder.style.color = '#333';
          } else {
            placeholder.textContent = '请选择产品';
            placeholder.style.color = '#999';
          }
        };
      });
    }
    
    // 切换产品树显示/隐藏
    function toggleProductTree() {
      const dropdown = document.querySelector('.tree-select-dropdown');
      if (dropdown) {
        dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
      }
    }
    
    // 切换树节点展开/收起
    function toggleTreeNode(toggle) {
      const node = toggle.parentElement;
      const children = node.querySelector('.tree-children');
      if (children) {
        const isExpanded = children.style.display !== 'none';
        children.style.display = isExpanded ? 'none' : 'block';
        toggle.textContent = isExpanded ? '▶' : '▼';
      }
    }
    
    // 过滤产品树
    function filterProductTree(keyword) {
      const nodes = document.querySelectorAll('.tree-node');
      nodes.forEach(node => {
        const label = node.querySelector('.tree-node-label').textContent.toLowerCase();
        const type = node.dataset.type;
        const matches = label.includes(keyword.toLowerCase()) || type.includes(keyword);
        node.style.display = matches ? 'block' : 'none';
      });
    }
    
    // 初始化搜索下拉框
    function initSearchableSelects(dlg) {
      // 国家下拉框
      const countryInput = dlg.querySelector('#countrySelectDialog');
      const countryDropdown = dlg.querySelector('.searchable-dropdown');
      
      if (countryInput && countryDropdown) {
        // 显示国家下拉框
        countryInput.onfocus = function() {
          countryDropdown.style.display = 'block';
        };
        
        // 隐藏国家下拉框
        countryInput.onblur = function() {
          setTimeout(() => {
            countryDropdown.style.display = 'none';
          }, 200);
        };
        
        // 选择国家选项
        countryDropdown.querySelectorAll('.dropdown-option').forEach(option => {
          option.onclick = function() {
            countryInput.value = this.dataset.value;
            countryDropdown.style.display = 'none';
          };
        });
      }
      
      // 客户下拉框
      const operatorInput = dlg.querySelector('#operatorSelectDialog');
      const operatorDropdown = dlg.querySelectorAll('.searchable-dropdown')[1];
      
      if (operatorInput && operatorDropdown) {
        // 显示客户下拉框
        operatorInput.onfocus = function() {
          operatorDropdown.style.display = 'block';
        };
        
        // 隐藏客户下拉框
        operatorInput.onblur = function() {
          setTimeout(() => {
            operatorDropdown.style.display = 'none';
          }, 200);
        };
        
        // 选择客户选项
        operatorDropdown.querySelectorAll('.dropdown-option').forEach(option => {
          option.onclick = function() {
            operatorInput.value = this.dataset.value;
            operatorDropdown.style.display = 'none';
          };
        });
      }
    }
    
    // 显示国家下拉框
    function showCountryDropdown() {
      const dropdown = document.querySelector('.searchable-dropdown');
      if (dropdown) {
        dropdown.style.display = 'block';
      }
    }
    
    // 隐藏国家下拉框
    function hideCountryDropdown() {
      setTimeout(() => {
        const dropdown = document.querySelector('.searchable-dropdown');
        if (dropdown) {
          dropdown.style.display = 'none';
        }
      }, 200);
    }
    
    // 显示客户下拉框
    function showOperatorDropdown() {
      const dropdowns = document.querySelectorAll('.searchable-dropdown');
      if (dropdowns[1]) {
        dropdowns[1].style.display = 'block';
      }
    }
    
    // 隐藏客户下拉框
    function hideOperatorDropdown() {
      setTimeout(() => {
        const dropdowns = document.querySelectorAll('.searchable-dropdown');
        if (dropdowns[1]) {
          dropdowns[1].style.display = 'none';
        }
      }, 200);
    }
    
    // 过滤国家选项
    function filterCountryOptions(keyword) {
      const options = document.querySelectorAll('.searchable-dropdown .dropdown-option');
      options.forEach(option => {
        const text = option.textContent.toLowerCase();
        const matches = text.includes(keyword.toLowerCase());
        option.style.display = matches ? 'block' : 'none';
      });
    }
    
    // 过滤客户选项
    function filterOperatorOptions(keyword) {
      const dropdowns = document.querySelectorAll('.searchable-dropdown');
      if (dropdowns[1]) {
        const options = dropdowns[1].querySelectorAll('.dropdown-option');
        options.forEach(option => {
          const text = option.textContent.toLowerCase();
          const matches = text.includes(keyword.toLowerCase());
          option.style.display = matches ? 'block' : 'none';
        });
      }
    }
    
    // 添加CSS样式
    const style = document.createElement('style');
    style.textContent = `
      .tree-node {
        padding: 6px 8px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 13px;
        border-radius: 4px;
        transition: background-color 0.2s;
      }
      
      .tree-node:hover {
        background-color: #f5f5f5;
      }
      
      .tree-node.selected {
        background-color: #e6f7ff;
        color: #1890ff;
      }
      
      .tree-node-toggle {
        font-size: 10px;
        color: #666;
        cursor: pointer;
        user-select: none;
        width: 12px;
        text-align: center;
      }
      
      .tree-node-label {
        flex: 1;
      }
      
      .tree-node-type {
        font-size: 11px;
        color: #999;
      }
      
      .dropdown-option {
        padding: 8px 12px;
        cursor: pointer;
        font-size: 13px;
        transition: background-color 0.2s;
      }
      
      .dropdown-option:hover {
        background-color: #f5f5f5;
      }
      
      .tree-select-input {
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        padding: 8px 12px;
        min-height: 36px;
        background: #fff;
        cursor: pointer;
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        align-items: center;
        transition: border-color 0.3s;
      }
      
      .tree-select-input:hover {
        border-color: #40a9ff;
      }
      
      .tree-select-input:focus-within {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    `;
    document.head.appendChild(style);
  </script>
</body>

</html>