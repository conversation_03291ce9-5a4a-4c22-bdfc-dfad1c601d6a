package com.zte.mcrm.adapter.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class IndustryTreeDTO {

    /**
     * 父编码(节点展开查询必填)
     */
    @ApiModelProperty(value = "父编码(节点展开查询必填)")
    private String parentCode;
    /**
     * 名称(模糊查询时必填)
     */
    @ApiModelProperty(value = "名称(模糊查询时必填)")
    private String name;
    /**
     * 编码数组(批量查询数据时用到)
     */
    @ApiModelProperty(value = "编码数组(批量查询数据时用到)")
    private List<String> codeList;

}
