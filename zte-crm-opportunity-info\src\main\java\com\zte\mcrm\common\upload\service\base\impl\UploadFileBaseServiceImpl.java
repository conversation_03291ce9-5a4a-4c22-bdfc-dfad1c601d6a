package com.zte.mcrm.common.upload.service.base.impl;

import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.common.business.service.IKeyIdService;
import com.zte.mcrm.common.upload.dao.base.ComUploadFileDao;
import com.zte.mcrm.common.upload.model.entity.ComUploadFile;
import com.zte.mcrm.common.upload.service.base.UploadFileBaseService;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.mcrm.common.util.EmpProcessUtil;
import com.zte.mcrm.common.util.EntityTransformUtils;
import com.zte.mcrm.common.util.FormDataHelpUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 *  服务类 
 * <AUTHOR>
 * @date 2021/06/05
 */
@Service
public class UploadFileBaseServiceImpl implements UploadFileBaseService {
    @Autowired
    private IKeyIdService iKeyIdService;

	@Autowired
	private ComUploadFileDao uploadFileDao ;

	@Value("${eSupport.model.Id:3480002}")
	private String moduleId;

	@Value("${role.Id:developer}")
	private String roleId;

	/**
	 * 根据主键获取实体对象
	 * @param oid 主键ID
	 * @return
	 * <AUTHOR>
     * @date 2021/06/05
     */
	@Override
	public ComUploadFile get(Long oid) {
		return uploadFileDao.get(oid);
	}

	/**
	 * 删除指定记录
	 * @param oid 主键ID
	 * @return 删除记录个数
	 * <AUTHOR>
     * @date 2021/06/05
     */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public int delete(Long oid){
		return uploadFileDao.delete(oid);
	}

	/**
	 * 新增指定记录
	 * @param entity 实体对象
	 * @return 新增记录个数
	 * <AUTHOR>
     * @date 2021/06/05
     */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public int insert(ComUploadFile entity){
		String emp = CommonUtils.getEmpNo();
		Date now = new Date();
		entity.setCreatedBy(emp);
		entity.setLastUpdatedBy(emp);
        entity.setCreatedDate(now);
		entity.setLastUpdatedDate(now);
        if (null==entity.getId()) {
            entity.setId(iKeyIdService.getKeyLongId());
        }		
		return uploadFileDao.insert(entity);
	}

	/**
	 * 修改指定记录
	 * @param entity 实体对象
	 * @return 修改记录个数
	 * <AUTHOR>
     * @date 2021/06/05
     */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public int update(ComUploadFile entity){
		entity.setLastUpdatedBy(CommonUtils.getEmpNo());
		entity.setLastUpdatedDate(new Date());
		return uploadFileDao.update(entity);
	}

	/**
	 * 修改指定记录
	 * @param entity 实体对象
	 * @return 修改记录个数
	 * <AUTHOR>
	 * @date 2021/06/05
	 */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public int deleteByDmeKey(ComUploadFile entity){
		entity.setLastUpdatedBy(CommonUtils.getEmpNo());
		entity.setLastUpdatedDate(new Date());
		return uploadFileDao.deleteByDmeKey(entity);
	}

//	/**
//	 * 批量更新文件密级
//	 * @param dmeKeyList, secretLevel
//	 * @return boolen
//	 * <AUTHOR> 10305348
//	 * @date 2021/09/02
//	 */
//	@Override
//	public Boolean updateUploadFileSecretLevel(List<String> dmeKeyList, int secretLevel) throws Exception{
//		// 判断是否为运维人员 flag
//		List<String> roleCodeList = new ArrayList<>();
//		roleCodeList.add(roleId);
//		String userInfoList = roleService.getUsersByRoleList(moduleId, roleCodeList);
//
//		// 判断有无 运维人员
//		if (userInfoList.equals(StringUtils.EMPTY)) {
//			throw new BusinessRuntimeException(PrmRetCode.BUSINESSERROR_CODE, UploadRetCode.NO_DEVELOPER);
//		}
//
//		// 遍历判断请求头id  是否为运维人员
//		String empNo = CommonUtils.getEmpNo();
//		List<String> userIdList = Splitter.on(',').omitEmptyStrings().trimResults().splitToList(userInfoList);
//		if (!userIdList.contains(empNo)) {
//			throw new BusinessRuntimeException(PrmRetCode.PERMISSIONDENIED_CODE,
//					UploadRetCode.NO_PERMISSION_UPDATE_SECRET_LEVEL);
//		}
//
//		// 如果是  则批量修改 密级 为secretLevel
//		UploadFile entity = new UploadFile();
//		Date now = new Date();
//		entity.setSecretLevel(secretLevel);
//		entity.setLastUpdatedBy(CommonUtils.getEmpNo());
//		entity.setLastUpdatedDate(now);
//		for (String dmeKey : dmeKeyList) {
//			entity.setDmeKey(dmeKey);
//			uploadFileDao.updateUploadFileSecretLevel(entity);
//		}
//		return true;
//	}

	/**
	 * 获取符合条件的实体列表,按指定属性排序
	 * @param entity 参数集合
	 * @return 实体集合
	 * <AUTHOR>
     * @date 2021/06/05
     */
	@Override
	public List<ComUploadFile> getList(ComUploadFile entity){
		return uploadFileDao.getList(EntityTransformUtils.toMapParams(entity));
	}

	/**
	 * 统计
	 * @param map 参数集合
	 * @return 统计总数
	 * <AUTHOR>
     * @date 2021/06/05
     */
	@Override
	public long getCount(Map<String, Object> map){
        return uploadFileDao.getCount(map);
	}

	/**
	 * 获取符合条件的记录列表,先按指定属性排序,在分页
	 * @param map 参数集合
	 * @return 实体集合
	 * <AUTHOR>
     * @date 2021/06/05
     */
	@Override
	public List<ComUploadFile> getPage(Map<String, Object> map){
		return uploadFileDao.getPage(map);
	}

	/**
	* 获取符合条件的记录列表,先按指定属性排序,在分页
	* @param form 参数集合
	* @return 实体集合
	* <AUTHOR>
     * @date 2021/06/05
     */
	@Override
	public PageRows<ComUploadFile> getPageRows(FormData<ComUploadFile> form){
		Map<String, Object> map = FormDataHelpUtil.getPageQueryMap(form);
		long total = this.getCount(map);
		List<ComUploadFile> result = this.getPage(map);
		return FormDataHelpUtil.getPageRowsResult(form, total, result);
	}

    /**
     * 更新附件顺序
     * @param list
     * 已上传附件的OID列表
     * @return
     */
    @Override
    public int updateUploadFileOrder(List<ComUploadFile> list) {
        return uploadFileDao.updateUploadFileOrder(list);
    }

    /**
     * 批量查询业务附件
     * @param uploadType
     * @param billOids
     * @return
     */
    @Override
    public List<ComUploadFile> queryUploadFileInBatch(String uploadType, List<String> billOids) {
	    return uploadFileDao.queryUploadFileInBatch(uploadType, billOids);
    }

	@Override
	public long getFuzzyCount(Map<String, Object> map) {
		return uploadFileDao.getFuzzyCount(map);
	}

	@Override
	public List<ComUploadFile> getFuzzyPage(Map<String, Object> map) throws Exception {
		return EmpProcessUtil.replaceEmpNo(uploadFileDao.getFuzzyPage(map));
	}
}
