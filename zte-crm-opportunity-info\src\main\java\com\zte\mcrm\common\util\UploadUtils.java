package com.zte.mcrm.common.util;

import com.zte.mcrm.common.consts.CommonConst;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.lang.reflect.Method;

/**
 * 文件上传工具类
 *
 * @Author: 10245190 DengZiqiao
 * @Date: 2021年9月14日 16:01:58
 * @Version: V1.0
 */
@Slf4j
public class UploadUtils {

    private UploadUtils() {
    }

    private static final String ZH_CN = "zh_CN";

    /**
     * 文档云需要的语言标识。中文：2052
     */
    private static final String LANGUAGE_ZH = "2052";
    /**
     * 文档云需要的语言标识。英文：9
     */
    private static final String LANGUAGE_EN = "9";

    /**
     * 生成groupKey，默认直接拼接业务id和业务类型
     *
     * @param businessId           业务ID
     * @param businessType         业务类型
     * @param springProfilesActive 环境标识
     * @return
     */
    public static String generateGroupKey(String businessId, String businessType, String springProfilesActive) {
        return businessId + businessType + springProfilesActive;
    }

    /**
     * 将请求头中的语言标识转换成文档云需要的语言标识
     * 语言（2052：中文，9：英文
     *
     * @param langId 请求头中的语言标识
     * @return 文档云需要的语言标识
     */
    public static String convertLangId(String langId) {
        return ZH_CN.equals(langId) ? LANGUAGE_ZH : LANGUAGE_EN;
    }

    public static String checkFileName(String fileName)
    {
        if(fileName.startsWith(CommonConst.URL_SPER) || fileName.contains(CommonConst.URL_SPER_LAST_LAYER))
        {
            log.error("fileName is error fileName=" + fileName);
            fileName = "";
        }
        return fileName;
    }

}
