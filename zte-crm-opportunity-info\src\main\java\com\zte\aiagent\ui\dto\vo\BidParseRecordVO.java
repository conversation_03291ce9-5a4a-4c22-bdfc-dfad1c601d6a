package com.zte.aiagent.ui.dto.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 解析项记录视图对象
 * 对应PO: BidParseRecordPO
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class BidParseRecordVO {
    /**
     * 主键ID
     */
    private String rowId;

    /**
     * 招标文件ID，关联bid_document.row_id
     */
    private String documentId;

    /**
     * 解析条目大类code
     */
    private String parseType;

    /**
     * 解析状态: PENDING-待解析, PROCESSING-解析中, SUCCESS-解析成功, FAILED-解析失败
     */
    private String parseStatus;

    /**
     * 解析开始时间
     */
    private Date parseStartTime;

    /**
     * 解析结束时间
     */
    private Date parseEndTime;

    /**
     * 解析耗时(毫秒)
     */
    private Integer parseDuration;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 提取的条目数量
     */
    private Integer extractedCount;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人
     */
    private String lastUpdatedBy;

    /**
     * 最后更新时间
     */
    private Date lastUpdatedDate;

    /**
     * 有效标记(Y/N)
     */
    private String enabledFlag;

    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 解析条目大类中文名称
     */
    private String parseTypeValueCn;

    /**
     * 解析条目大类英文名称
     */
    private String parseTypeValueEn;
} 