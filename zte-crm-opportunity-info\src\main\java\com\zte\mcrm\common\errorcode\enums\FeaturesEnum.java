package com.zte.mcrm.common.errorcode.enums;

/**
 * @Author: <EMAIL>
 * @Date: 2022/01/10
 * @Description:
 */
public enum FeaturesEnum {
    /**
     * 基础权限
     */
    A("01","","基础权限"),
    /**
     * 组织树
     */
    B("02","","组织树"),
    /**
     * 产品树
     */
    C("03","","产品树"),
    /**
     * 行业树
     */
    D("04","","行业树"),
    /**
     * 系统参数配置
     */
    E("05","","系统参数配置"),
    /**
     * KCP关键控制点
     */
    F("06","","KCP关键控制点"),
    /**
     * 任务管理
     */
    G("07","","任务管理"),
    /**
     * 附件管理
     */
    H("08","","附件管理"),
    /**
     * key生成
     */
    J("09","","key生成"),
    /**
     * 消息转发
     */
    K("10","","消息转发"),
    /**
     * 通用模板
     */
    L("11","","通用模板");
    private String oldCode;
    private String en;
    private String zh;

    FeaturesEnum(String oldCode, String en, String zh) {
        this.oldCode = oldCode;
        this.en = en;
        this.zh = zh;
    }

    public String getOldCode() {
        return oldCode;
    }

    public String getEn() {
        return en;
    }

    public String getZh() {
        return zh;
    }
}
