package com.zte.mcrm.channel.model.mapper;

import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.channel.model.dto.OpportunityInfoDTO;
import com.zte.mcrm.channel.model.entity.Opportunity;
import com.zte.mcrm.channel.model.entity.OpportunityDetail;
import com.zte.mcrm.channel.model.entity.OpportunityProduct;
import com.zte.mcrm.channel.model.vo.*;
import com.zte.mcrm.common.business.service.SensitiveEncryptor;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

public class OpportunityInfoVOMapper {

    private OpportunityInfoVOMapper(){
        throw new IllegalStateException("OpportunityInfoVOMapper class");
    }

    public static OpportunityInfoDTO transOpportunityInfoVOToOpportunityInfoDTO(OpportunityInfoVO prmOpportunityInfo){
        OpportunityInfoDTO opportunityInfoDTO = new OpportunityInfoDTO();
        OpportunityAddVO prmOpportunityAddVO = prmOpportunityInfo.getOpportunity();
        OpportunityDetailVO prmOpportunityDetailVO = prmOpportunityInfo.getOpportunityDetail();
        List<OpportunityProductVO> prmOpportunityProducts = prmOpportunityInfo.getOpportunityProducts();
        Opportunity opportunity = OpportunityMapper.INSTANCE.transOpportunityAddVOToOpportunity(prmOpportunityAddVO);
        OpportunityDetail opportunityDetail = OpportunityDetailMapper.INSTANCE.transOpportunityDetailVOToOpportunityDetail(prmOpportunityDetailVO);
        List<OpportunityProduct> opportunityProducts = new ArrayList<>();
        for(OpportunityProductVO prmOpportunityProductVO : prmOpportunityProducts){
            OpportunityProduct opportunityProduct = OpportunityProductMapper.INSTANCE.transOpportunityProductVOToOpportunityProduct(prmOpportunityProductVO);
            opportunityProducts.add(opportunityProduct);
        }
        opportunityInfoDTO.setOpportunityProducts(opportunityProducts);
        opportunityInfoDTO.setOpportunity(opportunity);
        opportunityInfoDTO.setOpportunityDetail(opportunityDetail);
        return opportunityInfoDTO;
    }

    public static OpportunityInfoVO transOpportunityInfoDTOToOpportunityInfoVO(OpportunityInfoDTO opportunityInfoDTO){
        OpportunityInfoVO opportunityInfoVO = new OpportunityInfoVO();
        Opportunity opportunity = opportunityInfoDTO.getOpportunity();
        OpportunityDetail opportunityDetail = opportunityInfoDTO.getOpportunityDetail();
        List<OpportunityProduct> opportunityProductList = opportunityInfoDTO.getOpportunityProducts();
        OpportunityAddVO prmOpportunityAddVO = OpportunityMapper.INSTANCE.transOpportunityToOpportunityAddVO(opportunity);
        OpportunityDetailVO prmOpportunityDetailVO = OpportunityDetailMapper.INSTANCE.transOpportunityDetailToOpportunityDetailVO(opportunityDetail);
        // 加密中兴业务经理
        List<OpportunityProductVO> opportunityProducts = new ArrayList<>();
        for(OpportunityProduct opportunityProduct : opportunityProductList){
            OpportunityProductVO prmOpportunityProductVO = OpportunityProductMapper.INSTANCE.transOpportunityProductToOpportunityProductVO(opportunityProduct);
            opportunityProducts.add(prmOpportunityProductVO);
        }
        opportunityInfoVO.setOpportunityProducts(opportunityProducts);
        opportunityInfoVO.setOpportunity(prmOpportunityAddVO);
        opportunityInfoVO.setOpportunityDetail(prmOpportunityDetailVO);
        return opportunityInfoVO;
    }

    public static List<OpportunityProduct> transOpportunityProductVOsToOpportunityProducts(List<OpportunityProductVO> opportunityProductVOs, String dataSource){
        List<OpportunityProduct> opportunityProducts = new ArrayList<>();
        for(OpportunityProductVO prmOpportunityProductVO : opportunityProductVOs){
            OpportunityProduct opportunityProduct = OpportunityProductMapper.INSTANCE.transOpportunityProductVOToOpportunityProduct(prmOpportunityProductVO);
            opportunityProduct.setDataSource(dataSource);
            opportunityProducts.add(opportunityProduct);
        }
        return opportunityProducts;
    }

    public static List<OpportunityProductVO> transOpportunityProductsToOpportunityProductVOs(List<OpportunityProduct> opportunityProducts){
        List<OpportunityProductVO> opportunityProductVOs = new ArrayList<>();
        for(OpportunityProduct opportunityProduct : opportunityProducts){
            OpportunityProductVO opportunityProductVO = OpportunityProductMapper.INSTANCE.transOpportunityProductToOpportunityProductVO(opportunityProduct);
            opportunityProductVOs.add(opportunityProductVO);
        }
        return opportunityProductVOs;
    }
}
