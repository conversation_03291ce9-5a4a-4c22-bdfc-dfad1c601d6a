package com.zte.mcrm.common.model;

import java.util.List;

/****
 *
 * <AUTHOR> @date 2021/2/10
 **/
public class DynamicMessage {
	private String fileType;
	private String infoClass;
	private String infoClassEmployee;
	private String infoCode;
	private String infoId;
	private String infoName;
	private String infoSort;
	private String infoSourceFlag;
	private String infoType;
	private String operContent;
	private String operNameNo;
	private String operNo;
	private String objHasLink;
	private String operTime;
	private String objLinkType;
	private String objLinkCode;
	private List<PushEmployee> pushEmpList;
	public String getFileType() {
		return fileType;
	}
	public void setFileType(String fileType) {
		this.fileType = fileType;
	}
	public String getInfoClass() {
		return infoClass;
	}
	public void setInfoClass(String infoClass) {
		this.infoClass = infoClass;
	}
	public String getInfoClassEmployee() {
		return infoClassEmployee;
	}
	public void setInfoClassEmployee(String infoClassEmployee) {
		this.infoClassEmployee = infoClassEmployee;
	}
	public String getInfoCode() {
		return infoCode;
	}
	public void setInfoCode(String infoCode) {
		this.infoCode = infoCode;
	}
	public String getInfoId() {
		return infoId;
	}
	public void setInfoId(String infoId) {
		this.infoId = infoId;
	}
	public String getInfoName() {
		return infoName;
	}
	public void setInfoName(String infoName) {
		this.infoName = infoName;
	}
	public String getInfoSort() {
		return infoSort;
	}
	public void setInfoSort(String infoSort) {
		this.infoSort = infoSort;
	}
	public String getInfoSourceFlag() {
		return infoSourceFlag;
	}
	public void setInfoSourceFlag(String infoSourceFlag) {
		this.infoSourceFlag = infoSourceFlag;
	}
	public String getInfoType() {
		return infoType;
	}
	public void setInfoType(String infoType) {
		this.infoType = infoType;
	}
	public String getOperContent() {
		return operContent;
	}
	public void setOperContent(String operContent) {
		this.operContent = operContent;
	}
	public String getOperNameNo() {
		return operNameNo;
	}
	public void setOperNameNo(String operNameNo) {
		this.operNameNo = operNameNo;
	}
	public String getOperNo() {
		return operNo;
	}
	public void setOperNo(String operNo) {
		this.operNo = operNo;
	}
	public String getObjHasLink() {
		return objHasLink;
	}
	public void setObjHasLink(String objHasLink) {
		this.objHasLink = objHasLink;
	}
	public String getOperTime() {
		return operTime;
	}
	public void setOperTime(String operTime) {
		this.operTime = operTime;
	}
	public String getObjLinkType() {
		return objLinkType;
	}
	public void setObjLinkType(String objLinkType) {
		this.objLinkType = objLinkType;
	}
	public String getObjLinkCode() {
		return objLinkCode;
	}
	public void setObjLinkCode(String objLinkCode) {
		this.objLinkCode = objLinkCode;
	}
	public List<PushEmployee> getPushEmpList() {
		return pushEmpList;
	}
	public void setPushEmpList(List<PushEmployee> pushEmpList) {
		this.pushEmpList = pushEmpList;
	}
	
}
