package com.zte.mcrm.adapter.constant;

/**
 * 用户中心配置 常量
 * <AUTHOR>
 * @date 2021/9/16
 */
public class UcsConstant {

    private UcsConstant() {
        throw new IllegalStateException("UcsConstant class");
    }

    /**
     * 用户账户ID
     */
    public static final String HTTP_HEADER_X_ACC_ID = "X-Acc-Id";
    /**
     * 用户ID
     */
    public static final String HTTP_HEADER_X_USER_ID = "X-User-Id";
    /**
     * 用户 token
     */
    public static final String HTTP_HEADER_X_UC_TOKEN = "X-Uc-Token";

    /**
     * 调用方access key X-Access-Key
     */
    public static final String HTTP_HEADER_X_ACCESS_KEY = "X-Access-Key";

    /**
     * 调用方id X-App-Id
     */
    public static final String HTTP_HEADER_X_APP_ID = "X-App-Id";

    /**
     * 内部调用方id X-Innerapp-Id
     */
    public static final String HTTP_HEADER_X_INNERAPP_ID = "X-Innerapp-Id";

    /**
     * 调用链追踪码 x-uuid
     */
    public static final String HTTP_HEADER_X_UUID = "x-uuid";

    /**
     * 每次最大查询数量
     */
    public static final Integer USER_CONTER_ACCOUNTID_BATCH_MAX=500;

    /** 查询账号信息+企业信息*/
    public static final String INFOBLOCK_ENTERPRISEINFO = "B0002";

    /** 已关联*/
    public static final int RELATION_STATUS = 1;

    /** appID-请求用户中心创建公司账号*/
    public static final String SOURCE = "10003";
}
