package com.zte.mcrm.channel.constant;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 商机报备来源枚举
 * <AUTHOR>
 */
public enum SourceOfOpportunityEnum {

    //渠道报备
    CHANNEL_FILING("iChannel"),
    //中兴自建
    ZTE_SELF_BUILT("PRM");

    private String value;

    SourceOfOpportunityEnum(String value) {
        this.value = value;
    }

    @JsonValue
    public String getValue() {
        return value;
    }

    /**
     * 判断数值是否属于枚举类的值
     * @param value
     * @return
     */
    public static boolean isInclude(String value){
        boolean include = false;
        for (SourceOfOpportunityEnum e: SourceOfOpportunityEnum.values()){
            if(e.getValue().equals(value)){
                include = true;
                break;
            }
        }
        return include;
    }
}
