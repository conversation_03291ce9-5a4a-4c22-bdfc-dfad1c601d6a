package com.zte.leadinfo.leadinfo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 */
@TableName(value = "s_opty")
@Data
public class SOptyDO implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "row_id")
    private String rowId;

    /**
     * 商机编码
     */
    @TableField(value = "opty_cd")
    private String optyCd;

    /**
     * 商机组织Id
     */
    @TableField(value = "BU_ID")
    private String buId;

    /**
     * 旧组织编码
     */
    @TableField(value = "old_bu_id")
    private String oldBuId;

    /**
     * 组织全路径
     */
    @TableField(value = "org_name_path")
    private String orgNamePath;

    /**
     * 商机状态
     */
    @TableField(value = "status_cd")
    private String statusCd;

    /**
     * 客户Id
     */
    @TableField(value = "PR_DEPT_OU_ID")
    private String prDeptOuId;

    /**
     * 业务范围
     */
    @TableField(value = "BUSINESS_TYPE_CD")
    private String businessTypeCd;

    /**
     * MKT活动
     */
    @TableField(value = "X_MKT_ID")
    private String xMktId;

    /**
     * 父商机id
     */
    @TableField(value = "PAR_OPTY_ID")
    private String parOptyId;

    /**
     *
     */
    @TableField(value = "data_source")
    private String dataSource;

    /**
     * 用于Oracle ODI用的字段，非空
     */
    @TableField(value = "NAME")
    private String name;

    /**
     *
     */
    @TableField(value = "SPACE_ID")
    private String spaceId;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id")
    private Long tenantId;

    /**
     * 有效标记(Y/N)
     */
    @TableField(value = "enabled_flag")
    private String enabledFlag;

    /**
     * 月报状态
     */
    @TableField(value = "report_status")
    private String reportStatus;

    /**
     * 提交时间
     */
    @TableField(value = "submit_date")
    private Date submitDate;

    /**
     * 失效时间
     */
    @TableField(value = "expiry_date")
    private Date expiryDate;

    /**
     * 报备成功时间
     */
    @TableField(value = "success_date")
    private Date successDate;

    /**
     * 商机当前状态
     */
    @TableField(value = "current_status")
    private String currentStatus;


    /**
     * 迁移状态
     */
    @TableField(value = "migration_status")
    private String migrationStatus;


    /**
     * 商机当前状态
     */
    @TableField(value = "migration_time")
    private Date migrationTime;


    /**
     * 商机当前状态
     */
    @TableField(value = "migration_message")
    private String migrationMessage;

    /**
     *
     */
    @TableField(value = "last_upd")
    private Date lastUpd;

    /**
     *
     */
    @TableField(value = "last_upd_by")
    private String lastUpdBy;

    /**
     *
     */
    @TableField(value = "created")
    private Date created;

    /**
     *
     */
    @TableField(value = "created_by")
    private String createdBy;

    public boolean toProjected(String toProjectOptyCoeds) {
        if (Objects.isNull(toProjectOptyCoeds)) {
            return false;
        }
        List<String> list = Arrays.asList(toProjectOptyCoeds.split(","));
        return list.contains(this.getOptyCd());
    }
}