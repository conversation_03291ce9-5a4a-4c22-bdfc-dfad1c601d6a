package com.zte.mcrm.adapter.projectauthorization.utils;

import com.zte.mcrm.adapter.constant.ProjectAuthStatusEnum;
import com.zte.mcrm.adapter.projectauthorization.dto.OppAuthInfo;
import com.zte.mcrm.adapter.projectauthorization.dto.OpportunityAuthDto;
import com.zte.mcrm.adapter.projectauthorization.service.ProjectAuthorizationService;
import com.zte.mcrm.channel.model.dto.ProjectAuthInfoDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ProjectAuthorizationUtils {

    @Autowired
    private ProjectAuthorizationService projectAuthorizationService;

    private static ProjectAuthorizationService sProjectAuthorizationService;

    @PostConstruct
    public void init() {
        sProjectAuthorizationService = projectAuthorizationService;
    }

    public static Map<String, ProjectAuthInfoDto> getProjectAuthInfo(List<String> opportunityCodes) throws Exception {
        Map<String, ProjectAuthInfoDto> projectAuthInfoDtoMap = new HashMap<>(opportunityCodes.size());
        List<OpportunityAuthDto> opportunityAuthDtos = sProjectAuthorizationService.getProjAuthInfosByOptyCd(opportunityCodes);
        for (OpportunityAuthDto opportunityAuthDto : opportunityAuthDtos) {
            ProjectAuthInfoDto projectAuthInfoDto = setProjectAuthInfoDto(opportunityAuthDto);
            projectAuthInfoDtoMap.put(opportunityAuthDto.getOptyCd(), projectAuthInfoDto);
        }
        return projectAuthInfoDtoMap;
    }

    public static Map<String, List<OppAuthInfo>> getProjectAuthInfoMap(List<String> opportunityCodes) throws Exception {
        Map<String, List<OppAuthInfo>> opportunityAuthMap = new HashMap<>(opportunityCodes.size());
        List<OpportunityAuthDto> opportunityAuthDtos = sProjectAuthorizationService.getProjAuthInfosByOptyCd(opportunityCodes);
        if (CollectionUtils.isNotEmpty(opportunityAuthDtos)) {
            opportunityAuthMap = opportunityAuthDtos.stream().collect(Collectors.toMap(OpportunityAuthDto::getOptyCd, OpportunityAuthDto::getInfos));
        }
        return opportunityAuthMap;
    }

    private static ProjectAuthInfoDto setProjectAuthInfoDto(OpportunityAuthDto opportunityAuthDto){
        ProjectAuthInfoDto returnProjectAuthInfo = new ProjectAuthInfoDto();
        List<OppAuthInfo> oppAuthInfoInfos = opportunityAuthDto.getInfos();
        if (CollectionUtils.isNotEmpty(oppAuthInfoInfos)){
            long projectAuthStatusCount1 = oppAuthInfoInfos.stream()
                    .filter(oppAuthInfo -> ProjectAuthStatusEnum.judgeProjectAuthStatus1(oppAuthInfo.getAuthStatus()))
                    .count();
            long projectAuthStatusCount2 = oppAuthInfoInfos.stream()
                    .filter(oppAuthInfo -> ProjectAuthStatusEnum.judgeProjectAuthStatus2(oppAuthInfo.getAuthStatus()))
                    .count();
            long projectAuthStatusCount3 = oppAuthInfoInfos.stream()
                    .filter(oppAuthInfo -> ProjectAuthStatusEnum.judgeProjectAuthStatus3(oppAuthInfo.getAuthStatus()))
                    .count();
            long projectAuthStatusCount4 = oppAuthInfoInfos.stream()
                    .filter(oppAuthInfo -> ProjectAuthStatusEnum.judgeProjectAuthStatus4(oppAuthInfo.getAuthStatus()))
                    .count();
            String authIdAndStatus = oppAuthInfoInfos.stream().map(ProjectAuthorizationUtils::packageAuthIdAndStatus).collect(Collectors.joining(";"));
            if(projectAuthStatusCount3 > 0){
                returnProjectAuthInfo.setStatusAuth(ProjectAuthStatusEnum.APPLIED);
            }else if(projectAuthStatusCount2 > 0){
                returnProjectAuthInfo.setStatusAuth(ProjectAuthStatusEnum.APPLYING);
            }else if(projectAuthStatusCount1 == oppAuthInfoInfos.size()){
                returnProjectAuthInfo.setStatusAuth(ProjectAuthStatusEnum.NOT_APPLIED);
            }
            returnProjectAuthInfo.setNumberOfAuthorizations(projectAuthStatusCount4);
            returnProjectAuthInfo.setAuthIdAndStatus(authIdAndStatus);
        }
        return returnProjectAuthInfo;
    }

    private static String packageAuthIdAndStatus(OppAuthInfo oppAuthInfo){
        return oppAuthInfo.getAuthorizedCode() + "/" + ProjectAuthStatusEnum.getNameByCode(oppAuthInfo.getAuthStatus());
    }

}
