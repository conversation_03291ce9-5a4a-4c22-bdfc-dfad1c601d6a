package com.zte.mcrm.channel.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class PrmOperationButtonVO {

    @ApiModelProperty(value = "修改")
    private Boolean editFlag = Boolean.FALSE;

    @ApiModelProperty(value = "删除")
    private Boolean deleteFlag = Boolean.FALSE;

    @ApiModelProperty(value = "更新状态")
    private Boolean updateStatusFlag = Boolean.FALSE;

    @ApiModelProperty(value = "激活")
    private Boolean activationFlag = Boolean.FALSE;

    @ApiModelProperty(value = "撤销")
    private Boolean cancelFlag = Boolean.FALSE;

    @ApiModelProperty(value = "催办")
    private Boolean urgeFlag = Boolean.FALSE;

    @ApiModelProperty(value = "查看月报")
    private Boolean readReportFlag = Boolean.FALSE;

    @ApiModelProperty(value = "更新月报")
    private Boolean updateReportFlag = Boolean.FALSE;

    @ApiModelProperty(value = "转立项")
    private Boolean transferProjectFlag = Boolean.FALSE;

    @ApiModelProperty(value = "快速立项")
    private Boolean fastTransferProjectFlag = Boolean.FALSE;

    @ApiModelProperty(value = "创建客户草稿")
    private Boolean createCustomerFlag = Boolean.FALSE;

    @ApiModelProperty(value = "查看")
    private Boolean viewFlag = Boolean.FALSE;

    @ApiModelProperty(value = "指派")
    private Boolean appointFlag = Boolean.FALSE;

    @ApiModelProperty(value = "更新合规状态")
    private Boolean complianceRefreshButtonFlag = Boolean.FALSE;

    @ApiModelProperty(value = "维护主产品信息（单据对应的中兴业务经理或商机管理员）")
    private Boolean maintainMainProdsFlag = Boolean.FALSE;
}
