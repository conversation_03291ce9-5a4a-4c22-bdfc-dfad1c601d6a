package com.zte.mcrm.common.util;

import com.alibaba.fastjson.TypeReference;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.common.consts.CommonConstant;
import com.zte.mcrm.common.consts.HttpMethodEnum;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class EmpInfoUtils {


    public static Map<String, String> getEmpNameMap(List<String> accountIds) throws Exception {

        Set<String> accoutIdSet = new HashSet<>(accountIds);

        Map<String, String> userInfoMap = MsaUtils.invokeBaseService(
                CommonConstant.EMP_INFO_NAME_ID_QUERY_URL,
                HttpMethodEnum.POST,
                accoutIdSet,
                new TypeReference<ServiceData<Map<String, String>>>() {});

        return userInfoMap;
    }
}
