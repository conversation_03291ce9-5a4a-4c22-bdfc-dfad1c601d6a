package com.zte.leadinfo.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@TableName(value ="cx_document")
@Data
public class CxDocumentDO implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "ROW_ID")
    private String rowId;

    /**
     * UDM一个附件单据的id，由业务系统生成
     */
    @TableField(value = "BILL_CODE")
    private String billCode;

    /**
     *
     */
    @TableField(value = "BUSINESS_ID")
    private String businessId;

    /**
     * 是否加密.0加密，1不加密
     */
    @TableField(value = "ENCRYPT")
    private String encrypt;

    /**
     * 业务类型。客户：01
     */
    @TableField(value = "BUSINESS_TYPE")
    private String businessType;

    /**
     * 备注
     */
    @TableField(value = "COMMENTS")
    private String comments;

    /**
     *
     */
    @TableField(value = "CREATED")
    private Date created;

    /**
     *
     */
    @TableField(value = "CREATED_BY")
    private String createdBy;

    /**
     *
     */
    @TableField(value = "LAST_UPD")
    private Date lastUpd;

    /**
     *
     */
    @TableField(value = "LAST_UPD_BY")
    private String lastUpdBy;

}