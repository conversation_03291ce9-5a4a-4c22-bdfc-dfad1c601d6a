<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zte.crm.eva.base.infrastructure.access.mapper.ZteLogMapper">

    <insert id="save" parameterType="com.zte.iss.gcsc.log.model.ZteLogRecord">
        insert into zte_log_record (
        rdc_id,
        http_method_type,
        micro_service_name,
        relative_path,
        local_ip,
        clazz_name,
        method_name,
        args,
        return_value,
        exception,
        consume_time_millis,
        operator
        ) values (
        #{rdcId},
        #{httpMethodType},
        #{microServiceName},
        #{relativePath},
        #{localIp},
        #{clazzName},
        #{methodName},
        #{args},
        #{returnValue},
        #{exception},
        #{consumeTimeMillis},
        #{operator}
        )
    </insert>

</mapper>