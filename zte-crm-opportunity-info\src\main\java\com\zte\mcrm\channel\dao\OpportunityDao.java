package com.zte.mcrm.channel.dao;

import com.zte.mcrm.channel.model.entity.*;
import com.zte.mcrm.channel.model.vo.OpportunityVO;
import com.zte.mcrm.channel.model.vo.PrmOpportunityPendingVO;
import com.zte.mcrm.channel.model.vo.PrmOpportunityVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 *  数据访问接口类 
 * <AUTHOR>
 * @date 2021/09/14 
 */
@Mapper
public interface OpportunityDao {
    /**
     * 根据主键查询
     * <AUTHOR>
     * @param rowId 主键
     * @date 2021/09/14 
     * @return 实体
     */
	Opportunity get(@Param("rowId")String rowId);

	MailOpportunityInfoEntity getMailOpportunityInfoEntity(@Param("rowId")String  rowId);

	Opportunity getByOptyCd(@Param("optyCd") String optyCd);

	OldOpportunityInfoEntity getOldOpportunityByRowId(@Param("rowId")String rowId);

	/**
	 * 根据主键查询-不过滤enabledFlag
	 * <AUTHOR>
	 * @param rowId 主键
	 * @date 2021/09/14 
	 * @return 实体
	 */
	Opportunity getAll(@Param("rowId")String rowId);
	
    /**
     * 查询列表
     * <AUTHOR>
     * @param map 查询条件
     * @date 2021/09/14 
     * @return 实体集合
     */
	List<Opportunity> getList(Map<String, Object> map);

	/**
	 * 软删除草稿
	 * <AUTHOR>
	 * @param rowId 主键
	 * @date 2021/09/14
	 * @return 删除总数
	 */
	int softDeleteDraft(@Param("rowId")String rowId);

	/**
	 * 获取用户为受限制主体的单据,且在审批中的单据
	 * */
	List<String> getOpportunityIdRestricted();

	/**
	 * 删除
	 * <AUTHOR>
	 * @date 2021/09/14
	 * @return 删除总数
	 */
	int deleteByOptyIds(@Param("optyIds")List<String> optyIds);

	int softDeleteByOptyIds(@Param("optyIds")List<String> optyIds);

    /**
     * 统计
     * <AUTHOR>
     * @param map 查询条件
     * @date 2021/09/14 
     * @return 统计总数
     */	
	long getCount(Map<String, Object> map);

    /**
     * 分页查询
     * <AUTHOR>
     * @param map 查询条件
     * @date 2021/09/14 
     * @return 实体集合
     */	
	List<OpportunityVO> getPage(Map<String, Object> map);

	/**
	 * 分页查询
	 * <AUTHOR>
	 * @param map 查询条件
	 * @date 2021/09/14
	 * @return 实体集合
	 */
	List<PrmOpportunityVO> getPrmOpportunityPage(Map<String, Object> map);


	/**
	 * 查询经销商需要填写月报的商机列表
	 * @param map - "crmCustomerCode": 客户编号
	 *            - "month": 当前月 如202203
	 *            - "empNo": 查询人工号
	 *            - "manageAuthFlag": 是否存在管理员权限
	 * @return
	 */
	List<OpportunityVO> getMonthReportOpportunity(Map<String, Object> map);


	/**
	 * 批量查询商机信息-查询待办列表信息
	 * @param pendingIds
	 * @return
	 */
	List<PrmOpportunityPendingVO> getPrmPendingInfos(List<String> pendingIds);


	/**
	 * 根据业务单据id查询流程实例id
	 * @param businessId
	 * @return
	 */
	String getFlowInstanceIdByBusinessId(@Param("businessId") String businessId);

	/**
	 * 根据单据id查询中兴业务经理工号
	 * @param rowId
	 * @return
	 */
	String getBusinessManagerIdByRowId(@Param("rowId") String rowId);

	/**
	 * 失效审批记录
	 * @param approvalRecord
	 * @return
	 */
	int invalidApprovalRecord(ComApprovalRecord approvalRecord);

	/**
	 * 根据 rowId 来更新 主表状态
	 * <AUTHOR>
	 * @param rowId 主键
	 * @date 2021/10/29
	 * @return 更新总数
	 */
	int updateStatusByRowId(@Param("rowId")String rowId, @Param("status")String status);

    /**
     * 获取商机状态=报备成功，赢率是【50%-70%】或【70以上】，商机来源=中兴自建 的商机列表
     * @param rowIds 商机主键
     * @param createdTimeStart 创建时间范围起点
     * @param createdTimeEnd 创建时间范围终点
     * @return 没有传入主键，则查询全部主键数据；没传入时间，则查询全部时间数据
     */
	List<String> getPrmRenewingHighWinRateOpptyIds(@Param("rowIds") List<String> rowIds, @Param("createdTimeStart") String createdTimeStart, @Param("createdTimeEnd") String createdTimeEnd);

	List<PrmOpportunityVO> getPrmOpportunityByIds(@Param("ids") List<String> ids);

	List<String> getPrmOpportunityByFlowIds(@Param("ids") List<String> ids);
}
