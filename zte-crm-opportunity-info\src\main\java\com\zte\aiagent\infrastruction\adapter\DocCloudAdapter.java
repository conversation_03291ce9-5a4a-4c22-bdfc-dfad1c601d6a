package com.zte.aiagent.infrastruction.adapter;

import com.zte.km.udm.api.DiskFileDownloadApi;
import com.zte.km.udm.api.DiskFileUploadApi;
import com.zte.km.udm.model.FileSaveResult;
import com.zte.km.udm.model.ServiceData;
import com.zte.km.udm.model.RetCode;
import com.zte.km.udm.model.FileUploadInput;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;

/**
 * 文档云适配器
 * 负责与文档云系统的对接
 */
@Component
@Slf4j
public class DocCloudAdapter {

    @Value("${tmp.path}")
    private String tmpPath;

    /**
     * 上传文件到文档云
     *
     * @param file 文件
     * @param fileName 文件名
     * @param operator 操作人
     * @return 文件key
     */
    public String uploadFile(File file, String fileName, String operator) {
        try {
            FileUploadInput fileUploadInput = new FileUploadInput();
            fileUploadInput.setFileName(fileName);
            fileUploadInput.setEmpNo(operator);

            ServiceData<FileSaveResult> serviceData = DiskFileUploadApi.fileUpload(file, fileUploadInput);
            if (serviceData != null && RetCode.SUCCESS_CODE.equals(serviceData.getCode().getCode())
                && serviceData.getBo() != null && !StringUtils.isEmpty(serviceData.getBo().getFileKey())) {
                String fileKey = serviceData.getBo().getFileKey();
                log.info("文件上传成功 fileKey={}, operator={}", fileKey, operator);
                return fileKey;
            } else {
                log.error("文档云上传失败 operator={}, fileName={}", operator, fileName);
                throw new RuntimeException("文件上传失败");
            }
        } catch (Exception e) {
            log.error("文档云上传异常 operator={}, fileName={}", operator, fileName, e);
            throw new RuntimeException("文件上传异常", e);
        }
    }

    /**
     * 上传文件到文档云
     *
     * @param file 文件
     * @param fileName 文件名
     * @param operator 操作人
     * @return 文件key
     */
    public String uploadFile(MultipartFile file, String fileName, String operator) {
        try {
            // 将MultipartFile转换为File
            File tempFile = convertMultipartFileToFile(file);
            return uploadFile(tempFile, fileName, operator);
        } catch (Exception e) {
            log.error("文档云上传异常 operator={}, fileName={}", operator, fileName, e);
            throw new RuntimeException("文件上传异常", e);
        }
    }

    /**
     *
     * @param fileKey 文档云附件key
     * @param fileName 附件名称
     * @param operator 操作人
     * @return 文档云下载链接
     */
    public String getDownloadUrl(String fileKey, String fileName, String operator) {
        ServiceData<String> serviceData = DiskFileDownloadApi.getFileDownloadUrl(operator, fileKey, fileName, -1, null, false, null, null, null);
        if (serviceData != null && RetCode.SUCCESS_CODE.equals(serviceData.getCode().getCode())) {
                return serviceData.getBo();
        } else {
            log.error("文档云上传异常 operator={}, fileName={}", operator, fileName);
            throw new RuntimeException("文件下载地址获取异常");
        }
    }

    /**
     * 将MultipartFile转换为File
     */
    private File convertMultipartFileToFile(MultipartFile multipartFile) throws IOException {
        File file = File.createTempFile(tmpPath + "upload_", "_" + multipartFile.getOriginalFilename());
        multipartFile.transferTo(file);
        return file;
    }
}
