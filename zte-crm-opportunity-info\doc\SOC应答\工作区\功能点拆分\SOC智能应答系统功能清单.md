# SOC智能应答系统功能清单

## 项目概述
**项目名称**：SOC智能应答系统  
**开发框架**：Spring Boot 2.x + 中兴MSA框架 4.0.3.4  
**数据库**：MySQL + MyBatis  
**前端技术**：Vue.js + Element UI  

---

## 功能模块清单

### 一、基础设施模块

| 序号 | 功能模块 | 功能点 | 功能描述 | 优先级 | 前端工作量(人天) | 后端工作量(人天) | 负责人 | 备注 |
|------|----------|--------|----------|--------|------------------|------------------|--------|------|
| 1.1 | 数据库设计 | 建表脚本 | 创建7张核心表及索引 | P0 | 0 | 2 | DBA | 严格按照设计文档 |
| 1.2 | 基础框架 | 项目脚手架 | 搭建基础开发框架 | P0 | 1 | 2 | 架构师 | 集成MSA框架 |
| 1.3 | 权限集成 | 统一权限 | 集成中兴UPP权限系统 | P0 | 1 | 3 | 后端 | 角色：SOC智能应答-普通用户 |
| 1.4 | 通用组件 | 审计字段 | 统一审计字段处理 | P0 | 0 | 1 | 后端 | created_by等字段 |
| 1.5 | 异常处理 | 全局异常 | 统一异常处理机制 | P0 | 0 | 1 | 后端 | BusinessException等 |

### 二、任务管理模块

| 序号 | 功能模块 | 功能点 | 功能描述 | 优先级 | 前端工作量(人天) | 后端工作量(人天) | 负责人 | 备注 |
|------|----------|--------|----------|--------|------------------|------------------|--------|------|
| 2.1 | 任务管理 | 任务列表 | 任务列表查询、分页、筛选 | P0 | 2 | 2 | 前端+后端 | 支持关键字搜索 |
| 2.2 | 任务管理 | 创建任务 | 任务创建弹窗及表单验证 | P0 | 2 | 2 | 前端+后端 | 必填字段验证 |
| 2.3 | 任务管理 | 编辑任务 | 任务信息编辑功能 | P0 | 1 | 1 | 前端+后端 | 权限控制 |
| 2.4 | 任务管理 | 复制任务 | 任务复制功能，支持选择是否复制应答结果 | P1 | 2 | 2 | 前端+后端 | 任务名称加"_复制" |
| 2.5 | 任务管理 | 删除任务 | 任务删除功能（逻辑删除） | P1 | 1 | 1 | 前端+后端 | 二次确认 |
| 2.6 | 任务管理 | 任务编码 | 自动生成唯一任务编码 | P0 | 0 | 1 | 后端 | 格式：TASK001 |
| 2.7 | 数据源集成 | GBBS集成 | 获取国家/MTO、客户等信息 | P0 | 1 | 3 | 后端 | 下拉框数据源 |
| 2.8 | 项目集成 | 项目查询 | 集成/projectWeb/myProject接口 | P1 | 1 | 2 | 后端 | InOne系统集成 |

### 三、条目管理模块

| 序号 | 功能模块 | 功能点 | 功能描述 | 优先级 | 前端工作量(人天) | 后端工作量(人天) | 负责人 | 备注 |
|------|----------|--------|----------|--------|------------------|------------------|--------|------|
| 3.1 | 条目管理 | 条目列表 | 条目列表展示、分页、筛选 | P0 | 3 | 3 | 前端+后端 | 按条目汇聚产品 |
| 3.2 | 条目管理 | 单条录入 | 条目单条录入弹窗 | P0 | 2 | 2 | 前端+后端 | 必填字段验证 |
| 3.3 | 条目管理 | 批量导入 | Excel文件批量导入 | P0 | 3 | 4 | 前端+后端 | 模板下载、错误提示 |
| 3.4 | 条目管理 | 列表编辑 | 列表行内编辑功能 | P1 | 2 | 1 | 前端+后端 | 应答、应答说明、备注 |
| 3.5 | 条目管理 | 条目删除 | 单条/批量删除条目 | P1 | 1 | 1 | 前端+后端 | 权限控制 |
| 3.6 | 条目管理 | 数据导出 | 条目数据Excel导出 | P1 | 2 | 2 | 前端+后端 | 按产品筛选导出 |
| 3.7 | 产品管理 | 产品设置 | 条目产品关联管理 | P0 | 2 | 2 | 前端+后端 | GBBS产品树 |
| 3.8 | 产品管理 | 新增产品 | 为条目新增产品关联 | P1 | 2 | 2 | 前端+后端 | 触发自动应答 |
| 3.9 | 标签管理 | 标签添加 | 批量添加标签 | P1 | 2 | 2 | 前端+后端 | 支持新建和选择 |
| 3.10 | 标签管理 | 标签删除 | 批量删除标签 | P1 | 1 | 1 | 前端+后端 | 批量操作 |
| 3.11 | 权限管理 | 指派应答 | 条目指派给指定人员 | P0 | 2 | 2 | 前端+后端 | 选人桥集成 |
| 3.12 | 权限管理 | 只读权限 | 设置任务只读权限 | P1 | 1 | 1 | 前端+后端 | 权限控制 |
| 3.13 | 通知中心 | 应答通知 | 指派通知发送 | P1 | 0 | 2 | 后端 | 邮件+工作通知 |
| 3.14 | 定时刷新 | 状态同步 | 条目状态定时刷新(5s) | P1 | 1 | 1 | 前端 | 不影响编辑操作 |

### 四、数据分析模块

| 序号 | 功能模块 | 功能点 | 功能描述 | 优先级 | 前端工作量(人天) | 后端工作量(人天) | 负责人 | 备注 |
|------|----------|--------|----------|--------|------------------|------------------|--------|------|
| 4.1 | 统计分析 | 总体统计 | 任务总体进度统计 | P0 | 2 | 2 | 前端+后端 | 总条目数、应答进度等 |
| 4.2 | 统计分析 | 产品维度 | 按产品维度统计分析 | P0 | 2 | 2 | 前端+后端 | 产品满足度统计 |
| 4.3 | 统计分析 | 满足度计算 | 满足度公式计算 | P0 | 0 | 1 | 后端 | (FC+PC)/总数×100% |
| 4.4 | 数据可视化 | 图表展示 | 进度和满足度图表 | P1 | 2 | 0 | 前端 | ECharts图表 |
| 4.5 | 权限过滤 | 数据权限 | 按用户权限过滤统计数据 | P0 | 0 | 1 | 后端 | 指派人只看自己的 |

### 五、AI应答模块

| 序号 | 功能模块 | 功能点 | 功能描述 | 优先级 | 前端工作量(人天) | 后端工作量(人天) | 负责人 | 备注 |
|------|----------|--------|----------|--------|------------------|------------------|--------|------|
| 5.1 | AI引擎 | GBBS数据源 | GBBS数据源接口对接 | P0 | 0 | 5 | 后端 | 数据源管理 |
| 5.2 | AI引擎 | 匹配算法 | 条目匹配度计算算法 | P0 | 0 | 8 | 后端 | 语义相似度、上下文匹配 |
| 5.3 | AI引擎 | 自动应答 | AI自动应答流程 | P0 | 1 | 5 | 前端+后端 | 异步处理 |
| 5.4 | AI引擎 | 匹配结果 | AI匹配结果存储管理 | P0 | 0 | 3 | 后端 | Top50结果存储 |
| 5.5 | 应答触发 | 批量应答 | 批量启动AI应答 | P0 | 1 | 2 | 前端+后端 | 开始应答按钮 |
| 5.6 | 应答触发 | 单条应答 | 单条AI应答触发 | P0 | 1 | 1 | 前端+后端 | AI应答按钮 |
| 5.7 | 状态管理 | 应答状态 | 应答状态流转管理 | P0 | 0 | 2 | 后端 | 未应答->应答中->已应答 |
| 5.8 | 相似度分析 | 条目分组 | 基于标签的相似度分析 | P1 | 0 | 3 | 后端 | 条目自动分组 |

### 六、人工应答模块

| 序号 | 功能模块 | 功能点 | 功能描述 | 优先级 | 前端工作量(人天) | 后端工作量(人天) | 负责人 | 备注 |
|------|----------|--------|----------|--------|------------------|------------------|--------|------|
| 6.1 | 应答详情 | 详情页面 | 人工应答详情页面 | P0 | 3 | 2 | 前端+后端 | 条目信息展示 |
| 6.2 | 应答编辑 | 应答结果 | 应答结果编辑功能 | P0 | 3 | 2 | 前端+后端 | 富文本编辑器 |
| 6.3 | 应答编辑 | 满足度编辑 | 满足度下拉选择 | P0 | 1 | 1 | 前端+后端 | FC/PC/NC选择 |
| 6.4 | 应答编辑 | 备注添加 | 应答备注编辑 | P0 | 1 | 1 | 前端+后端 | 文本框输入 |
| 6.5 | 匹配详情 | 匹配列表 | 显示所有匹配结果 | P0 | 3 | 2 | 前端+后端 | 分页卡片展示 |
| 6.6 | 匹配详情 | 筛选功能 | 按满足度、匹配度筛选 | P1 | 2 | 1 | 前端+后端 | 条件筛选 |
| 6.7 | 匹配详情 | 结果应用 | 应用匹配结果到当前应答 | P0 | 2 | 2 | 前端+后端 | 覆盖确认 |
| 6.8 | AI增强 | AI润色 | 集成IGPT润色功能 | P1 | 2 | 3 | 前端+后端 | IGPT接口对接 |
| 6.9 | AI增强 | AI翻译 | 集成IGPT翻译功能 | P1 | 2 | 3 | 前端+后端 | IGPT接口对接 |
| 6.10 | 版本管理 | 历史版本 | 应答历史版本查看 | P1 | 2 | 2 | 前端+后端 | 版本选择下拉 |
| 6.11 | 版本管理 | 变更记录 | 应答变更历史记录 | P1 | 0 | 2 | 后端 | 变更类型记录 |
| 6.12 | 产品切换 | 产品选择 | 同条目不同产品切换 | P0 | 2 | 1 | 前端+后端 | 产品Tab切换 |

### 七、快捷应答模块

| 序号 | 功能模块 | 功能点 | 功能描述 | 优先级 | 前端工作量(人天) | 后端工作量(人天) | 负责人 | 备注 |
|------|----------|--------|----------|--------|------------------|------------------|--------|------|
| 7.1 | 快捷入口 | 快捷页面 | 快捷应答页面设计 | P1 | 2 | 1 | 前端+后端 | 简化表单 |
| 7.2 | 快捷入口 | 字段填写 | 必填字段验证 | P1 | 1 | 1 | 前端+后端 | 数据源、产品必填 |
| 7.3 | 个人任务 | 任务创建 | 自动创建个人任务 | P1 | 1 | 2 | 前端+后端 | is_personal=1 |
| 7.4 | 个人任务 | 权限控制 | 个人任务不可删除 | P1 | 1 | 1 | 前端+后端 | 删除按钮隐藏 |
| 7.5 | 个人任务 | 任务跳转 | 跳转到任务详情页 | P1 | 1 | 0 | 前端 | 页面路由跳转 |

### 八、Agent人机交互模块

| 序号 | 功能模块 | 功能点 | 功能描述 | 优先级 | 前端工作量(人天) | 后端工作量(人天) | 负责人 | 备注 |
|------|----------|--------|----------|--------|------------------|------------------|--------|------|
| 8.1 | Agent框架 | 对话界面 | Agent聊天交互界面 | P2 | 5 | 2 | 前端+后端 | 聊天组件 |
| 8.2 | Agent框架 | 意图识别 | 自然语言意图识别 | P2 | 0 | 8 | 后端 | NLP处理 |
| 8.3 | Task工具 | 创建任务 | 自然语言创建任务 | P2 | 1 | 3 | 前端+后端 | 参数解析 |
| 8.4 | Query工具 | 任务查询 | 自然语言查询任务 | P2 | 1 | 3 | 前端+后端 | 语义查询 |
| 8.5 | Query工具 | 条目查询 | 自然语言查询条目 | P2 | 1 | 3 | 前端+后端 | 条件解析 |
| 8.6 | Query工具 | 数据分析 | 自然语言数据分析 | P2 | 1 | 2 | 前端+后端 | 统计查询 |
| 8.7 | Answer工具 | 单条应答 | 自然语言应答条目 | P2 | 1 | 4 | 前端+后端 | 应答解析 |
| 8.8 | Answer工具 | 批量应答 | 自然语言批量应答 | P2 | 1 | 3 | 前端+后端 | 批量操作 |
| 8.9 | File工具 | Excel导入 | 文件上传及进度显示 | P2 | 2 | 3 | 前端+后端 | 实时进度 |
| 8.10 | Export工具 | 数据导出 | 自然语言导出数据 | P2 | 1 | 2 | 前端+后端 | 条件解析 |
| 8.11 | Batch工具 | 指派操作 | 自然语言批量指派 | P2 | 1 | 3 | 前端+后端 | 批量权限 |

### 九、系统集成模块

| 序号 | 功能模块 | 功能点 | 功能描述 | 优先级 | 前端工作量(人天) | 后端工作量(人天) | 负责人 | 备注 |
|------|----------|--------|----------|--------|------------------|------------------|--------|------|
| 9.1 | 消息队列 | Kafka集成 | 异步消息处理 | P1 | 0 | 3 | 后端 | AI应答异步处理 |
| 9.2 | 缓存系统 | Redis集成 | 缓存常用数据 | P1 | 0 | 2 | 后端 | 产品树、用户信息 |
| 9.3 | 文件存储 | 文件上传 | Excel文件存储管理 | P0 | 1 | 2 | 前端+后端 | 文件服务器 |
| 9.4 | 接口文档 | Swagger集成 | API文档自动生成 | P0 | 0 | 1 | 后端 | 接口注解 |
| 9.5 | 日志管理 | 操作日志 | 用户操作日志记录 | P1 | 0 | 2 | 后端 | 审计日志 |
| 9.6 | 国际化 | 多语言支持 | 中英文双语环境 | P1 | 3 | 1 | 前端+后端 | i18n配置 |

---

## 优先级说明
- **P0**：核心功能，必须实现
- **P1**：重要功能，建议实现  
- **P2**：增强功能，后续实现

## 工作量统计

| 模块 | 前端工作量 | 后端工作量 | 总工作量 |
|------|------------|------------|----------|
| 基础设施模块 | 2人天 | 9人天 | 11人天 |
| 任务管理模块 | 10人天 | 14人天 | 24人天 |
| 条目管理模块 | 21人天 | 21人天 | 42人天 |
| 数据分析模块 | 6人天 | 6人天 | 12人天 |
| AI应答模块 | 3人天 | 29人天 | 32人天 |
| 人工应答模块 | 24人天 | 24人天 | 48人天 |
| 快捷应答模块 | 6人天 | 5人天 | 11人天 |
| Agent交互模块 | 15人天 | 36人天 | 51人天 |
| 系统集成模块 | 4人天 | 11人天 | 15人天 |
| **总计** | **91人天** | **155人天** | **246人天** |

## 建议开发排期

### 第一阶段（2个月）- 核心功能
- 基础设施模块
- 任务管理模块  
- 条目管理模块
- 数据分析模块

### 第二阶段（2个月）- AI功能
- AI应答模块
- 人工应答模块
- 系统集成模块

### 第三阶段（1个月）- 增强功能  
- 快捷应答模块
- Agent交互模块

## 人员配置建议
- **项目经理**：1人
- **架构师**：1人  
- **前端开发**：2-3人
- **后端开发**：3-4人
- **测试工程师**：2人
- **UI设计师**：1人

---

**备注**：
1. 工作量估算基于中等技术水平开发人员
2. 包含了详细设计、编码、单元测试的工作量
3. 不包含系统测试、部署运维等工作量
4. 建议根据团队实际情况调整工作量估算 