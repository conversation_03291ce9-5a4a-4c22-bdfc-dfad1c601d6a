<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zte.mcrm.common.upload.dao.base.ComUploadFileDao" >
    <!-- 屏蔽mybatis的2级缓存  <cache />  -->
  
    <!-- 记录和实体映射配置 -->
    <resultMap id="BaseMap" type="com.zte.mcrm.common.upload.model.entity.ComUploadFile" >
        <id column="id" property="id" jdbcType="BIGINT" />
		<result column="upload_type" property="uploadType" jdbcType="VARCHAR" />	
		<result column="bill_id" property="billId" jdbcType="VARCHAR" />
		<result column="doc_name" property="docName" jdbcType="VARCHAR" />	
		<result column="doc_desc" property="docDesc" jdbcType="VARCHAR" />	
		<result column="file_type" property="fileType" jdbcType="VARCHAR" />	
		<result column="extend_file_name" property="extendFileName" jdbcType="VARCHAR" />	
		<result column="file_order" property="fileOrder" jdbcType="INTEGER" />	
		<result column="is_encrypt" property="isEncrypt" jdbcType="VARCHAR" />	
		<result column="dme_key" property="dmeKey" jdbcType="VARCHAR" />	
		<result column="secret_level" property="secretLevel" jdbcType="TINYINT" />	
		<result column="enabled_flag" property="enabledFlag" jdbcType="CHAR" />	
		<result column="created_by" property="createdBy" jdbcType="VARCHAR" />	
		<result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />	
		<result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR" />	
		<result column="last_updated_date" property="lastUpdatedDate" jdbcType="TIMESTAMP" />	
    </resultMap>

    <sql id="base_column">
        t.id ,
        t.upload_type ,
        t.bill_id ,
        t.doc_name ,
        t.doc_desc ,
        t.file_type ,
        t.extend_file_name ,
        t.file_order ,
        t.is_encrypt ,
        t.dme_key ,
        t.secret_level ,
        t.enabled_flag ,
        t.created_by ,
        t.created_date ,
        t.last_updated_by ,
        t.last_updated_date 
    </sql>

    <sql id="base_where">
        <if test="id != null"> and t.id = #{id}</if>
        <if test="uploadType != null"> and t.upload_type = #{uploadType}</if>
        <if test="billId != null"> and t.bill_id = #{billId}</if>
        <if test="docName != null and docName != ''"> and t.doc_name = #{docName}</if>
        <if test="docDesc != null and docDesc != ''"> and t.doc_desc = #{docDesc}</if>
        <if test="fileType != null and fileType != ''"> and t.file_type = #{fileType}</if>
        <if test="extendFileName != null and extendFileName != ''"> and t.extend_file_name = #{extendFileName}</if>
        <if test="fileOrder != null"> and t.file_order = #{fileOrder}</if>
        <if test="isEncrypt != null and isEncrypt != ''"> and t.is_encrypt = #{isEncrypt}</if>
        <if test="dmeKey != null and dmeKey != ''"> and t.dme_key = #{dmeKey}</if>
		<if test="secretLevel != null and secretLevel != ''">and t.secret_level = #{secretLevel}</if>
        <if test="createdBy != null and createdBy != ''"> and t.created_by = #{createdBy}</if>
        <if test="createdDate != null"> and t.created_date = #{createdDate}</if>
        <if test="lastUpdatedBy != null and lastUpdatedBy != ''"> and t.last_updated_by = #{lastUpdatedBy}</if>
        <if test="lastUpdatedDate != null"> and t.last_updated_date = #{lastUpdatedDate}</if>
    and t.tenant_id = #{headerTenantId}
    </sql>

	<sql id="fuzzy_base_where">
		and t.enabled_flag='Y'
		<if test="uploadType != null"> and t.upload_type = #{uploadType}</if>
		<if test="docName != null and docName != ''"> and t.doc_name like concat('%',#{docName},'%') </if>
		<if test="docDesc != null and docDesc != ''"> and t.doc_desc like concat('%',#{docDesc},'%') </if>
		<if test="dmeKey != null and dmeKey != ''"> and t.dme_key like concat('%',#{dmeKey},'%')</if>
		<if test="lastUpdatedBy != null and lastUpdatedBy != ''"> and t.last_updated_by = #{lastUpdatedBy}</if>
		<if test="beginLastUpdatedDate !=null and beginLastUpdatedDate !=''" >
			and DATE_FORMAT(t.last_updated_date,'%Y-%m-%d') <![CDATA[ >= ]]> str_to_date(#{beginLastUpdatedDate,jdbcType=VARCHAR},'%Y-%m-%d')
		</if>
		<if test="endLastUpdatedDate !=null and endLastUpdatedDate != ''">
			and DATE_FORMAT(t.last_updated_date,'%Y-%m-%d') <![CDATA[ <= ]]> str_to_date(#{endLastUpdatedDate,jdbcType=VARCHAR},'%Y-%m-%d')
		</if>
	</sql>
    <!-- 获取一条记录 -->
    <select id="get"  resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM com_upload_file t
        WHERE t.id=#{id, jdbcType=BIGINT}
		and t.enabled_flag = 'Y'
	</select>
 
    <!-- 获取符合条件的记录列表 -->
    <select id="getList" parameterType="java.util.Map" resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM com_upload_file t
        WHERE 1=1
		and t.enabled_flag = 'Y'
        <include refid="base_where"/>
    </select>

    <!-- 删除一条记录 -->
    <delete id="delete" >
        DELETE FROM com_upload_file
        WHERE
        id = #{id, jdbcType=BIGINT}
    </delete>

	<!-- 删除一条记录 -->
	<delete id="deleteByOpptyId" >
		DELETE FROM com_upload_file
		WHERE bill_id in (
		<foreach collection ="optyIds" item="item" index= "index" separator =",">
			#{item, jdbcType=VARCHAR}
		</foreach>)
	</delete>

	<update id="softDeleteByOpptyId" >
		update com_upload_file set enabled_flag = 'N'
		WHERE bill_id in (
		<foreach collection ="optyIds" item="item" index= "index" separator =",">
			#{item, jdbcType=VARCHAR}
		</foreach>)
	</update>

    <!--添加一条记录 -->
    <insert id="insert" parameterType="com.zte.mcrm.common.upload.model.entity.ComUploadFile" >
        INSERT INTO com_upload_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
		    <if test="id != null">id ,</if>
		    <if test="uploadType != null">upload_type ,</if>
		    <if test="billId != null">bill_id ,</if>
		    <if test="docName != null">doc_name ,</if>
		    <if test="docDesc != null">doc_desc ,</if>
		    <if test="fileType != null">file_type ,</if>
		    <if test="extendFileName != null">extend_file_name ,</if>
		    <if test="fileOrder != null">file_order ,</if>
		    <if test="isEncrypt != null">is_encrypt ,</if>
		    <if test="dmeKey != null">dme_key ,</if>
		    <if test="secretLevel != null">secret_level ,</if>
		    <if test="enabledFlag != null">enabled_flag ,</if>
		    <if test="createdBy != null">created_by ,</if>
		    <if test="createdDate != null">created_date ,</if>
		    <if test="lastUpdatedBy != null">last_updated_by ,</if>
		    <if test="lastUpdatedDate != null">last_updated_date ,</if>
    		tenant_id
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
    	    <if test="id != null">#{id, jdbcType=BIGINT} ,</if>
    	    <if test="uploadType != null">#{uploadType, jdbcType=VARCHAR} ,</if>
    	    <if test="billId != null">#{billId, jdbcType=BIGINT} ,</if>
    	    <if test="docName != null">#{docName, jdbcType=VARCHAR} ,</if>
    	    <if test="docDesc != null">#{docDesc, jdbcType=VARCHAR} ,</if>
    	    <if test="fileType != null">#{fileType, jdbcType=VARCHAR} ,</if>
    	    <if test="extendFileName != null">#{extendFileName, jdbcType=VARCHAR} ,</if>
    	    <if test="fileOrder != null">#{fileOrder, jdbcType=INTEGER} ,</if>
    	    <if test="isEncrypt != null">#{isEncrypt, jdbcType=VARCHAR} ,</if>
    	    <if test="dmeKey != null">#{dmeKey, jdbcType=VARCHAR} ,</if>
    	    <if test="secretLevel != null">#{secretLevel, jdbcType=TINYINT} ,</if>
    	    <if test="enabledFlag != null">#{enabledFlag, jdbcType=CHAR} ,</if>
    	    <if test="createdBy != null">#{createdBy, jdbcType=VARCHAR} ,</if>
    	    <if test="createdDate != null">#{createdDate, jdbcType=TIMESTAMP} ,</if>
    	    <if test="lastUpdatedBy != null">#{lastUpdatedBy, jdbcType=VARCHAR} ,</if>
    	    <if test="lastUpdatedDate != null">#{lastUpdatedDate, jdbcType=TIMESTAMP} ,</if>
			#{headerTenantId, jdbcType=BIGINT}
        </trim>
    </insert>

    <!--批量添加记录 -->
    <insert id="insertByBatch" parameterType="java.util.List" >
        INSERT INTO com_upload_file
        (
		    id ,
    	    upload_type ,
    	    bill_id ,
    	    doc_name ,
    	    doc_desc ,
    	    file_type ,
    	    extend_file_name ,
    	    file_order ,
    	    is_encrypt ,
    	    dme_key ,
    	    secret_level ,
    	    enabled_flag ,
    	    created_by ,
    	    created_date ,
    	    last_updated_by ,
    	    last_updated_date ,
    		tenant_id
        )
        VALUES
        <foreach collection ="list" item="item" index= "index" separator =",">
        (
    	    #{item.id, jdbcType=BIGINT} ,
    	    #{item.uploadType, jdbcType=VARCHAR} ,
    	    #{item.billId, jdbcType=BIGINT} ,
    	    #{item.docName, jdbcType=VARCHAR} ,
    	    #{item.docDesc, jdbcType=VARCHAR} ,
    	    #{item.fileType, jdbcType=VARCHAR} ,
    	    #{item.extendFileName, jdbcType=VARCHAR} ,
    	    #{item.fileOrder, jdbcType=INTEGER} ,
    	    #{item.isEncrypt, jdbcType=VARCHAR} ,
    	    #{item.dmeKey, jdbcType=VARCHAR} ,
    	    #{item.secretLevel, jdbcType=TINYINT} ,
    	    #{item.enabledFlag, jdbcType=CHAR} ,
    	    #{item.createdBy, jdbcType=VARCHAR} ,
    	    #{item.createdDate, jdbcType=TIMESTAMP} ,
    	    #{item.lastUpdatedBy, jdbcType=VARCHAR} ,
    	    #{item.lastUpdatedDate, jdbcType=TIMESTAMP} ,
			#{headerTenantId, jdbcType=BIGINT}
        )
        </foreach>
    </insert>
  
    <!-- 更新一条记录 -->
    <update id="update" parameterType="com.zte.mcrm.common.upload.model.entity.ComUploadFile" >
        UPDATE com_upload_file
        <set>
		    <if test="uploadType != null">upload_type=#{uploadType, jdbcType=VARCHAR} ,</if>
		    <if test="billId != null">bill_id=#{billId, jdbcType=VARCHAR} ,</if>
		    <if test="docName != null">doc_name=#{docName, jdbcType=VARCHAR} ,</if>
		    <if test="docDesc != null">doc_desc=#{docDesc, jdbcType=VARCHAR} ,</if>
		    <if test="fileType != null">file_type=#{fileType, jdbcType=VARCHAR} ,</if>
		    <if test="extendFileName != null">extend_file_name=#{extendFileName, jdbcType=VARCHAR} ,</if>
		    <if test="fileOrder != null">file_order=#{fileOrder, jdbcType=INTEGER} ,</if>
		    <if test="isEncrypt != null">is_encrypt=#{isEncrypt, jdbcType=VARCHAR} ,</if>
		    <if test="dmeKey != null">dme_key=#{dmeKey, jdbcType=VARCHAR} ,</if>
		    <if test="secretLevel != null">secret_level=#{secretLevel, jdbcType=TINYINT} ,</if>
		    <if test="enabledFlag != null">enabled_flag=#{enabledFlag, jdbcType=CHAR} ,</if>
		    <if test="createdBy != null">created_by=#{createdBy, jdbcType=VARCHAR} ,</if>
		    <if test="createdDate != null">created_date=#{createdDate, jdbcType=TIMESTAMP} ,</if>
		    <if test="lastUpdatedBy != null">last_updated_by=#{lastUpdatedBy, jdbcType=VARCHAR} ,</if>
		    <if test="lastUpdatedDate != null">last_updated_date=#{lastUpdatedDate, jdbcType=TIMESTAMP} ,</if>
	    </set>
        WHERE
        id=#{id, jdbcType=BIGINT}
    </update>
	<!-- 根据dmekey删除记录 -->
	<update id="deleteByDmeKey" parameterType="com.zte.mcrm.common.upload.model.entity.ComUploadFile" >
		UPDATE com_upload_file
		<set>
			<if test="enabledFlag != null">enabled_flag=#{enabledFlag, jdbcType=CHAR} ,</if>
			<if test="createdBy != null">created_by=#{createdBy, jdbcType=VARCHAR} ,</if>
			<if test="createdDate != null">created_date=#{createdDate, jdbcType=TIMESTAMP} ,</if>
			<if test="lastUpdatedBy != null">last_updated_by=#{lastUpdatedBy, jdbcType=VARCHAR} ,</if>
			<if test="lastUpdatedDate != null">last_updated_date=#{lastUpdatedDate, jdbcType=TIMESTAMP} ,</if>
		</set>
		WHERE
		dme_key=#{dmeKey, jdbcType=VARCHAR}
		and tenant_id = #{headerTenantId}
	</update>
    <!-- 翻页函数:获取符合条件的记录数 -->
    <select id="getCount" parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT count(*) FROM com_upload_file t
        WHERE 1=1
        <include refid="base_where"/>
    </select>
	
    <!-- 翻页函数:获取一页的记录集 -->
    <select id="getPage" parameterType="java.util.Map" resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM com_upload_file t
        WHERE 1=1
        <include refid="base_where"/>
        <if test="orderField != null and orderField == 'id'"> order by t.id
            <if test="order != null and order == 'desc'"> desc </if>
        </if>
        <if test="startRow != null and rowSize != null"> limit #{startRow},#{rowSize} </if>
    </select>

	<!-- 根据给定的已上传附件的ID更新附件次序 -->
	<update id="updateUploadFileOrder" parameterType="java.util.List">
		<foreach collection="list" index="index" item="item" separator=";">
			UPDATE com_upload_file SET file_order = #{item.fileOrder} WHERE id = #{item.id}
		</foreach>
	</update>

	<!-- 根据 demKey 更新 secretLevel -->
	<update id="updateUploadFileSecretLevel" parameterType="com.zte.mcrm.common.upload.model.entity.ComUploadFile" >
		UPDATE com_upload_file
		<set>
			<if test="lastUpdatedDate != null">last_updated_date=#{lastUpdatedDate, jdbcType=TIMESTAMP} ,</if>
			<if test="secretLevel != null">secret_level=#{secretLevel, jdbcType=TINYINT} ,</if>
			<if test="lastUpdatedBy != null">last_updated_by=#{lastUpdatedBy, jdbcType=VARCHAR} ,</if>
		</set>
		WHERE
		enabled_flag = 'Y'
		and dme_key=#{dmeKey, jdbcType=VARCHAR}
	</update>

	<select id="queryUploadFileInBatch" resultMap="BaseMap">
		select id, upload_type, bill_id, doc_name, dme_key, file_order
		from com_upload_file
		where enabled_flag = 'Y'
		and bill_id in
		<foreach collection="billIds" index="index" item="item" separator="," open="(" close=")">
         #{item, jdbcType=VARCHAR}
		</foreach>
		<if test="uploadType != null"> and upload_type = #{uploadType}</if>
	</select>

	<!-- 翻页函数:获取符合条件的记录数 -->
	<select id="getFuzzyCount" parameterType="java.util.Map" resultType="java.lang.Long">
		SELECT count(1) FROM com_upload_file t
		WHERE 1=1
		<include refid="fuzzy_base_where"/>
	</select>

	<!-- 翻页函数:获取一页的记录集 -->
	<select id="getFuzzyPage" parameterType="java.util.Map" resultMap="BaseMap">
		SELECT <include refid="base_column"/>
		FROM com_upload_file t
		WHERE 1=1
		<include refid="fuzzy_base_where"/>
		order by last_updated_date desc
		<if test="startRow != null and rowSize != null"> limit #{startRow},#{rowSize} </if>

	</select>

	<select id="getSecretLevel" parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT secret_level
		FROM com_upload_file
		WHERE enabled_flag = 'Y'
		AND dme_key=#{dmeKey, jdbcType=VARCHAR}
	</select>

	<select id="getFileType"  resultMap="BaseMap">
		SELECT doc_name,file_type
		FROM com_upload_file
		WHERE enabled_flag = 'Y'
		AND dme_key=#{dmeKey, jdbcType=VARCHAR}
	</select>
</mapper>
