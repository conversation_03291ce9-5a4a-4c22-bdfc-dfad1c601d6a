 package com.zte.mcrm.common.consts;

 import java.util.Arrays;

 /**
  * Common常量类
  * <AUTHOR>
  * @date 2019/05/30
  */
 public class CommonConstant {
     private CommonConstant() {
         throw new IllegalStateException("CommonConstant class");

     }
     /**查询用户信息 姓名+id*/
     public static final String EMP_INFO_NAME_ID_QUERY_URL = "/ucs/account/map/idtouiname";
     /**
      * Yes
      */
     public static final String COMMON_FLAG_Y = "Y";
     /**
      * True
      */
     public static final String COMMON_FLAG_T = "T";
     /**
      * No
      */
     public static final String COMMON_FLAG_R = "R";
     /**
      * No
      */
     public static final String COMMON_FLAG_N = "N";
     /**
      * False
      */
     public static final String COMMON_FLAG_F = "F";
     
     /** 下划线*/
     public static final String UNDER_LINE = "_";
     
     /** 中线 横杠*/
     public static final String MID_LINE = "-";

     /** 横杠*/
     public static final String LINE ="－";
     
     /**
      * 分隔符 " - "两边有空格
      * 横杠
      */
     public static final String SPLIT_RUNG = " - ";
     
     /** 点       .     */
     public static final String POINT = ".";
     
     /** 逗号     */
     public static final String COMMA = ",";
     
     /** 冒号       */
     public static final String COLON = ":";

     /** 分号       */
     public static final String SEMICOLON = ";";
     
     /** 等号       */
     public static final String EQUAL_SIGN = "=";
     
     /** 单个双引号 " */
     public static final String SINGLE_DOUBLE_QUOTATION_MARK = "\"";
     
     /**
      * Yes
      */
     public static final String AT = "@";
     

     /** 0*/
     public static final int ZERO = 0;
     
     /** 1*/
     public static final int ONE = 1;

     /** 10*/
     public static final int TEN = 10;

     /** 50*/
     public static final int FIFTY = 50;

     /** 100*/
     public static final int HUNDRED = 100;

     /** 200*/
     public static final int TWO_HUNDRED = 200;

     /** 1000*/
     public static final int ONE_THOUSAND = 1000;

     /** 1024*/
     public static final int FILE_SIZE_UNIT = 1024;

     /**
      * -1 权限控制
      */
     public static final String NEGATIVE_ONE = "-1";
     
     /**
      * pageNo 1
      */
     public static final String PAGE_NO = "1";  
     
     /**
      * pageSize 10
      */
     public static final String PAGE_SIZE = "10";
     
     /**
      * pageSize 100
      */
     public static final String PAGE_SIZE_HUNDRED = "100";

     /** YYYY_MM_DD  12小时  **/
     public static final String YYYY_MM_DD = "yyyyMMdd";

     /** yyyyMMddHHmmss  24小时  **/
     public static final String YYYY_MM_DD_24 = "yyyyMMddHHmm";

     /**
      * yyyy-MM-dd
      */
     public static final String YYYY_MM_DD_GAP = "yyyy-MM-dd";
     
     /**
      * yyyy/MM/dd
      */
     public static final String YYYY_MM_DD_SLASH = "yyyy/MM/dd";

     /** YYYY_MM_DD_HH_MM_SS  12小时     */
     public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd hh:mm:ss";
     
     /** yyyy-MM-dd HH:mm:ss 24小时制度       */
     public static final String YYYY_MM_DD_HH_MM_SS24 = "yyyy-MM-dd HH:mm:ss";
     
     /** yyyy/MM/dd HH:mm:ss 24小时制度       */
     public static final String YYYY_MM_DD_HH_MM_SS24_SLASH = "yyyy/MM/dd HH:mm:ss";

     /**
      * 00:00:00
      */
     public static final String ZERO_HOUR_ZERO_MINUTE_ZERO_SECOND = "00:00:00";
     
     /**
      * 23:59:59
      */
     public static final String TWENTY_THREE_HOURS_FIFTY_NINE_MINUTES_FIFTY_NINE_SECONDS = "23:59:59";
     
     /** zh_CN   中文   */
     public static final String ZH_CN = "zh_CN";
     
     /** ZHS    中文  */
     public static final String ZHS = "ZHS";
     
     /** en_US   英语-美国  */
     public static final String EN_US = "en_US";
     
     /** 排序顺序 asc  */
     public static final String SORT_ORDER_ASC = "asc";
     
     /** 排序顺序 desc  */
     public static final String SORT_ORDER_DESC = "desc";
     
     /**ENABLED_FLAG**/
     public static final String ENABLED_FLAG = "enabledFlag";

     /**排序字段**/
     public static final String ORDER_FIELD = "orderField";
     
     /**
      * createdBy
      */
     public static final String FIELD_CREATED_BY = "createdBy";
     
     /**
      * lastUpdatedBy
      */
     public static final String FIELD_LAST_UPDATED_BY = "lastUpdatedBy";
     
     /**
      * enabledFlag
      */
     public static final String FIELD_ENABLED_FLAG = "enabledFlag";
     
     /**
      * system
      */
     public static final  String SYSTEM = "system";
     
     /**
      * creationDate
      */
     public static final String FIELD_CREATION_DATE = "creationDate";

     /**
      * lastUpdatedDate
      */
     public static final String FIELD_LAST_UPDATED_DATE = "lastUpdatedDate";


     /** null */
     public static final String NULL = "null";
     
     /**
      * undefined
      */
     public static final String UNDEFINED = "undefined";

     /** result */
     public static final String RESULT = "result";

     /**
      * 通用我司邮箱后缀
      */
     public static final String EMAIL_LABEL = "@zte.com.cn";

     /**
      * 导出excel文件后缀
      */
     public static final String ATTACHMENT_SUFFIX_XLSX = ".xlsx";

     /**
      * 压缩包文件后缀
      */
     public static final String FILE_SUFFIX_ZIP = ".zip";

     /**
      * 业务ID
      */
     public static final String BUSINESS_ID = "businessId";
     
     /**
      * 参数类型
      */
     public static final String PARAMTYPE0 = "paramType0";
     
     /**
      *参数值
      */
     public static final String PARAMVALUE0 = "paramValue0";
     
     /**
      * 参数类型
      */
     public static final String PARAMTYPE1 = "paramType1";
     
     /**
      *参数值
      */
     public static final String PARAMVALUE1 = "paramValue1";
     
     /**
      * 参数类型
      */
     public static final String PARAMTYPE2 = "paramType2";
     
     /**
      *参数值
      */
     public static final String PARAMVALUE2 = "paramValue2";
     
     /**
      * 参数类型
      */
     public static final String PARAMTYPE3 = "paramType3";
     
     /**
      *参数值
      */
     public static final String PARAMVALUE3 = "paramValue3";
     
     /**
      * 调用的方法名
      */
     public static final String CALL_METHOD_NAME = "callMethodName";
     
     /**
      * 调用的类名
      */
     public static final String CALL_CLASS_NAME = "callClassName";
     
     /**
      * 异常信息
      */
     public static final String MESSAGE = "message";
     
     /** serialVersionUID*/
     public static final String SERIAL_VERSION_UID = "serialVersionUID";

     /** ZTE  **/
     public static final String ZTE = "ZTE";

     /**ServiceData - code**/
     public static final String STR_CODE = "code";
     
     /**ServiceData - msg**/
     public static final String STR_MSG = "msg";
     
     /**ServiceData - bo**/
     public static final String STR_BO = "bo";
     
     /**ServiceData - FILEURL**/
     public static final String STR_FILEURL = "fileURL";
     
     /** title */
     public static final String TITLE = "title";
     
     /** entity */
     public static final String ENTITY = "entity";
     
     /** data */
     public static final String DATA = "data";
     
     /** 异常是返回ServiceDate中other-map存储的key*/
     public static final String RETCODE_MSG = "msg";
     
     /**
      * 分页查询参数-缺省页
      */
     public static final int DEFAULT_PAGE_NUM = 1;

     /**
      * 分页查询参数-缺省页记录数
      */
     public static final int DEFAULT_PAGE_SIZE = 10;

     /**
      * 微服务名称
      */
     public static final String SERVICE_NAME="zte-crm-ichannel-base";

     public static final String FILEPATH_1 = "../";

     public static final String FILEPATH_2 = "..\\";

     /**
      * 节点单一评审规则
      */
     public static final String SINGLE_RULE_TYPE = "single";

     /**
      * PRM系统编码
      */
     public static final String PRM_SYS_CODE = "PRM";

     public static final String MAIL_PATTERN = "<?xml version='1.0' encoding='utf-8'?>" +
             "<mail-config>" +
             "<head>" +
             "<systemType>%s</systemType>" +
             "<notUsedMailtemplateEx>false</notUsedMailtemplateEx>" +
             "</head>" +
             "<mails>" +
             "<mail>" +
             "<mailtoIsOne>%s</mailtoIsOne>" +
             "<importance>%d</importance>" +
             "<priority>%d</priority>" +
             "<subscribe>0</subscribe>" +
             "<forward>%d</forward>" +
             "<title>%s</title>" +
             "<mailfrom>%s</mailfrom>" +
             "<mailto>%s</mailto>" +
             "<mailcc>%s</mailcc>" +
             "<mailbcc>%s</mailbcc>" +
             "%s" +
             "</mail>" +
             "</mails>" +
             "</mail-config>";

     public  static final String MAIL_BODY_PATTERN = "<mailBody id='%s'>" +
             "<WarmCall>%s</WarmCall>" +
             "<MailSysName>%s</MailSysName>" +
             "<ClickLook>%s</ClickLook >" +
             "<LinkAdd>%s</LinkAdd>" +
             "<MainText>%s</MainText>" +
             "<BottomTip><![CDATA[#]]></BottomTip >" +
             "</mailBody>";

     public static final String HTTP = "http";

     /**
      * 查询组织树：获取子节点数据 url
      */
     public static final String ORGANIZATION_TREE_QUERY_URL = "/tree/organization/getDescendants";

     /**
      * 查询产品树：获取子节点数据 url
      */
     public static final String PRODUCT_TREE_QUERY_URL = "/product/tree/getByItemNo";

     /**
      * 查询组织树：根据树类型、是否可见、是否业绩单位、机构id、机构名称、机构标签组合查询机构树，支持根据机构id批量查询，支持分页
      */
     public static final String ORG_DATA_CONDITION_URL = "/tree/org/data/condition";

     /**
      * 根据组织编号获取组织信息
      */
     public static final String HRM_GETORGINFO = "/hrm/getOrgInfo";

     /**
      * GEBSDC表示政企中国组织
      */
     public static final String ORGANIZATION_CHINA = "GEBSDC";

     /**
      * EnableFlagEnum
      */
    public enum EnableFlagEnum {
        /**
         *Y
         */
         Y("Y", "是", "Yes"),
        /**
         *N
         */
         N("N", "否", "No"),
        /**
         * 空
         */
         EMPTY("", "", "");

         private String key;
         private String zhMsg;
         private String enMsg;

        /**
         * 构造
         * @param key
         * @param zhMsg
         * @param enMsg
         */
         EnableFlagEnum(String key, String zhMsg, String enMsg) {
             this.key = key;
             this.zhMsg = zhMsg;
             this.enMsg = enMsg;
         }

         /**
          * 根据key 和lang 获取国际化后的值
          * @param key
          * @param lang
          * @return
          */
         public static String getMsgByLang(String key, String lang){
             EnableFlagEnum[] values = EnableFlagEnum.values();
             EnableFlagEnum enableFlagEnum = Arrays.stream(values).filter(x -> x.key.equalsIgnoreCase(key)).findAny().orElse(EMPTY);
             return  EN_US.equalsIgnoreCase(lang)?enableFlagEnum.enMsg:enableFlagEnum.zhMsg;
         }

         /**
          * key
          * @return
          */
        public String getKey() {
            return key;
        }

         /**
          * zhMsg
          * @return
          */
        public String getZhMsg() {
            return zhMsg;
        }

         /**
          * enMsg
          * @return
          */
        public String getEnMsg() {
            return enMsg;
        }
    }

     /**
      * 组织树接口，总监办标识
      */
     public static final String ORG_LABEL_FLAG_DIRECTOR_OFFICE = "1";

     /** 组织树接口，办事处标识 */
     public static final String ORG_LABEL_FLAG_OFFICE = "2";

     /** 行业树接口，商业标识 */
     public static final String INDUSTRY_LOOKUP_CODE_COMMERCE = "20";

     /**
      * enabledFlag “1” 代表有效
      */
    public static final String ENABELD_FLAG_VALID = "1";

     /**
      * 组织状态ID  1 有效
      */
    public static final int ORG_STATUS_VALID = 1;
 }

