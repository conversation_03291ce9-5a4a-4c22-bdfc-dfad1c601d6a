package com.zte.mcrm.adapter.constant;

public enum BlackListTypeEnum {

    /**
     * 永久黑名单
     */
    PERMANENT_BLOCK_LIST("permanentBlocklist", "永久黑名单"),

    /**
     * 限制合作
     */
    RESTRICTED_COOPERATION("restrictedCooperation", "限制合作"),

    /**
     * 限制黑名单
     */
    PERIOD_BLOCK_LIST("periodBlocklist", "限制黑名单"),

    /**
     * 黄黑名单
     */
    YELLOW_LIST("yellowList", "黄黑名单"),

    /**
     * 未知类型
     */
    UNKOWN_TYPE("unkownType", "未知类型");

    private String fastCodeValue;

    private String chineseDescription;

    BlackListTypeEnum(String fastCodeValue, String chineseDescription) {
        this.fastCodeValue = fastCodeValue;
        this.chineseDescription = chineseDescription;
    }

    public String getFastCodeValue() {
        return fastCodeValue;
    }

    public String getChineseDescription() {
        return chineseDescription;
    }

    public static String getChineseDescriptionByKey(String fastCodeValue) {
        BlackListTypeEnum[] blackListTypeEnums = BlackListTypeEnum.values();
        for (BlackListTypeEnum blockListType : blackListTypeEnums) {
            if (blockListType.fastCodeValue.equals(fastCodeValue)) {
                return blockListType.getChineseDescription();
            }
        }
        return BlackListTypeEnum.UNKOWN_TYPE.getChineseDescription();
    }
}
