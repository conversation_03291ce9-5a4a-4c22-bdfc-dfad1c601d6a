package com.zte.mcrm.channel.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

@Setter
@Getter
@ToString
public class CrmCustomerDertimineParamDTO {
    @ApiModelProperty("渠道商客户编码")
    @NotNull(message = "{crmCustomerCodes.null}")
    private List<String> crmCustomerCodes;

    @ApiModelProperty("n年内，如3")
    @NotNull(message = "{years.not.null}")
    @Min(value = 1, message = "{years.must.be.positiveNumber}")
    private Integer years;
}
