package com.zte.mcrm.common.model;

import com.siebel.data.SiebelPropertySet;
import com.zte.mcrm.common.consts.CommonConst;


/**
 * 调用Siebel服务请求结果封装对象,本对象适用于稳定Siebel调用方式，如果Siebel的服务返回结果不稳定，建议不使用本对象
 * <AUTHOR> zhaoshiguang 2018年2月5日 下午4:28:09
 *
 */
public class SiebelServiceResult {
	/**调用服务结果，如果成功为真，如果失败为假**/
	private boolean resultFlag;
	/**调用服务结果返回的内容，Siebel服务出错会把出错信息塞在这里面**/
	private String msg;
	/**详细结果**/
	private SiebelPropertySet output;
	
	
	public SiebelServiceResult(SiebelPropertySet output){
		if(CommonConst.S.equals(output.getProperty(CommonConst.PROC_STATUS))){
			//中兴siebel服务的返回的结果状态码会放在procStatus属性里面
			this.resultFlag = true;
			//中兴siebel服务的返回结果说明会放在procMsg属性里面
			this.msg = output.getProperty(CommonConst.PROC_MSG);
		}else{
			this.resultFlag = false;
			this.msg = output.getProperty(CommonConst.PROC_MSG);
		}
	}


	public boolean isResultFlag() {
		return resultFlag;
	}


	public void setResultFlag(boolean resultFlag) {
		this.resultFlag = resultFlag;
	}


	public String getMsg() {
		return msg;
	}


	public void setMsg(String msg) {
		this.msg = msg;
	}


	public SiebelPropertySet getOutput() {
		return output;
	}


	public void setOutput(SiebelPropertySet output) {
		this.output = output;
	}
	
	

}
