package com.zte.itp.msa.core.exception;

import com.zte.iccp.epm.base.exception.BusiEnumException;
import com.zte.iccp.epm.base.holder.HeaderDataContextHolder;
import com.zte.iccp.epm.base.model.HeaderData;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.servicecenter.config.MsbProperties;
import com.zte.mcrm.channel.util.ExceptionMsgUtils;
import com.zte.mcrm.common.errorcode.util.ExceptionLogOperateUtil;
import com.zte.mcrm.common.exception.BusinessRuntimeException;
import com.zte.mcrm.common.framework.exception.ErrorCodeException;
import com.zte.mcrm.common.framework.exception.ErrorCodeRemoteException;
import com.zte.mcrm.exceptionlog.access.vo.ExceptionLogVO;
import com.zte.mcrm.exceptionlog.service.ExceptionLogServiceImpl;
import com.zte.springbootframe.common.exception.BusiAuthException;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import com.zte.springbootframe.common.exception.*;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.common.exception.ValidationException;
import com.zte.springbootframe.common.model.RetCodeCopy;
import com.zte.springbootframe.common.model.ServiceDataCopy;
import com.zte.springbootframe.common.model.ServiceDataUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.local.LocalMessageUtils;
import com.zte.springbootframe.util.string.DateHelper;
import com.zte.springbootframe.util.string.LogHelper;
import com.zte.springbootframe.util.string.LogModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.core.annotation.Order;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.CompletionException;
import java.util.stream.Collectors;

/**
 * @author：10262158 赵世光
 * @date： 2020/9/24 下午8:44
 */
@ControllerAdvice
@Order(1)
public class GlobalDefaultExceptionHandler {

    final static Logger logger = LoggerFactory.getLogger(com.zte.itp.msa.core.exception.GlobalDefaultExceptionHandler.class);
    public static final String UNKNOWN = "unknown";

    /**
     * 处理默认的系统异常，即没有封装自定义异常的情况下就到该方法处理
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public ServiceData<Object> defaultErrorHandler(HttpServletRequest req, Exception e)
    {
        logger.error("zte-crm-opportunity-info defaultException", e);
        ServiceData<Object> ret = new ServiceData<>();
        RetCode retCode = new RetCode(RetCodeCopy.SERVERERROR_CODE,RetCodeCopy.SERVERERROR_MSGID);
        ret.setCode(retCode);
        ret.setBo(LocalMessageUtils.getMessage("system.exception"));
        LogModel lm = new LogModel();
        lm.setBusinessType(LocalMessageUtils.getMessage("system.exception"));
        lm.setLevel("error");
        lm.setStatus(LocalMessageUtils.getMessage("system.exception"));
        lm.setExtend1(e.toString());
        logger.error(LogHelper.formatterLog(lm));
        saveExceptionLog(req,retCode.getMsg(),e);
        return ret;
    }

    @ExceptionHandler(value = org.springframework.web.bind.MethodArgumentNotValidException.class)
    @ResponseBody
    public ServiceData<Object> handleMethodArgumentNotValidException(org.springframework.web.bind.MethodArgumentNotValidException e) {
        logger.error("zte-crm-opportunity-info MethodArgumentNotValidException", e);
        ServiceData<Object> ret = new ServiceData<>();
        StringBuilder sb = new StringBuilder();
        List<ObjectError> allErrors = e.getBindingResult().getAllErrors();

        String message = allErrors.stream().map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.joining(";"));
        ret.setBo(message);
        RetCode retCode = new RetCode(RetCodeCopy.SERVICE_ERROR_NEED_TO_SHOW, message);
        ret.setCode(retCode);
        return ret;
    }

    /**
     * 处理效验异常
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = com.zte.springbootframe.common.exception.ValidationException.class)
    @ResponseBody
    public ServiceData<?> validationErrorHandler(HttpServletRequest req, ValidationException e)
    {
        logger.error("zte-crm-opportunity-info validationException", e);
        ServiceData<Object> ret = new ServiceData<>();
        String errorStr = null;
        Collection<String> error = e.getResultMap().values();
        errorStr = String.join(";", error);
        ret.setBo(errorStr);
        RetCode retCode = new RetCode(RetCodeCopy.SERVICE_ERROR_NEED_TO_SHOW, errorStr);
        ret.setCode(retCode);
        LogModel lm = new LogModel();
        lm.setBusinessType(LocalMessageUtils.getMessage("check.error"));
        lm.setLevel("error");
        lm.setStatus(LocalMessageUtils.getMessage("check.error"));
        lm.setExtend1(e.toString());
        logger.error(LogHelper.formatterLog(lm));
        saveExceptionLog(req,retCode.getMsg(),e);
        return ret;
    }

    @ExceptionHandler(value = {CompletionException.class, java.util.concurrent.ExecutionException.class})
    @ResponseBody
    public ServiceData<?> completeExceptionHandler(HttpServletRequest req, Exception exception)
    {
        logger.error("zte-crm-opportunity-info validationCompleteException", exception);
        if (null == exception.getCause()){
            return defaultErrorHandler(req, exception);
        }

        if (exception.getCause().getClass().equals(com.zte.itp.msa.core.exception.BusiException.class)){
            com.zte.itp.msa.core.exception.BusiException busiException =
                    (com.zte.itp.msa.core.exception.BusiException) exception.getCause();
            return busiExceptionHandler(req, busiException);
        }

        if (exception.getCause().getClass().equals(javax.validation.ValidationException.class)){
            javax.validation.ValidationException validationException =
                    (javax.validation.ValidationException) exception.getCause();
            return validationExceptionHandler(req, validationException);
        }

        if (exception.getCause().getClass().equals(com.zte.springbootframe.common.exception.BusiException.class)){
            com.zte.springbootframe.common.exception.BusiException busiError =
                    (com.zte.springbootframe.common.exception.BusiException) exception.getCause();
            return busiErrorHandler(req, busiError);
        }

        if (exception.getCause().getClass().equals(com.zte.mcrm.common.exception.ApprovalOperationException.class)){
            com.zte.mcrm.common.exception.ApprovalOperationException approvalOperationException =
                    (com.zte.mcrm.common.exception.ApprovalOperationException) exception.getCause();
            return approvalOperationExceptionHandler(req, approvalOperationException);
        }

        return defaultErrorHandler(req, exception);
    }

    @ExceptionHandler(value = javax.validation.ValidationException.class)
    @ResponseBody
    public ServiceData<?> validationExceptionHandler(HttpServletRequest req, javax.validation.ValidationException validationException)
    {
        logger.error("zte-crm-opportunity-info validationException", validationException);
        ServiceData<Object> ret = new ServiceData<>();
        ret.setBo(validationException.getMessage());
        RetCode retCode = new RetCode(RetCodeCopy.SERVICE_ERROR_NEED_TO_SHOW, validationException.getMessage());
        ret.setCode(retCode);
        LogModel logModel = new LogModel();
        logModel.setBusinessType(LocalMessageUtils.getMessage("check.error"));
        logModel.setLevel("error");
        logModel.setStatus(LocalMessageUtils.getMessage("check.error"));
        logModel.setExtend1(validationException.toString());
        logger.error(LogHelper.formatterLog(logModel));
        saveExceptionLog(req,retCode.getMsg(),validationException);
        return ret;
    }

    /**
     * 处理业务异常
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = com.zte.itp.msa.core.exception.BusiException.class)
    @ResponseBody
    public ServiceData<?> busiExceptionHandler(HttpServletRequest req, com.zte.itp.msa.core.exception.BusiException e)
    {
        logger.error("zte-crm-opportunity-info busiException", e);
        ServiceData<Object> ret = new ServiceData<>();
        RetCode retCode = new RetCode(RetCodeCopy.SERVICE_ERROR_NEED_TO_SHOW, e.getExMsg());
        ret.setCode(retCode);
        ret.setBo(LocalMessageUtils.getMessage(e.getExMsg()));
        LogModel lm = new LogModel();
        lm.setBusinessType(LocalMessageUtils.getMessage("services.are.abnormal"));
        lm.setLevel("error");
        lm.setStatus(LocalMessageUtils.getMessage("services.are.abnormal"));
        lm.setExtend1(e.getMessage());
        logger.error(LogHelper.formatterLog(lm));
        saveExceptionLog(req,retCode.getMsg(),e);
        return ret;
    }

    /**
     * 处理业务异常
     * @param req
     * @param busiException
     * @return
     */
    @ExceptionHandler(value = com.zte.springbootframe.common.exception.BusiException.class)
    @ResponseBody
    public ServiceData<?> busiErrorHandler(HttpServletRequest req, BusiException busiException)
    {
        logger.error("zte-crm-opportunity-info busiErrorException", busiException);
        ServiceData<Object> ret = new ServiceData<>();
        RetCode retCode = new RetCode(RetCodeCopy.SERVICE_ERROR_NEED_TO_SHOW, busiException.getExMsg());
        ret.setCode(retCode);
        ret.setBo(LocalMessageUtils.getMessage(busiException.getExMsg()));
        LogModel lm = new LogModel();
        lm.setBusinessType(LocalMessageUtils.getMessage("services.are.abnormal"));
        lm.setLevel("error");
        lm.setStatus(LocalMessageUtils.getMessage("services.are.abnormal"));
        lm.setExtend1(busiException.getMessage());
        logger.error(LogHelper.formatterLog(lm));
        saveExceptionLog(req,retCode.getMsg(), busiException);
        return ret;
    }

    /**
     * 处理业务权限异常
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = BusicAuthException.class)
    @ResponseBody
    public ServiceDataCopy<?> busiAuthHandler(HttpServletRequest req, Exception e)
    {
        ServiceDataCopy<?> ret = ServiceDataUtil.noAuthInBusiness(null);
        logger.info(e.getMessage());

        saveExceptionLog(req,ret.getCode().getMsg(),e);
        return ret;
    }



    /**
     * siebel权限校验异常
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = SiebelErrorAuthDeniedException.class)
    @ResponseBody
    public ServiceDataCopy<?> siebelErrorAuthDenyHandler(HttpServletRequest req, Exception e){
        logger.info(e.getMessage());
        ServiceDataCopy<?> ret = ServiceDataUtil.noAuthInBusiness(null);
        LogModel lm = new LogModel();
        lm.setBusinessType(LocalMessageUtils.getMessage("services.are.abnormal"));
        lm.setLevel("error");
        lm.setStatus(LocalMessageUtils.getMessage("services.are.abnormal"));
        lm.setExtend1(e.getMessage());
        logger.error(LogHelper.formatterLog(lm));
        saveExceptionLog(req,ret.getCode().getMsg(),e);
        return ret;
    }
    /**
     * 处理业务运行时异常
     *
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = BusinessRuntimeException.class)
    @ResponseBody
    public ServiceData<String> businessRuntimeExceptionHandler(HttpServletRequest req, BusinessRuntimeException e) {
        logger.error("zte-crm-opportunity-info businessRuntimeException", e);
        ServiceData<String> ret = new ServiceData<>();
        ret.setCode(new RetCode(RetCodeCopy.SERVICE_ERROR_NEED_TO_SHOW, e.getExMsg()));
        ret.setBo(e.getExMsg());
        saveExceptionLog(req,e.getExMsg(),e);
        return ret;
    }

    @ExceptionHandler(value = com.zte.mcrm.common.exception.ApprovalOperationException.class)
    @ResponseBody
    public ServiceData<String> approvalOperationExceptionHandler(HttpServletRequest req, com.zte.mcrm.common.exception.ApprovalOperationException e) {
        logger.error("zte-crm-opportunity-info approvalOperationException", e);
        ServiceData<String> ret = new ServiceData<>();
        ret.setCode(new RetCode(RetCodeCopy.SERVICE_ERROR_NEED_TO_SHOW, e.getExMsg()));
        ret.setBo(e.getExMsg());
        saveExceptionLog(req,e.getExMsg(),e);
        return ret;
    }


    @ExceptionHandler(value = BusiAuthException.class)
    @ResponseBody
    public ServiceData<?> busiAuthExceptionHandler(HttpServletRequest req, BusiAuthException e){
        ServiceData<Object> ret = new ServiceData<>();
        RetCode retCode = new RetCode(e.getExCode(), e.getExMsg());
        ret.setCode(retCode);
        saveExceptionLog(req,e.getExMsg(),e);
        return ret;
    }

    /**
     * 异常日志保存
     * @param request
     * @param e
     */
    public void saveExceptionLog(HttpServletRequest request, String errorMsg, Exception e){
        try {
            ExceptionLogVO logVo = new ExceptionLogVO();
            //日志应用标记 系统日志用：平台-子系统-服务名，业务日志用：平台-子系统-app 必填
            logVo.setSyslogtag(getServiceName());
            //用户登录短工号  必填
            logVo.setEmpNo(request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO));
            //请求方法类型
            logVo.setRequestMethod(request.getMethod());
            //请求地址，即浏览器中你输入的地址（IP或域名）必填
            logVo.setHttpUserAgent(request.getHeader(SysGlobalConst.HTTP_HEAD_USER_AGENT));
            logVo.setHttpHost(getRemoteRealIP(request));
            //请求的URI 或者 业务操作，可以使用中文等内容，例如：/用户管理/用户登录 必填
            logVo.setUrl(request.getRequestURI());
            logVo.setRequestParam(JacksonJsonConverUtil.beanToJson(request.getParameterMap()));
            logVo.setTimeLocal(DateHelper.getCurrentTime());
            logVo.setZteReverse1(errorMsg);

            String exceptionContent = ExceptionMsgUtils.getStackTrace(e, 6000);
            logVo.setExceptionContext(exceptionContent);
            synSaveLog(logVo);
        } catch (Exception e2) {
            logger.error("保存异常日志失败", e);
        }

    }

    public void synSaveLog(ExceptionLogVO logVo){
        try
        {
            Thread t= new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        /*ExceptionLogServiceImpl exceptionLogService =(ExceptionLogServiceImpl) SpringContextUtil.getBean("exceptionLogServiceImpl");
                        exceptionLogService.saveExceptionLog(logVo);*/
                    } catch (Exception e) {
                        //日志信息输出
                        logger.error(e.getCause().getMessage(), e.getCause());
                    }
                }
            });
            t.start();
        }
        catch(Exception e1)
        {
            logger.error("线程异步保存异常日志失败",e1);
        }
    }
    /**
     * 获取注册的服务名
     * @return
     */
    private static String getServiceName()
    {
        String n="";
        try
        {
            MsbProperties registerConfig = (MsbProperties)SpringContextUtil.getBean("msbProperties");
            n=registerConfig.getServiceName();
        }
        catch(Exception ex)
        {
            logger.info(ex.getMessage());
        }
        return n;
    }

    /**
     * getRemoteIP:获取远程请求客户端的外网IP <br/>
     * @param request 请求实体对象
     * @return ip 外网ip<br/>
     */
    private static String getRemoteRealIP(HttpServletRequest request)
    {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip))
        {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip))
        {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip))
        {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 处理错误码异常
     *
     * @param req
     * @param e
     * @return
     */
    @SuppressWarnings({"rawtypes"})
    @ExceptionHandler(value = ErrorCodeException.class)
    @ResponseBody
    public ServiceData<String> errorCodeExceptionHandler(HttpServletRequest req, ErrorCodeException e) {
        return e.getErrorCodeServiceData();
    }

    /**
     * 处理错误码远程异常
     *
     * @param req
     * @param e
     * @return
     */
    @SuppressWarnings({"rawtypes"})
    @ExceptionHandler(value = ErrorCodeRemoteException.class)
    @ResponseBody
    public ServiceData<String> errorCodeExceptionHandler(HttpServletRequest req, ErrorCodeRemoteException e) {
        return e.getErrorCodeServiceData();
    }

    /**
     * 处理导入异常
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = BusiEnumException.class)
    @ResponseBody
    public ServiceData<String> bussinessEnumException(HttpServletRequest req, BusiEnumException e) {
        ServiceData<String> ret = new ServiceData<>();
        HeaderData headerData = new HeaderData();
        headerData.setLangId(req.getHeader("X-Lang-Id"));
        headerData.setAuthValue(req.getHeader("X-Auth-Value"));
        headerData.setEmpNo(req.getHeader("X-Emp-No"));
        headerData.setItpValue(req.getHeader("X-Itp-Value"));
        headerData.setTenantId(req.getHeader("X-Tenant-Id"));
        HeaderDataContextHolder.set(headerData);
        ret.setCode(new RetCode(RetCodeCopy.SERVICE_ERROR_NEED_TO_SHOW, e.getMessage()));
        HeaderDataContextHolder.clean();
        return ret;
    }
}
