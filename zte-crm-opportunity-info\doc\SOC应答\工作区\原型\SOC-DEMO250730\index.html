<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SOC智能应答系统</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- 主体布局 -->
    <div class="main-container">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <div class="nav-menu">
                <div class="nav-item active" onclick="showPage('task-management')">
                    <span class="nav-icon">📋</span>
                    任务管理
                </div>
                <div class="nav-item" onclick="showPage('quick-response')">
                    <span class="nav-icon">⚡</span>
                    快捷应答
                </div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 任务管理页面 -->
            <div id="task-management" class="page">
                <div class="page-header">
                    <h1 class="page-title">任务管理</h1>
                    <p class="page-description">管理和查看SOC智能应答任务</p>
                </div>

                <div class="card">
                    <div class="card-body">
                        <!-- 操作栏 -->
                        <div class="toolbar">
                            <div class="toolbar-left">
                                <button class="btn btn-primary" onclick="showCreateTaskModal()">
                                    创建任务
                                </button>
                            </div>
                        </div>

                        <!-- 查询条件 -->
                        <div class="query-panel" style="margin: 16px 0;">
                            <div class="query-grid">
                                <div class="form-item">
                                    <label class="form-label">任务编码</label>
                                    <input type="text" class="form-control" placeholder="请输入任务编码" id="searchTaskCode">
                                </div>
                                <div class="form-item">
                                    <label class="form-label">任务名称</label>
                                    <input type="text" class="form-control" placeholder="请输入任务名称" id="searchTaskName">
                                </div>
                                <div class="form-item">
                                    <label class="form-label">国家</label>
                                    <select class="form-control" id="searchCountry">
                                        <option value="">全部国家</option>
                                        <option value="中国">中国</option>
                                        <option value="新加坡">新加坡</option>
                                        <option value="德国">德国</option>
                                        <option value="英国">英国</option>
                                        <option value="美国">美国</option>
                                    </select>
                                </div>
                                <div class="form-item">
                                    <label class="form-label">客户</label>
                                    <input type="text" class="form-control" placeholder="请输入客户名称" id="searchCustomer">
                                </div>
                                <div class="form-item">
                                    <label class="form-label">项目</label>
                                    <input type="text" class="form-control" placeholder="请输入项目名称" id="searchProject">
                                </div>
                            </div>
                            <div class="query-actions">
                                <button class="btn btn-primary" onclick="searchTasks()">查询</button>
                            </div>
                        </div>

                        <!-- 任务列表 -->
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>任务编码</th>
                                        <th>任务名称</th>
                                        <th>国家</th>
                                        <th>客户</th>
                                        <th>项目</th>
                                        <th>应答条目数</th>
                                        <th>应答进度</th>
                                        <th>总满足度</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="taskTableBody">
                                    <!-- 任务数据将通过JavaScript动态渲染 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <div class="pagination">
                            <div class="pagination-info" id="paginationInfo">共 0 条记录，第 1/1 页</div>
                            <div class="pagination-controls">
                                <select class="form-control" style="width: auto;">
                                    <option value="10">10条/页</option>
                                    <option value="20" selected>20条/页</option>
                                    <option value="50">50条/页</option>
                                    <option value="100">100条/页</option>
                                    <option value="200">200条/页</option>
                                </select>
                                <button class="btn" disabled>上一页</button>
                                <button class="btn" disabled>下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 任务详情页面 -->
            <div id="task-detail" class="page" style="display: none;">
                <!-- 面包屑 -->
                <div class="breadcrumb">
                    <a href="#" onclick="showPage('task-management')">任务管理</a>
                    <span>></span>
                    <span id="taskDetailTitle">任务详情</span>
                </div>

                <!-- 任务信息头部 -->
                <div class="card">
                    <div class="task-info-header" id="taskInfoHeader">
                        <!-- 任务信息将通过JavaScript动态渲染 -->
                    </div>

                    <!-- 标签页 -->
                    <div class="tabs">
                        <div class="tab active" onclick="showTab('item-management')">条目管理</div>
                        <div class="tab" onclick="showTab('data-analysis')">数据分析</div>
                    </div>

                    <!-- 条目管理内容 -->
                    <div id="item-management" class="tab-content">
                        <div class="task-detail-container">
                            <!-- 内容面板 -->
                            <div class="content-panel" id="contentPanel">
                                <!-- 查询面板 -->
                                <div class="query-panel">
                                    <div class="query-grid">
                                        <div class="form-item">
                                            <label class="form-label">编号</label>
                                            <input type="text" class="form-control" placeholder="请输入编号" id="queryCode">
                                        </div>
                                        <div class="form-item">
                                            <label class="form-label">条目描述</label>
                                            <input type="text" class="form-control" placeholder="请输入条目描述" id="queryDescription">
                                        </div>
                                        <div class="form-item">
                                            <label class="form-label">产品</label>
                                            <select class="form-control" id="queryProduct">
                                                <option value="">全部产品</option>
                                                <option value="华为云Stack">华为云Stack</option>
                                                <option value="FusionSphere">FusionSphere</option>
                                                <option value="云安全服务">云安全服务</option>
                                                <option value="FusionCompute">FusionCompute</option>
                                            </select>
                                        </div>
                                        <div class="form-item">
                                            <label class="form-label">应答状态</label>
                                            <select class="form-control" id="queryStatus">
                                                <option value="">全部</option>
                                                <option value="未应答">未应答</option>
                                                <option value="应答中">应答中</option>
                                                <option value="已应答">已应答</option>
                                            </select>
                                        </div>
                                        <div class="form-item">
                                            <label class="form-label">标签</label>
                                            <input type="text" class="form-control" placeholder="请输入标签" id="queryTag">
                                        </div>
                                        <div class="form-item">
                                            <label class="form-label">应答</label>
                                            <select class="form-control" id="querySatisfaction">
                                                <option value="">全部</option>
                                                <option value="FC">FC - 完全满足</option>
                                                <option value="PC">PC - 部分满足</option>
                                                <option value="NC">NC - 不满足</option>
                                            </select>
                                        </div>
                                        <div class="form-item">
                                            <label class="form-label">指派给</label>
                                            <input type="text" class="form-control" placeholder="请输入指派人" id="queryAssignee">
                                        </div>
                                    </div>
                                    <div class="query-actions">
                                        <button class="btn" onclick="resetQuery()">重置</button>
                                        <button class="btn btn-primary" onclick="queryItems()">查询</button>
                                        <!-- 列筛选按钮 -->
                                        <div class="column-filter">
                                            <button class="column-filter-btn" onclick="toggleColumnFilter()" id="columnFilterBtn">
                                                <span>⚙️</span>
                                                <span class="toggle-icon">▼</span>
                                            </button>
                                            <div class="column-filter-dropdown" id="columnFilterDropdown">
                                                <div class="column-filter-header">
                                                    <span>选择显示列</span>
                                                    <span id="selectedColumnCount">9/16</span>
                                                </div>
                                                <div class="column-filter-content">
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-select" checked>
                                                        <label for="col-select">选择框</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-序号" checked>
                                                        <label for="col-序号">序号</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-编号" checked>
                                                        <label for="col-编号">编号</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-条目描述" checked>
                                                        <label for="col-条目描述">条目描述</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-产品" checked>
                                                        <label for="col-产品">产品</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-标签">
                                                        <label for="col-标签">标签</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-应答状态" checked>
                                                        <label for="col-应答状态">应答状态</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-应答" checked>
                                                        <label for="col-应答">应答</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-指派给">
                                                        <label for="col-指派给">指派给</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-应答方式">
                                                        <label for="col-应答方式">应答方式</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-应答说明" checked>
                                                        <label for="col-应答说明">应答说明</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-应答来源" checked>
                                                        <label for="col-应答来源">应答来源</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-索引" checked>
                                                        <label for="col-索引">索引</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-备注">
                                                        <label for="col-备注">备注</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-最后更新人">
                                                        <label for="col-最后更新人">最后更新人</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-最后更新时间">
                                                        <label for="col-最后更新时间">最后更新时间</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-操作" checked>
                                                        <label for="col-操作">操作</label>
                                                    </div>
                                                </div>
                                                <div class="column-filter-actions">
                                                    <button class="btn" onclick="resetColumnFilter()">重置</button>
                                                    <button class="btn btn-primary" onclick="applyColumnFilter()">确定</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 操作工具栏 -->
                                <div class="toolbar">
                                    <div class="toolbar-left">
                                        <button class="btn btn-primary" onclick="startResponse()">开始应答</button>
                                        <button class="btn" onclick="batchManualResponse()" id="batchManualBtn" style="display: none;">手工应答</button>
                                        <button class="btn" onclick="batchAiResponse()" id="batchAiBtn" style="display: none;">AI应答</button>
                                        <button class="btn" onclick="batchDelete()">批量删除</button>
                                        <button class="btn" onclick="batchAddTag()">批量添加标签</button>
                                        <button class="btn" onclick="batchRemoveTag()">批量移除标签</button>
                                        <button class="btn" onclick="setProduct()">新增产品</button>
                                        <button class="btn" onclick="showPermissionManagementModal()">权限管理</button>
                                        <button class="btn" onclick="exportItems()">导出</button>
                                    </div>
                                    <div class="toolbar-right">
                                        <button class="btn" onclick="showAddItemModal()">单条录入</button>
                                        <button class="btn" onclick="showBatchImportModal()">批量导入</button>
                                    </div>
                                </div>

                                <!-- 条目列表 -->
                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th width="40">
                                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                                </th>
                                                <th>序号</th>
                                                <th>编号</th>
                                                <th>条目描述</th>
                                                <th>产品</th>
                                                <th>标签</th>
                                                <th>应答状态</th>
                                                <th>应答</th>
                                                <th>指派给</th>
                                                <th>应答方式</th>
                                                <th>应答说明</th>
                                                <th>应答来源</th>
                                                <th>索引</th>
                                                <th>备注</th>
                                                <th>最后更新人</th>
                                                <th>最后更新时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="itemTableBody">
                                            <!-- 条目数据将通过JavaScript动态渲染 -->
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 分页 -->
                                <div class="pagination">
                                    <div class="pagination-info" id="itemPaginationInfo">共 0 条记录，第 1/1 页</div>
                                    <div class="pagination-controls">
                                        <select class="form-control" style="width: auto;">
                                            <option value="10">10条/页</option>
                                            <option value="20" selected>20条/页</option>
                                            <option value="50">50条/页</option>
                                        </select>
                                        <button class="btn" disabled>上一页</button>
                                        <button class="btn" disabled>下一页</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据分析内容 -->
                    <div id="data-analysis" class="tab-content" style="display: none;">
                        <div class="card-body">
                            <!-- 筛选条件 -->
                            <div class="query-panel" style="margin-bottom: 24px;">
                                <div class="query-grid">
                                    <div class="form-item">
                                        <label class="form-label">指派给</label>
                                        <select class="form-control" onchange="filterAnalysisData()">
                                            <option value="">全部</option>
                                            <option value="123456">张三（123456）</option>
                                            <option value="789012">李四（789012）</option>
                                            <option value="345678">王五（345678）</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- 总体统计 -->
                            <div class="card" style="margin-bottom: 24px;">
                                <div class="card-header">
                                    <h4>进展分析</h4>
                                </div>
                                <div class="card-body">
                                    <div class="stats-grid" id="statsGrid">
                                        <!-- 统计数据将通过JavaScript动态渲染 -->
                                    </div>
                                </div>
                            </div>

                            <!-- 产品维度统计 -->
                            <div class="card">
                                <div class="card-header">
                                    <h4>产品维度统计</h4>
                                </div>
                                <div class="card-body">
                                    <div class="table-container">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>产品</th>
                                                    <th>总条目数</th>
                                                    <th>已应答数</th>
                                                    <th>FC</th>
                                                    <th>PC</th>
                                                    <th>NC</th>
                                                    <th>完成率</th>
                                                    <th>满足度</th>
                                                </tr>
                                            </thead>
                                            <tbody id="productStatsBody">
                                                <!-- 产品统计数据将通过JavaScript动态渲染 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快捷应答页面 -->
            <div id="quick-response" class="page" style="display: none;">
                <div class="page-header">
                    <h1 class="page-title">快捷应答</h1>
                    <p class="page-description">快速进行单个条目应答</p>
                </div>
                
                <div class="card">
                    <div class="card-body">
                        <form id="quickResponseForm">
                            <div class="form-row">
                                <div class="form-item">
                                    <label class="form-label required">产品</label>
                                    <select class="form-control" required id="quickProductSelect">
                                        <option value="">请选择产品</option>
                                        <option value="华为云Stack">华为云Stack</option>
                                        <option value="FusionSphere">FusionSphere</option>
                                        <option value="云安全服务">云安全服务</option>
                                        <option value="FusionCompute">FusionCompute</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-item">
                                    <label class="form-label">国家/MTO</label>
                                    <select class="form-control">
                                        <option value="">请选择</option>
                                        <option value="中国">中国</option>
                                        <option value="新加坡">新加坡</option>
                                        <option value="德国">德国</option>
                                        <option value="英国">英国</option>
                                        <option value="法国">法国</option>
                                    </select>
                                </div>
                                <div class="form-item">
                                    <label class="form-label">运营商/分支</label>
                                    <select class="form-control">
                                        <option value="">请选择</option>
                                        <option value="华为技术有限公司">华为技术有限公司</option>
                                        <option value="华为软件技术有限公司">华为软件技术有限公司</option>
                                        <option value="华为云计算技术有限公司">华为云计算技术有限公司</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-item">
                                    <label class="form-label">客户</label>
                                    <input type="text" class="form-control" placeholder="请输入客户名称">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-item">
                                    <label class="form-label required">条目输入</label>
                                    <textarea class="form-control" rows="6" placeholder="请输入要应答的条目描述" required></textarea>
                                    <div style="margin-top: 8px; font-size: 12px; color: #8c8c8c;">
                                        提示：输入条目描述后，系统将自动匹配GBBS中的相关数据进行应答
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-item">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <button type="button" class="btn" onclick="clearQuickForm()">清空</button>
                                        <button type="button" class="btn btn-primary" onclick="startQuickResponse()">开始应答</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 个人任务区提示 -->
                <div class="card" style="margin-top: 24px;">
                    <div class="card-body">
                        <div style="padding: 16px; background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 4px;">
                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                <span style="color: #52c41a; font-size: 16px;">ℹ️</span>
                                <strong style="color: #52c41a;">个人任务区说明</strong>
                            </div>
                            <p style="margin: 0; color: #52c41a; font-size: 14px;">
                                通过快捷应答提交的条目将自动创建个人任务并放置在个人任务区，您可以在任务管理中查看和管理这些任务。个人任务除了无法删除外，其他功能与普通任务完全相同。
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 人工应答详情页面 -->
            <div id="manual-response" class="page" style="display: none;">
                <!-- 面包屑 -->
                <div class="breadcrumb" id="manualResponseBreadcrumb">
                    <a href="#" onclick="showPage('task-management')">任务管理</a>
                    <span>></span>
                    <a href="#" onclick="openTaskDetail(globalData.currentTask ? globalData.currentTask.id : '')">
                        <%= globalData.currentTask ? globalData.currentTask.name : '' %>
                      </a>
                    <span>></span>
                    <span>人工应答</span>
                </div>

                <!-- 条目信息头部 -->
                <div class="card">
                    <div class="task-info-header">
                        <!-- 条目基本信息 -->
                        <div class="task-info-grid" style="margin-bottom: 16px;">
                            <div class="task-info-item" style="grid-column: span 3;">
                                <div class="task-info-label" style="font-size: 14px;">条目描述</div>
                                <div class="task-info-value" style="font-size: 18px; font-weight: 600; color: #262626;">${globalData.currentItem.description}</div>
                            </div>
                        </div>

                        <!-- 任务相关信息 -->
                        <div class="task-info-grid" style="margin-bottom: 16px;">
                            <div class="task-info-item">
                                <div class="task-info-label" style="font-size: 14px;">国家/MTO</div>
                                <div class="task-info-value" style="font-size: 16px; font-weight: 500;">${globalData.currentTask?.country || '中国'}</div>
                            </div>
                            <div class="task-info-item">
                                <div class="task-info-label" style="font-size: 14px;">运营商/分支</div>
                                <div class="task-info-value" style="font-size: 16px; font-weight: 500;">${globalData.currentTask?.company || '华为技术有限公司'}</div>
                            </div>
                            <div class="task-info-item">
                                <div class="task-info-label" style="font-size: 14px;">客户</div>
                                <div class="task-info-value" style="font-size: 16px; font-weight: 500;">${globalData.currentTask?.customer || '某银行'}</div>
                            </div>
                        </div>
                        
                        <!-- 产品切换和历史版本 -->
                        <div style="margin-top: 16px; display: flex; align-items: center; gap: 24px;">
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <label class="form-label">切换产品查看：</label>
                                <select class="form-control" style="width: 200px;" onchange="switchProduct()" id="productSwitchSelect">
                                    <option value="华为云Stack">华为云Stack</option>
                                    <option value="FusionSphere">FusionSphere</option>
                                    <option value="云安全服务">云安全服务</option>
                                    <option value="FusionCompute">FusionCompute</option>
                                </select>
                            </div>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <label class="form-label">历史版本：</label>
                                <select class="form-control" style="width: 300px;" onchange="loadHistoryVersion()">
                                    <option value="">选择历史版本</option>
                                    <option value="v1">版本1 - 2024-01-15 10:30 (AI应答)</option>
                                    <option value="v2">版本2 - 2024-01-15 11:15 (手工修改)</option>
                                    <option value="current">当前版本 - 2024-01-15 14:20</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 标签页 -->
                    <div class="tabs">
                        <div class="tab active" onclick="showResponseTab('response-result')">应答结果</div>
                        <div class="tab" onclick="showResponseTab('match-details')">匹配详情</div>
                    </div>

                    <!-- 应答结果内容 -->
                    <div id="response-result" class="tab-content">
                        <div class="card-body">
                            <form id="responseForm">
                                <!-- 历史版本选择 -->
                                <div class="form-row">
                                    <div class="form-item">
                                        <label class="form-label">历史版本</label>
                                        <select class="form-control" style="width: 300px;" id="historyVersionSelect" onchange="onHistoryVersionChange()">
                                            <option value="">选择历史版本</option>
                                            <option value="v1">版本1 - 2024-01-15 10:30 (AI应答)</option>
                                            <option value="v2">版本2 - 2024-01-15 11:15 (手工修改)</option>
                                            <option value="v3">版本3 - 2024-01-15 14:20 (AI应答)</option>
                                            <option value="current">当前版本 - 2024-01-15 16:45</option>
                                        </select>
                                        <div style="margin-top: 8px; font-size: 12px; color: #8c8c8c;">
                                            选择历史版本后会立即替换当前表单内容，点击"保存"按钮提交修改
                                        </div>
                                    </div>
                                </div>

                                <!-- 补充信息 -->
                                <div class="form-row">
                                    <div class="form-item">
                                        <label class="form-label">补充信息</label>
                                        <div style="display: flex; gap: 8px; align-items: center;">
                                            <textarea class="form-control" rows="3" placeholder="点击【AI应答】可在此处进行信息补充，用户可参考此处补充信息进行应答" id="additionalInfo"></textarea>
                                            <button type="button" class="btn btn-primary" onclick="aiEnhance()">AI应答</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-item">
                                        <label class="form-label">满足度</label>
                                        <select class="form-control" style="width: 200px;">
                                            <option value="">请选择</option>
                                            <option value="FC">FC - 完全满足</option>
                                            <option value="PC" selected>PC - 部分满足</option>
                                            <option value="NC">NC - 不满足</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- 应答说明 -->
                                <div class="form-row">
                                    <div class="form-item">
                                        <label class="form-label">应答说明</label>
                                        <div class="rich-editor">
                                            <div class="editor-toolbar">
                                                <button type="button" class="btn btn-sm" onclick="formatText('bold')">B</button>
                                                <button type="button" class="btn btn-sm" onclick="formatText('italic')">I</button>
                                                <button type="button" class="btn btn-sm" onclick="insertImage()">📷</button>
                                                <button type="button" class="btn btn-sm btn-primary" onclick="aiPolish()">AI润色</button>
                                                <button type="button" class="btn btn-sm btn-primary" onclick="aiTranslate()">AI翻译</button>
                                            </div>
                                            <div class="editor-content" contenteditable="true" id="responseContent">
                                                华为云Stack提供统一的云平台基础架构管理能力，支持虚拟机、容器等多种计算资源的统一管理。通过FusionSphere虚拟化平台，可以实现计算、存储、网络资源的动态分配和管理。
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 索引 -->
                                <div class="form-row">
                                    <div class="form-item">
                                        <label class="form-label">索引</label>
                                        <div style="display: flex; gap: 8px; align-items: center;">
                                            <input type="text" class="form-control" value="GBBS-001" readonly>
                                            <button type="button" class="btn" onclick="viewSource()">查看来源</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 来源 -->
                                <div class="form-row">
                                    <div class="form-item">
                                        <label class="form-label">来源</label>
                                        <input type="text" class="form-control" value="GBBS" readonly>
                                    </div>
                                </div>

                                <!-- 备注 -->
                                <div class="form-row">
                                    <div class="form-item">
                                        <label class="form-label">备注</label>
                                        <textarea class="form-control" rows="2" placeholder="可添加额外的描述"></textarea>
                                    </div>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="form-row" style="margin-top: 32px;">
                                    <div class="form-item">
                                        <div style="display: flex; justify-content: flex-end; gap: 16px;">
                                            <button type="button" class="btn" onclick="resetResponse()">取消</button>
                                            <button type="button" class="btn btn-primary" onclick="saveResponse()">保存</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 匹配详情内容 -->
                    <div id="match-details" class="tab-content" style="display: none;">
                        <div class="card-body">
                            <!-- 筛选条件 -->
                            <div class="query-panel" style="margin-bottom: 24px;">
                                <div class="query-grid">
                                    <div class="form-item">
                                        <label class="form-label">匹配度</label>
                                        <select class="form-control" onchange="filterMatches()">
                                            <option value="">全部</option>
                                            <option value="90">≥90%</option>
                                            <option value="80">≥80%</option>
                                            <option value="70">≥70%</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- GBBS匹配结果 -->
                            <div class="match-source-section">
                                <div class="match-source-header" onclick="toggleMatchSource('gbbs')">
                                    <div style="display: flex; align-items: center; gap: 16px;">
                                        <h4>GBBS 匹配结果</h4>
                                        <div class="match-stats">
                                            <span class="stat-item">总数: 15</span>
                                            <span class="stat-item fc">FC: 8</span>
                                            <span class="stat-item pc">PC: 5</span>
                                            <span class="stat-item nc">NC: 2</span>
                                        </div>
                                    </div>
                                    <span class="toggle-icon" id="gbbs-toggle">▼</span>
                                </div>
                                <div class="match-source-content" id="gbbs-content">
                                    <!-- 匹配卡片网格 -->
                                    <div class="match-cards-grid" id="matchCardsGrid">
                                        <!-- 卡片 1 -->
                                        <div class="match-card">
                                            <div class="match-card-header">
                                                <div class="match-score">
                                                    <span class="match-percentage">95%</span>
                                                    <div class="match-location">中国/华为技术有限公司/某银行</div>
                                                </div>
                                                <button class="btn btn-primary btn-sm" onclick="applyMatch(1)">应用</button>
                                            </div>
                                            <div class="match-card-content">
                                                <div class="match-field" style="margin-bottom: 8px;">
                                                    <strong>条目描述：</strong>云平台基础架构统一管理能力，支持虚拟机、容器、存储资源管理
                                                </div>
                                                <div class="match-field" style="margin-bottom: 8px;">
                                                    <strong>应答策略：</strong>
                                                    <span class="strategy-tag">标准化口径</span>
                                                </div>
                                                <div class="match-field" style="margin-bottom: 8px;">
                                                    <strong>满足度：</strong><span class="satisfaction-tag fc">FC</span>
                                                </div>
                                                <div class="match-field" style="margin-bottom: 8px;">
                                                    <strong>应答说明：</strong>华为云Stack基于OpenStack架构，提供完整的基础设施即服务(IaaS)能力...
                                                </div>
                                                <div class="match-field">
                                                    <strong>索引：</strong><a href="#" target="_blank">GBBS-HCS-001</a>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 卡片 2 -->
                                        <div class="match-card">
                                            <div class="match-card-header">
                                                <div class="match-score">
                                                    <span class="match-percentage">87%</span>
                                                    <div class="match-location">中国/华为技术有限公司/某保险公司</div>
                                                </div>
                                                <button class="btn btn-primary btn-sm" onclick="applyMatch(2)">应用</button>
                                            </div>
                                            <div class="match-card-content">
                                                <div class="match-field" style="margin-bottom: 8px;">
                                                    <strong>条目描述：</strong>云平台基础架构管理，包括计算、存储、网络资源的统一调度
                                                </div>
                                                <div class="match-field" style="margin-bottom: 8px;">
                                                    <strong>应答策略：</strong>
                                                    <span class="strategy-tag">客户定制</span>
                                                </div>
                                                <div class="match-field" style="margin-bottom: 8px;">
                                                    <strong>满足度：</strong><span class="satisfaction-tag pc">PC</span>
                                                </div>
                                                <div class="match-field" style="margin-bottom: 8px;">
                                                    <strong>应答说明：</strong>华为云Stack提供云平台基础架构管理能力，通过统一的管理平台...
                                                </div>
                                                <div class="match-field">
                                                    <strong>索引：</strong><a href="#" target="_blank">GBBS-HCS-002</a>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 卡片 3 -->
                                        <div class="match-card">
                                            <div class="match-card-header">
                                                <div class="match-score">
                                                    <span class="match-percentage">82%</span>
                                                    <div class="match-location">新加坡/华为软件技术有限公司/某银行</div>
                                                </div>
                                                <button class="btn btn-primary btn-sm" onclick="applyMatch(3)">应用</button>
                                            </div>
                                            <div class="match-card-content">
                                                <div class="match-field" style="margin-bottom: 8px;">
                                                    <strong>条目描述：</strong>基础设施即服务平台，提供虚拟化资源管理
                                                </div>
                                                <div class="match-field" style="margin-bottom: 8px;">
                                                    <strong>应答策略：</strong>
                                                    <span class="strategy-tag">客户保守</span>
                                                </div>
                                                <div class="match-field" style="margin-bottom: 8px;">
                                                    <strong>满足度：</strong><span class="satisfaction-tag pc">PC</span>
                                                </div>
                                                <div class="match-field" style="margin-bottom: 8px;">
                                                    <strong>应答说明：</strong>华为云Stack基于成熟的虚拟化技术，提供IaaS服务...
                                                </div>
                                                <div class="match-field">
                                                    <strong>索引：</strong><a href="#" target="_blank">GBBS-HCS-003</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 分页控件 -->
                                    <div class="match-pagination">
                                        <button class="btn btn-sm" onclick="prevMatchPage()" disabled>上一页</button>
                                        <span class="pagination-info">第 1/5 页</span>
                                        <button class="btn btn-sm" onclick="nextMatchPage()">下一页</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 自动刷新提示 -->
    <div class="auto-refresh" id="autoRefresh" style="display: none;">
        <span id="refreshTimer">30</span>秒后自动刷新
    </div>

    <!-- 创建任务弹窗 -->
    <div id="createTaskModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">创建任务</h3>
                <button class="modal-close" onclick="hideCreateTaskModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="createTaskForm">
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label required">任务名称</label>
                            <input type="text" class="form-control" placeholder="请输入任务名称" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">国家/MTO</label>
                            <select class="form-control">
                                <option value="">请选择</option>
                                <option value="中国">中国</option>
                                <option value="新加坡">新加坡</option>
                                <option value="德国">德国</option>
                                <option value="英国">英国</option>
                            </select>
                        </div>
                        <div class="form-item">
                            <label class="form-label">运营商/分支</label>
                            <select class="form-control">
                                <option value="">请选择</option>
                                <option value="华为技术有限公司">华为技术有限公司</option>
                                <option value="华为软件技术有限公司">华为软件技术有限公司</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">客户</label>
                            <input type="text" class="form-control" placeholder="请输入客户名称">
                        </div>
                        <div class="form-item">
                            <label class="form-label">项目</label>
                            <input type="text" class="form-control" placeholder="请输入项目名称">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">应答条目文件</label>
                            <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                                <div class="upload-icon">📁</div>
                                <div class="upload-text">点击上传Excel文件，或 <button type="button" class="btn btn-sm" onclick="downloadTemplate()">下载模板</button></div>
                                <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideCreateTaskModal()">取消</button>
                <button class="btn btn-primary" onclick="createTask()">创建</button>
            </div>
        </div>
    </div>

    <!-- 单条录入弹窗 -->
    <div id="addItemModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">单条录入</h3>
                <button class="modal-close" onclick="hideAddItemModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addItemForm">
                    <!-- 编号 -->
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label required">编号</label>
                            <input type="text" class="form-control" placeholder="请输入条目编号" required>
                        </div>
                    </div>
                    
                    <!-- 条目描述 -->
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label required">条目描述</label>
                            <textarea class="form-control" rows="3" placeholder="请输入条目描述内容" required></textarea>
                        </div>
                    </div>
                    
                    <!-- 产品 -->
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label required">产品</label>
                            <select class="form-control" required id="addItemProduct">
                                <option value="">请选择产品</option>
                                <option value="华为云Stack">华为云Stack</option>
                                <option value="FusionSphere">FusionSphere</option>
                                <option value="云安全服务">云安全服务</option>
                                <option value="FusionCompute">FusionCompute</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- 指派给 -->
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">指派给</label>
                            <input type="text" class="form-control" placeholder="请选择指派人">
                        </div>
                    </div>
                    
                    <!-- 自动应答 -->
                    <div class="form-row">
                        <div class="form-item">
                            <div class="checkbox">
                                <input type="checkbox" id="autoResponse" checked>
                                <label for="autoResponse">自动应答</label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 备注 -->
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">备注</label>
                            <textarea class="form-control" rows="2" placeholder="条目其他事项说明"></textarea>
                        </div>
                    </div>
                    
                    <!-- 重复时覆盖 -->
                    <div class="form-row">
                        <div class="form-item">
                            <div class="checkbox">
                                <input type="checkbox" id="overwriteWhenDuplicate" checked>
                                <label for="overwriteWhenDuplicate">重复时覆盖</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideAddItemModal()">取消</button>
                <button class="btn btn-primary" onclick="addItem()">提交</button>
            </div>
        </div>
    </div>

    <!-- 批量导入弹窗 -->
    <div id="batchImportModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">批量导入</h3>
                <button class="modal-close" onclick="hideBatchImportModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-item">
                    <label class="form-label">导入模板</label>
                    <div style="margin-bottom: 16px;">
                        <button class="btn btn-primary" onclick="downloadTemplate()">下载导入模板</button>
                        <span style="margin-left: 8px; color: #8c8c8c; font-size: 12px;">请先下载模板，按模板格式填写数据后上传</span>
                    </div>
                </div>
                <div class="form-item">
                    <label class="form-label required">导入文件</label>
                    <div class="upload-area" onclick="document.getElementById('batchFileInput').click()">
                        <div class="upload-icon">📁</div>
                        <div class="upload-text">支持Excel格式文件，包含编码、条目描述、产品、指派给、自动应答、备注、重复时覆盖等字段</div>
                        <input type="file" id="batchFileInput" accept=".xlsx,.xls" style="display: none;">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideBatchImportModal()">取消</button>
                <button class="btn btn-primary" onclick="batchImport()">提交</button>
            </div>
        </div>
    </div>

    <!-- 编辑任务弹窗 -->
    <div id="editTaskModal" class="modal edit-task-modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">编辑任务</h3>
                <button class="modal-close" onclick="hideEditTaskModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editTaskForm">
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label required">任务名称</label>
                            <input type="text" class="form-control" placeholder="请输入任务名称" required id="editTaskName">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">国家/MTO</label>
                            <select class="form-control" id="editTaskCountry">
                                <option value="">请选择</option>
                                <option value="中国">中国</option>
                                <option value="新加坡">新加坡</option>
                                <option value="德国">德国</option>
                                <option value="英国">英国</option>
                            </select>
                        </div>
                        <div class="form-item">
                            <label class="form-label">运营商/分支</label>
                            <select class="form-control" id="editTaskCompany">
                                <option value="">请选择</option>
                                <option value="华为技术有限公司">华为技术有限公司</option>
                                <option value="华为软件技术有限公司">华为软件技术有限公司</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">客户</label>
                            <input type="text" class="form-control" placeholder="请输入客户名称" id="editTaskCustomer">
                        </div>
                        <div class="form-item">
                            <label class="form-label">项目</label>
                            <input type="text" class="form-control" placeholder="请输入项目名称" id="editTaskProject">
                        </div>
                    </div>
                    
                    <!-- 自动应答提醒 -->
                    <div class="form-row" style="margin-top: 16px;">
                        <div class="form-item">
                            <div style="padding: 12px; background: #fff7e6; border: 1px solid #ffd591; border-radius: 4px;">
                                <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                    <span style="color: #fa8c16; font-size: 16px;">⚠️</span>
                                    <strong style="color: #fa8c16;">温馨提示</strong>
                                </div>
                                <p style="margin: 0; color: #fa8c16; font-size: 14px;">
                                    修改任务信息后，系统将自动触发该任务下所有条目的重新应答，请谨慎操作。
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <input type="hidden" id="editTaskId">
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideEditTaskModal()">取消</button>
                <button class="btn btn-primary" onclick="updateTask()">保存</button>
            </div>
        </div>
    </div>

    <!-- 复制任务弹窗 -->
    <div id="copyTaskModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">复制任务</h3>
                <button class="modal-close" onclick="hideCopyTaskModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="copyTaskForm">
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label required">任务名称</label>
                            <input type="text" class="form-control" placeholder="请输入任务名称" required id="copyTaskName">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">国家/MTO</label>
                            <select class="form-control" id="copyTaskCountry">
                                <option value="">请选择</option>
                                <option value="中国">中国</option>
                                <option value="新加坡">新加坡</option>
                                <option value="德国">德国</option>
                                <option value="英国">英国</option>
                            </select>
                        </div>
                        <div class="form-item">
                            <label class="form-label">运营商/分支</label>
                            <select class="form-control" id="copyTaskCompany">
                                <option value="">请选择</option>
                                <option value="华为技术有限公司">华为技术有限公司</option>
                                <option value="华为软件技术有限公司">华为软件技术有限公司</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">客户</label>
                            <input type="text" class="form-control" placeholder="请输入客户名称" id="copyTaskCustomer">
                        </div>
                        <div class="form-item">
                            <label class="form-label">项目</label>
                            <input type="text" class="form-control" placeholder="请输入项目名称" id="copyTaskProject">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <div class="checkbox">
                                <input type="checkbox" id="copyItemResults">
                                <label for="copyItemResults">复制条目应答结果</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">应答条目文件</label>
                            <div class="upload-area" onclick="document.getElementById('copyFileInput').click()">
                                <div class="upload-icon">📁</div>
                                <div class="upload-text">点击上传Excel文件</div>
                                <input type="file" id="copyFileInput" accept=".xlsx,.xls" style="display: none;">
                            </div>
                        </div>
                    </div>
                    <input type="hidden" id="copyTaskId">
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideCopyTaskModal()">取消</button>
                <button class="btn btn-primary" onclick="confirmCopyTask()">复制</button>
            </div>
        </div>
    </div>

    <!-- 批量添加标签弹窗 -->
    <div id="batchAddTagModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">批量添加标签</h3>
                <button class="modal-close" onclick="hideBatchAddTagModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-item">
                    <label class="form-label">标签</label>
                    <input type="text" class="form-control" placeholder="输入标签后按回车添加" id="batchTagInput">
                    <div class="tag-list" id="batchTagList" style="margin-top: 8px;">
                        <!-- 标签将动态添加到这里 -->
                    </div>
                    <div class="help-text">按回车键添加标签，可添加多个标签</div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideBatchAddTagModal()">取消</button>
                <button class="btn btn-primary" onclick="confirmBatchAddTag()">确认</button>
            </div>
        </div>
    </div>

    <!-- 批量移除标签弹窗 -->
    <div id="batchRemoveTagModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">批量移除标签</h3>
                <button class="modal-close" onclick="hideBatchRemoveTagModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-item">
                    <label class="form-label">请选择要删除的标签</label>
                    <div id="availableTagsList">
                        <!-- 可选标签列表将动态生成 -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideBatchRemoveTagModal()">取消</button>
                <button class="btn btn-primary" onclick="confirmBatchRemoveTag()">确认</button>
            </div>
        </div>
    </div>

    <!-- 设置产品弹窗 -->
    <div id="setProductModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">新增产品</h3>
                <button class="modal-close" onclick="hideSetProductModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-item">
                    <div id="targetItemsCount" style="color: #666; margin-bottom: 15px;">将对 0 个条目进行操作</div>
                </div>
                
                <div class="form-item">
                    <label class="form-label">产品名称</label>
                    <input type="text" class="form-control" id="manualProductName" placeholder="请输入产品名称">
                    <div class="help-text" style="margin-top: 8px; font-size: 12px; color: #8c8c8c;">
                        注意：不能选择已经勾选的产品
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideSetProductModal()">取消</button>
                <button class="btn btn-primary" onclick="confirmSetProduct()">确认</button>
            </div>
        </div>
    </div>
    
    <!-- 产品操作确认弹窗 -->
    <div id="productConfirmModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">确认操作</h3>
                <button class="modal-close" onclick="hideProductConfirmModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div id="productConfirmMessage" style="font-size: 16px; line-height: 1.5;">
                    拟将产品调整至产品B并触发自动应答，请确认是否继续？
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="cancelProductOperation()">取消</button>
                <button class="btn btn-primary" onclick="confirmProductOperation()">确认</button>
            </div>
        </div>
    </div>

    <!-- 权限管理弹窗 -->
    <div id="permissionManagementModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">权限管理</h3>
                <button class="modal-close" onclick="hidePermissionManagementModal()">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 权限类型选择 -->
                <div class="form-item">
                    <label class="form-label required">权限类型</label>
                    <div style="display: flex; gap: 16px; margin-bottom: 16px;">
                        <div class="radio-item">
                            <input type="radio" id="permissionReadOnly" name="permissionType" value="readonly" onchange="onPermissionTypeChange()">
                            <label for="permissionReadOnly">只读管理</label>
                        </div>
                        <div class="radio-item">
                            <input type="radio" id="permissionAssign" name="permissionType" value="assign" onchange="onPermissionTypeChange()">
                            <label for="permissionAssign">指派应答</label>
                        </div>
                    </div>
                </div>

                <!-- 已选条目提示 -->
                <div class="form-item" id="selectedItemsInfo" style="display: none;">
                    <div style="padding: 12px; background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 4px; color: #52c41a;">
                        已选择 <span id="selectedItemsCount">0</span> 个条目
                    </div>
                </div>

                <!-- 人员选择 -->
                <div class="form-item">
                    <label class="form-label">选择人员</label>
                    <div class="personnel-selector">
                        <input type="text" class="form-control" placeholder="请输入人员姓名进行搜索" id="personnelSearchInput" oninput="searchPersonnel()">
                        <div class="personnel-dropdown" id="personnelDropdown" style="display: none;">
                            <div class="personnel-item" onclick="selectPersonnel('张三', '123456')">张三（123456）</div>
                            <div class="personnel-item" onclick="selectPersonnel('李四', '789012')">李四（789012）</div>
                            <div class="personnel-item" onclick="selectPersonnel('王五', '345678')">王五（345678）</div>
                            <div class="personnel-item" onclick="selectPersonnel('赵六', '901234')">赵六（901234）</div>
                            <div class="personnel-item" onclick="selectPersonnel('孙七', '567890')">孙七（567890）</div>
                        </div>
                    </div>
                </div>

                <!-- 已选人员列表 -->
                <div class="form-item">
                    <label class="form-label">已选人员</label>
                    <div class="selected-personnel-list" id="selectedPersonnelList">
                        <!-- 已选人员将动态显示在这里 -->
                    </div>
                </div>

                <!-- 只读管理人员列表（仅在只读管理模式下显示） -->
                <div class="form-item" id="readonlyPersonnelSection" style="display: none;">
                    <label class="form-label">当前只读人员</label>
                    <div class="readonly-personnel-list" id="readonlyPersonnelList">
                        <div class="personnel-tag" data-id="123456">
                            <span>张三（123456）</span>
                            <button type="button" class="remove-btn" onclick="markReadonlyPersonnelForRemoval('123456')">&times;</button>
                        </div>
                        <div class="personnel-tag" data-id="789012">
                            <span>李四（789012）</span>
                            <button type="button" class="remove-btn" onclick="markReadonlyPersonnelForRemoval('789012')">&times;</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hidePermissionManagementModal()">取消</button>
                <button class="btn btn-primary" onclick="confirmPermissionManagement()">确认</button>
            </div>
        </div>
    </div>

    <!-- 导出弹窗 -->
    <div id="exportModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">导出</h3>
                <button class="modal-close" onclick="hideExportModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-item">
                    <label class="form-label">选择要导出的产品</label>
                    <div id="exportProductList">
                        <div class="checkbox">
                            <input type="checkbox" id="exportAll" checked onchange="toggleExportAll()">
                            <label for="exportAll">全部产品</label>
                        </div>
                        <div class="checkbox">
                            <input type="checkbox" id="exportHCS" checked>
                            <label for="exportHCS">华为云Stack</label>
                        </div>
                        <div class="checkbox">
                            <input type="checkbox" id="exportFS" checked>
                            <label for="exportFS">FusionSphere</label>
                        </div>
                        <div class="checkbox">
                            <input type="checkbox" id="exportSecurity" checked>
                            <label for="exportSecurity">云安全服务</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideExportModal()">取消</button>
                <button class="btn btn-primary" onclick="confirmExport()">下载</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html> 