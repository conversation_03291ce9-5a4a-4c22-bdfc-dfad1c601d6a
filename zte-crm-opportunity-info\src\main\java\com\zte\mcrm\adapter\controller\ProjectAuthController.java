package com.zte.mcrm.adapter.controller;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.clouddisk.util.ServiceDataUtil;
import com.zte.mcrm.adapter.projectauthorization.dto.OpportunityAuthDto;
import com.zte.mcrm.adapter.projectauthorization.service.ProjectAuthorizationService;
import com.zte.mcrm.adapter.projectauthorization.utils.ProjectAuthorizationUtils;
import com.zte.mcrm.channel.model.dto.ProjectAuthInfoDto;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(tags = "项目授权相关接口")
@RestController
@RequestMapping("/projectAuth")
public class ProjectAuthController {
    @Autowired
    ProjectAuthorizationService projectAuthorizationService;

    @PostMapping("/getProjAuthInfosByOptyCd")
    public ServiceData<List<OpportunityAuthDto>> getProjAuthInfosByOptyCd(@RequestBody List<String> optyCds) throws Exception {
        return ServiceDataUtil.success(projectAuthorizationService.getProjAuthInfosByOptyCd(optyCds));
    }

    @PostMapping("/getProjectAuthInfoMap")
    public ServiceData<Map<String, ProjectAuthInfoDto>> getProjectAuthInfoMap(@RequestBody List<String> optyCds) throws Exception {
        return ServiceDataUtil.success(ProjectAuthorizationUtils.getProjectAuthInfo(optyCds));
    }
}
