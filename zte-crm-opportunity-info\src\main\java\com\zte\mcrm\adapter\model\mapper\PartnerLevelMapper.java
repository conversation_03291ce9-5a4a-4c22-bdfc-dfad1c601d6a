package com.zte.mcrm.adapter.model.mapper;

import com.zte.mcrm.adapter.model.dto.CertificationQueryResult;
import com.zte.mcrm.adapter.model.vo.PartnerLevelVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2021/11/19
 */
@Mapper
public interface PartnerLevelMapper {
    PartnerLevelMapper INSTANCE = Mappers.getMapper(PartnerLevelMapper.class);
    @Mapping(target = "certificationInfos", ignore = true)
    PartnerLevelVO transToPartnerLevel(CertificationQueryResult certificationQueryResult);
}
