package com.zte.mcrm.adapter.approval.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description: 参数查询条件
 * @author: 10243305
 * @date: 2021/7/28 下午3:36
 */
@Data
public class ParamQueryDTO {
    @ApiModelProperty(value = "查询模式，值参见 ParamQueryModelEnum")
    private String queryMode;
    @NotNull
    @ApiModelProperty(value = "查询值")
    private Object queryValue;
}
