package com.zte.mcrm.adapter.util;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * 正则相关Utils
 * <AUTHOR>
 * @date 2021/03/11
 */
public class PatternUtils {

    private PatternUtils() {
        throw new IllegalStateException("PatternUtils class");
    }
    
    /**数字字母中线*/
    public static final String ALPHANUMERIC_MIDLINE_REGEX = "^[A-Za-z0-9-]{1,50}$";

    /**日志ID的正则*/
    public static final String TRACE_ID_STRONG_REGEX = "^[A-Za-z0-9]{4,30}-[A-Za-z0-9]{6}-[0-9]{13}$";
    
    /**
     * 判断数据是否符合数字字母中线
     * <AUTHOR>
     * @date 2021/03/11
     * @param msg 数据
     * @return boolean
     */
    public static boolean isAlphanumericAndMidline(String msg){
        return Pattern.matches(ALPHANUMERIC_MIDLINE_REGEX, msg);
    }
    
    /**
     * 校验日志ID是否符合
     * <AUTHOR>
     * @date 2021/03/11
     * @param traceId  日志ID
     * @return boolean
     */
    public static boolean verifyTraceIdStrong(String traceId){
        return Pattern.matches(TRACE_ID_STRONG_REGEX, traceId);
    }

    public static String encapsulationTextByCDATA(String text) {
        if (StringUtils.isBlank(text)) {
            return "";
        } else {
            return String.format("<![CDATA[%s]]>", text);
        }
    }

}
