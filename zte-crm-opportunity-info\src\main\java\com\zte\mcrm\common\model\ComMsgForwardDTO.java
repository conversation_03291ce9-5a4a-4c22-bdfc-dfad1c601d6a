package com.zte.mcrm.common.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2021/12/17
 * @Description:
 */
@ApiModel(description = "消息转发DTO")
@Getter
@Setter
@ToString
@Builder
public class ComMsgForwardDTO {

    /**
     *消息编号
     */
    @ApiModelProperty(value = "消息编号", example = "03001")
    @NotBlank
    private String msgId;
    /**
     * 消息类型
     */
    @ApiModelProperty(value = "消息类型", example = "1")
    private Byte type;
    /**
     *接收人
     */
    @ApiModelProperty(value = "接收人", example = "[\"A0675023909398781952\",\"10269210\"]")
    private List<String> to;
    /**
     *抄送人
     */
    @ApiModelProperty(value = "抄送人", example = "[\"A0675023909398781952\",\"10269210\"]")
    private List<String> cc;
    /**
     *密送人
     */
    @ApiModelProperty(value = "密送人", example = "[\"A0675023909398781952\",\"10269210\"]")
    private List<String> bcc;
    /**
     * 发送人
     */
    @ApiModelProperty(value = "发送人", example = "10269210")
    private String sender;
    /**
     *模板中变量参数键值对 k-v
     */
    @ApiModelProperty(value = "模板中变量参数键值对 k-v", example = "{\"arg1\":\"11\"}")
    private Map<String, String> args;
}
