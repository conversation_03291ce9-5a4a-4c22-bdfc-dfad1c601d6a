package com.zte.mcrm.adapter.approval.enums;

/**
 * @description: 参数查询模式枚举
 * @author: 10243305
 * @date: 2021/7/28 下午4:00
 */
public enum ParamQueryModelEnum {
    /**
     * 精确匹配
     */
    EXACT_MATCH("EXACT_MATCH"),
    /**
     * 精确包含，in操作
     */
    EXACT_MATCH_IN("EXACT_MATCH_IN"),
    /**
     * 模糊匹配
     */
    FUZZY_MATCH("FUZZY_MATCH"),
    /**
     * 源数据为json数组的包含查询
     */
    JSON_ARRAY_MATCH_IN("JSON_ARRAY_MATCH_IN");

    private String code;

    ParamQueryModelEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
