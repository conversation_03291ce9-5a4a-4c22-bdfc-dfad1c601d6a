package com.zte.mcrm.channel.constant;

import com.google.common.collect.ImmutableList;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public enum OpportunityRoleEnum {

    /** 政企中国商机管理员 */
    GEC_OPPORTUNITY_ADMIN("GECOpportunityAdmin", "政企中国商机管理员", null),

    /** 政企中国中兴业务经理 */
    GEC_ZTE_BUSINESS_MANAGER("GECZteBusinessManager", "政企中国中兴业务经理", null),

    /** 政企中国总监办行业科长 */
    GEC_DIRECTOR_OFFICE_INDUSTRY_SECTION_CHIEF("GECDirectorOfficeIndustrySectionChief", "政企中国总监办行业科长/副总监",
            Arrays.asList(OpportunityApprovalNodeEnum.APPROVAL_NODE, OpportunityApprovalNodeEnum.ARBITRATION_NODE_2_2)),

    /** 政企中国办事处经理 */
    GEC_REPRESENTATIVE_OFFICE_BUSINESS_MANAGER("GECRepresentativeOfficeBusinessManager", "政企中国办事处经理",
            Arrays.asList(OpportunityApprovalNodeEnum.APPROVAL_NODE)),

    /** 政企中国商机创建人 */
    GEC_OPPORTUNITY_CREATOR("GECOpportunityCreator","政企中国商机创建人", null),

    /** 政企中国商机查询员 */
    GEC_OPPORTUNITY_QUERIER("GECOpportunityQuerier", "政企中国商机查询员", null),

    /** 政企中国渠道业务部部长 */
    GEC_DIRECTOR_OF_CHANNEL_BUSINESS_DEPARTMENT("GECDirectorOfChannelBusinessDepartment",
            "政企中国渠道业务部部长", Arrays.asList(OpportunityApprovalNodeEnum.ARBITRATION_NODE_0_1,
            OpportunityApprovalNodeEnum.ARBITRATION_NODE_1_3)),
    /** 政企中国MKT综合方案部部长 */
    GECHEAD_OF_MKT_INTEGRATED_SOLUTIONS_DEPARTMENT("GECHeadOfMKTIntegratedSolutionsDepartment",
            "政企中国MKT综合方案部部长", Arrays.asList(OpportunityApprovalNodeEnum.ARBITRATION_NODE_0_2,
            OpportunityApprovalNodeEnum.ARBITRATION_NODE_1_1)),
    /** 政企中国总监办总监 */
    GEC_DIRECTORS_OFFICE("GECDirectorSOffice", "政企中国总监办总监",
            Arrays.asList(OpportunityApprovalNodeEnum.ARBITRATION_NODE_0_3)),
    /** 政企中国渠道管理团队部长 */
    GECHEAD_OF_CHANNEL_MANAGEMENT_TEAM("GECHeadOfChannelManagementTeam", "政企中国渠道管理团队部长",
            Arrays.asList(OpportunityApprovalNodeEnum.ARBITRATION_NODE_0_3)),
    /** 政企中国新业务科长 */
    GEC_NEW_BUSINESS_SECTIONCHIEF("GECNewBusinessSectionchief", "政企中国新业务科长",
                                  Collections.emptyList()),
    /** 政企中国办事处销售副经理 */
    GEC_REPRESENTATIVE_OFFICE_DEPUTY_MANAGER("GECRepresentativeOfficeDeputyManager", "政企中国办事处销售副经理",
                                             Collections.emptyList()),
    /** 政企中国总监办副总监 */
    GEC_DEPUTY_DIRECTOR_OF_THE_DIRECTORS_OFFICE("GECDeputyDirectorOfTheDirectorSOffice",
            "政企中国总监办副总监", Arrays.asList(OpportunityApprovalNodeEnum.ARBITRATION_NODE_1_2)),
    /** 政企中国MKT行业分管科长 */
    GEC_MKT_INDUSTRY_DIVISION_CHIEF("GECMKTIndustryDivisionChief","政企中国MKT行业分管科长",
            Arrays.asList(OpportunityApprovalNodeEnum.ARBITRATION_NODE_2_1)),
    /** 政企中国渠道业务部渠道总监 */
    GEC_CHANNEL_DIRECTOR_OF_CHANNEL_BUSINESS_DEPARTMENT("GECChannelDirectorOfChannelBusinessDepartment",
            "政企中国渠道业务部渠道总监", Arrays.asList(OpportunityApprovalNodeEnum.ARBITRATION_NODE_2_3));


    private final String code;
    private final String name;
    private final List<OpportunityApprovalNodeEnum> approvalNode;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public List<OpportunityApprovalNodeEnum> getApprovalNode() {
        return approvalNode;
    }

    OpportunityRoleEnum(String code, String name, List<OpportunityApprovalNodeEnum> approvalNode) {
        this.code = code;
        this.name = name;
        this.approvalNode = approvalNode;
    }

    /** 根据节点名称或编码获取角色编码列表 */
    public static List<String> getOpportunityRoleCodeByNodeNameOrCode(String val){
        List<String> result = new ArrayList<>();
        OpportunityApprovalNodeEnum approvalNode = OpportunityApprovalNodeEnum.valueOfNameOrCode(val);
        if (null == approvalNode){
            return result;
        }
        for (OpportunityRoleEnum value : OpportunityRoleEnum.values()) {
            if (CollectionUtils.isNotEmpty(value.approvalNode) && value.approvalNode.contains(approvalNode)){
                result.add(value.code);
            }
        }
        return result;
    }

    /**
     * 获取商机列表查询时有组织约束的角色编码
     * */
    public static List<String> getContraintRoles(){

        return ImmutableList.of(
                GEC_REPRESENTATIVE_OFFICE_BUSINESS_MANAGER.code,
                GEC_DIRECTOR_OFFICE_INDUSTRY_SECTION_CHIEF.code,
                GEC_OPPORTUNITY_QUERIER.code,
                GEC_DIRECTOR_OF_CHANNEL_BUSINESS_DEPARTMENT.code,
                GECHEAD_OF_MKT_INTEGRATED_SOLUTIONS_DEPARTMENT.code,
                GEC_DIRECTORS_OFFICE.code,
                GECHEAD_OF_CHANNEL_MANAGEMENT_TEAM.code,
                GEC_DEPUTY_DIRECTOR_OF_THE_DIRECTORS_OFFICE.code,
                GEC_MKT_INDUSTRY_DIVISION_CHIEF.code,
                GEC_CHANNEL_DIRECTOR_OF_CHANNEL_BUSINESS_DEPARTMENT.code
                );
    }

    /**
     * 获取能显示合规刷新按钮的权限集合
     * */
    public static List<String> getDisplayComplianceRefreshButtonRoles(){
        return Arrays.asList(GEC_OPPORTUNITY_ADMIN.code,GEC_ZTE_BUSINESS_MANAGER.code);
    }
}
