package com.zte.mcrm.channel.cron;

import com.zte.itp.timedjobs.annotation.ZTEJobWorker;
import com.zte.itp.timedjobs.api.ZteJobContext;
import com.zte.itp.timedjobs.api.ZteJobInterface;
import com.zte.mcrm.channel.constant.LastAccStatusEnum;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.channel.model.entity.OpportunityDetail;
import com.zte.mcrm.channel.service.channel.IOpportunityDetailService;
import com.zte.mcrm.channel.service.channel.IOpportunityInfoService;
import com.zte.mcrm.channel.util.ExceptionMsgUtils;
import com.zte.mcrm.common.access.vo.ComDictionaryMaintainVO;
import com.zte.mcrm.common.business.service.IComDictionaryMaintainService;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.model.SysGlobalConstVo;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.mcrm.logger.bussiness.service.LoggerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/12
 */
@Component
@Slf4j
@ZTEJobWorker(value = "StartFlowTask")
public class StartFlowTask implements ZteJobInterface {
    private static final Logger LOGGER = LoggerFactory.getLogger(StartFlowTask.class);

    @Autowired
    IOpportunityInfoService opportunityInfoService;
    @Autowired
    IOpportunityDetailService opportunityDetailService;
    @Autowired
    private LoggerService loggerService;
    @Autowired
    IComDictionaryMaintainService comDictionaryMaintainService;

    @Override
    public void execute(ZteJobContext zteJobContext) {
        int shardingItem = zteJobContext.getShardingItem();
        if (shardingItem == OpportunityConstant.FIRST_SHARDING) {
            LOGGER.info("---------------------startFlowTask开始执行---------------" + "当前时间:{}", new Date());
            try{
                updateLastAccStatusBatch();
            }catch (Exception e){
                LOGGER.error("startFlowTask执行出错", e);
            }
            LOGGER.info("---------------------startFlowTask执行结束---------------" + "当前时间:{}", new Date());
        }
    }

    public void updateLastAccStatusBatch(){
        if (!taskSwitch()){
            log.info("startFlowTask开关未打开(code未设置为Y)");
            return;
        }
        SysGlobalConstVo sysGlobalConstVo = new SysGlobalConstVo();
        // 从数据库中查询所有最终用户状态为“创建客户草稿后生效的客户”的数据；
        List<OpportunityDetail> opportunityDetailList = getOpportunityDetailByLastAccStatus(LastAccStatusEnum.CUSTOMER_DRAFT_CREATED.getKey());
        for (OpportunityDetail opportunityDetail : opportunityDetailList) {
            try {
                sysGlobalConstVo.setxEmpNo(opportunityDetail.getCreatedBy());
                CommonUtils.setSysGlobalConstVo(sysGlobalConstVo);
                opportunityInfoService.refresh(opportunityDetail);
            } catch (Exception e) {
                log.error("[startFlowTask]refresh error,rowId:{}",
                        opportunityDetail.getRowId(), e);
                String logStr = "rowId:[ " + opportunityDetail.getRowId() + "]Error,Exception: " + ExceptionMsgUtils.getStackTrace(e, 2000);
                loggerService.saveLogger(logStr, "startFlowTask");
            }finally {
                CommonUtils.remove();
            }
        }
    }

    /**
     * 根据最终用户状态从数据库中获取对应的数据
     * @param lastAccStatus 最终用户状态
     * @return List<OpportunityDetail>
     * <AUTHOR>
     * @date 2021/10/13
     */
    private List<OpportunityDetail> getOpportunityDetailByLastAccStatus(Integer lastAccStatus) {
        return opportunityDetailService.getOpportunitiesByLastAccStatus(lastAccStatus);
    }
    private boolean taskSwitch(){
        // 从数据库获取任务开关值
        List<ComDictionaryMaintainVO> comDictionaryMaintains = comDictionaryMaintainService.queryByType("startFlowTask");
        if (CollectionUtils.isNotEmpty(comDictionaryMaintains)) {
            String taskSwitchCode = comDictionaryMaintains.get(0).getCode();
            return CommonConst.Y.equals(taskSwitchCode);
        }
        return false;
    }
}
