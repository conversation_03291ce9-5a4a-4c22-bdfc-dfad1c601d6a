-- =====================================================
-- SOC应答系统 - soc_item_product表字段补充脚本
-- 执行时间: 2024-07-30
-- 说明: 为soc_item_product表添加缺失的字段，使其与soc_item_history表保持一致
-- =====================================================

-- 检查表是否存在
SELECT COUNT(*) as table_exists 
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name = 'soc_item_product';

-- 添加补充信息字段
ALTER TABLE soc_item_product 
ADD COLUMN additional_info TEXT COMMENT '补充信息' 
AFTER response_content;

-- 添加备注字段
ALTER TABLE soc_item_product 
ADD COLUMN remark TEXT COMMENT '备注' 
AFTER additional_info;

-- 添加当前版本号字段
ALTER TABLE soc_item_product 
ADD COLUMN current_version INT DEFAULT 1 COMMENT '当前版本号' 
AFTER remark;

-- 为新增字段添加索引（可选，根据查询需求）
-- ALTER TABLE soc_item_product ADD INDEX idx_current_version (current_version);

-- 验证字段添加结果
DESCRIBE soc_item_product;

-- 显示表结构确认
SHOW CREATE TABLE soc_item_product;

-- =====================================================
-- 数据迁移说明（如果需要）
-- =====================================================
-- 如果已有数据需要迁移，可以执行以下操作：

-- 1. 为现有记录设置默认版本号
-- UPDATE soc_item_product 
-- SET current_version = 1 
-- WHERE current_version IS NULL;

-- 2. 如果需要从其他表迁移补充信息，可以参考以下模板：
-- UPDATE soc_item_product p 
-- LEFT JOIN other_table o ON p.id = o.item_product_id 
-- SET p.additional_info = o.additional_info,
--     p.remark = o.remark
-- WHERE o.id IS NOT NULL;

-- =====================================================
-- 回滚脚本（如果需要撤销更改）
-- =====================================================
-- ALTER TABLE soc_item_product DROP COLUMN additional_info;
-- ALTER TABLE soc_item_product DROP COLUMN remark;
-- ALTER TABLE soc_item_product DROP COLUMN current_version;
