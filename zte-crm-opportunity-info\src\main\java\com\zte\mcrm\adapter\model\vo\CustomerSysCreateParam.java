package com.zte.mcrm.adapter.model.vo;

import lombok.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 客户系统创建客户参数
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSysCreateParam {

    /**
     * 公司名称
     */
    private String customerName;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 客户类型
     */
    private String accountType;

    /**
     * 客户子类型
     */
    private String accountSubType;

    /**
     * ZTE业务客户分类
     */
    private String accountThiType;

    /**
     * 通讯地址
     */
    private String communicateAddress;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 业务经理所在部门
     */
    private String deptNo;

    /**
     * 所属部门
     */
    private String fromDeptNo;

    /**
     * 中兴业务经理工号
     */
    private String businessManagerNo;

    /**
     * 业务经理姓名+工号
     */
    private String businessManager;

    /**
     * 创建日期
     */
    private String createDate;

    /**
     * 注册地址
     */
    private String registerAddress;

    /**
     * 营业执照附件
     */
    private String businessLicenseKey;

    /**
     * 参数：
     *  客户名称
     *  联系人电话
     * @return
     */
    public Map<String, Object> getSsQueryParams() {
        Map<String, Object> map = new HashMap<String, Object>(26);

        map.put("PRM_ID", "************");
        map.put("ACCOUNT_NAME", customerName);
        map.put("ACCOUNT_ALIAS", customerName);
        map.put("ACCOUNT_SCOPE", "N");
        map.put("COUNTRY", "0001");

        map.put("ACCOUNT_STATUS", "CUSTOMER");
        map.put("CONTACT_STATUS", "Y");
        map.put("SUBSIDIARY_FLG", "N");
        map.put("FINS_TYPE", "FINS ACNT 02");

        map.put("MAIN_PHONE", phone);
        map.put("ACCOUNT_TYPE", accountType);
        map.put("ACCOUNT_SUB_TYPE", accountSubType);
        map.put("ACCOUNT_THI_TYPE", "10504");
        map.put("LOCAL", communicateAddress);
        map.put("COUNTRY_CODE", "86");

        map.put("CREATED", createDate);
        map.put("DME_URL", "");
        map.put("SOURCE_SYS", "PRM");
        map.put("RESERVED_01", registerAddress);
        map.put("RESERVED_02", businessManagerNo);
        map.put("RESERVED_03", businessLicenseKey);

        map.put("CITY_CODE", "0728");
        map.put("CITY", cityCode);
        map.put("DEPT_NO", deptNo);
        map.put("from_department", fromDeptNo);
        map.put("ACCOUNT_TEAM", businessManagerNo);
        return map;
    }


}
