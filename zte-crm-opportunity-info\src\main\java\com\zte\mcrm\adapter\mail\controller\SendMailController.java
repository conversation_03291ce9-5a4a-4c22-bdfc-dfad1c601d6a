package com.zte.mcrm.adapter.mail.controller;


import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.mail.domain.ZmailModel;
import com.zte.mcrm.adapter.mail.dto.MailDTO;
import com.zte.mcrm.adapter.mail.service.SendMailService;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.channel.model.entity.OpportunityMailEntity;
import com.zte.mcrm.common.exception.BusinessRuntimeException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "邮件")
@RestController
@RequestMapping("/mail")
public class SendMailController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SendMailController.class);

    @Autowired
    private SendMailService sendMailService;


    @ApiOperation("发送邮件")
    @PostMapping("/send")
    public ServiceData<Boolean> sendMail(@ApiParam @RequestBody MailDTO mailDTO) {
        ServiceData<Boolean> result = new ServiceData<>();
        try {
            sendMailService.send(new ZmailModel(mailDTO));
        }catch (Exception e){
            LOGGER.error("send mail: {} error: {}", mailDTO, e.getMessage(), e);
            e.printStackTrace();
            throw new BusinessRuntimeException("send mail error");
        }
        result.setBo(true);
        return result;
    }

    @ApiOperation("发送商机邮件")
    @PostMapping("/optyMail")
    public ServiceData<Boolean> sendOptyMail(@ApiParam @RequestBody OpportunityMailEntity entity) {
        ServiceData<Boolean> result = new ServiceData<>();
        try {
            sendMailService.sendOptyMail(entity);
        }catch (Exception e){
            LOGGER.error("send mail: {} error: {}", entity, e.getMessage(), e);
            e.printStackTrace();
            throw new BusinessRuntimeException(OpportunityConstant.SEND_MAIL_ERROR);
        }
        result.setBo(true);
        return result;
    }
}
