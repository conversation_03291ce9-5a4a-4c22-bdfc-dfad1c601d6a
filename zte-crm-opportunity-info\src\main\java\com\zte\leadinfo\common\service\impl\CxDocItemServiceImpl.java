package com.zte.leadinfo.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.zte.leadinfo.common.entity.CxDocItemDO;
import com.zte.leadinfo.common.mapper.CxDocItemMapper;
import com.zte.leadinfo.common.service.CxDocItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务接口实现
 *
 * <AUTHOR>
 * @description 由 Mybatisplus Code Generator 创建
 * @since 2024-06-04 11:17:45
 */
@Slf4j
@Service
public class CxDocItemServiceImpl extends ServiceImpl<CxDocItemMapper, CxDocItemDO> implements CxDocItemService {

    @Override
    public List<CxDocItemDO> listByBillCodes(List<String> billCodes) {
        if (CollectionUtils.isEmpty(billCodes)) {
            return Lists.newArrayList();
        }

        billCodes = billCodes.stream().filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

        LambdaQueryWrapper<CxDocItemDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CxDocItemDO::getBillCode, billCodes);
        return this.list(queryWrapper);
    }
}