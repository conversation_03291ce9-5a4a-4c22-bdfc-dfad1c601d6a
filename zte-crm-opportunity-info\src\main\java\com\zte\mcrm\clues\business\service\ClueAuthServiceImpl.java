package com.zte.mcrm.clues.business.service;

import com.zte.itp.authorityclient.entity.output.ReturnConstraintEntity;
import com.zte.itp.authorityclient.entity.output.ReturnConstraintValue;
import com.zte.mcrm.authority.business.IAuthorityClientService;
import com.zte.mcrm.clues.common.CluesSysConst;
import com.zte.mcrm.common.model.PersonAndOrgInfoVO;
import com.zte.mcrm.common.util.OrganizationUtil;
import com.zte.mcrm.common.util.RequestMessage;
import com.zte.mcrm.opportunity.access.dao.PCOpportunityDao;
import com.zte.mcrm.opportunity.access.vo.Auth;
import com.zte.mcrm.opportunity.access.vo.RoleInfomation;
import com.zte.mcrm.opportunity.common.OppSysConst;
import com.zte.mcrm.opportunity.utils.OppAuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/****
 * @ClassName ClueAuthServiceImpl
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/2/4 
 * @Version V1.0
 **/
@Slf4j
@Service
public class ClueAuthServiceImpl implements IClueAuthService {

    @Autowired
    IAuthorityClientService authorityClientService;
    @Autowired
    PCOpportunityDao pcOpportunityDao;

    @Override
    public Auth getAuthOrgs() {
        Auth auth = new Auth();
        Map<String, ReturnConstraintEntity> dataConstrainMap = authorityClientService.getUserPowerFunctionCode(CluesSysConst.CLUE_AUTH_CODE);
        //授权管理员
        List<String> authAdministratorList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.AUTH_ADMINISTRATOR_ORG)).orElse(new ArrayList<>());
        // 商机综合查询员
        List<String> opportunityQueryList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.OPPORTUNITY_QUERY_ORG)).orElse(new ArrayList<>());
        // 国代/办事处经理
        List<String> officeManagerList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.OFFICE_MANAGER_ORG)).orElse(new ArrayList<>());
        //副国代/办事处经理
        List<String> viceOfficeManagerList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.VICE_OFFICE_MANAGER)).orElse(new ArrayList<>());
        //总监办客户经理
        List<String> directorAccountManagerList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.DIRECTOR_ACCOUNT_MANAGER_ORG)).orElse(new ArrayList<>());
        //商机总监
        List<String> opportunityDirectorList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.OPPORTUNITY_DIRECTOR_ORG)).orElse(new ArrayList<>());
        //大国CTO/片区CMO
        List<String> ctoList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.CTO_ORG)).orElse(new ArrayList<>());
        //服务商机总监
        List<String> serviceOpportunityDirectorList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.SERVICE_OPPORTUNITY_DIRECTOR_ORG)).orElse(new ArrayList<>());
        // 片区技术负责人
        List<String> areaTechManagerList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.AREA_TECH_MANAGER_ORG)).orElse(new ArrayList<>());
        //商机导出
        List<String> opportunityExportList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.OPPORTUNITY_EXPORT_ORG)).orElse(new ArrayList<>());
        // 商机客户经理
        List<String> opportunityAccountManagerList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.OPPORTUNITY_ACCOUNT_MANAGER_ORG)).orElse(new ArrayList<>());

        // 汇总所有授权组织
        List<String> authAdministrator = new ArrayList<>(authAdministratorList);
        authAdministrator.addAll(opportunityQueryList);
        authAdministrator.addAll(officeManagerList);
        authAdministrator.addAll(viceOfficeManagerList);
        authAdministrator.addAll(directorAccountManagerList);
        authAdministrator.addAll(opportunityDirectorList);
        authAdministrator.addAll(ctoList);
        authAdministrator.addAll(serviceOpportunityDirectorList);
        authAdministrator.addAll(areaTechManagerList);
        authAdministrator.addAll(opportunityExportList);
        authAdministrator.addAll(opportunityAccountManagerList);

        // 组织去重
        List<String> orgDistinct = authAdministrator.stream().distinct().collect(Collectors.toList());
        // 转换成siebel的组织id
        if (null != orgDistinct && !orgDistinct.isEmpty()) {
            Collections.replaceAll(orgDistinct,OppSysConst.ORG_ZTE_HR, OppSysConst.SIEBEL_ZTE_ORG);
            List<String> ids = pcOpportunityDao.getIdsByOrgNos(orgDistinct);
            auth.setSubOrgs(ids);
        }else {
            List<String> orgs=new ArrayList<>();
            auth.setSubOrgs(orgs);
        }

        return auth;
    }

    @Override
    public Auth getBusinessClueListAuth() throws Exception {
        Auth auth = new Auth();
        // 汇总所有授权组织
        Set<String> authOrgSet = this.getRoleOrgSet();
        // 获取组织全路径
        if(CollectionUtils.isEmpty(authOrgSet)){
            return auth;
        }
        List<PersonAndOrgInfoVO> orgInfo = OrganizationUtil.getOrgsByOrgNos(authOrgSet);
        if(CollectionUtils.isEmpty(orgInfo)){
            return auth;
        }
        Set<String> authOrgPathSet = orgInfo.stream().map(PersonAndOrgInfoVO::getOrgIDPath).collect(Collectors.toSet());
        auth.setSubOrgPathSet(authOrgPathSet);
        return auth;
    }

    private Set<String> getRoleOrgSet() {
        // 查询当前用户在对应模块下授权的组织
        Map<String, ReturnConstraintEntity> dataConstrainMap = authorityClientService.getUserPowerFunctionCode(CluesSysConst.CLUE_AUTH_CODE);
        if(Objects.isNull(dataConstrainMap) || dataConstrainMap.isEmpty()){
            return Collections.emptySet();
        }
        // 所有授权组织
        Set<String> authOrgSet = new HashSet<>();
        // 授权管理员
        authOrgSet.addAll(this.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.AUTH_ADMINISTRATOR_ORG));
        // 商机综合查询员
        authOrgSet.addAll(this.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.OPPORTUNITY_QUERY_ORG));
        // 国代/办事处经理
        authOrgSet.addAll(this.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.OFFICE_MANAGER_ORG));
        // 副国代/办事处经理
        authOrgSet.addAll(this.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.VICE_OFFICE_MANAGER));
        // 总监办客户经理
        authOrgSet.addAll(this.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.DIRECTOR_ACCOUNT_MANAGER_ORG));
        // 商机总监
        authOrgSet.addAll(this.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.OPPORTUNITY_DIRECTOR_ORG));
        // 大国CTO/片区CMO
        authOrgSet.addAll(this.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.CTO_ORG));
        // 服务商机总监
        authOrgSet.addAll(this.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.SERVICE_OPPORTUNITY_DIRECTOR_ORG));
        // 片区技术负责人
        authOrgSet.addAll(this.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.AREA_TECH_MANAGER_ORG));
        // 商机导出
        authOrgSet.addAll(this.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.OPPORTUNITY_EXPORT_ORG));
        // 商机客户经理
        authOrgSet.addAll(this.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.OPPORTUNITY_ACCOUNT_MANAGER_ORG));

        return authOrgSet;
    }

    private Set<String> getAuthDataByConstrainCode(Map<String, ReturnConstraintEntity> dataConstrainMap, String roleCode) {
        // 校验
        if(!dataConstrainMap.containsKey(roleCode) || Objects.isNull(dataConstrainMap.get(roleCode))){
            return Collections.emptySet();
        }
        // 根据key获取数据授权
        ReturnConstraintEntity constraint = dataConstrainMap.get(roleCode);
        // 获取授权数据
        List<ReturnConstraintValue> returnConstraintList = constraint.getConstraintValueList();
        if(CollectionUtils.isEmpty(returnConstraintList)){
            return Collections.emptySet();
        }
        // 授权组织
        List<String> constraintValueList = returnConstraintList.stream().map(ReturnConstraintValue::getConstraintValue).collect(Collectors.toList());
        // 去重
        Set<String> orgCodeSet = new HashSet<>();
        for(String constraintValue : constraintValueList){
            orgCodeSet.addAll(Arrays.asList(constraintValue.split(",")));
        }
        return orgCodeSet;
    }

}
