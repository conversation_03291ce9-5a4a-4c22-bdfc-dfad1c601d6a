package com.zte.mcrm.adapter.service;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.constant.UcsFeignSupportConfig;
import com.zte.mcrm.adapter.model.dto.AccountDetailQueryDTO;
import com.zte.mcrm.adapter.model.dto.AccountQueryAllDTO;
import com.zte.mcrm.adapter.model.dto.AccountQueryAllRespDTO;
import com.zte.mcrm.adapter.model.vo.AccountDetail;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 * @date 2021/9/16
 */
@FeignClient(name = "zte-bmt-ucs-api",
path = "zte-bmt-ucs-api",
configuration = UcsFeignSupportConfig.class)
public interface BmtUserServiceApi {
    /**
     * 查询用户账号详细信息
     * @param entity
     */
    @RequestMapping(value="/srv/account/detail", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    ServiceData<AccountDetail> getAccountDetail(@RequestBody AccountDetailQueryDTO entity);

    /**
     * 根据id批量查询用户信息
     * @param accountQueryAllDTO
     * @return
     */
    @RequestMapping(value="/srv/v2/account/queryall", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    ServiceData<AccountQueryAllRespDTO> queryUserInfoAllByAccountIdList(@RequestBody AccountQueryAllDTO accountQueryAllDTO);

}
