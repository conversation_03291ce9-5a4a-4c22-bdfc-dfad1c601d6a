package com.zte.mcrm.channel.util;

import com.alibaba.fastjson.JSON;
import com.zte.springbootframe.common.exception.BusiException;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Map工具类
 * <AUTHOR>
 * @date 2021/10/9
 */
public class MapUtil {
    /**
     * map转java对象
     */
    public static <T> T mapToObject(Map<String, Object> map, Class<T> t) throws Exception {
        if (map == null) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(map), t);
    }

    /**
     * java对象转map
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> objectToMap(Object obj) throws BusiException {
        try {
            if (obj == null) {
                return null;
            }
            Map<String, Object> map = new HashMap<String, Object>(16);
            Field[] declaredFields = obj.getClass().getDeclaredFields();
            for (Field field : declaredFields) {
                field.setAccessible(true);
                Object o = field.get(obj);
                if (o == null) {
                    continue;
                }
                if (o.getClass().getName().toLowerCase().contains("list")) {
                    List<Object> list = (List<Object>) o;
                    if (list.size() == 0) {
                        continue;
                    }
                }
                map.put(field.getName(), field.get(obj));
            }

            return map;
        } catch (Exception e) {
            throw new BusiException();
        }

    }
}
