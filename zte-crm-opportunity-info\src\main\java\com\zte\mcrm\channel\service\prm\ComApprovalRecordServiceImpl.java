package com.zte.mcrm.channel.service.prm;

import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.util.web.HttpHeaderUtil;
import com.zte.mcrm.channel.dao.ComApprovalRecordDao;
import com.zte.mcrm.channel.model.entity.ComApprovalRecord;
import com.zte.mcrm.common.business.service.IKeyIdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class ComApprovalRecordServiceImpl implements IComApprovalRecordService{
    @Autowired
    ComApprovalRecordDao comApprovalRecordDao;
    @Autowired
    private IKeyIdService iKeyIdService;

    /**
     * 根据主键查询
     *
     * @return 实体
     * @date 2021/10/09
     */
    @Override
    public ComApprovalRecord get(Long rowId) {
        return comApprovalRecordDao.get(rowId);
    }

    /**
     * 根据业务id查询
     *
     * @param businessId 主键
     * @return 实体
     * @date 2021/10/09
     */
    @Override
    public ComApprovalRecord getByBusinessId(String businessId) {
        return comApprovalRecordDao.getByBusinessId(businessId);
    }

    /**
     * 查询列表
     *
     * @param map 查询条件
     */
    @Override
    public List<ComApprovalRecord> getList(Map<String, Object> map) {
        return comApprovalRecordDao.getList(map);
    }

    /**
     * 软删除，enabled_flag字段更新为N
     *
     * @param rowId 主键
     * @return 删除总数
     */
    @Override
    public int softDelete(Long rowId) {
        return comApprovalRecordDao.softDelete(rowId);
    }

    /**
     * 根据流程id和业务Id失效记录
     *
     * @return
     */
    @Override
    public int softDeleteByBusinessIdAndFlowId(String businessId, String workFlowInstanceId) {
        return comApprovalRecordDao.softDeleteByBusinessIdAndFlowId(businessId, workFlowInstanceId);
    }

    /**
     * 删除
     *
     * @param rowId 主键
     * @return 删除总数
     */
    @Override
    public int delete(Long rowId) {
        return comApprovalRecordDao.delete(rowId);
    }

    /**
     * 动态新增
     *
     * @param entity 新增实体
     * @return 新增总数
     */
    @Override
    public int insert(ComApprovalRecord entity) {
        String emp = HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
        Date now = new Date();
        entity.setCreatedBy(emp);
        entity.setLastUpdatedBy(emp);
        entity.setCreatedDate(now);
        entity.setLastUpdatedDate(now);
        if (null==entity.getRowId()) {
            entity.setRowId(iKeyIdService.getKeyLongId());
        }
        return comApprovalRecordDao.insert(entity);
    }

    @Override
    public int insertAny(ComApprovalRecord entity) {
        if (null==entity.getRowId()) {
            entity.setRowId(iKeyIdService.getKeyLongId());
        }
        return comApprovalRecordDao.insert(entity);
    }

    /**
     * 批量新增
     *
     * @param list 新增实体集合
     * @return 新增总数
     */
    @Override
    public int insertByBatch(List<ComApprovalRecord> list) {
        return comApprovalRecordDao.insertByBatch(list);
    }

    /**
     * 更新
     *
     * @param entity 更新条件
     * @return 更新影响总数
     */
    @Override
    public int update(ComApprovalRecord entity) {
        return comApprovalRecordDao.update(entity);
    }

    /**
     * 统计
     *
     * @param map 查询条件
     * @return 统计总数
     */
    @Override
    public long getCount(Map<String, Object> map) {
        return comApprovalRecordDao.getCount(map);
    }

    /**
     * 分页查询
     *
     * @param map 查询条件
     * @return 实体集合
     */
    @Override
    public List<ComApprovalRecord> getPage(Map<String, Object> map) {
        return comApprovalRecordDao.getPage(map);
    }

    /**
     * 获取流程实例Id
     *
     * @param businessId 业务Id
     * @return 流程实例Id
     */
    @Override
    public String queryFlowInstance(String businessId) {
        return comApprovalRecordDao.queryFlowInstance(businessId);
    }
}
