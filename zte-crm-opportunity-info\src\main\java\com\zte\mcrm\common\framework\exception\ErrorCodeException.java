package com.zte.mcrm.common.framework.exception;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.common.errorcode.util.ErrorCodeMessageUtil;
import org.jetbrains.annotations.PropertyKey;

/**
 * 错误码异常类
 *
 * <AUTHOR>
 * @date 2019/11/6
 */
public class ErrorCodeException extends RuntimeException {

    /**
     * 业务异常编码
     */
    private ErrorCode errorCodeEnum;
    private String errorCode;
    private String errorCodeMessage;
    private String errorCodeTip;
    private ServiceData errorCodeServiceData;


    public String getErrorCode() {
        return errorCode;
    }

    public String getErrorCodeMessage() {
        return errorCodeMessage;
    }

    public String getErrorCodeTip() {
        return errorCodeTip;
    }

    public ServiceData getErrorCodeServiceData() {
        return errorCodeServiceData;
    }


    /**
     * 业务可变参数
     */
    private Object[] arguments;

    public ErrorCode getErrorCodeEnum() {
        return errorCodeEnum;
    }

    public Object[] getArguments() {
        return arguments;
    }

    public ErrorCodeException(ErrorCode errorCodeEnum, Object... arguments) {
        super(errorCodeEnum.code());
        this.errorCodeEnum = errorCodeEnum;
        setFieldValue(errorCodeEnum.code(), arguments);
    }

    public ErrorCodeException(@PropertyKey(resourceBundle = ErrorCodeMessageUtil.ERRORCODEMESSAGES_BUNDLE) String errorCode, Object... arguments) {
        super(errorCode);
        setFieldValue(errorCode, arguments);
    }

    private void setFieldValue(String errorCode, Object[] arguments) {
        this.errorCode = errorCode;
        this.arguments = arguments;
        this.errorCodeMessage = ErrorCodeMessageUtil.getErrorCodeMessage(errorCode, arguments);
        this.errorCodeTip = ErrorCodeMessageUtil.getErrorCodeTipByErrorMsg(errorCodeMessage);
        this.errorCodeServiceData = ErrorCodeMessageUtil.getErrorCodeServiceData(errorCode, arguments);
    }
}
