package com.zte.mcrm.adapter.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 查询渠道商认证信息出参
 *
 * <AUTHOR>
 * @date 2021/11/19
 */
@Data
public class CertificationQueryResult {
    @ApiModelProperty(value = "申请合作伙伴编码，公司账号")
    private String beid;

    @ApiModelProperty(value = "合作伙伴id  Partner ID")
    private String partnerId;


    @ApiModelProperty(value = "客户编码 Customer Code")
    private String crmCustomerCode;

    @ApiModelProperty(value = "统一社会信用代码 Unified Social Credit Code")
    private String socialCreditCode;

    @ApiModelProperty(value = "渠道商id customer_id")
    private Long customerId;

    @ApiModelProperty(value = "渠道商名称 customer_name")
    private String customerName;

    @ApiModelProperty(value = "受限制主体标签")
    private String gtsFlag;

    @ApiModelProperty(value = "受限制主体标签名称")
    private String gtsFlagName;

    @ApiModelProperty(value = "渠道商认证信息列表 Customer Certification Info List")
    private List<CertificationInfo> certificationInfos = new ArrayList<>();
}