
package com.zte.mcrm.adapter.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
public class CompanyOrgInfoVO {
    @ApiModelProperty(value="城市")
    private String city;
    @ApiModelProperty(value="城市Code")
    private String cityCode;
    @ApiModelProperty(value="国家/地区")
    private String country;
    @ApiModelProperty(value="国家/地区Code")
    private String countryCode;
    @ApiModelProperty(value="统一社会信用代码")
    private String creditCode;
    @ApiModelProperty(value="区")
    private String district;
    @ApiModelProperty(value="区Code")
    private String districtCode;
    @ApiModelProperty(value="N-国内 Y-国际")
    private String isInternational;
    @ApiModelProperty(value="组织名称")
    private String name;
    @ApiModelProperty(value="组织英文名称")
    private String nameEn;
    @ApiModelProperty(value="企业统一代码")
    private String orgUnifiedCode;
    @ApiModelProperty(value="省/州")
    private String province;
    @ApiModelProperty(value="省/州Code")
    private String provinceCode;
    @ApiModelProperty(value="注册地址")
    private String regLocation;

    @ApiModelProperty(value="工商快照")
    private CompanySnapshotVO companySnapshot;
}
