package com.zte.mcrm.adapter.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CustClassifyExternalQueryDTO {

    @ApiModelProperty("客户编码-批量")
    private List<String> accountCodes;
    @ApiModelProperty("客户名称-批量")
    private List<String> accountNames;
    @ApiModelProperty("客户名称")
    String accountName;
    @ApiModelProperty("法人实体ID")
    String corporateNo;

    @ApiModelProperty("单据组织编码")
    String organizationId;
}
