package com.zte.mcrm.adapter.model.vo;

import com.google.common.base.Splitter;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * 商机行业与SS系统行业映射
 * @date 2023-05-12
 * <AUTHOR>
 */
@Getter
@Setter
public class IndustryMappingBetweenLineAndSs {
    public static final int SS_INDUSTRY_SIZE = 2;
    /**
     * 商机行业
     */
    private String rawParentIndustry;
    /**
     * 商机子行业
     */
    private String rawSubIndustry;
    /**
     * SS行业
     */
    private String parentIndustryFromSs;
    /**
     * SS子行业
     */
    private String subIndustryFromSs;

    public IndustryMappingBetweenLineAndSs() {

    }

    public IndustryMappingBetweenLineAndSs(String rawParentIndustry, String rawSubIndustry) {
        this.rawParentIndustry = rawParentIndustry;
        this.rawSubIndustry = rawSubIndustry;
    }

    /**
     * 设置客户系统的主行业和子行业
     * @param customerIndustryCompose
     */
    public void setCustomerIndustry(String customerIndustryCompose) {
        if (StringUtils.isBlank(customerIndustryCompose)) {
            return;
        }
        List<String> customerIndustryList = Splitter.on(":").omitEmptyStrings().trimResults().splitToList(customerIndustryCompose);
        if (!Objects.equals(customerIndustryList.size(), SS_INDUSTRY_SIZE)) {
            throw new IllegalStateException("the number of ss industry from lineToSSIndustry must be two");
        }
        parentIndustryFromSs = customerIndustryList.get(0);
        subIndustryFromSs = customerIndustryList.get(1);
    }
}
