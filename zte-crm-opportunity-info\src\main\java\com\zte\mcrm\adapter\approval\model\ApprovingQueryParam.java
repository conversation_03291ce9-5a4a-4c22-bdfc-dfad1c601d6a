package com.zte.mcrm.adapter.approval.model;

import com.zte.mcrm.adapter.approval.model.dto.ParamQueryDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * @description: 待办高级查询请求参数
 * @author: 10243305
 * @date: 2021/7/28 下午3:50
 */
@Data
public class ApprovingQueryParam {
    @ApiModelProperty(value = "应用编码，渠道默认为 zte-crm-ichannel")
    private String appCode;
    @ApiModelProperty(value = "流程编码")
    private List<String> flowCodes;
    @ApiModelProperty(value = "业务id")
    private String businessId;
    @ApiModelProperty(value = "流程实例id")
    private String flowInstanceId;
    @NotBlank
    @ApiModelProperty(value = "操作人")
    private String empNo;
    @ApiModelProperty(value = "申请时间查询区间")
    private DateTimeInterval submitTimeInterval;
    @ApiModelProperty(value = "业务过滤条件")
    private Map<String, @Valid ParamQueryDTO> advancedCriteria;
    @NotNull
    @ApiModelProperty(value = "页码")
    private Long pageNo;
    @NotNull
    @ApiModelProperty(value = "页大小，上限500")
    private Long pageSize;
}
