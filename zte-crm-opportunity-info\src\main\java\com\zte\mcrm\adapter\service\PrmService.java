package com.zte.mcrm.adapter.service;

import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.adapter.model.AuthConstraintDTO;
import com.zte.mcrm.adapter.model.dto.*;
import com.zte.mcrm.adapter.model.vo.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/9/16
 */
public interface PrmService {

    /**
     * 查询行业树
     * @return IndustryTreeDataVO
     * <AUTHOR>
     * @date 2021/9/16
     */
    IndustryTreeDataVO listIndustry(String needInvalid) throws Exception;

    /**
     * 查询行业树
     * @return IndustryTreeDataVO
     * <AUTHOR>
     * @date 2021/9/16
     */
    PageRows<TreeNodeVO> getIndustryTree(IndustryTreeDTO industryTreeDTO) throws Exception;

    /**
     * 查询所有子行业列表
     *
     * @return 子行业列表
     * @throws Exception
     * <AUTHOR>
     * @date 2021年9月24日 11:47:41
     */
    List<AuthConstraintDTO> getSubIndustryList(String needInvalid) throws Exception;

    /**
     * 查询渠道商客户列表
     * @param formData
     * @return
     * @throws Exception
     */
    List<ChannelCustomerRes> getChannelCustomerList(FormData<ChannelCustomerRes> formData) throws Exception;

    AccountDetail getAccountDetail(AccountDetailQueryDTO accountDetailQueryDTO) throws Exception;

    /**
     * 查询渠道商帐号基本信息
     * @return IndustryTreeDataVO
     * <AUTHOR>
     * @date 2021/9/16
     */
    ChannelAccountDetailDTO getChannelAccountBasicInfo(String orgUnifiedCode) throws Exception;
    /**
     * 對外接口服務，获取投资方所在地的组织
     * @return OrgConditionVos
     * <AUTHOR>
     * @date 2021/09/16
     */
    List<OrgConditionVO> getOrganizationNodeList(boolean filterChannelDepart) throws Exception;

    /**
     * 获取投资方所在地的组织列表，先按总监办、办事处标签顺序排序，总监办、办事处的再分别按首字母拼音顺序排序
     * @param filterChannelDepart 是否过滤渠道业务部
     * @return
     * @throws Exception
     */
    List<OrgConditionVO> getOrganizationNodeListWithSorted(boolean filterChannelDepart) throws Exception;

    /**
     * 對外接口服務，获取产品信息
     * @return 实体List
     * <AUTHOR>
     * @date 2021/09/16
     */
    List<ProductTreeData> getProductList() throws Exception;

    /**
     * base服务查询 组织树接口：查询政企中国所有子节点
     * @return IndustryTreeDataVO
     * <AUTHOR>
     * @date 2021/9/17
     */
    List<OrganizationTreeData> getDescendants() throws Exception;

    /**
     * base服务查询 组织树接口：精确查询政企中国所有子节点
     * @return IndustryTreeDataVO
     * <AUTHOR>
     * @date 2021/10/11
     */
    OrganizationTreeData getDescendants(String orgId) throws Exception;

    /**
     * base服务查询 组织树接口：根据树类型、是否可见、是否业绩单位、机构id、机构名称、机构标签组合查询机构树
     *                      支持根据机构id批量查询，支持分页。 返回数据结构列表形式，非树形结构
     * @return OrgConditionVos
     * <AUTHOR>
     * @date 2021/10/28
     */
    OrgConditionVos getCondition(OrgConditionParam orgConditionParam) throws Exception;

    /**
     * base服务查询 组织树接口,获取组织信息，最大1000条
     * @param orgIds
     * @return
     * @throws Exception
     */
    List<OrgConditionVO> getOrgInfosFromChannel(List<String> orgIds) throws Exception;

    /**
     * base服务查询 产品树接口：查询产品树
     * @return IndustryTreeDataVO
     * <AUTHOR>
     * @date 2021/9/17
     */
    List<ProductTreeData> getProduction() throws Exception;

    /**
     * base服务查询 根据行业id集合，批量获取行业名称
     *
     * @param industryIds
     * @param needInvalid
     * @return
     * @throws Exception
     */
    List<IndustryTreeDataVO> getIndustryNames(List<String> industryIds, String needInvalid) throws Exception;

    /**
     * base服务查询 根据模块id及用户工号批量获取角色集合信息
     * @param roleId 用户工号
     * @param moduleId 模块id
     * @return
     * @throws Exception
     */
    Map<String, List<RoleDTO>> queryRoleList(String roleId, String moduleId) throws Exception;

    /**
     * base服务查询 根据组织机构id批量获取组织机构名称
     * @param industryIds
     * @return
     * @throws Exception
     */
    List<OrganizationNode> getOrganizationNames(List<String> industryIds)throws Exception;

    /**
     * base服务查询 根据组织机构id批量获取组织机构名称
     * @param orgCodeList
     * @return
     * @throws Exception
     */
    List<OrganizationNode> getOrganizationNamesWithNoException(List<String> orgCodeList);

    /**
     * 查询所有子行业列表
     * @return 子行业列表
     * <AUTHOR>
     * @date 2021年9月24日 11:47:41
     */
    List<AuthConstraintDTO> getSubIndustryListWithNoException(String needInvalid);

    /**
     * 渠道认证信息服务
     *
     * @param entity
     * @return CertificationQueryResult
     * <AUTHOR>
     * @date 2021/11/19
     */
    PageRows<CertificationQueryResult> queryCertificationInfoCustomer(CertificationQueryInfo entity);

    /**
     * 查询渠道系统参数配置
     * @param codeType
     * @return
     */
    List<PrmQuickCodeValueVO> getByCodeType(String codeType);

    /**
     * 根据系统参数类型列表获取状态为有效和历史的配置值数据
     * @param codeType
     * @return
     */
    List<PrmQuickCodeValueVO> getEffectiveAndHistoryByCodeType(String codeType);

    /**
     * 获取SS客户系统的行业映射
     * @param parentIndustry
     * @param subIndustry
     */
    Optional<IndustryMappingBetweenLineAndSs> getCustomerSystemIndustryMapping(String parentIndustry, String subIndustry);

    /**
     * 获取政企接口人
     * @return
     */
    List<String> getGovInterfacePerson();

    /**
     * 获取新业务的行业编码，该配置来自系统参数配置：newBusinessIndustry
     * @param industryCode 行业编码
     * @return
     */
    boolean isNewBusinessIndustry(String industryCode);

}
