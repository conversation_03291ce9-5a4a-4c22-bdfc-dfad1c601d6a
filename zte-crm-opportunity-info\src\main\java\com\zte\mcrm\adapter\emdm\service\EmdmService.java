package com.zte.mcrm.adapter.emdm.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.config.ICommonConfig;
import com.zte.mcrm.adapter.emdm.model.CityCode;
import com.zte.mcrm.common.business.service.IKeyIdService;
import com.zte.mcrm.common.model.SysGlobalConstVo;
import com.zte.mcrm.common.util.CommonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class EmdmService {
    private static final Logger log = LoggerFactory.getLogger(EmdmService.class);
    @Autowired
    private IKeyIdService keyIdService;
    
    @Autowired
	private ICommonConfig constConfig;

    public String getCityCode(String cityName){
        Map<String, Object> map = new HashMap<String, Object>(4);
        Map<String, Object> esb = new HashMap<String, Object>(4);
        Map<String, Object> data = new HashMap<String, Object>(4);
        Map<String, Object> datainfos = new HashMap<String, Object>(4);
        Map<String, Object> splitpage = new HashMap<String, Object>(4);
        List<Object> list = new ArrayList<>();
        Map<String, Object> desc3 = new HashMap<String, Object>(1);
        desc3.put("DESC1",cityName+"%%");
        list.add(desc3);

        datainfos.put("DATAINFO",list);
        splitpage.put("COUNTPERPAGE",100);
        splitpage.put("CURRENTPAGE",1);

        map.put( "ESB",esb);
        esb.put( "DATA",data);
        data.put( "DATAINFOS",datainfos);
        data.put("SPLITPAGE",splitpage);
        datainfos.put("PUUID",keyIdService.getKeyLongId());
        try {
            String respone = MicroServiceRestUtil.invokeService("zte-bda-emdm-esbmule",
                    "v1", "post", "/services/query/custom_queryMenutreeGJDQ",
                    JSON.toJSONString(map),
                    headerParams());
            String result = String.valueOf(JSON.parseObject(String.valueOf(JSON.parseObject(String.valueOf(JSON.parseObject(String.valueOf(JSON.parseObject(respone).get("ESB"))).get("DATA"))).get("DATAINFOS"))).get("DATAINFO"));
            List<CityCode> cityCodes = JSON.parseArray(result, CityCode.class);
            return cityCodes.get(0).getCode();
        }catch (Exception e){
            log.error("EmdmService getCityCode error:",e);
        }
        return "";
    }

    /**
     * 获取Header参数
     *  @param
     *  <AUTHOR>
     */
    private Map<String, String> headerParams() {
        Map<String, String> result = Maps.newHashMap();
        SysGlobalConstVo clientHeader = CommonUtils.getSysGlobalConstVo();
        if (null == clientHeader) {
            result.put("X-Emp-No", "System");
            result.put("X-Lang-Id", "zh");
            result.put("usercode", constConfig.getOtherMap().get("MDM_USER_CODE"));
            result.put("password", constConfig.getOtherMap().get("MDM_PASSWORD"));
            return result;
        }
        result.put("X-Emp-No", clientHeader.getxEmpNo());
        result.put("X-Auth-Value", clientHeader.getxAuthValue());
        result.put("X-Lang-Id", clientHeader.getxLangId());
        result.put("X-Tenant-Id", String.valueOf(clientHeader.getxTenantId()));
        result.put("X-Org-Id", String.valueOf(clientHeader.getxOrgId()));
        result.put("X-Origin-ServiceName", clientHeader.getxOriginServiceName());
        result.put("X-Itp-Value", clientHeader.getxItpValue());
        result.put("usercode", constConfig.getOtherMap().get("MDM_USER_CODE"));
        result.put("password", constConfig.getOtherMap().get("MDM_PASSWORD"));
        return result;
    }
}
