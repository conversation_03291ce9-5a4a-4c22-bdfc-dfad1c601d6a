package com.zte.mcrm.channel.service.channel;

import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.util.web.HttpHeaderUtil;
import com.zte.itp.security.AESOperator;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.channel.model.entity.OpportunityDetail;
import com.zte.mcrm.channel.model.entity.OpportunityMailEntity;
import com.zte.mcrm.channel.util.ExceptionMsgUtils;
import com.zte.mcrm.common.business.service.IKeyIdService;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.consts.CommonRetCode;
import com.zte.mcrm.common.exception.BusinessRuntimeException;
import com.zte.mcrm.common.framework.exception.PrmRetCode;
import com.zte.mcrm.common.util.FormDataHelpUtil;
import com.zte.opty.dao.SOptyXDao;
import com.zte.opty.model.bo.SOptyXBO;
import com.zte.opty.sync.util.LcapConverterUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;


/**
 *  服务类
 * <AUTHOR>
 * @date 2021/09/14
 */
@Service
@Slf4j
public class IOpportunityDetailServiceImpl implements IOpportunityDetailService{
    @Autowired
    private IKeyIdService iKeyIdService;

	@Autowired
	private com.zte.mcrm.channel.dao.OpportunityDetailDao opportunityDetailDao ;

	@Value("${encryption.secretKeySixteen}")
	private String secretKeySixteen;

	@Value("${encryption.iv}")
	private String iv;
	@Autowired
	private SOptyXDao sOptyXDao;

	/**
	 * 根据主键获取实体对象
	 * @param rowId 主键ID
	 * @return
	 * <AUTHOR>
     * @date 2021/09/14
     */
	@Override
	public OpportunityDetail get(String rowId) {
		return opportunityDetailDao.get(rowId);
	}

	/**
	 * 软删除指定记录
	 * @param rowId 主键ID
	 * @return 删除记录个数
	 * <AUTHOR>
     * @date 2021/09/14
     */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public int softDelete(String rowId){
		return opportunityDetailDao.softDelete(rowId);
	}

	/**
	 * 删除指定记录
	 * @param rowId 主键ID
	 * @return 删除记录个数
	 * <AUTHOR>
     * @date 2021/09/14
     */
//	@Override
//	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
//	public int delete(String rowId){
//		return opportunityDetailDao.delete(rowId);
//	}

	/**
	 * 新增指定记录
	 * @param entity 实体对象
	 * @return 新增的记录对象,注意是提交数据库之前的实体对象
	 * <AUTHOR>
     * @date 2021/09/14
     */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public OpportunityDetail insert(OpportunityDetail entity, boolean needEncrypt){
		String emp = HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
		if (StringUtils.isNotBlank(HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_TENANT_ID))){
			int tenantId = Integer.parseInt(HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_TENANT_ID));
			entity.setTenantId(tenantId);
		}
		Date now = new Date();
		entity.setCreatedBy(emp);
		entity.setLastUpdBy(emp);
        entity.setCreated(now);
		entity.setLastUpd(now);
        if (null==entity.getRowId()) {
            entity.setRowId(iKeyIdService.getKeyId());
        }


		SOptyXBO sOptyXBO = LcapConverterUtil.buildSoptyx(entity);
		sOptyXDao.insert(sOptyXBO);
//        opportunityDetailDao.insert(entity);
		return entity;
	}

	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public OpportunityDetail insertOrUpdate(OpportunityDetail opportunityDetail, boolean needEncrypt) {
		if(null == opportunityDetail){
			return new OpportunityDetail();
		}
		OpportunityDetail opportunityDetailTemp = opportunityDetailDao.get(opportunityDetail.getRowId());
		String businessManagerId = opportunityDetail.getBusinessManagerId();
		if (null != opportunityDetailTemp) {
			opportunityDetail.setCreated(opportunityDetailTemp.getCreated());
			opportunityDetail.setCreatedBy(opportunityDetailTemp.getCreatedBy());
			opportunityDetail.setEnabledFlag(opportunityDetailTemp.getEnabledFlag());
			if(CommonConst.Y.equals(opportunityDetail.getFromActiveFlag())
					&& StringUtils.isBlank(opportunityDetail.getFromActiveOpty())){
				opportunityDetail.setFromActiveOpty(opportunityDetailTemp.getFromActiveOpty());
			}

			OpportunityDetail opportunityDetailVO = updateAll(opportunityDetail, needEncrypt);
			opportunityDetailVO.setBusinessManagerId(businessManagerId);


			return opportunityDetailVO;
		}
		OpportunityDetail detail = insert(opportunityDetail, needEncrypt);
		detail.setBusinessManagerId(businessManagerId);
		return detail;
	}

	/**
	 * 修改指定记录
	 * @param entity 实体对象
	 * @return 修改的记录对象,注意是提交数据库之前的实体对象
	 * <AUTHOR>
     * @date 2021/09/14
     */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public OpportunityDetail update(OpportunityDetail entity, boolean needEncrypt){
		String emp = HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
		entity.setLastUpdBy(emp);
		entity.setLastUpd(new Date());
		SOptyXBO sOptyXBO = LcapConverterUtil.buildSoptyx(entity);
//		opportunityDetailDao.update(entity);
		sOptyXDao.update(sOptyXBO);
		return entity;
	}

	/**
	 * 修改指定记录
	 * @param entity 实体对象
	 * @return 修改的记录对象,注意是提交数据库之前的实体对象
	 * <AUTHOR>
	 * @date 2021/09/14
	 */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public OpportunityDetail updateWithNoDate(OpportunityDetail entity, boolean needEncrypt){
		opportunityDetailDao.update(entity);
		return entity;
	}

	/**
	 * 根据最终用户状态从数据库中获取对应的数据
	 * @param lastAccStatus 最终用户状态
	 * @return List<OpportunityDetail>
	 * <AUTHOR>
	 * @date 2021/10/13
	 */
	@Override
	public List<OpportunityDetail> getOpportunitiesByLastAccStatus(Integer lastAccStatus) {
		return opportunityDetailDao.getOpportunityDetailByLastAccStatus(lastAccStatus);
	}

	/**
	 * 更新用户状态字段
	 */
	@Override
	public int updateLastAccStatus(String rowId, Integer lastAccStatus) {
		OpportunityDetail entity = new OpportunityDetail();
		entity.setRowId(rowId);
		entity.setLastAccStatus(lastAccStatus);
		entity.setLastUpd(new Date());

		SOptyXBO sOptyXBO = LcapConverterUtil.buildSoptyx(entity);
//		return opportunityDetailDao.update(entity);
		return sOptyXDao.updateById(sOptyXBO);
	}


	/**
	 * 全量修改
	 * @param entity 实体对象
	 * @return 修改的记录对象,注意是提交数据库之前的实体对象
	 * <AUTHOR>
	 * @date 2021/09/14
	 */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public OpportunityDetail updateAll(OpportunityDetail entity, boolean needEncrypt){
		String emp = HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
		entity.setLastUpdBy(emp);
		entity.setLastUpd(new Date());
		if(StringUtils.isEmpty(entity.getEnabledFlag())){
			entity.setEnabledFlag("Y");
		}

		SOptyXBO sOptyXBO = LcapConverterUtil.buildSoptyx(entity);
		sOptyXDao.updateById(sOptyXBO);
//		opportunityDetailDao.updateAll(entity);
		return entity;
	}

	/**
	 * 获取符合条件的实体列表,按指定属性排序
	 * @param map 参数集合
	 * @return 实体集合
	 * <AUTHOR>
     * @date 2021/09/14
     */
	@Override
	public List<OpportunityDetail> getList(Map<String, Object> map){
		return opportunityDetailDao.getList(map);
	}

	/**
	 * 统计
	 * @param map 参数集合
	 * @return 统计总数
	 * <AUTHOR>
     * @date 2021/09/14
     */
	@Override
	public long getCount(Map<String, Object> map){
        return opportunityDetailDao.getCount(map);
	}

	/**
	 * 获取符合条件的记录列表,先按指定属性排序,在分页
	 * @param map 参数集合
	 * @return 实体集合
	 * <AUTHOR>
     * @date 2021/09/14
     */
	@Override
	public List<OpportunityDetail> getPage(Map<String, Object> map){
		return opportunityDetailDao.getPage(map);
	}

	/**
	* 获取符合条件的记录列表,先按指定属性排序,在分页
	* @param form 参数集合
	* @return 实体集合
	* <AUTHOR>
     * @date 2021/09/14
     */
	@Override
	public PageRows<OpportunityDetail> getPageRows(FormData<OpportunityDetail> form){
		Map<String, Object> map = FormDataHelpUtil.getPageQueryMap(form);
		long total = this.getCount(map);
		List<OpportunityDetail> result = this.getPage(map);
		return FormDataHelpUtil.getPageRowsResult(form, total, result);
	}

	/**
	 * 校验是否激活未超过2次
	 *
	 * @param rowId
	 */
	@Override
	public Boolean checkActiveCount(String rowId) {
		OpportunityDetail opportunityDetail = opportunityDetailDao.get(rowId);
		if (Objects.nonNull(opportunityDetail)
				&& (opportunityDetail.getActiveCount() == null || opportunityDetail.getActiveCount() < OpportunityConstant.MAX_ACTIVE_NUM)){
			return true;
		}
		return false;
	}

	@Override
	public OpportunityMailEntity getOpportunityMailEntityByRowId(String rowId){
		return opportunityDetailDao.getOpportunityMailEntityByRowId(rowId);
	}
}
