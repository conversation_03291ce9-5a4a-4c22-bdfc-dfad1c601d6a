package com.zte.mcrm.adapter.model.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/16
 */
@Data
public class AccountDetail {

    /** 账号ID*/
    private String accountId;

    /** 用户ID*/
    private String userId;

    /** 主体ID*/
    private String subjectId;

    /** 账号状态*/
    private String accountStatus;

    /** 用户名*/
    private String userName;

    /** 手机号码	*/
    private String phone;

    /** 邮箱地址*/
    private String email;

    /** 性别 0：女，1：男*/
    private String gender;

    /** 0：外部用户 1：内部用户*/
    private String userType;

    /** 姓名*/
    private String personName;

    /** 证件类型：0：身份证*/
    private String personIdType;

    /** 证件号码*/
    private String personId;

    /** 用户头像*/
    private String avatar;

    /** 用户属性*/
    private Object userAttributes;

    /** 所在国家代码*/
    private String country;

    /** 最近登录时间*/
    private String lastLoginDate;

    /** 账号创建时间*/
    private String gmtCreate;

    /** 账号创建时间*/
    private String gmtModified;

    /** 主体类型*/
    private String subjectType;

    /** 机构名称*/
    private String orgName;

    /** 企业信息列表*/
    private List<EnterpriseInfo> enterpriseList;

    /** */
    private String accountNo;
}
