package com.zte.mcrm.adapter.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 描述：客户信息VO（只包含客户模糊查询接口有值的字段）
 * 创建时间：2021/9/15
 *
 * @author：王丹凤**********
 */
@Data
public class AccountsVO {

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String id;
    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    private String accountNum;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String accountName;
    /**
     * 客户所属国家
     */
    @ApiModelProperty(value = "客户所属国家")
    private String localName;
    /**
     * 客户所属国家id
     */
    @ApiModelProperty(value = "客户所属国家id")
    private String localNum;
    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    private String cityName;
    /**
     * 城市编码
     */
    @ApiModelProperty(value = "城市编码")
    private String cityNo;
    /**
     * 客户受限制主体
     */
    @ApiModelProperty(value = "客户受限制主体")
    private String sanctionedPatry;
    /**
     * 客户受限制主体编码
     */
    @ApiModelProperty(value = "客户受限制主体编码")
    private String sanctionedPatryCode;
    /**
     * 客户类型编码
     */
    @ApiModelProperty(value = "客户类型编码")
    private String accntTypeCd;
    /**
     * 客户类型
     */
    @ApiModelProperty(value = "客户类型")
    private String accntType;
    /**
     * 主管部门名字(全路径)
     */
    @ApiModelProperty(value = "主管部门名字(全路径)")
    private String buName;
    /**
     * 综合信用评级
     */
    @ApiModelProperty(value = "综合信用评级")
    private String totalRating;
    /**
     * 可用信用额度
     */
    @ApiModelProperty(value = "可用信用额度")
    private String available;
    /**
     * mto名称 集团客户简称
     */
    @ApiModelProperty(value = "mto名称(集团客户简称)")
    private String mtoName;
    /**
     * mto属性 集团客户属性
     */
    @ApiModelProperty(value = "mto属性(集团客户属性)")
    private String mtoProperty;
    /**
     * mto名称
     */
    @ApiModelProperty(value = "mto名称")
    private String mtoNameCode;
    /**
     * mto属性编码 集团客户属性编码
     */
    @ApiModelProperty(value = "mto属性编码 集团客户属性编码")
    private String mtoPropertyCode;
    /**
     * 运营商类型
     */
    @ApiModelProperty(value = "运营商类型")
    private String operatorType;
    /**
     * 运营商类型编码
     */
    @ApiModelProperty(value = "运营商类型编码")
    private String operatorTypeCode;
    /**
     * 客户级别 运营商级别
     */
    @ApiModelProperty(value = "客户级别 运营商级别")
    private String accountLevel;
    /**
     * 客户级别编码 运营商级别编码
     */
    @ApiModelProperty(value = "客户级别编码 运营商级别编码")
    private String accountLevelCode;
    /**
     * 是否高端客户
     */
    @ApiModelProperty(value = "是否高端客户")
    private String highFlag;
    /**
     * 客户eccId
     */
    @ApiModelProperty(value = "客户eccId")
    private String acctEccId;
    /**
     * 客户范围
     */
    @ApiModelProperty(value = "客户范围")
    private String custRange;
    /**
     * 客户范围编码
     */
    @ApiModelProperty(value = "客户范围编码")
    private String custRangeCode;
    /**
     * 客户状态
     */
    @ApiModelProperty(value = "客户状态")
    private String custState;
    /**
     * 客户状态编码
     */
    @ApiModelProperty(value = "客户状态编码")
    private String custStateCode;
    /**
     * 客户简写
     */
    @ApiModelProperty(value = "客户简写")
    private String custSimple;
    /**
     * 是否子公司
     */
    @ApiModelProperty(value = "是否子公司")
    private String subcompany;
    /**
     * 是否子公司编码
     */
    @ApiModelProperty(value = "是否子公司编码")
    private String subcompanyCode;
    /**
     * 财务类型编码
     */
    @ApiModelProperty(value = "财务类型编码")
    private String financeTypeCode;
    /**
     * 财务类型
     */
    @ApiModelProperty(value = "财务类型")
    private String financeType;
    /**
     * 信息名称
     */
    @ApiModelProperty(value = "信息名称")
    private String infoName;
    /**
     * 国家缩写
     */
    @ApiModelProperty(value = "国家缩写")
    private String countryForShort;

    @ApiModelProperty("是否被合并标识，M:主客户，H:被合并客户")
    private String acctMergeFlag;
    @ApiModelProperty("被合并至客户的编码")
    private String mainAcctNum;
    @ApiModelProperty("被合并至客户名称")
    private String mainAcctName;
    @ApiModelProperty("是否活跃标识，F:沉默")
    private String frozenFlag;
}
