package com.zte.mcrm.channel.service.channel;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 审批信息服务类
 */
public interface ApprovalInfoService {

    /**
     * 查询审批中心获取审批通过时间
     * @param flowInstances
     * @return
     */
    Map<String, Date> getApproveDateMap(List<String> flowInstances, String type);

    /**
     * 查询审批中心获取审批人列表
     * @param flowInstances
     * @return
     */
    Map<String, List<String>> getApproverListMap(List<String> flowInstances);

    /**
     *刷新moa
     */
    Boolean refreshMoaParams(String flowInstanceId, HashMap<String,Object> parameterMap);
}
