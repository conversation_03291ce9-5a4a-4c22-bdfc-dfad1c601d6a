package com.zte.aiagent.ui.dto.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 招标文件分页查询DTO
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class BidDocumentPageQueryDTO {

    /**
     * 招标文件名称关键字
     */
    private String fileName;

    /**
     * 解析状态:PENDING/PARSING/SUCCESS/FAILED
     */
    private String parseStatus;

    /**
     * 创建开始时间:yyyy-MM-dd
     */
    private String startDate;

    /**
     * 创建结束时间:yyyy-MM-dd
     */
    private String endDate;

    /**
     * 模糊匹配输入(可匹配文件名称、客户名称、项目名称)
     */
    private String fuzzyInput;

    /**
     * 创建人
     */
    private String createdBy;
}
