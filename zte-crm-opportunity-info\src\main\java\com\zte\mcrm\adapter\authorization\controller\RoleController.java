package com.zte.mcrm.adapter.authorization.controller;

import com.zte.mcrm.adapter.authorization.service.ChannelAuthService;
import com.zte.mcrm.adapter.authorization.service.RoleService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/role")
public class RoleController {

    @Autowired
    private RoleService roleService;

    @Autowired
    private ChannelAuthService channelAuthService;

    @ApiOperation("查询所有角色列表，测试方法待删除")
    @GetMapping("/getChannelRoleList")
    public com.zte.itp.authorityclient.entity.output.ServiceData getChannelRoleList() {
        return channelAuthService.getRoleList();
    }

}
