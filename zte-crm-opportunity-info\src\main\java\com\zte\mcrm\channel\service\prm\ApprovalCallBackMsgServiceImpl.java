package com.zte.mcrm.channel.service.prm;

import com.alibaba.fastjson.JSON;
import com.zte.mcrm.adapter.clouddisk.util.JsonUtils;
import com.zte.mcrm.channel.constant.ArbitrationTypeEnum;
import com.zte.mcrm.channel.constant.OpportunityApprovalNodeEnum;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.channel.dao.OpportunityDetailDao;
import com.zte.mcrm.channel.model.dto.ApprovalCallBackMsgInfo;
import com.zte.mcrm.channel.model.entity.Opportunity;
import com.zte.mcrm.channel.model.vo.OpportunityOpinionVO;
import com.zte.mcrm.channel.service.channel.IOpportunityService;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.opty.common.enums.OptyStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ApprovalCallBackMsgServiceImpl implements IApprovalCallBackMsgService{

    @Autowired
    private OpportunityDetailDao opportunityDetailDao;

    @Autowired
    private IOpportunityService opportunityService;


    /**
     * 流程状态变更消息
     * @param statusChangeCallbackMsg
     */
    @Override
    public boolean approvalStatusChangeCallbackMsg(String statusChangeCallbackMsg) {
        log.info("start consummer kafka :key ={}, content={}", OpportunityConstant.APPROVAL_CENTER_KEY, statusChangeCallbackMsg);

        ApprovalCallBackMsgInfo approvalCallBackMsgInfo = JSON.parseObject(statusChangeCallbackMsg, ApprovalCallBackMsgInfo.class);
        if (!OpportunityConstant.FLOWCODE_LIST.contains(approvalCallBackMsgInfo.getFlowCode())) {
            return true;
        }
        if (StringUtils.equals(approvalCallBackMsgInfo.getNodeName(), OpportunityApprovalNodeEnum.APPROVAL_NODE.getNodeName())
                || StringUtils.equals(approvalCallBackMsgInfo.getNodeCode(), OpportunityApprovalNodeEnum.APPROVAL_NODE.getNodeCode())){
            processApprovalNodeApprovalResults(approvalCallBackMsgInfo);
        }
        return true;
    }

    private void processApprovalNodeApprovalResults(ApprovalCallBackMsgInfo approvalCallBackMsgInfo){
        if (JsonUtils.isJSON(approvalCallBackMsgInfo.getExtOpinion())){
            OpportunityOpinionVO approvalOpinion = JSON.parseObject(approvalCallBackMsgInfo.getExtOpinion(), OpportunityOpinionVO.class);
            log.info("The approvalOpinion is: {}", JSON.toJSONString(approvalOpinion));

            if (StringUtils.equals(approvalCallBackMsgInfo.getResult(), CommonConst.N)
                    && !approvalOpinion.getFailureReason().equals(ArbitrationTypeEnum.FAILURE_REASON_RESTRICTED_PARTY.getCode())){
                Opportunity opportunity = opportunityService.get(approvalCallBackMsgInfo.getBusinessId());
                if (!OptyStatusEnum.APPROVING.getCode().equals(opportunity.getStatusCd())) {
                    log.info("商机状态已经不是报备审批中了,rowId:{}", approvalCallBackMsgInfo.getBusinessId());
                    return;
                }
                log.info("更新状态为仲裁中,rowId:{}", approvalCallBackMsgInfo.getBusinessId());
                opportunityService.updateStatus(approvalCallBackMsgInfo.getBusinessId(), OptyStatusEnum.OPTY_SUSPEND);
            }

        }else {
            log.info("ExtOpinion format is not json: {}", approvalCallBackMsgInfo.getExtOpinion());
        }
    }

}
