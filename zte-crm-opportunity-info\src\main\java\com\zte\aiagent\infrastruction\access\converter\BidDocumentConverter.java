package com.zte.aiagent.infrastruction.access.converter;

import com.zte.aiagent.infrastruction.access.po.BidDocumentPO;
import com.zte.aiagent.ui.dto.vo.BidDocumentVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 招标文件PO与VO转换器
 * 使用MapStruct进行字段一一映射转换
 *
 * <AUTHOR>
 */
@Mapper
public interface BidDocumentConverter {

    BidDocumentConverter INSTANCE = Mappers.getMapper(BidDocumentConverter.class);

    /**
     * PO转VO
     *
     * @param po BidDocumentPO
     * @return BidDocumentVO
     */
    @Mappings({
            @Mapping(source = "rowId", target = "rowId"),
            @Mapping(source = "fileName", target = "fileName"),
            @Mapping(source = "fileSize", target = "fileSize"),
            @Mapping(source = "fileType", target = "fileType"),
            @Mapping(source = "fileKey", target = "fileKey"),
            @Mapping(source = "parseTemplateCode", target = "parseTemplateCode"),
            @Mapping(source = "parseTemplateId", target = "parseTemplateId"),
            @Mapping(source = "parseStatus", target = "parseStatus"),
            @Mapping(source = "parseStartTime", target = "parseStartTime"),
            @Mapping(source = "parseEndTime", target = "parseEndTime"),
            @Mapping(source = "createdBy", target = "createdBy"),
            @Mapping(source = "createdDate", target = "createdDate"),
            @Mapping(source = "lastUpdatedBy", target = "lastUpdatedBy"),
            @Mapping(source = "lastUpdatedDate", target = "lastUpdatedDate"),
            @Mapping(source = "enabledFlag", target = "enabledFlag"),
            @Mapping(source = "tenantId", target = "tenantId"),
            @Mapping(source = "exportParsedExcelFileKey", target = "exportParsedExcelFileKey"),
            @Mapping(source = "exportParsedWordFileKey", target = "exportParsedWordFileKey"),
            @Mapping(source = "yellowDocumentFileKey", target = "yellowDocumentFileKey"),
            @Mapping(source = "customerCode", target = "customerCode"),
            @Mapping(source = "customerName", target = "customerName"),
            @Mapping(source = "projectCode", target = "projectCode"),
            @Mapping(source = "projectName", target = "projectName"),
            @Mapping(target = "parseProgress", ignore = true),
            @Mapping(target = "bidParseRecordList", ignore = true)
    })
    BidDocumentVO toVO(BidDocumentPO po);

    /**
     * PO列表转VO列表
     *
     * @param poList BidDocumentPO列表
     * @return BidDocumentVO列表
     */
    List<BidDocumentVO> toVOList(List<BidDocumentPO> poList);

    /**
     * VO转PO
     *
     * @param vo BidDocumentVO
     * @return BidDocumentPO
     */
    @Mappings({
            @Mapping(source = "rowId", target = "rowId"),
            @Mapping(source = "fileName", target = "fileName"),
            @Mapping(source = "fileSize", target = "fileSize"),
            @Mapping(source = "fileType", target = "fileType"),
            @Mapping(source = "fileKey", target = "fileKey"),
            @Mapping(source = "parseTemplateCode", target = "parseTemplateCode"),
            @Mapping(source = "parseTemplateId", target = "parseTemplateId"),
            @Mapping(source = "parseStatus", target = "parseStatus"),
            @Mapping(source = "parseStartTime", target = "parseStartTime"),
            @Mapping(source = "parseEndTime", target = "parseEndTime"),
            @Mapping(source = "createdBy", target = "createdBy"),
            @Mapping(source = "createdDate", target = "createdDate"),
            @Mapping(source = "lastUpdatedBy", target = "lastUpdatedBy"),
            @Mapping(source = "lastUpdatedDate", target = "lastUpdatedDate"),
            @Mapping(source = "enabledFlag", target = "enabledFlag"),
            @Mapping(source = "tenantId", target = "tenantId"),
            @Mapping(source = "exportParsedExcelFileKey", target = "exportParsedExcelFileKey"),
            @Mapping(source = "exportParsedWordFileKey", target = "exportParsedWordFileKey"),
            @Mapping(source = "yellowDocumentFileKey", target = "yellowDocumentFileKey"),
            @Mapping(source = "customerCode", target = "customerCode"),
            @Mapping(source = "customerName", target = "customerName"),
            @Mapping(source = "projectCode", target = "projectCode"),
            @Mapping(source = "projectName", target = "projectName")
    })
    BidDocumentPO toPO(BidDocumentVO vo);

    /**
     * VO列表转PO列表
     *
     * @param voList BidDocumentVO列表
     * @return BidDocumentPO列表
     */
    List<BidDocumentPO> toPOList(List<BidDocumentVO> voList);
}
