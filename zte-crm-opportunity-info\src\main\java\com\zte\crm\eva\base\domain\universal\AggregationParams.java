package com.zte.crm.eva.base.domain.universal;


import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class AggregationParams {

    /**
     * 字段集
     */
    List<String> fields;

    /**
     * 表名
     */
    String tableName;

    /**
     * where条件
     */
    Map<String, Object> whereCondition;

    /**
     * where条件函数
     */
    Map<String, Object> whereConditionFunction;

    /**
     * 分组
     */
    List<String> groupByColumns;

    /**
     * having 条件
     */
    String havingConditions;

    /**
     * order字段
     */
    List<String> orderColumns;

    /**
     * limit
     */
    String limitParams;

}
