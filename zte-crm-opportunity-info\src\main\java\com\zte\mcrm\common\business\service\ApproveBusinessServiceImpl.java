package com.zte.mcrm.common.business.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.mcrm.clues.common.CluesSysConst;
import com.zte.mcrm.common.access.vo.ApproveHeadVO;
import com.zte.mcrm.common.business.IApproveBusinessService;
import com.zte.mcrm.common.util.RequestMessage;
import com.zte.springbootframe.common.model.HttpResultData;
import com.zte.springbootframe.common.serviceregister.MicroServiceWithConfigUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/****
 * 审批
 * @ClassName ApproveBusinessServiceImpl
 * <AUTHOR>
 * @Date 2021/3/523
 * @Version V1.0
 **/
@Slf4j
@Service
public class ApproveBusinessServiceImpl implements IApproveBusinessService {

    @Override
    public List<ApproveHeadVO> getApproveUserNoByObjectIdList(List<String> objectIdList) {
        List<ApproveHeadVO> approveHeadList = new ArrayList<>();
        try {
            Map<String, String> headerParamsMap = RequestMessage.getHeader("pcOpportunity");
            HttpResultData httpResultLovs = MicroServiceWithConfigUtil.invokeServiceWithPostMethod("4",
                    CluesSysConst.GET_APPROVE_USERNO_BY_OBJECTID_LIST, objectIdList, headerParamsMap);
            if (null != httpResultLovs && null != httpResultLovs.getBo()) {
                String resultBo = JacksonJsonConverUtil.beanToJson(httpResultLovs.getBo());
                approveHeadList = JacksonJsonConverUtil.jsonToListBean(resultBo, new TypeReference<List<ApproveHeadVO>>() {});
            }
        } catch (Exception e) {
            log.error("ApproveBusinessServiceImpl.getApproveUserNoByObjectIdList --> message:{} exception:{} ", e.getMessage(), e);
        }
        return approveHeadList;
    }
}
