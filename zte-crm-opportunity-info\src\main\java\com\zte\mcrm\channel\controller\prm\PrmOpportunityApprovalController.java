package com.zte.mcrm.channel.controller.prm;

import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import com.zte.mcrm.adapter.approval.model.dto.ApprovalCallBackDTO;
import com.zte.mcrm.adapter.approval.model.dto.ReassignDTO;
import com.zte.mcrm.adapter.authorization.model.RoleInfo;
import com.zte.mcrm.channel.model.dto.ApprovalCallBackMsgInfo;
import com.zte.mcrm.channel.model.dto.AuthorizationRoleInfoDTO;
import com.zte.mcrm.channel.model.dto.OpportunityOpinionDTO;
import com.zte.mcrm.channel.model.dto.SimilarOpportunityQueryDTO;
import com.zte.mcrm.channel.model.entity.*;
import com.zte.mcrm.channel.model.vo.CreateCustomerParamVO;
import com.zte.mcrm.channel.model.vo.PrmLastUserStatusVO;
import com.zte.mcrm.channel.model.vo.RestrictedPartyVO;
import com.zte.mcrm.channel.service.channel.IOpportunityDetailService;
import com.zte.mcrm.channel.service.channel.IOpportunityInfoService;
import com.zte.mcrm.channel.service.prm.IPrmOpportunityApprovalService;
import com.zte.springbootframe.common.exception.BusiException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * prm商机审批页面相关接口
 * <AUTHOR>
 * @date 2021/9/30
 */
@Api(tags ="PRM侧商机审批API")
@RestController
@RequestMapping("/prm")
public class PrmOpportunityApprovalController {

    @Autowired
    IPrmOpportunityApprovalService prmOpportunityApprovalService;
    @Autowired
    IOpportunityDetailService opportunityDetailService;
    @Autowired
    IOpportunityInfoService opportunityInfoService;

    @ApiOperation("查询类似商机 -Post方式")
    @PostMapping(value="/query/similaropportunity")
    public ServiceData<PageRows<SimilarOpportunity>> getSimilarOpportunity(@RequestBody FormData<SimilarOpportunityQueryDTO> form)  throws Exception{
        PageRows<SimilarOpportunity> similarOpportunity = prmOpportunityApprovalService.getSimilarOpportunity(form);
        return ServiceResultUtil.success(similarOpportunity);
    }

    @ApiOperation("依据角色、组织、子行业从权限平台找人")
    @PostMapping("/queryRoleInfoWithConstraint")
    public ServiceData<List<RoleInfo>> queryRoleInfoWithConstraint(@RequestBody AuthorizationRoleInfoDTO authorizationRoleInfoDTO) throws Exception {
        ServiceData<List<RoleInfo>> result = new ServiceData<>();
        result.setBo(prmOpportunityApprovalService.queryRoleInfoWithConstraint(authorizationRoleInfoDTO));
        return result;
    }

    @ApiOperation("修改中兴业务经理")
    @PostMapping("/modifyBusinessManager")
    public ServiceData<Integer> modifyBusinessManager(@RequestBody BusinessManagerBO businessManager) {
        ServiceData<Integer> result = new ServiceData<>();
        result.setBo(prmOpportunityApprovalService.modifyBusinessManager(businessManager));
        return result;
    }


    @ApiOperation("审批节点转交")
    @PostMapping("/reassign/approval")
    public ServiceData<Boolean> reassignApproval(@RequestBody ReassignDTO reassignDTO) {
        boolean approval = prmOpportunityApprovalService.reassignApproval(reassignDTO);
        return ServiceResultUtil.success(approval);
    }

    @ApiOperation("受限制主体校验")
    @GetMapping("/checkRestrictedParty")
    public ServiceData<RestrictedPartyVO> checkRestrictedParty(String lastAccId, String crmCustomerCode) throws Exception {
        RestrictedPartyVO result = prmOpportunityApprovalService.getRestrictedParty(lastAccId, crmCustomerCode);
        return ServiceResultUtil.success(result);
    }

    @ApiOperation("受限制主体校验--支持根据租户和法人")
    @GetMapping("/checkRestrictedParty/v2")
    public ServiceData<RestrictedPartyVO> checkRestrictedPartyV2(String lastAccId, String crmCustomerCode,String organizationId) throws Exception {
        RestrictedPartyVO result = prmOpportunityApprovalService.getRestrictedPartyV2(lastAccId, crmCustomerCode,organizationId);
        return ServiceResultUtil.success(result);
    }

    @ApiOperation("节点审批")
    @PostMapping("/approve")
    public ServiceData<String> approve(@RequestBody OpportunityOpinionDTO opinion) throws Exception {
        String approval = prmOpportunityApprovalService.approve(opinion);

        return ServiceResultUtil.success(approval);
    }

    @ApiOperation("节点审批校验")
    @PostMapping("/approve/verification/callback")
    public ServiceData<ApprovalVerificationResult> approveVerificationCallBack(@RequestBody ApprovalVerificationCallback approvalVerificationCallback) throws Exception {
        ApprovalVerificationResult approvalVerificationResult = prmOpportunityApprovalService.approveVerificationCallBack(approvalVerificationCallback);

        return ServiceResultUtil.success(approvalVerificationResult);
    }
    @ApiOperation("商机仲裁审批")
    @PostMapping("/arbitrationApprove")
    public ServiceData<String> arbitrationApprove(@RequestBody OpportunityOpinionDTO opinion) throws Exception{
        String arbitrationApprove = prmOpportunityApprovalService.arbitrationApprove(opinion);
        return ServiceResultUtil.success(arbitrationApprove);
    }

    @ApiOperation("商机仲裁节点获取审批人")
    @PostMapping("/getArbitrationApprover")
    public ServiceData<String> getArbitrationApprover(@RequestBody ApprovalCallBackMsgInfo callBackMsgInfo){

        return ServiceResultUtil.success(prmOpportunityApprovalService.getArbitrationApprover(callBackMsgInfo));
    }


    @ApiOperation("修改流程参数")
    @PostMapping("/modifyProcessVariables")
    public ServiceData<String> modifyProcessVariables( @RequestParam @ApiParam(value = "流程id", required = true) String flowInstanceId,
                                                       @RequestBody @ApiParam(value="需要修改的参数实例",required=true) ApprovalStartParamsBO approvalStartParamsBO) throws Exception {
        String result = prmOpportunityApprovalService.modifyApprovalCenterProcessVariables(flowInstanceId,approvalStartParamsBO, null);

        return ServiceResultUtil.success(result);
    }

    @ApiOperation("根据商机的进度")
    @GetMapping("/query/opptyprogress/{rowId}")
    public ServiceData< List<OpptyStatus>> queryOpptyProgress(@PathVariable(name="rowId") String  rowId) throws Exception {
        return ServiceResultUtil.success(prmOpportunityApprovalService.queryOpptyProgress(rowId));
    }

    @ApiOperation("根据商机的审批任务")
    @GetMapping("/query/approvaltasks/{rowId}")
    public ServiceData< List<ApprovalNode>> queryApprovalNodes(@PathVariable(name="rowId") String  rowId) throws Exception {
        return ServiceResultUtil.success(prmOpportunityApprovalService.queryApprovalNodes(rowId));
    }


    @ApiOperation("根据商机的审批任务")
    @GetMapping("/query/approval/{rowId}")
    public ServiceData< List<ApprovalDetail>> queryApproval(@PathVariable(name="rowId") String  rowId) throws Exception {
        return ServiceResultUtil.success(prmOpportunityApprovalService.queryApprovalDetail(rowId));
    }

    @ApiOperation("最终用户状态查询")
    @GetMapping("/lastUserStatus/query/{rowId}")
    public ServiceData<PrmLastUserStatusVO> queryLastUserStatus(@PathVariable("rowId") String rowId) throws BusiException {
        return ServiceResultUtil.success(prmOpportunityApprovalService.queryLastUserStatus(rowId));
    }


    @ApiOperation("刷新")
    @GetMapping("/refresh")
    public ServiceData<String> refresh(String rowId) throws Exception {
        ServiceData<String> result = new ServiceData<>();
        OpportunityDetail opportunityDetail = opportunityDetailService.get(rowId);
        if(Objects.nonNull(opportunityDetail)) {
            result.setBo(opportunityInfoService.refresh(opportunityDetail));
        }
        return result;
    }

    @ApiOperation("创建客户草稿 -POST方式")
    @PostMapping(value="/createCustomer")
    public ServiceData<String> createCustomer(@RequestBody CreateCustomerParamVO param)throws Exception {

        opportunityInfoService.doCreateCustomerDraft(param);
        return ServiceResultUtil.success("success");
    }


    @ApiOperation("审批流程回调接口")
    @PostMapping(value = "/flow/callback", produces = "application/json; charset=utf-8")
    public ServiceData<String> getCallbackInfo(@RequestBody ApprovalCallBackDTO callBackDTO) {
        ServiceData<String> ret = new ServiceData<>();
        ret.setBo(opportunityInfoService.handleApprovalCallBack(callBackDTO));
        return ret;
    }
}
