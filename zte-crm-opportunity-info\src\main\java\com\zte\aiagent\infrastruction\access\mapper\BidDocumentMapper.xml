<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.aiagent.infrastruction.access.mapper.BidDocumentMapper">
    <resultMap id="BaseResultMap" type="com.zte.aiagent.infrastruction.access.po.BidDocumentPO">
        <id column="row_id" property="rowId" jdbcType="VARCHAR"/>
        <result column="file_name" property="fileName" jdbcType="VARCHAR"/>
        <result column="file_size" property="fileSize" jdbcType="BIGINT"/>
        <result column="file_type" property="fileType" jdbcType="VARCHAR"/>
        <result column="file_key" property="fileKey" jdbcType="VARCHAR"/>
        <result column="parse_template_code" property="parseTemplateCode" jdbcType="VARCHAR"/>
        <result column="parse_template_id" property="parseTemplateId" jdbcType="VARCHAR"/>
        <result column="parse_status" property="parseStatus" jdbcType="VARCHAR"/>
        <result column="parse_start_time" property="parseStartTime" jdbcType="TIMESTAMP"/>
        <result column="parse_end_time" property="parseEndTime" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_date" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR"/>
        <result column="last_updated_date" property="lastUpdatedDate" jdbcType="TIMESTAMP"/>
        <result column="enabled_flag" property="enabledFlag" jdbcType="VARCHAR"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
        <result column="export_parsed_excel_file_key" property="exportParsedExcelFileKey" jdbcType="VARCHAR"/>
        <result column="export_parsed_word_file_key" property="exportParsedWordFileKey" jdbcType="VARCHAR"/>
        <result column="yellow_document_file_key" property="yellowDocumentFileKey" jdbcType="VARCHAR"/>
        <result column="customer_code" property="customerCode" jdbcType="VARCHAR"/>
        <result column="customer_name" property="customerName" jdbcType="VARCHAR"/>
        <result column="project_code" property="projectCode" jdbcType="VARCHAR"/>
        <result column="project_name" property="projectName" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="insert" parameterType="com.zte.aiagent.infrastruction.access.po.BidDocumentPO">
        INSERT INTO bid_document (
            row_id,
            file_name,
            file_size,
            file_type,
            file_key,
            parse_template_code,
            parse_template_id,
            parse_status,
            parse_start_time,
            parse_end_time,
            created_by,
            created_date,
            last_updated_by,
            last_updated_date,
            enabled_flag,
            tenant_id,
            export_parsed_excel_file_key,
            export_parsed_word_file_key,
            yellow_document_file_key,
            customer_code,
            customer_name,
            project_code,
            project_name
        ) VALUES (
            #{rowId,jdbcType=VARCHAR},
            #{fileName,jdbcType=VARCHAR},
            #{fileSize,jdbcType=BIGINT},
            #{fileType,jdbcType=VARCHAR},
            #{fileKey,jdbcType=VARCHAR},
            #{parseTemplateCode,jdbcType=VARCHAR},
            #{parseTemplateId,jdbcType=VARCHAR},
            #{parseStatus,jdbcType=VARCHAR},
            #{parseStartTime,jdbcType=TIMESTAMP},
            #{parseEndTime,jdbcType=TIMESTAMP},
            #{createdBy,jdbcType=VARCHAR},
            #{createdDate,jdbcType=TIMESTAMP},
            #{lastUpdatedBy,jdbcType=VARCHAR},
            #{lastUpdatedDate,jdbcType=TIMESTAMP},
            #{enabledFlag,jdbcType=VARCHAR},
            #{tenantId,jdbcType=BIGINT},
            #{exportParsedExcelFileKey,jdbcType=VARCHAR},
            #{exportParsedWordFileKey,jdbcType=VARCHAR},
            #{yellowDocumentFileKey,jdbcType=VARCHAR},
            #{customerCode,jdbcType=VARCHAR},
            #{customerName,jdbcType=VARCHAR},
            #{projectCode,jdbcType=VARCHAR},
            #{projectName,jdbcType=VARCHAR}
        )
    </insert>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
            row_id,
            file_name,
            file_size,
            file_type,
            file_key,
            parse_template_code,
            parse_template_id,
            parse_status,
            parse_start_time,
            parse_end_time,
            created_by,
            created_date,
            last_updated_by,
            last_updated_date,
            enabled_flag,
            tenant_id,
            export_parsed_excel_file_key,
            export_parsed_word_file_key,
            yellow_document_file_key,
            customer_code,
            customer_name,
            project_code,
            project_name
        FROM bid_document
        WHERE row_id = #{rowId,jdbcType=VARCHAR}
    </select>

    <update id="updateByPrimaryKey" parameterType="com.zte.aiagent.infrastruction.access.po.BidDocumentPO">
        UPDATE bid_document
        SET
            file_name = #{fileName,jdbcType=VARCHAR},
            file_size = #{fileSize,jdbcType=BIGINT},
            file_type = #{fileType,jdbcType=VARCHAR},
            file_key = #{fileKey,jdbcType=VARCHAR},
            parse_template_code = #{parseTemplateCode,jdbcType=VARCHAR},
            parse_template_id = #{parseTemplateId,jdbcType=VARCHAR},
            parse_status = #{parseStatus,jdbcType=VARCHAR},
            parse_start_time = #{parseStartTime,jdbcType=TIMESTAMP},
            parse_end_time = #{parseEndTime,jdbcType=TIMESTAMP},
            created_by = #{createdBy,jdbcType=VARCHAR},
            created_date = #{createdDate,jdbcType=TIMESTAMP},
            last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            last_updated_date = #{lastUpdatedDate,jdbcType=TIMESTAMP},
            enabled_flag = #{enabledFlag,jdbcType=VARCHAR},
            tenant_id = #{tenantId,jdbcType=BIGINT},
            export_parsed_excel_file_key = #{exportParsedExcelFileKey,jdbcType=VARCHAR},
            export_parsed_word_file_key = #{exportParsedWordFileKey,jdbcType=VARCHAR},
            yellow_document_file_key = #{yellowDocumentFileKey,jdbcType=VARCHAR},
            customer_code = #{customerCode,jdbcType=VARCHAR},
            customer_name = #{customerName,jdbcType=VARCHAR},
            project_code = #{projectCode,jdbcType=VARCHAR},
            project_name = #{projectName,jdbcType=VARCHAR}
        WHERE row_id = #{rowId,jdbcType=VARCHAR}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        DELETE FROM bid_document
        WHERE row_id = #{rowId,jdbcType=VARCHAR}
    </delete>

    <!-- 分页查询招标文件列表 -->
    <select id="selectPageList" resultMap="BaseResultMap">
        SELECT
            row_id,
            file_name,
            file_size,
            file_type,
            file_key,
            parse_template_code,
            parse_template_id,
            parse_status,
            parse_start_time,
            parse_end_time,
            created_by,
            created_date,
            last_updated_by,
            last_updated_date,
            enabled_flag,
            tenant_id,
            export_parsed_excel_file_key,
            export_parsed_word_file_key,
            yellow_document_file_key,
            customer_code,
            customer_name,
            project_code,
            project_name
        FROM bid_document
        WHERE enabled_flag = 'Y'
        <if test="bo != null">
            <if test="bo.fileName != null and bo.fileName != ''">
                AND file_name LIKE CONCAT('%', #{bo.fileName}, '%')
            </if>
            <if test="bo.parseStatus != null and bo.parseStatus != ''">
                AND parse_status = #{bo.parseStatus}
            </if>
            <if test="bo.createdBy != null and bo.createdBy != ''">
                AND created_by = #{bo.createdBy}
            </if>
            <if test="bo.startDate != null and bo.startDate != ''">
                AND created_date >= #{bo.startDate}
            </if>
            <if test="bo.endDate != null and bo.endDate != ''">
                AND created_date &lt;= #{bo.endDate}
            </if>
            <if test="bo.fuzzyInput != null and bo.fuzzyInput != ''">
                AND (file_name LIKE CONCAT('%', #{bo.fuzzyInput}, '%')
                OR customer_name LIKE CONCAT('%', #{bo.fuzzyInput}, '%')
                OR project_name LIKE CONCAT('%', #{bo.fuzzyInput}, '%'))
            </if>
        </if>
        <choose>
            <when test="sort != null and sort != ''">
                ORDER BY
                <choose>
                    <when test="sort == 'fileName'">file_name</when>
                    <when test="sort == 'fileSize'">file_size</when>
                    <when test="sort == 'parseStatus'">parse_status</when>
                    <when test="sort == 'createdDate'">created_date</when>
                    <when test="sort == 'parseStartTime'">parse_start_time</when>
                    <when test="sort == 'parseEndTime'">parse_end_time</when>
                    <otherwise>created_date</otherwise>
                </choose>
                <if test="order != null and order != ''">
                    ${order}
                </if>
                <if test="order == null or order == ''">
                    DESC
                </if>
            </when>
            <otherwise>
                ORDER BY created_date DESC
            </otherwise>
        </choose>
        LIMIT ${(page - 1) * rows}, #{rows}
    </select>

    <!-- 查询总记录数 -->
    <select id="selectPageCount" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM bid_document
        WHERE enabled_flag = 'Y'
        <if test="bo != null">
            <if test="bo.fileName != null and bo.fileName != ''">
                AND file_name LIKE CONCAT('%', #{bo.fileName}, '%')
            </if>
            <if test="bo.parseStatus != null and bo.parseStatus != ''">
                AND parse_status = #{bo.parseStatus}
            </if>
            <if test="bo.createdBy != null and bo.createdBy != ''">
                AND created_by = #{bo.createdBy}
            </if>
            <if test="bo.startDate != null and bo.startDate != ''">
                AND created_date >= #{bo.startDate}
            </if>
            <if test="bo.endDate != null and bo.endDate != ''">
                AND created_date &lt;= #{bo.endDate}
            </if>
            <if test="bo.fuzzyInput != null and bo.fuzzyInput != ''">
                AND (file_name LIKE CONCAT('%', #{bo.fuzzyInput}, '%')
                OR customer_name LIKE CONCAT('%', #{bo.fuzzyInput}, '%')
                OR project_name LIKE CONCAT('%', #{bo.fuzzyInput}, '%'))
            </if>
        </if>
    </select>
</mapper>
