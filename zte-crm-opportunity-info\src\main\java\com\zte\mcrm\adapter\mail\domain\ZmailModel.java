package com.zte.mcrm.adapter.mail.domain;

import com.google.common.collect.Lists;
import com.zte.mcrm.adapter.mail.dto.MailDTO;
import com.zte.mcrm.common.consts.CommonConstant;
import com.zte.mcrm.mail.access.vo.OptyMailModelVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

import static com.zte.mcrm.adapter.util.PatternUtils.encapsulationTextByCDATA;
import static com.zte.mcrm.common.consts.CommonConstant.*;


public class ZmailModel {

    public static final String OPPORTUNITY = "Opportunity Center";

    /** 系统名称 **/
    private String systemType;
    /** true: 多个收件人时只能看到自己 **/
    private Boolean mailtoIsOne;
    /** 0:不重要 1:重要 **/
    private Integer importance;
    /** 优先级 **/
    private MailPriority priority;
    /** 能否转发，1不能转发 0可以转发 **/
    private Integer forward;
    /** 邮件标题 **/
    private String mailSubject;
    /** 发件人 **/
    private String mailFrom;
    /** 收件人 **/
    private String mailTo;
    /** 抄送人 **/
    private String mailcc;
    /** 密送人 **/
    private String mailbcc;
    /** 邮件正文 **/
    private List<MailBody> mailBodies;

    public ZmailModel(String systemType, String mailSubject, String mailFrom, String mailTo, List<MailBody> mailBody) {
        this.systemType = systemType;
        this.mailtoIsOne = false;
        this.importance = 0;
        this.priority = MailPriority.NORMAL;
        this.forward = 0;
        this.mailSubject = mailSubject;
        this.mailFrom = mailFrom;
        this.mailTo = mailTo;
        this.mailcc = "";
        this.mailbcc = "";
        this.mailBodies = mailBody;
    }

    public  ZmailModel(MailDTO mailDTO) {
        this(OPPORTUNITY, mailDTO.getMailSubject(), mailDTO.getMailFrom(), mailDTO.getMailTo(), null);
        List<MailBody> mailBodies = Lists.newArrayList();
        if (StringUtils.isNotBlank(mailDTO.getMainTextCn())) {
            MailBody mailBodyCn = new MailBody(ZH_CN, mailDTO.getWarmCallCn(), OPPORTUNITY, mailDTO.getClickLookCn(), mailDTO.getLinkAddCn(), mailDTO.getMainTextCn());
            mailBodies.add(mailBodyCn);
        }
        if (StringUtils.isNotBlank(mailDTO.getMainTextEn())) {
            MailBody mailBodyEn = new MailBody(EN_US,  mailDTO.getWarmCallEn(), OPPORTUNITY, mailDTO.getClickLookEn(), mailDTO.getLinkAddEn(), mailDTO.getMainTextEn());
            mailBodies.add(mailBodyEn);
        }
        this.mailBodies = mailBodies;

    }

    public ZmailModel(OptyMailModelVO optyMailModelVO){
        this.systemType = optyMailModelVO.getSystemNameEn();
        this.mailtoIsOne = false;
        this.importance = 0;
        this.priority = MailPriority.NORMAL;
        this.forward = 0;
        this.mailSubject = optyMailModelVO.getMailTitle();
        this.mailFrom = optyMailModelVO.getMailFrom();
        this.mailTo = optyMailModelVO.getMailTo();
        this.mailcc = optyMailModelVO.getMailToCc();
        this.mailbcc = optyMailModelVO.getMailToBcc();
        this.mailBodies = null;
        List<MailBody> mailBodies = Lists.newArrayList();
        List<String> mailLang = new ArrayList<>();
        if(StringUtils.isNotBlank(optyMailModelVO.getMailLan())) {
            mailLang = Arrays.asList(optyMailModelVO.getMailLan().split(CommonConstant.MID_LINE));
        }
        boolean hasCnMailBody = CollectionUtils.isEmpty(mailLang)
                || (mailLang.contains(ZH_CN) && StringUtils.isNotBlank(optyMailModelVO.getMailBody()));
        if (hasCnMailBody) {
            MailBody mailBodyCn = new MailBody(ZH_CN, optyMailModelVO.getWarmCall(), optyMailModelVO.getSystemName(), optyMailModelVO.getClickLookCn(), optyMailModelVO.getMailLink(), optyMailModelVO.getMailBody());
            mailBodies.add(mailBodyCn);
        }
        boolean hasEnMailBody = (CollectionUtils.isNotEmpty(mailLang))
                && mailLang.contains(EN_US)
                && StringUtils.isNotBlank(optyMailModelVO.getMailBodyEn());
        if (hasEnMailBody) {
            MailBody mailBodyEn = new MailBody(EN_US,  optyMailModelVO.getWarmCallEn(), optyMailModelVO.getSystemNameEn(), optyMailModelVO.getClickLookEn(), optyMailModelVO.getMailLinkEn(), optyMailModelVO.getMailBodyEn());
            mailBodies.add(mailBodyEn);
        }
        this.mailBodies = mailBodies;
    }

    public String buildMailModelInXml() {
        StringBuilder mailBodyInXml = new StringBuilder();
        for (MailBody mailBody : mailBodies) {
            mailBodyInXml.append(mailBody.buildMailBodyInXml());
        }
        return String.format(MAIL_PATTERN,
                systemType,
                mailtoIsOne,
                importance,
                priority.getGrade(),
                forward,
                encapsulationTextByCDATA(mailSubject),
                encapsulationTextByCDATA(mailFrom),
                encapsulationTextByCDATA(mailTo),
                encapsulationTextByCDATA(mailcc),
                encapsulationTextByCDATA(mailbcc),
                mailBodyInXml.toString()
        );
    }







    public String getSystemType() {
        return systemType;
    }

    public void setSystemType(String systemType) {
        this.systemType = systemType;
    }

    public Boolean getMailtoIsOne() {
        return mailtoIsOne;
    }

    public void setMailtoIsOne(Boolean mailtoIsOne) {
        this.mailtoIsOne = mailtoIsOne;
    }

    public Integer getImportance() {
        return importance;
    }

    public void setImportance(Integer importance) {
        this.importance = importance;
    }

    public MailPriority getPriority() {
        return priority;
    }

    public void setPriority(MailPriority priority) {
        this.priority = priority;
    }

    public Integer getForward() {
        return forward;
    }

    public void setForward(Integer forward) {
        this.forward = forward;
    }

    public String getMailSubject() {
        return mailSubject;
    }

    public void setMailSubject(String mailSubject) {
        this.mailSubject = mailSubject;
    }

    public String getMailFrom() {
        return mailFrom;
    }

    public void setMailFrom(String mailFrom) {
        this.mailFrom = mailFrom;
    }

    public String getMailTo() {
        return mailTo;
    }

    public void setMailTo(String mailTo) {
        this.mailTo = mailTo;
    }

    public String getMailcc() {
        return mailcc;
    }

    public void setMailcc(String mailcc) {
        this.mailcc = mailcc;
    }

    public String getMailbcc() {
        return mailbcc;
    }

    public void setMailbcc(String mailbcc) {
        this.mailbcc = mailbcc;
    }

    public List<MailBody> getMailBodies() {
        return mailBodies;
    }

    public void setMailBodies(List<MailBody> mailBodies) {
        this.mailBodies = mailBodies;
    }

    @Override
    public String toString() {
        return "ZmailModel{" +
                "systemType='" + systemType + '\'' +
                ", mailtoIsOne=" + mailtoIsOne +
                ", importance=" + importance +
                ", priority=" + priority +
                ", forward=" + forward +
                ", mailSubject='" + mailSubject + '\'' +
                ", mailFrom='" + mailFrom + '\'' +
                ", mailTo='" + mailTo + '\'' +
                ", mailcc='" + mailcc + '\'' +
                ", mailbcc='" + mailbcc + '\'' +
                ", mailBodies=" + mailBodies +
                '}';
    }
}
