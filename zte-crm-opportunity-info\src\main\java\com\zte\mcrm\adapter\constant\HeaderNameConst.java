package com.zte.mcrm.adapter.constant;

/**
 * 微服务消息头规范要求必填的字段
 *
 * <AUTHOR> weiyiqing
 * @date 2021/4/8
 * @since v1.0
 */
public class HeaderNameConst {

    private HeaderNameConst() {
    }

    public static final String X_EMP_NO = "X-Emp-No";
    public static final String X_AUTH_VALUE = "X-Auth-Value";
    public static final String X_LANG_ID = "X-Lang-Id";
    public static final String X_TENANT_ID = "X-Tenant-Id";
    public static final String X_ORG_ID = "X-Org-Id";
    public static final String X_ORIGIN_SERVICENAME = "X-Origin-ServiceName";
    public static final String X_TARGET_SERVICENAME = "X-Target-ServiceName";
    public static final String X_AUTH_ACCESSKEY = "X-Auth-AccessKey";
    public static final String X_AUTH_TIMESTAMP = "X-Auth-Timestamp";
    public static final String X_AUTH_NONCE = "X-Auth-Nonce";
    public static final String X_AUTH_SIGNATURE = "X-Auth-Signature";
    public static final String X_ITP_VALUE = "X-Itp-Value";
    public static final String X_TENANT_ID_SUB = "X-Tenant-Id-Sub";

    public static final String X_COMPANY_ID = "X-Company-Id";

    public static final String DEFAULT_X_EMP_NO = "10000000";
    public static final String DEFAULT_X_AUTH_VALUE = "1234567890abcdef123456789abcdef0";
    public static final String DEFAULT_X_LANG_ID = "zh";
    public static final String DEFAULT_X_TENANT_ID = "10001";
    public static final String DEFAULT_X_ORG_ID = "ORG0100819";
    public static final String DEFAULT_X_ORIGIN_SERVICENAME = "zte-iss-approval-bff";
    public static final String DEFAULT_X_TARGET_SERVICENAME = "zte-iss-approval-manage";
    public static final String DEFAULT_X_ITP_VALUE = "appVersion=unknown;systemVersion=unknown";
}
