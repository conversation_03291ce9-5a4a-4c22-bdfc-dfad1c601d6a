package com.zte.crm.eva.base.common.utils;

import com.zte.itp.msa.core.model.PageRows;

import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class PageRowsUtil {

    /**
     * 构建空页数据对象
     *
     * @param pageNo   当前页
     * @param <T>
     * @return 空数据分页结果
     */
    public static <T> PageRows<T> buildEmptyPage(Long pageNo) {
        PageRows<T> pageRows = new PageRows<>();
        pageRows.setTotal(0);
        pageRows.setCurrent(pageNo == null ? 1 : pageNo);
        pageRows.setRows(Collections.emptyList());

        return pageRows;
    }

    /**
     * 整体构建分页信息
     *
     * @param pageNo 当前页
     * @param totalCount 总条数
     * @param curPageData 当前页数据
     * @param <T>
     * @return
     */
    public static <T> PageRows<T> buildPageRow(Long pageNo, Long totalCount, List<T> curPageData) {
        PageRows<T> res = new PageRows<>();
        res.setCurrent(pageNo);
        res.setTotal(totalCount);
        res.setRows(curPageData);
        return res;
    }

}
