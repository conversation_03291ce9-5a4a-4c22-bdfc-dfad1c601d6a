/**
 *  Copyright  2018 赵世光 All rights reserved.
 *  @author: 6011000197 
 *  @date: 2018年5月25日 上午10:24:49 
 */
package com.zte.mcrm.common.util;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.security.SecureRandom;

import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.util.local.LocalMessageUtils;

/**  
 * <p>ROWID生成工具，支持分布式，线程安全</p>  
 * <AUTHOR> ZhaoShiGuang
 * @date 2018年5月25日  
 */
public class RowIdUtil {
	
	/** ID前缀，Siebel系统为1-开头，SmartSales系统已5开头*/
	private static final String ID_PRE = "5";
	
	/**单例,private修饰保证其他对象不能new出这个实例**/
	private RowIdUtil(){
	}
	/** 饿汉式单例，保证线程安全*/
	private static RowIdUtil singleRowIdUtil = new RowIdUtil();
	
	private static long time = System.currentTimeMillis();
	/**
	 * <p>生产以5-开头，拼接当前系统ip后缀+当前系统36进制毫秒数的ROW_ID</p>
	 * <p>本方法适用于2018到2020中兴SmartSales微服务系统，当前SmartSales系统上线只部署两台服务器，并发数比较少，
	 * 	又由于需要兼容Siebel系统，所以有此方法，后期若有提高需要重新评估本方法，或者转成UUID的形式</p>
	 * @return
	 * @throws BusiException  
	 * <AUTHOR> ZhaoShiGuang
	 * @date 2018年5月25日
	 */
	public static String generateRowId() throws BusiException {
		try {
            //获取当前服务器IP后缀(36进制)
            String ip = Long.toString(Long.parseLong(singleRowIdUtil.getIP()),36);
            //获取当前毫秒数
            long currentTime = singleRowIdUtil.getCurrentTimeMillis();
            //当前毫秒数转为36进制
            String time = Long.toString(currentTime,36);
            SecureRandom random=new SecureRandom();
            int randomNum = random.nextInt(9)%10;
            //约定前缀+机器IP+1位随机码+横杆+系统时间递增数
            return ID_PRE +ip+randomNum+"-"+time;
		} catch (UnknownHostException e) {
			throw new BusiException("",LocalMessageUtils.getMessage("oppertunity.product.id.error") +e.getMessage());
		}
	}
	/**
	 * 获取当前将机器IP
	 * @return
	 * @throws UnknownHostException  
	 * <AUTHOR> ZhaoShiGuang
	 * @date 2018年5月25日
	 */
	private String getIP() throws UnknownHostException{
		InetAddress addr = InetAddress.getLocalHost();
        //获取当前IP
		String ip = addr.getHostAddress();
        //截取IP后缀
		ip = ip.substring(ip.lastIndexOf(".")+1);
		return ip;
	}
	/**
	 * 获取当前时间，已加同步锁避免服务并发时产生相同毫秒数
	 * @return  
	 * <AUTHOR> ZhaoShiGuang
	 * @date 2018年5月25日
	 */
	public  long  getCurrentTimeMillis(){
		long current = addTime();
        //重新实例化一个时间数，避免指向同一个内存地址
		long result = Long.valueOf(current);
		return result;
	}
	
	private long addTime(){
		//同步锁保证线程安全
		synchronized (singleRowIdUtil.getClass()){
			time++;
			return time;
		}
	}

}
