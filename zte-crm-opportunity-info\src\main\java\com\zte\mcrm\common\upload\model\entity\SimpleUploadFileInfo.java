package com.zte.mcrm.common.upload.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SimpleUploadFileInfo {

    @ApiModelProperty(value = "主键OID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long uploadFileId;

    @ApiModelProperty(value = "附件类型")
    private String uploadType;

    @ApiModelProperty(value = "业务单据ID")
    private String billId;

    @ApiModelProperty(value = "文档名称")
    private String docName;

    @ApiModelProperty(value = "DME附件key")
    private String dmeKey;

    /**
     * 判断是否需要插入
     * @return
     */
    public Boolean needInsertRecord() {
        if (uploadFileId == null) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
