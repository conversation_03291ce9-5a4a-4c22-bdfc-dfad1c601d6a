package com.zte.mcrm.channel.model.vo;

import io.swagger.annotations.ApiModelProperty;

public class OpportunityPartInfoVO {
    @ApiModelProperty(value = "商机id")
    private String rowId;
    @ApiModelProperty(value = "最终用户行业编码")
    private String finalCustomerParentTrade;

    @ApiModelProperty(value = "最终用户行业名")
    private String finalCustomerParentTradeName;
    @ApiModelProperty(value = "最终用户子行业编码")
    private String finalCustomerChildTrade;
    @ApiModelProperty(value = "最终用户子行业名")
    private String finalCustomerChildTradeName;
    @ApiModelProperty(value = "商机所属部门编号")
    private String deptNo;
    @ApiModelProperty(value = "商机所属部门名")
    private String deptName;

    public String getFinalCustomerChildTrade() {
        return finalCustomerChildTrade;
    }

    public void setFinalCustomerChildTrade(String finalCustomerChildTrade) {
        this.finalCustomerChildTrade = finalCustomerChildTrade;
    }


    public String getFinalCustomerParentTradeName() {
        return finalCustomerParentTradeName;
    }

    public void setFinalCustomerParentTradeName(String finalCustomerParentTradeName) {
        this.finalCustomerParentTradeName = finalCustomerParentTradeName;
    }

    public String getFinalCustomerChildTradeName() {
        return finalCustomerChildTradeName;
    }

    public void setFinalCustomerChildTradeName(String finalCustomerChildTradeName) {
        this.finalCustomerChildTradeName = finalCustomerChildTradeName;
    }

    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId;
    }

    public String getFinalCustomerParentTrade() {
        return finalCustomerParentTrade;
    }

    public void setFinalCustomerParentTrade(String finalCustomerParentTrade) {
        this.finalCustomerParentTrade = finalCustomerParentTrade;
    }



    public String getDeptNo() {
        return deptNo;
    }

    public void setDeptNo(String deptNo) {
        this.deptNo = deptNo;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    @Override
    public String toString() {
        return "OpportunityPartInfoVO{" +
                "rowId='" + rowId + '\'' +
                ", finalCustomerParentTrade='" + finalCustomerParentTrade + '\'' +
                ", finalCustomerParentTradeName='" + finalCustomerParentTradeName + '\'' +
                ", finalCustomerChildTrade='" + finalCustomerChildTrade + '\'' +
                ", finalCustomerChildTradeName='" + finalCustomerChildTradeName + '\'' +
                ", deptNo='" + deptNo + '\'' +
                ", deptName='" + deptName + '\'' +
                '}';
    }
}
