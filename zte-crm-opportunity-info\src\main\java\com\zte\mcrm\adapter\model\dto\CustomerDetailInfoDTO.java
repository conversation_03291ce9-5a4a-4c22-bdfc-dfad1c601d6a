package com.zte.mcrm.adapter.model.dto;
/* Started by AICoder, pid:88ebecc7a5b24522a50fbe711c22276a */
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(description = "客户信息模型")
@Getter
@Setter
public class CustomerDetailInfoDTO {

    @ApiModelProperty(value = "客户编码", example = "CN000000003192")
    private String customerCode;

    @ApiModelProperty(value = "客户基本信息")
    private CustomerBaseInfo baseInfo;

    @ApiModelProperty(value = "客户受限制主体信息")
    private ComplianceInfo complianceInfo;

    @ApiModelProperty(value = "主客户基本信息")
    private CustomerBaseInfo mainCustBaseInfo;
}
/* Ended by AICoder, pid:88ebecc7a5b24522a50fbe711c22276a */