package com.zte.mcrm.adapter.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ToString
public class OrganizationTreeData extends OrganizationNode implements Serializable {
    /**
     * 是否为叶子节点 默认：否
     */
    @ApiModelProperty("是否为叶子节点")
    private String isLeafNode = "N";
    /**
     * 上级节点
     */
    @ApiModelProperty("上级节点")
    List<OrganizationTreeData> parentNode = new ArrayList<>();
    /**
     * 下级节点
     */
    @ApiModelProperty("下级节点")
    List<OrganizationTreeData> sonNode= new ArrayList<>();
}
