package com.zte.mcrm.common.upload.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
*  实体类
* <AUTHOR> 10269210
* @date 2021/09/17
*/

@Setter @Getter @ToString
@ApiModel(description="")
public class ComUploadFile implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    @ApiModelProperty(value = "附件类型")
    private String uploadType;
    @ApiModelProperty(value = "业务单据ID")
    private String billId;
    @ApiModelProperty(value = "文档名称")
    private String docName;
    @ApiModelProperty(value = "附件描述")
    private String docDesc;
    @ApiModelProperty(value = "01：文本02：图片03：音频04：视频05：普通文件")
    private String fileType;
    @ApiModelProperty(value = "例如 png  jpeg  docx")
    private String extendFileName;
    @ApiModelProperty(value = "附件的顺序")
    private Integer fileOrder;
    @ApiModelProperty(value = "URL是否已加密（1是2否）")
    private String isEncrypt;
    @ApiModelProperty(value = "DME附件key")
    private String dmeKey;
    @ApiModelProperty(value = "附件密级：0公开,1内部公开,2秘密,3机密,4绝密")
    private Integer secretLevel;
    @ApiModelProperty(value = "是否有效(Y是/N否)")
    private String enabledFlag;
    @ApiModelProperty(value = "创建人")
    private String createdBy;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createdDate;
    @ApiModelProperty(value = "最后更新人")
    private String lastUpdatedBy;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpdatedDate;

}