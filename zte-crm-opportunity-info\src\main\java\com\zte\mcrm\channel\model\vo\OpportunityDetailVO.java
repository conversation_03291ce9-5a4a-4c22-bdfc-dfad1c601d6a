package com.zte.mcrm.channel.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.mcrm.common.util.ValidationGroups;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
*  实体类
* <AUTHOR>
* @date 2021/09/14
*/

@Setter @Getter @ToString
@ApiModel(description="")
public class OpportunityDetailVO implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @NotNull(message = "{optyId.null}", groups = {ValidationGroups.OptyAttribute.class})
    @NotBlank(message = "{optyId.empty}", groups = {ValidationGroups.OptyAttribute.class})
    private String rowId;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "")
    private Date created;
    @ApiModelProperty(value = "")
    private String createdBy;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "")
    private Date lastUpd;
    @ApiModelProperty(value = "")
    private String lastUpdBy;
    @NotBlank(message = "{opportunity.name.blank}", groups = {ValidationGroups.Submit.class})
    @Length(max = 100, message = "{opportunity.name.limit}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    @ApiModelProperty(value = "商机名称")
    private String attrib46;
    @ApiModelProperty(value = "预计签单金额")
    @Min(value = 5000, message = "{totalAmount.lower}", groups = {ValidationGroups.Submit.class})
    @NotNull(message = "{totalAmount.empty}", groups = {ValidationGroups.Submit.class})
    private BigDecimal totalAmount;
    @JsonFormat(pattern ="yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "预计发标/议标时间")
    private Date date1;
    @ApiModelProperty(value = "最终用户客户编码")
    private String lastAccId;
    @ApiModelProperty(value = "最终用户名称")
    @NotBlank(message = "{lastAccName.null}", groups = {ValidationGroups.Submit.class})
    private String lastAccName;
    @ApiModelProperty(value = "最终用户行业编码")
    @NotBlank(message = "{finalCustomerParentTrade.null}", groups = {ValidationGroups.Submit.class})
    private String finalCustomerParentTrade;
    @ApiModelProperty(value = "最终用户子行业编码")
    @NotBlank(message = "{finalCustomerChildTrade.null}", groups = {ValidationGroups.Submit.class})
    private String finalCustomerChildTrade;
    @ApiModelProperty(value = "最终用户联系人姓名")
    @Length(max = 50, message = "{finalCustomerContactName.length.limit}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    private String finalCustomerContactName;
    @ApiModelProperty(value = "最终用户联系人电话")
    @Length(max = 20, message = "{finalCustomerContactPhone.length.limit}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    private String finalCustomerContactPhone;
    @ApiModelProperty(value = "最终用户联系人邮箱")
    @Length(max = 100, message = "{finalCustomerContactEmail.length.limit}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    @Email(message = "{finalCustomerContactEmail.format.error}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    private String finalCustomerContactEmail;
    @ApiModelProperty(value = "投资方所在地（商机所属部门）")
    @NotBlank(message = "{deptNo.null}", groups = {ValidationGroups.Submit.class})
    private String deptNo;
    @ApiModelProperty(value = "项目当前阶段编码")
    @NotBlank(message = "{projectPhasesCode.null}", groups = {ValidationGroups.Submit.class})
    private String projectPhasesCode;
    @ApiModelProperty(value = "赢率")
    @NotBlank(message = "{winRate.null}", groups = {ValidationGroups.Submit.class})
    private String winRate;
    @ApiModelProperty(value = "招标类型编码")
    @NotBlank(message = "{tenderTypeCode.null}", groups = {ValidationGroups.Submit.class})
    private String tenderTypeCode;
    @ApiModelProperty(value = "招标方全称")
    @Length(max = 100, message = "{bidProviderName.length.limit}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    private String bidProviderName;
    @JsonFormat(pattern ="yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "竞标截止日期")
    private Date biddingDeadline;
    @ApiModelProperty(value = "报备人姓名")
    @Length(max = 50, message = "{agencyName.length.limit}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    private String agencyName;
    @ApiModelProperty(value = "报备人电话")
    @Length(max = 20, message = "{agencyPhone.length.limit}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    private String agencyPhone;
    @ApiModelProperty(value = "报备人邮箱")
    @Length(max = 100, message = "{agencyEmail.length.limit}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    @Email(message = "{agencyEmail.format.error}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    private String agencyEmail;
    @ApiModelProperty(value = "中兴业务经理id")
    private String businessManagerId;
    @ApiModelProperty(value = "中兴业务经理名字")
    private String businessManagerName;
    @ApiModelProperty(value = "商机概况")
    @Length(max=1500, message = "{projectDesc.length}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    @NotBlank(message = "{projectDesc.null}", groups = {ValidationGroups.Submit.class})
    private String projectDesc;
    @ApiModelProperty(value = "细分市场")
    @NotNull(message = "{optyAttribute.null}", groups = {ValidationGroups.OptyAttribute.class})
    @Length(max=1000, message = "{optyAttribute.length}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class, ValidationGroups.OptyAttribute.class})
    private String optyAttribute;
    @ApiModelProperty(value = "确认设备自用")
    private String selfUseFlag;
    @ApiModelProperty(value = "渠道商客户编码")
    private String crmCustomerCode;
    @ApiModelProperty(value = "渠道商名称")
    private String customerName;
    @ApiModelProperty(value = "报备代理商级别")
    private String agencyLevelName;
    @ApiModelProperty(value = "代理商级别编码")
    private String agencyLevelCode;
    @ApiModelProperty(value = "最终用户是否受限制主体")
    private String finalCustomerRestrictionFlag;
    @ApiModelProperty(value = "渠道商是否受限制主体")
    private String agencyRestrictionFlag;
    @ApiModelProperty(value = "TS审批单号")
    private String tsApprovalNumber;
    @ApiModelProperty(value = "激活次数")
    private Integer activeCount;
    @ApiModelProperty(value = "是否属于激活报备(Y/N)")
    private String fromActiveFlag;
    @ApiModelProperty(value = "从哪个商机激活的(记录商机id)")
    private String fromActiveOpty;

    @JsonFormat(pattern ="yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "预计签单时间")
    private Date date2;
}
