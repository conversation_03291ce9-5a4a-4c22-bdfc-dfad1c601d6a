
package com.zte.mcrm.channel.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;


@Setter
@Getter
@ToString
public class HistApproveDataDTO {

    @ExcelProperty(index = 0 ,value = "报备编号")
    private String optyCd;
    @ExcelProperty(index = 1 ,value = "报备状态")
    private String statusName;
    @ExcelProperty(index = 2 ,value = "报备的审批人")
    private String approver;
    @ExcelProperty(index = 3 ,value = "审批时间")
    private Date approveDate;
    @ExcelProperty(index = 4 ,value = "审批结论")
    private String approveResult;
    @ExcelProperty(index = 5 ,value = "审批意见")
    private String opinion;
    @ExcelProperty(index = 6 ,value = "仲裁类型")
    private String arbitrationType;
    @ExcelProperty(value = "error", index = 7)
    private String error;
    @ExcelProperty(value = "notImportResult", index = 8)
    private String notImportResult;
}
