package com.zte.mcrm.adapter.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class TreeNodeVO {

    /**
     * 编码
     */
    private String code;
    /**
     * 名称，模糊查询时必填
     */
    private String name;
    /**
     * 父编码
     */
    private String parentCode;
    /**
     * 是否叶子
     * 是否叶子节点  根据入参leafLevel确认当前节点是否是叶子
     */
    @JsonProperty("isLeaf")
    private boolean leafFlag;
    /**
     * 长编号	编号全路径（001-002）以短横线拼接  INHERIT_ID转换
     */
    private String codeFullPath;
    /**
     * 长名称	名称全路径（aaa-bbb）以短横线拼接  INHERIT_ID对应的ID，批量查询后，根据NAME的绑定逻辑，拼接转换
     */
    private String nameFullPath;
    /**
     * 子节点 仅模糊查询时，需要返回数据，其它为空即可
     */
    private List<TreeNodeVO> children;

}
