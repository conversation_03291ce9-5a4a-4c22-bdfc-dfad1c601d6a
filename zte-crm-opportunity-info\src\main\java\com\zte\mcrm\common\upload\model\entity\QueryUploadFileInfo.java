package com.zte.mcrm.common.upload.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName: QueryUploadFileInfo
 * @Description: 这里用一句话描述这个类的作用
 * @author: 吴伟 10285756
 * @date: 2021/6/23
 */
@Getter
@Setter
public class QueryUploadFileInfo {


    @ApiModelProperty(value = "文档名称")
    private String docName;

    @ApiModelProperty(value = "附件描述")
    private String docDesc;

    @ApiModelProperty(value = "file附件key")
    private String dmeKey;


    @ApiModelProperty(value = "附件类型")
    private String uploadType;

    @ApiModelProperty(value = "最后更新人工号")
    private String lastUpdatedBy;

    @ApiModelProperty(value = "最后更新时间")
    private String beginLastUpdatedDate;

    @ApiModelProperty(value = "最后更新时间")
    private String  endLastUpdatedDate;
}
