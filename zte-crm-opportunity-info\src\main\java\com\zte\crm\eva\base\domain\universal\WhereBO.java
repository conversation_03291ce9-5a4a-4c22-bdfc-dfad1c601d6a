package com.zte.crm.eva.base.domain.universal;

import cn.hutool.core.convert.Convert;
import com.zte.crm.eva.base.common.constant.universal.UniversalSqlConstants;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
public class WhereBO {
    public static final int INT_2 = 2;
    /**
     * in参数集合
     */
    private Map<String, List<Object>> inMap;

    /**
     * between参数集合
     */
    private Map<String, List<Object>> betweenMap;

    /**
     * like参数集合
     */
    private Map<String, Object> likeMap;

    /**
     * order集合
     */
    private Map<String, Object> orderByMap;

    /**
     * limit集合
     */
    private Map<String, Object> limitMap;

    /**
     * 构建where语句
     * @param queryParam
     * @param tableFields
     * @return
     */
    public static WhereBO buildWhere(Map<String, Object> queryParam, List<TableField> tableFields) {
        WhereBO whereBO = new WhereBO();
        Map<String, List<Object>> inMap = new HashMap<>(10);
        Map<String, List<Object>> betweenMap = new HashMap<>(10);
        Map<String, Object> likeMap = new HashMap<>(10);
        whereBO.setInMap(inMap);
        whereBO.setBetweenMap(betweenMap);
        whereBO.setLikeMap(likeMap);
        if (queryParam == null || queryParam.isEmpty()) {
            return whereBO;
        }
        whereBO.setOrderByMap(buildOrderByMap(queryParam));
        whereBO.setLimitMap(buildLimitMap(queryParam));

        Map<String, String> multiMap = tableFields.stream().collect(Collectors.toMap(TableField::getFieldRealName, TableField::getIsMultiType, (key1, key2) -> key2));
        Map<String, String> rangeMap = tableFields.stream().collect(Collectors.toMap(TableField::getFieldRealName, TableField::getIsRangeType, (key1, key2) -> key2));
        for (Map.Entry<String, Object> entry : queryParam.entrySet()) {
            if (multiMap.get(entry.getKey()) == null || rangeMap.get(entry.getKey()) == null) {
                continue;
            }
            if (StringUtils.equals("Y", multiMap.get(entry.getKey()))) {
                List<Object> multiData = Convert.toList(Object.class, entry.getValue());
                buildInSubMap(inMap, entry.getKey(), multiData);
            } else if (StringUtils.equals("Y", rangeMap.get(entry.getKey()))) {
                List<Object> rangeData = Convert.toList(Object.class, entry.getValue());
                buildBetweenMap(betweenMap, entry.getKey(), rangeData);
            } else {
                buildLikeMap(likeMap, entry.getKey(), entry.getValue());
            }
        }
        return whereBO;
    }

    /**
     * 构建排序子集合
     * @param queryParam
     * @return
     */
    private static Map<String, Object> buildOrderByMap(Map<String, Object> queryParam) {
        Map<String, Object> orderByMap = new HashMap<>(INT_2);
        String sort = Convert.toStr(queryParam.get(UniversalSqlConstants.SORT));
        String order = Convert.toStr(queryParam.get(UniversalSqlConstants.ORDER));
        if (StringUtils.isNotBlank(sort) && StringUtils.isNotBlank(order)) {
            orderByMap.put(UniversalSqlConstants.SORT, sort);
            orderByMap.put(UniversalSqlConstants.ORDER, order);
        }
        return orderByMap;
    }

    /**
     * 构建limit分页集合
     * @param queryParam
     * @return
     */
    private static Map<String, Object> buildLimitMap(Map<String, Object> queryParam) {
        Map<String, Object> limitMap = new HashMap<>(INT_2);

        String startRow = Convert.toStr(queryParam.get(UniversalSqlConstants.START_ROW));
        String rowSize = Convert.toStr(queryParam.get(UniversalSqlConstants.ROW_SIZE));
        if (StringUtils.isNotBlank(startRow) && StringUtils.isNotBlank(rowSize)) {
            limitMap.put(UniversalSqlConstants.START_ROW, Convert.toInt(startRow));
            limitMap.put(UniversalSqlConstants.ROW_SIZE, Convert.toInt(rowSize));
        }
        return limitMap;
    }

    /**
     * 构建like 模糊查询集合
     * @param likeMap
     * @param field
     * @param value
     */
    private static void buildLikeMap(Map<String, Object> likeMap, String field, Object value) {
        boolean isEmpty = StringUtils.isBlank(field) || StringUtils.isBlank(Convert.toStr(value));
        if (!isEmpty) {
            likeMap.put(field, value);
        }
    }

    /**
     * 构建范围查询集合
     * @param betweenMap
     * @param field
     * @param values
     */
    private static void buildBetweenMap(Map<String, List<Object>> betweenMap, String field, List<Object> values) {
        if (CollectionUtils.isNotEmpty(values) && values.size() == INT_2) {
            betweenMap.put(field, values);
        }
    }

    /**
     * 构建In子查询集合
     * @param inMap
     * @param field
     * @param values
     */
    private static void buildInSubMap(Map<String, List<Object>> inMap, String field, List<Object> values) {
        if (CollectionUtils.isNotEmpty(values)) {
            inMap.put(field, values);
        }
    }
}
