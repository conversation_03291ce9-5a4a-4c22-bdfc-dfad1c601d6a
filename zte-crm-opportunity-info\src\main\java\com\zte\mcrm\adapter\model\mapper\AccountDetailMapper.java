package com.zte.mcrm.adapter.model.mapper;

import com.zte.mcrm.adapter.model.vo.AccountDetail;
import com.zte.mcrm.adapter.model.vo.ChannelAccountInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2021/9/16
 */
@Mapper
public interface AccountDetailMapper {
    AccountDetailMapper INSTANCE = Mappers.getMapper(AccountDetailMapper.class);

    ChannelAccountInfo transToChannelAccountInfo(AccountDetail accountDetail);
}
