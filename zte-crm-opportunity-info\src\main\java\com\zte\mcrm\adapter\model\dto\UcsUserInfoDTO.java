package com.zte.mcrm.adapter.model.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 用户中心返回的用户信息对象
 * <AUTHOR>
 */
@Getter @Setter
@ToString
public class UcsUserInfoDTO {
/**
 * 账号ID
 */
private String accountId;
/**
 * 账号来源系统的编号
 */
private String source;
/**
 * 用户类型 0：外部用户 1：内部用户
 */
private String userType;
/**
 * 中文姓名
 */
private String personName;
/**
 * 英文姓名
 */
private String personNameEn;
/**
 * 用户头像
 */
private String avatar;
/**
 * 邮箱地址
 */
private String email;

/**
 * 手机号
 */
private String phone;
/**
 * 机构名称 主体类型为外部个体时为所属机构名称
内部用户为组织名称默认值为null
 */
private String orgName;
/**
 * 机构名称
 * 主体类型为外部个体时为所属机构名称
内部用户为组织名称，默认值为null
 */
private String orgNameEn;
/**
 * 用户状态
 */
private String accountStatus;
/**
 * 组织机构名称路径（中文）
 */
private String orgNamePath;
/**
 * 组织机构名称路径（英文）
 */
private String orgNamePathEn;
/**
 * 内部员工组织ID
 */
private String orgId;
/**
 * 用户属性
 */
private UserAttributesDTO userAttributes;
/**
 * 用户名
 */
private String userName;
/**
 * 性别
 */
private String gender;
/**
 * 所在国家代码
 */
private String country;
/**
 * 最近登录时间
 */
private String lastLoginDate;
/**
 * 主体类型
 */
private String subjectType;

}
