package com.zte.mcrm.adapter.clouddisk.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FilePermissionVO {
	private String tempFile;
	private  Integer rights;
	private Boolean encrypted;
	private Boolean supportCrypt;
	private Boolean canEdit;

	public String getTempFile() {
		return tempFile;
	}

	public void setTempFile(String tempFile) {
		this.tempFile = tempFile;
	}
	public void setRights(Integer rights) {
		this.rights = rights;
	}
	public Integer getRights() {
		return rights;
	}	
	public Boolean getEncrypted() {
		return encrypted;
	}
	public void setEncrypted(Boolean encrypted) {
		this.encrypted = encrypted;
	}
	public Boolean getSupportCrypt() {
		return supportCrypt;
	}
	public void setSupportCrypt(Boolean supportCrypt) {
		this.supportCrypt = supportCrypt;
	}
	public Boolean getCanEdit() {
		return canEdit;
	}
	public void setCanEdit(Boolean canEdit) {
		this.canEdit = canEdit;
	}
	
}
