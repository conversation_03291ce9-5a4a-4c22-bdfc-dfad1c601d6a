package com.zte.mcrm.common.util;

import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.opportunity.access.vo.ApproveVO;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.common.model.HttpResultData;
import com.zte.springbootframe.common.serviceregister.MicroServiceWithConfigUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 审批流程工具类
 * <AUTHOR>
 * @Created by 10261899
 * @Date 2019/8/19
 */
public class ApprovalProcessUtil {
    private static final Logger logger = LoggerFactory.getLogger(ApprovalProcessUtil.class);

    /**
     * 提交权限审批人
     */
    public static String sendApprove(ApproveVO approve) throws BusiException {

        return sendApprove(approve, null);
    }

    /**
     * 提交权限审批人
     */
    public static String sendApprove(ApproveVO approve, Map<String, String> headerParamsMap) throws BusiException {
        if (headerParamsMap == null) {

            headerParamsMap = RequestMessage.getHeader("common");
        }

        HttpResultData httpResult = MicroServiceWithConfigUtil
                .invokeServiceWithPostMethod("4", "/approve/submit", approve, headerParamsMap);
        String resultList = null;
        try {
            if (httpResult != null && RetCode.SUCCESS_CODE.equals(httpResult.getCode().getCode())) {
                resultList = JacksonJsonConverUtil.beanToJson(httpResult.getBo());
            }else {
                throw new BusiException(RetCode.SERVERERROR_CODE, RetCode.SERVERERROR_MSGID);
            }
        } catch (RouteException e) {
            logger.error(e.getErrorMsg(), e);
            throw new BusiException(e.getErrorCode(), e.getErrorMsg());
        }
        try {
            logger.info("调用审批结果：" + JacksonJsonConverUtil.beanToJson(resultList));
        } catch (RouteException e) {
            logger.error("json转换失败", e);
            throw new BusiException(e.getErrorCode(), e.getErrorMsg());
        }

        return resultList;
    }
}
