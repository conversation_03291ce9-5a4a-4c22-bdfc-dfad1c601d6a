package com.zte.mcrm.channel.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
*  实体类
* <AUTHOR>
* @date 2021/09/14
*/

@Setter @Getter @ToString
@ApiModel(description="")
public class OpportunityAddVO implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "")
    private String rowId;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "")
    private Date lastUpd;
    @ApiModelProperty(value = "")
    private String lastUpdBy;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "")
    private Date created;
    @ApiModelProperty(value = "")
    private String createdBy;
    @ApiModelProperty(value = "商机编码")
    private String optyCd;
    @ApiModelProperty(value = "商机状态")
    private String statusCd;
    @ApiModelProperty(value = "商机来源")
    private String dataSource;
    @ApiModelProperty(value = "是否新业务(Y/N)")
    private String isNewBusiness;
}
