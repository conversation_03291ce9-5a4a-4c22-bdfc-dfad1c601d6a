package com.zte.mcrm.channel.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;


/**
 *
 * <AUTHOR>
 * @date 2021/9/30
 */
@Getter
@Setter
@ToString
public class SimilarOpportunity {

    @ApiModelProperty(value = "商机主键id")
    private String opportunityRowId;

    @ApiModelProperty(value = "商机编号")
    String opportunityNo;

    @ApiModelProperty(value = "商机名称")
    String opportunityName;

    @ApiModelProperty(value = "渠道商")
    String channelVendor;

    @ApiModelProperty(value = "渠道商级别")
    String channelVendorLevel;

    @ApiModelProperty(value = "最终用户名称")
    String endUserName;

    @ApiModelProperty(value = "商机所属部门 / 代表处办事处")
    String opportunityDepartment;

    @ApiModelProperty(value = "商机状态")
    String opportunityStatus;

    @ApiModelProperty(value = "渠道商客户编码")
    private String crmCustomerCode;

    @ApiModelProperty(value = "商机产品, 多个之间以，隔开")
    private String opportunityProduct;

    @ApiModelProperty(value = "招标类型")
    private String tenderTypeCode;

    @ApiModelProperty(value = "最终用户行业(父行业-子行业形式)")
    private String industry;

    @ApiModelProperty(value = "最终用户行业编码")
    private String finalCustomerTradeCode;

    @ApiModelProperty(value = "最终用户子行业编码")
    private String finalCustomerTradeChildCode;

    @ApiModelProperty(value = "数据来源")
    private String dataSource;

    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "预计发标日期")
    private Date date1;

    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "预计签单日期")
    private Date date2;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "竞标截止日期")
    private Date biddingDeadline;
}
