package com.zte.crm.eva.base.common.constant.universal;

/**
 * @ClassName: UniversalSqlConstants
 * @Description: sql常量
 * @Author: 朱正梁10266679
 * @Date: 2021/11/22 18:26
 **/
public class UniversalSqlConstants {

    /**
     * 通用查询条数
     */
    public static final String TOTAL_SQL_TEMPLATE = "select count(1) as total from (%s) t";

    public static final String SELECT = "SELECT ";

    public static final String AS = " AS ";

    public static final String FROM = " FROM ";

    public static final String WHERE = " where ";

    public static final String LIKE_PREFIX = " like '%";

    public static final String LIKE_SUFFIX = "%' ";

    public static final String AND = " and ";

    public static final String SORT = "sort";

    public static final String ORDER = "order";

    public static final String START_ROW = "startRow";

    public static final String ROW_SIZE = "rowSize";

    public static final String ORDER_BY = " order by ";

    public static final String LIMIT = " limit ";

    public static final String COMMA = ",";

    public static final String ODD = "'";

    public static final String BETWEEN = " between ";

    public static final String IN = " in";

    public static final String LEFT_BRACKET = "(";

    public static final String RIGHT_BRACKET = ")";

    public static final String PAGE = "page";
}