
package com.zte.mcrm.adapter.model.dto;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Setter
@Getter
@ToString
public class PartnerObscureQueryDTO {
    /**
     * 合作伙伴名称
     * 支持模糊查询（不少于4个字符）；
     */
    private String name;

    /**
     * 合作伙伴大类
     */
    private String partnerBigCategory;
    /**
     * 合作伙伴小类
     */
    private String partnerSmallCategory;
    /**
     * 来源伙伴编码
     * 数组size不超过500
     */
    private List<String> sourceCodes;
    /**
     * 伙伴状态
     * 默认查询生效状态的记录，支持组合查询
     * approving 审批中；effective生效 ；invalid失效 ；rejected已驳回
     */
    private List<String> status;
    /**
     * 是否监控企业
     * N-未监控Y-已监控
     */
    private String isMonitored;
    /**
     * 法人代表
     * 支持文本like模糊查询
     */
    private String legalPersonName;
    /**
     * 合作伙伴Id
     * 数组size不超过500
     */
    private List<String> partnerIds;
}
