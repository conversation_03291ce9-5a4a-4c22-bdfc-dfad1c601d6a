package com.zte.leadinfo.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.zte.leadinfo.common.entity.CxDocumentDO;
import com.zte.leadinfo.common.mapper.CxDocumentMapper;
import com.zte.leadinfo.common.service.CxDocumentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务接口实现
 *
 * <AUTHOR>
 * @description 由 Mybatisplus Code Generator 创建
 * @since 2024-06-04 11:17:05
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class CxDocumentServiceImpl extends ServiceImpl<CxDocumentMapper, CxDocumentDO> implements CxDocumentService {

    @Override
    public List<CxDocumentDO> listByOpptyIds(List<String> optyRowIds) {
        if (CollectionUtils.isEmpty(optyRowIds)) {
            return Lists.newArrayList();
        }

        optyRowIds = optyRowIds.stream().filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

        LambdaQueryWrapper<CxDocumentDO> query = new LambdaQueryWrapper<>();
        query.in(CxDocumentDO::getBusinessId, optyRowIds);
        return this.list(query);
    }
}