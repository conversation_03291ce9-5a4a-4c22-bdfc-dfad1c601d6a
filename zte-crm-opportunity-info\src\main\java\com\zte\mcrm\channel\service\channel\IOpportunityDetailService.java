package com.zte.mcrm.channel.service.channel;

import com.zte.mcrm.channel.model.entity.OpportunityDetail;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.channel.model.entity.OpportunityMailEntity;

import java.util.List;
import java.util.Map;


/**
 *  服务接口类
 * <AUTHOR>
 * @date 2021/09/14
 */
public interface IOpportunityDetailService {
    /**
     * 根据ID查询
     * @param rowId 主键ID
     * @return 实体
     * <AUTHOR>
     * @date 2021/09/14
     */
	OpportunityDetail get(String rowId);

    /**
     * 查询列表
     * @param map 参数集合
     * @return 实体集合
     * <AUTHOR>
     * @date 2021/09/14
     */
	List<OpportunityDetail> getList(Map<String, Object> map);

    /**
     * 软删除
     * @param rowId 主键ID
     * @return 删除记录个数
     * <AUTHOR>
     * @date 2021/09/14
     */
	int softDelete(String rowId);

//	/**
//     * 删除
//     * @param rowId 主键ID
//     * @return 删除记录个数
//     * <AUTHOR>
//     * @date 2021/09/14
//     */
//	int delete(String rowId);

    /**
     * 新增
     * @param entity 实体对象
     * @return 新增的记录对象,注意是提交数据库之前的实体对象
     * <AUTHOR>
     * @date 2021/09/14
     */
	OpportunityDetail insert(OpportunityDetail entity, boolean needEncrypt);

    /**
     * 更新
     * @param entity 实体对象
     * @return 修改的记录对象,注意是提交数据库之前的实体对象
     * <AUTHOR>
     * @date 2021/09/14
     */
	OpportunityDetail update(OpportunityDetail entity,  boolean needEncrypt);

    /**
     * 更新商机详情，不更新时间和更新人
     * @param entity
     * @return
     */
    OpportunityDetail updateWithNoDate(OpportunityDetail entity, boolean needEncrypt);

    List<OpportunityDetail> getOpportunitiesByLastAccStatus(Integer lastAccStatus);

    /**
     * 更新最终用户状态
     * @param rowId
     * @param lastAccStatus
     * @return
     */
    int updateLastAccStatus(String rowId, Integer lastAccStatus);

    /**
     * 全量修改
     * @param entity 实体对象
     * @return 修改的记录对象,注意是提交数据库之前的实体对象
     * <AUTHOR>
     * @date 2021/09/14
     */
    OpportunityDetail updateAll(OpportunityDetail entity, boolean needEncrypt);

    /**
     * 插入或更新商机扩展信息
     * @param opportunityDetail 商机扩展信息
     * @return
     */
    OpportunityDetail insertOrUpdate(OpportunityDetail opportunityDetail, boolean needEncrypt);

    /**
     * 统计
     * @param map 参数集合
     * @return 统计总数
     * <AUTHOR>
     * @date 2021/09/14
     */
	long getCount(Map<String, Object> map);

   /**
    * 分页查询
    * @param map 参数集合
    * @return 实体集合
    * <AUTHOR>
    * @date 2021/09/14
    */
	List<OpportunityDetail> getPage(Map<String, Object> map);

    /**
    * 分页查询
    * @param form 参数集合
    * @return 实体集合
    * <AUTHOR>
    * @date 2021/09/14
    */
    PageRows<OpportunityDetail> getPageRows(FormData<OpportunityDetail> form);

    /**
     * 校验是否激活未超过2次
     * @param rowId
     */
    Boolean checkActiveCount(String rowId);


    /**
     * 查询邮件发送相关信息
     * @param rowId
     * @return
     */
    OpportunityMailEntity getOpportunityMailEntityByRowId(String rowId);

}
