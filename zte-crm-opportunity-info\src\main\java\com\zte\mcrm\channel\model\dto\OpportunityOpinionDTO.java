package com.zte.mcrm.channel.model.dto;

import com.zte.mcrm.channel.model.entity.BusinessManagerBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 商机审批意见
 * <AUTHOR>
 * @date 2021/10/13
 */
@Data
public class OpportunityOpinionDTO {

    @ApiModelProperty(value = "审批意见")
    private String opinion;
    @ApiModelProperty(value = "审批结果")
    private String result;
    @ApiModelProperty(value = "任务id")
    private String taskId;
    @ApiModelProperty(value = "中兴业务经理")
    private BusinessManagerBO businessManagerBO;
    @ApiModelProperty(value = "不通过原因，1：已有报备；2：不同意此报备；3：是受限制主体")
    private Integer failureReason;
    @ApiModelProperty(value = "TS审批单号")
    private String tsApprovalNo;
    @ApiModelProperty(value = "类似商机")
    private String similarOptyCd;
    @ApiModelProperty(value = "渠道商客户编码")
    private String crmCustomerCode;
    @ApiModelProperty(value = "最终用户Id")
    private String lastAccId;
    @ApiModelProperty(value = "组织编码")
    private String deptNo;

}
