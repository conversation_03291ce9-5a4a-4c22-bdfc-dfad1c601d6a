package com.zte.mcrm.common.exception;

import com.zte.itp.msa.core.model.RetCode;

/**
 * 业务运行时异常类
 *
 * <AUTHOR>
 * @date 2019/11/6
 */
public class BusinessRuntimeException extends RuntimeException {

    /**
     *
     */
    private static final long serialVersionUID = -5898464012866551545L;
    /**
     * 业务异常编码
     */
    private String exCode;
    /**
     * 业务异常消息
     */
    private String exMsg;
    /**
     * 业务可变参数
     */
    private Object[] arguments;

    public String getExCode() {
        return exCode;
    }

    public void setExCode(String exCode) {
        this.exCode = exCode;
    }

    public String getExMsg() {
        return exMsg;
    }

    public void setExMsg(String exMsg) {
        this.exMsg = exMsg;
    }

    public Object[] getArguments() {
        return arguments;
    }

    public void setArguments(Object[] arguments) {
        this.arguments = arguments;
    }

    public BusinessRuntimeException() {
        super();

    }
    public BusinessRuntimeException(Throwable cause) {
        super(cause);
    }

    public BusinessRuntimeException(Throwable cause, String exCode, String exMsg, Object[] arguments) {
        super(exMsg,cause);
        this.exCode = exCode;
        this.exMsg = exMsg;
        this.arguments = arguments;
    }

    public BusinessRuntimeException(Throwable cause, String exMsg, Object[] arguments) {
        super(exMsg,cause);
        this.exCode = RetCode.BUSINESSERROR_CODE;
        this.exMsg = exMsg;
        this.arguments = arguments;
    }

    public BusinessRuntimeException(Throwable cause, String exMsg) {
        super(exMsg,cause);
        this.exCode = RetCode.BUSINESSERROR_CODE;
        this.exMsg = exMsg;
    }

    public BusinessRuntimeException(Throwable cause, String exCode, String exMsg) {
        super(exMsg,cause);
        this.exCode = exCode;
        this.exMsg = exMsg;
    }

    public BusinessRuntimeException(String exCode, String exMsg, Object... arguments) {
        super(exMsg);
        this.exCode = exCode;
        this.exMsg = exMsg;
        this.arguments = arguments;
    }

    public BusinessRuntimeException(String exMsg, Object[] arguments) {
        super(exMsg);
        this.exCode = RetCode.BUSINESSERROR_CODE;
        this.exMsg = exMsg;
        this.arguments = arguments;
    }

    public BusinessRuntimeException(String exCode, String exMsg) {
        super(exMsg);
        this.exCode = exCode;
        this.exMsg = exMsg;
    }

    public BusinessRuntimeException(String exMsg) {
        super(exMsg);
        this.exCode = RetCode.BUSINESSERROR_CODE;
        this.exMsg = exMsg;
    }
}
