package com.zte.mcrm.common.webService;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.siebel.data.SiebelDataBean;
import com.siebel.data.SiebelPropertySet;
import com.siebel.data.SiebelService;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.config.face.ISysDataConfig;

/**
 * 
 * <AUTHOR> zhaoshiguang 2017年7月18日 上午11:26:25
 *
 */
public class SiebelServerUtil {
	
	/** 语言code，兼容zh zh_CN*/
	private static final String ZH = "zh";
	private static final String ZH_CN = "zh_CN";
	
	/**
	 * 调用Siebel WebService工具类，input为输入参数，service为BS名字，method为方法
	 * @param input
	 * @param service
	 * @param method
	 * @return
	 * @throws Exception
	 */
	public static synchronized SiebelPropertySet invoiceSiebelServer(SiebelPropertySet input,String service,String method) throws Exception{
		
		HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
        ISysDataConfig sysDataConfig = (ISysDataConfig)SpringContextUtil.getBean("sysDataConfig");
        // siebel地址
        String url = sysDataConfig.getSysDataMap("urlMap",sysDataConfig).get("siebelAppUrl");
        // siebel token信息
        String token = sysDataConfig.getSysDataMap("urlMap",sysDataConfig).get("token");
        // 当前用户
        String login = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
        // 用户使用语言
        String userLang =request.getHeader(SysGlobalConst.HTTP_HEADER_X_LANG_ID);
		if(login==null){
			login = input.getProperty("empShorNo");
		}
		if(login==null){
			throw new Exception("the user who operated Siebel can not be not");
		}
		if(userLang==null){
			userLang = ZH;
		}
		String siebelLang;
        // 语言转化
		if(ZH.equals(userLang) || ZH_CN.equals(userLang)){
			siebelLang = "chs";
		}else{
			siebelLang = "enu";
		}
		
		SiebelDataBean dataBean = new SiebelDataBean();
        // 登录
        dataBean.login(url, login, token, siebelLang);
        // 获取Sibel BS
        SiebelService businessService = dataBean.getService(service);
        SiebelPropertySet output = new SiebelPropertySet();
        // 调用siebel逻辑
        businessService.invokeMethod(method, input, output);
        dataBean.logoff();
        // 返回调用后的输出结果
        return output;
	}

}
