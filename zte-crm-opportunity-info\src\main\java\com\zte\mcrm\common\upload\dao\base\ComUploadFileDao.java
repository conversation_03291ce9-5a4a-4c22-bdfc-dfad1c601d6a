package com.zte.mcrm.common.upload.dao.base;

import com.zte.mcrm.common.upload.model.entity.ComUploadFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 *  数据访问接口类 
 * <AUTHOR> 10269210
 * @date 2021/09/17 
 */
@Mapper
public interface ComUploadFileDao{
    /**
     * 根据主键查询
     * <AUTHOR> 10269210
     * @param id 主键
     * @date 2021/09/17 
     * @return 实体
     */
	ComUploadFile get(@Param("id")Long id);
	
    /**
     * 查询列表
     * <AUTHOR> 10269210
     * @param map 查询条件
     * @date 2021/09/17 
     * @return 实体集合
     */
	List<ComUploadFile> getList(Map<String, Object> map);
	
	
    /**
     * 删除
     * <AUTHOR> 10269210
     * @param id 主键
     * @date 2021/09/17 
     * @return 删除总数
     */	
	int delete(@Param("id")Long id);

    /**
     * 删除
     * <AUTHOR>
     * @date 2021/09/14
     * @return 删除总数
     */
    int deleteByOpptyId(@Param("optyIds")List<String> optyIds);

    int softDeleteByOpptyId(@Param("optyIds")List<String> optyIds);

    /**
     * 动态新增
     * <AUTHOR> 10269210
     * @param entity 新增实体
     * @date 2021/09/17 
     * @return 新增总数
     */	
	int insert(ComUploadFile entity);

    /**
     * 批量新增
     * <AUTHOR> 10269210
     * @param list 新增实体集合
     * @date 2021/09/17 
     * @return 新增总数
     */	
	int insertByBatch(List<ComUploadFile> list);

    /**
     * 更新
     * <AUTHOR> 10269210
     * @param entity 更新条件
     * @date 2021/09/17 
     * @return 更新影响总数
     */		
	int update(ComUploadFile entity);
    /**
     * 根据dmeKey软删除记录
     * <AUTHOR>
     * @param entity 更新条件
     * @return 更新影响总数
     */
	int deleteByDmeKey(ComUploadFile entity);

    /**
     * 统计
     * <AUTHOR> 10269210
     * @param map 查询条件
     * @date 2021/09/17 
     * @return 统计总数
     */	
	long getCount(Map<String, Object> map);

    /**
     * 分页查询
     * <AUTHOR> 10269210
     * @param map 查询条件
     * @date 2021/09/17 
     * @return 实体集合
     */	
	List<ComUploadFile> getPage(Map<String, Object> map);

    /**
     * 根据给定的已上传附件的OID更新附件次序
     * <AUTHOR>
     * @param list
     * 已上传附件的OID列表
     * @return
     */
    int updateUploadFileOrder(List<ComUploadFile> list);

    /**
     * 批量查询业务的附件
     * @param uploadType
     * @param billIds
     * @return
     */
    List<ComUploadFile> queryUploadFileInBatch(@Param("uploadType") String uploadType, @Param("billIds") List<String> billIds);

    /**
     * 模糊统计
     * <AUTHOR>
     * @param map 查询条件
     * @date 2021/06/05
     * @return 统计总数
     */
    long getFuzzyCount(Map<String, Object> map);

    /**
     * 模糊分页查询
     * <AUTHOR>
     * @param map 查询条件
     * @date 2021/06/05
     * @return 实体集合
     */
    List<ComUploadFile> getFuzzyPage(Map<String, Object> map);

    /**
     * 查询附件密级
     * @param dmeKey 文档云key
     * @return 附件密级
     */
    Integer getSecretLevel(String dmeKey);

    /**
     * 查询附件类型
     * @param dmeKey 文档云key
     * @return 文件
     */
    ComUploadFile getFileType(String dmeKey);

    /**
     * 根据 dmekey 更新单个文件密级
     * @param entity
     * @return boolen
     * <AUTHOR> 10305348
     * @date 2021/09/06
     */
    int updateUploadFileSecretLevel(ComUploadFile entity);
}
