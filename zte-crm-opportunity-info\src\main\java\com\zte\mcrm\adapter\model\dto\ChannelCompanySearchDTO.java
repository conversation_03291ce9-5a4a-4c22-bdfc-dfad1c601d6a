package com.zte.mcrm.adapter.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 渠道商高级搜索对象
 *
 * @Author: <EMAIL>
 * @Date: 2021/07/23
 * @Description:
 */
@ApiModel(description = "渠道商高级搜索对象")
@Data
public class ChannelCompanySearchDTO {
    /**
     * 渠道商名称关键字最少四位
     */
    @ApiModelProperty(value = "公司名称关键字", required = true, example = "四个关键字")
    private String customerNamekeyWord;

    /**
     * 国家/地区
     */
    @ApiModelProperty(value = "国家", required = false, allowableValues = "CN,EN")
    private String countryRegion;

    @ApiModelProperty(value = "是否过滤合并客户", required = false, allowableValues = "Y,N")
    private String filterHistoryName;

    @ApiModelProperty(value = "是否过滤沉默客户", required = false, allowableValues = "Y,N")
    private String filterFrozenCustomer;

    @ApiModelProperty(value = "组织ID", required = false)
    private  String organizationId;

    public CompanyInfoSearchDTO toCompanyInfoSearchDTO() {
        CompanyInfoSearchDTO companyInfoSearchDto = new CompanyInfoSearchDTO();
        companyInfoSearchDto.setName(this.getCustomerNamekeyWord());
        companyInfoSearchDto.setKeyword(this.getCustomerNamekeyWord());
        companyInfoSearchDto.setCountry(StringUtils.isNotBlank(this.getCountryRegion()) ? this.getCountryRegion() : "CN");
        companyInfoSearchDto.setCountryCode("0001");
        return companyInfoSearchDto;
    }

}
