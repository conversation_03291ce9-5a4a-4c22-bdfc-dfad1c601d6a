package com.zte.mcrm.channel.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.ToString;

/**
 * 启动流程所需参数
 * <AUTHOR>
 * @date 2021/10/9
 */
@ToString
public class ApprovalStartParamsBO {
    public Integer getDraftStatus() {
        return draftStatus;
    }

    public void setDraftStatus(Integer draftStatus) {
        this.draftStatus = draftStatus;
    }

    public String getApprovingPerson() {
        return approvingPerson;
    }

    public void setApprovingPerson(String approvingPerson) {
        this.approvingPerson = approvingPerson;
    }

    public String getCreated() {
        return created;
    }

    public void setCreated(String created) {
        this.created = created;
    }

    public String getApprovalReportLink() {
        return approvalReportLink;
    }

    public void setApprovalReportLink(String approvalReportLink) {
        this.approvalReportLink = approvalReportLink;
    }

    public String getIChannelApprovalDetailLink() {
        return iChannelApprovalDetailLink;
    }

    public void setIChannelApprovalDetailLink(String iChannelApprovalDetailLink) {
        this.iChannelApprovalDetailLink = iChannelApprovalDetailLink;
    }

    public String getPrmApprovalDetailLink() {
        return prmApprovalDetailLink;
    }

    public void setPrmApprovalDetailLink(String prmApprovalDetailLink) {
        this.prmApprovalDetailLink = prmApprovalDetailLink;
    }

    public String getBusinessManagerId() {
        return businessManagerId;
    }

    public void setBusinessManagerId(String businessManagerId) {
        this.businessManagerId = businessManagerId;
    }

    public String getBusinessManager() {
        return businessManager;
    }

    public void setBusinessManager(String businessManager) {
        this.businessManager = businessManager;
    }

    public String getOptyCd() {
        return optyCd;
    }

    public void setOptyCd(String optyCd) {
        this.optyCd = optyCd;
    }

    public String getAttrib46() {
        return attrib46;
    }

    public void setAttrib46(String attrib46) {
        this.attrib46 = attrib46;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getDeptNo() {
        return deptNo;
    }

    public void setDeptNo(String deptNo) {
        this.deptNo = deptNo;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(String totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getFinalCustomerParentTradeName() {
        return finalCustomerParentTradeName;
    }

    public void setFinalCustomerParentTradeName(String finalCustomerParentTradeName) {
        this.finalCustomerParentTradeName = finalCustomerParentTradeName;
    }

    public String getFinalCustomerChildTrade() {
        return finalCustomerChildTrade;
    }

    public void setFinalCustomerChildTrade(String finalCustomerChildTrade) {
        this.finalCustomerChildTrade = finalCustomerChildTrade;
    }

    public String getTenderTypeCode() {
        return tenderTypeCode;
    }

    public void setTenderTypeCode(String tenderTypeCode) {
        this.tenderTypeCode = tenderTypeCode;
    }

    public String getProducts() {
        return products;
    }

    public void setProducts(String products) {
        this.products = products;
    }

    public String getLastAccName() {
        return lastAccName;
    }

    public void setLastAccName(String lastAccName) {
        this.lastAccName = lastAccName;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public String getKeyWordForQuery() {
        return keyWordForQuery;
    }

    public void setKeyWordForQuery(String keyWordForQuery) {
        this.keyWordForQuery = keyWordForQuery;
    }

    public String getExisted() {
        return existed;
    }

    public void setExisted(String existed) {
        this.existed = existed;
    }

    public String getStatusCd() {
        return statusCd;
    }

    public void setStatusCd(String statusCd) {
        this.statusCd = statusCd;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getProjectPhasesCode() {
        return projectPhasesCode;
    }

    public void setProjectPhasesCode(String projectPhasesCode) {
        this.projectPhasesCode = projectPhasesCode;
    }

    public String getProjectPhasesName() {
        return projectPhasesName;
    }

    public void setProjectPhasesName(String projectPhasesName) {
        this.projectPhasesName = projectPhasesName;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public String getWinRate() {
        return winRate;
    }

    public void setWinRate(String winRate) {
        this.winRate = winRate;
    }

    public String getDate1() {
        return date1;
    }

    public void setDate1(String date1) {
        this.date1 = date1;
    }

    public String getProjectDesc() {
        return projectDesc;
    }

    public void setProjectDesc(String projectDesc) {
        this.projectDesc = projectDesc;
    }

    public String getTenderTypeName() {
        return tenderTypeName;
    }

    public void setTenderTypeName(String tenderTypeName) {
        this.tenderTypeName = tenderTypeName;
    }

    public String getBlackListInfo() {
        return blackListInfo;
    }

    public void setBlackListInfo(String blackListInfo) {
        this.blackListInfo = blackListInfo;
    }

    public String getSimilarOpportunityInfo() {
        return similarOpportunityInfo;
    }

    public void setSimilarOpportunityInfo(String similarOpportunityInfo) {
        this.similarOpportunityInfo = similarOpportunityInfo;
    }

    public String getConsumerRestrictedParty() {
        return consumerRestrictedParty;
    }

    public void setConsumerRestrictedParty(String consumerRestrictedParty) {
        this.consumerRestrictedParty = consumerRestrictedParty;
    }

    public String getChannelRestrictedParty() {
        return channelRestrictedParty;
    }

    public void setChannelRestrictedParty(String channelRestrictedParty) {
        this.channelRestrictedParty = channelRestrictedParty;
    }

    public String getFinalCustomerParentTrade() {
        return finalCustomerParentTrade;
    }

    public void setFinalCustomerParentTrade(String finalCustomerParentTrade) {
        this.finalCustomerParentTrade = finalCustomerParentTrade;
    }

    public String getTsApprovalNumber() {
        return tsApprovalNumber;
    }

    public void setTsApprovalNumber(String tsApprovalNumber) {
        this.tsApprovalNumber = tsApprovalNumber;
    }

    public String getTsApprovalNo() {
        return tsApprovalNo;
    }

    public void setTsApprovalNo(String tsApprovalNo) {
        this.tsApprovalNo = tsApprovalNo;
    }

    public String getCheckedSimilarOpportunity() {
        return checkedSimilarOpportunity;
    }

    public void setCheckedSimilarOpportunity(String checkedSimilarOpportunity) {
        this.checkedSimilarOpportunity = checkedSimilarOpportunity;
    }

    public String getSimilarOptyCd() {
        return similarOptyCd;
    }

    public void setSimilarOptyCd(String similarOptyCd) {
        this.similarOptyCd = similarOptyCd;
    }

    public String getFailureReason() {
        return failureReason;
    }

    public void setFailureReason(String failureReason) {
        this.failureReason = failureReason;
    }

    public String getiChannelApprovalDetailLink() {
        return iChannelApprovalDetailLink;
    }

    public void setiChannelApprovalDetailLink(String iChannelApprovalDetailLink) {
        this.iChannelApprovalDetailLink = iChannelApprovalDetailLink;
    }

    public Integer getArbitrationType() {
        return arbitrationType;
    }

    public void setArbitrationType(Integer arbitrationType) {
        this.arbitrationType = arbitrationType;
    }

    public String getArbitrationTypeName() {
        return arbitrationTypeName;
    }

    public void setArbitrationTypeName(String arbitrationTypeName) {
        this.arbitrationTypeName = arbitrationTypeName;
    }

    public String getOrgLabel() {
        return orgLabel;
    }

    public void setOrgLabel(String orgLabel) {
        this.orgLabel = orgLabel;
    }

    public String getArbitrationLevel() {
        return arbitrationLevel;
    }

    public void setArbitrationLevel(String arbitrationLevel) {
        this.arbitrationLevel = arbitrationLevel;
    }

    public String getSsHttpHead() {
        return ssHttpHead;
    }

    public void setSsHttpHead(String ssHttpHead) {
        this.ssHttpHead = ssHttpHead;
    }

    @ApiModelProperty(value = "是否已创建客户操作，0：未创建，1：已创建")
    private Integer draftStatus;

    @ApiModelProperty(value = "审批人")
    private String approvingPerson;

    @ApiModelProperty(value = "提交时间")
    private String created;

    @ApiModelProperty(value = "审批页面链接地址")
    private String approvalReportLink;

    @ApiModelProperty(value = "IChannel侧商机详情链接地址")
    private String iChannelApprovalDetailLink;

    @ApiModelProperty(value = "PRM侧商机详情链接地址")
    private String prmApprovalDetailLink;

    @ApiModelProperty(value = "中兴业务经理工号")
    private String businessManagerId;


    @ApiModelProperty(value = "中兴业务经理姓名+工号")
    private String businessManager;

    @ApiModelProperty(value = "商机编号")
    private String optyCd;

    @ApiModelProperty(value = "商机名称")
    private String attrib46;

    @ApiModelProperty(value = "投资方所在地")
    private String deptName;

    @ApiModelProperty(value = "投资方所在地编码")
    private String deptNo;

    @ApiModelProperty(value = "发起人")
    private String createdBy;

    @ApiModelProperty(value = "预计签单金额（元）")
    private String totalAmount;

    @ApiModelProperty(value = "渠道合作伙伴名称")
    private String customerName;

    @ApiModelProperty(value = "最终用户行业")
    private String finalCustomerParentTradeName;

    @ApiModelProperty(value = "最终用户子行业编码")
    private String finalCustomerChildTrade;

    @ApiModelProperty(value = "招标类型编码")
    private String tenderTypeCode;

    @ApiModelProperty(value = "产品")
    private String products;

    @ApiModelProperty(value = "最终用户名称")
    private String lastAccName;

    @ApiModelProperty(value = "关键字")
    private String keyWord;

    @ApiModelProperty(value = "prm查询商机关键字")
    private String keyWordForQuery;

    @ApiModelProperty(value = "在客户中是否已存在")
    private String existed;

    @ApiModelProperty(value = "商机状态编码")
    private String statusCd;

    @ApiModelProperty(value = "商机状态名称")
    private String statusName;

    @ApiModelProperty(value = "商机当前阶段编码")
    private String projectPhasesCode;

    @ApiModelProperty(value = "商机当前阶段名称")
    private String projectPhasesName;

    @ApiModelProperty(value = "商机来源")
    private String dataSource;

    @ApiModelProperty(value = "赢率")
    private String winRate;

    @ApiModelProperty(value = "预计发标/议标时间")
    @JsonFormat(pattern ="yyyy-MM-dd", timezone = "GMT+8")
    private String date1;

    @ApiModelProperty(value = "商机概况")
    private String projectDesc;

    @ApiModelProperty(value = "招标类型")
    private String  tenderTypeName;
     @ApiModelProperty(value = "黑名单信息")
    private String  blackListInfo;

    @ApiModelProperty(value = "类似商机")
    private String  similarOpportunityInfo;
    @ApiModelProperty(value = "最终用户是否受限制主体")
    private String  consumerRestrictedParty;
     @ApiModelProperty(value = "渠道商是否受限制主体")
    private String  channelRestrictedParty;
    @ApiModelProperty(value = "最终用户行业")
    private String  finalCustomerParentTrade;

    @ApiModelProperty(value = "TS审批单号")
    private String  tsApprovalNumber;

    @ApiModelProperty(value = "ts审批单号")
    private String tsApprovalNo;

    @ApiModelProperty(value = "勾选的类似商机")
    private String checkedSimilarOpportunity;

    @ApiModelProperty(value = "类似商机列表")
    private String similarOptyCd;

    @ApiModelProperty(value = "同意/拒绝原因")
    private String failureReason;

    @ApiModelProperty(value = "不通过原因，1：已有报备；2：不同意此报备；3：是受限制主体, 启动流程时不设置值")
    private Integer arbitrationType;

    @ApiModelProperty(value = "不通过原因-名称，1：已有报备；2：不同意此报备；3：是受限制主体, 启动流程时不设置值")
    private String arbitrationTypeName;

    @ApiModelProperty(value = "商机所属部门类型（1：总监办，2：办事处）")
    private String orgLabel;

    @ApiModelProperty(value = "仲裁级别（根据预计签单金额判断） 1/2")
    private String arbitrationLevel;

    @ApiModelProperty(value = "链接前置域名，默认为”mktapi.zte.com.cn“")
    private String ssHttpHead;
}
