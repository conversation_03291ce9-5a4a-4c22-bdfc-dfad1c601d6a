package com.zte.leadinfo.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Data;

/**
 * 
 * <AUTHOR>
 * @TableName cx_appr_opp_ln
 */
@TableName(value ="cx_appr_opp_ln")
@Data
public class CxApprOppLn implements Serializable {
    /**
     * 
     */
    @TableId(value = "ROW_ID")
    private String rowId;

    /**
     * 
     */
    @TableField(value = "CREATED")
    private Date created;

    /**
     * 
     */
    @TableField(value = "CREATED_BY")
    private String createdBy;

    /**
     * 
     */
    @TableField(value = "LAST_UPD")
    private Date lastUpd;

    /**
     * 
     */
    @TableField(value = "LAST_UPD_BY")
    private String lastUpdBy;

    /**
     * 待我审批
     */
    @TableField(value = "ACTIVE_FLG")
    private String activeFlg;

    /**
     * 节点名称
     */
    @TableField(value = "APPROVE_NODE_ID")
    private String approveNodeId;

    /**
     * 审批人
     */
    @TableField(value = "APPROVE_USER_ID")
    private String approveUserId;

    /**
     * 操作类型-COM_POL_UW_DECISION
     */
    @TableField(value = "OPERATION_TYPE")
    private String operationType;

    /**
     * 审批意见
     */
    @TableField(value = "APPROVE_ADVICE")
    private String approveAdvice;

    /**
     * 审批转交意见
     */
    @TableField(value = "APPROVE_TRANSFER")
    private String approveTransfer;

    /**
     * 审批头Id
     */
    @TableField(value = "APPR_OPR_HEAD_ID")
    private String apprOprHeadId;

    /**
     * 审批日期
     */
    @TableField(value = "APPROVE_DATE")
    private Date approveDate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}