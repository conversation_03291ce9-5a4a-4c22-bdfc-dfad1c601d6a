package com.zte.mcrm.common.util;


import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.zte.mcrm.common.model.DynamicMessage;
import com.zte.mcrm.opportunity.common.OppSysConst;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.common.serviceregister.MicroServiceWithConfigUtil;
import com.zte.springbootframe.util.string.DateHelper;

/**
 * 动态消息工具类：拼接json、推送
 * <AUTHOR>
 *
 */
public class DynamicUtil {

    /**
     * 推送动态消息
     * @param dynamicMessage
     */
	public static void pushDynamic(DynamicMessage dynamicMessage) {
		ServletRequestAttributes servlet =((ServletRequestAttributes)RequestContextHolder.getRequestAttributes());
		HttpServletRequest request = null;
		if(null!=servlet) {
			request = servlet.getRequest();
		}
		String lang = "";
		String empShortNo = "";
		if(null!=request) {
			lang =request.getHeader(SysGlobalConst.HTTP_HEADER_X_LANG_ID);
			empShortNo = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
		} else {
			lang = OppSysConst.LANG_ZH_CN;
			empShortNo = OppSysConst.SADMIN;
		}
		Map<String,String> headerParamsMap = new HashMap<>(3);
		headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_TENANT_ID, "smartsales");
		headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_LANG_ID, lang);
		headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO, empShortNo);
		String url = "/dynamicInfo/save"; 
		try {
			if(StringUtils.isNotBlank(dynamicMessage.getOperContent())) {
				MicroServiceWithConfigUtil.invokeServiceWithPostMethod("4", url, dynamicMessage, headerParamsMap);
			}
		} catch (BusiException e) {
			e.printStackTrace();
		}
	}
	
}
