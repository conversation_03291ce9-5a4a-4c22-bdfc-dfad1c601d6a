package com.zte.mcrm.common.access.vo;

/**
 * 分布式锁VO
 * <AUTHOR>
 *
 */
public class LockVO {
	private String id;
	private String empId;
	private String methodName;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getEmpId() {
		return empId;
	}
	public void setEmpId(String empId) {
		this.empId = empId;
	}
	public String getMethodName() {
		return methodName;
	}
	public void setMethodName(String methodName) {
		this.methodName = methodName;
	}

    @Override
    public String toString() {
        return "LockVO{" +
                "id='" + id + '\'' +
                ", empId='" + empId + '\'' +
                ", methodName='" + methodName + '\'' +
                '}';
    }
}
