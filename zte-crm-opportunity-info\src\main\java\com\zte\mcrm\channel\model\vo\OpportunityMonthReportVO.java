package com.zte.mcrm.channel.model.vo;

import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.common.upload.model.entity.SimpleUploadFileInfo;
import com.zte.mcrm.common.util.ValidationGroups;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

@Setter
@Getter
@ToString
public class OpportunityMonthReportVO {
    @ApiModelProperty(value = "商机id")
    @NotBlank(message = "{optyId.null}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    private String optyId;
    @ApiModelProperty(value = "当前月报归属期，格式：年月，如：202107")
    @NotBlank(message = "{reportMonth.null}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    @Pattern(regexp = OpportunityConstant.REPORT_MONTH_REGEX, message = "{reportMonth.pattern}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    private String reportMonth;
    @Length(max=1500, message = "{reportInfo.length}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    @NotBlank(message = "{reportInfo.null}", groups = {ValidationGroups.Submit.class})
    @ApiModelProperty(value = "本月进展")
    private String reportInfo;
    @NotBlank(message = "{currentStatus.null}", groups = {ValidationGroups.Submit.class})
    @ApiModelProperty(value = "商机当前状态编码(进行中/中标/丢标/取消)")
    private String currentStatus;
    @ApiModelProperty(value = "商机当前状态名称(进行中/中标/丢标/取消)")
    private String currentStatusName;
    @ApiModelProperty(value = "产品列表")
    private List<OpportunityProductVO> opportunityProducts;
    @ApiModelProperty(value = "月报详情")
    @Valid
    private MonthReportDetailVO monthReportDetail;
    @ApiModelProperty(value = "附件列表")
    private List<SimpleUploadFileInfo> fileList;
}
