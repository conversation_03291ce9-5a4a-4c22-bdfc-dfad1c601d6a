package com.zte.mcrm.channel.constant;

import com.google.common.collect.Lists;
import com.zte.mcrm.channel.model.entity.OpptyStatus;
import com.zte.mcrm.common.access.vo.ComDictionaryMaintainVO;
import com.zte.mcrm.common.util.DictUtils;
import com.zte.opty.sync.util.CommonMapUtil;

import java.util.Collections;
import java.util.List;

public enum OpptyProgressEnum {
    /** 报备提交 */
    SUBMITED("submited","报备提交"){
        @Override
        public OpptyProgressEnum getPreNode() {
            return OTHER;
        }
    },
    /** 平台审批 */
    APPROVED("approved","平台审批"){
        @Override
        public OpptyProgressEnum getPreNode() {
            return SUBMITED;
        }
    },
//    DRAFT("draft","草稿"),
    /** 报备审批中 */
    REPORTED_APPROVALING("reportedApprovaling","报备审批中"){
        @Override
        public OpptyProgressEnum getPreNode() {
            return APPROVED;
        }

        @Override
        public String getDisplayedCode() {
            return REPORTED_SUCCESS.getCode();
        }
},
    /** 仲裁中 */
    ARBITRATION("arbitration","仲裁中"){
        @Override
        public OpptyProgressEnum getPreNode() {
            return APPROVED;
        }
        @Override
        public String getDisplayedCode() {
            return REPORTED_SUCCESS.getCode();
        }
    },
    /** 报备成功 */
    REPORTED_SUCCESS("Renewing","报备成功"){
        @Override
        public OpptyProgressEnum getPreNode() {
            return APPROVED;
        }
    },
    /** 报备不通过 */
    REPORTED_FAILED("Refused","报备不通过"){
        @Override
        public OpptyProgressEnum getPreNode() {
            return APPROVED;
        }
    },
    /** 报备失效 */
    INVALIDATION("invalidation","报备失效"),
    /** 立项审批中 */
    PROJECT_APPROVALING("Proj App Submit","立项审批中"),
    /** 立项成功 */
    PROJECT_SUCCESS("Transferred","立项成功") ,
    /** 招标项目 */
    TICKET_WIN("ticketWin","赢单"),
    /** 丢单 */
    TICKET_LOSS("Closed","丢单"),
    /** 取消 */
    CANCEL("cancel","取消"),
    /** OTHER */
    OTHER("","");

    private String code ;
    private String name;

    private OpptyProgressEnum(String code, String name) {
        this.code = code;
        this.name = name ;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return this.name;
    }

    public String getDisplayedCode() {
        return this.code;
    }


    public static OpptyProgressEnum getOpptyProgressEnum(String code) {
        for (OpptyProgressEnum opptyProgress : OpptyProgressEnum.values()) {
            if (opptyProgress.getCode().equalsIgnoreCase(code)) {
                return opptyProgress;
            }
        }
        return OTHER;
    }

    public static List<OpptyStatus> getOpptyStatus(String code, List<ComDictionaryMaintainVO> dictMaintains) {
        code = CommonMapUtil.STATUS_MAP.inverse().getOrDefault(code, code);
        OpptyProgressEnum opptyProgress = getOpptyProgressEnum(code);
        if (opptyProgress == OTHER) {
            return Collections.emptyList();
        }
        List<OpptyStatus> opptyStatuses = Lists.newArrayList();
        do {
           OpptyStatus opptyStatus = new OpptyStatus();
            String curCode = opptyProgress.getCode();
            String name = DictUtils.getName(curCode, dictMaintains);
            String displayedCode = opptyProgress.getDisplayedCode();
            String displayedName = DictUtils.getName(displayedCode, dictMaintains);
            opptyStatus.setCode(curCode);
            opptyStatus.setName(name.equals(curCode)?opptyProgress.getName():name);
            opptyStatus.setDisplayedName(displayedName);
            opptyStatuses.add(0,opptyStatus);
            opptyProgress = opptyProgress.getPreNode();
        } while (opptyProgress != OTHER);

        return opptyStatuses;
    }


    public OpptyProgressEnum getPreNode() {
        return REPORTED_SUCCESS;
    }

    public String  getNeedCode() {
        return this.getCode();
    }



}
