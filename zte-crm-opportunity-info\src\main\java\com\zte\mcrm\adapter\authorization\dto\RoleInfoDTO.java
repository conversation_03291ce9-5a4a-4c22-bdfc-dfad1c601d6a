package com.zte.mcrm.adapter.authorization.dto;

import com.zte.itp.authorityclient.entity.input.CommonModuleIdEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class RoleInfoDTO  extends CommonModuleIdEntity {
    @ApiModelProperty("模块Id")
    private String moduleId;
    @ApiModelProperty("行业Id")
    private List<String> industryIds;
    @ApiModelProperty("组织Id")
    private List<String> orgIds;
    @ApiModelProperty("角色Id")
    private String roleId;
}
