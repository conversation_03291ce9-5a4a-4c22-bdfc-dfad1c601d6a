# SOC智能应答系统 - 数据库表优化计划

## 执行时间
**计划创建时间**: 2025-01-27 14:35:00
**计划执行人**: 数据库设计团队
**预计完成时间**: 2025-01-29

## 优化目标
针对 `soc_ai_match_result` 表的设计进行优化，消除冗余，提升性能和易维护性。

## 当前问题分析

### 1. 字段冗余问题
- `match_score` 在两个表中都存在
- `satisfaction`、`response_content`、`source_index` 字段重复
- 外键关系复杂（同时关联3个ID）

### 2. 设计不够清晰
- 匹配结果和最终应答结果边界模糊
- 数据流向不够明确

## 优化方案

### 方案A：职责明确分离（推荐）

#### 1. `soc_ai_match_result` 表职责
- **仅存储AI匹配过程数据**
- 去除业务结果字段（satisfaction, response_content）
- 专注于匹配算法相关字段

```sql
-- 优化后的表结构
CREATE TABLE soc_ai_match_result (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '匹配结果ID',
    item_product_id BIGINT NOT NULL COMMENT '条目产品关联ID',
    data_source VARCHAR(50) NOT NULL COMMENT '数据源：GBBS,文档库等',
    source_id VARCHAR(100) COMMENT '数据源中的记录ID',
    source_description TEXT COMMENT '数据源条目描述',
    
    -- 匹配算法核心字段
    match_score DECIMAL(5,2) NOT NULL COMMENT '综合匹配度分数',
    semantic_score DECIMAL(5,2) COMMENT '语义相似度分数',
    context_score DECIMAL(5,2) COMMENT '上下文匹配分数',
    
    -- 业务匹配维度
    country_match TINYINT(1) DEFAULT 0 COMMENT '国家匹配：0-否,1-是',
    branch_match TINYINT(1) DEFAULT 0 COMMENT '分支匹配：0-否,1-是',
    customer_match TINYINT(1) DEFAULT 0 COMMENT '客户匹配：0-否,1-是',
    
    -- 数据源信息
    source_index VARCHAR(200) COMMENT '索引链接',
    rank_position INT COMMENT '匹配结果排名位置',
    
    -- 应用状态
    is_applied TINYINT(1) DEFAULT 0 COMMENT '是否已应用：0-否,1-是',
    applied_at DATETIME COMMENT '应用时间',
    
    -- 审计字段
    created_by VARCHAR(50) NOT NULL COMMENT '创建人工号',
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    enabled_flag CHAR(1) DEFAULT 'Y' NOT NULL COMMENT '有效标记(Y/N)',
    tenant_id BIGINT DEFAULT 10001 COMMENT '租户ID'
);
```

#### 2. `soc_item_product` 表职责
- **存储最终的应答结果**
- 保留所有业务应答字段
- 添加关联到应用的匹配结果

```sql
-- 为 soc_item_product 表添加字段
ALTER TABLE soc_item_product 
ADD COLUMN applied_match_result_id BIGINT COMMENT '应用的匹配结果ID',
ADD CONSTRAINT fk_applied_match_result 
FOREIGN KEY (applied_match_result_id) REFERENCES soc_ai_match_result(id);
```

### 方案B：保持现状，优化索引和查询

如果不想大幅修改表结构，可以：

1. **优化索引策略**
```sql
-- 针对性能优化的索引
CREATE INDEX idx_match_item_score ON soc_ai_match_result(item_product_id, match_score DESC, is_applied);
CREATE INDEX idx_match_source_rank ON soc_ai_match_result(data_source, source_id, rank_position);
```

2. **添加排名字段**
```sql
ALTER TABLE soc_ai_match_result 
ADD COLUMN rank_position INT COMMENT '匹配结果排名位置' AFTER match_score;
```

## 执行计划

### 阶段1：评估影响（1天）
- [ ] 分析现有代码依赖
- [ ] 评估数据迁移复杂度
- [ ] 制定测试方案

### 阶段2：实施优化（1天）  
- [ ] 执行表结构变更
- [ ] 数据迁移（如需要）
- [ ] 更新相关代码

### 阶段3：验证测试（0.5天）
- [ ] 功能回归测试
- [ ] 性能测试
- [ ] 数据完整性验证

## 风险评估

### 高风险
- 表结构变更可能影响现有功能

### 中风险  
- 数据迁移过程中的数据完整性

### 低风险
- 索引优化对现有功能基本无影响

## 推荐执行方案

**推荐采用方案A**，原因：
1. 职责更加清晰，符合单一职责原则
2. 减少数据冗余，提升维护性
3. 便于后续功能扩展
4. 提升查询性能

## 后续监控

- 监控查询性能变化
- 跟踪数据一致性
- 收集开发团队反馈

---
**备注**: 本计划需要与开发团队充分沟通后执行，确保对现有功能的影响最小化。