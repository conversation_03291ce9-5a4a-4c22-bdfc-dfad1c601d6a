package com.zte.mcrm.adapter.clouddisk.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;


/**
 *
 * <AUTHOR>
 */
@Component
@Primary
@ConfigurationProperties(prefix = "clouddisk")
public class CloudDiskConfig {
	private String xOriginServiceName;
	private String xAccessKey;
	private String xSecKey;
	private String url;
	private String httpsUrl;
	private String moaUrl;
	@Value("${socket_time_out:60000}")
	private Integer socketTimeOut;


	public String getxOriginServiceName() {
		return xOriginServiceName;
	}

	public void setxOriginServiceName(String xOriginServiceName) {
		this.xOriginServiceName = xOriginServiceName;
	}

	public String getxAccessKey() {
		return xAccessKey;
	}

	public void setxAccessKey(String xAccessKey) {
		this.xAccessKey = xAccessKey;
	}

	public String getxSecKey() {
		return xSecKey;
	}

	public void setxSecKey(String xSecKey) {
		this.xSecKey = xSecKey;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Integer getSocketTimeOut() {
		return socketTimeOut;
	}

	public void setSocketTimeOut(Integer socketTimeOut) {
		this.socketTimeOut = socketTimeOut;
	}

	public String getMoaUrl() {
		return moaUrl;
	}

	public void setMoaUrl(String moaUrl) {
		this.moaUrl = moaUrl;
	}

	public String getHttpsUrl() {
		return httpsUrl;
	}

	public void setHttpsUrl(String httpsUrl) {
		this.httpsUrl = httpsUrl;
	}
}