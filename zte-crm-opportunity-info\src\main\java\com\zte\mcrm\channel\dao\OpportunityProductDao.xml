<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zte.mcrm.channel.dao.OpportunityProductDao" >
    <!-- 屏蔽mybatis的2级缓存  <cache />  -->
  
    <!-- 记录和实体映射配置 -->
    <resultMap id="BaseMap" type="com.zte.mcrm.channel.model.entity.OpportunityProduct" >
        <id column="ROW_ID" property="rowId" jdbcType="BIGINT" />
		<result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR" />	
		<result column="LAST_UPD_BY" property="lastUpdBy" jdbcType="VARCHAR" />	
		<result column="CREATED" property="created" jdbcType="TIMESTAMP" />	
		<result column="LAST_UPD" property="lastUpd" jdbcType="TIMESTAMP" />	
		<result column="PROD_LV1_ID" property="prodLv1Id" jdbcType="VARCHAR" />	
		<result column="PROD_LV1_NAME" property="prodLv1Name" jdbcType="VARCHAR" />	
		<result column="PROD_LV2_ID" property="prodLv2Id" jdbcType="VARCHAR" />	
		<result column="PROD_LV2_NAME" property="prodLv2Name" jdbcType="VARCHAR" />	
		<result column="PROD_LV2_1_ID" property="prodLv21Id" jdbcType="VARCHAR" />	
		<result column="PROD_LV2_1_NAME" property="prodLv21Name" jdbcType="VARCHAR" />	
		<result column="PROD_LV3_ID" property="prodLv3Id" jdbcType="VARCHAR" />	
		<result column="PROD_LV3_NAME" property="prodLv3Name" jdbcType="VARCHAR" />	
		<result column="PROD_LV4_ID" property="prodLv4Id" jdbcType="VARCHAR" />	
		<result column="PROD_LV4_NAME" property="prodLv4Name" jdbcType="VARCHAR" />	
		<result column="OPPTY_ID" property="opptyId" jdbcType="VARCHAR" />
		<result column="business_type" property="businessType" jdbcType="VARCHAR"/>
		<result column="ACTIVE_FLG" property="activeFlg" jdbcType="VARCHAR" />	
		<result column="PRODUCT_AMOUNT" property="productAmount" jdbcType="DECIMAL" />	
		<result column="FOR_SIGN_DATE" property="forSignDate" jdbcType="DATE" />	
		<result column="DATA_SOURCE_PROD" property="dataSource" jdbcType="VARCHAR" />
		<result column="PAR_PROD_ID" property="parProdId" jdbcType="VARCHAR" />
		<result column="enableFlagExt" property="enableFlagExt" jdbcType="VARCHAR" />
		<result column = "successRateJson" property="successRateJson"
				typeHandler="com.zte.springbootframe.config.typehandler.OptionBOListTypeHandler"/>
		<result column = "enable_flag" property="enableFlag"
				typeHandler="com.zte.springbootframe.config.typehandler.OptionBOListTypeHandler"/>
    </resultMap>

    <sql id="base_column">
        t.id row_id,
        t.CREATE_BY as CREATED_BY ,
        t.CREATE_TIME create_time,
        t.PROD_LV1_ID ,
		t.PROD_LV1_NAME ,
		t.PROD_LV2_NAME ,
		t.PROD_LV2_1_NAME ,
		t.PROD_LV3_NAME ,
		t.PROD_LV4_ID ,
		t.PROD_LV4_NAME ,
		t.p_id as OPPTY_ID ,
		JSON_VALUE(t.main_prod, '$[0].value') AS ZTE_MAIN_PRODUCT,
		t.business_type ,
        t.FOR_SIGN_DATE ,
		t.DATA_SOURCE_PROD ,
		t.PAR_PROD_ID,
		t.success_rate successRateJson,
		t.product_operation_team as PROD_LV2_ID ,
		t.product_line as PROD_LV2_1_ID ,
		t.prod_major_class as PROD_LV3_ID ,
		t.last_modified_by as LAST_UPD_BY ,
		t.last_modified_time as LAST_UPD ,
		t.enable_flag,
		t.expected_sign_amount as PRODUCT_AMOUNT
	</sql>

    <sql id="base_where">
        <if test="rowId != null and rowId != ''"> and t.id = #{rowId}</if>
        <if test="createdBy != null and createdBy != ''"> and t.CREATE_BY = #{createdBy}</if>
        <if test="lastUpdBy != null and lastUpdBy != ''"> and t.last_modified_by = #{lastUpdBy}</if>
        <if test="created != null"> and t.CREATE_TIME = #{created}</if>
        <if test="lastUpd != null"> and t.last_modified_time = #{lastUpd}</if>
        <if test="prodLv1Id != null and prodLv1Id != ''"> and t.PROD_LV1_ID = #{prodLv1Id}</if>
        <if test="prodLv1Name != null and prodLv1Name != ''"> and t.PROD_LV1_NAME = #{prodLv1Name}</if>
        <if test="prodLv2Id != null and prodLv2Id != ''"> and t.last_modified_by = #{prodLv2Id}</if>
        <if test="prodLv2Name != null and prodLv2Name != ''"> and t.PROD_LV2_NAME = #{prodLv2Name}</if>
        <if test="prodLv21Id != null and prodLv21Id != ''"> and t.product_line = #{prodLv21Id}</if>
        <if test="prodLv21Name != null and prodLv21Name != ''"> and t.PROD_LV2_1_NAME = #{prodLv21Name}</if>
        <if test="prodLv3Id != null and prodLv3Id != ''"> and t.prod_major_class = #{prodLv3Id}</if>
        <if test="prodLv3Name != null and prodLv3Name != ''"> and t.PROD_LV3_NAME = #{prodLv3Name}</if>
        <if test="prodLv4Id != null and prodLv4Id != ''"> and t.PROD_LV4_ID = #{prodLv4Id}</if>
        <if test="prodLv4Name != null and prodLv4Name != ''"> and t.PROD_LV4_NAME = #{prodLv4Name}</if>
		<if test="zteMainProduct != null and zteMainProduct != ''"> and JSON_VALUE(t.main_prod, '$[0].value') = #{zteMainProduct}</if>
        <if test="opptyId != null and opptyId != ''"> and t.p_id = #{opptyId}</if>
		<if test="businessType != null and businessType != ''"> and t.business_type = #{businessType}</if>
        <if test="productAmount != null"> and t.expected_sign_amount = #{productAmount}</if>
        <if test="forSignDate != null"> and t.FOR_SIGN_DATE = #{forSignDate}</if>
        <if test="dataSource != null and dataSource != ''"> and t.DATA_SOURCE_PROD = #{dataSource}</if>
		<if test="parProdId != null and parProdId != ''"> and t.par_prod_id = #{parProdId}</if>
		<if test="businessTypes != null and businessTypes.size > 0">
			and t.business_type in
			<foreach collection = "businessTypes" item = "type" open = "(" close=")" separator = ",">
				#{type}
			</foreach>
		</if>
    </sql>

    <!-- 获取一条记录 -->
    <select id="get"  resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM s_opty_product t
        WHERE
        t.id=#{rowId, jdbcType=VARCHAR}
    </select>

	<select id="getOldOpportunityProductsByRowId" resultMap="BaseMap">
		SELECT
		p.PRODUCT_ID PROD_LV2_ID,
		p.PRODUCT_NAME_CN PROD_LV2_NAME,
		p.PRODUCT_NAME_EN,
		p.PRODUCT_PIRCE PRODUCT_AMOUNT
		FROM
		cx_prm_prod p
		WHERE
		p.CLUES_CODE = (SELECT o.CLUES_CODE from cx_prm_opp o where o.OPP_ID=#{rowId, jdbcType=VARCHAR})
	</select>

	<!-- 根据商机id批量失效 -->
	<update id="batchLogicDeleteByOptyId">
		update s_opty_product
		SET is_deleted = 1
		WHERE
		p_id = #{opptyId, jdbcType=VARCHAR}
		<if test="businessType != null and businessType != ''">
			and business_type = #{businessType, jdbcType=VARCHAR}
		</if>
	</update>
 
    <!-- 获取符合条件的记录列表 -->
    <select id="getList" parameterType="java.util.Map" resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM s_opty_product t
        WHERE is_deleted = 0
        <include refid="base_where"/>
    </select>
  
    <!-- 软删除一条记录 -->
	<update id="softDelete" >
		UPDATE s_opty_product
		SET is_deleted = 1
		WHERE
		id = #{rowId, jdbcType=VARCHAR}
	</update>
	<!-- 批量软删除记录 -->
	<update id="softDeleteByOpptyId" >
		UPDATE s_opty_product
		SET is_deleted = 1
		WHERE
		p_id = #{opptyId, jdbcType=VARCHAR}
	</update>

    <!-- 删除一条记录 -->
    <delete id="delete" >
        DELETE FROM s_opty_product
        WHERE
        id = #{rowId, jdbcType=VARCHAR}
    </delete>

	<delete id="deleteByOpptyId" >
		DELETE FROM s_opty_product
		WHERE
		p_id in (
		<foreach collection ="optyIds" item="item" index= "index" separator =",">
			#{item, jdbcType=VARCHAR}
		</foreach>)
	</delete>

    <!--添加一条记录 -->
    <insert id="insert" parameterType="com.zte.mcrm.channel.model.entity.OpportunityProduct" >
        INSERT INTO s_opty_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
		    <if test="rowId != null">id ,</if>
		    <if test="createdBy != null">CREATE_BY ,</if>
		    <if test="lastUpdBy != null">last_modified_by ,</if>
		    <if test="created != null">CREATE_TIME ,</if>
		    <if test="lastUpd != null">last_modified_time ,</if>
		    <if test="prodLv1Id != null">PROD_LV1_ID ,</if>
		    <if test="prodLv1Name != null">PROD_LV1_NAME ,</if>
		    <if test="prodLv2Id != null">product_operation_team ,</if>
		    <if test="prodLv2Name != null">PROD_LV2_NAME ,</if>
		    <if test="prodLv21Id != null">product_line ,</if>
		    <if test="prodLv21Name != null">PROD_LV2_1_NAME ,</if>
		    <if test="prodLv3Id != null">prod_major_class ,</if>
		    <if test="prodLv3Name != null">PROD_LV3_NAME ,</if>
		    <if test="prodLv4Id != null">PROD_LV4_ID ,</if>
		    <if test="prodLv4Name != null">PROD_LV4_NAME ,</if>
		    <if test="opptyId != null">P_ID ,</if>
			<if test="businessType != null">business_type,</if>
		    <if test="activeFlg != null">enable_flag ,</if>
		    <if test="productAmount != null">expected_sign_amount ,</if>
		    <if test="forSignDate != null">FOR_SIGN_DATE ,</if>
		    <if test="dataSource != null">DATA_SOURCE_PROD ,</if>
			<if test="parProdId != null">PAR_PROD_ID ,</if>
			<if test="mainProd != null">main_prod ,</if>
			<if test="currency != null">currency ,</if>
			<if test="successRate != null">success_rate ,</if>
			<if test="marketType != null">market_type ,</if>
			<if test="forSignAmount != null">for_sign_amount ,</if>
			<if test="enableFlagExt != null">enable_flag_ext ,</if>
			<if test="successRateExt != null">success_rate_ext </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
    	    <if test="rowId != null">#{rowId, jdbcType=VARCHAR} ,</if>
    	    <if test="createdBy != null">#{createdBy, jdbcType=VARCHAR} ,</if>
    	    <if test="lastUpdBy != null">#{lastUpdBy, jdbcType=VARCHAR} ,</if>
    	    <if test="created != null">#{created, jdbcType=TIMESTAMP} ,</if>
    	    <if test="lastUpd != null">#{lastUpd, jdbcType=TIMESTAMP} ,</if>
    	    <if test="prodLv1Id != null">#{prodLv1Id, jdbcType=VARCHAR} ,</if>
    	    <if test="prodLv1Name != null">#{prodLv1Name, jdbcType=VARCHAR} ,</if>
    	    <if test="prodLv2Id != null">#{prodLv2Id, jdbcType=VARCHAR} ,</if>
    	    <if test="prodLv2Name != null">#{prodLv2Name, jdbcType=VARCHAR} ,</if>
    	    <if test="prodLv21Id != null">#{prodLv21Id, jdbcType=VARCHAR} ,</if>
    	    <if test="prodLv21Name != null">#{prodLv21Name, jdbcType=VARCHAR} ,</if>
    	    <if test="prodLv3Id != null">#{prodLv3Id, jdbcType=VARCHAR} ,</if>
    	    <if test="prodLv3Name != null">#{prodLv3Name, jdbcType=VARCHAR} ,</if>
    	    <if test="prodLv4Id != null">#{prodLv4Id, jdbcType=VARCHAR} ,</if>
    	    <if test="prodLv4Name != null">#{prodLv4Name, jdbcType=VARCHAR} ,</if>
    	    <if test="opptyId != null">#{opptyId, jdbcType=VARCHAR} ,</if>
			<if test="businessType != null">#{businessType, jdbcType=VARCHAR} ,</if>
    	    <if test="activeFlg != null">#{activeFlg, jdbcType=VARCHAR} ,</if>
    	    <if test="productAmount != null">#{productAmount, jdbcType=DECIMAL} ,</if>
    	    <if test="forSignDate != null">#{forSignDate, jdbcType=DATE} ,</if>
    	    <if test="dataSource != null">#{dataSource, jdbcType=VARCHAR} ,</if>
			<if test="parProdId != null">#{parProdId, jdbcType=VARCHAR} ,</if>
			<if test="mainProd != null">#{mainProd, jdbcType=VARCHAR} ,</if>
			<if test="currency != null">#{currency, jdbcType=VARCHAR} ,</if>
			<if test="successRate != null">#{successRate, jdbcType=VARCHAR} ,</if>
			<if test="marketType != null">#{marketType, jdbcType=VARCHAR} ,</if>
			<if test="forSignAmount != null">#{forSignAmount, jdbcType=DECIMAL} ,</if>
			<if test="enableFlagExt != null">#{enableFlagExt, jdbcType=VARCHAR} ,</if>
			<if test="successRateExt != null">#{successRateExt, jdbcType=VARCHAR} </if>
        </trim>
    </insert>

    <!--批量添加记录 -->
    <insert id="insertByBatch" parameterType="java.util.List" >
        INSERT INTO s_opty_product
        (
		    id ,
    	    CREATE_BY ,
    	    last_modified_by ,
    	    CREATE_TIME ,
    	    last_modified_time ,
    	    PROD_LV1_ID ,
    	    PROD_LV1_NAME ,
    	    product_operation_team ,
    	    PROD_LV2_NAME ,
    	    product_line ,
    	    PROD_LV2_1_NAME ,
    	    prod_major_class ,
    	    PROD_LV3_NAME ,
    	    PROD_LV4_ID ,
    	    PROD_LV4_NAME ,
    	    p_id ,
			business_type ,
    	    enable_flag ,
    	    expected_sign_amount ,
    	    FOR_SIGN_DATE ,
			DATA_SOURCE_PROD ,
			PAR_PROD_ID ,
			main_prod ,
			currency ,
			success_rate ,
			market_type ,
			for_sign_amount ,
			enable_flag_ext ,
		    success_rate_ext
        )
        VALUES
        <foreach collection ="list" item="item" index= "index" separator =",">
        (
    	    #{item.rowId, jdbcType=BIGINT} ,
    	    #{item.createdBy, jdbcType=VARCHAR} ,
    	    #{item.lastUpdBy, jdbcType=VARCHAR} ,
    	    #{item.created, jdbcType=TIMESTAMP} ,
    	    #{item.lastUpd, jdbcType=TIMESTAMP} ,
    	    #{item.prodLv1Id, jdbcType=VARCHAR} ,
    	    #{item.prodLv1Name, jdbcType=VARCHAR} ,
    	    #{item.prodLv2Id, jdbcType=VARCHAR} ,
    	    #{item.prodLv2Name, jdbcType=VARCHAR} ,
    	    #{item.prodLv21Id, jdbcType=VARCHAR} ,
    	    #{item.prodLv21Name, jdbcType=VARCHAR} ,
    	    #{item.prodLv3Id, jdbcType=VARCHAR} ,
    	    #{item.prodLv3Name, jdbcType=VARCHAR} ,
    	    #{item.prodLv4Id, jdbcType=VARCHAR} ,
    	    #{item.prodLv4Name, jdbcType=VARCHAR} ,
    	    #{item.opptyId, jdbcType=VARCHAR} ,
			#{item.businessType, jdbcType=VARCHAR} ,
    	    #{item.activeFlg, jdbcType=VARCHAR} ,
    	    #{item.productAmount, jdbcType=DECIMAL} ,
    	    #{item.forSignDate, jdbcType=DATE} ,
    	    #{item.dataSource, jdbcType=VARCHAR} ,
			#{item.parProdId, jdbcType=VARCHAR}  ,
			#{item.mainProd, jdbcType=VARCHAR} ,
			#{item.currency, jdbcType=VARCHAR} ,
			#{item.successRate, jdbcType=VARCHAR} ,
			#{item.marketType, jdbcType=VARCHAR} ,
			#{item.forSignAmount, jdbcType=DECIMAL} ,
			#{item.enableFlagExt, jdbcType=VARCHAR} ,
			#{item.successRateExt, jdbcType=VARCHAR}
        )
        </foreach>
    </insert>
  
    <!-- 更新一条记录 -->
    <update id="update" parameterType="com.zte.mcrm.channel.model.entity.OpportunityProduct" >
        UPDATE s_opty_product
        <set>
		    <if test="createdBy != null">CREATE_BY=#{createdBy, jdbcType=VARCHAR} ,</if>
		    <if test="lastUpdBy != null">last_modified_by=#{lastUpdBy, jdbcType=VARCHAR} ,</if>
		    <if test="created != null">CREATE_TIME=#{created, jdbcType=TIMESTAMP} ,</if>
		    <if test="lastUpd != null">last_modified_time=#{lastUpd, jdbcType=TIMESTAMP} ,</if>
		    <if test="prodLv1Id != null">PROD_LV1_ID=#{prodLv1Id, jdbcType=VARCHAR} ,</if>
		    <if test="prodLv1Name != null">PROD_LV1_NAME=#{prodLv1Name, jdbcType=VARCHAR} ,</if>
		    <if test="prodLv2Id != null">product_operation_team=#{prodLv2Id, jdbcType=VARCHAR} ,</if>
		    <if test="prodLv2Name != null">PROD_LV2_NAME=#{prodLv2Name, jdbcType=VARCHAR} ,</if>
		    <if test="prodLv21Id != null">product_line=#{prodLv21Id, jdbcType=VARCHAR} ,</if>
		    <if test="prodLv21Name != null">PROD_LV2_1_NAME=#{prodLv21Name, jdbcType=VARCHAR} ,</if>
		    <if test="prodLv3Id != null">prod_major_class=#{prodLv3Id, jdbcType=VARCHAR} ,</if>
		    <if test="prodLv3Name != null">PROD_LV3_NAME=#{prodLv3Name, jdbcType=VARCHAR} ,</if>
		    <if test="prodLv4Id != null">PROD_LV4_ID=#{prodLv4Id, jdbcType=VARCHAR} ,</if>
		    <if test="prodLv4Name != null">PROD_LV4_NAME=#{prodLv4Name, jdbcType=VARCHAR} ,</if>
		    <if test="opptyId != null">P_ID=#{opptyId, jdbcType=VARCHAR} ,</if>
			<if test="businessType != null">business_type=#{businessType, jdbcType=VARCHAR} ,</if>
		    <if test="activeFlg != null">enable_flag=#{activeFlg, jdbcType=VARCHAR} ,</if>
		    <if test="productAmount != null">expected_sign_amount=#{productAmount, jdbcType=DECIMAL} ,</if>
		    <if test="forSignDate != null">FOR_SIGN_DATE=#{forSignDate, jdbcType=DATE} ,</if>
			<if test="dataSource != null">DATA_SOURCE_PROD=#{dataSource, jdbcType=VARCHAR} ,</if>
			<if test="parProdId != null">PAR_PROD_ID=#{parProdId, jdbcType=VARCHAR} ,</if>
			<if test="mainProd != null">MAIN_PROD=#{mainProd, jdbcType=VARCHAR} ,</if>
			<if test="currency != null">CURRENCY=#{currency, jdbcType=VARCHAR} ,</if>
			<if test="successRate != null">SUCCESS_RATE=#{successRate, jdbcType=VARCHAR} ,</if>
			<if test="marketType != null">MARKET_TYPE=#{marketType, jdbcType=VARCHAR} ,</if>
			<if test="forSignAmount != null">FOR_SIGN_AMOUNT=#{forSignAmount, jdbcType=DECIMAL} ,</if>
			<if test="enableFlagExt != null">ENABLE_FLAG_EXT=#{enableFlagExt, jdbcType=VARCHAR} </if>
	    </set>
        WHERE
        id=#{rowId, jdbcType=VARCHAR}
    </update>
  
    <!-- 翻页函数:获取符合条件的记录数 -->
    <select id="getCount" parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT count(*) FROM s_opty_product t
        WHERE 1=1
        <include refid="base_where"/>
    </select>
	
    <!-- 翻页函数:获取一页的记录集 -->
    <select id="getPage" parameterType="java.util.Map" resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM s_opty_product t
        WHERE 1=1
        <include refid="base_where"/>
        <if test="orderField != null and orderField == 'rowId'"> order by t.id
            <if test="order != null and order == 'desc'"> desc </if>
        </if>
        <if test="startRow != null and rowSize != null"> limit #{startRow},#{rowSize} </if>
    </select>

	<select id="getMainProdsRelationByOptyIds" resultMap="BaseMap">
		select p.id rowId,
		       p.p_id,
		       p.business_type,
		       p.PAR_PROD_ID
		from s_opty_product p
		where p.is_deleted = 0
		and p.business_type in ('newOpportunity','pdm_prod')
		<if test="optyIds != null and optyIds.size() > 0">
			and p.p_id in (
			<foreach collection ="optyIds" item="item" index= "index" separator =",">
				#{item, jdbcType=VARCHAR}
			</foreach>)
		</if>
	</select>

</mapper>
