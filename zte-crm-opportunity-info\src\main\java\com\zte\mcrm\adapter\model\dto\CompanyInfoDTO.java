package com.zte.mcrm.adapter.model.dto;

import com.zte.mcrm.adapter.model.vo.UnregisteredCompanyVO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2021/07/21
 * @Description:
 */
@Data
public class CompanyInfoDTO {

    /**
     * 企业名称
     */
    private String name;
    /**
     * 企业英文名称
     */
    private String nameEn;
    /**
     * 企业状态（经营状态）
     */
    private String regStatus;
    /**
     * 统一社会信用代码
     */
    private String creditCode;
    /**
     * 企业类型
     */
    private String orgType;
    /**
     * 法人类型
     */
    private String legalPersonType;
    /**
     * 成立日期
     */
    private Long establishTime;
    /**
     * 法人姓名
     */
    private String legalPersonName;
    /**
     * 区
     */
    private String district;
    /**
     * 城市
     */
    private String city;
    /**
     * 省/州
     */
    private String province;
    /**
     * 国家/地区
     */
    private String country;
    /**
     * 注册资金
     */
    private String regCapital;
    /**
     * 注册资本币种
     */
    private String regCapitalCurrency;
    /**
     * 实收注册资金
     */
    private String actualCapital;
    /**
     * 实收注册资本币种
     */
    private String actualCapitalCurrency;
    /**
     * 登记机关
     */
    private String regInstitute;
    /**
     * 股票名
     */
    private String bondName;
    /**
     * 股票类型
     */
    private String bondType;
    /**
     * 股票号
     */
    private String bondNum;
    /**
     * 股票曾用名
     */
    private String usedBondName;
    /**
     * 注册地址
     */
    private String regLocation;
    /**
     * 公司主页
     */
    private String website;
    /**
     * 企业邮箱
     */
    private String email;
    /**
     * 公司电话
     */
    private String phoneNumber;
    /**
     * 人员规模
     */
    private String staffNumRange;
    /**
     * 参保人数
     */
    private String insuredCount;
    /**
     * 评分
     */
    private String score;
    /**
     * 国民经济行业分类门类
     */
    private String industryCategory;
    /**
     * 行业门类code
     */
    private String industryCategoryCode;
    /**
     * N-国内Y-国际
     */
    private String isInternational;
    /**
     * N-没监控Y-被监控
     */
    private String isMonitored;
    /**
     * 公司扩展信息
     */
    private String otherExtendFields;
    /**
     * 是否小微企业
     */
    private String isMicroEnt;
    /**
     * 曾用名
     */
    private List<String> orgHistoryNames;
    /**
     * 对象转换为UnregisteredCompanyVO
     * @return
     */
    public UnregisteredCompanyVO toUnregisteredCompany() {
        UnregisteredCompanyVO unregisteredCompanyVO = new UnregisteredCompanyVO();
        unregisteredCompanyVO.setCustomerName(this.getName());
        unregisteredCompanyVO.setSocialCreditCode(this.getCreditCode());
        unregisteredCompanyVO.setLegalRepresent(this.getLegalPersonName());
        unregisteredCompanyVO.setOfficeTelephone(this.getPhoneNumber());
        unregisteredCompanyVO.setCountryRegion(this.getCountry());
        unregisteredCompanyVO.setRegisterAddress(this.getRegLocation());
        unregisteredCompanyVO.setProvinceName(this.getProvince());
        unregisteredCompanyVO.setCityName(this.getCity());
        String regCapital = StringUtils.isBlank(this.getRegCapital()) ? "" : this.getRegCapital();
        String regCapitalCurrency = StringUtils.isBlank(this.getRegCapitalCurrency()) ? "" : this.getRegCapitalCurrency();
        unregisteredCompanyVO.setRegisterCapital(regCapital + regCapitalCurrency);
        //成立日期是时间戳需要转换成UTC 时间
        unregisteredCompanyVO.setBuildDate(null==this.getEstablishTime()?null:new Date(this.getEstablishTime()));
        unregisteredCompanyVO.setInsuranceNum(StringUtils.isBlank(this.getInsuredCount()) ? null : Long.valueOf(this.getInsuredCount()));
        String actualCapital = StringUtils.isBlank(this.getActualCapital()) ? "" : this.getActualCapital();
        String actualCapitalCurrency = StringUtils.isBlank(this.getActualCapitalCurrency()) ? "" : this.getActualCapitalCurrency();
        unregisteredCompanyVO.setTrueRegisterCapital(actualCapital + actualCapitalCurrency);
        unregisteredCompanyVO.setOfficeTelephone(this.getPhoneNumber());
        List<String> orgHistoryNames = this.getOrgHistoryNames();
        if (CollectionUtils.isEmpty(orgHistoryNames)) {
            unregisteredCompanyVO.setOldName("");
        } else {
            unregisteredCompanyVO.setOldName(this.getOrgHistoryNames().stream().collect(Collectors.joining(",")));
        }
        return unregisteredCompanyVO;
    }

}
