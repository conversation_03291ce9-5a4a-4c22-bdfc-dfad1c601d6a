package com.zte.mcrm.common.business.service;

import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.security.AESOperator;
import com.zte.mcrm.channel.util.ExceptionMsgUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class SensitiveEncryptor {


    @Value("${mcrm.common.sensitive.secretKeySixteen}")
    private String secretKeySixteen;

    @Value("${mcrm.common.sensitive.iv}")
    private String iv;


    public static String secretKeySixteenStatic;

    public static String ivStatic;

    @PostConstruct
    public void init() {
        secretKeySixteenStatic = secretKeySixteen;
        ivStatic = iv;
    }

    private static SensitiveEncryptor instance = null;

    private final static Logger logger = LoggerFactory.getLogger(SensitiveEncryptor.class);


    public static synchronized SensitiveEncryptor getInstance() {
        if (instance == null) {
            instance = new SensitiveEncryptor();
        }

        return instance;
    }

    public static synchronized SensitiveEncryptor getInstance(String secretKeySixteen, String ivParameter) {
        if (instance == null) {
            instance = new SensitiveEncryptor();
        }

        return instance;
    }

    /**
     * 解密
     *
     * @param value
     * @return
     */
    public String decryptSensitive(String value) {
        return decryptSensitive(value,secretKeySixteenStatic, ivStatic);
    }

    /**
     * 加密
     *
     * @param value
     * @return
     */
    public String encryptSensitive(String value) {
        return encryptSensitive(value, secretKeySixteenStatic, ivStatic);
    }

    /**
     * 解密
     *
     * @param value
     * @param secretKeySixteen
     * @param vector
     * @return
     */
    public String decryptSensitive(String value, String secretKeySixteen, String vector) {
        String returnStr = value;
        try {
            returnStr = AESOperator.getInstance().decrypt(value, secretKeySixteen, vector);
        }catch (Exception e){
            logger.error("decryptSensitive解密失败：{}", ExceptionMsgUtils.getStackTrace(e, 2000));
        }
        return returnStr;
    }

    /**
     * 加密
     *
     * @param value
     * @param secretKeySixteen
     * @param vector
     * @return
     */
    public String encryptSensitive(String value, String secretKeySixteen, String vector) {
        String returnStr = value;
        try {
            returnStr = AESOperator.getInstance().encrypt(value, secretKeySixteen, vector);
        }catch (Exception e){
            logger.error("encryptSensitive加密失败：{}", ExceptionMsgUtils.getStackTrace(e, 2000));
            throw new BusiException(RetCode.BUSINESSERROR_CODE, "encryptSensitive加密失败");
        }
        return returnStr;
    }

}
