package com.zte.mcrm.common.business.service;

import com.zte.mcrm.common.consts.ComFileInfoConsts;
import com.zte.mcrm.common.util.HmacSha256Util;
import com.zte.mcrm.common.util.UploadUtils;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * @Author: 10245190 DengZiqiao
 * @Date: 2021年9月14日 16:01:58
 * @Version: V1.0
 */
@Service
public class ComFileInfoServiceImpl implements IComFileInfoService {

    @Value("${fileUpload.appCode}")
    private String appCode;

    @Value("${fileUpload.secretKey}")
    private String secretKey;

    @Value("${fileUpload.idriveUrl}")
    private String idriveUrl;

    @Value("${spring.profiles.active}")
    private String springProfilesActive;

    /**
     * 获取附件上传下载地址
     * @param params
     *  xEmpNo        工号
     *  xLangId       语言
     *  businessId    业务ID
     *  businessType  业务类型
     *  operationFlag 操作标识（1：可以上传下载，0：只读）
     *  cipherGrade   是否加密（-99：不加密，2：可编辑，1：只读）
     * @return 附件上传下载地址
     * @throws Exception
     */
    @Override
    public String getUploadUrl(Map<String, String> params) throws Exception {
        String xEmpNo = params.get("xEmpNo");
        String xLangId = params.get("xLangId");
        String businessId = params.get("businessId");
        String businessType = params.get("businessType");
        String operationFlag = params.get("operationFlag");
        String cipherGrade = params.get("cipherGrade");
        StringBuilder urlBuilder = new StringBuilder();
        urlBuilder.append(idriveUrl);
        urlBuilder.append(ComFileInfoConsts.APP_CODE);
        urlBuilder.append(appCode);
        urlBuilder.append(ComFileInfoConsts.PARAM_A);
        // 生成文档有请求参数a的值
        StringBuilder aLink = new StringBuilder();
        aLink.append(ComFileInfoConsts.LANGUAGE_ID);
        aLink.append(UploadUtils.convertLangId(xLangId));
        aLink.append(ComFileInfoConsts.USER_ID);
        aLink.append(xEmpNo);
        aLink.append(ComFileInfoConsts.OPER);
        aLink.append(operationFlag);
        aLink.append(ComFileInfoConsts.GROUP_KEY);
        aLink.append(UploadUtils.generateGroupKey(businessId, businessType, springProfilesActive));
        aLink.append(ComFileInfoConsts.CIPHER_GRADE);
        aLink.append(cipherGrade);
        // 加密
        String a = Base64.encodeBase64String(aLink.toString().getBytes(StandardCharsets.UTF_8));
        String s = HmacSha256Util.generateHmacSha256(aLink.toString(), secretKey);
        urlBuilder.append(a);
        urlBuilder.append(ComFileInfoConsts.PARAM_S);
        urlBuilder.append(s);
        urlBuilder.append(ComFileInfoConsts.PARAM_E);
        return urlBuilder.toString();
    }
}
