package com.zte.mcrm.channel.service.channel;

import com.zte.mcrm.adapter.approval.model.dto.ApprovalProgressDTO;
import com.zte.mcrm.adapter.approval.model.dto.ApprovalTask;
import com.zte.mcrm.adapter.approval.model.dto.FlowBusiRefreshParamDTO;
import com.zte.mcrm.adapter.approval.service.ApprovalFlowService;
import com.zte.mcrm.channel.constant.ApprovalNodeStatusEnum;
import com.zte.mcrm.channel.constant.ApprovalStatusEnum;
import com.zte.mcrm.channel.constant.OpportunityApprovalNodeEnum;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.channel.util.ExceptionMsgUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zte.mcrm.channel.constant.OpportunityConstant.NEW_OPPORTUNITY_APP_CODE;
import static com.zte.mcrm.channel.constant.OpportunityConstant.NEW_OPPORTUNITY_FLOW_CODE;

@Slf4j
@Service
/**
 * 审批信息服务类
 */
public class ApprovalInfoServiceImpl implements ApprovalInfoService {


    @Autowired
    private ApprovalFlowService approvalFlowService;

    /**
     * 审批中心批量查询最大量
     */
    private static final int FLOW_INSTANCE_BATCH_SIZE = 100;


    /**
     * 查询审批中心获取审批通过时间
     *
     * @param flowInstances
     * @return
     */
    @Override
    public Map<String, Date> getApproveDateMap(List<String> flowInstances, String type) {

        Map<String, Date> successDateMap = new HashMap<>();
        if (CollectionUtils.isEmpty(flowInstances)) {
            return successDateMap;
        }
        for (int i = 0; i < flowInstances.size(); i += FLOW_INSTANCE_BATCH_SIZE) {
            try {
                List<String> flowInstancesBatch = flowInstances.subList(i, Math.min(i + FLOW_INSTANCE_BATCH_SIZE, flowInstances.size()));
                Map<String, ApprovalProgressDTO> approvalProgressDTOMap = approvalFlowService.getFlowProgressByFlowInstanceIds(flowInstancesBatch);
                processDate(approvalProgressDTOMap, successDateMap, type);
            } catch (Exception e) {
                log.error("查询审批节点审批完成时间出错，Exception：{}", ExceptionMsgUtils.getStackTrace(e, 2000));
            }
        }
        return successDateMap;
    }

    private void processDate(Map<String, ApprovalProgressDTO> approvalProgressDTOMap, Map<String, Date> successDateMap, String type) {
        Date resultDate;
        for (Map.Entry<String, ApprovalProgressDTO> entry : approvalProgressDTOMap.entrySet()) {
            String key = entry.getKey();
            ApprovalProgressDTO approvalProgress = entry.getValue();
            resultDate = null;
            if (ApprovalNodeStatusEnum.COMPLETED.getDescEn().equals(approvalProgress.getStatus())
                    || ApprovalStatusEnum.COMPLETE.getDescEn().equals(approvalProgress.getStatus())) {
                List<ApprovalTask> approvalTasks = approvalProgress.getApprovalTaskList();
                if (OpportunityConstant.SUBMIT_DATE.equals(type)) {
                    resultDate = getSubmitDate(approvalTasks);
                } else if (OpportunityConstant.SUCCESS_DATE.equals(type)) {
                    resultDate = getApprovedDate(approvalTasks);
                }
            }
            if (null != resultDate) {
                successDateMap.put(key, resultDate);
            }
        }
    }

    private Date getSubmitDate (List<ApprovalTask> approvalTasks){
        List<Date> submitDateList = approvalTasks.stream()
                .filter(approvalTask -> OpportunityApprovalNodeEnum.APPROVAL_NODE.getNodeName().equals(approvalTask.getNodeName()) && null != approvalTask.getApprovalDate())
                .map(ApprovalTask::getCreatedDate).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(submitDateList)) {
            return submitDateList.get(0);
        }
        return null;
    }

    private Date getApprovedDate(List<ApprovalTask> approvalTasks) {
        List<Date> approvedDateList = approvalTasks.stream()
                .map(ApprovalTask::getApprovalDate)
                .filter(Objects::nonNull).sorted(Comparator.reverseOrder()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(approvedDateList)) {
            return approvedDateList.get(0);
        }
        return null;
    }


    @Override
    public Map<String, List<String>> getApproverListMap(List<String> flowInstances) {
        Map<String, List<String>> approverListMap = new HashMap<>();
        if (CollectionUtils.isEmpty(flowInstances)) {
            return approverListMap;
        }
        for (int i = 0; i < flowInstances.size(); i += FLOW_INSTANCE_BATCH_SIZE) {
            try {
                List<String> flowInstancesBatch = flowInstances.subList(i, Math.min(i + FLOW_INSTANCE_BATCH_SIZE, flowInstances.size()));
                Map<String, ApprovalProgressDTO> approvalProgressDTOMap = approvalFlowService.getFlowProgressByFlowInstanceIds(flowInstancesBatch);
                processApproverList(approvalProgressDTOMap, approverListMap);
            } catch (Exception e) {
                log.error("查询审批中心审批人出错，Exception：{}", ExceptionMsgUtils.getStackTrace(e, 2000));
            }
        }
        return approverListMap;
    }

    @Override
    public Boolean refreshMoaParams(String flowInstanceId, HashMap<String, Object> parameterMap) {
        FlowBusiRefreshParamDTO flowBusiRefreshParamDTO = new FlowBusiRefreshParamDTO();
        flowBusiRefreshParamDTO.setAppCode(NEW_OPPORTUNITY_APP_CODE);
        flowBusiRefreshParamDTO.setFlowCode(NEW_OPPORTUNITY_FLOW_CODE);
        flowBusiRefreshParamDTO.setFlowInstanceId(flowInstanceId);
        flowBusiRefreshParamDTO.setParameterMap(parameterMap);
        approvalFlowService.refreshMoaParameters(flowBusiRefreshParamDTO);
        return Boolean.TRUE;
    }

    private void processApproverList(Map<String, ApprovalProgressDTO> approvalProgressDTOMap, Map<String, List<String>> approverListMap) {
        for (Map.Entry<String, ApprovalProgressDTO> entry : approvalProgressDTOMap.entrySet()) {
            String key = entry.getKey();
            ApprovalProgressDTO approvalProgress = entry.getValue();
            List<ApprovalTask> approvalTasks = approvalProgress.getApprovalTaskList();
            List<String> approverList = approvalTasks.stream()
                    .map(ApprovalTask::getApprover).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(approverList)) {
                approverListMap.put(key, approverList);
            }
        }
    }
}
