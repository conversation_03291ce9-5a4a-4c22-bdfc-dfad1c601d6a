package com.zte.mcrm.channel.service.channel;

import cn.hutool.core.date.DateUtil;
import com.google.common.base.MoreObjects;
import com.google.common.collect.Lists;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.adapter.model.dto.AccountInfo;
import com.zte.mcrm.adapter.model.dto.CompanyInfoDTO;
import com.zte.mcrm.adapter.model.dto.CustomerCreateInfoDTO;
import com.zte.mcrm.adapter.model.vo.IndustryMappingBetweenLineAndSs;
import com.zte.mcrm.adapter.service.CustomerInfoService;
import com.zte.mcrm.channel.constant.OpportunityErrorEnum;
import com.zte.mcrm.channel.model.dto.CustomerInfoDTO;
import com.zte.mcrm.channel.model.entity.OpportunityDetail;
import com.zte.mcrm.channel.model.entity.OpptyCustomerCreateRecord;
import com.zte.mcrm.channel.model.mapper.OpportunityInfoVOMapper;
import com.zte.mcrm.channel.service.support.CustomerCreateSupport;
import com.zte.mcrm.common.framework.exception.ErrorCode;
import com.zte.mcrm.common.framework.exception.ErrorCodeException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

import static com.zte.mcrm.channel.constant.OpportunityConstant.APPROVING;

/**
 * 创建客户
 * <AUTHOR>
 * @date 2023-05-16
 */
@Component
public class OpportunityCreateCustomerServiceImpl implements OpportunityCreateCustomerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OpportunityCreateCustomerServiceImpl.class);

    @Autowired
    private CustomerInfoService customerInfoService;

    @Autowired
    private CustomerCreateSupport customerCreateSupport;

    /**
     * 创建客户草稿
     * @param customerInfoDTO
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public RetCode createCustomer(CustomerInfoDTO customerInfoDTO) {
        Map<String, AccountInfo> accountInfoMap = customerInfoService.getAccountInfoByBatch(Lists.newArrayList(customerInfoDTO.getLastAccName()));
        if (accountInfoMap.containsKey(customerInfoDTO.getLastAccName())) {
            AccountInfo accountInfo = accountInfoMap.get(customerInfoDTO.getLastAccName());
            OpportunityErrorEnum accountCreateStatusResult = customerCreateSupport.getAccountCreateStatusResult(accountInfo);
            if (OpportunityErrorEnum.SG3011.equals(accountCreateStatusResult) || OpportunityErrorEnum.SG2011.equals(accountCreateStatusResult)) {
                return new RetCode(OpportunityErrorEnum.SG3011.code(), OpportunityErrorEnum.SG3011.msg());
            }
            if (OpportunityErrorEnum.SG2031.equals(accountCreateStatusResult)) {
                return new RetCode(OpportunityErrorEnum.SG2061.code(), String.format(OpportunityErrorEnum.SG2061.msg(), customerInfoDTO.getLastAccName()));
            }
        }
        //其他情况，导入客户
        return importCustomerInternal(customerInfoDTO);
    }

    /**
     * 创建客户
     * @param customerInfoDTO
     */
    private RetCode importCustomerInternal(CustomerInfoDTO customerInfoDTO) {
        createCustomerAccordingToCustomerInfo(customerInfoDTO);
        OpptyCustomerCreateRecord opptyCustomerCreateRecord = customerCreateSupport.saveCustomerCreateRecord(customerInfoDTO, APPROVING);
        customerCreateSupport.saveOrgAttachments(opptyCustomerCreateRecord.getRowId(), customerInfoDTO.getOrgAttachments());
        return new RetCode(OpportunityErrorEnum.SG2061.code(), String.format(OpportunityErrorEnum.SG2061.msg(), customerInfoDTO.getLastAccName()));
    }




    /**
     * 在客户系统SS创建客户
     * @param customerInfoDTO
     * @throws BusiException
     */
    private void createCustomerAccordingToCustomerInfo(CustomerInfoDTO customerInfoDTO) throws BusiException {
        try {
            IndustryMappingBetweenLineAndSs industryMappingBetweenLineAndSs = customerCreateSupport.getIndustryMappingBetweenLineAndSs(customerInfoDTO);
            CompanyInfoDTO companyInfo = customerCreateSupport.queryCompanyInfo(customerInfoDTO);
            CustomerCreateInfoDTO customerCreateInfoDTO = new CustomerCreateInfoDTO();
            customerCreateInfoDTO.setOperator(customerInfoDTO.getBusinessManagerId());
            customerCreateInfoDTO.setAccountType(industryMappingBetweenLineAndSs.getParentIndustryFromSs());
            customerCreateInfoDTO.setAccountSubType(industryMappingBetweenLineAndSs.getSubIndustryFromSs());
            customerCreateInfoDTO.setDeptNo(customerInfoDTO.getDeptNo());
            customerCreateInfoDTO.setAccountName(customerInfoDTO.getLastAccName());
            customerCreateInfoDTO.setLocal(companyInfo.getRegLocation());
            customerCreateInfoDTO.setMainPhone(MoreObjects.firstNonNull(customerInfoDTO.getFinalCustomerContactPhone(), companyInfo.getPhoneNumber()));
            customerCreateInfoDTO.setCity(customerCreateSupport.getCityCode(companyInfo.getCity()));
            customerCreateInfoDTO.setCreated(DateUtil.now());
            customerCreateInfoDTO.setChannelName(customerInfoDTO.getCustomerName());
            customerCreateInfoDTO.setRegisterAddress(companyInfo.getRegLocation());
            customerInfoService.createFinalCustomer(customerCreateInfoDTO);
        } catch (ErrorCodeException errorCodeException) {
            throw errorCodeException;
        } catch (Exception e) {
            LOGGER.error("createCustomer:{} error: {}", customerInfoDTO.getLastAccName(), e.getMessage(), e);
            throw new ErrorCodeException(ErrorCode.UniversalErrorEnum.OU3153);
        }
    }

    public RetCode getCustomerStatusCodeAndSaveCustomerCreateRecord(OpportunityDetail opportunityDetail) {
        Map<String, AccountInfo> accountInfoByPriorityMap = customerInfoService.getAccountInfoByPriorityMap(Lists.newArrayList(opportunityDetail.getLastAccName()));

        if (!accountInfoByPriorityMap.containsKey(opportunityDetail.getLastAccName())) {
            return new RetCode(OpportunityErrorEnum.SG2051.code(), String.format(OpportunityErrorEnum.SG2051.msg(), opportunityDetail.getLastAccName()));
        }
        AccountInfo accountInfo = accountInfoByPriorityMap.get(opportunityDetail.getLastAccName());
        // 获取客户状态返回码
        OpportunityErrorEnum accountCreateStatusResult = customerCreateSupport.getAccountCreateStatusResult(accountInfo);
        if (OpportunityErrorEnum.SG3011.equals(accountCreateStatusResult)) {
            opportunityDetail.setLastAccId(accountInfo.getAccountNum());
            opportunityDetail.setLastAccName(accountInfo.getAccountName());
            return new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID);
        }
        if (OpportunityErrorEnum.SG2011.equals(accountCreateStatusResult)) {
            opportunityDetail.setLastAccId(accountInfo.getMainAcctNum());
            opportunityDetail.setLastAccName(accountInfo.getMainAcctName());
            return new RetCode(OpportunityErrorEnum.SG2011.code(), String.format(OpportunityErrorEnum.SG2011.msg(), opportunityDetail.getLastAccName(), accountInfo.getMainAcctName()));
        }
        if (OpportunityErrorEnum.SG2031.equals(accountCreateStatusResult)) {
            customerCreateSupport.saveCustomerCreateRecord(opportunityDetail);
            return new RetCode(OpportunityErrorEnum.SG2031.code(), String.format(OpportunityErrorEnum.SG2031.msg(), opportunityDetail.getLastAccName()));
        }
        return new RetCode(OpportunityErrorEnum.SG2051.code(), String.format(OpportunityErrorEnum.SG2051.msg(), opportunityDetail.getLastAccName()));
    }

}
