<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.aiagent.infrastruction.access.mapper.SysConfValueMapper">
    <!-- 基础结果集映射，定义表字段与PO类属性的映射关系 -->
    <resultMap id="BaseResultMap" type="com.zte.aiagent.infrastruction.access.po.SysConfValuePO">
        <id column="row_id" property="rowId" jdbcType="VARCHAR"/>
        <result column="code_type_id" property="codeTypeId" jdbcType="VARCHAR"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="value_cn" property="valueCn" jdbcType="VARCHAR"/>
        <result column="value_en" property="valueEn" jdbcType="VARCHAR"/>
        <result column="order_value" property="orderValue" jdbcType="INTEGER"/>
        <result column="parent_value_code" property="parentValueCode" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="VARCHAR"/>
        <result column="data_level" property="dataLevel" jdbcType="INTEGER"/>
        <result column="memo" property="memo" jdbcType="VARCHAR"/>
        <result column="last_updated_date" property="lastUpdatedDate" jdbcType="TIMESTAMP"/>
        <result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR"/>
        <result column="created_date" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="enabled_flag" property="enabledFlag" jdbcType="VARCHAR"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="expand" property="expand" jdbcType="OTHER"/>
    </resultMap>

    <!-- 插入系统参数配置值记录 -->
    <insert id="insert" parameterType="com.zte.aiagent.infrastruction.access.po.SysConfValuePO">
        INSERT INTO sys_conf_value (
            row_id,
            code_type_id,
            code,
            value_cn,
            value_en,
            order_value,
            parent_value_code,
            parent_id,
            data_level,
            memo,
            last_updated_date,
            last_updated_by,
            created_date,
            created_by,
            enabled_flag,
            tenant_id,
            status,
            expand
        ) VALUES (
                     #{rowId,jdbcType=VARCHAR},
                     #{codeTypeId,jdbcType=VARCHAR},
                     #{code,jdbcType=VARCHAR},
                     #{valueCn,jdbcType=VARCHAR},
                     #{valueEn,jdbcType=VARCHAR},
                     #{orderValue,jdbcType=INTEGER},
                     #{parentValueCode,jdbcType=VARCHAR},
                     #{parentId,jdbcType=VARCHAR},
                     #{dataLevel,jdbcType=INTEGER},
                     #{memo,jdbcType=VARCHAR},
                     #{lastUpdatedDate,jdbcType=TIMESTAMP},
                     #{lastUpdatedBy,jdbcType=VARCHAR},
                     #{createdDate,jdbcType=TIMESTAMP},
                     #{createdBy,jdbcType=VARCHAR},
                     #{enabledFlag,jdbcType=VARCHAR},
                     #{tenantId,jdbcType=BIGINT},
                     #{status,jdbcType=VARCHAR},
                     #{expand,jdbcType=VARCHAR}
                 )
    </insert>

    <!-- 批量插入系统参数配置值记录 -->
    <insert id="batchInsert" parameterType="java.util.Map">
        INSERT INTO sys_conf_value (
        row_id,
        code_type_id,
        code,
        value_cn,
        value_en,
        order_value,
        parent_value_code,
        parent_id,
        data_level,
        memo,
        last_updated_date,
        last_updated_by,
        created_date,
        created_by,
        enabled_flag,
        tenant_id,
        status,
        expand
        ) VALUES
        <foreach collection="items" item="item" separator=",">
            (
            #{item.rowId,jdbcType=VARCHAR},
            #{item.codeTypeId,jdbcType=VARCHAR},
            #{item.code,jdbcType=VARCHAR},
            #{item.valueCn,jdbcType=VARCHAR},
            #{item.valueEn,jdbcType=VARCHAR},
            #{item.orderValue,jdbcType=INTEGER},
            #{item.parentValueCode,jdbcType=VARCHAR},
            #{item.parentId,jdbcType=VARCHAR},
            #{item.dataLevel,jdbcType=INTEGER},
            #{item.memo,jdbcType=VARCHAR},
            #{item.lastUpdatedDate,jdbcType=TIMESTAMP},
            #{item.lastUpdatedBy,jdbcType=VARCHAR},
            #{item.createdDate,jdbcType=TIMESTAMP},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.enabledFlag,jdbcType=VARCHAR},
            #{item.tenantId,jdbcType=BIGINT},
            #{item.status,jdbcType=VARCHAR},
            #{item.expand,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- 根据ID查询系统参数配置值 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
            row_id,
            code_type_id,
            code,
            value_cn,
            value_en,
            order_value,
            parent_value_code,
            parent_id,
            data_level,
            memo,
            last_updated_date,
            last_updated_by,
            created_date,
            created_by,
            enabled_flag,
            tenant_id,
            status,
            expand
        FROM sys_conf_value
        WHERE row_id = #{rowId,jdbcType=VARCHAR}
    </select>

    <!-- 根据编码类型ID查询参数值列表 -->
    <select id="selectByCodeTypeId" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
            row_id,
            code_type_id,
            code,
            value_cn,
            value_en,
            order_value,
            parent_value_code,
            parent_id,
            data_level,
            memo,
            last_updated_date,
            last_updated_by,
            created_date,
            created_by,
            enabled_flag,
            tenant_id,
            status,
            expand
        FROM sys_conf_value
        WHERE code_type_id = #{codeTypeId,jdbcType=VARCHAR}
          AND tenant_id = #{tenantId,jdbcType=BIGINT}
          AND enabled_flag = 'Y'
          AND status = 'Y'
        ORDER BY order_value ASC, data_level ASC
    </select>

    <!-- 根据编码类型ID、代码和租户ID查询参数值 -->
    <select id="selectByTypeIdCodeAndTenant" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
            row_id,
            code_type_id,
            code,
            value_cn,
            value_en,
            order_value,
            parent_value_code,
            parent_id,
            data_level,
            memo,
            last_updated_date,
            last_updated_by,
            created_date,
            created_by,
            enabled_flag,
            tenant_id,
            status,
            expand
        FROM sys_conf_value
        WHERE code_type_id = #{codeTypeId,jdbcType=VARCHAR}
          AND code = #{code,jdbcType=VARCHAR}
          AND tenant_id = #{tenantId,jdbcType=BIGINT}
          AND enabled_flag = 'Y'
          AND status = 'Y'
    </select>

    <!-- 根据父值ID查询子参数列表 -->
    <select id="selectByParentId" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
            row_id,
            code_type_id,
            code,
            value_cn,
            value_en,
            order_value,
            parent_value_code,
            parent_id,
            data_level,
            memo,
            last_updated_date,
            last_updated_by,
            created_date,
            created_by,
            enabled_flag,
            tenant_id,
            status,
            expand
        FROM sys_conf_value
        WHERE parent_id = #{parentId,jdbcType=VARCHAR}
          AND tenant_id = #{tenantId,jdbcType=BIGINT}
          AND enabled_flag = 'Y'
          AND status = 'Y'
        ORDER BY order_value ASC
    </select>

    <!-- 根据ID更新系统参数配置值 -->
    <update id="updateByPrimaryKey" parameterType="com.zte.aiagent.infrastruction.access.po.SysConfValuePO">
        UPDATE sys_conf_value
        SET
            code_type_id = #{codeTypeId,jdbcType=VARCHAR},
            code = #{code,jdbcType=VARCHAR},
            value_cn = #{valueCn,jdbcType=VARCHAR},
            value_en = #{valueEn,jdbcType=VARCHAR},
            order_value = #{orderValue,jdbcType=INTEGER},
            parent_value_code = #{parentValueCode,jdbcType=VARCHAR},
            parent_id = #{parentId,jdbcType=VARCHAR},
            data_level = #{dataLevel,jdbcType=INTEGER},
            memo = #{memo,jdbcType=VARCHAR},
            last_updated_date = #{lastUpdatedDate,jdbcType=TIMESTAMP},
            last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            created_date = #{createdDate,jdbcType=TIMESTAMP},
            enabled_flag = #{enabledFlag,jdbcType=VARCHAR},
            tenant_id = #{tenantId,jdbcType=BIGINT},
            status = #{status,jdbcType=VARCHAR},
            expand = #{expand,jdbcType=VARCHAR}
        WHERE row_id = #{rowId,jdbcType=VARCHAR}
    </update>

    <!-- 逻辑删除系统参数配置值（将enabled_flag设为N） -->
    <update id="logicDelete" parameterType="java.util.Map">
        UPDATE sys_conf_value
        SET
            enabled_flag = 'N',
            last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            last_updated_date = #{lastUpdatedDate,jdbcType=TIMESTAMP}
        WHERE row_id = #{rowId,jdbcType=VARCHAR}
    </update>

    <!-- 批量逻辑删除系统参数配置值 -->
    <update id="batchLogicDelete" parameterType="java.util.Map">
        UPDATE sys_conf_value
        SET
        enabled_flag = 'N',
        last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
        last_updated_date = #{lastUpdatedDate,jdbcType=TIMESTAMP}
        WHERE row_id IN
        <foreach collection="rowIds" item="rowId" open="(" separator="," close=")">
            #{rowId,jdbcType=VARCHAR}
        </foreach>
    </update>
</mapper>
