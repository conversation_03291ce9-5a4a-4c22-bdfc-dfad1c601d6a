package com.zte.mcrm.channel.controller;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.clouddisk.util.ServiceDataUtil;
import com.zte.mcrm.channel.model.dto.OpportunityMsgNotifyDTO;
import com.zte.mcrm.channel.service.common.IMessageNotifyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "消息通知相关API")
@RestController
@RequestMapping("/msg/notify")
public class MessageNotifyController {

    @Resource
    IMessageNotifyService messageNotifyService;

    @ApiOperation("发送消息")
    @PostMapping(value = "/sendMessage")
    public ServiceData<Void> sendMessage(@RequestBody OpportunityMsgNotifyDTO opportunityMsgNotifyDTO){
        messageNotifyService.sendMessage(opportunityMsgNotifyDTO);
        return ServiceDataUtil.success(null);
    }
}
