package com.zte.mcrm.channel.util;

import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

@Slf4j
public class DateUtils {
    public static final String DATE_FORMAT_DAY = "yyyy-MM-dd";

    public static String getDate(Long dateL) {
        if (dateL == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(" yyyy-MM-dd hh:mm:ss");
       return  sdf.format(new Date(dateL));
    }


    public static String getDate(Date date, String pattern) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return  sdf.format(date);
    }

    /**
     * 字符串转日期
     * @param dateString
     * @param dateFormat
     * @return
     * @throws ParseException
     */
    public static Date parseDate(String dateString, String dateFormat) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
            return sdf.parse(dateString);
        } catch (Exception e) {
            log.error("DateUtils parseDate error: dateString = {}, dateFormat = {}, error: {}", dateString, dateFormat, e);
        }
        return null;
    }


}
