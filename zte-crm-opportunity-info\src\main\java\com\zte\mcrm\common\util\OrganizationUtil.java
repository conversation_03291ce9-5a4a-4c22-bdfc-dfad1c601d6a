package com.zte.mcrm.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zte.mcrm.common.model.PersonAndOrgInfoVO;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.common.model.HttpResultData;
import com.zte.springbootframe.common.serviceregister.MicroServiceWithConfigUtil;
import com.zte.springbootframe.util.string.StringHelper;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @Author: 10261899
 * @Date: 2019/12/3
 * @Description:
 */
@Slf4j
public class OrganizationUtil {

    /**
     * 根据组织编码获取组织信息
     * @param orgNo
     * @return
     * @throws BusiException
     */
    public static PersonAndOrgInfoVO getOrgByOrgNo(String orgNo) throws BusiException {

        JSONObject param=new JSONObject();

        param.put("orgNo",orgNo);

        HttpResultData httpResultData = MicroServiceWithConfigUtil.invokeServiceWithGetMethod("4", "/org/getOrgInfoById", param, RequestMessage.getHeader("pcOpportunity"));

        if(httpResultData!=null&&httpResultData.getBo()!=null){
            String result= JSONObject.toJSONString(httpResultData.getBo());
            return JSONObject.parseObject(result,PersonAndOrgInfoVO.class);
        }

        return null;

    }

    /**
     * 根据组织编码集合批量获取组织信息
     * @param orgNos
     * @return
     * @throws BusiException
     */
    public static List<PersonAndOrgInfoVO> getOrgsByOrgNos(Set<String> orgNos) throws BusiException {

        List<PersonAndOrgInfoVO> result=new ArrayList<>();
        if(StringHelper.isEmpty(orgNos)){
            return result;
        }
        HttpResultData httpResultData = MicroServiceWithConfigUtil.invokeServiceWithPostMethod("4", "/org/getOrgInfosByIds", orgNos, RequestMessage.getHeader("pcOpportunity"));
        log.error("OrganizationUtil.getOrgsByOrgNos orgNos: {}, httpResultData:{}", JSON.toJSONString(orgNos), JSON.toJSONString(httpResultData));
        if(httpResultData!=null&&httpResultData.getBo()!=null){
            String resultString= JSONObject.toJSONString(httpResultData.getBo());
            result= JSONArray.parseArray(resultString,PersonAndOrgInfoVO.class);
        }

        return result;

    }
}
