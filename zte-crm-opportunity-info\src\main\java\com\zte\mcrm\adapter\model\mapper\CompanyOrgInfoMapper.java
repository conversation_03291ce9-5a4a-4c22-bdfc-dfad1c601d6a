package com.zte.mcrm.adapter.model.mapper;

import com.zte.mcrm.adapter.model.dto.CompanyOrgInfoDTO;
import com.zte.mcrm.adapter.model.vo.CompanyOrgInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2021/9/16
 */
@Mapper
public interface CompanyOrgInfoMapper {
    CompanyOrgInfoMapper INSTANCE = Mappers.getMapper(CompanyOrgInfoMapper.class);

    CompanyOrgInfoVO transToCompanyOrgInfoVO(CompanyOrgInfoDTO companyOrgInfoDTO);
}
