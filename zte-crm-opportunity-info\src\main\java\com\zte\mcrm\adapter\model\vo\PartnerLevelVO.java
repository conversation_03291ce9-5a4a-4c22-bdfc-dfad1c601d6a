package com.zte.mcrm.adapter.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class PartnerLevelVO {

    @ApiModelProperty(value = "合作伙伴id")
    private String partnerId;

    @ApiModelProperty(value = "客户编码")
    private String crmCustomerCode;

    @ApiModelProperty(value = "渠道商id")
    private Long customerId;

    @ApiModelProperty(value = "渠道商名称")
    private String customerName;

    @ApiModelProperty(value = "认证信息")
    private String certificationInfos;
}
