package com.zte.mcrm.common.framework.interceptors;

import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.mcrm.common.consts.CommonConst;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;

/**
 * 检查请求头必填
 * <AUTHOR>
 * @date 2021/03/11
 */
@Component
public class CheckRequestHeaderInterceptor implements HandlerInterceptor{
    
    private final Logger logger = LoggerFactory.getLogger(CheckRequestHeaderInterceptor.class);
    
    /**
     * 
     * <AUTHOR>
     * @date 2021/03/11
     * @see HandlerInterceptor
     * #preHandle(javax.servlet.http.HttpServletRequest, javax.servlet.http.HttpServletResponse, java.lang.Object)
     */
    @Override
    public boolean preHandle(HttpServletRequest request,
        HttpServletResponse response, Object handler)
            throws Exception {
        String messsage = "HTTP Header `%s` is empty";
        String empNo = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
        if (StringUtils.isBlank(empNo)) {
            return handleUnionFail(response, String.format(messsage, SysGlobalConst.HTTP_HEADER_X_EMP_NO));
        }
        
        String xAuthValue = request.getHeader(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE);
        if (StringUtils.isBlank(xAuthValue)) {
            return handleUnionFail(response, String.format(messsage, SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE));
        }
        
        String language = request.getHeader(SysGlobalConst.HTTP_HEADER_X_LANG_ID);
        if (StringUtils.isBlank(language)) {
            return handleUnionFail(response, String.format(messsage, SysGlobalConst.HTTP_HEADER_X_LANG_ID));
        }
        
        String xTenantId = request.getHeader(SysGlobalConst.HTTP_HEADER_X_TENANT_ID);
        if (StringUtils.isBlank(xTenantId)) {
            return handleUnionFail(response, String.format(messsage, SysGlobalConst.HTTP_HEADER_X_TENANT_ID));
        }
        
        return true;
    }  
    
    @Override
    public void postHandle(
        HttpServletRequest request, 
        HttpServletResponse response, 
        Object handler,
        ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(
        HttpServletRequest request, 
        HttpServletResponse response, 
        Object handler, 
        Exception ex)
            throws Exception {

    }
   
    /**
     * 失败返回数据
     * <AUTHOR>
     * @date 2021/03/09
     * @param response HttpServletResponse
     * @param messsage 信息
     * @return Boolean
     * @throws IOException
     */
    private Boolean handleUnionFail(HttpServletResponse response,String messsage) throws IOException{
        OutputStream outputStream =null;
        try {
            Map<String,Object> other = new HashMap<>(16);
            other.put(CommonConst.STR_MSG, messsage);
            ServiceData<Boolean> sd = new ServiceData<>();
            RetCode code = new RetCode(RetCode.AUTHFAILED_CODE,RetCode.AUTHFAILED_MSGID);
            sd.setCode(code);
            sd.setBo(false);
            sd.setOther(other);

            // 直接返回失败信息
            String msg = JacksonJsonConverUtil.beanToJson(sd);
            logger.info("===请求头信息是空的：{}",msg);
            // 重新设置返回的消息类型和消息头,SPRING mvc设置为JSON类型,
            // 内容修改为加密字符串后,类型也要修改为text/html,防止angularjs自动根据类型转换数据
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json;charset=UTF-8");
            // 将加密数据写到原始的response对象中,返回客户端
            outputStream = response.getOutputStream();
            StreamUtils.copy(msg, Charset.forName("utf-8"), outputStream);
            return false;
        }catch(Exception e) {
            throw new BusiException();  
        }finally {
            if (outputStream!=null) {
                outputStream.close();
                outputStream=null;
            }
        }
    }

}
