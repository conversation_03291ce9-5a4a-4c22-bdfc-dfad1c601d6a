package com.zte.mcrm.adapter.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.adapter.constant.AdapterConstant;
import com.zte.mcrm.adapter.constant.CpcPropertiesConstant;
import com.zte.mcrm.adapter.constant.HeaderNameConst;
import com.zte.mcrm.adapter.constant.Sha256EncryptUtil;
import com.zte.mcrm.adapter.model.dto.*;
import com.zte.mcrm.adapter.model.vo.CompanySnapshotVO;
import com.zte.mcrm.adapter.model.vo.PartnerListVO;
import com.zte.mcrm.adapter.service.ICpcServiceApi;
import com.zte.mcrm.common.consts.CommonConstant;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.mcrm.common.util.RequestMessage;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.common.model.HttpResultData;
import com.zte.springbootframe.common.serviceregister.MicroServiceWithConfigUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 描述：合作伙伴API
 * 创建时间：2021/9/16
 *
 * @author：王丹凤**********
 */
@Service
@Slf4j
public class CpcServiceApiImpl implements ICpcServiceApi {

    /**
     * 法人模糊查询服务
     *
     * @param form
     * @return
     */
    @Override
    public  PageRows<CompanyInfoDTO> queryCompanyInfoList(FormData<CompanyInfoSearchDTO> form) throws BusiException, RouteException {
        PageRows<CompanyInfoDTO> pageRows = new PageRows<CompanyInfoDTO>();
        HttpResultData accountHttpResult = MicroServiceWithConfigUtil.invokeServiceWithPostMethod(
                AdapterConstant.MIROSERVICES_ELEVEN,
                CpcPropertiesConstant.THIRDPARTY_OBSCUREQUERYBYNAME_URL,
                form,
                getHeaderParams(CpcPropertiesConstant.ACCESSKEY, CpcPropertiesConstant.SECRETKEY));
        if(accountHttpResult!=null)
        {
            String rtnStr = JacksonJsonConverUtil.beanToJson(accountHttpResult.getBo());
            pageRows = JacksonJsonConverUtil.jsonToListBeanOther(rtnStr, new TypeReference<PageRows<CompanyInfoDTO>>() {});
        }
        return pageRows;
    }


    @Override
    public  CompanyOrgInfoDTO queryOrgInfoByKeyword(CompanyInfoSearchDTO companyInfoSearch) throws BusiException, RouteException {
        HttpResultData accountHttpResult = MicroServiceWithConfigUtil.invokeServiceWithPostMethod(
                AdapterConstant.MIROSERVICES_ELEVEN,
                "/org/queryOrgInfoByKeyword",
                companyInfoSearch,
                getHeaderParams(CpcPropertiesConstant.ACCESSKEY, CpcPropertiesConstant.SECRETKEY));
        if(accountHttpResult!=null)
        {
            String rtnStr = JacksonJsonConverUtil.beanToJson(accountHttpResult.getBo());
            return JacksonJsonConverUtil.jsonToListBeanOther(rtnStr, new TypeReference<CompanyOrgInfoDTO>() {});
        }
        return null;
    }

    @Override
    public CompanySnapshotVO querySnapshot(CompanyInfoSearchDTO companyInfoSearch) throws BusiException, RouteException {
        HttpResultData accountHttpResult = MicroServiceWithConfigUtil.invokeServiceWithPostMethod(
                AdapterConstant.MIROSERVICES_ELEVEN,
                "/org/querySnapshot",
                companyInfoSearch,
                getHeaderParams(CpcPropertiesConstant.ACCESSKEY, CpcPropertiesConstant.SECRETKEY));
        if(accountHttpResult!=null)
        {
            String rtnStr = JacksonJsonConverUtil.beanToJson(accountHttpResult.getBo());
            return JacksonJsonConverUtil.jsonToListBeanOther(rtnStr, new TypeReference<CompanySnapshotVO>() {});
        }
        return null;
    }

    /**
     * 法人基本信息查询服务
     *
     * @param partnerName
     * @return
     */
    @Override
    public CompanyInfoDTO searchCompanyInfo(String partnerName) throws BusiException, RouteException {
        CompanyInfoDTO bo = null;
        Map<String,String> param = new HashMap<>(2);
        param.put("keyword",partnerName);
        param.put("country","CN");
        HttpResultData accountHttpResult = MicroServiceWithConfigUtil.invokeServiceWithPostMethod(
                AdapterConstant.MIROSERVICES_ELEVEN,
                CpcPropertiesConstant.QUERY_ORGINFO_ORGUNIOBYKEYWORD_URL,
                param,
                getHeaderParams(CpcPropertiesConstant.ACCESSKEY, CpcPropertiesConstant.SECRETKEY));
        if(accountHttpResult!=null)
        {
            String rtnStr = JacksonJsonConverUtil.beanToJson(accountHttpResult.getBo());
            bo = JacksonJsonConverUtil.jsonToListBeanOther(rtnStr, new TypeReference<CompanyInfoDTO>() {});
        }
        return bo;
    }

    /**
     * 批量黑黄名单查询服务
     * @param entity
     * @return
     * @throws Exception
     */
    @Override
    public PageRows<BlackListDTO> getBlackListBatch(CompanyBatchInfoDTO entity) throws Exception {
        PageRows<BlackListDTO> pageRows = new PageRows<>();
        FormData<CompanyBatchInfoDTO> formData = new FormData<>();
        formData.setBo(entity);
        formData.setPage(CommonConstant.ONE);
        formData.setRows(CommonConstant.TWO_HUNDRED);
        HttpResultData accountHttpResult = MicroServiceWithConfigUtil.invokeServiceWithPostMethod(
                AdapterConstant.MIROSERVICES_ELEVEN,
                CpcPropertiesConstant.BLOCKLIST_QUERY_URL,
                formData,
                getHeaderParams(CpcPropertiesConstant.ACCESSKEY, CpcPropertiesConstant.SECRETKEY));
        if(accountHttpResult!=null)
        {
            String rtnStr = JacksonJsonConverUtil.beanToJson(accountHttpResult.getBo());
            pageRows = JacksonJsonConverUtil.jsonToListBeanOther(rtnStr, new TypeReference<PageRows<BlackListDTO>>() {});
        }
        return pageRows;
    }

    /**
     * 合作伙伴列表查询服务
     *
     * @param formData
     * @return
     */
    @Override
    public PageRows<PartnerListVO> partnerObscureQuery(FormData<PartnerObscureQueryDTO> formData) throws RouteException, BusiException {
        PageRows<PartnerListVO> pageRows = new PageRows<>();
        HttpResultData accountHttpResult = MicroServiceWithConfigUtil.invokeServiceWithPostMethod(
                AdapterConstant.MIROSERVICES_ELEVEN,
                CpcPropertiesConstant.CPC_PARTNER_OBSCUREQUERY_URL,
                formData, getHeaderParams(CpcPropertiesConstant.ACCESSKEY, CpcPropertiesConstant.SECRETKEY));
        if(accountHttpResult!=null)
        {
            String rtnStr = JacksonJsonConverUtil.beanToJson(accountHttpResult.getBo());
            pageRows = JacksonJsonConverUtil.jsonToListBeanOther(rtnStr, new TypeReference<PageRows<PartnerListVO>>() {});
        }
        return pageRows;
    }

    private Map<String, String> getHeaderParams(String accessKey, String secretKey){
        String uuid = UUID.randomUUID().toString();
        long timeStamp = System.currentTimeMillis();
        String content = "uuid=" + uuid
                + "&timestamp=" + timeStamp
                + "&accessKey=" + accessKey
                + "&secretKey=" + secretKey;
        String signature = Sha256EncryptUtil.sha256Hex(content);

        Map<String, String> headerParams = Maps.newHashMap();
        headerParams.put(HeaderNameConst.X_AUTH_NONCE, uuid);
        headerParams.put(HeaderNameConst.X_AUTH_TIMESTAMP, String.valueOf(timeStamp));
        headerParams.put(HeaderNameConst.X_AUTH_ACCESSKEY, accessKey);
        headerParams.put(HeaderNameConst.X_AUTH_SIGNATURE, signature);
        headerParams.put(HeaderNameConst.X_EMP_NO,CommonUtils.getEmpNo());
        String tenantId = CommonUtils.getTenantId();
        headerParams.put(HeaderNameConst.X_TENANT_ID, StringUtils.isNotBlank(tenantId)? tenantId : HeaderNameConst.DEFAULT_X_TENANT_ID);
        headerParams.put("Accept", "application/json;charset=UTF-8");
        return headerParams;
    }
}
