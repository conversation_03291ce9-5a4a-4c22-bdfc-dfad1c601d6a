/**
 * Copyright  2018 赵世光 All rights reserved.
 *
 * @author: **********
 * @date: 2018年4月8日 下午3:24:50
 */
package com.zte.mcrm.account.business.service;

import com.google.common.collect.Maps;
import com.zte.mcrm.account.access.vo.Account;
import com.zte.mcrm.account.access.vo.CreditVO;
import com.zte.mcrm.clues.common.CluesSysConst;
import com.zte.mcrm.common.util.RequestMessage;
import com.zte.mcrm.opportunity.common.OppSysConst;
import com.zte.springbootframe.common.model.HttpResultData;
import com.zte.springbootframe.common.serviceregister.MicroServiceWithConfigUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>Title: AccountServiceImpl</p>  
 * <p>Description: </p>  
 * <AUTHOR> ZhaoShiGuang
 * @date 2018年4月8日
 */
@Service
public class AccountServiceImpl implements AccountService {

    /** 日志对象*/
    private final Logger logger = LoggerFactory.getLogger(this.getClass());


    @Override
    public Account getAccount(String accountId) {
        Account account = new Account();
        if(StringUtils.isBlank(accountId)){
            return account;
        }
        try {
            Map<String, String> headerParamsMap = RequestMessage.getHeader("SIEBEL_ACCOUNT");
            Map<String, String> accountMap = new HashMap<>(16);
            // accountId
            accountMap.put(CluesSysConst.PARAM_ACCOUNTID, accountId);
            // /zte-crm-account-info/noPermisonAccount
            HttpResultData accountHttpResult = MicroServiceWithConfigUtil.invokeServiceWithGetMethod(CluesSysConst.MIROSERVICES_FIVE,
                    CluesSysConst.URL_NOPERMISONACCOUNT, accountMap, headerParamsMap);
            if(accountHttpResult != null){
                String accountString = JacksonJsonConverUtil.beanToJson(accountHttpResult.getBo());
                account = (Account) JacksonJsonConverUtil.jsonToBean(accountString, Account.class);
            }
        } catch (Exception e) {
            logger.error("捕获异常：" + e.getMessage());
        }
        return account;
    }


    @Override
    public CreditVO getAccountCredit(String accountId) {
        CreditVO credit = new CreditVO();
        if(StringUtils.isBlank(accountId)){
            return credit;
        }
        try {
            Map<String, String> creditMap = Maps.newHashMap();
            creditMap.put(CluesSysConst.PARAM_ACCOUNTID, accountId);

            Map<String, String> headerParamsMap = RequestMessage.getHeader(OppSysConst.SIEBEL);
            HttpResultData creditHttpResult = MicroServiceWithConfigUtil.invokeServiceWithGetMethod("5", "/account/credit",
                    creditMap, headerParamsMap);
            if (creditHttpResult != null) {

                String credits = JacksonJsonConverUtil.beanToJson(creditHttpResult.getBo());
                credit = (CreditVO) JacksonJsonConverUtil.jsonToBean(credits, CreditVO.class);
            }
        } catch (Exception e) {
            logger.error("获取客户信用评级信息失败！", e);
        }

        return credit;
    }



}
