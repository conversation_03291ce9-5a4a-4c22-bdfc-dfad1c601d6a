package com.zte.mcrm.adapter.approval.controller;

import com.zte.itp.msa.core.exception.ValidationException;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.approval.model.*;
import com.zte.mcrm.adapter.approval.model.dto.*;
import com.zte.mcrm.adapter.approval.service.ApprovalFlowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @description: 审批中心接口
 * @author: 10243305
 * @date: 2021/6/24 下午2:38
 */
@Api(value = "审批中心-审批流程相关api", tags = {"审批中心-审批流程相关api"})
@RestController
@RequestMapping("/approval")
public class ApprovalFlowController {
    @Autowired
    private ApprovalFlowService approvalFlowService;

    @ApiOperation("启动流程")
    @PostMapping(value = "/flow/start", produces = "application/json; charset=utf-8")
    public ServiceData<String> startFlow(@RequestBody ApprovalStartParamsDTO startParams) {
        ServiceData<String> ret = new ServiceData<>();
        ret.setBo(approvalFlowService.startFlow(startParams));
        return ret;
    }

    @ApiOperation("节点审批")
    @PostMapping(value = "/task/approve", produces = "application/json; charset=utf-8")
    public ServiceData<Void> approve(@RequestBody OpinionDTO opinion) {
        ServiceData<Void> ret = new ServiceData<>();
        approvalFlowService.approve(opinion);
        return ret;
    }

    @ApiOperation("转交审批")
    @PostMapping(value = "/task/reassign", produces = "application/json; charset=utf-8")
    public ServiceData<String> reassign(@RequestBody ReassignDTO opinion) {
        ServiceData<String> ret = new ServiceData<>();
        ret.setBo(approvalFlowService.reassignApproval(opinion));
        return ret;
    }

    @ApiOperation("重设流程参数")
    @PostMapping(value = "/flow/parameters/reset", produces = "application/json; charset=utf-8")
    public ServiceData<String> resetFlowParams(@RequestBody ApprovalResetStartParamsDTO params) {
        ServiceData<String> ret = new ServiceData<>();
        ret.setBo(approvalFlowService.resetFlowParams(params));
        return ret;
    }

    @ApiOperation("获取待审批节点信息")
    @PostMapping(value = "/task/query", produces = "application/json; charset=utf-8")
    public ServiceData<PageRows<ApprovingNodeInfo>> getApprovingNodes(@Validated @RequestBody ApprovingParameter approvingParameter,
                                                                      BindingResult error) throws Exception {
        if (error != null && error.hasErrors()) {
            throw new ValidationException(error);
        }
        ServiceData<PageRows<ApprovingNodeInfo>> ret = new ServiceData<>();
        ret.setBo(approvalFlowService.getApprovingNodes(approvingParameter));
        return ret;
    }

    @ApiOperation("获取流程进程")
    @PostMapping(value = "/flow/progress", produces = "application/json; charset=utf-8")
    public ServiceData<List<ApprovalProgressDTO>> getFlowProgress(@RequestBody FlowParameter flowParameter) {
        ServiceData<List<ApprovalProgressDTO>> ret = new ServiceData<>();
        ret.setBo(approvalFlowService.getFlowProgress(flowParameter));
        return ret;
    }

    @ApiOperation("注销流程")
    @PostMapping(value = "/flow/revoke", produces = "application/json; charset=utf-8")
    public ServiceData<String> revokeFlow(@RequestBody ApprovalRevokeParamsDTO revokeParams) {
        ServiceData<String> ret = new ServiceData<>();
        ret.setBo(approvalFlowService.revokeFlow(revokeParams));
        return ret;
    }


    @ApiOperation("审批流程回退到指定节点")
    @PostMapping(value = "/flow/rollback", produces = "application/json; charset=utf-8")
    public ServiceData<Void> rollbackFlow(@RequestBody ApprovalRollBackParamDTO rollBackParam) {
        ServiceData<Void> ret = new ServiceData<>();
        approvalFlowService.rollBack(rollBackParam);
        return ret;
    }

    @ApiOperation("审批催办")
    @PostMapping(value = "/task/urge", produces = "application/json; charset=utf-8")
    public ServiceData<String> urgeTask(@RequestBody FlowTaskParameter flowTaskParameter) {
        ServiceData<String> ret = new ServiceData<>();
        ret.setBo(approvalFlowService.urgeTask(flowTaskParameter));
        return ret;
    }

    @ApiOperation("审批流程中可回退的节点查询接口")
    @PostMapping(value = "/flow/rollback/task/list", produces = "application/json; charset=utf-8")
    public ServiceData<List<FlowTask>> getRollBackTaskList(@RequestBody FlowParameter flowParameter) {
        ServiceData<List<FlowTask>> ret = new ServiceData<>();
        ret.setBo(approvalFlowService.getRollBackNodeList(flowParameter));
        return ret;
    }

    @ApiOperation("流程节点信息")
    @GetMapping(value = "/flow/task/info/{flowCode}", produces = "application/json; charset=utf-8")
    public ServiceData<List<FlowTaskInfo>> getFlowTaskList(@PathVariable("flowCode") String flowCode) {
        ServiceData<List<FlowTaskInfo>> ret = new ServiceData<>();
        ret.setBo(approvalFlowService.getFlowTaskList(flowCode));
        return ret;
    }

    @ApiOperation("获取流程实例待审批节点信息")
    @PostMapping(value = "/flow/active/task", produces = "application/json; charset=utf-8")
    public ServiceData<PageRows<FlowActiveTaskInfo>> getApprovingTaskList(@RequestBody ApprovalActiveTaskParamsDTO param) {
        ServiceData<PageRows<FlowActiveTaskInfo>> ret = new ServiceData<>();
        ret.setBo(approvalFlowService.getApprovingTaskList(param));
        return ret;
    }

    @ApiOperation("批量获取审批进展")
    @PostMapping(value = "/batch/flow/progress", produces = "application/json; charset=utf-8")
    public ServiceData<Map<String, ApprovalProgressDTO>> getFlowProgressByFlowInstanceIds(@RequestBody List<String> flowInstanceIds) {
        ServiceData<Map<String, ApprovalProgressDTO>> ret = new ServiceData<>();
        ret.setBo(approvalFlowService.getFlowProgressByFlowInstanceIds(flowInstanceIds));
        return ret;
    }

    @ApiOperation("待我审批高级查询（支持业务过滤字段）")
    @PostMapping(value = "/task/advanced/query", produces = "application/json; charset=utf-8")
    public ServiceData<PageRows<ApprovingNodeInfo>> getApprovingTasksByCondition(@Validated @RequestBody ApprovingQueryParam approvingQueryParam,
                                                                                BindingResult error) throws Exception {
        if (error != null && error.hasErrors()) {
            throw new ValidationException(error);
        }

        ServiceData<PageRows<ApprovingNodeInfo>> ret = new ServiceData<>();
        ret.setBo(approvalFlowService.getApprovingTasksByCondition(approvingQueryParam));
        return ret;
    }

    @ApiOperation("我已审批高级查询（支持业务过滤字段）")
    @PostMapping(value = "/task/approved/advanced/query", produces = "application/json; charset=utf-8")
    public ServiceData<PageRows<ApprovedNodeInfo>> getApprovedTasksByCondition(@Validated @RequestBody ApprovingQueryParam approvingQueryParam,
                                                                                 BindingResult error) throws Exception {
        if (error != null && error.hasErrors()) {
            throw new ValidationException(error);
        }

        ServiceData<PageRows<ApprovedNodeInfo>> ret = new ServiceData<>();
        ret.setBo(approvalFlowService.getApprovedTasksByCondition(approvingQueryParam));
        return ret;
    }

    @ApiOperation("审批流程实例记录")
    @PostMapping(value = "/flow/approve/records", produces = "application/json; charset=utf-8")
    public ServiceData<List<ApprovalRecordsDTO>> getApprovalRecords(@RequestBody FlowParameter flowParameter) throws Exception {
        ServiceData<List<ApprovalRecordsDTO>> ret = new ServiceData<>();
        ret.setBo(approvalFlowService.getApprovalRecords(flowParameter));
        return ret;
    }

    @ApiOperation("审批结束后流程实例全景图")
    @GetMapping(value = "/flow/panorama/{flowInstanceId}", produces = "application/json; charset=utf-8")
    public ServiceData<List<FlowNodeApproverDTO>> getFlowInstanceaPnorama(@PathVariable("flowInstanceId") String flowInstanceId) throws Exception {
        ServiceData<List<FlowNodeApproverDTO>> ret = new ServiceData<>();
        ret.setBo(approvalFlowService.getFlowInstanceaPnorama(flowInstanceId));
        return ret;
    }
}
