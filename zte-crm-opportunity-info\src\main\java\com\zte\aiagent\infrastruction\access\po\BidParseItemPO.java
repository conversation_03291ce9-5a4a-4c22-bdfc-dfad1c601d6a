package com.zte.aiagent.infrastruction.access.po;

import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * 解析条目表PO类
 * 对应数据库表：bid_parse_item
 */
@Data
public class BidParseItemPO {
    /**
     * 主键ID
     */
    private String rowId;

    /**
     * 解析记录ID，关联bid_parse_record.row_id
     */
    private String parseRecordId;

    /**
     * 条目编码
     */
    private String itemCode;

    /**
     * 条目名称
     */
    private String itemName;

    /**
     * 条目值/内容
     */
    private String itemValue;

    /**
     * 原文内容
     */
    private String sourceText;

    /**
     * 数据层级: 1-解析项, 2-条目分类, 3-具体条目
     */
    private Integer dataLevel;

    /**
     * 父层级条目编码
     */
    private String parentItemCode;

    /**
     * 排序值(从配置模板中获取)
     */
    private Integer orderValue;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人
     */
    private String lastUpdatedBy;

    /**
     * 最后更新时间
     */
    private Date lastUpdatedDate;

    /**
     * 有效标记(Y/N)
     */
    private String enabledFlag;

    /**
     * 租户ID
     */
    private Long tenantId;
}
