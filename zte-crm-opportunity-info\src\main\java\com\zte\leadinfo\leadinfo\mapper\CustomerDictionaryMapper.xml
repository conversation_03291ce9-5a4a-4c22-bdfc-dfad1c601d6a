<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.leadinfo.leadinfo.mapper.CustomerDictionaryMapper">

    <resultMap id="BaseResultMap" type="com.zte.leadinfo.leadinfo.entity.CustomerDictionaryDO">
        <id property="inputParam" column="input_param" jdbcType="VARCHAR"/>
        <result property="customerId" column="customer_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        input_param,customer_id
    </sql>

</mapper>