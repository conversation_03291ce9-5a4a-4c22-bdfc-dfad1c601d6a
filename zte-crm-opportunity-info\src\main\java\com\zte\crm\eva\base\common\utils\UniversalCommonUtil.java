package com.zte.crm.eva.base.common.utils;

import com.google.common.collect.Maps;
import com.zte.crm.eva.base.common.constant.universal.UniversalUpdateConstant;
import com.zte.crm.eva.base.infrastructure.client.model.PrmQuickCodeValue;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class UniversalCommonUtil {

    private UniversalCommonUtil() {
        throw new IllegalStateException("Utility class");
    }
    /**
     * 组织调整系统参数配置转换集合
     *
     * @param prmQuickCodeValueVOList
     * @return
     */
    public static Map<String, Map<String, String>> convertTableInfo(List<PrmQuickCodeValue> prmQuickCodeValueVOList) {
        Map<String, Map<String, String>> tableInfos = Maps.newHashMap();
        prmQuickCodeValueVOList.forEach(prmQuickCodeValueVO -> {
            Map<String, String> tableInfo = Maps.newLinkedHashMap();
            tableInfo.put(UniversalUpdateConstant.DATABASE_NAME, prmQuickCodeValueVO.getCodeValueZh());
            tableInfo.put(UniversalUpdateConstant.TABLE_NAME, prmQuickCodeValueVO.getCodeValueEn());
            prmQuickCodeValueVO.getChildren().forEach(filed -> {
                String column = filed.getValueCode();
                String flag = filed.getCodeValueEn();
                tableInfo.put(column, flag);
            });
            tableInfos.put(prmQuickCodeValueVO.getValueCode(), tableInfo);
        });
        return tableInfos;
    }
}
