package com.zte.mcrm.channel.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
public class OperationButtonVO {

    @ApiModelProperty(value = "修改")
    private Boolean editFlag = Boolean.FALSE;

    @ApiModelProperty(value = "删除")
    private Boolean deleteFlag = Boolean.FALSE;

    @ApiModelProperty(value = "更新状态")
    private Boolean updateStatusFlag = Boolean.FALSE;

    @ApiModelProperty(value = "激活")
    private Boolean activationFlag = Boolean.FALSE;

    @ApiModelProperty(value = "撤销")
    private Boolean cancelFlag = Boolean.FALSE;

    @ApiModelProperty(value = "查看月报")
    private Boolean readReportFlag = Boolean.FALSE;

    @ApiModelProperty(value = "更新月报")
    private Boolean updateReportFlag = Boolean.FALSE;
}
