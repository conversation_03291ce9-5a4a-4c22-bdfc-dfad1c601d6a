package com.zte.mcrm.adapter.constant;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @date 2021/9/16
 */
@Component
public class CpcPropertiesConstant {
    @Autowired
    CpcSpringProperties cpcSpringProperties;
    public static String PARTNER_CATEGORY;
    public static String PARTNER_SMALL_CATEGORY;
    public static String ACCESSKEY;
    public static String SECRETKEY;
    public static String THIRDPARTY_OBSCUREQUERYBYNAME_URL;
    public static String QUERY_ORGINFO_ORGUNIOBYKEYWORD_URL;
    public static String BLOCKLIST_QUERY_URL;
    public static String CPC_PARTNER_OBSCUREQUERY_URL;

    @PostConstruct
    private void init(){
        PARTNER_CATEGORY = cpcSpringProperties.getPartnerCategory();
        PARTNER_SMALL_CATEGORY = cpcSpringProperties.getPartnerSmallCategory();
        ACCESSKEY = cpcSpringProperties.getAccessKey();
        SECRETKEY = cpcSpringProperties.getSecretKey();
        THIRDPARTY_OBSCUREQUERYBYNAME_URL = cpcSpringProperties.getThirdPartyObscureQueryByNameUrl();
        QUERY_ORGINFO_ORGUNIOBYKEYWORD_URL = cpcSpringProperties.getQueryOrgInfoByKeyWordUrl();
        BLOCKLIST_QUERY_URL = cpcSpringProperties.getBlockListQueryUrl();
        CPC_PARTNER_OBSCUREQUERY_URL = cpcSpringProperties.getPartnerObscureQueryUrl();
    }
}
