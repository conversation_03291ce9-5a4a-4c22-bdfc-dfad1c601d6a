<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zte.mcrm.channel.dao.OptyCdRecordDao" >
    <!-- 屏蔽mybatis的2级缓存  <cache />  -->

    <!-- 记录和实体映射配置 -->
    <resultMap id="BaseMap" type="com.zte.mcrm.channel.model.dto.OptyCdRecordDto" >
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="opty_cd" property="optyCd" jdbcType="VARCHAR" />
        <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
        <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
        <result column="enabled_flag" property="enabledFlag" jdbcType="CHAR" />
    </resultMap>

    <!-- 获取一条记录 -->
    <select id="get"  resultMap="BaseMap">
        select
        t.id ,
        t.opty_cd ,
        t.created_by ,
        t.created_date ,
        t.enabled_flag
        from opty_cd_record t
        where
        t.id=#{id,jdbcType=VARCHAR}
    </select>

    <!--添加一条记录 -->
    <insert id="insert" parameterType="com.zte.mcrm.channel.model.dto.OptyCdRecordDto" >
        <!--主键是自增字段时,放开注释代码
      <selectKey keyProperty="id" resultType="String"  order="AFTER">
          select LAST_INSERT_ID() as id
        </selectKey>
      -->
        insert into opty_cd_record
        (
        id ,
        opty_cd ,
        created_by ,
        created_date ,
        enabled_flag
        )
        values
        (
        #{id,jdbcType=VARCHAR} ,
        #{optyCd,jdbcType=VARCHAR} ,
        #{createdBy,jdbcType=VARCHAR} ,
        SYSDATE() ,
        'Y'
        )
    </insert>

    <insert id="insertByBatch" parameterType="com.zte.mcrm.channel.model.dto.OptyCdRecordDto" >
        insert into opty_cd_record
        (
        id ,
        opty_cd ,
        created_by ,
        created_date ,
        enabled_flag
        )
        values
        <foreach collection ="list" item="item" index= "index" separator =",">
        (
        #{id,jdbcType=VARCHAR} ,
        #{optyCd,jdbcType=VARCHAR} ,
        #{createdBy,jdbcType=VARCHAR} ,
        SYSDATE() ,
        'Y'
        )
        </foreach>
    </insert>

    <!-- 更新一条记录 -->
    <update id="update" parameterType="com.zte.mcrm.channel.model.dto.OptyCdRecordDto" >
        update opty_cd_record
        set
        <if test="optyCd != null">opty_cd=#{optyCd,jdbcType=VARCHAR} ,</if>
        <if test="createdBy != null">created_by=#{createdBy,jdbcType=VARCHAR} ,</if>
        <if test="createdDate != null">created_date=#{createdDate,jdbcType=TIMESTAMP} ,</if>
        <if test="enabledFlag != null">enabled_flag=#{enabledFlag,jdbcType=CHAR} </if>
        where
        id=#{id,jdbcType=VARCHAR}
    </update>

    <!-- 获取同类型的最近一个评审单号 -->
    <select id="getLatelyOptyCd" parameterType="java.lang.String" resultType="java.lang.String">
        select t.opty_cd
        from opty_cd_record t
        where locate(#{optyCd, jdbcType=VARCHAR}, t.opty_cd)
        order by t.opty_cd DESC
        limit 1
    </select>


    <!-- 获取同类型的最近一个评审单号 -->
    <select id="getLatelyOptyCdForUpdate" parameterType="java.lang.String" resultType="java.lang.String">
        select t.opty_cd
        from opty_cd_record t
        where locate(#{optyCd, jdbcType=VARCHAR}, t.opty_cd)
        order by t.opty_cd DESC
        limit 1
        for update
    </select>
</mapper>
