package com.zte.mcrm.channel.constant;

/**
 * <AUTHOR>
 */

public enum FrozenFlagEnum {
    /**
     * F:沉默
     **/
    SILENT_CUSTOMER("F"),
    /**
     * FA-沉默审批中
     **/
    SILENT_APPROVING_CUSTOMER("FA"),
    /**
     * FU-唤醒待审批
     **/
    WAKE_UP_APPROVING_CUSTOMER("FU");

    private String value;

    FrozenFlagEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    /**
     * 根据标记判断是否沉默客户
     * @param frozenFlag
     * @return
     */
    public static boolean silentCustomer(String frozenFlag) {
        return FrozenFlagEnum.SILENT_CUSTOMER.getValue().equals(frozenFlag) || FrozenFlagEnum.WAKE_UP_APPROVING_CUSTOMER.getValue().equals(frozenFlag);
    }

    public Boolean isMe(String frozenFlag) {
        return this.value.equalsIgnoreCase(frozenFlag);
    }
}
