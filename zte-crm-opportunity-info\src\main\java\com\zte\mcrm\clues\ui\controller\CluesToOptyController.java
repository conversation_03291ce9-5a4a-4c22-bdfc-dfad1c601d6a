package com.zte.mcrm.clues.ui.controller;

import com.zte.mcrm.clues.access.vo.BusinessClues;
import com.zte.mcrm.clues.business.service.BusinessCluesService;
import com.zte.mcrm.clues.business.service.PCBusinessCluesService;
import com.zte.mcrm.clues.common.CluesSysConst;
import com.zte.mcrm.clues.model.CluesToOptyDTO;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.opportunity.business.service.OpportunityDetailService;
import com.zte.mcrm.opportunity.common.OppSysConst;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.common.exception.ValidationException;
import com.zte.springbootframe.common.model.ServiceDataCopy;
import com.zte.springbootframe.common.model.ServiceDataUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.local.LocalMessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/****
 *
 * <AUTHOR> @date 2021/2/10
 **/

@RestController
@Api("线索相关API")
public class CluesToOptyController {

	@Autowired
	private BusinessCluesService businessCluesService;
	@Autowired
	private PCBusinessCluesService pcbusinessCluesService;
	@Autowired
	private OpportunityDetailService opportunityDetailService;
	@Autowired
	MessageSource messageSource;
	
	@ApiOperation("线索转商机")
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", name = "empNo", dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "00181547"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "10260293"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, dataType="String", dataTypeClass=String.class, required = true, value = "租户ID", defaultValue = "pcOpportunity"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, dataType="String", dataTypeClass=String.class, required = true, value = "语言", defaultValue = "zh") })
	@RequestMapping(value = "/clueTransfer", method = RequestMethod.POST)
	public ServiceDataCopy<String> getclueTransfer(
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, required = true) String empShorNo,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, required = true) String tenantId,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, required = true) String lang,
			@RequestParam(name = "empNo", required = true) String empNo,
			@Validated @RequestBody(required=true) BusinessClues businessClues,
			BindingResult bindingResult) throws Exception {
		if(bindingResult != null && bindingResult.hasErrors())
		{
			throw new ValidationException(bindingResult);
		}
		BusinessClues entity = new BusinessClues();
		String empId = empNo;
		entity.setxEmpNo(empNo);
		entity.setId(businessClues.getId());
		entity.setxLangId(lang);
		entity.setxTenantId(tenantId);
		entity.setEmpId(empId);
		boolean transferAuth = businessCluesService.checktransferAuthClue(entity);
		if(!transferAuth){
			// 无权限转商机 返回0003，无业务权限
			return ServiceDataUtil.noAuthInBusiness(null);
		}
		String resultMsg ="";
		// 保存
		if(CommonConst.PC_OPPORTUNITY.equalsIgnoreCase(tenantId)){
			businessClues.setStatusCode(CluesSysConst.STATUS_CODE_OPPTY);
			businessClues.setEmpId(empId);
            String optyId = businessCluesService.mobileClueToOpty(businessClues);
			if(StringUtils.isNotBlank(optyId)) {
				resultMsg = opportunityDetailService.creditCheck(optyId, OppSysConst.TRANSFER_OPPORTUNITY);
			}
		}
		return ServiceDataUtil.success(resultMsg);
	}
	@ApiOperation("线索分配")
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", name = "id", dataType="String", dataTypeClass=String.class, required = true, value = "线索ID", defaultValue = "1-3ARTTA"),
			@ApiImplicitParam(paramType = "query", name = "empNo", dataType="String", dataTypeClass=String.class, required = true, value = "添加员工工号", defaultValue = "00181547"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, dataType="String", dataTypeClass=String.class, required = true, value = "权限工号", defaultValue = "00181547"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, dataType="String", dataTypeClass=String.class, required = true, value = "租户ID", defaultValue = "pcOpportunity"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, dataType="String", dataTypeClass=String.class, required = true, value = "语言", defaultValue = "zh_CN") })
	@RequestMapping(value = "/clueAssigned", method = RequestMethod.POST)
	public ServiceDataCopy<?> getclueAssigned(
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, required = true) String empShorNo,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, required = true) String tenantId,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, required = true) String lang,
			@RequestParam(name = "empNo", required = true) String empNo,
			@RequestParam(name = "id", required = true) String id) throws BusiException {
		//查询实体
		String empId = empNo;
		BusinessClues entity = new BusinessClues();
        // 操作人
		entity.setxEmpNo(empShorNo);
        // 分配对象
		entity.setEmpNo(empNo);
		entity.setId(id);
		entity.setxLangId(lang);
		entity.setxTenantId(tenantId);
		entity.setEmpId(empId);
		entity.setOwnerMgr(empId);
		// 待客户经理更新
		entity.setStatusCode(CluesSysConst.STATUS_CODE_RENEWING);
		// 是否有权限分配
		boolean assignedAuth = businessCluesService.checkAssignedAuthClue(entity);
		if(!assignedAuth){
			// 无权限分配 返回0003，无业务权限
			return ServiceDataUtil.noAuthInBusiness(null);
		}
		// 保存
		businessCluesService.assignedSave(entity);
		return ServiceDataUtil.success(null);
	}
	
	@ApiOperation("PC-线索分配")
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", name = "clueNo", dataType="String", dataTypeClass=String.class, required = true, value = "线索编号"),
			@ApiImplicitParam(paramType = "query", name = "empId", dataType="String", dataTypeClass=String.class, required = true, value = "被分配人Id"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, dataType="String", dataTypeClass=String.class, required = true, value = "权限工号", defaultValue = "6011000145"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, dataType="String", dataTypeClass=String.class, required = true, value = "租户ID", defaultValue = "siebel"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, dataType="String", dataTypeClass=String.class, required = true, value = "语言", defaultValue = "zh") })
	@RequestMapping(value = "/pc/clueAssigned", method = RequestMethod.POST)
	public ServiceDataCopy<?> getclueAssignedPC(
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, required = true) String empShorNo,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, required = true) String tenantId,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, required = true) String lang,
			@RequestParam(name = "empId", required = true) String empId,
			@RequestParam(name = "clueNo", required = true) String clueNo) throws Exception {

		// 获取登录人empId
		String emptyId = empShorNo;
		BusinessClues entity = new BusinessClues();
        // 操作人
		entity.setxEmpNo(empShorNo);
		entity.setClueNum(clueNo);
		entity.setxLangId(lang);
		entity.setxTenantId(tenantId);
		entity.setEmpId(emptyId);
        // 待客户经理更新
		entity.setStatusCode(OppSysConst.RENEWING);
        // 归属客户经理
		entity.setOwnerMgr(empId);
		boolean assignedAuth = pcbusinessCluesService.checkAssignedAuthClue(entity);
		if(!assignedAuth){
			// 无权限分配 返回0003，无业务权限
			return ServiceDataUtil.noAuthInBusiness(null);
		}
		// 保存
		entity.setEmpId(emptyId);
		pcbusinessCluesService.assignedSavePC(entity);
		return ServiceDataUtil.success(null);
	}
	
	@ApiOperation("线索关闭")
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", name = "id", dataType="String", dataTypeClass=String.class, required = true, value = "线索ID", defaultValue = "1-3ARTTA"),
			@ApiImplicitParam(paramType = "query", name = "reasonId", dataType="String", dataTypeClass=String.class, required = true, value = "关闭原因", defaultValue = ""),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "00181547"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, dataType="String", dataTypeClass=String.class, required = true, value = "租户ID", defaultValue = "pcOpportunity"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, dataType="String", dataTypeClass=String.class, required = true, value = "语言", defaultValue = "zh_CN") })
	@RequestMapping(value = "/clueClose", method = RequestMethod.POST)
	public ServiceDataCopy<?> getclueClose(
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, required = true) String empShorNo,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, required = true) String tenantId,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, required = true) String lang,
			@RequestParam(name = "reasonId", required = true) String reasonId,
			@RequestParam(name = "id", required = true) String id) throws BusiException {
		// 查询实体
		String empId = empShorNo;
		BusinessClues entity = new BusinessClues();
		entity.setxEmpNo(empShorNo);
		entity.setId(id);
		entity.setxLangId(lang);
		entity.setxTenantId(tenantId);
		entity.setReasonId(reasonId);
		entity.setEmpId(empId);
		entity.setBackPerson(empId);
		entity.setStatusCode(CluesSysConst.STATUS_CODE_CLOSED);
		// 权限判断
		boolean closeAuth = businessCluesService.checkClosedAuthClue(entity);
		if(!closeAuth){
            //,返回0003，无业务权限
			return ServiceDataUtil.noAuthInBusiness(null);
		}
		// 关闭
		businessCluesService.closeSave(entity);
		return ServiceDataUtil.success(null);
	}
	
	
	@ApiOperation("线索退回保存")
	@ApiImplicitParams({
		@ApiImplicitParam(paramType = "query", name = "id", dataType="String", dataTypeClass=String.class, required = true, value = "线索ID", defaultValue = "1-3ARTTA"),
		@ApiImplicitParam(paramType = "query", name = "reasonId", dataType="String", dataTypeClass=String.class, required = true, value = "退回原因Id"),
		@ApiImplicitParam(paramType = "query", name = "empNo", dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "00181547"),
		@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "00181547"),
		@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, dataType="String", dataTypeClass=String.class, required = true, value = "租户ID", defaultValue = "pcOpportunity"),
		@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, dataType="String", dataTypeClass=String.class, required = true, value = "语言", defaultValue = "zh_CN") })
		@RequestMapping(value = "/clueBackSave", method = RequestMethod.POST)
	public ServiceDataCopy<?> clueBackSave(
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, required = true) String empShorNo,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, required = true) String tenantId,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, required = true) String lang,
			@RequestParam(name = "empNo", required = true) String empNo,
			@RequestParam(name = "id", required = true) String id,
			@RequestParam(name = "reasonId", required = true) String reasonId
			) throws Exception{
		// 查询实体
		String empId = empNo;
		BusinessClues entity = new BusinessClues();
		entity.setxEmpNo(empShorNo);
		entity.setEmpNo(empNo);
		entity.setId(id);
		entity.setxLangId(lang);
		entity.setxTenantId(tenantId);
		entity.setEmpId(empId);
		entity.setBackReasonId(reasonId);
		entity.setBackPersonId(empId);
		// 被退回
		entity.setStatusCode(CluesSysConst.STATUS_CODE_REFUSED);
		// 当前登录人是否为归属客户经理 、 线索状态是否为“待客户经理更新”
		if(businessCluesService.isBelongCustomerMgr(entity) && businessCluesService.isLeadUpdate(entity)){
			businessCluesService.backLead(entity);
		} 
		return ServiceDataUtil.success(null);
	}
	
	
	@ApiOperation("线索退回-PC")
	@ApiImplicitParams({
		@ApiImplicitParam(paramType = "query", name = "clueNo", dataType="String", dataTypeClass=String.class, required = true, value = "线索编号", defaultValue = "L17011214363"),
		@ApiImplicitParam(paramType = "query", name = "reasonCode", dataType="String", dataTypeClass=String.class, required = true, value = "退回原因Code", defaultValue = "Others"),
		@ApiImplicitParam(paramType = "query", name = "empNo", dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "00181547"),
		@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "00181547"),
		@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, dataType="String", dataTypeClass=String.class, required = true, value = "租户ID", defaultValue = "pcOpportunity"),
		@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, dataType="String", dataTypeClass=String.class, required = true, value = "语言", defaultValue = "zh_CN") })
		@RequestMapping(value = "/clueBackSavePC", method = RequestMethod.POST)
	public ServiceDataCopy<?> clueBackSavePC(
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, required = true) String empShorNo,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, required = true) String tenantId,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, required = true) String lang,
			@RequestParam(name = "empNo", required = true) String empNo,
			@RequestParam(name = "clueNo", required = true) String clueNo,
			@RequestParam(name = "reasonCode", required = true) String reasonCode
			) throws Exception{
		// 获取登录人empId
		Map<String, String> paramMap = new HashMap<>(3);
		paramMap.put("empNo", empShorNo);
		paramMap.put("tenantId", tenantId);
		paramMap.put("lang", lang);
		String empId = empNo;
		BusinessClues entity = new BusinessClues();
		entity.setClueNum(clueNo);
		entity.setBackReasonCode(reasonCode);
		entity.setEmpId(empId);
        // 被退回
		entity.setStatusCode(OppSysConst.REFUSED);
		entity.setBackPerson(empId);
		// 当前登录人是否为归属客户经理 、 线索状态是否为“待客户经理更新”
		if(pcbusinessCluesService.isBelongCustomerMgr(entity) && pcbusinessCluesService.isLeadUpdate(entity)){
			// 线索退回
			pcbusinessCluesService.backClue(entity);
			return ServiceDataUtil.success(null);
		} 
		else
		{
			return ServiceDataUtil.noAuthInBusiness(null);
		}
	}
	@ApiOperation("PC端线索转商机")
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", name = "empNo", dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "00181547"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "00181547"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, dataType="String", dataTypeClass=String.class, required = true, value = "租户ID", defaultValue = "pcOpportunity"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, dataType="String", dataTypeClass=String.class, required = true, value = "语言", defaultValue = "zh_CN") })
	@RequestMapping(value = "/clueTransferPC", method = RequestMethod.POST)
	public ServiceDataCopy<String> clueTransferPC(
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, required = true) String empShorNo,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, required = true) String tenantId,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, required = true) String lang,
			@RequestParam(name = "empNo", required = true) String empNo,
			@Validated @RequestBody(required=true) CluesToOptyDTO dto,
			BindingResult bindingResult) throws Exception {
		if(bindingResult != null && bindingResult.hasErrors())
		{
			throw new ValidationException(bindingResult);
		}
		if(dto == null) {
			return null;
		}
		/**线索转商机黑名单限制*/
		String params=JacksonJsonConverUtil.beanToJson(dto);
		Boolean  sactionedParty= pcbusinessCluesService.queryForbiddon(dto);
		
		if(sactionedParty==true){
			return ServiceDataUtil.noAuthInBusiness(CluesSysConst.EMBARGO_SACTIONED);
		}
		BusinessClues entity = new BusinessClues();
		entity.setxEmpNo(empNo);
		entity.setxLangId(lang);
		entity.setxTenantId(tenantId);
		entity.setId(dto.getId());
		entity.setEmpId(empNo);
		boolean transferAuth = pcbusinessCluesService.checkAssignedAuthWithClue(entity);
		if(!transferAuth){
			// 无权限转商机 返回0003，无业务权限
			return ServiceDataUtil.noAuthInBusiness(null);
		}
		// 转
		BusinessClues businessClues = new BusinessClues();
		BeanUtils.copyProperties(dto, businessClues);
		businessClues.setEmpId(empNo);
		String optyId=pcbusinessCluesService.transferSave(businessClues);
		String resultMsg = "";
		if(StringUtils.isNotBlank(optyId)) {
			resultMsg = opportunityDetailService.creditCheck(optyId, OppSysConst.TRANSFER_OPPORTUNITY);
		}
		return ServiceDataUtil.success(resultMsg);
	}
	@ApiOperation("PC端线索关闭")
	@ApiImplicitParams({
		    @ApiImplicitParam(paramType = "query", name = "empNo", dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "00181547"),
			@ApiImplicitParam(paramType = "query", name = "id", dataType="String", dataTypeClass=String.class, required = true, value = "线索ID", defaultValue = "1-3ARTTA"),
			@ApiImplicitParam(paramType = "query", name = "reasonCode", dataType="String", dataTypeClass=String.class, required = true, value = "关闭原因", defaultValue = ""),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "6011000145"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, dataType="String", dataTypeClass=String.class, required = true, value = "租户ID", defaultValue = "pcOpportunity"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, dataType="String", dataTypeClass=String.class, required = true, value = "语言", defaultValue = "zh") })
	@RequestMapping(value = "/PCclueClose", method = RequestMethod.POST)
	public ServiceDataCopy<?> getclueCloseByPc(
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, required = true) String empShorNo,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, required = true) String tenantId,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, required = true) String lang,
			@RequestParam(name = "empNo", required = true) String empNo,
			@RequestParam(name = "reasonCode", required = true) String reasonCode,
			@RequestParam(name = "id", required = true) String id) throws BusiException {
		BusinessClues entity = new BusinessClues();
		entity.setxEmpNo(empNo);
		entity.setId(id);
		entity.setxLangId(lang);
		entity.setxTenantId(tenantId);
		entity.setReasonCode(reasonCode);
		entity.setEmpId(empNo);
		boolean closeAuth = pcbusinessCluesService.checkClosedAuthClue(entity);
		if(!closeAuth){
			//无权限关闭 返回0003，无业务权限
			return ServiceDataUtil.noAuthInBusiness(null);
		}
		// 关闭
		String resultMsg=pcbusinessCluesService.closeSave(entity);
		if (!StringUtils.isEmpty(resultMsg)) {
			return ServiceDataUtil.success(null);
		}
		return ServiceDataUtil.busiError(messageSource.getMessage("change.filed",null,LocaleContextHolder.getLocale())+":"+resultMsg);
	}
	@ApiOperation("PC端线索还原")
	@ApiImplicitParams({
		    @ApiImplicitParam(paramType = "query", name = "empNo", dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "00181547"),
			@ApiImplicitParam(paramType = "query", name = "id", dataType="String", dataTypeClass=String.class, required = true, value = "线索ID", defaultValue = "1-3ARTTA"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "6011000145"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, dataType="String", dataTypeClass=String.class, required = true, value = "租户ID", defaultValue = "pcOpportunity"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, dataType="String", dataTypeClass=String.class, required = true, value = "语言", defaultValue = "zh") })
	@RequestMapping(value = "/PCclueRestore", method = RequestMethod.POST)
	public ServiceDataCopy<?> getclueRestoreByPc(
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, required = true) String empShorNo,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, required = true) String tenantId,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, required = true) String lang,
			@RequestParam(name = "empNo", required = true) String empNo,
			@RequestParam(name = "id", required = true) String id) throws BusiException {
		BusinessClues entity = new BusinessClues();
		entity.setxEmpNo(empNo);
		entity.setId(id);
		entity.setxLangId(lang);
		entity.setxTenantId(tenantId);
		entity.setEmpId(empNo);
		// 校验权限和状态
		boolean closeAuth = pcbusinessCluesService.checkRestoreAuthClue(entity);
		if(!closeAuth){
			// 无权限 返回0003，无业务权限
			return ServiceDataUtil.noAuthInBusiness(null);
		}
		// 还原
		String resultMsg=pcbusinessCluesService.clueRestore(entity);
		if (!StringUtils.isEmpty(resultMsg)) {
			return ServiceDataUtil.success(null);
		}
		return ServiceDataUtil.busiError(LocalMessageUtils.getMessage("oppertunity.change.failed")+":"+resultMsg);
	}
}
