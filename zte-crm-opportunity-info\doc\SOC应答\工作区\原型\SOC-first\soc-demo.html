<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SOC智能应答系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        /* 顶部导航栏 */
        .top-navbar {
            height: 60px;
            background: #fff;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .logo-section {
            display: flex;
            align-items: center;
        }

        .logo {
            width: 32px;
            height: 32px;
            background: #1890ff;
            border-radius: 4px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .system-name {
            font-size: 18px;
            font-weight: 500;
            color: #262626;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .user-info {
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .user-info:hover {
            background-color: #f5f5f5;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #87d068;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        /* 主体布局 */
        .main-container {
            display: flex;
            margin-top: 60px;
            min-height: calc(100vh - 60px);
        }

        /* 左侧导航栏 */
        .sidebar {
            width: 240px;
            background: #fff;
            border-right: 1px solid #e8e8e8;
            overflow-y: auto;
        }

        .nav-menu {
            padding: 16px 0;
        }

        .nav-item {
            padding: 12px 24px;
            cursor: pointer;
            transition: all 0.3s;
            border-left: 3px solid transparent;
            display: flex;
            align-items: center;
        }

        .nav-item:hover {
            background-color: #f5f5f5;
        }

        .nav-item.active {
            background-color: #e6f7ff;
            border-left-color: #1890ff;
            color: #1890ff;
        }

        .nav-icon {
            width: 16px;
            height: 16px;
            margin-right: 12px;
            display: inline-block;
        }

        /* 内容区域 */
        .content-area {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }

        .page-header {
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 500;
            color: #262626;
            margin-bottom: 8px;
        }

        .page-description {
            color: #8c8c8c;
            font-size: 14px;
        }

        /* 卡片容器 */
        .card {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            overflow: hidden;
            margin-bottom: 16px;
        }

        .card-header {
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
        }

        .card-body {
            padding: 24px;
        }

        /* 面包屑 */
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 16px;
            font-size: 14px;
            color: #8c8c8c;
        }

        .breadcrumb a {
            color: #1890ff;
            text-decoration: none;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        /* 任务信息头部 */
        .task-info-header {
            background: #f8f9fa;
            padding: 16px 24px;
            border-bottom: 1px solid #e8e8e8;
        }

        .task-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .task-info-item {
            display: flex;
            flex-direction: column;
        }

        .task-info-label {
            font-size: 12px;
            color: #8c8c8c;
            margin-bottom: 4px;
        }

        .task-info-value {
            font-size: 14px;
            color: #262626;
            font-weight: 500;
        }

        /* 标签页 */
        .tabs {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
        }

        .tab {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab:hover {
            color: #40a9ff;
        }

        .tab.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
        }

        /* 任务详情布局 */
        .task-detail-container {
            display: flex;
            height: calc(100vh - 200px);
        }

        .content-panel {
            flex: 1;
            background: #fff;
            overflow-y: auto;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: #fff;
            color: #262626;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            min-height: 32px;
        }

        .btn:hover {
            border-color: #40a9ff;
            color: #40a9ff;
        }

        .btn-primary {
            background: #1890ff;
            border-color: #1890ff;
            color: #fff;
        }

        .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
            color: #fff;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
            min-height: 28px;
        }

        .btn-danger {
            background: #ff4d4f;
            border-color: #ff4d4f;
            color: #fff;
        }

        .btn-danger:hover {
            background: #ff7875;
            border-color: #ff7875;
            color: #fff;
        }

        /* 表单样式 */
        .form-row {
            display: flex;
            gap: 24px;
            margin-bottom: 16px;
        }

        .form-item {
            flex: 1;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #262626;
        }

        .form-label.required::after {
            content: '*';
            color: #ff4d4f;
            margin-left: 4px;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s;
        }

        .form-control:focus {
            border-color: #40a9ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .form-control::placeholder {
            color: #bfbfbf;
        }

        /* 复选框样式 */
        .checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .checkbox input[type="checkbox"] {
            margin: 0;
        }

        /* 查询面板优化 */
        .query-panel {
            background: #fafafa;
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .query-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 12px;
            margin-bottom: 12px;
        }

        .query-actions {
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }

        /* 操作栏样式 */
        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding: 0 16px;
        }

        .toolbar-left {
            display: flex;
            gap: 8px;
        }

        .toolbar-right {
            display: flex;
            gap: 16px;
            align-items: center;
        }

        .search-box {
            width: 280px;
        }

        /* 表格样式 */
        .table-container {
            overflow-x: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: #fff;
        }

        .table th,
        .table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: top;
        }

        .table th {
            background: #fafafa;
            font-weight: 500;
            color: #262626;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table tbody tr:hover {
            background-color: #f5f5f5;
        }

        .table-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        /* 统一操作按钮样式 */
        .table-actions .btn {
            min-width: 70px;
            height: 28px;
            padding: 4px 8px;
            font-size: 12px;
            line-height: 1.2;
            text-align: center;
            white-space: nowrap;
        }

        /* 条目分组样式 */
        .item-group {
            border-left: 3px solid #1890ff;
            margin-bottom: 1px;
        }

        .item-group .first-row {
            background: #f0f9ff;
        }

        /* 标签样式 */
        .tag {
            display: inline-block;
            padding: 2px 8px;
            background: #f0f0f0;
            border-radius: 12px;
            font-size: 12px;
            margin-right: 4px;
            margin-bottom: 4px;
        }

        .tag.primary {
            background: #e6f7ff;
            color: #1890ff;
        }

        /* 状态标签 */
        .status-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-tag.processing {
            background: #fff7e6;
            color: #fa8c16;
        }

        .status-tag.completed {
            background: #f6ffed;
            color: #52c41a;
        }

        .status-tag.pending {
            background: #f0f0f0;
            color: #8c8c8c;
        }

        /* 满足度标签 */
        .satisfaction-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .satisfaction-tag.fc {
            background: #f6ffed;
            color: #52c41a;
        }

        .satisfaction-tag.pc {
            background: #fff7e6;
            color: #fa8c16;
        }

        .satisfaction-tag.nc {
            background: #fff2f0;
            color: #ff4d4f;
        }

        /* 进度条 */
        .progress {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .progress-bar {
            width: 60px;
            height: 6px;
            background: #f0f0f0;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #1890ff;
            transition: width 0.3s;
        }

        .progress-text {
            font-size: 12px;
            color: #8c8c8c;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            padding: 16px;
            gap: 16px;
            border-top: 1px solid #f0f0f0;
        }

        .pagination-info {
            color: #8c8c8c;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.45);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-dialog {
            background: #fff;
            border-radius: 8px;
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 16px 24px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 16px;
            font-weight: 500;
            color: #262626;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #8c8c8c;
            padding: 4px;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }

        /* 文件上传 */
        .upload-area {
            border: 1px dashed #d9d9d9;
            border-radius: 4px;
            padding: 24px;
            text-align: center;
            background: #fafafa;
            cursor: pointer;
            transition: all 0.3s;
        }

        .upload-area:hover {
            border-color: #40a9ff;
        }

        .upload-icon {
            font-size: 24px;
            color: #8c8c8c;
            margin-bottom: 8px;
        }

        .upload-text {
            color: #8c8c8c;
            font-size: 14px;
        }

        /* 富文本编辑器样式 */
        .rich-editor {
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            min-height: 120px;
        }

        .editor-toolbar {
            border-bottom: 1px solid #f0f0f0;
            padding: 8px 12px;
            background: #fafafa;
            display: flex;
            gap: 8px;
        }

        .editor-content {
            padding: 12px;
            min-height: 80px;
            outline: none;
        }

        /* 自动刷新提示 */
        .auto-refresh {
            position: fixed;
            top: 80px;
            right: 24px;
            background: #fff;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 12px;
            color: #8c8c8c;
            z-index: 100;
        }

        /* 统计卡片样式 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 24px;
        }

        .stat-item {
            text-align: center;
            padding: 16px;
            background: #fafafa;
            border-radius: 8px;
            border: 1px solid #f0f0f0;
        }

        .stat-value {
            font-size: 32px;
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: #8c8c8c;
        }

        /* 快捷应答样式优化 */
        #quick-response .form-row:last-child {
            margin-top: 32px;
        }

        #quick-response textarea {
            min-height: 120px;
            resize: vertical;
        }

        .help-text {
            font-size: 12px;
            color: #8c8c8c;
            margin-top: 8px;
            line-height: 1.4;
        }

        /* 数据分析样式优化 */
        #data-analysis .card {
            border: 1px solid #f0f0f0;
        }

        #data-analysis .card-header {
            background: #fafafa;
            border-bottom: 1px solid #f0f0f0;
        }

        #data-analysis .card-header h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
            color: #262626;
        }

        /* 人工应答页面样式 */
        .match-source-section {
            border: 1px solid #f0f0f0;
            border-radius: 8px;
            margin-bottom: 16px;
            background: #fff;
        }

        .match-source-header {
            padding: 16px 20px;
            background: #fafafa;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-radius: 8px 8px 0 0;
        }

        .match-source-header:hover {
            background: #f5f5f5;
        }

        .match-source-header h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
            color: #262626;
        }

        .toggle-icon {
            color: #8c8c8c;
            transition: transform 0.3s;
        }

        .toggle-icon.collapsed {
            transform: rotate(-90deg);
        }

        .match-source-content {
            padding: 16px 20px;
        }

        .match-source-content.collapsed {
            display: none;
        }

        .match-item {
            border: 1px solid #f0f0f0;
            border-radius: 6px;
            margin-bottom: 16px;
            background: #fff;
            transition: all 0.3s;
        }

        .match-item:hover {
            border-color: #d9d9d9;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
        }

        .match-item-header {
            padding: 12px 16px;
            background: #fafafa;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-radius: 6px 6px 0 0;
        }

        .match-score {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .match-percentage {
            font-size: 18px;
            font-weight: bold;
            color: #1890ff;
        }

        .match-stars {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .star {
            color: #d9d9d9;
            font-size: 16px;
        }

        .star.active {
            color: #faad14;
        }

        .match-label {
            font-size: 12px;
            color: #8c8c8c;
            margin-left: 8px;
        }

        .match-item-content {
            padding: 16px;
        }

        .match-field {
            margin-bottom: 12px;
            line-height: 1.5;
        }

        .match-field:last-child {
            margin-bottom: 0;
        }

        .match-field strong {
            color: #262626;
            margin-right: 8px;
        }

        /* 富文本编辑器增强 */
        .editor-content[contenteditable="true"]:empty::before {
            content: "请输入应答说明...";
            color: #bfbfbf;
        }

        .editor-content[contenteditable="true"]:focus::before {
            content: "";
        }

        /* AI功能按钮样式 */
        .ai-result-panel {
            margin-top: 16px;
            padding: 16px;
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 6px;
            display: none;
        }

        .ai-result-panel.show {
            display: block;
        }

        .ai-result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .ai-result-title {
            font-weight: 500;
            color: #52c41a;
        }

        .ai-result-actions {
            display: flex;
            gap: 8px;
        }

        .ai-result-content {
            background: #fff;
            padding: 12px;
            border-radius: 4px;
            border: 1px solid #d9f7be;
            white-space: pre-wrap;
            line-height: 1.6;
        }

        /* 版本对比样式 */
        .version-compare-modal .modal-dialog {
            max-width: 1000px;
        }

        .version-compare-content {
            display: flex;
            gap: 24px;
        }

        .version-panel {
            flex: 1;
            border: 1px solid #f0f0f0;
            border-radius: 6px;
        }

        .version-header {
            padding: 12px 16px;
            background: #fafafa;
            border-bottom: 1px solid #f0f0f0;
            font-weight: 500;
        }

        .version-content {
            padding: 16px;
            max-height: 400px;
            overflow-y: auto;
        }

        /* 匹配统计样式 */
        .match-stats {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .match-stats .stat-item {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            background: #f0f0f0;
            color: #666;
        }

        .match-stats .stat-item.fc {
            background: #f6ffed;
            color: #52c41a;
        }

        .match-stats .stat-item.pc {
            background: #fff7e6;
            color: #fa8c16;
        }

        .match-stats .stat-item.nc {
            background: #fff2f0;
            color: #ff4d4f;
        }

        /* 匹配卡片网格样式 */
        .match-cards-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            margin-bottom: 16px;
        }

        .match-card {
            border: 1px solid #f0f0f0;
            border-radius: 6px;
            background: #fff;
            transition: all 0.3s;
        }

        .match-card:hover {
            border-color: #d9d9d9;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .match-card-header {
            padding: 12px 16px;
            background: #fafafa;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-radius: 6px 6px 0 0;
        }

        .match-card-content {
            padding: 16px;
            font-size: 13px;
            line-height: 1.6;
        }

        .match-pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin-top: 16px;
        }

        .match-pagination .btn {
            min-width: 32px;
            height: 32px;
            padding: 0;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .sidebar {
                width: 200px;
            }
            
            .content-area {
                padding: 16px;
            }
            
            .form-row {
                flex-direction: column;
                gap: 16px;
            }
            
            .toolbar {
                flex-direction: column;
                gap: 16px;
            }
            
            .search-box {
                width: 100%;
            }

            .task-detail-container {
                flex-direction: column;
                height: auto;
            }

            .outline-panel {
                width: 100%;
                height: 200px;
            }

            .outline-panel.collapsed {
                height: 0;
                width: 100%;
            }

            .query-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;
            }
            
            .stat-item {
                padding: 12px;
            }
            
            .stat-value {
                font-size: 24px;
            }

            .match-item-header {
                flex-direction: column;
                gap: 12px;
                align-items: stretch;
            }

            .match-score {
                justify-content: space-between;
            }

            .version-compare-content {
                flex-direction: column;
            }
        }

        @media (max-width: 480px) {
            .query-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 编辑任务弹窗样式 */
        .edit-task-modal .modal-dialog {
            max-width: 900px;
        }

        /* 列筛选样式 */
        .column-filter {
            position: relative;
            display: inline-block;
        }

        .column-filter-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: #fff;
            color: #262626;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            min-height: 32px;
        }

        .column-filter-btn:hover {
            border-color: #40a9ff;
            color: #40a9ff;
        }

        .column-filter-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            margin-top: 4px;
            background: #fff;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 100;
            min-width: 200px;
            display: none;
        }

        .column-filter-dropdown.show {
            display: block;
        }

        .column-filter-header {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            font-weight: 500;
            color: #262626;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .column-filter-content {
            padding: 8px 0;
            max-height: 300px;
            overflow-y: auto;
        }

        .column-filter-item {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .column-filter-item:hover {
            background-color: #f5f5f5;
        }

        .column-filter-item input[type="checkbox"] {
            margin-right: 8px;
        }

        .column-filter-actions {
            padding: 8px 16px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            gap: 8px;
        }

        .column-filter-actions .btn {
            flex: 1;
            min-height: 28px;
            padding: 4px 8px;
            font-size: 12px;
        }

    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <div class="top-navbar">
        <div class="logo-section">
            <div class="logo">SOC</div>
            <div class="system-name">SOC智能应答系统</div>
        </div>
        <div class="user-menu">
            <div class="user-info">
                <div class="user-avatar">张</div>
                <span>张三（工号：123456）</span>
            </div>
        </div>
    </div>

    <!-- 主体布局 -->
    <div class="main-container">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <div class="nav-menu">
                <div class="nav-item active" onclick="showPage('task-management')">
                    <span class="nav-icon">📋</span>
                    任务管理
                </div>
                <div class="nav-item" onclick="showPage('quick-response')">
                    <span class="nav-icon">⚡</span>
                    快捷应答
                </div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 任务管理页面 -->
            <div id="task-management" class="page">
                <div class="page-header">
                    <h1 class="page-title">任务管理</h1>
                    <p class="page-description">管理和查看SOC智能应答任务</p>
                </div>

                <div class="card">
                    <div class="card-body">
                        <!-- 操作栏 -->
                        <div class="toolbar">
                            <div class="toolbar-left">
                                <button class="btn btn-primary" onclick="showCreateTaskModal()">
                                    创建任务
                                </button>
                            </div>
                        </div>

                        <!-- 查询条件 -->
                        <div class="query-panel" style="margin: 16px 0;">
                            <div class="query-grid">
                                <div class="form-item">
                                    <label class="form-label">任务编码</label>
                                    <input type="text" class="form-control" placeholder="请输入任务编码" id="searchTaskCode">
                                </div>
                                <div class="form-item">
                                    <label class="form-label">任务名称</label>
                                    <input type="text" class="form-control" placeholder="请输入任务名称" id="searchTaskName">
                                </div>
                                <div class="form-item">
                                    <label class="form-label">国家</label>
                                    <select class="form-control" id="searchCountry">
                                        <option value="">全部国家</option>
                                        <option value="中国">中国</option>
                                        <option value="新加坡">新加坡</option>
                                        <option value="德国">德国</option>
                                        <option value="英国">英国</option>
                                        <option value="美国">美国</option>
                                    </select>
                                </div>
                                <div class="form-item">
                                    <label class="form-label">客户</label>
                                    <input type="text" class="form-control" placeholder="请输入客户名称" id="searchCustomer">
                                </div>
                                <div class="form-item">
                                    <label class="form-label">项目</label>
                                    <input type="text" class="form-control" placeholder="请输入项目名称" id="searchProject">
                                </div>
                            </div>
                            <div class="query-actions">
                                <button class="btn btn-primary" onclick="searchTasks()">查询</button>
                            </div>
                        </div>

                        <!-- 任务列表 -->
                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>任务编码</th>
                                        <th>任务名称</th>
                                        <th>国家</th>
                                        <th>客户</th>
                                        <th>项目</th>
                                        <th>应答条目数</th>
                                        <th>应答进度</th>
                                        <th>总满足度</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="taskTableBody">
                                    <!-- 任务数据将通过JavaScript动态渲染 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <div class="pagination">
                            <div class="pagination-info" id="paginationInfo">共 0 条记录，第 1/1 页</div>
                            <div class="pagination-controls">
                                <select class="form-control" style="width: auto;">
                                    <option value="10">10条/页</option>
                                    <option value="20" selected>20条/页</option>
                                    <option value="50">50条/页</option>
                                    <option value="100">100条/页</option>
                                    <option value="200">200条/页</option>
                                </select>
                                <button class="btn" disabled>上一页</button>
                                <button class="btn" disabled>下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 任务详情页面 -->
            <div id="task-detail" class="page" style="display: none;">
                <!-- 面包屑 -->
                <div class="breadcrumb">
                    <a href="#" onclick="showPage('task-management')">任务管理</a>
                    <span>></span>
                    <span id="taskDetailTitle">任务详情</span>
                </div>

                <!-- 任务信息头部 -->
                <div class="card">
                    <div class="task-info-header" id="taskInfoHeader">
                        <!-- 任务信息将通过JavaScript动态渲染 -->
                    </div>

                    <!-- 标签页 -->
                    <div class="tabs">
                        <div class="tab active" onclick="showTab('item-management')">条目管理</div>
                        <div class="tab" onclick="showTab('data-analysis')">数据分析</div>
                    </div>

                    <!-- 条目管理内容 -->
                    <div id="item-management" class="tab-content">
                        <div class="task-detail-container">
                            <!-- 内容面板 -->
                            <div class="content-panel" id="contentPanel">
                                <!-- 查询面板 -->
                                <div class="query-panel">
                                    <div class="query-grid">
                                        <div class="form-item">
                                            <label class="form-label">编号</label>
                                            <input type="text" class="form-control" placeholder="请输入编号" id="queryCode">
                                        </div>
                                        <div class="form-item">
                                            <label class="form-label">条目描述</label>
                                            <input type="text" class="form-control" placeholder="请输入条目描述" id="queryDescription">
                                        </div>
                                        <div class="form-item">
                                            <label class="form-label">产品</label>
                                            <select class="form-control" id="queryProduct">
                                                <option value="">全部产品</option>
                                                <option value="华为云Stack">华为云Stack</option>
                                                <option value="FusionSphere">FusionSphere</option>
                                                <option value="云安全服务">云安全服务</option>
                                                <option value="FusionCompute">FusionCompute</option>
                                            </select>
                                        </div>
                                        <div class="form-item">
                                            <label class="form-label">应答状态</label>
                                            <select class="form-control" id="queryStatus">
                                                <option value="">全部</option>
                                                <option value="未应答">未应答</option>
                                                <option value="应答中">应答中</option>
                                                <option value="已应答">已应答</option>
                                            </select>
                                        </div>
                                        <div class="form-item">
                                            <label class="form-label">标签</label>
                                            <input type="text" class="form-control" placeholder="请输入标签" id="queryTag">
                                        </div>
                                        <div class="form-item">
                                            <label class="form-label">应答</label>
                                            <select class="form-control" id="querySatisfaction">
                                                <option value="">全部</option>
                                                <option value="FC">FC - 完全满足</option>
                                                <option value="PC">PC - 部分满足</option>
                                                <option value="NC">NC - 不满足</option>
                                            </select>
                                        </div>
                                        <div class="form-item">
                                            <label class="form-label">指派给</label>
                                            <input type="text" class="form-control" placeholder="请输入指派人" id="queryAssignee">
                                        </div>
                                    </div>
                                    <div class="query-actions">
                                        <button class="btn" onclick="resetQuery()">重置</button>
                                        <button class="btn btn-primary" onclick="queryItems()">查询</button>
                                        <!-- 列筛选按钮 -->
                                        <div class="column-filter">
                                            <button class="column-filter-btn" onclick="toggleColumnFilter()" id="columnFilterBtn">
                                                <span>⚙️</span>
                                                <span class="toggle-icon">▼</span>
                                            </button>
                                            <div class="column-filter-dropdown" id="columnFilterDropdown">
                                                <div class="column-filter-header">
                                                    <span>选择显示列</span>
                                                    <span id="selectedColumnCount">9/16</span>
                                                </div>
                                                <div class="column-filter-content">
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-select" checked>
                                                        <label for="col-select">选择框</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-序号" checked>
                                                        <label for="col-序号">序号</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-编号" checked>
                                                        <label for="col-编号">编号</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-条目描述" checked>
                                                        <label for="col-条目描述">条目描述</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-产品" checked>
                                                        <label for="col-产品">产品</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-标签">
                                                        <label for="col-标签">标签</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-应答状态" checked>
                                                        <label for="col-应答状态">应答状态</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-应答" checked>
                                                        <label for="col-应答">应答</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-指派给">
                                                        <label for="col-指派给">指派给</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-应答方式">
                                                        <label for="col-应答方式">应答方式</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-应答说明" checked>
                                                        <label for="col-应答说明">应答说明</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-应答来源" checked>
                                                        <label for="col-应答来源">应答来源</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-索引" checked>
                                                        <label for="col-索引">索引</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-备注">
                                                        <label for="col-备注">备注</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-最后更新人">
                                                        <label for="col-最后更新人">最后更新人</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-最后更新时间">
                                                        <label for="col-最后更新时间">最后更新时间</label>
                                                    </div>
                                                    <div class="column-filter-item">
                                                        <input type="checkbox" id="col-操作" checked>
                                                        <label for="col-操作">操作</label>
                                                    </div>
                                                </div>
                                                <div class="column-filter-actions">
                                                    <button class="btn" onclick="resetColumnFilter()">重置</button>
                                                    <button class="btn btn-primary" onclick="applyColumnFilter()">确定</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 操作工具栏 -->
                                <div class="toolbar">
                                    <div class="toolbar-left">
                                        <button class="btn btn-primary" onclick="startResponse()">开始应答</button>
                                        <button class="btn" onclick="batchManualResponse()" id="batchManualBtn" style="display: none;">手工应答</button>
                                        <button class="btn" onclick="batchAiResponse()" id="batchAiBtn" style="display: none;">AI应答</button>
                                        <button class="btn" onclick="batchDelete()">批量删除</button>
                                        <button class="btn" onclick="batchAddTag()">批量添加标签</button>
                                        <button class="btn" onclick="batchRemoveTag()">批量移除标签</button>
                                        <button class="btn" onclick="setProduct()">设置产品</button>
                                        <button class="btn" onclick="assignTo()">指派给</button>
                                        <button class="btn" onclick="exportItems()">导出</button>
                                    </div>
                                    <div class="toolbar-right">
                                        <button class="btn" onclick="showAddItemModal()">单条录入</button>
                                        <button class="btn" onclick="showBatchImportModal()">批量导入</button>
                                    </div>
                                </div>

                                <!-- 条目列表 -->
                                <div class="table-container">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th width="40">
                                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                                </th>
                                                <th>序号</th>
                                                <th>编号</th>
                                                <th>条目描述</th>
                                                <th>产品</th>
                                                <th>标签</th>
                                                <th>应答状态</th>
                                                <th>应答</th>
                                                <th>指派给</th>
                                                <th>应答方式</th>
                                                <th>应答说明</th>
                                                <th>应答来源</th>
                                                <th>索引</th>
                                                <th>备注</th>
                                                <th>最后更新人</th>
                                                <th>最后更新时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="itemTableBody">
                                            <!-- 条目数据将通过JavaScript动态渲染 -->
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 分页 -->
                                <div class="pagination">
                                    <div class="pagination-info" id="itemPaginationInfo">共 0 条记录，第 1/1 页</div>
                                    <div class="pagination-controls">
                                        <select class="form-control" style="width: auto;">
                                            <option value="10">10条/页</option>
                                            <option value="20" selected>20条/页</option>
                                            <option value="50">50条/页</option>
                                        </select>
                                        <button class="btn" disabled>上一页</button>
                                        <button class="btn" disabled>下一页</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据分析内容 -->
                    <div id="data-analysis" class="tab-content" style="display: none;">
                        <div class="card-body">
                            <!-- 筛选条件 -->
                            <div class="query-panel" style="margin-bottom: 24px;">
                                <div class="query-grid">
                                    <div class="form-item">
                                        <label class="form-label">指派给</label>
                                        <select class="form-control" onchange="filterAnalysisData()">
                                            <option value="">全部</option>
                                            <option value="123456">张三（123456）</option>
                                            <option value="789012">李四（789012）</option>
                                            <option value="345678">王五（345678）</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- 总体统计 -->
                            <div class="card" style="margin-bottom: 24px;">
                                <div class="card-header">
                                    <h4>进展分析</h4>
                                </div>
                                <div class="card-body">
                                    <div class="stats-grid" id="statsGrid">
                                        <!-- 统计数据将通过JavaScript动态渲染 -->
                                    </div>
                                </div>
                            </div>

                            <!-- 产品维度统计 -->
                            <div class="card">
                                <div class="card-header">
                                    <h4>产品维度统计</h4>
                                </div>
                                <div class="card-body">
                                    <div class="table-container">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>产品</th>
                                                    <th>总条目数</th>
                                                    <th>已应答数</th>
                                                    <th>FC</th>
                                                    <th>PC</th>
                                                    <th>NC</th>
                                                    <th>满足度</th>
                                                </tr>
                                            </thead>
                                            <tbody id="productStatsBody">
                                                <!-- 产品统计数据将通过JavaScript动态渲染 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快捷应答页面 -->
            <div id="quick-response" class="page" style="display: none;">
                <div class="page-header">
                    <h1 class="page-title">快捷应答</h1>
                    <p class="page-description">快速进行单个条目应答</p>
                </div>
                
                <div class="card">
                    <div class="card-body">
                        <form id="quickResponseForm">
                            <div class="form-row">
                                <div class="form-item">
                                    <label class="form-label required">产品</label>
                                    <select class="form-control" required id="quickProductSelect">
                                        <option value="">请选择产品</option>
                                        <option value="华为云Stack">华为云Stack</option>
                                        <option value="FusionSphere">FusionSphere</option>
                                        <option value="云安全服务">云安全服务</option>
                                        <option value="FusionCompute">FusionCompute</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-item">
                                    <label class="form-label">国家/MTO</label>
                                    <select class="form-control">
                                        <option value="">请选择</option>
                                        <option value="中国">中国</option>
                                        <option value="新加坡">新加坡</option>
                                        <option value="德国">德国</option>
                                        <option value="英国">英国</option>
                                        <option value="法国">法国</option>
                                    </select>
                                </div>
                                <div class="form-item">
                                    <label class="form-label">省公司/分支</label>
                                    <select class="form-control">
                                        <option value="">请选择</option>
                                        <option value="华为技术有限公司">华为技术有限公司</option>
                                        <option value="华为软件技术有限公司">华为软件技术有限公司</option>
                                        <option value="华为云计算技术有限公司">华为云计算技术有限公司</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-item">
                                    <label class="form-label">客户</label>
                                    <input type="text" class="form-control" placeholder="请输入客户名称">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-item">
                                    <label class="form-label required">条目输入</label>
                                    <textarea class="form-control" rows="6" placeholder="请输入要应答的条目描述" required></textarea>
                                    <div style="margin-top: 8px; font-size: 12px; color: #8c8c8c;">
                                        提示：输入条目描述后，系统将自动匹配GBBS中的相关数据进行应答
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-item">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <button type="button" class="btn" onclick="clearQuickForm()">清空</button>
                                        <button type="button" class="btn btn-primary" onclick="startQuickResponse()">开始应答</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 个人任务区提示 -->
                <div class="card" style="margin-top: 24px;">
                    <div class="card-body">
                        <div style="padding: 16px; background: #f6ffed; border: 1px solid #b7eb8f; border-radius: 4px;">
                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                <span style="color: #52c41a; font-size: 16px;">ℹ️</span>
                                <strong style="color: #52c41a;">个人任务区说明</strong>
                            </div>
                            <p style="margin: 0; color: #52c41a; font-size: 14px;">
                                通过快捷应答提交的条目将自动创建个人任务并放置在个人任务区，您可以在任务管理中查看和管理这些任务。个人任务除了无法删除外，其他功能与普通任务完全相同。
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 人工应答详情页面 -->
            <div id="manual-response" class="page" style="display: none;">
                <!-- 面包屑 -->
                <div class="breadcrumb" id="manualResponseBreadcrumb">
                    <a href="#" onclick="showPage('task-management')">任务管理</a>
                    <span>></span>
                    <a href="#" onclick="openTaskDetail(globalData.currentTask ? globalData.currentTask.id : '')">
                        <%= globalData.currentTask ? globalData.currentTask.name : '' %>
                      </a>
                    <span>></span>
                    <span>人工应答</span>
                </div>

                <!-- 条目信息头部 -->
                <div class="card">
                    <div class="task-info-header">
                        <!-- 历史版本选择 -->
                        <div style="margin-top: 16px;">
                            <div style="display: flex; gap: 8px; align-items: center;">
                                <label class="form-label" style="margin: 0; font-weight: 500;">历史版本：</label>
                                <select class="form-control" style="width: 300px;" onchange="loadHistoryVersion()">
                                    <option value="">选择历史版本</option>
                                    <option value="v1">版本1 - 2024-01-15 10:30 (AI应答)</option>
                                    <option value="v2">版本2 - 2024-01-15 11:15 (手工修改)</option>
                                    <option value="current">当前版本 - 2024-01-15 14:20</option>
                                </select>
                            </div>
                        </div>

                        <!-- 条目基本信息 -->
                        <div class="task-info-grid" style="margin-bottom: 16px;">
                            <div class="task-info-item" style="grid-column: span 3;">
                                <div class="task-info-label" style="font-size: 14px;">条目描述</div>
                                <div class="task-info-value" style="font-size: 18px; font-weight: 600; color: #262626;">${globalData.currentItem.description}</div>
                            </div>
                            <div class="task-info-item">
                                <div class="task-info-label" style="font-size: 14px;">条目编号</div>
                                <div class="task-info-value" style="font-size: 16px; font-weight: 500;">${globalData.currentItem.code}</div>
                            </div>
                            <div class="task-info-item">
                                <div class="task-info-label" style="font-size: 14px;">当前产品</div>
                                <div class="task-info-value" style="font-size: 16px; font-weight: 500;" id="currentProduct">${globalData.currentItem.product}</div>
                            </div>
                        </div>

                        <!-- 任务相关信息 -->
                        <div class="task-info-grid" style="margin-bottom: 16px;">
                            <div class="task-info-item">
                                <div class="task-info-label" style="font-size: 14px;">国家/MTO</div>
                                <div class="task-info-value" style="font-size: 16px; font-weight: 500;">${globalData.currentTask?.country || '中国'}</div>
                            </div>
                            <div class="task-info-item">
                                <div class="task-info-label" style="font-size: 14px;">省公司/分支</div>
                                <div class="task-info-value" style="font-size: 16px; font-weight: 500;">${globalData.currentTask?.company || '华为技术有限公司'}</div>
                            </div>
                            <div class="task-info-item">
                                <div class="task-info-label" style="font-size: 14px;">客户</div>
                                <div class="task-info-value" style="font-size: 16px; font-weight: 500;">${globalData.currentTask?.customer || '某银行'}</div>
                            </div>
                        </div>
                        
                        <!-- 产品切换 -->
                        <div style="margin-top: 16px;">
                            <label class="form-label">切换产品查看：</label>
                            <select class="form-control" style="width: 200px;" onchange="switchProduct()">
                                <option value="华为云Stack" ${globalData.currentItem.product === '华为云Stack' ? 'selected' : ''}>华为云Stack</option>
                                <option value="FusionSphere" ${globalData.currentItem.product === 'FusionSphere' ? 'selected' : ''}>FusionSphere</option>
                                <option value="云安全服务" ${globalData.currentItem.product === '云安全服务' ? 'selected' : ''}>云安全服务</option>
                                <option value="FusionCompute" ${globalData.currentItem.product === 'FusionCompute' ? 'selected' : ''}>FusionCompute</option>
                            </select>
                        </div>
                    </div>

                    <!-- 标签页 -->
                    <div class="tabs">
                        <div class="tab active" onclick="showResponseTab('response-result')">应答结果</div>
                        <div class="tab" onclick="showResponseTab('match-details')">匹配详情</div>
                    </div>

                    <!-- 应答结果内容 -->
                    <div id="response-result" class="tab-content">
                        <div class="card-body">
                            <form id="responseForm">
                                <!-- 补充信息 -->
                                <div class="form-row">
                                    <div class="form-item">
                                        <label class="form-label">补充信息</label>
                                        <div style="display: flex; gap: 8px; align-items: center;">
                                            <textarea class="form-control" rows="3" placeholder="点击【AI应答】可在此处进行信息补充，用户可参考此处补充信息进行应答" id="additionalInfo"></textarea>
                                            <button type="button" class="btn btn-primary" onclick="aiEnhance()">AI应答</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-item">
                                        <label class="form-label">满足度</label>
                                        <select class="form-control" style="width: 200px;">
                                            <option value="">请选择</option>
                                            <option value="FC">FC - 完全满足</option>
                                            <option value="PC" selected>PC - 部分满足</option>
                                            <option value="NC">NC - 不满足</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- 应答说明 -->
                                <div class="form-row">
                                    <div class="form-item">
                                        <label class="form-label">应答说明</label>
                                        <div class="rich-editor">
                                            <div class="editor-toolbar">
                                                <button type="button" class="btn btn-sm" onclick="formatText('bold')">B</button>
                                                <button type="button" class="btn btn-sm" onclick="formatText('italic')">I</button>
                                                <button type="button" class="btn btn-sm" onclick="insertImage()">📷</button>
                                                <button type="button" class="btn btn-sm btn-primary" onclick="aiPolish()">AI润色</button>
                                                <button type="button" class="btn btn-sm btn-primary" onclick="aiTranslate()">AI翻译</button>
                                            </div>
                                            <div class="editor-content" contenteditable="true" id="responseContent">
                                                华为云Stack提供统一的云平台基础架构管理能力，支持虚拟机、容器等多种计算资源的统一管理。通过FusionSphere虚拟化平台，可以实现计算、存储、网络资源的动态分配和管理。
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 索引 -->
                                <div class="form-row">
                                    <div class="form-item">
                                        <label class="form-label">索引</label>
                                        <div style="display: flex; gap: 8px; align-items: center;">
                                            <input type="text" class="form-control" value="GBBS-001" readonly>
                                            <button type="button" class="btn" onclick="viewSource()">查看来源</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 来源 -->
                                <div class="form-row">
                                    <div class="form-item">
                                        <label class="form-label">来源</label>
                                        <input type="text" class="form-control" value="GBBS" readonly>
                                    </div>
                                </div>

                                <!-- 备注 -->
                                <div class="form-row">
                                    <div class="form-item">
                                        <label class="form-label">备注</label>
                                        <textarea class="form-control" rows="2" placeholder="可添加额外的描述"></textarea>
                                    </div>
                                </div>

                                <!-- 历史版本 -->
                                <div class="form-row">
                                    <div class="form-item">
                                        <label class="form-label">历史版本</label>
                                        <div style="display: flex; gap: 8px; align-items: center;">
                                            <select class="form-control" style="width: 300px;" onchange="loadHistoryVersion()">
                                                <option value="">选择历史版本</option>
                                                <option value="v1">版本1 - 2024-01-15 10:30 (AI应答)</option>
                                                <option value="v2">版本2 - 2024-01-15 11:15 (手工修改)</option>
                                                <option value="current">当前版本 - 2024-01-15 14:20</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="form-row" style="margin-top: 32px;">
                                    <div class="form-item">
                                        <div style="display: flex; justify-content: flex-end; gap: 16px;">
                                            <button type="button" class="btn" onclick="resetResponse()">重置</button>
                                            <button type="button" class="btn btn-primary" onclick="saveResponse()">保存</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 匹配详情内容 -->
                    <div id="match-details" class="tab-content" style="display: none;">
                        <div class="card-body">
                            <!-- 筛选条件 -->
                            <div class="query-panel" style="margin-bottom: 24px;">
                                <div class="query-grid">
                                    <div class="form-item">
                                        <label class="form-label">匹配度</label>
                                        <select class="form-control" onchange="filterMatches()">
                                            <option value="">全部</option>
                                            <option value="90">≥90%</option>
                                            <option value="80">≥80%</option>
                                            <option value="70">≥70%</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- GBBS匹配结果 -->
                            <div class="match-source-section">
                                <div class="match-source-header" onclick="toggleMatchSource('gbbs')">
                                    <div style="display: flex; align-items: center; gap: 16px;">
                                        <h4>GBBS 匹配结果</h4>
                                        <div class="match-stats">
                                            <span class="stat-item">总数: 15</span>
                                            <span class="stat-item fc">FC: 8</span>
                                            <span class="stat-item pc">PC: 5</span>
                                            <span class="stat-item nc">NC: 2</span>
                                        </div>
                                    </div>
                                    <span class="toggle-icon" id="gbbs-toggle">▼</span>
                                </div>
                                <div class="match-source-content" id="gbbs-content">
                                    <!-- 匹配卡片网格 -->
                                    <div class="match-cards-grid" id="matchCardsGrid">
                                        <!-- 卡片 1 -->
                                        <div class="match-card">
                                            <div class="match-card-header">
                                                <div class="match-score">
                                                    <span class="match-percentage">95%</span>
                                                    <div class="match-stars">
                                                        <span class="star active">★</span>
                                                        <span class="star active">★</span>
                                                        <span class="star active">★</span>
                                                    </div>
                                                </div>
                                                <button class="btn btn-primary btn-sm" onclick="applyMatch(1)">应用</button>
                                            </div>
                                            <div class="match-card-content">
                                                <div class="match-field" style="margin-bottom: 8px;">
                                                    <strong>条目描述：</strong>云平台基础架构统一管理能力，支持虚拟机、容器、存储资源管理
                                                </div>
                                                <div class="match-field" style="margin-bottom: 8px;">
                                                    <strong>满足度：</strong><span class="satisfaction-tag fc">FC</span>
                                                </div>
                                                <div class="match-field" style="margin-bottom: 8px;">
                                                    <strong>应答说明：</strong>华为云Stack基于OpenStack架构，提供完整的基础设施即服务(IaaS)能力...
                                                </div>
                                                <div class="match-field">
                                                    <strong>索引：</strong><a href="#" target="_blank">GBBS-HCS-001</a>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 卡片 2 -->
                                        <div class="match-card">
                                            <div class="match-card-header">
                                                <div class="match-score">
                                                    <span class="match-percentage">87%</span>
                                                    <div class="match-stars">
                                                        <span class="star active">★</span>
                                                        <span class="star active">★</span>
                                                        <span class="star">★</span>
                                                    </div>
                                                </div>
                                                <button class="btn btn-primary btn-sm" onclick="applyMatch(2)">应用</button>
                                            </div>
                                            <div class="match-card-content">
                                                <div class="match-field" style="margin-bottom: 8px;">
                                                    <strong>条目描述：</strong>云平台基础架构管理，包括计算、存储、网络资源的统一调度
                                                </div>
                                                <div class="match-field" style="margin-bottom: 8px;">
                                                    <strong>满足度：</strong><span class="satisfaction-tag pc">PC</span>
                                                </div>
                                                <div class="match-field" style="margin-bottom: 8px;">
                                                    <strong>应答说明：</strong>华为云Stack提供云平台基础架构管理能力，通过统一的管理平台...
                                                </div>
                                                <div class="match-field">
                                                    <strong>索引：</strong><a href="#" target="_blank">GBBS-HCS-002</a>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 卡片 3 -->
                                        <div class="match-card">
                                            <div class="match-card-header">
                                                <div class="match-score">
                                                    <span class="match-percentage">82%</span>
                                                    <div class="match-stars">
                                                        <span class="star active">★</span>
                                                        <span class="star">★</span>
                                                        <span class="star">★</span>
                                                    </div>
                                                </div>
                                                <button class="btn btn-primary btn-sm" onclick="applyMatch(3)">应用</button>
                                            </div>
                                            <div class="match-card-content">
                                                <div class="match-field" style="margin-bottom: 8px;">
                                                    <strong>条目描述：</strong>基础设施即服务平台，提供虚拟化资源管理
                                                </div>
                                                <div class="match-field" style="margin-bottom: 8px;">
                                                    <strong>满足度：</strong><span class="satisfaction-tag pc">PC</span>
                                                </div>
                                                <div class="match-field" style="margin-bottom: 8px;">
                                                    <strong>应答说明：</strong>华为云Stack基于成熟的虚拟化技术，提供IaaS服务...
                                                </div>
                                                <div class="match-field">
                                                    <strong>索引：</strong><a href="#" target="_blank">GBBS-HCS-003</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 分页控件 -->
                                    <div class="match-pagination">
                                        <button class="btn btn-sm" onclick="prevMatchPage()" disabled>上一页</button>
                                        <span class="pagination-info">第 1/5 页</span>
                                        <button class="btn btn-sm" onclick="nextMatchPage()">下一页</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 自动刷新提示 -->
    <div class="auto-refresh" id="autoRefresh" style="display: none;">
        <span id="refreshTimer">5</span>秒后自动刷新
    </div>

    <!-- 创建任务弹窗 -->
    <div id="createTaskModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">创建任务</h3>
                <button class="modal-close" onclick="hideCreateTaskModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="createTaskForm">
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label required">任务名称</label>
                            <input type="text" class="form-control" placeholder="请输入任务名称" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">国家/MTO</label>
                            <select class="form-control">
                                <option value="">请选择</option>
                                <option value="中国">中国</option>
                                <option value="新加坡">新加坡</option>
                                <option value="德国">德国</option>
                                <option value="英国">英国</option>
                            </select>
                        </div>
                        <div class="form-item">
                            <label class="form-label">省公司/分支</label>
                            <select class="form-control">
                                <option value="">请选择</option>
                                <option value="华为技术有限公司">华为技术有限公司</option>
                                <option value="华为软件技术有限公司">华为软件技术有限公司</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">客户</label>
                            <input type="text" class="form-control" placeholder="请输入客户名称">
                        </div>
                        <div class="form-item">
                            <label class="form-label">项目</label>
                            <input type="text" class="form-control" placeholder="请输入项目名称">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">应答条目文件</label>
                            <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                                <div class="upload-icon">📁</div>
                                <div class="upload-text">点击上传Excel文件，或 <button type="button" class="btn btn-sm" onclick="downloadTemplate()">下载模板</button></div>
                                <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideCreateTaskModal()">取消</button>
                <button class="btn btn-primary" onclick="createTask()">创建</button>
            </div>
        </div>
    </div>

    <!-- 单条录入弹窗 -->
    <div id="addItemModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">单条录入</h3>
                <button class="modal-close" onclick="hideAddItemModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addItemForm">
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label required">编号</label>
                            <input type="text" class="form-control" placeholder="请输入条目编号" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label required">条目描述</label>
                            <textarea class="form-control" rows="3" placeholder="请输入条目描述内容" required></textarea>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label required">产品</label>
                            <select class="form-control" required id="addItemProduct">
                                <option value="">请选择产品</option>
                                <option value="华为云Stack">华为云Stack</option>
                                <option value="FusionSphere">FusionSphere</option>
                                <option value="云安全服务">云安全服务</option>
                                <option value="FusionCompute">FusionCompute</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">标签</label>
                            <input type="text" class="form-control" placeholder="输入标签后按回车添加">
                            <div class="tag-list" style="margin-top: 8px;">
                                <!-- 标签将动态添加到这里 -->
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">应答</label>
                            <select class="form-control">
                                <option value="">请选择</option>
                                <option value="FC">FC - 完全满足</option>
                                <option value="PC">PC - 部分满足</option>
                                <option value="NC">NC - 不满足</option>
                            </select>
                        </div>
                        <div class="form-item">
                            <label class="form-label">指派给</label>
                            <input type="text" class="form-control" placeholder="请选择指派人">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">应答说明</label>
                            <div class="rich-editor">
                                <div class="editor-toolbar">
                                    <button type="button" class="btn btn-sm">B</button>
                                    <button type="button" class="btn btn-sm">I</button>
                                    <button type="button" class="btn btn-sm">📷</button>
                                </div>
                                <div class="editor-content" contenteditable="true" placeholder="支持图文混排..."></div>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">补充信息</label>
                            <textarea class="form-control" rows="2" placeholder="填值时作为条目的补充信息"></textarea>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <div class="checkbox">
                                <input type="checkbox" id="autoResponse" checked>
                                <label for="autoResponse">自动应答</label>
                            </div>
                            <div class="checkbox">
                                <input type="checkbox" id="overwriteWhenDuplicate" checked>
                                <label for="overwriteWhenDuplicate">重复时覆盖</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">备注</label>
                            <textarea class="form-control" rows="2" placeholder="条目其他事项说明"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideAddItemModal()">取消</button>
                <button class="btn btn-primary" onclick="addItem()">提交</button>
            </div>
        </div>
    </div>

    <!-- 批量导入弹窗 -->
    <div id="batchImportModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">批量导入</h3>
                <button class="modal-close" onclick="hideBatchImportModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-item">
                    <label class="form-label">导入模板</label>
                    <div style="margin-bottom: 16px;">
                        <button class="btn btn-primary" onclick="downloadTemplate()">下载导入模板</button>
                        <span style="margin-left: 8px; color: #8c8c8c; font-size: 12px;">请先下载模板，按模板格式填写数据后上传</span>
                    </div>
                </div>
                <div class="form-item">
                    <label class="form-label required">导入文件</label>
                    <div class="upload-area" onclick="document.getElementById('batchFileInput').click()">
                        <div class="upload-icon">📁</div>
                        <div class="upload-text">支持Excel格式文件，包含编号、条目描述、标签等字段</div>
                        <input type="file" id="batchFileInput" accept=".xlsx,.xls" style="display: none;">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideBatchImportModal()">取消</button>
                <button class="btn btn-primary" onclick="batchImport()">提交</button>
            </div>
        </div>
    </div>

    <!-- 编辑任务弹窗 -->
    <div id="editTaskModal" class="modal edit-task-modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">编辑任务</h3>
                <button class="modal-close" onclick="hideEditTaskModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editTaskForm">
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label required">任务名称</label>
                            <input type="text" class="form-control" placeholder="请输入任务名称" required id="editTaskName">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">国家/MTO</label>
                            <select class="form-control" id="editTaskCountry">
                                <option value="">请选择</option>
                                <option value="中国">中国</option>
                                <option value="新加坡">新加坡</option>
                                <option value="德国">德国</option>
                                <option value="英国">英国</option>
                            </select>
                        </div>
                        <div class="form-item">
                            <label class="form-label">省公司/分支</label>
                            <select class="form-control" id="editTaskCompany">
                                <option value="">请选择</option>
                                <option value="华为技术有限公司">华为技术有限公司</option>
                                <option value="华为软件技术有限公司">华为软件技术有限公司</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">客户</label>
                            <input type="text" class="form-control" placeholder="请输入客户名称" id="editTaskCustomer">
                        </div>
                        <div class="form-item">
                            <label class="form-label">项目</label>
                            <input type="text" class="form-control" placeholder="请输入项目名称" id="editTaskProject">
                        </div>
                    </div>
                    <input type="hidden" id="editTaskId">
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideEditTaskModal()">取消</button>
                <button class="btn btn-primary" onclick="updateTask()">保存</button>
            </div>
        </div>
    </div>

    <!-- 复制任务弹窗 -->
    <div id="copyTaskModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">复制任务</h3>
                <button class="modal-close" onclick="hideCopyTaskModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="copyTaskForm">
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label required">任务名称</label>
                            <input type="text" class="form-control" placeholder="请输入任务名称" required id="copyTaskName">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">国家/MTO</label>
                            <select class="form-control" id="copyTaskCountry">
                                <option value="">请选择</option>
                                <option value="中国">中国</option>
                                <option value="新加坡">新加坡</option>
                                <option value="德国">德国</option>
                                <option value="英国">英国</option>
                            </select>
                        </div>
                        <div class="form-item">
                            <label class="form-label">省公司/分支</label>
                            <select class="form-control" id="copyTaskCompany">
                                <option value="">请选择</option>
                                <option value="华为技术有限公司">华为技术有限公司</option>
                                <option value="华为软件技术有限公司">华为软件技术有限公司</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">客户</label>
                            <input type="text" class="form-control" placeholder="请输入客户名称" id="copyTaskCustomer">
                        </div>
                        <div class="form-item">
                            <label class="form-label">项目</label>
                            <input type="text" class="form-control" placeholder="请输入项目名称" id="copyTaskProject">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <div class="checkbox">
                                <input type="checkbox" id="copyItemResults">
                                <label for="copyItemResults">复制条目应答结果</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-item">
                            <label class="form-label">应答条目文件</label>
                            <div class="upload-area" onclick="document.getElementById('copyFileInput').click()">
                                <div class="upload-icon">📁</div>
                                <div class="upload-text">点击上传Excel文件</div>
                                <input type="file" id="copyFileInput" accept=".xlsx,.xls" style="display: none;">
                            </div>
                        </div>
                    </div>
                    <input type="hidden" id="copyTaskId">
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideCopyTaskModal()">取消</button>
                <button class="btn btn-primary" onclick="confirmCopyTask()">复制</button>
            </div>
        </div>
    </div>

    <!-- 批量添加标签弹窗 -->
    <div id="batchAddTagModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">批量添加标签</h3>
                <button class="modal-close" onclick="hideBatchAddTagModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-item">
                    <label class="form-label">标签</label>
                    <input type="text" class="form-control" placeholder="输入标签后按回车添加" id="batchTagInput">
                    <div class="tag-list" id="batchTagList" style="margin-top: 8px;">
                        <!-- 标签将动态添加到这里 -->
                    </div>
                    <div class="help-text">按回车键添加标签，可添加多个标签</div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideBatchAddTagModal()">取消</button>
                <button class="btn btn-primary" onclick="confirmBatchAddTag()">确认</button>
            </div>
        </div>
    </div>

    <!-- 批量移除标签弹窗 -->
    <div id="batchRemoveTagModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">批量移除标签</h3>
                <button class="modal-close" onclick="hideBatchRemoveTagModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-item">
                    <label class="form-label">请选择要删除的标签</label>
                    <div id="availableTagsList">
                        <!-- 可选标签列表将动态生成 -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideBatchRemoveTagModal()">取消</button>
                <button class="btn btn-primary" onclick="confirmBatchRemoveTag()">确认</button>
            </div>
        </div>
    </div>

    <!-- 设置产品弹窗 -->
    <div id="setProductModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">设置产品</h3>
                <button class="modal-close" onclick="hideSetProductModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-item">
                    <label class="form-label">选择产品</label>
                    <select class="form-control" id="productSelect">
                        <option value="">请选择产品</option>
                        <option value="华为云Stack">华为云Stack</option>
                        <option value="FusionSphere">FusionSphere</option>
                        <option value="云安全服务">云安全服务</option>
                        <option value="FusionCompute">FusionCompute</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideSetProductModal()">取消</button>
                <button class="btn btn-primary" onclick="confirmSetProduct()">确认</button>
            </div>
        </div>
    </div>

    <!-- 指派给弹窗 -->
    <div id="assignToModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">指派给</h3>
                <button class="modal-close" onclick="hideAssignToModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-item">
                    <label class="form-label">选择指派人</label>
                    <input type="text" class="form-control" placeholder="请输入指派人姓名" id="assigneeInput">
                    <div class="help-text">可输入：张三（123456）、李四（789012）、王五（345678）等</div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideAssignToModal()">取消</button>
                <button class="btn btn-primary" onclick="confirmAssignTo()">确认</button>
            </div>
        </div>
    </div>

    <!-- 导出弹窗 -->
    <div id="exportModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">导出</h3>
                <button class="modal-close" onclick="hideExportModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-item">
                    <label class="form-label">选择要导出的产品</label>
                    <div id="exportProductList">
                        <div class="checkbox">
                            <input type="checkbox" id="exportAll" checked onchange="toggleExportAll()">
                            <label for="exportAll">全部产品</label>
                        </div>
                        <div class="checkbox">
                            <input type="checkbox" id="exportHCS" checked>
                            <label for="exportHCS">华为云Stack</label>
                        </div>
                        <div class="checkbox">
                            <input type="checkbox" id="exportFS" checked>
                            <label for="exportFS">FusionSphere</label>
                        </div>
                        <div class="checkbox">
                            <input type="checkbox" id="exportSecurity" checked>
                            <label for="exportSecurity">云安全服务</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideExportModal()">取消</button>
                <button class="btn btn-primary" onclick="confirmExport()">下载</button>
            </div>
        </div>
    </div>

    <script>
        // 全局数据状态管理
        let globalData = {
            tasks: [
                {
                    id: 1,
                    code: 'TASK001',
                    name: '华为云Stack解决方案技术标',
                    country: '中国',
                    customer: '某银行',
                    project: '云计算平台建设项目',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 25,
                    completedCount: 20,
                    satisfaction: 85,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-10 09:00:00',
                    updateTime: '2024-01-15 16:30:00'
                },
                {
                    id: 2,
                    code: 'TASK002',
                    name: 'FusionSphere虚拟化技术标',
                    country: '新加坡',
                    customer: '某运营商',
                    project: '数据中心虚拟化改造',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 18,
                    completedCount: 18,
                    satisfaction: 92,
                    status: '已完成',
                    isPersonal: false,
                    createTime: '2024-01-08 14:00:00',
                    updateTime: '2024-01-14 18:45:00'
                },
                {
                    id: 3,
                    code: 'TASK003',
                    name: '安全产品技术方案',
                    country: '德国',
                    customer: '某制造企业',
                    project: '工业互联网安全建设',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 12,
                    completedCount: 0,
                    satisfaction: 0,
                    status: '未开始',
                    isPersonal: false,
                    createTime: '2024-01-12 11:00:00',
                    updateTime: '2024-01-12 11:00:00'
                },
                {
                    id: 4,
                    code: 'TASK004',
                    name: '5G核心网解决方案',
                    country: '英国',
                    customer: '英国电信',
                    project: '5G网络建设',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 15,
                    completedCount: 8,
                    satisfaction: 75,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-05 10:00:00',
                    updateTime: '2024-01-16 14:20:00'
                },
                {
                    id: 5,
                    code: 'TASK005',
                    name: 'AI大模型训练平台',
                    country: '美国',
                    customer: '某科技公司',
                    project: 'AI基础设施建设',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 22,
                    completedCount: 15,
                    satisfaction: 88,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-01 09:30:00',
                    updateTime: '2024-01-17 11:45:00'
                },
                {
                    id: 6,
                    code: 'TASK006',
                    name: '智慧城市物联网平台',
                    country: '日本',
                    customer: '东京市政府',
                    project: '智慧城市建设',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 28,
                    completedCount: 20,
                    satisfaction: 82,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-03 08:00:00',
                    updateTime: '2024-01-18 16:30:00'
                },
                {
                    id: 7,
                    code: 'TASK007',
                    name: '数据中心网络架构',
                    country: '法国',
                    customer: '某跨国企业',
                    project: '全球数据中心升级',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 20,
                    completedCount: 20,
                    satisfaction: 95,
                    status: '已完成',
                    isPersonal: false,
                    createTime: '2024-01-02 13:00:00',
                    updateTime: '2024-01-15 17:00:00'
                },
                {
                    id: 8,
                    code: 'TASK008',
                    name: '边缘计算解决方案',
                    country: '韩国',
                    customer: '某制造集团',
                    project: '工业4.0升级改造',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 16,
                    completedCount: 10,
                    satisfaction: 78,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-07 15:30:00',
                    updateTime: '2024-01-19 09:15:00'
                },
                {
                    id: 9,
                    code: 'TASK009',
                    name: '区块链金融平台',
                    country: '瑞士',
                    customer: '某银行集团',
                    project: '数字货币基础设施',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 24,
                    completedCount: 12,
                    satisfaction: 68,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-04 11:20:00',
                    updateTime: '2024-01-20 14:40:00'
                },
                {
                    id: 10,
                    code: 'TASK010',
                    name: '量子通信网络',
                    country: '加拿大',
                    customer: '某政府机构',
                    project: '量子安全通信',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 18,
                    completedCount: 5,
                    satisfaction: 45,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-06 16:45:00',
                    updateTime: '2024-01-21 10:20:00'
                },
                {
                    id: 11,
                    code: 'TASK011',
                    name: '自动驾驶车联网',
                    country: '澳大利亚',
                    customer: '某汽车厂商',
                    project: '智能交通系统',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 30,
                    completedCount: 25,
                    satisfaction: 89,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-09 12:10:00',
                    updateTime: '2024-01-22 15:30:00'
                },
                {
                    id: 12,
                    code: 'TASK012',
                    name: '医疗AI诊断系统',
                    country: '印度',
                    customer: '某医疗机构',
                    project: '智慧医疗建设',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 26,
                    completedCount: 18,
                    satisfaction: 85,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-11 09:40:00',
                    updateTime: '2024-01-23 13:50:00'
                },
                {
                    id: 13,
                    code: 'TASK013',
                    name: '绿色数据中心方案',
                    country: '丹麦',
                    customer: '某能源公司',
                    project: '可持续发展项目',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 19,
                    completedCount: 19,
                    satisfaction: 92,
                    status: '已完成',
                    isPersonal: false,
                    createTime: '2024-01-13 14:25:00',
                    updateTime: '2024-01-24 16:15:00'
                },
                {
                    id: 14,
                    code: 'TASK014',
                    name: '卫星通信终端',
                    country: '巴西',
                    customer: '某电信运营商',
                    project: '偏远地区通信覆盖',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 14,
                    completedCount: 7,
                    satisfaction: 62,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-14 17:10:00',
                    updateTime: '2024-01-25 11:25:00'
                },
                {
                    id: 15,
                    code: 'TASK015',
                    name: '工业互联网平台',
                    country: '意大利',
                    customer: '某制造企业',
                    project: '数字化转型',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 21,
                    completedCount: 14,
                    satisfaction: 73,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-16 10:55:00',
                    updateTime: '2024-01-26 08:40:00'
                },
                {
                    id: 16,
                    code: 'TASK016',
                    name: '智能电网管理系统',
                    country: '西班牙',
                    customer: '某电力公司',
                    project: '智能电网建设',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 23,
                    completedCount: 16,
                    satisfaction: 80,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-17 13:20:00',
                    updateTime: '2024-01-27 14:10:00'
                },
                {
                    id: 17,
                    code: 'TASK017',
                    name: '视频监控AI分析',
                    country: '荷兰',
                    customer: '某安防公司',
                    project: '智慧安防系统',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 17,
                    completedCount: 9,
                    satisfaction: 65,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-18 15:45:00',
                    updateTime: '2024-01-28 12:30:00'
                },
                {
                    id: 18,
                    code: 'TASK018',
                    name: '云原生应用平台',
                    country: '瑞典',
                    customer: '某软件公司',
                    project: '云原生转型',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 25,
                    completedCount: 22,
                    satisfaction: 91,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-19 11:30:00',
                    updateTime: '2024-01-29 16:45:00'
                },
                {
                    id: 19,
                    code: 'TASK019',
                    name: '数字孪生工厂',
                    country: '比利时',
                    customer: '某汽车制造商',
                    project: '智能制造升级',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 27,
                    completedCount: 13,
                    satisfaction: 58,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-20 08:15:00',
                    updateTime: '2024-01-30 10:20:00'
                },
                {
                    id: 20,
                    code: 'TASK020',
                    name: '分布式存储系统',
                    country: '挪威',
                    customer: '某科研机构',
                    project: '大数据存储方案',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 20,
                    completedCount: 20,
                    satisfaction: 96,
                    status: '已完成',
                    isPersonal: false,
                    createTime: '2024-01-21 12:40:00',
                    updateTime: '2024-01-31 14:55:00'
                },
                {
                    id: 21,
                    code: 'TASK021',
                    name: 'AR/VR协作平台',
                    country: '芬兰',
                    customer: '某教育机构',
                    project: '沉浸式教学系统',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 22,
                    completedCount: 11,
                    satisfaction: 72,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-22 16:25:00',
                    updateTime: '2024-02-01 09:35:00'
                },
                {
                    id: 22,
                    code: 'TASK022',
                    name: '网络安全态势感知',
                    country: '奥地利',
                    customer: '某金融机构',
                    project: '网络安全防护',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 18,
                    completedCount: 6,
                    satisfaction: 48,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-23 09:50:00',
                    updateTime: '2024-02-02 13:15:00'
                },
                {
                    id: 23,
                    code: 'TASK023',
                    name: '智能运维平台',
                    country: '葡萄牙',
                    customer: '某互联网公司',
                    project: 'IT运维自动化',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 16,
                    completedCount: 12,
                    satisfaction: 82,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-24 14:30:00',
                    updateTime: '2024-02-03 11:40:00'
                },
                {
                    id: 24,
                    code: 'TASK024',
                    name: '容器化微服务架构',
                    country: '希腊',
                    customer: '某电商平台',
                    project: '架构现代化改造',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 24,
                    completedCount: 17,
                    satisfaction: 79,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-25 10:15:00',
                    updateTime: '2024-02-04 15:25:00'
                },
                {
                    id: 25,
                    code: 'TASK025',
                    name: '人工智能推荐系统',
                    country: '爱尔兰',
                    customer: '某媒体公司',
                    project: '内容个性化推荐',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 19,
                    completedCount: 8,
                    satisfaction: 55,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-26 12:05:00',
                    updateTime: '2024-02-05 08:50:00'
                },
                {
                    id: 26,
                    code: 'TASK026',
                    name: '混合云管理平台',
                    country: '波兰',
                    customer: '某政府部门',
                    project: '政务云建设',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 21,
                    completedCount: 15,
                    satisfaction: 84,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-27 15:40:00',
                    updateTime: '2024-02-06 12:10:00'
                },
                {
                    id: 27,
                    code: 'TASK027',
                    name: '物联网数据采集平台',
                    country: '捷克',
                    customer: '某农业集团',
                    project: '智慧农业系统',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 15,
                    completedCount: 4,
                    satisfaction: 38,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-28 11:20:00',
                    updateTime: '2024-02-07 16:30:00'
                },
                {
                    id: 28,
                    code: 'TASK028',
                    name: '语音识别AI服务',
                    country: '匈牙利',
                    customer: '某呼叫中心',
                    project: '智能客服系统',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 17,
                    completedCount: 13,
                    satisfaction: 87,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-29 13:55:00',
                    updateTime: '2024-02-08 09:25:00'
                },
                {
                    id: 29,
                    code: 'TASK029',
                    name: '区块链供应链追溯',
                    country: '罗马尼亚',
                    customer: '某零售企业',
                    project: '供应链透明化',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 23,
                    completedCount: 19,
                    satisfaction: 90,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-30 16:10:00',
                    updateTime: '2024-02-09 14:45:00'
                },
                {
                    id: 30,
                    code: 'TASK030',
                    name: '量子计算云服务',
                    country: '克罗地亚',
                    customer: '某科技公司',
                    project: '量子计算研发',
                    company: '华为技术有限公司',
                    dataSource: 'GBBS',
                    itemCount: 26,
                    completedCount: 21,
                    satisfaction: 93,
                    status: '进行中',
                    isPersonal: false,
                    createTime: '2024-01-31 08:35:00',
                    updateTime: '2024-02-10 11:55:00'
                }
            ],
            items: [
                // 为TASK001生成条目 (原有的25个条目保持不变)
                {
                    id: 1,
                    taskId: 1,
                    code: 'CODE001',
                    description: '云平台基础架构能力要求，需要支持虚拟机、容器、存储等资源的统一管理',
                    product: '华为云Stack',
                    tags: ['基础架构', '云平台'],
                    status: '应答中',
                    satisfaction: null,
                    assignee: '张三（123456）',
                    responseMethod: 'AI应答',
                    responseContent: '华为云Stack提供统一的云平台基础架构...',
                    source: 'GBBS',
                    index: 'GBBS-001',
                    remark: '',
                    updateUser: '张三（123456）',
                    updateTime: '2024-01-15 10:30'
                },
                {
                    id: 2,
                    taskId: 1,
                    code: 'CODE001',
                    description: '云平台基础架构能力要求，需要支持虚拟机、容器、存储等资源的统一管理',
                    product: 'FusionSphere',
                    tags: ['基础架构', '虚拟化'],
                    status: '已应答',
                    satisfaction: 'PC',
                    assignee: '张三（123456）',
                    responseMethod: '手工应答',
                    responseContent: 'FusionSphere虚拟化平台支持基础架构管理...',
                    source: 'GBBS',
                    index: 'GBBS-002',
                    remark: '需要补充容器支持',
                    updateUser: '张三（123456）',
                    updateTime: '2024-01-15 11:15'
                },
                {
                    id: 3,
                    taskId: 1,
                    code: 'CODE001',
                    description: '云平台基础架构能力要求，需要支持虚拟机、容器、存储等资源的统一管理',
                    product: '云安全服务',
                    tags: ['基础架构', '安全'],
                    status: '未应答',
                    satisfaction: null,
                    assignee: '李四（789012）',
                    responseMethod: null,
                    responseContent: null,
                    source: 'GDBS',
                    index: null,
                    remark: null,
                    updateUser: null,
                    updateTime: null
                },
                {
                    id: 4,
                    taskId: 1,
                    code: 'CODE002',
                    description: '虚拟化资源管理能力，包括计算、存储、网络资源的动态分配和管理',
                    product: '华为云Stack',
                    tags: ['虚拟化', '资源管理'],
                    status: '已应答',
                    satisfaction: 'FC',
                    assignee: '王五（345678）',
                    responseMethod: 'AI应答',
                    responseContent: '华为云Stack提供全面的虚拟化资源管理能力...',
                    source: 'GBBS',
                    index: 'GBBS-003',
                    remark: '',
                    updateUser: '王五（345678）',
                    updateTime: '2024-01-15 14:20'
                },
                {
                    id: 5,
                    taskId: 1,
                    code: 'CODE002',
                    description: '虚拟化资源管理能力，包括计算、存储、网络资源的动态分配和管理',
                    product: 'FusionSphere',
                    tags: ['虚拟化', '管理平台'],
                    status: '已应答',
                    satisfaction: 'FC',
                    assignee: '王五（345678）',
                    responseMethod: 'AI应答',
                    responseContent: 'FusionSphere提供统一的虚拟化管理平台...',
                    source: 'GBBS',
                    index: 'GBBS-004',
                    remark: '',
                    updateUser: '王五（345678）',
                    updateTime: '2024-01-15 15:10'
                },
                // 新增条目 (共25个条目补齐到30个)
                {
                    id: 6,
                    taskId: 1,
                    code: 'CODE003',
                    description: '安全防护机制要求，包括身份认证、访问控制、数据encryption等',
                    product: '云安全服务',
                    tags: ['安全', '防护'],
                    status: '已应答',
                    satisfaction: 'FC',
                    assignee: '李四（789012）',
                    responseMethod: 'AI应答',
                    responseContent: '云安全服务提供全方位的安全防护...',
                    source: 'GBBS',
                    index: 'GBBS-005',
                    remark: '',
                    updateUser: '李四（789012）',
                    updateTime: '2024-01-16 09:30'
                },
                {
                    id: 7,
                    taskId: 1,
                    code: 'CODE003',
                    description: '安全防护机制要求，包括身份认证、访问控制、数据encryption等',
                    product: '华为云Stack',
                    tags: ['安全', '认证'],
                    status: '应答中',
                    satisfaction: null,
                    assignee: '赵六（567890）',
                    responseMethod: 'AI应答',
                    responseContent: null,
                    source: 'GBBS',
                    index: null,
                    remark: '',
                    updateUser: '赵六（567890）',
                    updateTime: '2024-01-16 10:15'
                },
                {
                    id: 8,
                    taskId: 1,
                    code: 'CODE004',
                    description: '监控运维管理能力，需要支持系统监控、日志分析、告警通知等功能',
                    product: '华为云Stack',
                    tags: ['监控', '运维'],
                    status: '已应答',
                    satisfaction: 'PC',
                    assignee: '孙七（234567）',
                    responseMethod: '手工应答',
                    responseContent: '华为云Stack提供完整的监控运维能力...',
                    source: 'GBBS',
                    index: 'GBBS-006',
                    remark: '需要补充第三方集成',
                    updateUser: '孙七（234567）',
                    updateTime: '2024-01-16 14:45'
                },
                {
                    id: 9,
                    taskId: 1,
                    code: 'CODE004',
                    description: '监控运维管理能力，需要支持系统监控、日志分析、告警通知等功能',
                    product: 'FusionSphere',
                    tags: ['监控', '日志'],
                    status: '已应答',
                    satisfaction: 'FC',
                    assignee: '孙七（234567）',
                    responseMethod: 'AI应答',
                    responseContent: 'FusionSphere提供专业的监控运维解决方案...',
                    source: 'GBBS',
                    index: 'GBBS-007',
                    remark: '',
                    updateUser: '孙七（234567）',
                    updateTime: '2024-01-16 16:20'
                },
                {
                    id: 10,
                    taskId: 1,
                    code: 'CODE005',
                    description: '数据备份与恢复能力，包括定期备份、灾难恢复、数据迁移等',
                    product: '华为云Stack',
                    tags: ['备份', '恢复'],
                    status: '已应答',
                    satisfaction: 'FC',
                    assignee: '周八（345678）',
                    responseMethod: 'AI应答',
                    responseContent: '华为云Stack提供可靠的数据备份恢复方案...',
                    source: 'GBBS',
                    index: 'GBBS-008',
                    remark: '',
                    updateUser: '周八（345678）',
                    updateTime: '2024-01-17 08:30'
                },
                {
                    id: 11,
                    taskId: 1,
                    code: 'CODE005',
                    description: '数据备份与恢复能力，包括定期备份、灾难恢复、数据迁移等',
                    product: 'FusionSphere',
                    tags: ['备份', '灾难恢复'],
                    status: '已应答',
                    satisfaction: 'PC',
                    assignee: '周八（345678）',
                    responseMethod: '手工应答',
                    responseContent: 'FusionSphere支持多层次的数据保护...',
                    source: 'GBBS',
                    index: 'GBBS-009',
                    remark: '需要验证RPO/RTO指标',
                    updateUser: '周八（345678）',
                    updateTime: '2024-01-17 11:45'
                },
                {
                    id: 12,
                    taskId: 1,
                    code: 'CODE006',
                    description: '网络连接与带宽管理，支持多种网络协议和QoS控制',
                    product: '华为云Stack',
                    tags: ['网络', '带宽'],
                    status: '应答中',
                    satisfaction: null,
                    assignee: '吴九（456789）',
                    responseMethod: 'AI应答',
                    responseContent: null,
                    source: 'GBBS',
                    index: null,
                    remark: '',
                    updateUser: '吴九（456789）',
                    updateTime: '2024-01-17 13:20'
                },
                {
                    id: 13,
                    taskId: 1,
                    code: 'CODE006',
                    description: '网络连接与带宽管理，支持多种网络协议和QoS控制',
                    product: 'FusionSphere',
                    tags: ['网络', 'QoS'],
                    status: '已应答',
                    satisfaction: 'FC',
                    assignee: '吴九（456789）',
                    responseMethod: 'AI应答',
                    responseContent: 'FusionSphere提供灵活的网络管理能力...',
                    source: 'GBBS',
                    index: 'GBBS-010',
                    remark: '',
                    updateUser: '吴九（456789）',
                    updateTime: '2024-01-17 15:35'
                },
                {
                    id: 14,
                    taskId: 1,
                    code: 'CODE007',
                    description: '容器编排与管理，支持Kubernetes集群的部署和管理',
                    product: '华为云Stack',
                    tags: ['容器', 'Kubernetes'],
                    status: '已应答',
                    satisfaction: 'FC',
                    assignee: '郑十（567890）',
                    responseMethod: 'AI应答',
                    responseContent: '华为云Stack完全支持Kubernetes容器编排...',
                    source: 'GBBS',
                    index: 'GBBS-011',
                    remark: '',
                    updateUser: '郑十（567890）',
                    updateTime: '2024-01-18 09:10'
                },
                {
                    id: 15,
                    taskId: 1,
                    code: 'CODE007',
                    description: '容器编排与管理，支持Kubernetes集群的部署和管理',
                    product: 'FusionSphere',
                    tags: ['容器', '编排'],
                    status: '未应答',
                    satisfaction: null,
                    assignee: '郑十（567890）',
                    responseMethod: null,
                    responseContent: null,
                    source: 'GBBS',
                    index: null,
                    remark: null,
                    updateUser: null,
                    updateTime: null
                },
                {
                    id: 16,
                    taskId: 1,
                    code: 'CODE008',
                    description: '自动化部署与运维，支持CI/CD流水线和自动化脚本',
                    product: '华为云Stack',
                    tags: ['自动化', 'CI/CD'],
                    status: '已应答',
                    satisfaction: 'PC',
                    assignee: '钱一（678901）',
                    responseMethod: '手工应答',
                    responseContent: '华为云Stack集成了DevOps工具链...',
                    source: 'GBBS',
                    index: 'GBBS-012',
                    remark: '需要第三方工具集成',
                    updateUser: '钱一（678901）',
                    updateTime: '2024-01-18 13:25'
                },
                {
                    id: 17,
                    taskId: 1,
                    code: 'CODE009',
                    description: '多租户隔离与权限管理，支持企业级的租户管理',
                    product: '华为云Stack',
                    tags: ['多租户', '权限'],
                    status: '已应答',
                    satisfaction: 'FC',
                    assignee: '孙二（789012）',
                    responseMethod: 'AI应答',
                    responseContent: '华为云Stack提供完善的多租户隔离机制...',
                    source: 'GBBS',
                    index: 'GBBS-013',
                    remark: '',
                    updateUser: '孙二（789012）',
                    updateTime: '2024-01-18 16:40'
                },
                {
                    id: 18,
                    taskId: 1,
                    code: 'CODE009',
                    description: '多租户隔离与权限管理，支持企业级的租户管理',
                    product: 'FusionSphere',
                    tags: ['多租户', '隔离'],
                    status: '应答中',
                    satisfaction: null,
                    assignee: '孙二（789012）',
                    responseMethod: 'AI应答',
                    responseContent: null,
                    source: 'GBBS',
                    index: null,
                    remark: '',
                    updateUser: '孙二（789012）',
                    updateTime: '2024-01-19 08:15'
                },
                {
                    id: 19,
                    taskId: 1,
                    code: 'CODE010',
                    description: '性能监控与优化，支持资源使用情况监控和性能调优',
                    product: '华为云Stack',
                    tags: ['性能', '监控'],
                    status: '已应答',
                    satisfaction: 'FC',
                    assignee: '李三（890123）',
                    responseMethod: 'AI应答',
                    responseContent: '华为云Stack提供全方位的性能监控...',
                    source: 'GBBS',
                    index: 'GBBS-014',
                    remark: '',
                    updateUser: '李三（890123）',
                    updateTime: '2024-01-19 10:50'
                },
                {
                    id: 20,
                    taskId: 1,
                    code: 'CODE010',
                    description: '性能监控与优化，支持资源使用情况监控和性能调优',
                    product: 'FusionSphere',
                    tags: ['性能', '优化'],
                    status: '已应答',
                    satisfaction: 'PC',
                    assignee: '李三（890123）',
                    responseMethod: '手工应答',
                    responseContent: 'FusionSphere提供详细的性能分析报告...',
                    source: 'GBBS',
                    index: 'GBBS-015',
                    remark: '性能优化建议需要完善',
                    updateUser: '李三（890123）',
                    updateTime: '2024-01-19 14:30'
                },
                {
                    id: 21,
                    taskId: 1,
                    code: 'CODE011',
                    description: 'API接口与集成能力，支持RESTful API和第三方系统集成',
                    product: '华为云Stack',
                    tags: ['API', '集成'],
                    status: '已应答',
                    satisfaction: 'FC',
                    assignee: '张四（901234）',
                    responseMethod: 'AI应答',
                    responseContent: '华为云Stack提供标准的RESTful API...',
                    source: 'GBBS',
                    index: 'GBBS-016',
                    remark: '',
                    updateUser: '张四（901234）',
                    updateTime: '2024-01-20 09:20'
                },
                {
                    id: 22,
                    taskId: 1,
                    code: 'CODE012',
                    description: '高可用性与负载均衡，支持集群部署和故障转移',
                    product: '华为云Stack',
                    tags: ['高可用', '负载均衡'],
                    status: '已应答',
                    satisfaction: 'FC',
                    assignee: '王五（012345）',
                    responseMethod: 'AI应答',
                    responseContent: '华为云Stack提供企业级的高可用方案...',
                    source: 'GBBS',
                    index: 'GBBS-017',
                    remark: '',
                    updateUser: '王五（012345）',
                    updateTime: '2024-01-20 11:55'
                },
                {
                    id: 23,
                    taskId: 1,
                    code: 'CODE012',
                    description: '高可用性与负载均衡，支持集群部署和故障转移',
                    product: 'FusionSphere',
                    tags: ['高可用', '集群'],
                    status: '未应答',
                    satisfaction: null,
                    assignee: '王五（012345）',
                    responseMethod: null,
                    responseContent: null,
                    source: 'GBBS',
                    index: null,
                    remark: null,
                    updateUser: null,
                    updateTime: null
                },
                {
                    id: 24,
                    taskId: 1,
                    code: 'CODE013',
                    description: '数据分析与报表功能，支持业务数据的统计分析',
                    product: '华为云Stack',
                    tags: ['数据分析', '报表'],
                    status: '应答中',
                    satisfaction: null,
                    assignee: '赵六（123456）',
                    responseMethod: 'AI应答',
                    responseContent: null,
                    source: 'GBBS',
                    index: null,
                    remark: '',
                    updateUser: '赵六（123456）',
                    updateTime: '2024-01-20 15:40'
                },
                {
                    id: 25,
                    taskId: 1,
                    code: 'CODE014',
                    description: '弹性伸缩与自动扩容，根据负载自动调整资源规模',
                    product: '华为云Stack',
                    tags: ['弹性伸缩', '自动扩容'],
                    status: '未应答',
                    satisfaction: null,
                    assignee: '孙七（234567）',
                    responseMethod: null,
                    responseContent: null,
                    source: 'GBBS',
                    index: null,
                    remark: null,
                    updateUser: null,
                    updateTime: null
                }
            ].concat(
                // 为其他29个任务生成条目数据
                (() => {
                    const additionalItems = [];
                    let itemIdCounter = 26;
                    
                    // 为任务ID 2-30 各生成3-8个条目
                    for (let taskId = 2; taskId <= 30; taskId++) {
                        const itemCount = Math.floor(Math.random() * 6) + 3; // 3-8个条目
                        
                        for (let i = 0; i < itemCount; i++) {
                            const codeNum = String(i + 1).padStart(3, '0');
                            const products = ['华为云Stack', 'FusionSphere', '云安全服务', 'FusionCompute'];
                            const statuses = ['未应答', '应答中', '已应答'];
                            const satisfactions = [null, null, 'FC', 'PC', 'NC'];
                            const assignees = ['张三（123456）', '李四（789012）', '王五（345678）', '赵六（567890）'];
                            
                            const randomProduct = products[Math.floor(Math.random() * products.length)];
                            const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
                            const randomSatisfaction = randomStatus === '已应答' ? satisfactions[Math.floor(Math.random() * 3) + 2] : null;
                            const randomAssignee = assignees[Math.floor(Math.random() * assignees.length)];
                            
                            additionalItems.push({
                                id: itemIdCounter++,
                                taskId: taskId,
                                code: `CODE${codeNum}`,
                                description: `${randomProduct}相关技术需求条目${i + 1}，包含功能特性、性能指标、兼容性等要求`,
                                product: randomProduct,
                                tags: ['技术需求', '产品特性'],
                                status: randomStatus,
                                satisfaction: randomSatisfaction,
                                assignee: randomAssignee,
                                responseMethod: randomStatus === '已应答' ? (Math.random() > 0.5 ? 'AI应答' : '手工应答') : null,
                                responseContent: randomStatus === '已应答' ? `${randomProduct}相关的技术应答内容...` : null,
                                source: 'GBBS',
                                index: randomStatus === '已应答' ? `GBBS-${String(itemIdCounter).padStart(3, '0')}` : null,
                                remark: '',
                                updateUser: randomStatus !== '未应答' ? randomAssignee : null,
                                updateTime: randomStatus !== '未应答' ? new Date().toLocaleString('zh-CN') : null
                            });
                        }
                    }
                    
                    return additionalItems;
                })()
            ),
            currentTask: null,
            personalTaskCounter: 0,
            selectedItems: new Set(), // 记忆选中状态
            selectAllState: false // 记忆全选状态
        };

        // 全局变量
        let autoRefreshInterval;
        let refreshCountdown = 5;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('SOC智能应答系统加载完成');
            
            // 初始化所有任务的条目计数
            initializeTaskCounts();
            
            renderTaskList();
            
            // 添加标签输入功能
            setupTagInput();
            
            // 初始化列筛选
            initColumnFilter();
        });

        // 设置标签输入功能
        function setupTagInput() {
            // 单条录入弹窗的标签输入
            const addItemTagInput = document.querySelector('#addItemModal input[placeholder="输入标签后按回车添加"]');
            if (addItemTagInput) {
                addItemTagInput.addEventListener('keypress', function(event) {
                    if (event.key === 'Enter') {
                        event.preventDefault();
                        const tagValue = this.value.trim();
                        if (tagValue) {
                            const tagList = this.parentNode.querySelector('.tag-list');
                            const tagElement = document.createElement('span');
                            tagElement.className = 'tag primary';
                            tagElement.innerHTML = `${tagValue} <span onclick="this.parentNode.remove()" style="cursor: pointer; margin-left: 4px;">×</span>`;
                            tagList.appendChild(tagElement);
                            this.value = '';
                        }
                    }
                });
            }
            
            // 批量标签输入
            const batchTagInput = document.getElementById('batchTagInput');
            if (batchTagInput) {
                batchTagInput.addEventListener('keypress', function(event) {
                    if (event.key === 'Enter') {
                        event.preventDefault();
                        const tagValue = this.value.trim();
                        if (tagValue) {
                            const tagList = document.getElementById('batchTagList');
                            const tagElement = document.createElement('span');
                            tagElement.className = 'tag primary';
                            tagElement.innerHTML = `${tagValue} <span onclick="this.parentNode.remove()" style="cursor: pointer; margin-left: 4px;">×</span>`;
                            tagList.appendChild(tagElement);
                            this.value = '';
                        }
                    }
                });
            }
        }

        // 初始化所有任务的条目计数
        function initializeTaskCounts() {
            globalData.tasks.forEach(task => {
                const taskItems = globalData.items.filter(item => item.taskId === task.id);
                task.itemCount = taskItems.length;
                task.completedCount = taskItems.filter(item => item.status === '已应答').length;
                
                // 计算满足度
                const satisfiedItems = taskItems.filter(item => item.satisfaction === 'FC');
                const partiallySatisfiedItems = taskItems.filter(item => item.satisfaction === 'PC');
                const totalRatedItems = taskItems.filter(item => item.satisfaction);
                
                if (totalRatedItems.length > 0) {
                    task.satisfaction = Math.round(((satisfiedItems.length * 1 + partiallySatisfiedItems.length * 0.5) / totalRatedItems.length) * 100);
                } else {
                    task.satisfaction = 0;
                }
                
                // 更新任务状态
                if (task.completedCount === 0) {
                    task.status = '未开始';
                } else if (task.completedCount === task.itemCount) {
                    task.status = '已完成';
                } else {
                    task.status = '进行中';
                }
            });
            
            console.log('✅ 所有任务的条目计数已初始化');
        }

        // 渲染任务列表
        function renderTaskList() {
            const tbody = document.getElementById('taskTableBody');
            const paginationInfo = document.getElementById('paginationInfo');
            
            tbody.innerHTML = '';
            
            // 任务排序：优先展示正在处理或刚处理完的任务
            const sortedTasks = [...globalData.tasks].sort((a, b) => {
                // 1. 按状态优先级排序（进行中 > 已完成 > 未开始）
                const statusPriority = {
                    '进行中': 3,
                    '已完成': 2,
                    '未开始': 1
                };
                
                const statusDiff = statusPriority[b.status] - statusPriority[a.status];
                if (statusDiff !== 0) return statusDiff;
                
                // 2. 同状态下按更新时间倒序排序（最新的在前面）
                const timeA = new Date(a.updateTime || a.createTime);
                const timeB = new Date(b.updateTime || b.createTime);
                return timeB - timeA;
            });
            
            sortedTasks.forEach(task => {
                const progress = task.itemCount > 0 ? Math.round((task.completedCount / task.itemCount) * 100) : 0;
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${task.code}</td>
                    <td>${task.name}${task.isPersonal ? ' <span class="tag primary">个人任务</span>' : ''}</td>
                    <td>${task.country}</td>
                    <td>${task.customer}</td>
                    <td>${task.project}</td>
                    <td>${task.itemCount}</td>
                    <td>
                        <div class="progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${progress}%"></div>
                            </div>
                            <span class="progress-text">${task.completedCount}/${task.itemCount}</span>
                        </div>
                    </td>
                    <td>${task.satisfaction}%</td>
                    <td><span class="status-tag ${getStatusClass(task.status)}">${task.status}</span></td>
                    <td>
                        <div class="table-actions">
                            <button class="btn btn-sm" onclick="openTaskDetail(${task.id})">应答</button>
                            <button class="btn btn-sm" onclick="editTask(${task.id})">编辑</button>
                            <button class="btn btn-sm" onclick="copyTask(${task.id})">复制</button>
                            ${!task.isPersonal ? `<button class="btn btn-sm" onclick="deleteTask(${task.id})">删除</button>` : ''}
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
            
            paginationInfo.textContent = `共 ${globalData.tasks.length} 条记录，第 1/1 页`;
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch(status) {
                case '进行中': return 'processing';
                case '已完成': return 'completed';
                case '未开始': return 'pending';
                default: return 'pending';
            }
        }

        // 渲染条目列表
        function renderItemList(taskId = null) {
            const tbody = document.getElementById('itemTableBody');
            const paginationInfo = document.getElementById('itemPaginationInfo');
            
            if (!tbody) {
                console.error('❌ 找不到itemTableBody元素');
                return;
            }
            
            let items = globalData.items;
            if (taskId) {
                // 确保数据类型一致进行比较
                const targetTaskId = parseInt(taskId);
                items = items.filter(item => item.taskId === targetTaskId);
            }
            
            tbody.innerHTML = '';
            
            if (items.length === 0) {
                tbody.innerHTML = '<tr><td colspan="17" style="text-align: center; padding: 40px; color: #999;">暂无条目数据</td></tr>';
                if (paginationInfo) {
                    paginationInfo.textContent = '共 0 条记录，第 1/1 页';
                }
                return;
            }
            
            // 按编号分组
            const groupedItems = {};
            items.forEach(item => {
                if (!groupedItems[item.code]) {
                    groupedItems[item.code] = [];
                }
                groupedItems[item.code].push(item);
            });
            
            let rowIndex = 1;
            Object.keys(groupedItems).forEach(code => {
                const codeItems = groupedItems[code];
                codeItems.forEach((item, index) => {
                    const row = document.createElement('tr');
                    row.className = index === 0 ? 'item-group first-row' : 'item-group';
                    row.setAttribute('data-item', code);
                    
                    row.innerHTML = `
                        <td><input type="checkbox" name="itemSelect" value="${item.id}" ${item.status === '应答中' ? 'disabled' : ''}></td>
                        <td>${rowIndex}</td>
                        ${index === 0 ? `<td rowspan="${codeItems.length}">${code}</td>` : ''}
                        ${index === 0 ? `<td rowspan="${codeItems.length}">${item.description}</td>` : ''}
                        <td>${item.product}</td>
                        <td>${item.tags.map(tag => `<span class="tag primary">${tag}</span>`).join('')}</td>
                        <td><span class="status-tag ${getStatusClass(item.status)}">${item.status}</span></td>
                        <td>${item.satisfaction ? `<span class="satisfaction-tag ${item.satisfaction.toLowerCase()}">${item.satisfaction}</span>` : '-'}</td>
                        <td>${item.assignee || '-'}</td>
                        <td>${item.responseMethod || '-'}</td>
                        <td>${item.responseContent ? truncateText(item.responseContent, 30) : '-'}</td>
                        <td>${item.source || '-'}</td>
                        <td>${item.index ? `<a href="#" target="_blank">${item.index}</a>` : '-'}</td>
                        <td>${item.remark || '-'}</td>
                        <td>${item.updateUser || '-'}</td>
                        <td>${item.updateTime || '-'}</td>
                        <td>
                            <div class="table-actions">
                                ${getItemActionButtons(item)}
                            </div>
                        </td>
                    `;
                    tbody.appendChild(row);
                    rowIndex++;
                });
            });
            
            if (paginationInfo) {
                paginationInfo.textContent = `共 ${items.length} 条记录，第 1/1 页`;
            }
            
            // 表格渲染完成后，自动应用当前的列筛选设置 - 修复联动问题
            setTimeout(() => {
                // 清除之前的列索引标记，确保重新计算
                const rows = document.querySelectorAll('#itemTableBody tr');
                rows.forEach(row => {
                    row.removeAttribute('data-column-processed');
                    const cells = row.querySelectorAll('td');
                    cells.forEach(cell => {
                        cell.removeAttribute('data-column-index');
                    });
                });
                
                applyCurrentColumnFilter();
            }, 50); // 增加延迟确保DOM完全更新
        }

        // 文本截断
        function truncateText(text, maxLength) {
            if (text && text.length > maxLength) {
                return text.substring(0, maxLength) + '...';
            }
            return text || '';
        }

        // 渲染统计数据
        function renderStats(taskId = null, assigneeFilter = null) {
            const statsGrid = document.getElementById('statsGrid');
            const productStatsBody = document.getElementById('productStatsBody');
            
            if (!statsGrid || !productStatsBody) return;
            
            let items = globalData.items;
            if (taskId) {
                items = items.filter(item => item.taskId === taskId);
            }
            
            // 根据指派人筛选
            if (assigneeFilter) {
                items = items.filter(item => {
                    return item.assignee && item.assignee.includes(assigneeFilter);
                });
            }
            
            // 计算总体统计
            const totalCount = items.length;
            const completedCount = items.filter(item => item.status === '已应答').length;
            const processingCount = items.filter(item => item.status === '应答中').length;
            const pendingCount = items.filter(item => item.status === '未应答').length;
            const fcCount = items.filter(item => item.satisfaction === 'FC').length;
            const pcCount = items.filter(item => item.satisfaction === 'PC').length;
            const ncCount = items.filter(item => item.satisfaction === 'NC').length;
            const completionRate = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0;
            const satisfactionRate = (fcCount + pcCount + ncCount) > 0 ? Math.round(((fcCount * 1 + pcCount * 0.5) / (fcCount + pcCount + ncCount)) * 100) : 0;
            
            statsGrid.innerHTML = `
                <div class="stat-item">
                    <div class="stat-value">${totalCount}</div>
                    <div class="stat-label">总条目数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${completedCount}</div>
                    <div class="stat-label">已应答数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${pendingCount}</div>
                    <div class="stat-label">未应答数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${processingCount}</div>
                    <div class="stat-label">应答中数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${completionRate}%</div>
                    <div class="stat-label">应答完成率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${fcCount}</div>
                    <div class="stat-label">FC</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${pcCount}</div>
                    <div class="stat-label">PC</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${ncCount}</div>
                    <div class="stat-label">NC</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${satisfactionRate}%</div>
                    <div class="stat-label">满足度</div>
                </div>
            `;
            
            // 计算产品维度统计（同样支持指派人筛选）
            const productStats = {};
            items.forEach(item => {
                if (!productStats[item.product]) {
                    productStats[item.product] = {
                        total: 0,
                        completed: 0,
                        fc: 0,
                        pc: 0,
                        nc: 0
                    };
                }
                productStats[item.product].total++;
                if (item.status === '已应答') {
                    productStats[item.product].completed++;
                }
                if (item.satisfaction === 'FC') productStats[item.product].fc++;
                else if (item.satisfaction === 'PC') productStats[item.product].pc++;
                else if (item.satisfaction === 'NC') productStats[item.product].nc++;
            });
            
            productStatsBody.innerHTML = '';
            Object.keys(productStats).forEach(product => {
                const stats = productStats[product];
                const satisfactionRate = (stats.fc + stats.pc + stats.nc) > 0 ? 
                    Math.round(((stats.fc * 1 + stats.pc * 0.5) / (stats.fc + stats.pc + stats.nc)) * 100) : 0;
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${product}</td>
                    <td>${stats.total}</td>
                    <td>${stats.completed}</td>
                    <td>${stats.fc}</td>
                    <td>${stats.pc}</td>
                    <td>${stats.nc}</td>
                    <td>
                        <div class="progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${satisfactionRate}%"></div>
                            </div>
                            <span class="progress-text">${satisfactionRate}%</span>
                        </div>
                    </td>
                `;
                productStatsBody.appendChild(row);
            });
        }

        // 页面切换
        function showPage(pageId) {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.style.display = 'none');
            
            // 显示指定页面
            document.getElementById(pageId).style.display = 'block';
            
            // 更新导航状态
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));
            
            // 查找对应的导航项
            navItems.forEach(item => {
                const text = item.textContent.trim();
                if ((pageId === 'task-management' && text === '任务管理') ||
                    (pageId === 'quick-response' && text === '快捷应答')) {
                    item.classList.add('active');
                }
            });

            // 停止自动刷新
            stopAutoRefresh();
            
            // 如果切换到任务管理页面，重新渲染任务列表
            if (pageId === 'task-management') {
                renderTaskList();
            }
        }

        // 标签页切换
        function showTab(tabId) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.style.display = 'none');
            
            // 显示指定标签页内容
            document.getElementById(tabId).style.display = 'block';
            
            // 更新标签页状态
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');

            // 如果是条目管理页签，启动自动刷新
            if (tabId === 'item-management') {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
            }
            
            // 如果是数据分析页签，渲染统计数据
            if (tabId === 'data-analysis' && globalData.currentTask) {
                // 获取当前选中的指派人筛选条件
                const assigneeSelect = document.querySelector('#data-analysis select[onchange="filterAnalysisData()"]');
                const selectedAssignee = assigneeSelect ? assigneeSelect.value : null;
                
                // 渲染统计数据，应用筛选条件
                renderStats(globalData.currentTask.id, selectedAssignee);
                
                console.log('切换到数据分析页面，当前任务:', globalData.currentTask.id, '筛选条件:', selectedAssignee);
            }
        }

        // 自动刷新功能
        function startAutoRefresh() {
            const refreshDisplay = document.getElementById('autoRefresh');
            const timerDisplay = document.getElementById('refreshTimer');
            
            refreshDisplay.style.display = 'block';
            refreshCountdown = 5;
            
            autoRefreshInterval = setInterval(() => {
                refreshCountdown--;
                timerDisplay.textContent = refreshCountdown;
                
                if (refreshCountdown <= 0) {
                    refreshItemList();
                    refreshCountdown = 5;
                }
            }, 1000);
        }

        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
            document.getElementById('autoRefresh').style.display = 'none';
        }

        function refreshItemList() {
            console.log('自动刷新条目列表');
            
            // 保存当前勾选状态到全局状态
            const selectedCheckboxes = document.querySelectorAll('input[name="itemSelect"]:checked');
            globalData.selectedItems.clear();
            selectedCheckboxes.forEach(checkbox => {
                globalData.selectedItems.add(checkbox.value);
            });
            globalData.selectAllState = document.getElementById('selectAll')?.checked || false;
            
            if (globalData.currentTask) {
                renderItemList(globalData.currentTask.id);
                
                // 恢复勾选状态
                setTimeout(() => {
                    // 恢复全选状态
                    const selectAllCheckbox = document.getElementById('selectAll');
                    if (selectAllCheckbox) {
                        selectAllCheckbox.checked = globalData.selectAllState;
                    }
                    
                    // 恢复单个条目的勾选状态
                    globalData.selectedItems.forEach(itemId => {
                        const checkbox = document.querySelector(`input[name="itemSelect"][value="${itemId}"]`);
                        if (checkbox) {
                            checkbox.checked = true;
                        }
                    });
                    
                    // 自动应用列筛选设置 - 修复联动问题
                    applyCurrentColumnFilter();
                }, 100); // 增加延迟确保DOM完全更新
            }
        }

        // 全选功能
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const itemSelects = document.querySelectorAll('input[name="itemSelect"]');
            
            itemSelects.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }

        // 查询功能
        function queryItems() {
            console.log('查询条目');
            
            const filters = {
                code: document.getElementById('queryCode').value,
                description: document.getElementById('queryDescription').value,
                product: document.getElementById('queryProduct').value,
                status: document.getElementById('queryStatus').value,
                tag: document.getElementById('queryTag').value,
                satisfaction: document.getElementById('querySatisfaction').value,
                assignee: document.getElementById('queryAssignee').value
            };
            
            // 应用筛选逻辑
            let filteredItems = globalData.items.filter(item => {
                if (globalData.currentTask && item.taskId !== globalData.currentTask.id) return false;
                if (filters.code && !item.code.includes(filters.code)) return false;
                if (filters.description && !item.description.includes(filters.description)) return false;
                if (filters.product && item.product !== filters.product) return false;
                if (filters.status && item.status !== filters.status) return false;
                if (filters.tag && !item.tags.some(tag => tag.includes(filters.tag))) return false;
                if (filters.satisfaction && item.satisfaction !== filters.satisfaction) return false;
                if (filters.assignee && (!item.assignee || !item.assignee.includes(filters.assignee))) return false;
                return true;
            });
            
            // 重新渲染筛选后的结果
            renderFilteredItems(filteredItems);
        }

        function renderFilteredItems(items) {
            const tbody = document.getElementById('itemTableBody');
            const paginationInfo = document.getElementById('itemPaginationInfo');
            
            tbody.innerHTML = '';
            
            if (items.length === 0) {
                tbody.innerHTML = '<tr><td colspan="17" style="text-align: center; padding: 40px; color: #999;">没有符合条件的数据</td></tr>';
                if (paginationInfo) {
                    paginationInfo.textContent = '共 0 条记录，第 1/1 页';
                }
                return;
            }
            
            // 按编号分组
            const groupedItems = {};
            items.forEach(item => {
                if (!groupedItems[item.code]) {
                    groupedItems[item.code] = [];
                }
                groupedItems[item.code].push(item);
            });
            
            let rowIndex = 1;
            Object.keys(groupedItems).forEach(code => {
                const codeItems = groupedItems[code];
                codeItems.forEach((item, index) => {
                    const row = document.createElement('tr');
                    row.className = index === 0 ? 'item-group first-row' : 'item-group';
                    row.setAttribute('data-item', code);
                    
                    row.innerHTML = `
                        <td><input type="checkbox" name="itemSelect" value="${item.id}" ${item.status === '应答中' ? 'disabled' : ''}></td>
                        <td>${rowIndex}</td>
                        ${index === 0 ? `<td rowspan="${codeItems.length}">${code}</td>` : ''}
                        ${index === 0 ? `<td rowspan="${codeItems.length}">${item.description}</td>` : ''}
                        <td>${item.product}</td>
                        <td>${item.tags.map(tag => `<span class="tag primary">${tag}</span>`).join('')}</td>
                        <td><span class="status-tag ${getStatusClass(item.status)}">${item.status}</span></td>
                        <td>${item.satisfaction ? `<span class="satisfaction-tag ${item.satisfaction.toLowerCase()}">${item.satisfaction}</span>` : '-'}</td>
                        <td>${item.assignee || '-'}</td>
                        <td>${item.responseMethod || '-'}</td>
                        <td>${item.responseContent ? truncateText(item.responseContent, 30) : '-'}</td>
                        <td>${item.source || '-'}</td>
                        <td>${item.index ? `<a href="#" target="_blank">${item.index}</a>` : '-'}</td>
                        <td>${item.remark || '-'}</td>
                        <td>${item.updateUser || '-'}</td>
                        <td>${item.updateTime || '-'}</td>
                        <td>
                            <div class="table-actions">
                                ${getItemActionButtons(item)}
                            </div>
                        </td>
                    `;
                    tbody.appendChild(row);
                    rowIndex++;
                });
            });
            
            if (paginationInfo) {
                paginationInfo.textContent = `共 ${items.length} 条记录，第 1/1 页`;
            }
            
            // 渲染完成后，应用当前的列筛选设置
            setTimeout(() => {
                // 清除之前的列索引标记，确保重新计算
                const rows = document.querySelectorAll('#itemTableBody tr');
                rows.forEach(row => {
                    row.removeAttribute('data-column-processed');
                    const cells = row.querySelectorAll('td');
                    cells.forEach(cell => {
                        cell.removeAttribute('data-column-index');
                    });
                });
                
                applyCurrentColumnFilter();
            }, 50); // 增加延迟确保DOM完全更新
        }

        function resetQuery() {
            document.getElementById('queryCode').value = '';
            document.getElementById('queryDescription').value = '';
            document.getElementById('queryProduct').value = '';
            document.getElementById('queryStatus').value = '';
            document.getElementById('queryTag').value = '';
            document.getElementById('querySatisfaction').value = '';
            document.getElementById('queryAssignee').value = '';
            
            // 重新渲染原始数据
            if (globalData.currentTask) {
                renderItemList(globalData.currentTask.id);
            }
        }

        // 批量操作功能 - 按需求要求重新实现
        function startResponse() {
            const selectedItems = getSelectedItems();
            const taskItems = globalData.currentTask ? globalData.items.filter(item => item.taskId === globalData.currentTask.id) : [];
            // 只处理未应答的条目
            const availableItems = taskItems.filter(item => item.status === '未应答');
            
            if (selectedItems.length === 0) {
                if (availableItems.length === 0) {
                    alert('没有可操作的条目（排除应答中的条目）');
                    return;
                }
                if (confirm(`是否启动所有未应答的 ${availableItems.length} 个条目的应答？`)) {
                    console.log('启动所有未应答条目应答');
                    
                    // 更新状态为应答中
                    availableItems.forEach(item => {
                        item.status = '应答中';
                        item.responseMethod = 'AI应答';
                        item.updateTime = new Date().toLocaleString('zh-CN');
                    });
                    
                    renderItemList(globalData.currentTask.id);
                    updateTaskProgress(globalData.currentTask.id);
                    renderTaskList();
                    
                    alert(`开始应答 ${availableItems.length} 个条目...`);
                }
            } else {
                console.log('启动选中条目应答:', selectedItems);
                
                // 只处理选中的且状态为未应答的条目
                let updatedCount = 0;
                selectedItems.forEach(itemId => {
                    const item = globalData.items.find(i => i.id == itemId);
                    if (item && item.status === '未应答') {
                        item.status = '应答中';
                        item.responseMethod = 'AI应答';
                        item.updateTime = new Date().toLocaleString('zh-CN');
                        updatedCount++;
                    }
                });
                
                if (updatedCount === 0) {
                    alert('选中的条目中没有可开始应答的条目（只能对未应答状态的条目开始应答）');
                    return;
                }
                
                if (globalData.currentTask) {
                    renderItemList(globalData.currentTask.id);
                    updateTaskProgress(globalData.currentTask.id);
                    renderTaskList();
                }
                
                alert(`开始应答选中的 ${updatedCount} 个条目...`);
            }
        }

        function batchDelete() {
            const selectedItems = getSelectedItems();
            const taskItems = globalData.currentTask ? globalData.items.filter(item => item.taskId === globalData.currentTask.id) : [];
            
            if (selectedItems.length === 0) {
                if (taskItems.length === 0) {
                    alert('没有可删除的条目');
                    return;
                }
                const message = `是否要删除所有 ${taskItems.length} 个条目？`;
                if (confirm(message)) {
                    console.log('批量删除所有条目');
                    
                    // 删除所有条目
                    globalData.items = globalData.items.filter(item => item.taskId !== globalData.currentTask.id);
                    updateTaskItemCount(globalData.currentTask.id);
                    renderItemList(globalData.currentTask.id);
                    renderTaskList();
                    
                    alert('所有条目删除成功！');
                }
            } else {
                const message = `是否要删除选中的 ${selectedItems.length} 个条目？`;
                if (confirm(message)) {
                    console.log('批量删除选中条目:', selectedItems);
                    
                    // 删除选中的条目
                    selectedItems.forEach(itemId => {
                        const index = globalData.items.findIndex(i => i.id == itemId);
                        if (index > -1) {
                            globalData.items.splice(index, 1);
                        }
                    });
                    
                    if (globalData.currentTask) {
                        updateTaskItemCount(globalData.currentTask.id);
                        renderItemList(globalData.currentTask.id);
                        renderTaskList();
                    }
                    
                    alert(`${selectedItems.length} 个条目删除成功！`);
                }
            }
        }

        function batchAddTag() {
            const selectedItems = getSelectedItems();
            if (selectedItems.length === 0 && globalData.currentTask) {
                const taskItems = globalData.items.filter(item => item.taskId === globalData.currentTask.id);
                if (taskItems.length === 0) {
                    alert('没有可操作的条目');
                    return;
                }
            }
            
            showBatchAddTagModal();
        }

        function showBatchAddTagModal() {
            document.getElementById('batchAddTagModal').classList.add('show');
            document.getElementById('batchTagList').innerHTML = '';
            document.getElementById('batchTagInput').value = '';
        }

        function hideBatchAddTagModal() {
            document.getElementById('batchAddTagModal').classList.remove('show');
        }

        function confirmBatchAddTag() {
            const selectedItems = getSelectedItems();
            const tagElements = document.querySelectorAll('#batchTagList .tag');
            const tags = Array.from(tagElements).map(el => el.textContent.replace(' ×', '').trim());
            
            if (tags.length === 0) {
                alert('请先添加标签');
                return;
            }
            
            if (selectedItems.length === 0) {
                if (confirm(`是否给所有条目添加标签？`)) {
                    if (globalData.currentTask) {
                        globalData.items.forEach(item => {
                            if (item.taskId === globalData.currentTask.id) {
                                tags.forEach(tag => {
                                    if (!item.tags.includes(tag)) {
                                        item.tags.push(tag);
                                    }
                                });
                                item.updateTime = new Date().toLocaleString('zh-CN');
                            }
                        });
                        renderItemList(globalData.currentTask.id);
                    }
                    hideBatchAddTagModal();
                    alert('标签添加成功！');
                }
            } else {
                selectedItems.forEach(itemId => {
                    const item = globalData.items.find(i => i.id == itemId);
                    if (item) {
                        tags.forEach(tag => {
                            if (!item.tags.includes(tag)) {
                                item.tags.push(tag);
                            }
                        });
                        item.updateTime = new Date().toLocaleString('zh-CN');
                    }
                });
                
                if (globalData.currentTask) {
                    renderItemList(globalData.currentTask.id);
                }
                
                hideBatchAddTagModal();
                alert(`已为 ${selectedItems.length} 个条目添加标签`);
            }
        }

        function batchRemoveTag() {
            const selectedItems = getSelectedItems();
            let itemsToProcess = [];
            
            if (selectedItems.length === 0) {
                if (globalData.currentTask) {
                    itemsToProcess = globalData.items.filter(item => item.taskId === globalData.currentTask.id);
                }
            } else {
                itemsToProcess = globalData.items.filter(item => selectedItems.includes(item.id.toString()));
            }
            
            if (itemsToProcess.length === 0) {
                alert('没有可操作的条目');
                return;
            }
            
            // 收集所有标签
            const allTags = new Set();
            itemsToProcess.forEach(item => {
                item.tags.forEach(tag => allTags.add(tag));
            });
            
            if (allTags.size === 0) {
                alert('选中条目没有标签可移除');
                return;
            }
            
            // 显示标签选择弹窗
            const tagsList = document.getElementById('availableTagsList');
            tagsList.innerHTML = '';
            Array.from(allTags).forEach(tag => {
                const div = document.createElement('div');
                div.className = 'checkbox';
                div.innerHTML = `
                    <input type="checkbox" id="tag_${tag}" value="${tag}">
                    <label for="tag_${tag}">${tag}</label>
                `;
                tagsList.appendChild(div);
            });
            
            showBatchRemoveTagModal();
        }

        function showBatchRemoveTagModal() {
            document.getElementById('batchRemoveTagModal').classList.add('show');
        }

        function hideBatchRemoveTagModal() {
            document.getElementById('batchRemoveTagModal').classList.remove('show');
        }

        function confirmBatchRemoveTag() {
            const selectedTags = [];
            document.querySelectorAll('#availableTagsList input[type="checkbox"]:checked').forEach(checkbox => {
                selectedTags.push(checkbox.value);
            });
            
            if (selectedTags.length === 0) {
                alert('请选择要删除的标签');
                return;
            }
            
            const selectedItems = getSelectedItems();
            const message = selectedItems.length === 0 ? 
                `确认删除全部条目的 ${selectedTags.join(', ')} 标签？` : 
                `确认删除勾选的条目的 ${selectedTags.join(', ')} 标签？`;
            
            if (confirm(message)) {
                if (selectedItems.length === 0) {
                    if (globalData.currentTask) {
                        globalData.items.forEach(item => {
                            if (item.taskId === globalData.currentTask.id) {
                                selectedTags.forEach(tag => {
                                    const tagIndex = item.tags.indexOf(tag);
                                    if (tagIndex > -1) {
                                        item.tags.splice(tagIndex, 1);
                                        item.updateTime = new Date().toLocaleString('zh-CN');
                                    }
                                });
                            }
                        });
                        renderItemList(globalData.currentTask.id);
                    }
                } else {
                    selectedItems.forEach(itemId => {
                        const item = globalData.items.find(i => i.id == itemId);
                        if (item) {
                            selectedTags.forEach(tag => {
                                const tagIndex = item.tags.indexOf(tag);
                                if (tagIndex > -1) {
                                    item.tags.splice(tagIndex, 1);
                                    item.updateTime = new Date().toLocaleString('zh-CN');
                                }
                            });
                        }
                    });
                    
                    if (globalData.currentTask) {
                        renderItemList(globalData.currentTask.id);
                    }
                }
                
                hideBatchRemoveTagModal();
                alert('标签删除成功！');
            }
        }

        function setProduct() {
            const selectedItems = getSelectedItems();
            if (selectedItems.length === 0 && globalData.currentTask) {
                const taskItems = globalData.items.filter(item => item.taskId === globalData.currentTask.id);
                if (taskItems.length === 0) {
                    alert('没有可操作的条目');
                    return;
                }
            }
            
            showSetProductModal();
        }

        function showSetProductModal() {
            document.getElementById('setProductModal').classList.add('show');
            document.getElementById('productSelect').value = '';
        }

        function hideSetProductModal() {
            document.getElementById('setProductModal').classList.remove('show');
        }

        function confirmSetProduct() {
            const selectedProduct = document.getElementById('productSelect').value;
            if (!selectedProduct) {
                alert('请选择产品');
                return;
            }
            
            const selectedItems = getSelectedItems();
            const message = selectedItems.length === 0 ? 
                `拟将产品调整至 ${selectedProduct} 并触发自动应答，请确认是否继续？` : 
                `拟将选中条目的产品调整至 ${selectedProduct} 并触发自动应答，请确认是否继续？`;
            
            if (confirm(message)) {
                if (selectedItems.length === 0) {
                    if (globalData.currentTask) {
                        globalData.items.forEach(item => {
                            if (item.taskId === globalData.currentTask.id) {
                                item.product = selectedProduct;
                                item.updateTime = new Date().toLocaleString('zh-CN');
                            }
                        });
                        renderItemList(globalData.currentTask.id);
                    }
                } else {
                    selectedItems.forEach(itemId => {
                        const item = globalData.items.find(i => i.id == itemId);
                        if (item) {
                            item.product = selectedProduct;
                            item.updateTime = new Date().toLocaleString('zh-CN');
                        }
                    });
                    
                    if (globalData.currentTask) {
                        renderItemList(globalData.currentTask.id);
                    }
                }
                
                hideSetProductModal();
                alert('产品设置成功！');
            }
        }

        function assignTo() {
            const selectedItems = getSelectedItems();
            if (selectedItems.length === 0 && globalData.currentTask) {
                const taskItems = globalData.items.filter(item => item.taskId === globalData.currentTask.id);
                if (taskItems.length === 0) {
                    alert('没有可操作的条目');
                    return;
                }
            }
            
            showAssignToModal();
        }

        function showAssignToModal() {
            document.getElementById('assignToModal').classList.add('show');
            document.getElementById('assigneeInput').value = '';
        }

        function hideAssignToModal() {
            document.getElementById('assignToModal').classList.remove('show');
        }

        function confirmAssignTo() {
            const assignee = document.getElementById('assigneeInput').value.trim();
            if (!assignee) {
                alert('请输入指派人');
                return;
            }
            
            const selectedItems = getSelectedItems();
            
            if (selectedItems.length === 0) {
                if (confirm(`请确认是否将所有条目指派给 ${assignee}？`)) {
                    if (globalData.currentTask) {
                        globalData.items.forEach(item => {
                            if (item.taskId === globalData.currentTask.id) {
                                item.assignee = assignee;
                                item.updateTime = new Date().toLocaleString('zh-CN');
                            }
                        });
                        renderItemList(globalData.currentTask.id);
                    }
                    hideAssignToModal();
                    alert('指派成功！');
                }
            } else {
                selectedItems.forEach(itemId => {
                    const item = globalData.items.find(i => i.id == itemId);
                    if (item) {
                        item.assignee = assignee;
                        item.updateTime = new Date().toLocaleString('zh-CN');
                    }
                });
                
                if (globalData.currentTask) {
                    renderItemList(globalData.currentTask.id);
                }
                
                hideAssignToModal();
                alert(`已将 ${selectedItems.length} 个条目指派给：${assignee}`);
            }
        }

        function exportItems() {
            console.log('导出条目');
            showExportModal();
        }

        function showExportModal() {
            document.getElementById('exportModal').classList.add('show');
        }

        function hideExportModal() {
            document.getElementById('exportModal').classList.remove('show');
        }

        function toggleExportAll() {
            const exportAll = document.getElementById('exportAll').checked;
            document.getElementById('exportHCS').checked = exportAll;
            document.getElementById('exportFS').checked = exportAll;
            document.getElementById('exportSecurity').checked = exportAll;
        }

        function confirmExport() {
            const selectedProducts = [];
            if (document.getElementById('exportHCS').checked) selectedProducts.push('华为云Stack');
            if (document.getElementById('exportFS').checked) selectedProducts.push('FusionSphere');
            if (document.getElementById('exportSecurity').checked) selectedProducts.push('云安全服务');
            
            if (selectedProducts.length === 0) {
                alert('请选择要导出的产品');
                return;
            }
            
            console.log('导出产品:', selectedProducts);
            alert(`正在导出 ${selectedProducts.join(', ')} 的条目数据...`);
            hideExportModal();
        }

        // 修复快捷应答跳转问题
        function startQuickResponse() {
            const form = document.getElementById('quickResponseForm');
            
            // 验证必填字段
            const product = document.getElementById('quickProductSelect').value;
            const itemContent = form.querySelector('textarea').value.trim();
            
            if (!product) {
                alert('请选择产品');
                return;
            }
            
            if (!itemContent) {
                alert('请输入条目描述');
                return;
            }
            
            // 显示处理中状态
            const submitBtn = event.target;
            const originalText = submitBtn.textContent;
            submitBtn.disabled = true;
            submitBtn.textContent = '处理中...';
            
            // 模拟异步处理
            setTimeout(() => {
                // 查找是否已存在个人任务
                let personalTask = globalData.tasks.find(task => task.isPersonal);
                
                if (!personalTask) {
                    // 如果没有个人任务，创建一个
                    globalData.personalTaskCounter = globalData.personalTaskCounter || 0;
                    globalData.personalTaskCounter++;
                    const personalTaskId = Math.max(...globalData.tasks.map(t => t.id)) + 1;
                    const personalTaskCode = `PERSONAL001`;
                    
                    personalTask = {
                        id: personalTaskId,
                        code: personalTaskCode,
                        name: '个人任务区',
                        country: form.querySelector('select').nextElementSibling ? form.querySelector('select').nextElementSibling.value || '中国' : '中国',
                        customer: form.querySelector('input[placeholder="请输入客户名称"]') ? form.querySelector('input[placeholder="请输入客户名称"]').value || '个人客户' : '个人客户',
                        project: '快捷应答项目',
                        company: '华为技术有限公司',
                        dataSource: 'GBBS',
                        itemCount: 0,
                        completedCount: 0,
                        satisfaction: 0,
                        status: '进行中',
                        isPersonal: true,
                        createTime: new Date().toLocaleString('zh-CN'),
                        updateTime: new Date().toLocaleString('zh-CN')
                    };
                    
                    globalData.tasks.push(personalTask);
                }
                
                // 在现有个人任务中创建新条目
                const newItemId = Math.max(...globalData.items.map(i => i.id)) + 1;
                const existingPersonalItems = globalData.items.filter(item => item.taskId === personalTask.id);
                const nextCodeNum = String(existingPersonalItems.length + 1).padStart(3, '0');
                
                const newItem = {
                    id: newItemId,
                    taskId: personalTask.id,
                    code: `QUICK${nextCodeNum}`,
                    description: itemContent,
                    product: product,
                    tags: ['快捷应答'],
                    status: '应答中',
                    satisfaction: null,
                    assignee: '张三（123456）',
                    responseMethod: 'AI应答',
                    responseContent: null,
                    source: 'GBBS',
                    index: null,
                    remark: '通过快捷应答创建',
                    updateUser: '张三（123456）',
                    updateTime: new Date().toLocaleString('zh-CN')
                };
                
                globalData.items.push(newItem);
                
                // 更新个人任务的条目计数
                updateTaskItemCount(personalTask.id);
                
                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
                
                console.log('添加条目到个人任务成功:', newItem);
                
                // 更新任务列表显示
                renderTaskList();
                
                // 直接跳转到个人任务详情页
                globalData.currentTask = personalTask;
                openTaskDetail(personalTask.id);
                
                // 清空表单
                clearQuickForm();
                
                // 显示成功提示
                alert('快捷应答已提交并跳转到个人任务详情页面！');
            }, 1500);
        }

        // 数据分析功能
        function filterAnalysisData() {
            const assigneeSelect = event.target;
            const selectedAssignee = assigneeSelect.value;
            console.log('筛选分析数据:', selectedAssignee);
            
            if (globalData.currentTask) {
                // 根据指派人筛选数据并重新渲染统计
                renderStats(globalData.currentTask.id, selectedAssignee);
            }
        }

        // 点击模态框外部关闭
        document.addEventListener('click', function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.classList.remove('show');
                }
            });
        });

        // 文件上传处理
        document.getElementById('fileInput').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const uploadArea = event.target.parentNode;
                uploadArea.innerHTML = `
                    <div class="upload-icon">✓</div>
                    <div class="upload-text">已选择文件: ${file.name}</div>
                `;
            }
        });

        document.getElementById('batchFileInput').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const uploadArea = event.target.parentNode;
                uploadArea.innerHTML = `
                    <div class="upload-icon">✓</div>
                    <div class="upload-text">已选择文件: ${file.name}</div>
                `;
            }
        });

        // 标签输入处理
        document.querySelector('#addItemModal input[placeholder="输入标签后按回车添加"]').addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                const tagValue = this.value.trim();
                if (tagValue) {
                    const tagList = this.parentNode.querySelector('.tag-list');
                    const tagElement = document.createElement('span');
                    tagElement.className = 'tag primary';
                    tagElement.innerHTML = `${tagValue} <span onclick="this.parentNode.remove()" style="cursor: pointer; margin-left: 4px;">×</span>`;
                    tagList.appendChild(tagElement);
                    this.value = '';
                }
            }
        });

        // 人工应答功能
        function switchProduct() {
            const selectedProduct = event.target.value;
            document.getElementById('currentProduct').textContent = selectedProduct;
            console.log('切换产品:', selectedProduct);
            // 这里应该重新加载该产品的应答数据
        }

        // 应答页面标签页切换
        function showResponseTab(tabId) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('#manual-response .tab-content');
            tabContents.forEach(content => content.style.display = 'none');
            
            // 显示指定标签页内容
            document.getElementById(tabId).style.display = 'block';
            
            // 更新标签页状态
            const tabs = document.querySelectorAll('#manual-response .tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
        }

        // AI功能
        function aiEnhance() {
            const additionalInfo = document.getElementById('additionalInfo');
            
            // 显示处理中状态
            const btn = event.target;
            const originalText = btn.textContent;
            btn.disabled = true;
            btn.textContent = '处理中...';
            
            // 模拟AI处理
            setTimeout(() => {
                additionalInfo.value = '基于华为云Stack的基础架构管理能力分析：\n1. 支持OpenStack标准API\n2. 提供统一的资源管理界面\n3. 支持多种虚拟化技术\n4. 具备自动化运维能力';
                
                btn.disabled = false;
                btn.textContent = originalText;
                
                alert('AI应答补充信息已生成！');
            }, 2000);
        }

        function aiPolish() {
            const content = document.getElementById('responseContent');
            const originalContent = content.innerHTML;
            
            // 显示AI结果面板
            showAiResult('AI润色结果', '华为云Stack基于先进的OpenStack架构，提供企业级云基础设施即服务(IaaS)解决方案。该平台具备完整的基础架构管理能力，能够实现虚拟机、容器、存储等多种计算资源的统一管理和智能调度，为用户提供稳定可靠的云服务体验。', content);
        }

        function aiTranslate() {
            const content = document.getElementById('responseContent');
            const originalContent = content.innerHTML;
            
            // 显示AI翻译结果
            showAiResult('AI翻译结果', 'Huawei Cloud Stack provides unified cloud platform infrastructure management capabilities, supporting unified management of virtual machines, containers, storage and other resources. Through the FusionSphere virtualization platform, dynamic allocation and management of computing, storage, and network resources can be achieved.', content);
        }

        function showAiResult(title, result, targetElement) {
            // 创建AI结果面板
            let resultPanel = document.querySelector('.ai-result-panel');
            if (!resultPanel) {
                resultPanel = document.createElement('div');
                resultPanel.className = 'ai-result-panel';
                targetElement.parentNode.appendChild(resultPanel);
            }
            
            resultPanel.innerHTML = `
                <div class="ai-result-header">
                    <span class="ai-result-title">${title}</span>
                    <div class="ai-result-actions">
                        <button class="btn btn-sm" onclick="closeAiResult()">取消</button>
                        <button class="btn btn-primary btn-sm" onclick="applyAiResult()">应用</button>
                    </div>
                </div>
                <div class="ai-result-content">${result}</div>
            `;
            
            resultPanel.classList.add('show');
            resultPanel.setAttribute('data-target', targetElement.id);
        }

        function applyAiResult() {
            const resultPanel = document.querySelector('.ai-result-panel.show');
            const targetId = resultPanel.getAttribute('data-target');
            const targetElement = document.getElementById(targetId);
            const resultContent = resultPanel.querySelector('.ai-result-content').textContent;
            
            if (targetElement.contentEditable === 'true') {
                targetElement.innerHTML = resultContent;
            } else {
                targetElement.value = resultContent;
            }
            
            closeAiResult();
            alert('AI结果已应用！');
        }

        function closeAiResult() {
            const resultPanel = document.querySelector('.ai-result-panel.show');
            if (resultPanel) {
                resultPanel.classList.remove('show');
            }
        }

        // 编辑器功能
        function formatText(command) {
            document.execCommand(command, false, null);
        }

        function insertImage() {
            const url = prompt('请输入图片URL:');
            if (url) {
                document.execCommand('insertImage', false, url);
            }
        }

        function viewSource() {
            window.open('https://gbbs.example.com/item/001', '_blank');
        }

        // 历史版本功能
        function loadHistoryVersion() {
            const version = event.target.value;
            if (!version) return;
            
            console.log('加载历史版本:', version);
            
            // 模拟加载历史版本数据
            const versionData = {
                'v1': {
                    content: '华为云Stack提供基础的云平台管理能力。',
                    satisfaction: 'PC',
                    updateTime: '2024-01-15 10:30'
                },
                'v2': {
                    content: '华为云Stack提供统一的云平台基础架构管理能力，支持虚拟机管理。',
                    satisfaction: 'PC', 
                    updateTime: '2024-01-15 11:15'
                },
                'current': {
                    content: '华为云Stack提供统一的云平台基础架构管理能力，支持虚拟机、容器等多种计算资源的统一管理。',
                    satisfaction: 'PC',
                    updateTime: '2024-01-15 14:20'
                }
            };
            
            if (versionData[version]) {
                const data = versionData[version];
                document.getElementById('responseContent').innerHTML = data.content;
                alert(`已加载${version === 'current' ? '当前' : '历史'}版本 (${data.updateTime})`);
            }
        }

        function compareVersions() {
            // 显示版本对比弹窗
            alert('版本对比功能开发中...');
        }

        // 匹配详情功能
        function filterMatches() {
            console.log('筛选匹配结果');
            // 这里应该根据筛选条件重新加载匹配数据
        }

        // 匹配详情分页功能
        let currentMatchPage = 1;
        const matchPageSize = 3;
        const totalMatchPages = 5;

        function nextMatchPage() {
            if (currentMatchPage < totalMatchPages) {
                currentMatchPage++;
                updateMatchPagination();
                loadMatchPage(currentMatchPage);
            }
        }

        function prevMatchPage() {
            if (currentMatchPage > 1) {
                currentMatchPage--;
                updateMatchPagination();
                loadMatchPage(currentMatchPage);
            }
        }

        function updateMatchPagination() {
            const paginationInfo = document.querySelector('.match-pagination .pagination-info');
            const prevBtn = document.querySelector('.match-pagination button:first-child');
            const nextBtn = document.querySelector('.match-pagination button:last-child');
            
            if (paginationInfo) {
                paginationInfo.textContent = `第 ${currentMatchPage}/${totalMatchPages} 页`;
            }
            
            if (prevBtn) {
                prevBtn.disabled = currentMatchPage === 1;
            }
            
            if (nextBtn) {
                nextBtn.disabled = currentMatchPage === totalMatchPages;
            }
        }

        function loadMatchPage(page) {
            // 模拟加载不同页面的匹配数据
            const matchGrid = document.getElementById('matchCardsGrid');
            if (!matchGrid) return;
            
            // 这里可以根据页面加载不同的匹配数据
            console.log(`加载第 ${page} 页的匹配数据`);
        }

        function toggleMatchSource(sourceId) {
            const content = document.getElementById(`${sourceId}-content`);
            const toggle = document.getElementById(`${sourceId}-toggle`);
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                toggle.classList.remove('collapsed');
                toggle.textContent = '▼';
            } else {
                content.classList.add('collapsed');
                toggle.classList.add('collapsed');
                toggle.textContent = '▶';
            }
        }

        function applyMatch(matchId) {
            if (confirm('将覆盖当前应答结果，请确认是否继续！')) {
                console.log('应用匹配结果:', matchId);
                
                // 模拟应用匹配数据
                const matchData = {
                    1: {
                        content: '华为云Stack基于OpenStack架构，提供完整的基础设施即服务(IaaS)能力，支持虚拟机、容器、存储等资源的统一管理和调度。',
                        satisfaction: 'FC',
                        index: 'GBBS-HCS-001'
                    },
                    2: {
                        content: '华为云Stack提供云平台基础架构管理能力，通过统一的管理平台实现计算、存储、网络资源的集中管理和动态分配。',
                        satisfaction: 'PC',
                        index: 'GBBS-HCS-002'
                    },
                    3: {
                        content: '华为云Stack基于成熟的虚拟化技术，提供IaaS服务，支持虚拟机的创建、配置、监控等全生命周期管理。',
                        satisfaction: 'PC',
                        index: 'GBBS-HCS-003'
                    }
                };
                
                if (matchData[matchId]) {
                    const data = matchData[matchId];
                    // 切换到应答结果页签
                    showResponseTab('response-result');
                    // 应用数据
                    document.getElementById('responseContent').innerHTML = data.content;
                    // 更新满足度
                    const satisfactionSelect = document.querySelector('#response-result select');
                    satisfactionSelect.value = data.satisfaction;
                    
                    alert('匹配结果已应用，应答方式已设置为AI！');
                }
            }
        }

        // 保存和重置功能
        function saveResponse() {
            if (!globalData.currentItem) {
                alert('没有找到当前条目信息');
                return;
            }
            
            const form = document.getElementById('responseForm');
            console.log('保存应答结果');
            
            // 获取表单数据
            const additionalInfo = document.getElementById('additionalInfo').value;
            const satisfactionSelect = document.querySelector('#response-result select');
            const responseContent = document.getElementById('responseContent').innerHTML;
            const remarkTextarea = form.querySelector('textarea[placeholder="可添加额外的描述"]');
            
            // 模拟保存
            const btn = event.target;
            const originalText = btn.textContent;
            btn.disabled = true;
            btn.textContent = '保存中...';
            
            setTimeout(() => {
                // 更新全局数据中的条目信息
                const item = globalData.items.find(i => i.id === globalData.currentItem.id);
                if (item) {
                    item.status = '已应答';
                    item.satisfaction = satisfactionSelect ? satisfactionSelect.value : null;
                    item.responseMethod = '手工应答';
                    item.responseContent = responseContent;
                    item.remark = remarkTextarea ? remarkTextarea.value : '';
                    item.updateUser = '张三（123456）';
                    item.updateTime = new Date().toLocaleString('zh-CN');
                    item.index = 'GBBS-' + String(Math.floor(Math.random() * 1000)).padStart(3, '0');
                    
                    // 更新当前条目引用
                    globalData.currentItem = item;
                    
                    // 如果是从任务详情页进入的，更新任务统计并返回任务详情页
                    if (globalData.currentTask) {
                        updateTaskItemCount(globalData.currentTask.id);
                        renderTaskList(); // 更新任务列表
                        
                        // 返回到任务详情页而不是任务管理页
                        openTaskDetail(globalData.currentTask.id);
                    }
                }
                
                btn.disabled = false;
                btn.textContent = originalText;
                
                alert('应答结果保存成功！应答方式已设置为"手工"，已返回任务详情页。');
            }, 1000);
        }

        function resetResponse() {
            if (confirm('确定要重置到编辑前的状态吗？')) {
                console.log('重置应答结果');
                // 重置表单内容
                document.getElementById('responseContent').innerHTML = '华为云Stack提供统一的云平台基础架构管理能力，支持虚拟机、容器等多种计算资源的统一管理。通过FusionSphere虚拟化平台，可以实现计算、存储、网络资源的动态分配和管理。';
                alert('已重置到编辑前状态');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('SOC智能应答系统加载完成');
        });

        // 批量标签输入处理
        document.getElementById('batchTagInput').addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                const tagValue = this.value.trim();
                if (tagValue) {
                    const tagList = document.getElementById('batchTagList');
                    const tagElement = document.createElement('span');
                    tagElement.className = 'tag primary';
                    tagElement.innerHTML = `${tagValue} <span onclick="this.parentNode.remove()" style="cursor: pointer; margin-left: 4px;">×</span>`;
                    tagList.appendChild(tagElement);
                    this.value = '';
                }
            }
        });

        // 复制文件上传处理
        document.getElementById('copyFileInput').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const uploadArea = event.target.parentNode;
                uploadArea.innerHTML = `
                    <div class="upload-icon">✓</div>
                    <div class="upload-text">已选择文件: ${file.name}</div>
                `;
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('SOC智能应答系统加载完成');
        });

        function refreshTasks() {
            console.log('刷新任务列表');
            renderTaskList();
        }

        function openTaskDetail(taskId) {
            // 确保taskId是数字类型
            const targetTaskId = parseInt(taskId);
            
            const task = globalData.tasks.find(t => t.id === targetTaskId);
            
            if (task) {
                globalData.currentTask = task;
                
                // 更新页面标题和任务信息
                const titleElement = document.getElementById('taskDetailTitle');
                if (titleElement) {
                    titleElement.textContent = task.name;
                } else {
                    console.error('❌ 找不到taskDetailTitle元素');
                }
                
                const taskInfoHeader = document.getElementById('taskInfoHeader');
                if (taskInfoHeader) {
                    taskInfoHeader.innerHTML = `
                        <div class="task-info-grid">
                            <div class="task-info-item">
                                <div class="task-info-label">国家/MTO</div>
                                <div class="task-info-value">${task.country}</div>
                            </div>
                            <div class="task-info-item">
                                <div class="task-info-label">省公司/分支</div>
                                <div class="task-info-value">${task.company}</div>
                            </div>
                            <div class="task-info-item">
                                <div class="task-info-label">客户</div>
                                <div class="task-info-value">${task.customer}</div>
                            </div>
                            <div class="task-info-item">
                                <div class="task-info-label">项目</div>
                                <div class="task-info-value">${task.project}</div>
                            </div>
                            <div class="task-info-item">
                                <div class="task-info-label">数据源</div>
                                <div class="task-info-value">${task.dataSource}</div>
                            </div>
                        </div>
                    `;
                } else {
                    console.error('❌ 找不到taskInfoHeader元素');
                }
                
                // 渲染条目列表
                renderItemList(targetTaskId);
                
                // 显示任务详情页面
                showPage('task-detail');
                
                // 启动自动刷新（条目管理是默认激活的标签页）
                startAutoRefresh();
                
            } else {
                console.error('❌ 任务不存在, taskId:', taskId);
                console.log('📋 当前所有任务:', globalData.tasks.map(t => ({id: t.id, name: t.name})));
                alert('任务不存在！');
            }
        }

        function editTask(taskId) {
            console.log('编辑任务:', taskId);
            
            const task = globalData.tasks.find(t => t.id === taskId);
            if (task) {
                // 填充编辑表单
                document.getElementById('editTaskId').value = task.id;
                document.getElementById('editTaskName').value = task.name;
                document.getElementById('editTaskCountry').value = task.country;
                document.getElementById('editTaskCompany').value = task.company;
                document.getElementById('editTaskCustomer').value = task.customer;
                document.getElementById('editTaskProject').value = task.project;
                
                showEditTaskModal();
            }
        }

        function updateTask() {
            const taskId = parseInt(document.getElementById('editTaskId').value);
            const task = globalData.tasks.find(t => t.id === taskId);
            
            if (task) {
                task.name = document.getElementById('editTaskName').value;
                task.country = document.getElementById('editTaskCountry').value;
                task.company = document.getElementById('editTaskCompany').value;
                task.customer = document.getElementById('editTaskCustomer').value;
                task.project = document.getElementById('editTaskProject').value;
                task.updateTime = new Date().toLocaleString('zh-CN');
                
                renderTaskList();
                hideEditTaskModal();
                
                console.log('更新任务:', task);
                alert('任务更新成功！');
            }
        }

        function copyTask(taskId) {
            console.log('复制任务:', taskId);
            
            const task = globalData.tasks.find(t => t.id === taskId);
            if (task) {
                // 填充复制表单
                document.getElementById('copyTaskId').value = task.id;
                document.getElementById('copyTaskName').value = task.name + ' - 副本';
                document.getElementById('copyTaskCountry').value = task.country;
                document.getElementById('copyTaskCompany').value = task.company;
                document.getElementById('copyTaskCustomer').value = task.customer;
                document.getElementById('copyTaskProject').value = task.project;
                document.getElementById('copyItemResults').checked = false;
                
                showCopyTaskModal();
            }
        }

        function showCopyTaskModal() {
            document.getElementById('copyTaskModal').classList.add('show');
        }

        function hideCopyTaskModal() {
            document.getElementById('copyTaskModal').classList.remove('show');
        }

        function confirmCopyTask() {
            const originalTaskId = parseInt(document.getElementById('copyTaskId').value);
            const copyItemResults = document.getElementById('copyItemResults').checked;
            
            const newId = Math.max(...globalData.tasks.map(t => t.id)) + 1;
            const newTaskCode = `TASK${String(newId).padStart(3, '0')}`;
            
            const originalTask = globalData.tasks.find(t => t.id === originalTaskId);
            
            const newTask = {
                ...originalTask,
                id: newId,
                code: newTaskCode,
                name: document.getElementById('copyTaskName').value,
                country: document.getElementById('copyTaskCountry').value,
                company: document.getElementById('copyTaskCompany').value,
                customer: document.getElementById('copyTaskCustomer').value,
                project: document.getElementById('copyTaskProject').value,
                itemCount: 0,
                completedCount: 0,
                satisfaction: 0,
                status: '未开始',
                createTime: new Date().toLocaleString('zh-CN'),
                updateTime: new Date().toLocaleString('zh-CN')
            };
            
            globalData.tasks.push(newTask);
            
            // 复制条目
            const originalItems = globalData.items.filter(item => item.taskId === originalTaskId);
            originalItems.forEach(originalItem => {
                const newItemId = Math.max(...globalData.items.map(i => i.id)) + 1;
                const newItem = {
                    ...originalItem,
                    id: newItemId,
                    taskId: newId,
                    status: copyItemResults ? originalItem.status : '未应答',
                    satisfaction: copyItemResults ? originalItem.satisfaction : null,
                    responseMethod: copyItemResults ? originalItem.responseMethod : null,
                    responseContent: copyItemResults ? originalItem.responseContent : null,
                    updateUser: '张三（123456）',
                    updateTime: new Date().toLocaleString('zh-CN')
                };
                globalData.items.push(newItem);
            });
            
            // 更新新任务的条目计数
            updateTaskItemCount(newId);
            renderTaskList();
            hideCopyTaskModal();
            
            alert('任务复制成功！');
        }

        function deleteTask(taskId) {
            if (confirm('确定要删除此任务吗？此操作不可恢复。')) {
                console.log('删除任务:', taskId);
                
                // 删除任务相关的所有条目
                globalData.items = globalData.items.filter(item => item.taskId !== taskId);
                
                // 删除任务
                const taskIndex = globalData.tasks.findIndex(t => t.id === taskId);
                if (taskIndex > -1) {
                    globalData.tasks.splice(taskIndex, 1);
                }
                
                renderTaskList();
                alert('任务删除成功！');
            }
        }

        function showEditTaskModal() {
            document.getElementById('editTaskModal').classList.add('show');
        }

        function hideEditTaskModal() {
            document.getElementById('editTaskModal').classList.remove('show');
        }

        // 搜索相关函数
        function searchTasks() {
            const filters = {
                taskCode: document.getElementById('searchTaskCode').value.trim(),
                taskName: document.getElementById('searchTaskName').value.trim(),
                country: document.getElementById('searchCountry').value,
                customer: document.getElementById('searchCustomer').value.trim(),
                project: document.getElementById('searchProject').value.trim(),
                status: document.getElementById('searchStatus').value
            };
            
            console.log('搜索任务:', filters);
            
            let filteredTasks = globalData.tasks.filter(task => {
                // 任务编码 - 精确查询
                if (filters.taskCode && task.code !== filters.taskCode) return false;
                // 任务名称 - 模糊查询
                if (filters.taskName && !task.name.toLowerCase().includes(filters.taskName.toLowerCase())) return false;
                // 国家 - 精确查询
                if (filters.country && task.country !== filters.country) return false;
                // 客户 - 精确查询
                if (filters.customer && task.customer !== filters.customer) return false;
                // 项目 - 精确查询
                if (filters.project && task.project !== filters.project) return false;
                // 状态 - 精确查询
                if (filters.status && task.status !== filters.status) return false;
                return true;
            });
            
            // 临时替换任务列表进行渲染
            const originalTasks = globalData.tasks;
            globalData.tasks = filteredTasks;
            renderTaskList();
            globalData.tasks = originalTasks;
        }

        function resetTaskSearch() {
            document.getElementById('searchTaskCode').value = '';
            document.getElementById('searchTaskName').value = '';
            document.getElementById('searchCountry').value = '';
            document.getElementById('searchCustomer').value = '';
            document.getElementById('searchProject').value = '';
            document.getElementById('searchStatus').value = '';
            renderTaskList();
        }

        // 辅助函数
        function clearQuickForm() {
            document.getElementById('quickProductSelect').value = '';
            document.querySelector('#quickResponseForm textarea').value = '';
            document.querySelector('#quickResponseForm select[onchange="loadProductOptions()"] + select').value = '';
            document.querySelector('#quickResponseForm input[placeholder="请输入客户名称"]').value = '';
        }

        function updateTaskItemCount(taskId) {
            const task = globalData.tasks.find(t => t.id === taskId);
            if (task) {
                const taskItems = globalData.items.filter(item => item.taskId === taskId);
                task.itemCount = taskItems.length;
                task.completedCount = taskItems.filter(item => item.status === '已应答').length;
                task.satisfaction = task.completedCount > 0 ? 
                    Math.round((taskItems.filter(item => item.satisfaction === 'FC').length / task.completedCount) * 100) : 0;
                task.updateTime = new Date().toLocaleString('zh-CN');
                
                // 更新任务状态
                if (task.completedCount === 0) {
                    task.status = '未开始';
                } else if (task.completedCount === task.itemCount) {
                    task.status = '已完成';
                } else {
                    task.status = '进行中';
                }
            }
        }

        function updateTaskProgress(taskId) {
            updateTaskItemCount(taskId);
        }

        function getSelectedItems() {
            const checkboxes = document.querySelectorAll('input[name="itemSelect"]:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }

        // 任务详情操作函数
        function manualResponse(itemId) {
            console.log('手工应答:', itemId);
            const item = globalData.items.find(i => i.id == itemId);
            if (item) {
                // 设置当前条目为全局变量，供手工应答页面使用
                globalData.currentItem = item;
                
                // 更新手工应答页面的条目信息
                updateManualResponsePage(item);
                
                // 跳转到手工应答页面
                showPage('manual-response');
                
                // 更新状态为应答中
                item.status = '应答中';
                item.responseMethod = '手工应答';
                item.updateTime = new Date().toLocaleString('zh-CN');
                
                if (globalData.currentTask) {
                    renderItemList(globalData.currentTask.id);
                    updateTaskProgress(globalData.currentTask.id);
                    renderTaskList();
                }
            }
        }

        // 更新手工应答页面的条目信息
        function updateManualResponsePage(item) {
            // 更新面包屑导航 - 修复面包屑问题
            const breadcrumb = document.querySelector('#manual-response .breadcrumb');
            if (breadcrumb && globalData.currentTask) {
                breadcrumb.innerHTML = `
                    <a href="#" onclick="showPage('task-management')">任务管理</a>
                    <span>></span>
                    <a href="#" onclick="openTaskDetail(${globalData.currentTask.id})">
                        ${globalData.currentTask.name}
                    </a>
                    <span>></span>
                    <span>人工应答</span>
                `;
            }
            
            // 获取当前条目编号下的所有产品 - 修复产品切换问题
            const sameCodeItems = globalData.items.filter(i => 
                i.taskId === item.taskId && i.code === item.code
            );
            const availableProducts = [...new Set(sameCodeItems.map(i => i.product))];
            
            // 更新条目信息头部
            const taskInfoHeader = document.querySelector('#manual-response .task-info-header');
            if (taskInfoHeader) {
                taskInfoHeader.innerHTML = `
                    <!-- 条目基本信息 -->
                    <div class="task-info-grid" style="margin-bottom: 16px;">
                        <div class="task-info-item" style="grid-column: span 3;">
                            <div class="task-info-label" style="font-size: 14px;">条目描述</div>
                            <div class="task-info-value" style="font-size: 18px; font-weight: 600; color: #262626;">${item.description}</div>
                        </div>
                        <div class="task-info-item">
                            <div class="task-info-label" style="font-size: 14px;">条目编号</div>
                            <div class="task-info-value" style="font-size: 16px; font-weight: 500;">${item.code}</div>
                        </div>
                        <div class="task-info-item">
                            <div class="task-info-label" style="font-size: 14px;">当前产品</div>
                            <div class="task-info-value" style="font-size: 16px; font-weight: 500;" id="currentProduct">${item.product}</div>
                        </div>
                    </div>

                    <!-- 任务相关信息 -->
                    <div class="task-info-grid" style="margin-bottom: 16px;">
                        <div class="task-info-item">
                            <div class="task-info-label" style="font-size: 14px;">国家/MTO</div>
                            <div class="task-info-value" style="font-size: 16px; font-weight: 500;">${globalData.currentTask?.country || '中国'}</div>
                        </div>
                        <div class="task-info-item">
                            <div class="task-info-label" style="font-size: 14px;">省公司/分支</div>
                            <div class="task-info-value" style="font-size: 16px; font-weight: 500;">${globalData.currentTask?.company || '华为技术有限公司'}</div>
                        </div>
                        <div class="task-info-item">
                            <div class="task-info-label" style="font-size: 14px;">客户</div>
                            <div class="task-info-value" style="font-size: 16px; font-weight: 500;">${globalData.currentTask?.customer || '某银行'}</div>
                        </div>
                    </div>
                    
                    <!-- 产品切换和历史版本 - 调整布局 -->
                    <div style="display: flex; gap: 24px; align-items: center; margin-top: 16px;">
                        <div>
                            <label class="form-label" style="margin: 0 8px 0 0; font-weight: 500;">切换产品查看：</label>
                            <select class="form-control" style="width: 200px;" onchange="switchProduct()">
                                ${availableProducts.map(product => 
                                    `<option value="${product}" ${item.product === product ? 'selected' : ''}>${product}</option>`
                                ).join('')}
                            </select>
                        </div>
                        <div>
                            <label class="form-label" style="margin: 0 8px 0 0; font-weight: 500;">历史版本：</label>
                            <select class="form-control" style="width: 300px;" onchange="loadHistoryVersion()">
                                <option value="">选择历史版本</option>
                                <option value="v1">版本1 - 2024-01-15 10:30 (AI应答)</option>
                                <option value="v2">版本2 - 2024-01-15 11:15 (手工修改)</option>
                                <option value="current">当前版本 - 2024-01-15 14:20</option>
                            </select>
                        </div>
                    </div>
                `;
            }
            
            // 更新应答表单内容
            const responseContent = document.getElementById('responseContent');
            if (responseContent) {
                responseContent.innerHTML = item.responseContent || '请输入应答说明...';
            }
            
            // 更新满足度选择
            const satisfactionSelect = document.querySelector('#response-result select');
            if (satisfactionSelect) {
                satisfactionSelect.value = item.satisfaction || '';
            }
        }

        function aiResponse(itemId) {
            console.log('AI应答:', itemId);
            const item = globalData.items.find(i => i.id == itemId);
            if (item) {
                // 模拟AI应答处理
                item.status = '应答中';
                item.responseMethod = 'AI应答';
                item.updateTime = new Date().toLocaleString('zh-CN');
                
                // 模拟异步处理
                setTimeout(() => {
                    item.status = '已应答';
                    item.satisfaction = 'FC';
                    item.responseContent = 'AI自动生成的应答内容...';
                    item.updateTime = new Date().toLocaleString('zh-CN');
                    
                    if (globalData.currentTask) {
                        renderItemList(globalData.currentTask.id);
                        updateTaskProgress(globalData.currentTask.id);
                        renderTaskList();
                    }
                    
                    alert('AI应答完成！');
                }, 2000);
                
                if (globalData.currentTask) {
                    renderItemList(globalData.currentTask.id);
                    updateTaskProgress(globalData.currentTask.id);
                    renderTaskList();
                }
                
                alert('AI应答处理中...');
            }
        }

        function deleteItem(itemId) {
            if (confirm('确定要删除此条目吗？')) {
                console.log('删除条目:', itemId);
                const index = globalData.items.findIndex(i => i.id == itemId);
                if (index > -1) {
                    globalData.items.splice(index, 1);
                    
                    if (globalData.currentTask) {
                        renderItemList(globalData.currentTask.id);
                        updateTaskProgress(globalData.currentTask.id);
                        renderTaskList();
                    }
                    
                    alert('条目删除成功！');
                }
            }
        }

        function loadProductOptions() {
            // 产品选项加载函数，这里已经在HTML中静态定义了选项
            console.log('加载产品选项');
        }

        // 模态框相关函数
        function showCreateTaskModal() {
            document.getElementById('createTaskModal').classList.add('show');
        }

        function hideCreateTaskModal() {
            document.getElementById('createTaskModal').classList.remove('show');
        }

        function createTask() {
            console.log('创建任务');
            // 这里应该有创建任务的逻辑
            hideCreateTaskModal();
            alert('任务创建功能开发中...');
        }

        function showAddItemModal() {
            document.getElementById('addItemModal').classList.add('show');
        }

        function hideAddItemModal() {
            document.getElementById('addItemModal').classList.remove('show');
            
            // 清空表单内容
            const form = document.getElementById('addItemForm');
            if (form) {
                form.reset();
                
                // 清空标签列表
                const tagList = form.querySelector('.tag-list');
                if (tagList) {
                    tagList.innerHTML = '';
                }
                
                // 清空富文本编辑器
                const editorContent = form.querySelector('.editor-content');
                if (editorContent) {
                    editorContent.innerHTML = '';
                }
            }
        }

        function addItem() {
            console.log('添加条目');
            
            const form = document.getElementById('addItemForm');
            const code = form.querySelector('input[placeholder="请输入条目编号"]').value.trim();
            const description = form.querySelector('textarea[placeholder="请输入条目描述内容"]').value.trim();
            const productSelect = document.getElementById('addItemProduct');
            const satisfactionSelect = form.querySelector('select:has(option[value="FC"])');
            const assigneeInput = form.querySelector('input[placeholder="请选择指派人"]');
            const supplementInput = form.querySelector('textarea[placeholder="填值时作为条目的补充信息"]');
            const remarkInput = form.querySelector('textarea[placeholder="条目其他事项说明"]');
            const autoResponseCheck = document.getElementById('autoResponse');
            const overwriteCheck = document.getElementById('overwriteWhenDuplicate');
            
            // 验证必填字段
            if (!code) {
                alert('请输入条目编号');
                return;
            }
            
            if (!description) {
                alert('请输入条目描述');
                return;
            }
            
            if (!productSelect || !productSelect.value) {
                alert('请选择产品');
                return;
            }
            
            if (!globalData.currentTask) {
                alert('请先选择一个任务');
                return;
            }
            
            // 修改防重逻辑：只有条目编号+产品重复时才考虑是覆盖还是跳过
            const existingItem = globalData.items.find(item => 
                item.taskId === globalData.currentTask.id && 
                item.code === code && 
                item.product === productSelect.value
            );
            
            if (existingItem) {
                if (!overwriteCheck.checked) {
                    if (confirm(`条目编号"${code}"在产品"${productSelect.value}"下已存在，是否覆盖？\n\n点击"确定"覆盖现有条目\n点击"取消"跳过此条目`)) {
                        // 用户选择覆盖，继续执行覆盖逻辑
                    } else {
                        // 用户选择跳过
                        alert('已跳过重复条目');
                        return;
                    }
                }
                
                // 覆盖现有条目
                existingItem.description = description;
                existingItem.product = productSelect.value;
                existingItem.tags = tags;
                existingItem.satisfaction = satisfactionSelect ? satisfactionSelect.value : null;
                existingItem.assignee = assigneeInput ? assigneeInput.value : null;
                existingItem.responseContent = responseContent !== '支持图文混排...' ? responseContent : null;
                existingItem.remark = remarkInput ? remarkInput.value : '';
                existingItem.updateUser = '张三（123456）';
                existingItem.updateTime = new Date().toLocaleString('zh-CN');
                
                if (autoResponseCheck.checked) {
                    existingItem.status = '应答中';
                    existingItem.responseMethod = 'AI应答';
                }
                
                console.log('覆盖现有条目:', existingItem);
            } else {
                // 收集标签
                const tags = [];
                const tagElements = form.querySelectorAll('.tag-list .tag');
                tagElements.forEach(tagEl => {
                    const tagText = tagEl.textContent.replace(' ×', '').trim();
                    if (tagText) tags.push(tagText);
                });
                
                // 获取应答说明内容
                const responseContent = form.querySelector('.editor-content').innerHTML;
                
                // 创建新条目
                const newItemId = Math.max(...globalData.items.map(i => i.id)) + 1;
                const newItem = {
                    id: newItemId,
                    taskId: globalData.currentTask.id,
                    code: code,
                    description: description,
                    product: productSelect.value,
                    tags: tags,
                    status: autoResponseCheck.checked ? '应答中' : '未应答',
                    satisfaction: satisfactionSelect ? satisfactionSelect.value : null,
                    assignee: assigneeInput ? assigneeInput.value : null,
                    responseMethod: autoResponseCheck.checked ? 'AI应答' : null,
                    responseContent: responseContent !== '支持图文混排...' ? responseContent : null,
                    source: 'GBBS',
                    index: null,
                    remark: remarkInput ? remarkInput.value : '',
                    updateUser: '张三（123456）',
                    updateTime: new Date().toLocaleString('zh-CN')
                };
                
                globalData.items.push(newItem);
                console.log('创建新条目:', newItem);
            }
            
            // 更新任务统计
            updateTaskItemCount(globalData.currentTask.id);
            
            // 重新渲染列表
            renderItemList(globalData.currentTask.id);
            renderTaskList();
            
            // 关闭弹窗
            hideAddItemModal();
            
            alert('条目添加成功！');
        }

        function showBatchImportModal() {
            document.getElementById('batchImportModal').classList.add('show');
        }

        function hideBatchImportModal() {
            document.getElementById('batchImportModal').classList.remove('show');
        }

        function batchImport() {
            console.log('批量导入');
            hideBatchImportModal();
            alert('批量导入功能开发中...');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('SOC智能应答系统加载完成');
            renderTaskList();
        });

        // 调试函数 - 可以在浏览器控制台中调用
        window.debugSOC = function() {
            console.log('=== SOC系统调试信息 ===');
            console.log('📋 任务数量:', globalData.tasks.length);
            console.log('📝 条目数量:', globalData.items.length);
            console.log('🎯 当前任务:', globalData.currentTask);
            
            // 检查各任务的条目分布
            const taskItemCounts = globalData.tasks.map(task => {
                const itemCount = globalData.items.filter(item => item.taskId === task.id).length;
                return {id: task.id, name: task.name, itemCount: itemCount};
            });
            console.log('📊 各任务条目数量:', taskItemCounts);
            
            // 检查DOM元素
            const tbody = document.getElementById('itemTableBody');
            console.log('🔍 itemTableBody元素存在:', !!tbody);
            if (tbody) {
                console.log('📏 当前显示的行数:', tbody.children.length);
            }
            
            return {
                tasks: globalData.tasks.length,
                items: globalData.items.length,
                currentTask: globalData.currentTask,
                hasItemTableBody: !!tbody,
                itemDistribution: taskItemCounts
            };
        };
        
        console.log('💡 提示：可以在控制台中输入 debugSOC() 来查看系统调试信息');

        // 下载导入模板
        function downloadTemplate() {
            // 创建模板数据
            const templateData = [
                ['编号', '条目描述', '产品', '标签', '应答', '指派给', '备注'],
                ['CODE001', '云平台基础架构能力要求示例', '华为云Stack', '基础架构;云平台', 'FC', '张三（123456）', '示例数据'],
                ['CODE002', '虚拟化资源管理能力示例', 'FusionSphere', '虚拟化;资源管理', 'PC', '李四（789012）', '示例数据'],
                ['CODE003', '网络安全防护机制示例', '云安全服务', '安全;防护', 'NC', '王五（345678）', '示例数据']
            ];
            
            // 创建CSV内容
            let csvContent = templateData.map(row => 
                row.map(cell => `"${cell}"`).join(',')
            ).join('\n');
            
            // 添加BOM以支持中文
            csvContent = '\uFEFF' + csvContent;
            
            // 创建下载链接
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', 'SOC条目导入模板.csv');
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                alert('模板文件下载成功！请按照模板格式填写数据后上传。\n\n说明：\n- 标签字段多个标签用分号(;)分隔\n- 满足度可选值：FC/PC/NC\n- 指派给格式：姓名（工号）');
            } else {
                alert('当前浏览器不支持文件下载');
            }
        }

        // 根据条目状态获取操作按钮 - 修正按钮逻辑
        function getItemActionButtons(item) {
            let buttons = [];
            
            // 应答中的条目：只显示删除按钮，不显示手工应答和AI应答按钮
            if (item.status === '应答中') {
                buttons.push(`<button class="btn btn-sm btn-danger" onclick="deleteItem(${item.id})">删除</button>`);
            } else {
                // 未应答和已应答的条目：显示手工应答、AI应答、删除按钮
                buttons.push(`<button class="btn btn-sm btn-primary" onclick="manualResponse(${item.id})">手工应答</button>`);
                buttons.push(`<button class="btn btn-sm" onclick="aiResponse(${item.id})">AI应答</button>`);
                buttons.push(`<button class="btn btn-sm btn-danger" onclick="deleteItem(${item.id})">删除</button>`);
            }
            
            return buttons.join('');
        }

        // 条目编辑功能
        function editItem(itemId) {
            console.log('编辑条目:', itemId);
            const item = globalData.items.find(i => i.id == itemId);
            if (!item) {
                alert('条目不存在');
                return;
            }
            
            // 应答中的条目不可编辑
            if (item.status === '应答中') {
                alert('应答中的条目不可编辑');
                return;
            }
            
            showEditItemModal(item);
        }

        function showEditItemModal(item) {
            // 创建编辑弹窗
            const existingModal = document.getElementById('editItemModal');
            if (existingModal) {
                existingModal.remove();
            }
            
            const modalHTML = `
                <div id="editItemModal" class="modal show">
                    <div class="modal-dialog">
                        <div class="modal-header">
                            <h3 class="modal-title">编辑条目</h3>
                            <button class="modal-close" onclick="hideEditItemModal()">&times;</button>
                        </div>
                        <div class="modal-body">
                            <form id="editItemForm">
                                <div class="form-row">
                                    <div class="form-item">
                                        <label class="form-label">应答状态</label>
                                        <select class="form-control" id="editItemStatus">
                                            <option value="未应答" ${item.status === '未应答' ? 'selected' : ''}>未应答</option>
                                            <option value="已应答" ${item.status === '已应答' ? 'selected' : ''}>已应答</option>
                                        </select>
                                    </div>
                                    <div class="form-item">
                                        <label class="form-label">应答</label>
                                        <select class="form-control" id="editItemSatisfaction">
                                            <option value="">请选择</option>
                                            <option value="FC" ${item.satisfaction === 'FC' ? 'selected' : ''}>FC - 完全满足</option>
                                            <option value="PC" ${item.satisfaction === 'PC' ? 'selected' : ''}>PC - 部分满足</option>
                                            <option value="NC" ${item.satisfaction === 'NC' ? 'selected' : ''}>NC - 不满足</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-item">
                                        <label class="form-label">指派给</label>
                                        <input type="text" class="form-control" id="editItemAssignee" value="${item.assignee || ''}" placeholder="请输入指派人">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-item">
                                        <label class="form-label">应答说明</label>
                                        <textarea class="form-control" rows="4" id="editItemResponseContent" placeholder="请输入应答说明">${item.responseContent || ''}</textarea>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-item">
                                        <label class="form-label">备注</label>
                                        <textarea class="form-control" rows="2" id="editItemRemark" placeholder="请输入备注">${item.remark || ''}</textarea>
                                    </div>
                                </div>
                                <input type="hidden" id="editItemId" value="${item.id}">
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button class="btn" onclick="hideEditItemModal()">取消</button>
                            <button class="btn btn-primary" onclick="saveEditItem()">保存</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }

        function hideEditItemModal() {
            const modal = document.getElementById('editItemModal');
            if (modal) {
                modal.remove();
            }
        }

        function saveEditItem() {
            const itemId = parseInt(document.getElementById('editItemId').value);
            const item = globalData.items.find(i => i.id === itemId);
            
            if (!item) {
                alert('条目不存在');
                return;
            }
            
            // 获取表单数据
            const status = document.getElementById('editItemStatus').value;
            const satisfaction = document.getElementById('editItemSatisfaction').value;
            const assignee = document.getElementById('editItemAssignee').value.trim();
            const responseContent = document.getElementById('editItemResponseContent').value.trim();
            const remark = document.getElementById('editItemRemark').value.trim();
            
            // 更新条目数据
            item.status = status;
            item.satisfaction = satisfaction || null;
            item.assignee = assignee || null;
            item.responseContent = responseContent || null;
            item.remark = remark || null;
            item.updateUser = '张三（123456）';
            item.updateTime = new Date().toLocaleString('zh-CN');
            
            // 如果状态改为已应答，设置应答方式
            if (status === '已应答' && !item.responseMethod) {
                item.responseMethod = '手工应答';
            }
            
            // 重新渲染列表
            if (globalData.currentTask) {
                renderItemList(globalData.currentTask.id);
                updateTaskProgress(globalData.currentTask.id);
                renderTaskList();
            }
            
            hideEditItemModal();
            alert('条目编辑成功！');
        }

        // 列筛选功能
        let columnFilterVisible = false;
        
        // 默认显示的列
        const defaultVisibleColumns = [
            'select', '序号', '编号', '条目描述', '产品', '应答状态', 
            '应答', '应答说明', '应答来源', '索引', '操作'
        ];

        // 列筛选按钮点击
        function toggleColumnFilter() {
            const dropdown = document.getElementById('columnFilterDropdown');
            const btn = document.getElementById('columnFilterBtn');
            const toggleIcon = btn.querySelector('.toggle-icon');
            
            columnFilterVisible = !columnFilterVisible;
            
            if (columnFilterVisible) {
                dropdown.classList.add('show');
                toggleIcon.textContent = '▲';
            } else {
                dropdown.classList.remove('show');
                toggleIcon.textContent = '▼';
            }
        }

        // 重置列筛选
        function resetColumnFilter() {
            // 取消所有选中
            const checkboxes = document.querySelectorAll('#columnFilterDropdown input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            
            // 设置默认选中状态
            defaultVisibleColumns.forEach(col => {
                const checkbox = document.getElementById(`col-${col}`);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });
            
            updateColumnCount();
        }

        // 应用列筛选
        function applyColumnFilter() {
            const table = document.querySelector('.table');
            if (!table) {
                console.log('❌ 找不到表格元素');
                return;
            }
            
            const checkboxes = document.querySelectorAll('#columnFilterDropdown input[type="checkbox"]');
            const visibleColumns = [];
            
            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    const columnName = checkbox.id.replace('col-', '');
                    visibleColumns.push(columnName);
                }
            });
            
            // 获取列索引映射
            const columnIndexMap = {
                'select': 0,
                '序号': 1,
                '编号': 2,
                '条目描述': 3,
                '产品': 4,
                '标签': 5,
                '应答状态': 6,
                '应答': 7,
                '指派给': 8,
                '应答方式': 9,
                '应答说明': 10,
                '应答来源': 11,
                '索引': 12,
                '备注': 13,
                '最后更新人': 14,
                '最后更新时间': 15,
                '操作': 16
            };
            
            // 隐藏/显示表头
            const headerCells = table.querySelectorAll('thead th');
            headerCells.forEach((cell, index) => {
                const columnName = Object.keys(columnIndexMap).find(key => columnIndexMap[key] === index);
                if (columnName && !visibleColumns.includes(columnName)) {
                    cell.style.display = 'none';
                } else {
                    cell.style.display = '';
                }
            });
            
            // 隐藏/显示表格数据 - 修复合并单元格处理
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                
                // 重新计算每个单元格的逻辑列索引
                let logicalIndex = 0;
                cells.forEach(cell => {
                    // 检查当前行之前是否有跨行单元格占用了这个位置
                    const rowIndex = Array.from(row.parentNode.children).indexOf(row);
                    let skipCount = 0;
                    
                    // 检查前面的行中跨行到当前行的单元格
                    for (let prevRowIndex = 0; prevRowIndex < rowIndex; prevRowIndex++) {
                        const prevRow = row.parentNode.children[prevRowIndex];
                        const prevCells = prevRow.querySelectorAll('td');
                        let prevLogicalIndex = 0;
                        
                        prevCells.forEach(prevCell => {
                            const prevRowspan = parseInt(prevCell.getAttribute('rowspan') || '1');
                            if (prevRowspan > 1 && (prevRowIndex + prevRowspan) > rowIndex) {
                                if (prevLogicalIndex <= logicalIndex + skipCount) {
                                    skipCount++;
                                }
                            }
                            prevLogicalIndex++;
                        });
                    }
                    
                    const actualLogicalIndex = logicalIndex + skipCount;
                    const columnName = Object.keys(columnIndexMap).find(key => columnIndexMap[key] === actualLogicalIndex);
                    
                    if (columnName && !visibleColumns.includes(columnName)) {
                        cell.style.display = 'none';
                    } else {
                        cell.style.display = '';
                    }
                    
                    logicalIndex++;
                });
            });
            
            // 关闭下拉菜单
            toggleColumnFilter();
            
            console.log('✅ 应用列筛选，可见列:', visibleColumns);
        }

        // 应用当前列筛选（不关闭下拉菜单）- 修复联动问题
        function applyCurrentColumnFilter() {
            const table = document.querySelector('.table');
            if (!table) {
                console.log('❌ 找不到表格元素');
                return;
            }
            
            const checkboxes = document.querySelectorAll('#columnFilterDropdown input[type="checkbox"]');
            const visibleColumns = [];
            
            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    const columnName = checkbox.id.replace('col-', '');
                    visibleColumns.push(columnName);
                }
            });
            
            // 获取列索引映射
            const columnIndexMap = {
                'select': 0,
                '序号': 1,
                '编号': 2,
                '条目描述': 3,
                '产品': 4,
                '标签': 5,
                '应答状态': 6,
                '应答': 7,
                '指派给': 8,
                '应答方式': 9,
                '应答说明': 10,
                '应答来源': 11,
                '索引': 12,
                '备注': 13,
                '最后更新人': 14,
                '最后更新时间': 15,
                '操作': 16
            };
            
            // 隐藏/显示表头
            const headerCells = table.querySelectorAll('thead th');
            headerCells.forEach((cell, index) => {
                const columnName = Object.keys(columnIndexMap).find(key => columnIndexMap[key] === index);
                if (columnName && !visibleColumns.includes(columnName)) {
                    cell.style.display = 'none';
                } else {
                    cell.style.display = '';
                }
            });
            
            // 简化的表格数据处理 - 直接按列索引处理
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                
                // 为每个单元格标记列索引（如果没有的话）
                if (!row.hasAttribute('data-column-processed')) {
                    let currentColumnIndex = 0;
                    cells.forEach(cell => {
                        // 检查是否有跨列属性
                        const colspan = parseInt(cell.getAttribute('colspan') || '1');
                        cell.setAttribute('data-column-index', currentColumnIndex);
                        currentColumnIndex += colspan;
                    });
                    row.setAttribute('data-column-processed', 'true');
                }
                
                // 根据列索引隐藏/显示单元格
                cells.forEach(cell => {
                    const columnIndex = parseInt(cell.getAttribute('data-column-index') || '0');
                    const columnName = Object.keys(columnIndexMap).find(key => columnIndexMap[key] === columnIndex);
                    
                    if (columnName && !visibleColumns.includes(columnName)) {
                        cell.style.display = 'none';
                    } else {
                        cell.style.display = '';
                    }
                });
            });
            
            console.log('✅ 自动应用列筛选，可见列:', visibleColumns);
        }

        // 更新选中列数量显示
        function updateColumnCount() {
            const checkboxes = document.querySelectorAll('#columnFilterDropdown input[type="checkbox"]');
            const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
            const totalCount = checkboxes.length;
            
            const countDisplay = document.getElementById('selectedColumnCount');
            if (countDisplay) {
                countDisplay.textContent = `${checkedCount}/${totalCount}`;
            }
        }

        // 点击页面其他地方关闭列筛选下拉菜单
        document.addEventListener('click', function(event) {
            const columnFilter = document.querySelector('.column-filter');
            const dropdown = document.getElementById('columnFilterDropdown');
            
            if (columnFilter && !columnFilter.contains(event.target) && dropdown && dropdown.classList.contains('show')) {
                toggleColumnFilter();
            }
        });

        // 监听列筛选复选框变化
        document.addEventListener('change', function(event) {
            if (event.target.matches('#columnFilterDropdown input[type="checkbox"]')) {
                updateColumnCount();
                // 立即应用列筛选效果 - 修复实时联动问题
                applyCurrentColumnFilter();
            }
        });

        // 初始化列筛选
        function initColumnFilter() {
            // 设置默认选中状态
            resetColumnFilter();
            
            console.log('✅ 列筛选功能已初始化');
        }

    </script>
</body>
</html> 