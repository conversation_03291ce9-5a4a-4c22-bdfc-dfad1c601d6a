package com.zte.mcrm.adapter.approval.service;

import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.adapter.approval.model.*;
import com.zte.mcrm.adapter.approval.model.dto.*;

import java.util.List;
import java.util.Map;

/**
 * @description: 审批中心审批流程服务
 * @author: 10243305
 * @date: 2021/6/24 下午2:41
 */
public interface ApprovalFlowService {
    /**
     * 启动流程
     * @param startParams 启动参数
     * @return
     */
    String startFlow(ApprovalStartParamsDTO startParams);

    /**
     * 审批
     * @param opinion
     */
    void approve(OpinionDTO opinion);

    /**
     * 转交审批
     * @param opinion
     */
    String reassignApproval(ReassignDTO opinion);

    /**
     * 重设流程参数
     * @param params
     */
    String resetFlowParams(ApprovalResetStartParamsDTO params);

    /**
     * 获取待审批节点
     * @param approvingParameter 待审批节点获取信息
     * @return
     */
    PageRows<ApprovingNodeInfo> getApprovingNodes(ApprovingParameter approvingParameter);

    /**
     * 查询流程进展
     * @param flowParameter
     * @return
     */
    List<ApprovalProgressDTO> getFlowProgress(FlowParameter flowParameter);

    /**
     * 注销流程
     * @param revokeParams
     * @return
     */
    String revokeFlow(ApprovalRevokeParamsDTO revokeParams);

    /**
     * 审批催办
     * @param flowTaskParameter
     * @return
     */
    String urgeTask(FlowTaskParameter flowTaskParameter);

    /**
     * 审批回退
     * @param rollBackParam
     */
    void rollBack(ApprovalRollBackParamDTO rollBackParam);

    /**
     * 获取可回退节点列表
     * @param flowParameter
     */
    List<FlowTask> getRollBackNodeList(FlowParameter flowParameter);

    /**
     * 获取流程节点信息
     * @param flowCode 流程编码
     * @return
     */
    List<FlowTaskInfo> getFlowTaskList(String flowCode);

    /**
     * 获取待审批任务列表
     * @param param
     * @return
     */
    PageRows<FlowActiveTaskInfo> getApprovingTaskList(ApprovalActiveTaskParamsDTO param);

    /**
     * 根据流程实例批量获取流程进程
     * @param flowInstanceIds 流程实例id列表
     * @return
     */
    Map<String, ApprovalProgressDTO> getFlowProgressByFlowInstanceIds(List<String> flowInstanceIds);

    /**
     * 获取所有待我审批单据
     * @param approvingQueryParam
     * @return
     */
    List<ApprovingNodeInfo> getAllMyPending(ApprovingQueryParam approvingQueryParam);

    /**
     * 获取所有我已审批单据
     * @param approvingQueryParam
     * @return
     */
    List<ApprovedNodeInfo> getAllMyPended(ApprovingQueryParam approvingQueryParam);

    /**
     * 通过业务字段过滤获取待办信息
     * @param approvingQueryParam
     * @return
     */
    PageRows<ApprovingNodeInfo> getApprovingTasksByCondition(ApprovingQueryParam approvingQueryParam);

    /**
     * 通过业务字段过滤获取我已审批信息
     * @param approvingQueryParam
     * @return
     */
    PageRows<ApprovedNodeInfo> getApprovedTasksByCondition(ApprovingQueryParam approvingQueryParam);

    List<ApprovalRecordsDTO> getApprovalRecords(FlowParameter flowParameter) throws Exception;

    List<FlowNodeApproverDTO> getFlowInstanceaPnorama(String flowInstanceId);

    /**
     * 系统审批接口
     * @param opinionDTO
     * @param empNo
     * @return
     */
    Boolean systemApprove(OpinionDTO opinionDTO, String empNo);

    /**
     * 刷新moa接口
     * */
    String refreshMoaParameters(FlowBusiRefreshParamDTO flowBusiRefreshParamDTO);
}
