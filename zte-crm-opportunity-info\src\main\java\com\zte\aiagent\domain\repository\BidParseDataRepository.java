package com.zte.aiagent.domain.repository;

import com.zte.aiagent.infrastruction.access.po.BidParseDataPO;
import java.util.List;

/**
 * 解析数据存储数据访问仓储接口
 * 负责解析数据相关的数据访问操作
 *
 * <AUTHOR>
 */
public interface BidParseDataRepository {

    /**
     * 插入单条解析数据记录
     * @param bidParseData 解析数据PO对象
     * @return 影响的行数
     */
    int insert(BidParseDataPO bidParseData);

    /**
     * 批量插入解析数据记录
     * @param items 解析数据列表
     * @return 影响的行数
     */
    int batchInsert(List<BidParseDataPO> items);

    /**
     * 根据ID查询解析数据
     * @param rowId 主键ID
     * @return 解析数据PO对象
     */
    BidParseDataPO selectByPrimaryKey(String rowId);

    /**
     * 根据解析记录ID和数据类型查询所有分块数据
     * @param parseRecordId 解析记录ID
     * @param dataType 数据类型
     * @return 解析数据列表（按块索引升序排列）
     */
    List<BidParseDataPO> selectByRecordIdAndType(String parseRecordId, String dataType);

    /**
     * 根据解析记录ID查询所有关联的解析数据
     * @param parseRecordId 解析记录ID
     * @return 解析数据列表
     */
    List<BidParseDataPO> selectByParseRecordId(String parseRecordId);

    /**
     * 根据ID更新解析数据
     * @param bidParseData 解析数据PO对象
     * @return 影响的行数
     */
    int updateByPrimaryKey(BidParseDataPO bidParseData);

    /**
     * 根据ID删除解析数据
     * @param rowId 主键ID
     * @return 影响的行数
     */
    int deleteByPrimaryKey(String rowId);

    /**
     * 根据解析记录ID删除所有关联的解析数据
     * @param parseRecordId 解析记录ID
     * @return 影响的行数
     */
    int deleteByParseRecordId(String parseRecordId);

    /**
     * 根据解析记录ID和数据类型删除关联数据
     * @param parseRecordId 解析记录ID
     * @param dataType 数据类型
     * @return 影响的行数
     */
    int deleteByRecordIdAndType(String parseRecordId, String dataType);
}
