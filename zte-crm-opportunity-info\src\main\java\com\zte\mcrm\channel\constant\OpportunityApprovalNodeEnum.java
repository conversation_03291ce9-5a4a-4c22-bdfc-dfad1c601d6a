package com.zte.mcrm.channel.constant;

public enum OpportunityApprovalNodeEnum {
    /** 审批节点 */
    APPROVAL_NODE("approvalNode","审批节点"),
    /** 渠道业务部部长 */
    ARBITRATION_NODE_0_1("ARBITRATION_NODE_0_1","渠道业务部部长"),
    /** MKT综合方案部部长 */
    ARBITRATION_NODE_0_2("ARBITRATION_NODE_0_2","MKT综合方案部部长"),
    /** 总监办总监/渠道管理团队部长 */
    ARBITRATION_NODE_0_3("ARBITRATION_NODES_0_3","总监办总监/渠道管理团队部长"),
    /** MKT综合方案部部长 */
    ARBITRATION_NODE_1_1("ARBITRATION_NODE_1_1","MKT综合方案部部长"),
    /** 总监办副总监 */
    ARBITRATION_NODE_1_2("ARBITRATION_NODE_1_2","总监办副总监"),
    /** 渠道业务部部长_1 */
    ARBITRATION_NODE_1_3("ARBITRATION_NODE_1_3","渠道业务部部长"),
    /** MKT行业分管科长 */
    ARBITRATION_NODE_2_1("ARBITRATION_NODE_2_1","MKT行业分管科长"),
    /** 总监办行业科长 */
    ARBITRATION_NODE_2_2("ARBITRATION_NODE_2_2","总监办行业科长"),
    /** 渠道业务部渠道总监 */
    ARBITRATION_NODE_2_3("ARBITRATION_NODES_2_3","渠道业务部渠道总监");

    private final String nodeCode;
    private final String nodeName;

    OpportunityApprovalNodeEnum(String nodeCode, String nodeName) {
        this.nodeCode = nodeCode;
        this.nodeName = nodeName;
    }

    public String getNodeCode() {
        return nodeCode;
    }

    public String getNodeName() {
        return nodeName;
    }

    public static OpportunityApprovalNodeEnum valueOfNameOrCode(String str){
        for (OpportunityApprovalNodeEnum value : OpportunityApprovalNodeEnum.values()) {
            if (value.nodeCode.equals(str) || value.nodeName.equals(str)){
                return value;
            }
        }
        return null;
    }

}
