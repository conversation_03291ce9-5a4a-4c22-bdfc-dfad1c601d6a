package com.zte.mcrm.clues.access.dao;

import com.zte.mcrm.clues.access.vo.BusinessClues;
import org.springframework.stereotype.Repository;

/****
 *
 * <AUTHOR> @date 2021/1/21
**/
@Repository
public interface CluesSaveDao {

    /**
     * saveInfoInMainTable
     * @param vo
     */
	void saveInfoInMainTable(BusinessClues vo);

    /**
     * saveInfoInExTable
     * @param vo
     */
	void saveInfoInExTable(BusinessClues vo);

	/**
	 * 更新线索主表
	 * @param vo
	 */
	void updateInfoInMainTable(BusinessClues vo);
	/**
	 * 更新线索拓展表
	 * @param vo
	 */
	void updateInfoInExTable(BusinessClues vo);
	/**
	 * 更新金额
	 * @param vo
	 */
	void updateAmountInExTable(BusinessClues vo);
}
