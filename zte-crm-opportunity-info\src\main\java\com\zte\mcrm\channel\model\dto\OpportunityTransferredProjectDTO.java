package com.zte.mcrm.channel.model.dto;

import com.zte.mcrm.common.util.ValidationGroups;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Setter
@Getter
@ToString
public class OpportunityTransferredProjectDTO {

    @ApiModelProperty(value = "商机ID")
    @NotBlank(message = "{OpptyTeam.opptyId.empty}")
    @NotBlank(message = "{OpptyTeam.opptyId.empty}", groups = {ValidationGroups.Fasttransfer.class})
    private String rowId;

    @ApiModelProperty(value = "商机编码")
    @NotBlank(message = "{opportunity.code.null}")
    @NotBlank(message = "{opportunity.code.null}" , groups = {ValidationGroups.Fasttransfer.class})
    private String optyCd;

    @ApiModelProperty(value = "业务范围")
    @NotBlank(message = "{opportunity.businessTypeCd.null}", groups = {ValidationGroups.Fasttransfer.class})
    private String businessTypeCd;

    @ApiModelProperty(value = "最终用途")
    @NotBlank(message = "{opportunity.finalUsage.null}")
    @NotBlank(message = "{opportunity.finalUsage.null}", groups = {ValidationGroups.Fasttransfer.class})
    private String finalUsage;

    @ApiModelProperty("最终用途描述")
    @NotBlank(message = "{opportunity.specificUsageDesc.null}", groups = {ValidationGroups.Fasttransfer.class})
    @Length(max = 1000, message = "{opportunity.specificUsageDesc.lengthLimit}", groups = {ValidationGroups.Fasttransfer.class})
    private String specificUsageDesc;

    @ApiModelProperty(value = "销售模式")
    @NotBlank(message = "{opportunity.salesType.null}")
    @NotBlank(message = "{opportunity.salesType.null}", groups = {ValidationGroups.Fasttransfer.class})
    private String salesType;

    @ApiModelProperty(value = "商机执行地区id")
    @NotBlank(message = "{opportunity.nationalAreaId.null}", groups = {ValidationGroups.Fasttransfer.class})
    private String nationalAreaId;

    @ApiModelProperty(value = "商机执行地区名称")
    private String nationalAreaName;

    @ApiModelProperty(value = "商机执行省份id")
    private String nationalAreaProvinceId;

    @ApiModelProperty(value = "商机执行省份名称")
    private String nationalAreaProvinceName;

    @ApiModelProperty(value = "商机执行城市id")
    private String nationalAreaCityId;

    @ApiModelProperty(value = "商机执行城市名称")
    private String nationalAreaCityName;

    @ApiModelProperty(value = "产品信息")
    @NotEmpty(message = "{opportunity.productList.null}")
    @NotEmpty(message = "{opportunity.productList.null}", groups = {ValidationGroups.Fasttransfer.class})
    private List<OpportunityProductDTO> productList;

    @ApiModelProperty(value = "商机来源")
    @NotBlank(message = "{opportunity.dataSource.null}")
    @NotBlank(message = "{opportunity.dataSource.null}", groups = {ValidationGroups.Fasttransfer.class})
    private String dataSource;

    @ApiModelProperty(value = "渠道商客户编码")
    private String customerCode;

    @ApiModelProperty(value = "渠道商名称")
    private String customerName;

    @ApiModelProperty(value = "渠道商级别编码")
    private String agencyLevelCode;

    @ApiModelProperty(value = "渠道商级别")
    private String agencyLevelName;

    @ApiModelProperty(value = "渠道商是否受限制主体")
    private String agencyRestrictionFlag;

    @ApiModelProperty(value = "TS审批单号")
    private String tsApprovalNumber;

    @ApiModelProperty(value = "商机名称")
    @NotBlank(message = "{opportunity.name.blank}", groups = {ValidationGroups.Fasttransfer.class})
    private String attrib46;

    @ApiModelProperty(value = "投资方所在地（商机所属部门）")
    @NotBlank(message = "{deptNo.null}", groups = {ValidationGroups.Fasttransfer.class})
    private String deptNo;

    @ApiModelProperty(value = "最终用户编码")
    @NotBlank(message = "{opportunity.lastAccId.null}", groups = {ValidationGroups.Fasttransfer.class})
    private String lastAccId;

    @ApiModelProperty(value = "客户名称Code")
    @NotBlank(message = "{opportunity.signCustomerCode.null}")
    @NotBlank(message = "{opportunity.signCustomerCode.null}", groups = {ValidationGroups.Fasttransfer.class})
    private String signCustomerCode;

    @ApiModelProperty(value = "项目指委会主任")
    @NotBlank(message = "{opportunity.directorOfPsc.null}", groups = {ValidationGroups.Fasttransfer.class})
    private String directorOfPsc;

    @ApiModelProperty(value = "赢率")
    private String winRate;
}
