package com.zte.mcrm.channel.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商机详情接口出参--对外提供
 * <AUTHOR>
 * @date 2021/9/24
 */
@Data
public class ExternalOpportunityDetail {
    @ApiModelProperty(value = "商机Id")
    private String rowId;
    @ApiModelProperty(value = "商机编号")
    private String optyCd;
    @ApiModelProperty(value = "商机名称")
    private String optyName;
    @ApiModelProperty(value = "渠道商名称")
    private String channelName;
    @ApiModelProperty(value = "渠道商客户编码")
    private String crmCustomerCode;
    private String oldOppCustomerId;
    @ApiModelProperty(value = "商机状态")
    private String statusCdName;
    @ApiModelProperty(value = "中兴业务经理-工号")
    private String businessManager;
    @ApiModelProperty(value = "中兴业务经理-姓名")
    private String businessManagerName;
    @ApiModelProperty(value = "商机状态code")
    private String statusCd;
    @ApiModelProperty(value = "项目所属部门")
    private String deptName;
    @ApiModelProperty(value = "项目所属部门id")
    private String deptNo;
    @ApiModelProperty(value = "商机概况")
    private String projectDesc;
    @ApiModelProperty(value = "预计签单金额（元）")
    private BigDecimal expectSignMoney;
    @ApiModelProperty(value = "最终用户编号")
    private String finalCustomerId;
    @ApiModelProperty(value = "最终用户名称")
    private String finalCustomerName;
    @ApiModelProperty(value = "是否受限制主体")
    private String restrictedPartyOrNot;
    @ApiModelProperty(value = "最终用户行业")
    private String finalCustomerParentTradeName;
    @ApiModelProperty(value = "最终用户行业id")
    private String finalCustomerParentTrade;
    @ApiModelProperty(value = "产品范围")
    private String prodName;
    @ApiModelProperty(value = "产品ID,多个以逗号隔开")
    private String prodId;
    @ApiModelProperty(value = "报备时间")
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;
    @ApiModelProperty(value = "当前阶段编码")
    private String projectPhases;
    @ApiModelProperty(value = "当前阶段")
    private String projectPhasesName;
    @ApiModelProperty(value = "赢率编码")
    private String winRate;
    @ApiModelProperty(value = "赢率")
    private String winRateName;
    @ApiModelProperty(value = "招标类型编码")
    private String tendType;
    @ApiModelProperty(value = "招标类型")
    private String tenderTypeName;
    @ApiModelProperty(value = "招标方全称")
    private String bidProviderName;
    @ApiModelProperty(value = "预计发标时间/预计签约时间")
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date estimatedBiddingTime;
    @ApiModelProperty(value = "竞标截止时间")
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date biddingDeadline;
    @ApiModelProperty(value = "报备人姓名")
    private String agencyName;
    @ApiModelProperty(value = "报备人电话")
    private String agencyPhone;
    @ApiModelProperty(value = "报备人邮箱")
    private String agencyEmail;
    @ApiModelProperty(value = "联系人姓名")
    private String finalCustomerContactName;
    @ApiModelProperty(value = "联系人工作电话")
    private String finalCustomerContactPhone;
    @ApiModelProperty(value = "联系人邮箱")
    private String finalCustomerContactEmail;
    @ApiModelProperty(value = "最终用户子行业名称")
    private String finalCustomerChildTradeName;
    @ApiModelProperty(value = "最终用户子行业名称id")
    private String finalCustomerChildTrade;
    @ApiModelProperty(value = "TS审批单号")
    private String tsApprovalNo;
    @ApiModelProperty(value = "商机来源")
    private String dataSourceName;
}
