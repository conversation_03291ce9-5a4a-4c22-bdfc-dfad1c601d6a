package com.zte.aiagent.infrastruction.access.mapper;

import com.zte.aiagent.infrastruction.access.po.BidParseRecordPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface BidParseRecordMapper {
    /**
     * 插入解析项记录
     * @param bidParseRecord 解析项记录PO对象
     * @return 影响的行数
     */
    int insert(BidParseRecordPO bidParseRecord);

    /**
     * 根据ID查询解析项记录
     * @param rowId 主键ID
     * @return 解析项记录PO对象
     */
    BidParseRecordPO selectByPrimaryKey(String rowId);

    /**
     * 根据ID更新解析项记录
     * @param bidParseRecord 解析项记录PO对象
     * @return 影响的行数
     */
    int updateByPrimaryKey(BidParseRecordPO bidParseRecord);

    /**
     * 根据ID删除解析项记录
     * @param rowId 主键ID
     * @return 影响的行数
     */
    int deleteByPrimaryKey(String rowId);

    /**
     * 根据文档ID查询解析项记录列表
     * @param documentId 文档ID
     * @return 解析项记录列表
     */
    List<BidParseRecordPO> selectByDocumentId(String documentId);
}
