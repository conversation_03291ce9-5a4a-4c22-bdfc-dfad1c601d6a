package com.zte.aiagent.infrastruction.access.repository;

import com.zte.aiagent.domain.repository.SysConfTypeRepository;
import com.zte.aiagent.infrastruction.access.mapper.SysConfTypeMapper;
import com.zte.aiagent.infrastruction.access.po.SysConfTypePO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 系统参数配置类型数据访问仓储实现类
 * 负责系统参数配置类型相关的数据访问操作
 *
 * <AUTHOR>
 */
@Service
public class SysConfTypeRepositoryImpl implements SysConfTypeRepository {

    @Autowired
    private SysConfTypeMapper sysConfTypeMapper;

    @Override
    public int insert(SysConfTypePO sysConfType) {
        return sysConfTypeMapper.insert(sysConfType);
    }

    @Override
    public SysConfTypePO selectByPrimaryKey(String rowId) {
        return sysConfTypeMapper.selectByPrimaryKey(rowId);
    }

    @Override
    public SysConfTypePO selectByCodeTypeAndTenantId(String codeType, Long tenantId) {
        return sysConfTypeMapper.selectByCodeTypeAndTenantId(codeType, tenantId);
    }

    @Override
    public List<SysConfTypePO> selectAllValid(Long tenantId) {
        return sysConfTypeMapper.selectAllValid(tenantId);
    }

    @Override
    public int updateByPrimaryKey(SysConfTypePO sysConfType) {
        return sysConfTypeMapper.updateByPrimaryKey(sysConfType);
    }

    @Override
    public int logicDelete(String rowId, String lastUpdatedBy, Date lastUpdatedDate) {
        return sysConfTypeMapper.logicDelete(rowId, lastUpdatedBy, lastUpdatedDate);
    }
}
