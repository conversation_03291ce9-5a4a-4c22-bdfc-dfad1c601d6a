package com.zte.mcrm.common.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.commons.lang3.builder.ToStringBuilder;

/**
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class PersonAndOrgInfoVO {

    private String  id;

    /**
     * 员工姓名
     */
    private String empName;
    /**
     * 员工工号
     */
    private String empNO;
    /**
     * 员工姓名英文
     */
    private String empNameEN;

    /**
     * 组织名hrOrgID
     */
    private String hrOrgName;
    /**
     * 组织全路径
     */
    private String hrOrgNamePath;
    /**
     * 组织编码全路径
     */
    private String orgIDPath;
    /**
     * 统一组织编码
     */
    private String hrOrgID;
    /**
     * 统一组织编码父节点
     */
    private String hrOrgPID;
    /**
     * 人事旧部门编号
     */
    private String hrOldDeptNO;
    /**
     * 人事旧部门编号父节点
     */
    private String hrOldDeptPNO;
    /**邮编*/
    private String email;
    /**数据来源：股份、自公交公司*/
    private String source;

    /**是否业绩统计单位*/
    private String isPartakePerfStats;

    /**组织成立时间*/
    private String orgEstDate;

    /**机构层级（1，2，3，4，5）*/

    private String orgLevel;

    /**备注*/
    private String remark;

    /**组织状态ID（1有效，2待撤销，0已撤销 名称放多语言表）*/
    private String orgStatusID;

    /**组织有效性*/
    private String enabled;

    /**行政级别*/
    private String hrLevel;

    private String orgFullName;

    /**
     * 员工电脑号
     */
    private String computerNum;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrgEstDate() {
        return orgEstDate;
    }

    public void setOrgEstDate(String orgEstDate) {
        this.orgEstDate = orgEstDate;
    }

    public String getOrgLevel() {
        return orgLevel;
    }

    public void setOrgLevel(String orgLevel) {
        this.orgLevel = orgLevel;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOrgStatusID() {
        return orgStatusID;
    }

    public void setOrgStatusID(String orgStatusID) {
        this.orgStatusID = orgStatusID;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public String getHrLevel() {
        return hrLevel;
    }

    public void setHrLevel(String hrLevel) {
        this.hrLevel = hrLevel;
    }

    public String getIsPartakePerfStats() {
        return isPartakePerfStats;
    }

    public void setIsPartakePerfStats(String isPartakePerfStats) {
        this.isPartakePerfStats = isPartakePerfStats;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getHrOrgName() {
        return hrOrgName;
    }

    public void setHrOrgName(String hrOrgName) {
        this.hrOrgName = hrOrgName;
    }

    public String getEmpName() {
        return empName;
    }

    public void setEmpName(String empName) {
        this.empName = empName;
    }

    public String getEmpNO() {
        return empNO;
    }

    public void setEmpNO(String empNO) {
        this.empNO = empNO;
    }

    public String getEmpNameEN() {
        return empNameEN;
    }

    public void setEmpNameEN(String empNameEN) {
        this.empNameEN = empNameEN;
    }

    public String getHrOrgNamePath() {
        return hrOrgNamePath;
    }

    public void setHrOrgNamePath(String hrOrgNamePath) {
        this.hrOrgNamePath = hrOrgNamePath;
    }

    public String getOrgIDPath() {
        return orgIDPath;
    }

    public void setOrgIDPath(String orgIDPath) {
        this.orgIDPath = orgIDPath;
    }

    public String getHrOrgID() {
        return hrOrgID;
    }

    public void setHrOrgID(String hrOrgID) {
        this.hrOrgID = hrOrgID;
    }

    public String getHrOrgPID() {
        return hrOrgPID;
    }

    public void setHrOrgPID(String hrOrgPID) {
        this.hrOrgPID = hrOrgPID;
    }

    public String getHrOldDeptNO() {
        return hrOldDeptNO;
    }

    public void setHrOldDeptNO(String hrOldDeptNO) {
        this.hrOldDeptNO = hrOldDeptNO;
    }

    public String getHrOldDeptPNO() {
        return hrOldDeptPNO;
    }

    public void setHrOldDeptPNO(String hrOldDeptPNO) {
        this.hrOldDeptPNO = hrOldDeptPNO;
    }

    public String getOrgFullName() {
        return orgFullName;
    }

    public void setOrgFullName(String orgFullName) {
        this.orgFullName = orgFullName;
    }

    public String getComputerNum() {
        return computerNum;
    }

    public void setComputerNum(String computerNum) {
        this.computerNum = computerNum;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", id)
                .append("empName", empName)
                .append("empNO", empNO)
                .append("empNameEN", empNameEN)
                .append("hrOrgName", hrOrgName)
                .append("hrOrgNamePath", hrOrgNamePath)
                .append("orgIDPath", orgIDPath)
                .append("hrOrgID", hrOrgID)
                .append("hrOrgPID", hrOrgPID)
                .append("hrOldDeptNO", hrOldDeptNO)
                .append("hrOldDeptPNO", hrOldDeptPNO)
                .append("email", email)
                .append("source", source)
                .append("isPartakePerfStats", isPartakePerfStats)
                .append("orgEstDate", orgEstDate)
                .append("orgLevel", orgLevel)
                .append("remark", remark)
                .append("orgStatusID", orgStatusID)
                .append("enabled", enabled)
                .append("hrLevel", hrLevel)
                .append("orgFullName", orgFullName)
                .append("computerNum",computerNum)
                .toString();
    }
}
