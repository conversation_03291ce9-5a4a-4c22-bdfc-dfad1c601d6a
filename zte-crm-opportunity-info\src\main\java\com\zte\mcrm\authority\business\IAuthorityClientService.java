package com.zte.mcrm.authority.business;

import com.zte.itp.authorityclient.entity.output.ReturnConstraintEntity;
import com.zte.mcrm.opportunity.access.bo.RoleUser;
import com.zte.mcrm.opportunity.access.vo.RoleInfomation;

import java.util.List;
import java.util.Map;

/****
 * 统一权限平台业务实现接口**
 * @ClassName:IAuthorityClientService
 * @Description: 这里用一句话描述这个类的作用
 * @author: 6092002949
 * @date: 2021年10月14日
 */
public interface IAuthorityClientService {

    /**
     * 查询当前用户在商机模块下授权的组织
     * @param roleCode
     * @return
     */
    String getUserConstraintAuthorizationByRoleCode(String roleCode);

    /**
     * 查询当前用户在商机模块下授权的组织
     * @param roleCode
     * @param empNo
     * @return
     */
    String getUserConstraintAuthorizationByRoleCodeAndUserId(String roleCode, String empNo);

    /**
     * 查询当前用户在商机模块下授权的组织
     * @param empNo
     * @return
     */
    Map<String, ReturnConstraintEntity> getUserPowerRole(String empNo);

    /**
     * 查询当前用户在对应模块下授权的组织
     * @param functionCode
     * @return
     */
    Map<String, ReturnConstraintEntity> getUserPowerFunctionCode(String functionCode);

    /**
     * 获取资源下的数据授权Map
     * @param functionCode
     * @param emptyId
     * @param token
     * @return
     */
    Map<String, ReturnConstraintEntity> getUserPowerFunctionCodeAndUserId(String functionCode, String emptyId, String token);

    /**
     * 获取用户所有角色
     * @param empNo
     * @return
     * @throws Exception
     */
    List<RoleInfomation> queryUserRole(String empNo) throws Exception;

    /**
     * 根据角色code查询角色id
     * @param roleCode
     * @return
     * @throws Exception
     */
    Long queryRoleIdByCode(String roleCode) throws Exception;

    /**
     * 根据角色id和授权数据查询人员
     * @param roleId
     * @param authOrg
     * @param authProduct
     * @return
     * @throws Exception
     */
    List<RoleUser> queryPersonByRoleIdAndAuthData(Long roleId, String authOrg, String authProduct) throws Exception;
}
