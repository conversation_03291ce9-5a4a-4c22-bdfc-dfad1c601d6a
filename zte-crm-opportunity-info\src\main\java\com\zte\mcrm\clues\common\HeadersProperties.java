package com.zte.mcrm.clues.common;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 *
 * @ClassName: HeadersProperties
 * @Description: TODO(这里用一句话描述这个类的作用)
 * <AUTHOR>
 * @date 2019年1月5日
 *
 */
public class HeadersProperties {
    /**
     * 语言参数：例如 en，zh，默认为zh
     */
    public static final String X_LANG_ID = "X-Lang-Id";
    /**
     * 员工短工号，股份的8位，其他的10位/系统识别码
     */
    public static final String X_EMP_NO = "X-Emp-No";
    /**
     * 租户ID；默认：100000
     */
    public static final String X_TENANT_ID = "X-Tenant-Id";
    /**
     * 该租户下的多组织ID；默认：100000
     */
    public static final String X_ORG_ID = "X-Org-Id";
    /**
     * 身份令牌参数：单点登录后获取的token串/系统识别码；默认从单点登录中拿，拿不到传空
     */
    public static final String X_AUTH_VALUE = "X-Auth-Value";
    /**
     * 服务名
     */
    public static final String X_ORIGIN_SERVICENAME = "X-Origin-ServiceName";
    public static final String X_ITP_VALUE = "X-Itp-Value";
    public static final String X_INTERFACE_KEY = "X-Interface-Key";

    public static HttpServletRequest getHttpServletRequest() {
        if(RequestContextHolder.getRequestAttributes() != null) {
            return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        }

        return null;
    }

    public static String getXLangId() {
        HttpServletRequest r = getHttpServletRequest();
        return r == null?null:r.getHeader(X_LANG_ID);
    }

    public static String getXEmpNo() {
        HttpServletRequest r = getHttpServletRequest();
        return r == null?null:r.getHeader(X_EMP_NO);
    }
}
