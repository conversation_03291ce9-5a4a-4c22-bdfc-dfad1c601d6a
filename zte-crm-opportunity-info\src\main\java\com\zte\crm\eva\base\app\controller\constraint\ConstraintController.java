/* Started by AICoder, pid:acc27le610g877c149cc0b33f021cf33cd695651 */
package com.zte.crm.eva.base.app.controller.constraint;

import com.google.common.collect.Lists;
import com.zte.mcrm.common.consts.CommonConstant;
import com.zte.opty.common.constants.CommonConst;
import com.zte.springbootframe.common.model.ServiceData;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

import static com.zte.mcrm.common.consts.CommonConstant.EN_US;
import static com.zte.mcrm.common.consts.CommonConstant.ZH_CN;

/**
 * 权限约束
 */

@RequestMapping("/constraint")
@RestController
public class ConstraintController {

    /**
     * 新业务约束
     * @return
     */
    @RequestMapping("/getNewBusinessConstraint")
    public ServiceData getConstraint() {
        return new ServiceData(){{setBo(Lists.newArrayList(new HashMap() {{
            put(CommonConst.MessageDataParams.ID, CommonConstant.EnableFlagEnum.Y.getKey());
            put(ZH_CN, CommonConstant.EnableFlagEnum.Y.getZhMsg());
            put(EN_US, CommonConstant.EnableFlagEnum.Y.getEnMsg());
        }}, new HashMap() {{
            put(CommonConst.MessageDataParams.ID, CommonConstant.EnableFlagEnum.N.getKey());
            put(ZH_CN, CommonConstant.EnableFlagEnum.N.getZhMsg());
            put(EN_US, CommonConstant.EnableFlagEnum.N.getEnMsg());
        }}));}};
    }
}
/* Ended by AICoder, pid:acc27le610g877c149cc0b33f021cf33cd695651 */