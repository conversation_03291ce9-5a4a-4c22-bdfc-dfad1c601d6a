package com.zte.aiagent.domain.repository;

import com.zte.aiagent.infrastruction.access.po.SysConfTypePO;
import java.util.Date;
import java.util.List;

/**
 * 系统参数配置类型数据访问仓储接口
 * 负责系统参数配置类型相关的数据访问操作
 *
 * <AUTHOR>
 */
public interface SysConfTypeRepository {

    /**
     * 插入系统参数配置类型记录
     * @param sysConfType 系统参数配置类型PO对象
     * @return 影响的行数
     */
    int insert(SysConfTypePO sysConfType);

    /**
     * 根据ID查询系统参数配置类型
     * @param rowId 主键ID
     * @return 系统参数配置类型PO对象
     */
    SysConfTypePO selectByPrimaryKey(String rowId);

    /**
     * 根据编码类型和租户ID查询系统参数配置类型
     * @param codeType 编码类型
     * @param tenantId 租户ID
     * @return 系统参数配置类型PO对象
     */
    SysConfTypePO selectByCodeTypeAndTenantId(String codeType, Long tenantId);

    /**
     * 查询所有有效系统参数配置类型
     * @param tenantId 租户ID
     * @return 系统参数配置类型列表
     */
    List<SysConfTypePO> selectAllValid(Long tenantId);

    /**
     * 根据ID更新系统参数配置类型
     * @param sysConfType 系统参数配置类型PO对象
     * @return 影响的行数
     */
    int updateByPrimaryKey(SysConfTypePO sysConfType);

    /**
     * 逻辑删除系统参数配置类型（将enabled_flag设为N）
     * @param rowId 主键ID
     * @param lastUpdatedBy 最后更新人
     * @param lastUpdatedDate 最后更新时间
     * @return 影响的行数
     */
    int logicDelete(String rowId, String lastUpdatedBy, Date lastUpdatedDate);
}
