package com.zte.mcrm.channel.dao;

import com.zte.mcrm.channel.model.entity.OpportunityDetail;
import com.zte.mcrm.channel.model.entity.OpportunityMailEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 *  数据访问接口类 
 * <AUTHOR>
 * @date 2021/09/14 
 */
@Mapper
public interface OpportunityDetailDao {
    /**
     * 根据主键查询
     * <AUTHOR>
     * @param rowId 主键
     * @date 2021/09/14 
     * @return 实体
     */
	OpportunityDetail get(@Param("rowId")String rowId);
	
    /**
     * 查询列表
     * <AUTHOR>
     * @param map 查询条件
     * @date 2021/09/14 
     * @return 实体集合
     */
	List<OpportunityDetail> getList(Map<String, Object> map);

	List<OpportunityDetail> getNoCrmCustomerCodeList(@Param("type")String type);

	List<OpportunityDetail> getNoLastAccNameList();
	
	/**
     * 软删除，enabled_flag字段更新为N
     * <AUTHOR>
     * @param rowId 主键
     * @date 2021/09/14 
     * @return 删除总数
     */	
	int softDelete(@Param("rowId")String rowId);


	/**
	 * 删除
	 * <AUTHOR>
	 * @date 2021/09/14
	 * @return 删除总数
	 */
	int deleteByOptyIds(@Param("optyIds")List<String> optyIds);


	int softDeleteByOptyIds(@Param("optyIds")List<String> optyIds);

    /**
     * 动态新增
     * <AUTHOR>
     * @param entity 新增实体
     * @date 2021/09/14 
     * @return 新增总数
     */	
	int insert(OpportunityDetail entity);


    /**
     * 更新
     * <AUTHOR>
     * @param entity 更新条件
     * @date 2021/09/14 
     * @return 更新影响总数
     */		
	int update(OpportunityDetail entity);

	/**
	 * 全量更新
	 * <AUTHOR>
	 * @param entity 需要更新的实体
	 * @date 2021/09/14
	 * @return 更新影响总数
	 */
	int updateAll(OpportunityDetail entity);

	int markReportDataMigration(@Param("optyIds")List<String> optyIds);

    /**
     * 统计
     * <AUTHOR>
     * @param map 查询条件
     * @date 2021/09/14 
     * @return 统计总数
     */	
	long getCount(Map<String, Object> map);

    /**
     * 分页查询
     * <AUTHOR>
     * @param map 查询条件
     * @date 2021/09/14 
     * @return 实体集合
     */	
	List<OpportunityDetail> getPage(Map<String, Object> map);

	/**
	 * 查询最终用户ID
	 * <AUTHOR>
	 * @param rowId 查询条件
	 */
	String getLastAccId(String rowId);

	/**
	 * 根据lastAccStatus查询数据
	 * <AUTHOR>
	 * @param lastAccStatus 最终用户状态
	 */
	List<OpportunityDetail> getOpportunityDetailByLastAccStatus(Integer lastAccStatus);

	/**
	 * 更新报备审批信息
	 * @param entity
	 */
	int updateOpportunityApproveInfo(OpportunityDetail entity);

	/**
	 *
	 * @param rowId
	 * @return
	 */
	OpportunityMailEntity getOpportunityMailEntityByRowId(String rowId);
}
