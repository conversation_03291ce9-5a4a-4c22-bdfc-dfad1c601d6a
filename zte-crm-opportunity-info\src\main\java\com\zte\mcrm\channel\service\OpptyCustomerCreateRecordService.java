package com.zte.mcrm.channel.service;

import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.channel.model.entity.IChannelOpptyCustomerCreateRecordQuery;
import com.zte.mcrm.channel.model.entity.OpptyCustomerCreateRecord;
import com.zte.mcrm.channel.model.entity.PrmOpptyCustomerCreateRecordQuery;
import com.zte.mcrm.channel.model.vo.OpptyCustomerCreateRecordVO;

import java.util.List;
import java.util.Map;


/**
 * 渠道商新建最终客户记录 服务接口类
 * <AUTHOR>
 * @date 2023/05/10  
 */
public interface OpptyCustomerCreateRecordService {
    /**
     * 根据ID查询
     * @param rowId 主键ID
     * @return 实体
     * <AUTHOR>
     * @date 2023/05/10
     */
	OpptyCustomerCreateRecord get(String rowId);

    /**
     * 查询列表
     * @param map 参数集合
     * @return 实体集合
     * <AUTHOR>
     * @date 2023/05/10
     */
	List<OpptyCustomerCreateRecord> getList(Map<String, Object> map);

    /**
     * 软删除
     * @param rowId 主键ID
     * @return 删除记录个数
     * <AUTHOR>
     * @date 2023/05/10
     */
	int softDelete(String rowId);
	
	/**
     * 删除
     * @param rowId 主键ID
     * @return 删除记录个数
     * <AUTHOR>
     * @date 2023/05/10
     */
	int delete(String rowId);

    /**
     * 新增
     * @param entity 实体对象
     * @return 新增的记录对象,注意是提交数据库之前的实体对象
     * <AUTHOR>
     * @date 2023/05/10
     */
	OpptyCustomerCreateRecord insert(OpptyCustomerCreateRecord entity);

    /**
     * 更新
     * @param entity 实体对象
     * @return 修改的记录对象,注意是提交数据库之前的实体对象
     * <AUTHOR>
     * @date 2023/05/10
     */
	OpptyCustomerCreateRecord update(OpptyCustomerCreateRecord entity);

    /**
     * 统计
     * @param map 参数集合
     * @return 统计总数
     * <AUTHOR>
     * @date 2023/05/10
     */
	long getCount(Map<String, Object> map);

   /**
    * 分页查询
    * @param map 参数集合
    * @return 实体集合
    * <AUTHOR>
    * @date 2023/05/10
    */
	List<OpptyCustomerCreateRecord> getPage(Map<String, Object> map);

    /**
    * 分页查询
    * @param form 参数集合
    * @return 实体集合
    * <AUTHOR>
    * @date 2023/05/10
    */
    PageRows<OpptyCustomerCreateRecord> getPageRows(FormData<OpptyCustomerCreateRecord> form);

    /**
     * PRM分页查询
     *
     * @param formData 参数集合
     * @return 实体集合
     * @throws Exception
     * <AUTHOR>
     * @date 2023/05/10
     */
    PageRows<OpptyCustomerCreateRecordVO> queryCustomerRecordWithPrm(FormData<PrmOpptyCustomerCreateRecordQuery> formData) throws Exception;

    /**
     * iChannel分页查询最终客户创建记录
     *
     * @param formData 参数集合
     * @return 实体集合
     * @throws Exception
     * <AUTHOR>
     * @date 2023/05/10
     */
    PageRows<OpptyCustomerCreateRecordVO> queryCustomerRecordWithIchannel(FormData<IChannelOpptyCustomerCreateRecordQuery> formData) throws Exception;


    /**
     * 定时任务刷新客户编码
     * */
    void refreshCustomerStatus();
}