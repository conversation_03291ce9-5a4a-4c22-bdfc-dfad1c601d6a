package com.zte.mcrm.channel.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@ToString
public class OpportunityProductDTO {

    @ApiModelProperty(value = "是否主产品")
    private String zteMainProduct;

    @ApiModelProperty(value = "体系内部分类Id")
    private String prodLv1Id;

    @ApiModelProperty(value = "体系内部分类名称")
    private String prodLv1Name;

    @ApiModelProperty(value = "大产品线Id")
    private String prodLv2Id;

    @ApiModelProperty(value = "大产品线")
    private String prodLv2Name;

    @ApiModelProperty(value = "产品线Id")
    private String prodLv21Id;

    @ApiModelProperty(value = "产品线")
    private String prodLv21Name;

    @ApiModelProperty(value = "产品大类Id")
    private String prodLv3Id;

    @ApiModelProperty(value = "产品大类")
    private String prodLv3Name;

    @ApiModelProperty(value = "产品小类Id")
    private String prodLv4Id;

    @ApiModelProperty(value = "产品小类")
    private String prodLv4Name;

    @ApiModelProperty(value = "产品预计签单金额")
    private BigDecimal productAmount;

    @JsonFormat(pattern ="yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "预计签单时间")
    private Date forSignDate;
}
