package com.zte.mcrm.clues.util;

import com.zte.itp.authorityclient.entity.output.ReturnConstraintEntity;
import com.zte.mcrm.clues.common.CluesSysConst;
import com.zte.mcrm.opportunity.access.dao.PCOpportunityDao;
import com.zte.mcrm.opportunity.access.vo.Auth;
import com.zte.mcrm.opportunity.common.OppSysConst;
import com.zte.mcrm.opportunity.utils.OppAuthUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: 10261899
 * @Date: 2019/12/5
 * @Description:
 */
public class CluesAuthUtil {

    public static Auth getAuthOrgs(PCOpportunityDao pcOpportunityDao){

        Auth auth=new Auth();
        Map<String, ReturnConstraintEntity> dataConstrainMap = OppAuthUtils.getUserPower(CluesSysConst.CLUE_AUTH_CODE);
        //授权管理员
        List<String> authAdministratorList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.AUTH_ADMINISTRATOR_ORG)).orElse(new ArrayList<>());
        // 商机综合查询员
        List<String> opportunityQueryList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.OPPORTUNITY_QUERY_ORG)).orElse(new ArrayList<>());
        // 国代/办事处经理
        List<String> officeManagerList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.OFFICE_MANAGER_ORG)).orElse(new ArrayList<>());
        //副国代/办事处经理
        List<String> viceOfficeManagerList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.VICE_OFFICE_MANAGER)).orElse(new ArrayList<>());
        //总监办客户经理
        List<String> directorAccountManagerList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.DIRECTOR_ACCOUNT_MANAGER_ORG)).orElse(new ArrayList<>());
        //商机总监
        List<String> opportunityDirectorList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.OPPORTUNITY_DIRECTOR_ORG)).orElse(new ArrayList<>());
        //大国CTO/片区CMO
        List<String> ctoList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.CTO_ORG)).orElse(new ArrayList<>());
        //服务商机总监
        List<String> serviceOpportunityDirectorList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.SERVICE_OPPORTUNITY_DIRECTOR_ORG)).orElse(new ArrayList<>());
        // 片区技术负责人
        List<String> areaTechManagerList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.AREA_TECH_MANAGER_ORG)).orElse(new ArrayList<>());
        //商机导出
        List<String> opportunityExportList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.OPPORTUNITY_EXPORT_ORG)).orElse(new ArrayList<>());
        // 商机客户经理
        List<String> opportunityAccountManagerList = Optional.ofNullable(OppAuthUtils.getAuthDataByConstrainCode(dataConstrainMap, OppSysConst.OPPORTUNITY_ACCOUNT_MANAGER_ORG)).orElse(new ArrayList<>());

        // 汇总所有授权组织
        authAdministratorList.addAll(authAdministratorList);
        authAdministratorList.addAll(opportunityQueryList);
        authAdministratorList.addAll(officeManagerList);
        authAdministratorList.addAll(viceOfficeManagerList);
        authAdministratorList.addAll(directorAccountManagerList);
        authAdministratorList.addAll(opportunityDirectorList);
        authAdministratorList.addAll(ctoList);
        authAdministratorList.addAll(serviceOpportunityDirectorList);
        authAdministratorList.addAll(areaTechManagerList);
        authAdministratorList.addAll(opportunityExportList);
        authAdministratorList.addAll(opportunityAccountManagerList);

        // 组织去重
        List<String> orgDistinct = authAdministratorList.stream().distinct().collect(Collectors.toList());
        // 转换成siebel的组织id
        if (null != orgDistinct && !orgDistinct.isEmpty()) {
            Collections.replaceAll(orgDistinct,OppSysConst.ORG_ZTE_HR, OppSysConst.SIEBEL_ZTE_ORG);
            List<String> ids = pcOpportunityDao.getIdsByOrgNos(orgDistinct);
            auth.setSubOrgs(ids);
        }else {
            List<String> orgs=new ArrayList<>();
            auth.setSubOrgs(orgs);
        }

        return auth;
    }
}
