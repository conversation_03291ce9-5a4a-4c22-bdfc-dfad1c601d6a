package com.zte.mcrm.channel.service.channel;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.approval.model.ApprovalResponseBO;
import com.zte.mcrm.channel.model.dto.HistApproveDataDTO;
import com.zte.mcrm.channel.model.dto.InsFlow;
import com.zte.mcrm.channel.model.dto.InsTask;
import com.zte.mcrm.channel.model.entity.OpportunityKeyInfoEntity;
import com.zte.springbootframe.common.exception.BusiException;
import org.apache.http.entity.mime.content.ByteArrayBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface IOpportunityMigrationService {

    /**
     * 批量失效
     * @param rowIds
     * @return
     */
    String invalid(List<String> rowIds);

    /**
     * 更新渠道商客户编码及名称
     * @param type
     * @return
     * @throws BusiException
     */
    List<String> refreshCrmCustomerCode(String type) throws BusiException;

    /**
     * 更新最终用户名称
     * @return
     * @throws BusiException
     */
    Integer updateLastAccNameByOldOpp() throws BusiException;

    ByteArrayBody getFailLogAndRollBack(String serialNumber, String type);

    void rollBackByTask(List<InsTask> rollBackInsTask);

    void rollBackByFlow(List<InsFlow> rollBackInsFlow);

    ServiceData<ApprovalResponseBO> sendApprovalCenterExcel(MultipartFile excelFile, String type);

    /**
     * 更新报备成功时间
     * @return
     * @throws BusiException
     */
    List<String> updateApproveDate(String type, int limit);

    /**
     * 更新中兴业务经理
     * @return
     */
    String updateBusinessManager() throws BusiException;

    List<OpportunityKeyInfoEntity> adaptToTheNewProcess() throws Exception;

    void createCustomerAndStartFlow(OpportunityKeyInfoEntity entity) throws Exception;
}
