package com.zte.mcrm.common.business;

import com.zte.mcrm.common.access.vo.ApproveHeadVO;

import java.util.List;

/****
 * 审批
 * @ClassName IApproveBusinessService
 * <AUTHOR>
 * @Date 2021/3/523
 * @Version V1.0
 **/
public interface IApproveBusinessService {

    /**
     * 根据评审对象ID(例：商机头表ID)查询评审人(approveEmpId)
     * @param objectIdList
     * @return
     */
    List<ApproveHeadVO> getApproveUserNoByObjectIdList(List<String> objectIdList);
}
