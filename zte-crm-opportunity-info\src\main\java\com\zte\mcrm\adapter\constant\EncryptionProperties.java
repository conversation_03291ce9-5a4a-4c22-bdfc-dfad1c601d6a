package com.zte.mcrm.adapter.constant;


import com.zte.mcrm.common.business.service.SensitiveEncryptor;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
@ConfigurationProperties(prefix = "encryption")
@PropertySource(value = "classpath:${spring.application.name}.properties", ignoreResourceNotFound = true)
@Data
public class EncryptionProperties {
    @Value("${encryption.secretKeySixteen}")
    private String secretKeySixteen;

    @Value("${encryption.iv}")
    private String iv;

    @PostConstruct
    private void init(){
        SensitiveEncryptor.getInstance(secretKeySixteen, iv);
    }
}
