package com.zte.mcrm.channel.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.model.vo.PrmQuickCodeValueVO;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.channel.model.dto.OrgProductDTO;
import com.zte.mcrm.channel.service.channel.IOpportunityService;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.opty.model.dto.IHolOrgQueryDTO;
import com.zte.opty.model.vo.IHolOrgVO;
import com.zte.opty.service.SOpportunityExternalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CommonRemoteUtils {

    @Autowired
    IOpportunityService opportunityService;

    @Autowired
    SOpportunityExternalService sOpportunityExternalService;

    public List<IHolOrgVO> queryOrg(List<String> codeList) {
        IHolOrgQueryDTO queryDTO = new IHolOrgQueryDTO();
        queryDTO.setCodeList(codeList);
        queryDTO.setPageSize(codeList.size());
        ServiceData<PageRows<IHolOrgVO>> pageRowsServiceData =
                sOpportunityExternalService.queryOrgTree(queryDTO);
        return Optional.ofNullable(pageRowsServiceData.getBo()).orElse(new PageRows<>()).getRows();
    }

    /**
     * 获取组织产品信息
     * @return
     */
    public OrgProductDTO getOrgProduct() {
        List<PrmQuickCodeValueVO> prmQuickCodeValues = opportunityService.queryByCodeType(Collections.singletonList(OpportunityConstant.OPTY_SOURCE));
        List<OrgProductDTO> orgProductDTOList = prmQuickCodeValues.stream().filter(e -> StringUtils.equals(e.getValueCode(), CommonUtils.getSubTenantId())).map(e -> JSON.parseObject(e.getExpand(),
                new TypeReference<OrgProductDTO>() {
                })).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orgProductDTOList)) {
            throw new BusiException(RetCode.BUSINESSERROR_CODE, "映射关系未配置");
        }
        return orgProductDTOList.get(0);
    }
}
