package com.zte.mcrm.clues.access.vo;

import java.io.Serializable;
import java.util.List;

import com.zte.itp.msa.core.model.HeaderData;
import com.zte.mcrm.opportunity.access.vo.ListOfValueOpty;

/****
 *
 * <AUTHOR> @date 2021/1/21
**/
public class BusinessCluesInfoVO extends HeaderData implements Serializable {
	private static final long serialVersionUID = 7567293037569047140L;
    /**  线索id*/
    private String id;
    /**  线索编号*/
    private String clueNum;
    /**  线索名称*/
    private String clueName;
    /** 线索详情*/
    private BusinessClues businessClues;
    /** 业务范围：值列表 */
    private List<ListOfValueOpty> businessTypeList;
    /** 线索来源：值列表*/
    private List<ListOfValueOpty> clueSourceList;
    /** 销售模式：值列表*/
    private List<ListOfValueOpty> saleModeList;
    /** 客户类型：值列表*/
    private List<ListOfValueOpty> custTypeList;
    /** 行业：值列表*/
    private List<ListOfValueOpty> parentTradeList;
    /** 子行业：值列表*/
    private List<ListOfValueOpty> childTradeList;
    /** 市场类型*/
    private List<ListOfValueOpty> makeTypeList;
    /** 是否融资*/
    private List<ListOfValueOpty> foundFlagList;
    /** 客户属性*/
    private List<ListOfValueOpty> accountAttributeList;
    /** 潜在融资模式*/
    private List<ListOfValueOpty> potentialFoundModelList;


	public List<ListOfValueOpty> getFoundFlagList() {
		return foundFlagList;
	}
	public void setFoundFlagList(List<ListOfValueOpty> foundFlagList) {
		this.foundFlagList = foundFlagList;
	}
	public List<ListOfValueOpty> getAccountAttributeList() {
		return accountAttributeList;
	}
	public void setAccountAttributeList(List<ListOfValueOpty> accountAttributeList) {
		this.accountAttributeList = accountAttributeList;
	}
	public List<ListOfValueOpty> getPotentialFoundModelList() {
		return potentialFoundModelList;
	}
	public void setPotentialFoundModelList(List<ListOfValueOpty> potentialFoundModelList) {
		this.potentialFoundModelList = potentialFoundModelList;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getClueNum() {
		return clueNum;
	}
	public void setClueNum(String clueNum) {
		this.clueNum = clueNum;
	}
	public String getClueName() {
		return clueName;
	}
	public void setClueName(String clueName) {
		this.clueName = clueName;
	}
	public BusinessClues getBusinessClues() {
		return businessClues;
	}
	public void setBusinessClues(BusinessClues businessClues) {
		this.businessClues = businessClues;
	}
	public List<ListOfValueOpty> getClueSourceList() {
		return clueSourceList;
	}
	public void setClueSourceList(List<ListOfValueOpty> clueSourceList) {
		this.clueSourceList = clueSourceList;
	}
	public List<ListOfValueOpty> getSaleModeList() {
		return saleModeList;
	}
	public void setSaleModeList(List<ListOfValueOpty> saleModeList) {
		this.saleModeList = saleModeList;
	}
	public List<ListOfValueOpty> getBusinessTypeList() {
		return businessTypeList;
	}
	public void setBusinessTypeList(List<ListOfValueOpty> businessTypeList) {
		this.businessTypeList = businessTypeList;
	}
	public List<ListOfValueOpty> getCustTypeList() {
		return custTypeList;
	}
	public void setCustTypeList(List<ListOfValueOpty> custTypeList) {
		this.custTypeList = custTypeList;
	}
	public List<ListOfValueOpty> getParentTradeList() {
		return parentTradeList;
	}
	public void setParentTradeList(List<ListOfValueOpty> parentTradeList) {
		this.parentTradeList = parentTradeList;
	}
	public List<ListOfValueOpty> getChildTradeList() {
		return childTradeList;
	}
	public void setChildTradeList(List<ListOfValueOpty> childTradeList) {
		this.childTradeList = childTradeList;
	}
	public List<ListOfValueOpty> getMakeTypeList() {
		return makeTypeList;
	}
	public void setMakeTypeList(List<ListOfValueOpty> makeTypeList) {
		this.makeTypeList = makeTypeList;
	}

    @Override
    public String toString() {
        return "BusinessCluesInfoVO{" +
                "id='" + id + '\'' +
                ", clueNum='" + clueNum + '\'' +
                ", clueName='" + clueName + '\'' +
                ", businessClues=" + businessClues +
                ", businessTypeList=" + businessTypeList +
                ", clueSourceList=" + clueSourceList +
                ", saleModeList=" + saleModeList +
                ", custTypeList=" + custTypeList +
                ", parentTradeList=" + parentTradeList +
                ", childTradeList=" + childTradeList +
                ", makeTypeList=" + makeTypeList +
                ", foundFlagList=" + foundFlagList +
                ", accountAttributeList=" + accountAttributeList +
                ", potentialFoundModelList=" + potentialFoundModelList +
                '}';
    }
}
