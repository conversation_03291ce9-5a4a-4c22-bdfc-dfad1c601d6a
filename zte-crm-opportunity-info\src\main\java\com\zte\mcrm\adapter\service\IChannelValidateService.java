package com.zte.mcrm.adapter.service;

import com.zte.mcrm.channel.model.dto.CrmCustomerDertimineParamDTO;
import com.zte.mcrm.channel.model.vo.CrmCustomerDetermineResultVO;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;

import java.util.List;
import java.util.Map;

public interface IChannelValidateService {

    /**
     * 根据渠道商编码及年份获取是否存在有效商机（政企方案&签约）
     * @param crmCustomerDertimineParamDTO
     * @return
     */
    List<CrmCustomerDetermineResultVO> determineIntransitsAndValidByGov(CrmCustomerDertimineParamDTO crmCustomerDertimineParamDTO) throws RouteException;

    /**
     * 根据渠道商编码及年份获取是否存在有效商机（订单）
     * @param crmCustomerDertimineParamDTO
     * @return
     */
    List<CrmCustomerDetermineResultVO> determineIntransitsAndValidByOrderForm(CrmCustomerDertimineParamDTO crmCustomerDertimineParamDTO) throws RouteException;

    /**
     * 根据渠道商编码及年份获取是否存在有效商机（项目授权）
     * @param crmCustomerDertimineParamDTO
     * @return
     */
    List<CrmCustomerDetermineResultVO> determineIntransitsAndValidByAuth(CrmCustomerDertimineParamDTO crmCustomerDertimineParamDTO) throws RouteException;

    /**
     * 根据渠道商编码及年份获取是否存在有效商机
     * @param crmCustomerDertimineParamDTO
     * @param serviceName
     * @param url
     * @return
     * @throws RouteException
     */
    List<CrmCustomerDetermineResultVO> determineIntransitsAndValid(CrmCustomerDertimineParamDTO crmCustomerDertimineParamDTO, String serviceName, String url) throws RouteException;

    /**
     * 根据渠道商编码及年份获取是否存在有效商机（配置报价）
     * @param crmCustomerDertimineParamDTO
     * @return
     */
    List<CrmCustomerDetermineResultVO> determineIntransitsAndValidByCpq(CrmCustomerDertimineParamDTO crmCustomerDertimineParamDTO) throws RouteException;

}
