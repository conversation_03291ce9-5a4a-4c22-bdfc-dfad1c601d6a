<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.leadinfo.common.mapper.CxDocumentMapper">

    <resultMap id="BaseResultMap" type="com.zte.leadinfo.common.entity.CxDocumentDO">
        <id property="rowId" column="ROW_ID" jdbcType="VARCHAR"/>
        <result property="created" column="CREATED" jdbcType="TIMESTAMP"/>
        <result property="createdBy" column="CREATED_BY" jdbcType="VARCHAR"/>
        <result property="lastUpd" column="LAST_UPD" jdbcType="TIMESTAMP"/>
        <result property="lastUpdBy" column="LAST_UPD_BY" jdbcType="VARCHAR"/>
        <result property="billCode" column="BILL_CODE" jdbcType="VARCHAR"/>
        <result property="businessId" column="BUSINESS_ID" jdbcType="VARCHAR"/>
        <result property="encrypt" column="ENCRYPT" jdbcType="VARCHAR"/>
        <result property="businessType" column="BUSINESS_TYPE" jdbcType="VARCHAR"/>
        <result property="comments" column="COMMENTS" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ROW_ID,CREATED,CREATED_BY,
        LAST_UPD,LAST_UPD_BY,BILL_CODE,
        BUSINESS_ID,ENCRYPT,BUSINESS_TYPE,
        COMMENTS
    </sql>

</mapper>