package com.zte.mcrm.channel.constant;

import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.util.CommonUtils;

import java.util.Objects;

/**
 * 受限制主体枚举类
 * <AUTHOR>
 * @date 2021/10/14
 */
public enum RestrictedPartyEnum {
    /**
     * 是
     */
    Y("yes","Y","是"),

    YES("yes","yes","是"),
    /**
     * 否
     */
    N("no","N","否"),

    NO("no", "no","否"),
    /**
     * 禁运国
     */
    EMBARGO("embargo","Embargo","禁运国"),
    /**
     * 待确认
     */
    PENDING("pending", "Pending","待确认");

    private String descEn;
    private String descCn;
    private String code;

    public String getDescEn() {
        return descEn;
    }

    public String getDescCn() {
        return descCn;
    }

    public String getCode() {
        return code;
    }

    RestrictedPartyEnum(String code, String descEn, String descCn) {
        this.descEn = descEn;
        this.descCn = descCn;
        this.code = code;
    }

    /**
     * 根据状态码属性获取对应
     * @param descCn
     * @return
     */
    public static String getEnByCn(String descCn){
        for (RestrictedPartyEnum item : RestrictedPartyEnum.values()){
            if (item.getDescCn().equals(descCn)){
                return item.getDescEn();
            }
        }
        return null;
    }

    /*
     * 根据状态码属性获取对应
     * @param descCn
     * @return
     */
    public static String getCnByEn(String descEn){
        for (RestrictedPartyEnum item : RestrictedPartyEnum.values()){
            if (item.getDescEn().equalsIgnoreCase(descEn)){
                return item.getDescCn();
            }
        }
        return descEn;
    }

    public static String getNameByCode(String code){
        boolean isZh = CommonConst.ZH_CN.equalsIgnoreCase(CommonUtils.getxLangId());
        for (RestrictedPartyEnum item : RestrictedPartyEnum.values()){
            if (item.getCode().equalsIgnoreCase(code)){
                return isZh? item.getDescCn() : item.descEn;
            }
        }
        return code;
    }

    public static RestrictedPartyEnum valueOfCode(String code){
        for (RestrictedPartyEnum value : RestrictedPartyEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)){
                return value;
            }
        }
        return null;
    }

    /**
     * 受限制主体为“是”、“禁运国“、”待确认“时 返回true
     * @param restrictedPartyCode
     * @return
     */
    public static Boolean isRestrictedParty(String restrictedPartyCode) {
        return Objects.equals(restrictedPartyCode, EMBARGO.code)
                || Objects.equals(restrictedPartyCode, YES.code)
                || Objects.equals(restrictedPartyCode, Y.code)
                || Objects.equals(restrictedPartyCode, PENDING.code);
    }
}
