package com.zte.mcrm.adapter.projectauthorization.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class OppAuthInfo {
    @ApiModelProperty("授权状态")
    private String authStatus;
    @ApiModelProperty("授权编码")
    private String authorizedCode;
    @ApiModelProperty("创建时间")
    private String createTime;
    @ApiModelProperty("授权id")
    private String id;
    @ApiModelProperty("授权链接")
    private String url;
}
