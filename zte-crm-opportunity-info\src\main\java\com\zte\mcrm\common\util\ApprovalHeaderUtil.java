package com.zte.mcrm.common.util;

import com.zte.mcrm.adapter.constant.HeaderNameConst;
import com.zte.mcrm.adapter.constant.Sha256EncryptUtil;
import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 消息头工具类
 *
 * <AUTHOR> weiyiqing
 * @date 2021/4/11
 * @since v1.0
 */
public class ApprovalHeaderUtil {

    /**
     * 私有构建函数
     */
    private ApprovalHeaderUtil() {
    }

    /**
     * 获取规范化的消息头
     * @param accessKey 应用接入key，由审批中心分配，即appCode
     * @param secretKey 应用接入密钥，由审批中心分配，线下提供
     * @return 规范化填充后的消息头
     */
    public static Map<String, String> getHeaderParamsMap(String accessKey, String secretKey) {
        return getHeaderParamsMap(accessKey, secretKey, null);
    }

    /**
     * 在已有消息头上追加规范化的消息头内容
     * @param accessKey 应用接入key，由审批中心分配，即appCode
     * @param secretKey 应用接入密钥，由审批中心分配，线下提供
     * @param headerParamsMap 在已有消息头内容
     * @return 追加规范化内容后的消息头
     */
    public static Map<String, String> getHeaderParamsMap(String accessKey, String secretKey, Map<String,String> headerParamsMap) {
        if (null == headerParamsMap) {
            headerParamsMap = new HashMap<>(16);
        }

        headerParamsMap.putIfAbsent(HeaderNameConst.X_EMP_NO, HeaderNameConst.DEFAULT_X_EMP_NO);
        headerParamsMap.putIfAbsent(HeaderNameConst.X_AUTH_VALUE, HeaderNameConst.DEFAULT_X_AUTH_VALUE);
        headerParamsMap.putIfAbsent(HeaderNameConst.X_LANG_ID, HeaderNameConst.DEFAULT_X_LANG_ID);
        headerParamsMap.putIfAbsent(HeaderNameConst.X_TENANT_ID, HeaderNameConst.DEFAULT_X_TENANT_ID);
        headerParamsMap.putIfAbsent(HeaderNameConst.X_ORG_ID, HeaderNameConst.DEFAULT_X_ORG_ID);
        headerParamsMap.putIfAbsent(HeaderNameConst.X_ORIGIN_SERVICENAME, HeaderNameConst.DEFAULT_X_ORIGIN_SERVICENAME);
        headerParamsMap.putIfAbsent(HeaderNameConst.X_TARGET_SERVICENAME, HeaderNameConst.DEFAULT_X_TARGET_SERVICENAME);

        String uuid = getUuid();
        long timestamp = getTimeStamp();
        String signature = getSignature(uuid, timestamp, accessKey, secretKey);
        headerParamsMap.put(HeaderNameConst.X_AUTH_NONCE, uuid);
        headerParamsMap.put(HeaderNameConst.X_AUTH_TIMESTAMP, String.valueOf(timestamp));
        headerParamsMap.put(HeaderNameConst.X_AUTH_ACCESSKEY, accessKey);
        headerParamsMap.put(HeaderNameConst.X_AUTH_SIGNATURE, signature);

        if (!(headerParamsMap.containsKey(HeaderNameConst.X_ITP_VALUE)
                && StringUtils.isNotBlank(headerParamsMap.get(HeaderNameConst.X_ITP_VALUE)))) {
            headerParamsMap.put(HeaderNameConst.X_ITP_VALUE, urlEncode(HeaderNameConst.DEFAULT_X_ITP_VALUE));
        }

        return headerParamsMap;
    }

    /**
     * URL加密
     * @param content 原始内容
     * @return URL加密后内容
     */
    public static String urlEncode(String content) {
        String result = null;
        try {
            result = URLEncoder.encode(content, StandardCharsets.UTF_8.toString());
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        return result;
    }

    /**
     * URL解码
     * @param content 已URL加密串
     * @return 解码后内容
     */
    public static String urlDecode(String content) {
        String result = null;
        try {
            result = URLDecoder.decode(content, StandardCharsets.UTF_8.toString());
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 获取唯一标识ID
     * @return 唯一标识ID
     */
    public static String getUuid() {
        return UUID.randomUUID().toString();
    }

    /**
     * 获取当前时间戳，毫秒单位
     * @return 时间戳
     */
    public static long getTimeStamp() {
        return System.currentTimeMillis();
    }

    /**
     * 生成签名内容
     * @param uuid 唯一标识ID
     * @param timestamp 时间戳
     * @param accessKey 审批中心分配置的接入key，即appId
     * @param secretKey 审批中心分配的密钥，线下提供
     * @return 签名内容
     */
    public static String getSignature(String uuid, long timestamp, String accessKey, String secretKey) {
        String content = "uuid=" + uuid
                + "&timestamp=" + timestamp
                + "&accessKey=" + accessKey
                + "&secretKey=" + secretKey;
        return Sha256EncryptUtil.sha256Hex(content);
    }



    /**
     * 生成密钥
     * @param appId 应用ID
     * @return 密钥
     */
    public static String getSecretKey(String appId) {
        return getSignature(getUuid(), getTimeStamp(), appId, getUuid());
    }


}

