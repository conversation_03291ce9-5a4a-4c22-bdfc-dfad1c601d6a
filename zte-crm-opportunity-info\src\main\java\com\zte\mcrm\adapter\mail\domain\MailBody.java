package com.zte.mcrm.adapter.mail.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import static com.zte.mcrm.adapter.util.PatternUtils.encapsulationTextByCDATA;
import static com.zte.mcrm.common.consts.CommonConstant.MAIL_BODY_PATTERN;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class MailBody {

    /** 语言 **/
    private String id;
    /** 邮件中显示的温馨提醒 **/
    private String warmCall;
    /** 邮件中显示的系统名称 **/
    private String mailSysName;
    /** 点击查看 **/
    private String clickLook;
    /** 点击查看的链接 **/
    private String linkAdd;
    /** 邮件内容 **/
    private String mainText;

    /**
     * 构建邮件内容的xml
     * @return
     */
    public String buildMailBodyInXml() {
        return String.format(MAIL_BODY_PATTERN, id,
                encapsulationTextByCDATA(warmCall),
                encapsulationTextByCDATA(mailSysName),
                encapsulationTextByCDATA(clickLook),
                encapsulationTextByCDATA(linkAdd),
                encapsulationTextByCDATA(mainText));
    }

    @Override
    public String toString() {
        return "MailBody{" +
                "id='" + id + '\'' +
                ", warmCall='" + warmCall + '\'' +
                ", mailSysName='" + mailSysName + '\'' +
                ", clickLook='" + clickLook + '\'' +
                ", linkAdd='" + linkAdd + '\'' +
                ", mainText='" + mainText + '\'' +
                '}';
    }

}
