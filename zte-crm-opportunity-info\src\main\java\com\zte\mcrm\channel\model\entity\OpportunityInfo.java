package com.zte.mcrm.channel.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.mcrm.adapter.projectauthorization.dto.OppAuthInfo;
import com.zte.mcrm.channel.model.vo.OpportunityPdmProductVO;
import com.zte.mcrm.common.annotation.EmpName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
@Data
public class OpportunityInfo {
    @ApiModelProperty(value = "商机状态")
    private String statusCd;
    @ApiModelProperty(value = "商机状态名")
    private String statusCdName;
    @ApiModelProperty(value = "中兴业务经理id")
    private String businessManagerId;
    @ApiModelProperty(value = "中兴业务经理名")
    private String businessManagerName;
    @ApiModelProperty(value = "审批人")
    @EmpName
    private String approver;
    @ApiModelProperty(value = "审批时间")
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approvalDate;
    @ApiModelProperty(value = "审批意见")
    private String opinion;

    @ApiModelProperty(value = "本月进展")
    private String reportInfo;
    @ApiModelProperty(value = "商机当前状态编码(进行中/中标/丢标/取消)")
    private String currentStatus;

    @ApiModelProperty(value = "基本信息")
    private OpptyBaseInfo opptyBaseInfo;
    @ApiModelProperty(value = "产品信息")
    private List<ProductInfo> productInfo;
    @ApiModelProperty(value = "最终用户信息")
    private ConsumerInfo consumerInfo;
    @ApiModelProperty(value = "渠道商信息")
    private ChannelConsumerInfo channelConsumerInfo;
    @ApiModelProperty(value = "转立项信息")
    private TransferProjectInfo transferProjectInfo;



@Data
public  static class OpptyBaseInfo{
    @ApiModelProperty(value = "商机编码")
    private String optyCd;
    @ApiModelProperty(value = "渠道商客户编码")
    private String crmCustomerCode;

    @ApiModelProperty(value = "项目当前阶段编码")
    private String projectPhasesCode;

    @ApiModelProperty(value = "项目当前阶段名称")
    private String projectPhasesName;

    @ApiModelProperty(value = "商机名称")
    private String attrib46;

    @ApiModelProperty(value = "商机所属部门编号")
    private String deptNo;
    @ApiModelProperty(value = "商机所属部门名")
    private String deptName;

    @ApiModelProperty(value = "赢率")
    private String winRate;
    @ApiModelProperty(value = "赢率名")
    private String winRateName;

    @ApiModelProperty(value = "招标类型编码")
    private String tenderTypeCode;
    @ApiModelProperty(value = "招标类型名")
    private String tenderTypeName;
    @ApiModelProperty(value = "招标方全称")
    private String bidProviderName;
    @JsonFormat(pattern ="yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "预计发标/议标时间")
    private Date date1;
    @ApiModelProperty(value = "报备人姓名")
    private String agencyName;
    @JsonFormat(pattern ="yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "竞标截止日期")
    private Date biddingDeadline;
    @ApiModelProperty(value = "报备人邮箱")
    private String agencyEmail;
    @ApiModelProperty(value = "报备人电话")
    private String agencyPhone;
    @ApiModelProperty(value = "中兴业务经理id")
    private String businessManagerId;
    @ApiModelProperty(value = "中兴业务经理名字")
    private String businessManagerName;
    @ApiModelProperty(value = "业务经理邮箱")
    private  String businessManagerEmail;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "报备时间")
    private Date created;
    @ApiModelProperty(value = "附件")
    private List<String> fileIds;
    @ApiModelProperty(value = "商机概况")
    private String projectDesc;
    @ApiModelProperty(value = "商机属性")
    private String optyAttribute;
    @ApiModelProperty(value = "是否新业务(Y/N)")
    private String isNewBusiness;
    @ApiModelProperty(value = "项目编号")
    private String projectCode;
    @ApiModelProperty(value = "项目Id")
    private String projectId;
    @ApiModelProperty(value = "授权信息")
    private List<OppAuthInfo> oppAuthInfo;
    @ApiModelProperty(value = "ts审批单号")
    private String tsApprovalNumber ;
    @ApiModelProperty(value = "原因代码")
    private List<MonthReport> monthReports;
    @ApiModelProperty(value = "是否属于激活报备(Y/N)")
    private String fromActiveFlag;
    @ApiModelProperty(value = "商机来源")
    private String dataSource;
    @ApiModelProperty(value = "商机来源名称")
    private String dataSourceName;
    @JsonFormat(pattern ="yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "预计签单时间")
    private Date date2;
}

@Data
public static class  MonthReport{

    @ApiModelProperty(value = "主键id")
    private String id;
    @ApiModelProperty(value = "商机id")
    private String optyId;
    @ApiModelProperty(value = "当前月报归属期")
    private String reportMonth;
    @ApiModelProperty(value = "原因代码")
    private String reasonCode;
    @ApiModelProperty(value = "原因")
    private String reason;
    @ApiModelProperty(value = "说明")
    private String directions;
    @ApiModelProperty(value = "备注")
    private String memo;
}

@Data
public static class ProductInfo{
    @ApiModelProperty(value = "产品主键id")
    private String rowId;
    @ApiModelProperty(value = "产品线Id")
    private String prodLv2Id;
    @ApiModelProperty(value = "")
    private String prodLv2Name;
    @ApiModelProperty(value = "产品大类Id")
    private String prodLv3Id;
    @ApiModelProperty(value = "产品预计签单金额")
    private BigDecimal productAmount;
    @ApiModelProperty(value = "公司主产品")
    private OpportunityPdmProductVO pdmProd;
    @ApiModelProperty(value = "是否主产品")
    private String zteMainProduct;
    @ApiModelProperty(value = "预计签单时间")
    private Date forSignDate;
    @ApiModelProperty(value = "成功率")
    private String successRateShow;
    @ApiModelProperty(value = " 是否可用(Y,N)")
    private String enableFlagShow;
}

@Data
public static class ConsumerInfo{

    @ApiModelProperty(value = "最终用户id")
    private String lastAccId;
    @ApiModelProperty(value = "最终用户编码")
    private String lastAccCode;
    @ApiModelProperty(value = "最终用户名称")
    private String lastAccName;
    @ApiModelProperty(value = "最终用户受限制主体")
    private String restrictedParty;
    @ApiModelProperty(value = "最终用户地址")
    private String finalCustomerAddress;
    @ApiModelProperty(value = "最终用户行业编码")
    private String finalCustomerParentTrade;
    @ApiModelProperty(value = "最终用户行名")
    private String finalCustomerParentTradeName;
    @ApiModelProperty(value = "最终用户子行业编码")
    private String finalCustomerChildTrade;
    @ApiModelProperty(value = "最终用户子行业编码名")
    private String finalCustomerChildTradeName;
    @ApiModelProperty(value = "最终用户联系人姓名")
    private String finalCustomerContactName;
    @ApiModelProperty(value = "最终用户联系人电话")
    private String finalCustomerContactPhone;
    @ApiModelProperty(value = "最终用户联系人邮箱")
    private String finalCustomerContactEmail;

}

@Data
public static class ChannelConsumerInfo{

    @ApiModelProperty(value = "渠道商名称")
    private String customerName;
    @ApiModelProperty(value = "报备代理商级别")
    private String agencyLevelName;
    @ApiModelProperty(value = "代理商级别编码")
    private String agencyLevelCode;
    @ApiModelProperty(value = "渠道商受限制主体")
    private String restrictedParty;
    @ApiModelProperty(value = "渠道商客户编码")
    private String crmCustomerCode;
    @ApiModelProperty(value = "原渠道商名称")
    private String sourceCustomerName;
    @ApiModelProperty(value = "原渠道商客户编码")
    private String sourceCrmCustomerCode;

}

@Data
public static class TransferProjectInfo{
    @ApiModelProperty(value = "业务范围名称")
    private String businessTypeName;
    @ApiModelProperty(value = "业务范围")
    private String businessTypeCd;
    @ApiModelProperty(value = "客户名称编码")
    private String signCustomerCode;
    @ApiModelProperty(value = "客户名称")
    private String signCustomerName;
    @ApiModelProperty(value = "项目指委会主任")
    @EmpName
    private String directorOfPsc;
    @ApiModelProperty(value = "最终用途")
    private String finalUsage;
    @ApiModelProperty(value = "最终用途")
    private String finalUsageName;
    @ApiModelProperty(value = "销售模式")
    private String salesType;
    @ApiModelProperty(value = "销售模式")
    private String salesTypeName;
    @ApiModelProperty(value = "商机执行地")
    private String nationalAreaId;
    @ApiModelProperty(value = "商机执行地名称")
    private String nationalAreaName;
    @ApiModelProperty("最终用户类型")
    private String endUserType;
    @ApiModelProperty("最终用户类型名称")
    private String endUserTypeName;
    @ApiModelProperty("最终用户的最终用途")
    private String endUseOfEndUser;
    @ApiModelProperty("最终用户的最终用途名称")
    private String endUseOfEndUserName;
    @ApiModelProperty("具体客户描述 ")
    private String specificCustomerDesc;
    @ApiModelProperty("具体用途描述 ")
    private String specificUsageDesc;
    private List<OpptyProductInfo> opptyProductInfos;

}

@Data
public static class OpptyProductInfo{

    @ApiModelProperty(value = "主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private String rowId;
    @ApiModelProperty(value = "体系内部分类Id")
    private String prodLv1Id;
    @ApiModelProperty(value = "")
    private String prodLv1Name;
    @ApiModelProperty(value = "大产品线Id")
    private String prodLv2Id;
    @ApiModelProperty(value = "大产品线")
    private String prodLv2Name;
    @ApiModelProperty(value = "产品线Id")
    private String prodLv21Id;
    @ApiModelProperty(value = "产品线")
    private String prodLv21Name;
    @ApiModelProperty(value = "产品大类Id")
    private String prodLv3Id;
    @ApiModelProperty(value = "产品大类")
    private String prodLv3Name;
    @ApiModelProperty(value = "产品小类Id")
    private String prodLv4Id;
    @ApiModelProperty(value = "产品小类")
    private String prodLv4Name;
    @ApiModelProperty(value = "")
    private String opptyId;
    @ApiModelProperty(value = "是否主产品")
    private String zteMainProduct;
    @ApiModelProperty(value = "")
    private String activeFlg;
    @ApiModelProperty(value = "产品预计签单金额")
    private BigDecimal productAmount;
    @JsonFormat(pattern ="yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "预计签单时间")
    private Date forSignDate;
    @ApiModelProperty(value = "")
    private String dataSource;
    @ApiModelProperty(value = "是否新业务(Y/N)")
    private String isNewBusiness;
}


}
