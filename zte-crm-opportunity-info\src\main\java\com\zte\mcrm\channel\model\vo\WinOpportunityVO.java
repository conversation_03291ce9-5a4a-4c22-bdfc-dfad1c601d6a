package com.zte.mcrm.channel.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 中标商机查询结果VO
 * <AUTHOR>
 * @date 2024/01/01
 */
@Setter
@Getter
@ToString
public class WinOpportunityVO {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "商机编码")
    private String optyCd;

    @ApiModelProperty(value = "商机状态")
    private String statusCd;

    @ApiModelProperty(value = "商机来源")
    private String dataSource;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date created;

    @ApiModelProperty(value = "签单总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "渠道商客户编码")
    private String crmCustomerCode;

    @ApiModelProperty(value = "渠道商名称")
    private String crmCustomer;
} 