package com.zte.mcrm.common.business.service;

import com.google.common.collect.Lists;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.common.access.dao.IComDictionaryMaintainDao;
import com.zte.mcrm.common.access.vo.ComDictionaryMaintainVO;
import com.zte.mcrm.common.util.DictUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: 10245190 DengZiqiao
 * @Date: 2021/9/9 11:37
 * @Version: V1.0
 */
@Service
public class ComDictionaryMaintainServiceImpl implements IComDictionaryMaintainService {

    @Autowired
    private IComDictionaryMaintainDao comDictionaryMaintainDao;

    /**
     * 根据“类型集合”查询测算数据字典
     *
     * @param typeList 类型集合
     * @return key为入参中的type，value为该type从数据字典表查询的List结果
     */
    @Override
    public Map<String, List<ComDictionaryMaintainVO>> queryByTypeList(@NotNull List<String> typeList) {
        List<ComDictionaryMaintainVO> resultList = comDictionaryMaintainDao.queryByTypeList(typeList);
        return resultList.stream().collect(Collectors.groupingBy(ComDictionaryMaintainVO::getType, HashMap::new, Collectors.toList()));
    }

    /**
     * 插入字典表数据
     * @param comDictionaryMaintainVO
     * @return
     */
    @Override
    public int insert(ComDictionaryMaintainVO comDictionaryMaintainVO){
        return comDictionaryMaintainDao.insert(comDictionaryMaintainVO);
    }

    /**
     * 更新字典表数据
     * @param comDictionaryMaintainVO
     * @return
     */
    @Override
    public int update(ComDictionaryMaintainVO comDictionaryMaintainVO){
        return comDictionaryMaintainDao.updateDictById(comDictionaryMaintainVO);
    }


    @Override
    public  List<ComDictionaryMaintainVO> queryByType(@NotNull String type) {
        return comDictionaryMaintainDao.queryByTypeList(Lists.newArrayList(type));
    }

    @Override
    public String getDataSourceName(String code){
        List<ComDictionaryMaintainVO> dataSourceDictionaryList = queryByType(OpportunityConstant.SOURCE_OF_OPPORTUNITY);
        return DictUtils.getName(code, dataSourceDictionaryList);
    }

    @Override
    public String getStatusCdName(String code){
        List<ComDictionaryMaintainVO> statusDictionaryList = queryByType(OpportunityConstant.OPPORTUNITY_STATUS);
        return DictUtils.getName(code, statusDictionaryList);
    }

    @Override
    public String getCurrentPhaseTypeName(String code){
        List<ComDictionaryMaintainVO> currentPhaseDictionaryList = queryByType(OpportunityConstant.CURRENT_PHASES_TYPE);
        return DictUtils.getName(code, currentPhaseDictionaryList);
    }

    @Override
    public String getTenderTypeName(String code){
        List<ComDictionaryMaintainVO> tenderTypeDictionaryList = queryByType(OpportunityConstant.TYPE_FOR_TENDER_TYPE);
        return DictUtils.getName(code, tenderTypeDictionaryList);
    }

    @Override
    public String getStatusAuthName(String code){
        List<ComDictionaryMaintainVO> authStatusDictionaryList = queryByType(OpportunityConstant.AUTHORIZATION_STATUS);
        return DictUtils.getName(code, authStatusDictionaryList);
    }

    @Override
    public String getWinRateTypeName(String code){
        List<ComDictionaryMaintainVO> winRateDictionaryList = queryByType(OpportunityConstant.WIN_RATE_TYPE);
        return DictUtils.getName(code, winRateDictionaryList);
    }

    @Override
    public String getMonthlyReportStatusName(String code){
        List<ComDictionaryMaintainVO> monthlyReportStatusDictionaryList = queryByType(OpportunityConstant.MONTHLY_REPORT_STATUS);
        return DictUtils.getName(code, monthlyReportStatusDictionaryList);
    }

}
