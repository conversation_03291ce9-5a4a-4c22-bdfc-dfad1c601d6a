<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zte.mcrm.channel.dao.OpportunityQueryDao" >
    <!-- 屏蔽mybatis的2级缓存  <cache />  -->

	<resultMap id="ExternalOpportunityDetailMap" type="com.zte.mcrm.channel.model.entity.ExternalOpportunityDetail">
		<result column="id" property="rowId" jdbcType="VARCHAR"/>
		<result column="opty_cd" property="optyCd" jdbcType="VARCHAR" />
		<result column="ATTRIB_46" property="optyName" jdbcType="VARCHAR" />
		<result column="customer_name" property="channelName" jdbcType="VARCHAR" />
		<result column="crm_customer_code" property="crmCustomerCode" jdbcType="VARCHAR" />
		<result column="SECOND_DEALER_ID" property="oldOppCustomerId" jdbcType="VARCHAR"/>
		<result column="status_cd" property="statusCd" jdbcType="VARCHAR" />
		<result column="dept_no" property="deptNo" jdbcType="VARCHAR" />
		<result column="project_desc" property="projectDesc" jdbcType="VARCHAR" />
		<result column="X_TOTAL_AMOUNT" property="expectSignMoney" jdbcType="DECIMAL" />
		<result column="X_LAST_ACC_ID" property="finalCustomerId" jdbcType="VARCHAR" />
		<result column="x_last_acc_name" property="finalCustomerName" jdbcType="VARCHAR" />
		<result column="ts_approval_number" property="tsApprovalNo" jdbcType="VARCHAR"/>
		<result column="final_customer_parent_trade" property="finalCustomerParentTrade" jdbcType="VARCHAR" />
		<result column="PROD_LV2_NAME" property="prodName" jdbcType="VARCHAR" />
		<result column="PROD_LV2_ID" property="prodId" jdbcType="VARCHAR"/>
		<result column="created" property="createdTime" jdbcType="TIMESTAMP" />
		<result column="project_phases_code" property="projectPhases" jdbcType="VARCHAR" />
		<result column="win_rate" property="winRate" jdbcType="VARCHAR" />
		<result column="tender_type_code" property="tendType" jdbcType="VARCHAR" />
		<result column="bid_provider_name" property="bidProviderName" jdbcType="VARCHAR" />
		<result column="DATE_1" property="estimatedBiddingTime" jdbcType="TIMESTAMP" />
		<result column="bidding_deadline" property="biddingDeadline" jdbcType="TIMESTAMP" />
		<result column="agency_name" property="agencyName" jdbcType="VARCHAR" />
		<result column="agency_phone" property="agencyPhone" jdbcType="VARCHAR" />
		<result column="agency_email" property="agencyEmail" jdbcType="VARCHAR" />
		<result column="final_customer_contact_name" property="finalCustomerContactName" jdbcType="VARCHAR" />
		<result column="final_customer_contact_phone" property="finalCustomerContactPhone" jdbcType="VARCHAR" />
		<result column="final_customer_contact_email" property="finalCustomerContactEmail" jdbcType="VARCHAR" />
		<result column="final_customer_child_trade" property="finalCustomerChildTrade" jdbcType="VARCHAR" />
		<result column="data_source_name" property="dataSourceName" jdbcType="VARCHAR"/>
		<result column="business_manager_id" property="businessManager" jdbcType="VARCHAR"/>
		<result column="business_manager_name" property="businessManagerName" jdbcType="VARCHAR"/>
	</resultMap>

    <select id="queryOpportunityList" parameterType="com.zte.mcrm.channel.model.vo.OpportunityQueryParamVO" resultType="com.zte.mcrm.channel.model.vo.OpportunityDataVO">
       select
		o.opty_code as optyCd,
		o.opty_name optyName,
		x.crm_customer_code crmCustomerCode,
		x.customer_name channelName,
		x.SECOND_DEALER_ID oldOppCustomerId,
		o.opty_status statusCd,
		d.chinese_name statusCdName,
		o.org_tree deptNo,
		x.project_desc projectDesc,
		x.X_TOTAL_AMOUNT expectSignMoney,
		x.customer finalCustomerId,
		x.x_last_acc_name finalCustomerName,
		x.final_customer_parent_trade finalCustomerParentTrade,
		(select GROUP_CONCAT(p.PROD_LV2_NAME) from s_opty_product p
		where p.p_id = o.id
		and p.business_type = 'newOpportunity'
		and p.is_deleted = 0
		GROUP BY p.p_id) prodName,
		o.create_time createdTime,
		(select cdm.chinese_name from com_dictionary_maintain cdm
		where cdm.code = o.data_source
		and cdm.type = 'sourceOfOpportunity' )dataSourceName,
		o.id rowId,
		x.final_customer_child_trade finalCustomerChildTrade
		from s_opty o
		left join s_opty_x x on o.id = x.id
		left join s_opty_team t on o.id = t.p_id
		and t.employee_type = 1
		and JSON_VALUE(t.core_info_flag, '$[0].value') = 'Y'
		left join com_dictionary_maintain d on d.code = o.opty_status and d.type = 'opportunityStatus'
		where o.is_deleted = 0
		<if test="shortNo != null and shortNo != ''">
			and json_value(substring(to_char(t.employee),2,char_length(t.employee)-2),'$.id') = #{shortNo}
		</if>
		<if test="crmCustomerCode != null and crmCustomerCode != ''">
			and x.crm_customer_code = #{crmCustomerCode}
		</if>
		<if test="channelName != null and channelName != ''">
			and x.customer_name like CONCAT(CONCAT('%',#{channelName}),'%')
		</if>
		<if test="optyCd != null and optyCd != ''">
			and o.opty_code like CONCAT(CONCAT('%',#{optyCd}),'%')
		</if>
		<if test="optyName != null and optyName != ''">
			and o.opty_name like CONCAT(CONCAT('%',#{optyName}),'%')
		</if>
		<if test="statusCd != null and statusCd.size > 0">
			and o.opty_status in
			<foreach item="item" index="index" collection="statusCd" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="deptNo != null and deptNo != ''">
			and (o.org_tree = #{deptNo})
		</if>
		<if test="finalCustomerChildTrade != null and finalCustomerChildTrade != ''">
			and x.final_customer_child_trade = #{finalCustomerChildTrade}
		</if>
		<if test="createdTimeStart != null and createdTimeStart != ''">
			<![CDATA[ and  o.create_time >= #{createdTimeStart,jdbcType=TIMESTAMP}]]>
		</if>
		<if test="createdTimeEnd != null and createdTimeEnd != ''">
			<![CDATA[ and  o.create_time <= #{createdTimeEnd,jdbcType=TIMESTAMP}]]>
		</if>
		<if test="optyCdList != null and optyCdList.size > 0">
			and o.opty_code in
			<foreach item="item" index="index" collection="optyCdList" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		and (
		<choose>
			<when test='dataSource !=null and dataSource == "0"'>
				o.data_source = 'iChannel'
			</when>
			<when test='dataSource !=null and dataSource == "1"'>
				o.data_source = 'PRM'
			</when>
			<otherwise>
				o.data_source in ('iChannel','PRM')
			</otherwise>
		</choose>

		)
		order by o.create_time desc
		limit #{startIndex},#{pageSize}
    </select>


    <!-- 翻页函数:获取符合条件的记录数 -->
    <select id="queryOpportunityListCount" parameterType="com.zte.mcrm.channel.model.vo.OpportunityQueryParamVO" resultType="int">
		select
		count(o.id)
		from
		s_opty o
		left join s_opty_x x on o.id = x.id
		left join s_opty_team t on o.id = t.p_id
		and t.employee_type = 1
		and JSON_VALUE(t.core_info_flag, '$[0].value') = 'Y'
		where o.is_deleted = 0
		<if test="shortNo != null and shortNo != ''">
			and json_value(substring(to_char(t.employee),2,char_length(t.employee)-2),'$.id') = #{shortNo}
		</if>
		<if test="crmCustomerCode != null and crmCustomerCode != ''">
			and x.crm_customer_code = #{crmCustomerCode}
		</if>
		<if test="channelName != null and channelName != ''">
			and x.customer_name like CONCAT(CONCAT('%',#{channelName}),'%')
		</if>
		<if test="optyCd != null and optyCd != ''">
			and o.opty_code like CONCAT(CONCAT('%',#{optyCd}),'%')
		</if>
		<if test="optyName != null and optyName != ''">
			and o.opty_name like CONCAT(CONCAT('%',#{optyName}),'%')
		</if>
		<if test="statusCd != null and statusCd.size > 0">
			and o.opty_status in
			<foreach item="item" index="index" collection="statusCd" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="deptNo != null and deptNo != ''">
			and o.org_tree = #{deptNo}
		</if>
		<if test="finalCustomerChildTrade != null and finalCustomerChildTrade != ''">
			and x.final_customer_child_trade = #{finalCustomerChildTrade}
		</if>
		<if test="createdTimeStart != null and createdTimeStart != ''">
			<![CDATA[ and  o.create_time >= #{createdTimeStart,jdbcType=TIMESTAMP}]]>
		</if>
		<if test="createdTimeEnd != null and createdTimeEnd != ''">
			<![CDATA[ and  o.create_time <= #{createdTimeEnd,jdbcType=TIMESTAMP}]]>
		</if>
		<if test="optyCdList != null and optyCdList.size > 0">
			and o.opty_code in
			<foreach item="item" index="index" collection="optyCdList" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		and (
		<choose>
			<when test='dataSource !=null and dataSource == "0"'>
				o.data_source = 'iChannel'
			</when>
			<when test='dataSource !=null and dataSource == "1"'>
				o.data_source = 'PRM'
			</when>
			<otherwise>
				o.data_source in ('iChannel','PRM')
			</otherwise>
		</choose>

		)
    </select>

	<select id="determineIntransitsAndValidOptys" resultType="com.zte.mcrm.channel.model.vo.CrmCustomerDetermineResultVO">
		select case when count(*) > 0 then 1 end as existedDocument,
		x.crm_customer_code crmCustomerCode
		from s_opty s
		left join s_opty_x x on s.id = x.id
		where s.is_deleted = 0 and s.data_source in ('iChannel','PRM')
		<if test="crmCustomerCodes != null and crmCustomerCodes.size > 0">
			and x.crm_customer_code in
				<foreach item="item" index="index" collection="crmCustomerCodes" open="(" separator="," close=")">
					#{item}
				</foreach>
		</if>
		and s.create_time >= date_sub(curdate(), interval #{years} year)
		group by x.crm_customer_code
	</select>


	<select id="getExternalOpportunityDetail" parameterType="java.lang.String" resultMap="ExternalOpportunityDetailMap">
		SELECT
		o.id,
		o.opty_code as opty_cd,
		o.opty_name as ATTRIB_46,
		x.crm_customer_code,
		x.customer_name,
		x.SECOND_DEALER_ID,
		JSON_VALUE(t.employee, '$[0].empUIID') as business_manager_id,
		JSON_UNQUOTE(JSON_VALUE(t.employee, '$[0].empName')) as business_manager_name,
		o.opty_status as status_cd,
		o.org_tree as dept_no,
		x.project_desc,
		x.X_TOTAL_AMOUNT,
		x.customer as X_LAST_ACC_ID,
		x.x_last_acc_name,
		x.ts_approval_number,
		x.final_customer_parent_trade,
		(select group_concat(product_operation_team) from s_opty_product p
		where p_id = o.id and p.is_deleted = 0
		and business_type = 'newOpportunity' group by p_id)  as PROD_LV2_ID,
		(select group_concat(PROD_LV2_NAME)
		from s_opty_product p where p.p_id = o.id
		and p.is_deleted = 0
		and business_type = 'newOpportunity' group by p_id)  as PROD_LV2_NAME,
		o.create_time as created,
		x.project_phases_code,
		JSON_VALUE((select success_rate from s_opty_product where p_id = o.id and is_deleted = 0 limit 1), '$[0].value') as win_rate,
		JSON_VALUE(o.bidding_type, '$[0].value') as tender_type_code,
		x.bid_provider_name,
		o.for_bid_time as DATE_1,
		x.bidding_deadline,
		x.agency_name,
		x.agency_phone,
		x.agency_email,
		x.final_customer_contact_name,
		x.final_customer_contact_phone,
		x.final_customer_contact_email,
		x.final_customer_child_trade,
		(select cdm.chinese_name from com_dictionary_maintain cdm
		where cdm.code = o.data_source
		and cdm.type = 'sourceOfOpportunity' ) data_source_name
		FROM s_opty o
		LEFT JOIN s_opty_x x ON o.id = x.id
		left join s_opty_team t on o.id = t.p_id
		and t.employee_type = 1
		and JSON_VALUE(t.core_info_flag, '$[0].value') = 'Y'
		where
		o.data_source in ('iChannel','PRM')  and
		o.opty_code=#{optyCd, jdbcType=VARCHAR}
	</select>


	<select id="querySimilarOpportunityByRowId"
			resultType="com.zte.mcrm.channel.model.entity.SimilarOpportunity">
		SELECT
			t.id opportunityRowId,
			so.opty_name opportunityName,
			t.customer_name channelVendor,
			t.x_last_acc_name endUserName,
			t.crm_customer_code crmCustomerCode,
			so.org_tree opportunityDepartment,
			JSON_VALUE(so.bidding_type, '$[0].value') tenderTypeCode,
			t.final_customer_parent_trade finalCustomerTradeCode,
			t.final_customer_child_trade finalCustomerTradeChildCode,
			so.for_bid_time date1,
			t.DATE_2 date2,
			t.bidding_deadline biddingDeadline,
			t.agency_level_name channelVendorLevel,
			so.opty_code opportunityNo,
			so.data_source dataSource,
			d.chinese_name opportunityStatus,
			(select group_concat(PROD_LV2_NAME)
		from s_opty_product p where p.p_id = so.id
		and p.is_deleted = 0
		and business_type = 'newOpportunity' group by p_id) opportunityProduct
		FROM
			(
				SELECT  t3.*
				FROM    (
							SELECT  distinct t.*
							FROM    (
										SELECT   t1.*
										FROM     s_opty_x  AS t1
										WHERE    t1.x_last_acc_name = #{currentOptyLastName, jdbcType=VARCHAR}
								        <if test="rowId != null and rowId != ''">
										AND      t1.id != #{rowId, jdbcType=VARCHAR}
										</if>
									)
									AS t,
							s_opty_product AS cop
							WHERE   cop.p_id = t.id AND cop.is_deleted = 0
									AND cop.prod_lv2_name IN (
									<choose>
										<when test="rowId != null and rowId != ''">
											SELECT  cop2.PROD_LV2_NAME
											FROM    s_opty_product cop2
											WHERE   cop.is_deleted = 0 and cop2.p_id = #{rowId, jdbcType=VARCHAR}
											AND     cop2.PROD_LV2_NAME  IS NOT NULL
										</when>
										<otherwise>
											<!--													<foreach item="item" index="index" collection="currentProductList" open="(" separator="," close=")">-->
											<foreach item="item" index="index" collection="currentProductList"  separator="," >
												#{item}
											</foreach>

										</otherwise>
									</choose>

									)
						)
						AS t3
			)
			as t

		LEFT JOIN s_opty so                  ON so.id = t.id   AND  so.is_deleted = 0
		LEFT JOIN com_dictionary_maintain d  ON d.code = so.opty_status  AND  d.type = 'opportunityStatus'

		WHERE  so.opty_status IN ("Renewing", "reportedApprovaling", "arbitration", "ticketWin")
		AND so.data_source in ('iChannel','PRM')

		<choose>
			<when test='currentOptyTenderType !=null and currentOptyTenderType == "tenderProject"'>
				AND  IF(	JSON_VALUE(so.bidding_type, '$[0].value')  = 10 ,
							#{currentOptyTime, jdbcType=VARCHAR},
							date_add(#{currentDeadline, jdbcType=VARCHAR}, interval 1 month)
						)
						BETWEEN  date_sub(so.for_bid_time, interval 6 month)
						AND      date_add(so.for_bid_time, interval 3 month)
			</when>
			<otherwise>
						AND  IF(JSON_VALUE(so.bidding_type, '$[0].value')  = 10 ,
							date_add(t.bidding_deadline, interval 1 month),
							so.for_bid_time
						)
						BETWEEN  date_sub(#{currentOptyTime, jdbcType=VARCHAR}, interval 3 month)
						AND      date_add(#{currentOptyTime, jdbcType=VARCHAR}, interval 6 month)
			</otherwise>
		</choose>

		ORDER BY t.crm_customer_code != #{currentCrmCode, jdbcType=VARCHAR} and so.for_bid_time desc
		<if test="startRow != null and rowSize != null"> limit #{startRow}, #{rowSize} </if>

	</select>


	<select id="querySimilarOpportunityByName"
			resultType="com.zte.mcrm.channel.model.entity.SimilarOpportunity">
		SELECT
			t.id opportunityRowId,
			so.opty_name opportunityName,
			t.customer_name channelVendor,
			t.x_last_acc_name endUserName,
			t.crm_customer_code crmCustomerCode,
			so.org_tree opportunityDepartment,
			t.final_customer_parent_trade finalCustomerTradeCode,
			t.final_customer_child_trade finalCustomerTradeChildCode,
			t.agency_level_name channelVendorLevel,
			so.opty_code opportunityNo,
			so.data_source dataSource,
			d.chinese_name opportunityStatus,
			(select GROUP_CONCAT(cop.PROD_LV2_NAME) from s_opty_product cop
			where cop.p_id = t.id and cop.is_deleted = 0
			GROUP BY cop.p_id) opportunityProduct
		FROM s_opty_x  t
		LEFT JOIN s_opty so ON so.id = t.id AND so.is_deleted = 0
		LEFT JOIN com_dictionary_maintain d ON d.code = so.opty_status AND d.type = 'opportunityStatus'
		<if test="opportunityName != null and opportunityName != ''">
			AND so.opty_name = #{opportunityName, jdbcType=VARCHAR}
		</if>
		<if test="channelVendor != null and channelVendor != ''">
			AND t.customer_name = #{channelVendor, jdbcType=VARCHAR}
		</if>
		AND so.opty_status in ("Renewing", "reportedApprovaling", "arbitration", "ticketWin")
		AND so.data_source in ('iChannel','PRM')
		ORDER BY t.crm_customer_code != #{currentCrmCode, jdbcType=VARCHAR} and so.for_bid_time desc
	</select>


	<select id="querySimilarOpportunityByLastAccName"
			resultType="com.zte.mcrm.channel.model.entity.SimilarOpportunity">
		SELECT
		t.id opportunityRowId,
		so.opty_name opportunityName,
		t.customer_name channelVendor,
		t.x_last_acc_name endUserName,
		so.org_tree as opportunityDepartment,
		JSON_VALUE(so.bidding_type, '$[0].value') tenderTypeCode,
		t.final_customer_parent_trade finalCustomerTradeCode,
		t.final_customer_child_trade finalCustomerTradeChildCode,
		so.for_bid_time as date1,
		t.DATE_2 date2,
		t.bidding_deadline biddingDeadline,
		t.agency_level_name channelVendorLevel,
		so.opty_code opportunityNo,
		so.data_source dataSource,
		d.chinese_name opportunityStatus,
		(select GROUP_CONCAT(cop.PROD_LV2_NAME) from s_opty_product cop
		where cop.p_id = t.id and cop.is_deleted = 0
		GROUP BY cop.p_id) opportunityProduct
		FROM
		(select t1.*
		from
		s_opty_x as t1
		where t1.x_last_acc_name = #{lastAccName, jdbcType=VARCHAR} ) as t
		LEFT JOIN s_opty so ON so.id = t.id AND so.is_deleted = 0
		LEFT JOIN com_dictionary_maintain d ON d.code = so.opty_status AND d.type = 'opportunityStatus'
		WHERE so.opty_status in ("Renewing", "reportedApprovaling", "arbitration", "ticketWin")
		AND so.data_source in ('iChannel','PRM')
		ORDER BY t.crm_customer_code != #{currentCrmCode, jdbcType=VARCHAR} and so.for_bid_time desc
	</select>


	<select id="querySimilarOpportunityByOptyCd"
			resultType="com.zte.mcrm.channel.model.entity.SimilarOpportunity">
		SELECT
			t.id opportunityRowId,
			so.opty_name as  opportunityName,
			t.customer_name channelVendor,
			t.x_last_acc_name endUserName,
			t.crm_customer_code crmCustomerCode,
			so.org_tree as  opportunityDepartment,
			t.final_customer_parent_trade finalCustomerTradeCode,
			t.final_customer_child_trade finalCustomerTradeChildCode,
			t.agency_level_name channelVendorLevel,
			so.opty_code opportunityNo,
			so.data_source dataSource,
			d.chinese_name opportunityStatus
		FROM s_opty_x  t
		LEFT JOIN s_opty so ON so.id = t.id AND so.is_deleted = 0
		LEFT JOIN com_dictionary_maintain d ON d.code = so.opty_status AND d.type = 'opportunityStatus'
		WHERE so.opty_code = #{simiarOptyCd, jdbcType=VARCHAR}
		AND so.data_source in ('iChannel','PRM')
		AND so.opty_status in ("Renewing", "reportedApprovaling", "arbitration", "ticketWin")
	</select>

	<select id="queryCurrentOpportunityByRowId"
			resultType="com.zte.mcrm.channel.model.entity.CurrentOptyInfo">

		SELECT  JSON_VALUE(so.bidding_type, '$[0].value') currentOptyTenderType,
				x.x_last_acc_name currentOptyLastName,
				so.for_bid_time currentOptyTime,
				x.bidding_deadline currentDeadline,
				x.crm_customer_code currentCrmCode
		FROM    s_opty_x x
		left join s_opty so on so.id = x.id AND so.is_deleted = 0
		WHERE   x.id = #{rowId, jdbcType=VARCHAR}
		AND so.data_source in ('iChannel','PRM')
	</select>

	<select id="queryCurrentOpportunityByOptyCd"
			resultType="com.zte.mcrm.channel.model.dto.OpportunityStatusDTO">

		SELECT  t.opty_code optyCd,
		       	t.id rowId,
				t.opty_status AS  statusCd
		FROM    s_opty t
		WHERE   opty_code = #{optyCd, jdbcType=VARCHAR}
		  AND   is_deleted = 0
		  AND t.data_source in ('iChannel','PRM')
		  AND t.opty_status="Transferred"
	</select>

	<!-- 查询中标商机信息 -->
	<select id="queryWinOpportunities" resultType="com.zte.mcrm.channel.model.vo.WinOpportunityVO">
		<![CDATA[
		SELECT t1.id         		id,
		       t1.opty_code         optyCd,
		       t1.opty_status       statusCd,
		       t1.data_source       dataSource,
		       t1.create_time       created,
		       tm1.amount           totalAmount,
		       t2.crm_customer_code crmCustomerCode,
		       t2.crm_customer      crmCustomer
		FROM s_opty t1
		         JOIN s_opty_x t2 ON t1.id = t2.id
		         LEFT JOIN (SELECT m1.p_id, SUM(m1.for_sign_amount) AS amount
		                    FROM s_opty_product m1
		                    WHERE m1.is_deleted = 0
		                      AND m1.business_type = 'newOpportunity'
		                      AND COALESCE(JSON_VALUE(m1.enable_flag_ext, '$[0]'), '') = 'Y'
		                    GROUP BY m1.p_id) tm1 ON t1.id = tm1.p_id
		WHERE t1.opty_status = ('ticketWin')
		  AND t1.create_time >= '2023-07-01'
		  AND t1.create_time < '2025-08-01'
		  AND (t1.data_source = 'iChannel' OR t1.data_source = 'PRM')
		  AND t2.crm_customer_code = #{crmCustomerCode}
		ORDER BY t1.create_time DESC
		]]>
	</select>


</mapper>
