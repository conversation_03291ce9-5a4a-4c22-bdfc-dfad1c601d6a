package com.zte.mcrm.common.business;

import com.zte.mcrm.oftensearch.access.vo.OftensearchVO;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;

import java.util.List;

/****
 * 查看历史记录
 * @ClassName IOftenSearchService
 * <AUTHOR>
 * @Date 2021/3/12
 * @Version V1.0
 */
public interface IOftenSearchService {

    /****
     * 查看历史记录
     * @param empNo
     * @param bizType
     * @param pageNo
     * @param pageSize
     * @return
     * @throws BusiException
     * @throws RouteException
     */
    List<OftensearchVO> getOftenSearchList(String empNo, String bizType, Integer pageNo, Integer pageSize) throws BusiException, RouteException;

    /****
     * 失效最近查询记录
     * @param bizType
     * @return
     */
    int invalidOftenSearchByBizType(String bizType);
}
