package com.zte.mcrm.channel.constant;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

import static com.zte.mcrm.channel.constant.OpportunityNotifyMsgEnum.*;

/**
 * 客户状态枚举
 * <AUTHOR>
 * @date 2023-05-19
 */
public enum AccountStatusEnum {
    /**客户状态:待提交**/
    WAIT_FOR_SUBMIT("Account State 1", "draft", "待提交"){
        @Override
        public OpportunityNotifyMsgEnum getOpportunityNotifyMsgEnum() {
            return OPPORTUNITY_EMAIL_FOR_CUSTOMER_SUBMITTING_REMINDER;
        }

    },
    /**待审批**/
    APPROVING("Account State 2", "approving", "待审批"){
        @Override
        public OpportunityNotifyMsgEnum getOpportunityNotifyMsgEnum() {
            return null;
        }

    },
    /**客户状态：驳回**/
    REFUSE("Account State 4", "refuse", "驳回") {
        @Override
        public OpportunityNotifyMsgEnum getOpportunityNotifyMsgEnum() {
            return OPPORTUNITY_EMAIL_FOR_CUSTOMER_REJECT_REMINDER;
        }

    },
    /**客户状态：生效**/
    ACCT_ACTIVE_STATUS("Account State 3", "effect", "生效") {
        @Override
        public OpportunityNotifyMsgEnum getOpportunityNotifyMsgEnum() {
            return OPPORTUNITY_EMAIL_FOR_CUSTOMER_EFFECTED_REMINDER_EXTERNAL;
        }

    },
    /**客户状态：失效**/
    ACCT_INACTIVE_STATUS("Account State 5", "invalidity", "失效") {
        @Override
        public OpportunityNotifyMsgEnum getOpportunityNotifyMsgEnum() {
            return OPPORTUNITY_EMAIL_FOR_CUSTOMER_INVALID_REMINDER;
        }

    },
    /**
     * 不存在
     */
    NOT_EXIST("",  "", "不存在") {
        @Override
        public OpportunityNotifyMsgEnum getOpportunityNotifyMsgEnum() {
            return null;
        }

    };

    private String value;

    private String desc;

    private String newValue;

    AccountStatusEnum(String value, String newValue, String desc){
        this.value = value;
        this.newValue = newValue;
        this.desc = desc;
    }

    /**
     * 获取客户状态相应的邮件模板
     * @return
     */
    abstract public OpportunityNotifyMsgEnum getOpportunityNotifyMsgEnum();

    public String getValue() {
        return this.value;
    }

    public String getDesc() {
        return this.desc;
    }

    public String getNewValue() {
        return this.newValue;
    }

    /**
     * 客户草稿已创建但尚未生效
     * @param value
     * @return
     */
    public static boolean isCreatedCustomer(String value) {
        return Objects.equals(value, WAIT_FOR_SUBMIT.value)
                || Objects.equals(value, APPROVING.value);
    }

    /**
     * 生效的客户状态
     * @param value
     * @return
     */
    public static boolean isEffectiveCustomer(String value) {
        return Objects.equals(value, ACCT_ACTIVE_STATUS.value);
    }

    /**
     * 驳回或失效的客户状态
     * @param value
     * @return
     */
    public static boolean isInvalidCustomer(String value) {
        return Objects.equals(value, REFUSE.value) || Objects.equals(value, ACCT_INACTIVE_STATUS.value);
    }

    /**
     * 查找客户状态枚举
     * @param value
     * @return
     */
    public static AccountStatusEnum findAccountStatusEnum(String value) {
        if (StringUtils.isBlank(value)) {
            return NOT_EXIST;
        }
        for (AccountStatusEnum accountStatusEnum : AccountStatusEnum.values()) {
            if (accountStatusEnum.isMe(value)) {
                return accountStatusEnum;
            }
        }
        return NOT_EXIST;
    }

    public boolean isMe(String value) {
        return this.value.equalsIgnoreCase(value) || this.newValue.equalsIgnoreCase(value);
    }
}
