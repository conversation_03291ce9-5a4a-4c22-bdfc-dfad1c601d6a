package com.zte.mcrm.common.consts;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * 公共常量类
 * <AUTHOR>
 * @date 2017-12-12
 *
 */
public class CommonConst implements Serializable {

	private static final long serialVersionUID = 6652778328818381910L;
	/**缓存耗时**/
	public static final long CACHE_TIME_MAX=1800;
	/**缓存耗时**/
	public static final long CACHE_TIME_MIN=1000;
	/**中英文：CHS**/
	public static final String LANGUAGE_CHS = "CHS";
	/**中英文：ENU**/
	public static final String LANGUAGE_ENU = "ENU";
	/**中英文：zh**/
	public static final String LANGUAGE_ZH = "zh";
	/**中英文：zh_CN**/
	public static final String LANGUAGE_ZH_CN = "zh_CN";
	/**英文环境:EN_US*/
	public static final String LANGUAGE_EN_US = "en_US";
	/**英文环境:EN_US*/
	public static final String LANGUAGE_EN = "en";
	/**项目服务租户ID**/
	public static final String SIEBEL_PROJECT = "SIEBEL_PROJECT";
	/**商机服务租户ID**/
	public static final String PC_OPPORTUNITY = "pcOpportunity";
	/**siebel oracle数据源租户ID**/
	public static final String SIEBEL ="siebel";
	/**数据来源常量**/
	public static final String MOBILE_DATA_SOURCE = "mobile";
	/**股份*/
	public static final String SHARE = "T0001";
	/**子公司*/
	public static final String SUB_COMPANY = "T0002";

	public static final String Y = "Y";

	public static final String N = "N";

	public static final String INVALID = "（已失效）";

//	public static final String WITHOUT_ROW_ID ="找不到主键";

//	public static final String DATA_NOT_FUND ="找不到数据";

	public static final String DONT_REPEAT="TeamRepeat";

	public static final String LOVID_NULL = "LovIdTransferFail";

//	public static final String OPTY_TO_PROJECT_STATUS_ERROR = "商机状态为：商机培育，已发起转立项（可再次转立项）方可转商机";

	/**
	 * 最终用途页签字段必填校验信息对应的国际化资源key
	 */
	public static final String END_USE_INFO_TIPS_END_USER_TYPE_NULL = "endUseInfoEndUserTypeCanNotNull";
	public static final String END_USE_INFO_TIPS_END_USE_OF_USER_NULL = "endUseInfoEndUseOfEndUserCanNotNull";
	public static final String END_USE_INFO_TIPS_SPECIFIC_CUST_DESC_NULL = "endUseInfoSpecificCustomerDescCanNotNull";
	public static final String END_USE_INFO_TIPS_SPECIFIC_USAGE_DESC_NULL = "endUseInfoSpecificUsageDescCanNotNull";

	public static final String BACK_GROUP_IS_NOT_WHEN_TURNS_TO_PROJECT = "BackGroupIsNotWhenTrunsToProject";

	public static final String ACCOUNT_NATION_IS_NULL_TURNS_TO_PROJECT = "AccountNationisNullTrunsToProject";
	/**转立项时商机订货预测时间为空**/
	public static final String PREORDER_TIME_IS_NULL_TURNS_TO_PROJECT = "PreOrderTimeIsNullTrunsToProject";

	public static final String INTERNATIONAL_SCOPE_IS_NULL_TURNS_TO_PROJECT = "InternationalScopeIsNullTrunsToProject";

	public static final String ACCOUNT_TYPE_IS_NULL_TURNS_TO_PTOJECT = "AccountTypeIsNullTrunsToProject";

	public static final String FIRST_CLASS_IS_NULL_TURNS_TO_PROJECT = "FirstClassIsNullTrunsToProject";
	/**转立项发标时间不能为空**/
	public static final String BIDDING_IS_NULL_TURNS_TO_PROJECT = "BiddingIsNullTrunsToProject";

	public static final String SALE_MODEL_IS_NULL_TURNS_TO_PROJECT = "SaleModelIsNullTrunsToProject";

	public static final String OPTY_ACCT_IS_NULL = "OptyAcctIsNull";

	public static final String MTO_NAME_CODE_EXIST_BUT_PROPERTY_IS_NULL = "MtoNameCodeExistButMtoPropertyIsNull";

	public static final String PROJECT_PLACE_IS_NULL_TURNS_TO_PROJECT = "ProjectPlaceIsNullTrunsToProject";
	/**转立项商机等级人工输入为空**/
	public static final String OPTY_LEVEL_IS_NULL_TURNS_TO_PROJECT = "OptyLevelIsNUllTrunsToProject";
	/**转立项最终用途为空**/
	public static final String FINAL_USAGE_IS_NULL_TURNS_TO_PROJECT = "FinalUsageIsNullTrunsToProject";
	/**客户类型为运营商的时候商机转立项最终客户不能为空**/
	public static final String LAST_ACCT_IS_NULL_TURNS_TO_PROJECT = "LastAcctIsNullTrunsToProject";
	/**商机转立项订货预测金额为空**/
	public static final String PREORDER_AMT_IS_NULL_TURNS_TO_PROJECT = "PreOrderAmtIsNullTrunsToProject";
	/**商机转立项毛利率为空**/
	public static final String RATE_OF_MARGIN_IS_NULL_TURNS_TO_PROJECT = "RateOfMarginIsNullTrunsToProject";
	/**转立项商机等级系统计算输入为空**/
	public static final String OPTY_SYS_LEVEL_IS_NULL_TRUNS_TO_PROJECT = "OptySysLevelIsNullTrunsToProject";
	/**转立项订货预测为空**/
	public static final String ORDER_PREDICT_IS_NULL_TURNS_TO_PROJECT = "OrderPredictIsNullTrunsToProject";
	/**转立项订货预测预计签单时间为空**/
	public static final String ORDER_PREDICT_TIME_IS_NULL_TURNS_TO_PROJECT = "OrderPredictTimeIsNullTrunsToProject";
	/**转立项订货预测预计签单时间晚于当前时间**/
	public static final String ORDER_PREDICT_BEFORE_CURRENT_TIME_TURNS_TO_PROJECT = "OrderPredictBeforeCurrentTimeTrunsToProject";

	/**转立项服务产品产品占比未达100%**/
	public static final String ORDER_PREDICT_PRODUCT_PROPORTION_TURNS_TO_PROJECT = "OrderPredictProductProportionTrunsToProject";
	/**转立项商机订货预测时间过期**/
	public static final String PRE_ORDER_TIME_BE_OVERDUE_TURNS_TO_PROJECT = "PreOrderTimeBeOverdueTrunsToProject";
	/**转立项客户信用评级为空**/
	public static final String ACCT_CREDIT_IS_NULL_TURNS_TO_PROJECT = "AcctCreditIsNullTrunsToProject";
	/**转立项客户信用评级过期**/
	public static final String ACCT_CREDIT_BE_OVERDUE_TURNS_TO_PROJECT = "AcctCreditBeOverdueTrunsToProject";
	/**转立项客户信用评级失效**/
	public static final String ACCT_CREDIT_DIS_ACTIVE_TURN_TO_PROJECT = "AcctCreditDisActiveTrunsToProject";
	/**转立项业务范围为服务的缺少服务产品**/
	public static final String SERVICE_WITHOUT_SERVICE_PROJECT = "SeverWithoutServerProduct";
	/**转立项业务范围为服务类时缺少服务属性**/
	public static final String SERVICE_ATTRIBUTE_IS_NULL_TURN_TO_PROJECT = "ServerAttributeIsNullTrunsToProject";
	/**订货预测四层产品存在空值**/
	public static final String FOUR_LEVEL_PRODUCT_CONTAIN_NULL_TURN_TO_PROJECT = "FourLevelProductContainNullTrunsToProject";

	/**当前状态不允许操作**/
	public static final String STATUS_CAN_NOT_OPERATER = "StatusCanNotOperater";
	/**找不到数据**/
	public static final String DATANOTFOUND = "DataNotFound";

	/**无权限添加非普通团队成员**/
	public static final String NOT_AUTH_ADD_DISNORMAL_TEAM = "NotAuthAddDisNormalTeam";
	/**无权限修改团队成员**/
	public static final String NOT_AUTH_EDIT_TEAM = "NotAuthEditTeam";
	/**无权限删除团队成员**/
	public static final String NOT_AUTH_DELETE_TEAM = "NotAuthDeleteTeam";
	/**不允许删除商机负责人**/
	public static final String CAN_NOT_DELETE_OPTY_OWNER = "CannotDeleteOptyOwner";
	/**没有权限修改“核心信息是否开放”**/
	public static final String NOT_AUTH_EDIT_CORE_FLG = "NotAuthEditCoreFlg";
	/**没有权限修改商机负责人**/
	public static final String NOT_AUTH_EDIT_OPTY_OWNER = "NotAuthEditOptyOwner";
	/**查不到商机负责人**/
	public static final String CAN_NOT_FIND_OPTY_OWNER = "FindOptyOwnerIsNull";
	/**双语提示 - 数据找不到*/
	public static final String DATA_NOT_FOUND = "DataNotFound";
	/**双语提示 - 保存异常*/
	public static final String SAVE_EXCEPTION = "SaveException";
	/**双语提示 - 归属客户经理存在*/
	public static final String BELONG_MANAGER_EXIST = "BelongManagerExist";
	/**双语提示 - 关闭原因不存在*/
	public static final String CLOSE_REASON_NOT_EXIST = "CloseReasonNotExist";
	/**双语提示 - 退回原因不存在*/
	public static final String BACK_REASON_NOT_EXIST = "BackReasonNotExist";
	/**双语提示 - 用户不存在*/
	public static final String USER_NOT_FOUND = "UserNotFound";
	/**双语提示 - 线索不存在*/
	public static final String CLUE_NOT_FOUND = "ClueNotFound";
	/**双语提示 - 线索不允许修改*/
	public static final String CLUE_STATUS_NOT_CHANGE = "ClueStatusNotChange";
	/**双语提示 - 线索关闭失败*/
	public static final String CLUE_CLOSE_FAIL = "ClueCloseFail";
	/**双语提示 - 线索删除失败*/
	public static final String CLUE_DELETE_FAIL = "ClueDeleteFail";
	/**双语提示 - 值列表查询失败*/
	public static final String LOV_QUERY_FAIL = "LovQueryFail";
	/**双语提示 - 检查不到邮件发件人*/
	public static final String NO_MAIL_FROM = "NoMailFrom";
	/**双语提示 - 检查不到邮件系统名称*/
	public static final String NO_MAIL_SYSTEM_NAME = "NoMailSysName";
	/**双语提示 - 收件人为空*/
	public static final String NO_RECEIVERS = "NoReceivers";
	/**双语提示 - 邮件中看到的系统名称为空*/
	public static final String NO_MAIL_BODY_SYSTEM_NAME = "NoMailBodySysName";
	/**双语提示 - 生成编码失败*/
	public static final String FAIL_TO_GENERATE_NUMBER = "FailToGenerateNumber";
	/**双语提示 - 商机状态不允许修改*/
	public static final String OPPORTUNITY_STATUS_NOT_CHANGE = "OpportunityStatusNotChange";
	/**双语提示 - 当前状态无法转立项*/
	public static final String STATUS_CAN_NOT_TRANSFER_PROJECT = "StatusCannotTransferProject";
	/**双语提示 - 商机转立项异常*/
	public static final String FAIL_TRANSFER_OPPORTUNITY = "FailTransferOpportunity";
	/**双语提示 - 商机id不能为空*/
	public static final String OPPORTUNITY_ID_EMPTY = "OpportuntiyIdEmpty";
	/**双语提示 - 商机名称不能为空*/
	public static final String  OPPORTUNITY_NAME_EMPTY = "OpportunityNameEmpty";
	/**双语提示 - 代表处不能为空*/
	public static final String  OPPORTUNITY_DEPARTMENT_EMPTY = "OpportunityDepartmentEmpty";
	/**双语提示 - 商机负责人不能为空*/
	public static final String  OPPORTUNITY_MANAGER_EMPTY = "OpportunityManagerEmpty";
	/**双语提示 - 该商机不存在*/
	public static final String OPPORTUNITY_NOT_FOUND =  "OpportunityNotFound";
	/**双语提示 - 只有商机总监或总监办客户经理才允许修改商机群*/
	public static final String OPPORTUNITY_GROUP_AUTH = "OpportunityGroupAuth";
	/**双语提示 - 您没有查看商机核心信息的权限*/
	public static final String NO_OPPORTUNITY_AUTH_TIP = "NoOpportunityAuthTip";
	/**双语提示 - 员工工号不能为空*/
	public static final String  EMPLOYEE_NO_EMPTY = "EmployeeNoEmpty";
	/**双语提示 - 无权限进入空间*/
	public static final String NO_SPACE_AUTH = "NoSpaceAuth";
	/**双语提示 - 商机负责人只能添加一个*/
	public static final String OPPORTUNITY_MANAGER_UNIQUE = "OpportunityManagerUnique";

	/**修改魔法值--石浪生*/
	public static final String S ="S";
	/**修改魔法值--石浪生*/
	public static final String PROC_STATUS="procStatus";

	/**修改魔法值--石浪生*/
	public static final String PROC_MSG="procMsg";

	/**修改魔法值--石浪生*/
	public static final String SUBMIT="submit";
	/**kafka topic*/
	public static final String TOPIC_ZTE_CRM_COMMON = "zte-crm-common";
	/**@*/
	public static final String MAIL_AT = "@";
	/**通过*/
	public static final String PASS = "pass";

	/**预计签单金额*/
	public static final String OPPORTUNITY_IS_APPROVED_QUICKLY = "opportunityisApprovedQuickly";

	/**项目执行地*/
	public static final String PROJECT_EXECUTION_PLACE_CANNOT_BE_EMPTY = "ProjectExecutionPlaceCannotBeEmpty";

	/**附件*/
	public static final String ATTENTION_UPLOAD_ATTACHMENT_INFORMATION = "AttentionUploadAttachmentInformation";
	/**商机状态：草稿*/
	public static final String DRAFT = "Draft";
	/**商机状态：已退回*/
	public static final String REFUSED = "Refused";
	/**商机状态：商机培育*/
	public static final String RENEWING = "Renewing";


	public static final String SUBORG = "suborg";
	public static final String MTO = "mto";
	public static final String PROD = "prod";
	public static final String TRADE = "trade";
	public static final String SERVICE = "service";
	public static final String OPERSER = "operser";
	public static final String OPERATOR = "operator";
	public static final String GOVERSER = "goverser";
	public static final String GOVERNMENT = "government";



	/**返回提示*/
	public static final String TIP = "tip";

	/**返回提示*/
	public static final String CODE_FLAG = "code";

	/**返回提示*/
	public static final String CODE_0 = "0";

	/**返回提示*/
	public static final String CODE_1 = "1";

	/**返回提示*/
	public static final String CODE_LIST = "list";

	/**日期格式*/
	public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

	/**商机*/
	public static final String OPPORTUNITY="商机";

	/**新商机报备*/
	public static final String NEW_OPPORTUNITY="新商机报备";

	/**线索*/
	public static final String LEAD="线索";

	/**渠道宝报备*/
	public static final String CHANNELREPORT="报备";

	/**
	 * 分页查询参数-缺省页
	 */
	public static final int DEFAULT_PAGE_NUM = 1;

	/**
	 * 分页查询参数-缺省页记录数
	 */
	public static final int DEFAULT_PAGE_SIZE = 10;

	/**
	 * 分页查询参数-最大页记录数
	 */
	public static final int MAX_PAGE_SIZE = 1000;

	/** zh_CN   中文   */
	public static final String ZH_CN = "zh_CN";

	/** en_US   英语-美国  */
	public static final String EN_US = "en_US";
	/**
	 * undefined
	 */
	public static final String UNDEFINED = "undefined";

	/**
	 * system
	 */
	public static final  String SYSTEM = "system";
	/**ServiceData - msg**/
	public static final String STR_MSG = "msg";


	/**海外部门编码*/
	public static final List<String> OVERSEAS_AREA_ORG_LIST = Arrays.asList("ORG0008312", "ORG0007128", "ORG0007137", "ORG2223727");

	public static final String URL_SPER = "/";
	public static final String URL_SPER_LAST_LAYER = "..";

}
