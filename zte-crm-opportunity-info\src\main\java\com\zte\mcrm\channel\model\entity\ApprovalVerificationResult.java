
package com.zte.mcrm.channel.model.entity;

import com.google.gson.annotations.Expose;
import io.swagger.annotations.ApiModelProperty;


public class ApprovalVerificationResult {

    @ApiModelProperty("submit为N时的弹窗显示内容")
    private Object msg;
    @ApiModelProperty("是否继续提交，Y:继续,N:中止")
    private String submit;

    public Object getMsg() {
        return msg;
    }

    public void setMsg(Object msg) {
        this.msg = msg;
    }

    public String getSubmit() {
        return submit;
    }

    public void setSubmit(String submit) {
        this.submit = submit;
    }

    @Override
    public String toString() {
        return "ApprovalVerificationResult{" +
                "msg=" + msg +
                ", submit='" + submit + '\'' +
                '}';
    }
}
