package com.zte.mcrm.channel.service.channel;

import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.crm.eva.base.infrastructure.adapter.IchannelBaseAdapter;
import com.zte.crm.eva.base.infrastructure.client.IchannelBaseFeign;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.web.HttpHeaderUtil;
import com.zte.itp.security.AESOperator;
import com.zte.mcrm.adapter.approval.model.ApprovalTaskInfo;
import com.zte.mcrm.adapter.approval.model.FlowActiveTaskInfo;
import com.zte.mcrm.adapter.approval.model.FlowTaskParameter;
import com.zte.mcrm.adapter.approval.model.dto.ApprovalActiveTaskParamsDTO;
import com.zte.mcrm.adapter.approval.model.dto.ApprovalRevokeParamsDTO;
import com.zte.mcrm.adapter.approval.model.dto.ApprovedTask;
import com.zte.mcrm.adapter.approval.service.ApprovalFlowService;
import com.zte.mcrm.adapter.authorization.service.ChannelAuthService;
import com.zte.mcrm.adapter.authorization.service.SalesOpportunitiesAuthService;
import com.zte.mcrm.adapter.constant.ProjectAuthStatusEnum;
import com.zte.mcrm.adapter.mail.service.SendMailService;
import com.zte.mcrm.adapter.model.AuthConstraintDTO;
import com.zte.mcrm.adapter.model.dto.AccountInfo;
import com.zte.mcrm.adapter.model.dto.ChannelAccountDetailDTO;
import com.zte.mcrm.adapter.model.dto.OrganizationNode;
import com.zte.mcrm.adapter.model.dto.UcsUserInfoDTO;
import com.zte.mcrm.adapter.model.vo.BulkCodeValuesByOneTypeVo;
import com.zte.mcrm.adapter.model.vo.IndustryTreeDataVO;
import com.zte.mcrm.adapter.model.vo.PrmQuickCodeValueVO;
import com.zte.mcrm.adapter.projectauthorization.utils.ProjectAuthorizationUtils;
import com.zte.mcrm.adapter.service.CustomerInfoService;
import com.zte.mcrm.adapter.service.IBmtUcsService;
import com.zte.mcrm.adapter.service.PrmService;
import com.zte.mcrm.channel.constant.*;
import com.zte.mcrm.channel.dao.ComApprovalRecordDao;
import com.zte.mcrm.channel.dao.OpportunityDao;
import com.zte.mcrm.channel.dao.OpportunityDetailDao;
import com.zte.mcrm.channel.dao.OpportunityProductDao;
import com.zte.mcrm.channel.model.dto.OpportunityInfoDTO;
import com.zte.mcrm.channel.model.dto.OpportunityQueryDTO;
import com.zte.mcrm.channel.model.dto.ProjectAuthInfoDto;
import com.zte.mcrm.channel.model.dto.SendEmailPreDto;
import com.zte.mcrm.channel.model.entity.*;
import com.zte.mcrm.channel.model.vo.*;
import com.zte.mcrm.channel.service.OpptyCustomerCreateRecordService;
import com.zte.mcrm.channel.service.prm.IPrmOpportunityApprovalService;
import com.zte.mcrm.channel.service.prm.IPrmOpportunityService;
import com.zte.mcrm.channel.util.CommonRemoteUtils;
import com.zte.mcrm.common.access.vo.ComDictionaryMaintainVO;
import com.zte.mcrm.common.business.service.IComDictionaryMaintainService;
import com.zte.mcrm.common.business.service.IKeyIdService;
import com.zte.mcrm.common.consts.ComDictionaryMaintainConsts;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.consts.CommonConstant;
import com.zte.mcrm.common.exception.BusinessRuntimeException;
import com.zte.mcrm.common.framework.exception.PrmRetCode;
import com.zte.mcrm.common.model.PersonAndOrgInfoVO;
import com.zte.mcrm.common.util.*;
import com.zte.mcrm.export.tool.ExportExcel;
import com.zte.mcrm.export.tool.ExportExcelHandle;
import com.zte.mcrm.export.tool.ExportListObjectExcel;
import com.zte.mcrm.logger.bussiness.service.LoggerService;
import com.zte.mcrm.opportunity.utils.PersonAndOrgInfoUtil;
import com.zte.opty.common.enums.OptyStatusEnum;
import com.zte.opty.common.util.MapperUtil;
import com.zte.opty.dao.SOpportunityRepository;
import com.zte.opty.dao.SOptyXDao;
import com.zte.opty.model.bo.SOptyBO;
import com.zte.opty.model.bo.SOptyXBO;
import com.zte.opty.model.vo.IHolOrgVO;
import com.zte.opty.sync.util.CommonMapUtil;
import com.zte.opty.sync.util.LcapConverterUtil;
import com.zte.opty.sync.util.RadioFieldConverterUtils;
import com.zte.springbootframe.util.local.LocalMessageUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.zte.mcrm.channel.constant.OpportunityConstant.*;


/**
 *  服务类
 * <AUTHOR>
 * @date 2021/09/14
 */
@Service
public class IOpportunityServiceImpl implements IOpportunityService{

	@Autowired
    private IKeyIdService iKeyIdService;

	@Autowired
	private OpportunityDao opportunityDao ;

	@Autowired
	private SOpportunityRepository sOpportunityRepository ;

	@Autowired
	IOpportunityMonthReportService opportunityMonthReportService;


	@Autowired
	private OpportunityDetailDao opportunityDetailDao ;

	@Autowired
	private OpportunityProductDao  opportunityProductDao ;

	@Autowired
	ComApprovalRecordDao comApprovalRecordDao;

	@Autowired
	OpportunityInfoServiceImpl opportunityInfoServiceImpl;

	@Autowired
	private IComDictionaryMaintainService comDictionaryMaintainService;

	@Autowired
	private IPrmOpportunityApprovalService iPrmOpportunityApprovalService;

	@Autowired
	IPrmOpportunityService prmOpportunityService;

	@Autowired
	CustomerInfoService customerInfoService;

	@Autowired
	PrmService prmService;

	@Resource
	ApprovalInfoService approvalInfoService;

	@Autowired
	private LoggerService loggerService;

	@Value("${encryption.secretKeySixteen}")
	private String secretKeySixteen;

	@Value("${encryption.iv}")
	private String iv;
	@Value("${opportunity.mail.from}")
	private String mailFrom;
	@Value("${prmui.url}")
	private String prmUrl;
	@Value("${opportunity.internalui.url}")
	private String internaluiUrl;

	@Autowired
	SalesOpportunitiesAuthService salesOpportunitiesAuthService;

	@Autowired
	ApprovalFlowService approvalFlowService;

	@Autowired
	private IBmtUcsService iBmtUcsService;

	@Autowired
	private SendMailService sendMailService;

	private  static Logger logger = LoggerFactory.getLogger(IOpportunityServiceImpl.class);

	@Autowired
	private IOpportunityDetailService opportunityDetailService;

	@Autowired
	IOpportunityInfoService opportunityInfoService;


	@Autowired
	ChannelAuthService channelAuthService;

	@Autowired
	private OpptyCustomerCreateRecordService opptyCustomerCreateRecordService;

	@Resource(name="sendRestrictedEntityMainThread")
	ThreadPoolTaskExecutor taskExecutor;

	@Autowired
	private SOptyXDao sOptyXDao;

	@Autowired
	private IchannelBaseFeign ichannelBaseFeign;

	@Autowired
	IchannelBaseAdapter ichannelBaseAdapter;

	@Autowired
	CommonRemoteUtils commonRemoteUtils;

	/**
	 * 根据主键获取实体对象
	 * @param rowId 主键ID
	 * @return
	 * <AUTHOR>
     * @date 2021/09/14
     */
	@Override
	public Opportunity get(String rowId) {
		Opportunity opportunity = opportunityDao.get(rowId);
		Optional.ofNullable(opportunity).ifPresent(
				e -> e.setBusinessTypeCd(CommonMapUtil.DELIVERY_INTERFACE_MAP.inverse().getOrDefault(e.getBusinessTypeCd(), e.getBusinessTypeCd())));
		return opportunity;
	}

	/**
	 * 根据ID查询-不过滤enabledFlag
	 * @param rowId 主键ID
	 * @return
	 * <AUTHOR>
	 * @date 2021/09/14
	 */
	@Override
	public Opportunity getAll(String rowId) {
		Opportunity opportunity = opportunityDao.getAll(rowId);
		Optional.ofNullable(opportunity).ifPresent(
				e -> e.setBusinessTypeCd(CommonMapUtil.DELIVERY_INTERFACE_MAP.inverse().getOrDefault(e.getBusinessTypeCd(), e.getBusinessTypeCd())));
		return opportunity;
	}

	@Override
	public Opportunity getByOptyCd(String optyCd) {
		Opportunity opportunity = opportunityDao.getByOptyCd(optyCd);
		Optional.ofNullable(opportunity).ifPresent(
				e -> e.setBusinessTypeCd(CommonMapUtil.DELIVERY_INTERFACE_MAP.inverse().getOrDefault(e.getBusinessTypeCd(), e.getBusinessTypeCd())));
		return opportunity;
	}

	/**
	 * 更新商机状态
	 * @param rowId
	 * @param status
	 * @return
	 */
	@Override
	public int updateStatus(String rowId, OptyStatusEnum status){
		String emp = HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
		Opportunity opportunity = new Opportunity();
		opportunity.setRowId(rowId);
		opportunity.setStatusCd(status.getCode());
		opportunity.setLastUpdBy(emp);
		opportunity.setLastUpd(new Date());
		if (OptyStatusEnum.OPTY_RENEWING == status || OptyStatusEnum.OPTY_APPROVAL_NOT_PASSED == status){
			opportunity.setSuccessDate(new Date());
		}else if (OptyStatusEnum.OPTY_SUSPEND == status){
			opportunity.setExpiryDate(new Date());
		}
		return sOpportunityRepository.updateStatus(opportunity);
	}


	@Override
	public OpportunityInfo getIChannelOpportunity(String rowId) throws Exception {
		OpportunityInfo opportunityInfo = new OpportunityInfo();
		Opportunity opportunity = opportunityDao.get(rowId);
		if(null == opportunity ){
			return null;
		}
		opportunity.setBusinessTypeCd(CommonMapUtil.DELIVERY_INTERFACE_MAP.inverse().getOrDefault(opportunity.getBusinessTypeCd(), opportunity.getBusinessTypeCd()));
		if (opportunity.getOptyCd().startsWith(OpportunityConstant.OPPORTUNITY_PREFIX)){
			opportunityInfo = getOpportunity(rowId);
		}else{
			opportunityInfo = getOldOpportunityInfo(rowId);
		}
		String businessManagerId = opportunityInfo.getOpptyBaseInfo().getBusinessManagerId();
		if (StringUtils.isNotBlank(businessManagerId)) {
			opportunityInfo.setBusinessManagerId(businessManagerId);
		}

		// 设置月报信息
		setMonthlyReportInfo(opportunityInfo, opportunity);
		// 招标类型 == 议标项目，预计发标/议标时间 = 预计签单时间
		setDate1ToDate2(opportunityInfo);
		return opportunityInfo;
	}

	private void setMonthlyReportInfo(OpportunityInfo opportunityInfo, Opportunity opportunity){
		OpportunityMonthReportVO opportunityMonthReport = getReportInfo(opportunity.getRowId());
		if (null != opportunityMonthReport){
			opportunityInfo.setReportInfo(opportunityMonthReport.getReportInfo());
			opportunityInfo.setCurrentStatus(opportunityMonthReport.getCurrentStatus());
		}
		// 商机当前状态默认为进行中
		if (StringUtils.isBlank(opportunityInfo.getCurrentStatus())
			&& OptyStatusEnum.OPTY_RENEWING.getCode().equals(opportunity.getStatusCd())){
			opportunityInfo.setCurrentStatus(OpportunityCurrentStatus.UNDERWAY.getValue());
		}
	}

	public void setDate1ToDate2(OpportunityInfo opportunityInfo) {
		if (BooleanUtil.and(!TenderTypeEnum.TENDER_PROJECT.getValue().equals(opportunityInfo.getOpptyBaseInfo().getTenderTypeCode()),
				Objects.isNull(opportunityInfo.getOpptyBaseInfo().getDate2()))) {
			opportunityInfo.getOpptyBaseInfo().setDate2(opportunityInfo.getOpptyBaseInfo().getDate1());
		}
	}

	/**
	 * 获取当前月的月报进展信息
	 * @param rowId
	 * @return
	 */
	private OpportunityMonthReportVO getReportInfo(String rowId){
		SimpleDateFormat parser = new SimpleDateFormat(OpportunityConstant.DATE_FORMAT_YYYYMM);
		Date date = new Date();
		String reportMonth = parser.format(date);
		return opportunityMonthReportService.getMonthReportDetail(rowId, reportMonth);
	}

	@Override
	public OpportunityInfo getOldOpportunityInfo(String rowId) throws Exception{
		OldOpportunityInfoEntity oldOpportunityInfoEntity = opportunityDao.getOldOpportunityByRowId(rowId);
		OpportunityInfo opportunityInfo = new OpportunityInfo();
		if (OptyStatusEnum.TICKET_LOSS.getCode().equals(oldOpportunityInfoEntity.getStatusCd())){
			oldOpportunityInfoEntity.setStatusCd(OptyStatusEnum.OPTY_SUSPEND.getCode());
		}
		oldOpportunityInfoEntity.setBusinessTypeCd(CommonMapUtil.DELIVERY_INTERFACE_MAP.inverse().getOrDefault(oldOpportunityInfoEntity.getBusinessTypeCd(), oldOpportunityInfoEntity.getBusinessTypeCd()));
		BeanUtils.copyProperties(oldOpportunityInfoEntity, opportunityInfo);
		OpportunityInfo.OpptyBaseInfo opptyBaseInfo = getOpptyBaseInfo(oldOpportunityInfoEntity);
		OpportunityInfo.ConsumerInfo consumerInfo = getConsumerInfo(oldOpportunityInfoEntity);
		OpportunityInfo.TransferProjectInfo transferProjectInfo = getTransferProjectInfo(oldOpportunityInfoEntity);
		OpportunityInfo.ChannelConsumerInfo channelConsumerInfo = getChannelConsumerInfo(oldOpportunityInfoEntity);
		List<OpportunityInfo.ProductInfo> productInfos = getProductInfos(rowId, true);
		opportunityInfo.setProductInfo(productInfos);
		opportunityInfo.setOpptyBaseInfo(opptyBaseInfo);
		opportunityInfo.setConsumerInfo(consumerInfo);
		opportunityInfo.setTransferProjectInfo(transferProjectInfo);
		opportunityInfo.setChannelConsumerInfo(channelConsumerInfo);
		setDictValue(opportunityInfo);
		if(StringUtils.isNotBlank(opptyBaseInfo.getBusinessManagerId())) {
			PersonAndOrgInfoVO personAndOrgInfoVO = EmployeeUtil.getEmployeeByEmpNo(opptyBaseInfo.getBusinessManagerId());
			if (null != personAndOrgInfoVO && StringUtils.isNotBlank(personAndOrgInfoVO.getEmpNO())){
				opptyBaseInfo.setBusinessManagerId(personAndOrgInfoVO.getEmpNO());
				opptyBaseInfo.setBusinessManagerName(personAndOrgInfoVO.getEmpName());
				opportunityInfo.setBusinessManagerName(personAndOrgInfoVO.getEmpName());
				opptyBaseInfo.setBusinessManagerEmail(personAndOrgInfoVO.getEmail());
			}
		}
		return opportunityInfo;
	}

	@Override
	public OpportunityInfo getOpportunity(String rowId) throws Exception {
		OpportunityInfo opportunityInfo = getOpportunityInfo(rowId);
		List<ApprovedTask> approvedTasks = iPrmOpportunityApprovalService.queryApprovedTasks(rowId);
		if (CollectionUtils.isEmpty(approvedTasks)) {
			opportunityInfo.setApprover( StringUtils.isNotBlank(opportunityInfo.getBusinessManagerName())?opportunityInfo.getBusinessManagerName():opportunityInfo.getOpptyBaseInfo().getBusinessManagerName());
			return opportunityInfo;
		}
		String approveResultFilterFlag = OptyStatusEnum.OPTY_APPROVAL_NOT_PASSED.getCode().equals(opportunityInfo.getStatusCd()) ? CommonConst.N : CommonConst.Y;
		boolean needApproveResultFlag = !Arrays.asList(OptyStatusEnum.DRAFT.getCode(),
													   OptyStatusEnum.APPROVING.getCode(),
													   OptyStatusEnum.OPTY_SUSPEND.getCode()).contains(opportunityInfo.getStatusCd());
		if (needApproveResultFlag) {
			approvedTasks.stream()
					.filter(approvedTask -> ApprovalNodeStatusEnum.COMPLETED.getDescEn().equals(approvedTask.getTaskStatus()))
					.filter(task -> approveResultFilterFlag.equals(task.getResult()))
					.max(Comparator.comparing(ApprovedTask::getApprovalDate)).ifPresent(task2 -> {
						opportunityInfo.setApprover(task2.getApprover());
						opportunityInfo.setOpinion(task2.getOpinion());
						opportunityInfo.setApprovalDate(task2.getApprovalDate());
					});
		}


		//替换工号
		EmpProcessUtil.replaceEmpNoWithName(Lists.newArrayList(opportunityInfo));
		return opportunityInfo;
	}

	@Override
	public OpportunityInfo getOpportunityInfo(String rowId) throws Exception {

		Opportunity opportunity = null;
		OpportunityDetail opportunityDetail;
		if((opportunity = opportunityDao.get(rowId)) == null
				|| (opportunityDetail = opportunityDetailDao.get(rowId))==null){
			return new OpportunityInfo();
		}
		opportunityDetail.setTenderTypeCode(CommonMapUtil.BIDDING_TYPE_MAP.inverse()
				.getOrDefault(opportunityDetail.getTenderTypeCode(),opportunityDetail.getTenderTypeCode()));
		opportunity.setBusinessTypeCd(CommonMapUtil.DELIVERY_INTERFACE_MAP.inverse().get(opportunity.getBusinessTypeCd()));
		if (!opportunity.getOptyCd().startsWith(OpportunityConstant.OPPORTUNITY_PREFIX)
			&& OptyStatusEnum.TICKET_LOSS.getCode().equals(opportunity.getStatusCd())){
			opportunity.setStatusCd(OptyStatusEnum.OPTY_SUSPEND.getCode());
		}

		OpportunityInfo opportunityInfo = getOpportunityInfo(opportunity, opportunityDetail);

		//基本信息
		OpportunityInfo.OpptyBaseInfo opptyBaseInfo = getOpptyBaseInfo(opportunity, opportunityDetail);
		opportunityInfo.setOpptyBaseInfo(opptyBaseInfo);
		//最终用户
		OpportunityInfo.ConsumerInfo consumerInfo = getConsumerInfo(opportunity, opportunityDetail);
		opportunityInfo.setConsumerInfo(consumerInfo);
		//产品信息
		List<OpportunityInfo.ProductInfo> productInfos = getProductInfos(rowId, false);
		opportunityInfo.setProductInfo(productInfos);

		// 转立项 部分信息
		OpportunityInfo.TransferProjectInfo transferProjectInfo = getTransferProjectInfo(opportunity, opportunityDetail);
		//替换工号
		EmpProcessUtil.replaceEmpNo(Lists.newArrayList(transferProjectInfo));
		opportunityInfo.setTransferProjectInfo(transferProjectInfo);
		//设置字典值
		setDictValue(opportunityInfo);
		//渠道商信息
		OpportunityInfo.ChannelConsumerInfo customerInfo = getChannelConsumerInfo(opportunityDetail);
		opportunityInfo.setChannelConsumerInfo(customerInfo);

		//查询业务经理邮箱
		List<UcsUserInfoDTO> userInfos = iBmtUcsService.getUserInfoByAccountIdList(Lists.newArrayList(opptyBaseInfo.getBusinessManagerId()));
		if (CollectionUtils.isNotEmpty(userInfos)) {
			opptyBaseInfo.setBusinessManagerEmail(userInfos.get(0).getEmail());
		}

		// 解密返回
		OpportunityInfo.ConsumerInfo consumer = opportunityInfo.getConsumerInfo();
		return opportunityInfo;
	}

	private OpportunityInfo.TransferProjectInfo getTransferProjectInfo(Opportunity opportunity, OpportunityDetail opportunityDetail) {
		OpportunityInfo.TransferProjectInfo  transferProjectInfo  = new OpportunityInfo.TransferProjectInfo();
		transferProjectInfo.setBusinessTypeCd(opportunity.getBusinessTypeCd());
		transferProjectInfo.setSignCustomerCode(opportunity.getPrDeptOuId());
		transferProjectInfo.setSignCustomerName(customerInfoService.getAccountByCodeOrId(opportunity.getPrDeptOuId()).getAccountName());
		transferProjectInfo.setDirectorOfPsc(opportunityDetail.getDirectorOfPsc());
		transferProjectInfo.setFinalUsage(opportunityDetail.getFinalUsage());
		transferProjectInfo.setSalesType(opportunityDetail.getSalesType());
		transferProjectInfo.setNationalAreaId(opportunityDetail.getNationalAreaId());
		transferProjectInfo.setNationalAreaName(opportunityDetail.getNationalAreaName());
		transferProjectInfo.setEndUserType(opportunityDetail.getEndUserType());
		transferProjectInfo.setEndUseOfEndUser(opportunityDetail.getEnduseOfEnduser());
		transferProjectInfo.setSpecificCustomerDesc(opportunityDetail.getSpecificCustomerDesc());
		transferProjectInfo.setSpecificUsageDesc(opportunityDetail.getSpecificUsageDesc());
		return transferProjectInfo;
	}

	private OpportunityInfo.TransferProjectInfo getTransferProjectInfo(OldOpportunityInfoEntity oldOpportunityInfoEntity) {
		OpportunityInfo.TransferProjectInfo  transferProjectInfo  = new OpportunityInfo.TransferProjectInfo();
		BeanUtils.copyProperties(oldOpportunityInfoEntity, transferProjectInfo);
		AccountInfo accountInfo = customerInfoService.getAccountByCodeOrId(oldOpportunityInfoEntity.getPrDeptOuId());
		transferProjectInfo.setSignCustomerCode(accountInfo.getCustNo());
		transferProjectInfo.setSignCustomerName(accountInfo.getAccountName());
		return transferProjectInfo;
	}

	private OpportunityInfo.ChannelConsumerInfo getChannelConsumerInfo(OpportunityDetail opportunityDetail) {
		OpportunityInfo.ChannelConsumerInfo customerInfo = new OpportunityInfo.ChannelConsumerInfo();
		customerInfo.setCustomerName(opportunityDetail.getCustomerName());
		customerInfo.setCrmCustomerCode(opportunityDetail.getCrmCustomerCode());
		customerInfo.setSourceCrmCustomerCode(opportunityDetail.getSourceCrmCustomerCode());
		customerInfo.setSourceCustomerName(opportunityDetail.getSourceCustomerName());
		customerInfo.setAgencyLevelName(opportunityDetail.getAgencyLevelName());
		customerInfo.setAgencyLevelCode(opportunityDetail.getAgencyLevelCode());
		customerInfo.setRestrictedParty(opportunityDetail.getAgencyRestrictionFlag());
		return customerInfo;
	}

	private OpportunityInfo.ChannelConsumerInfo getChannelConsumerInfo(OldOpportunityInfoEntity oldOpportunityInfoEntity) {
		OpportunityInfo.ChannelConsumerInfo customerInfo = new OpportunityInfo.ChannelConsumerInfo();
		if (StringUtils.isBlank(oldOpportunityInfoEntity.getCrmCustomerCode())
				&& StringUtils.isNotBlank(oldOpportunityInfoEntity.getSecondDealerId())) {
			AccountInfo accountInfo = customerInfoService.getAccountByCodeOrId(oldOpportunityInfoEntity.getSecondDealerId());
			customerInfo.setCustomerName(accountInfo.getCustNo());
			customerInfo.setCrmCustomerCode(StringUtils.isNotBlank(accountInfo.getAccountNum()) ? accountInfo.getAccountNum() : accountInfo.getId());
		}else{
			customerInfo.setCustomerName(oldOpportunityInfoEntity.getCustomerName());
			customerInfo.setCrmCustomerCode(oldOpportunityInfoEntity.getCrmCustomerCode());
		}
		return customerInfo;
	}

	private void setDictValue(OpportunityInfo opportunityInfo) {
		List<String> types = Lists.newArrayList(OpportunityConstant.CURRENT_PHASES_TYPE,
				OpportunityConstant.WIN_RATE_TYPE,
				OpportunityConstant.TYPE_FOR_TENDER_TYPE,
				OpportunityConstant.OPPORTUNITY_STATUS,
				OpportunityConstant.ZTE_OPPTY_BUS_TYPE,
				OpportunityConstant.ZTE_FINAL_USAGE,
				OpportunityConstant.ZTE_OPTY_SALES,
				OpportunityConstant.SOURCE_OF_OPPORTUNITY,
				OpportunityConstant.END_USER_TYPE_DICTIONARY,
				OpportunityConstant.END_USE_OF_END_USER_DICTIONARY
				);
		Map<String, List<ComDictionaryMaintainVO>> dcitMap = comDictionaryMaintainService.queryByTypeList(types);

		String projectPhasesName = DictUtils.getName(opportunityInfo.getOpptyBaseInfo().getProjectPhasesCode(), dcitMap.get(OpportunityConstant.CURRENT_PHASES_TYPE));
		String tenderTypeName = DictUtils.getName(opportunityInfo.getOpptyBaseInfo().getTenderTypeCode(), dcitMap.get(OpportunityConstant.TYPE_FOR_TENDER_TYPE));
		String businessTypeName = DictUtils.getName(opportunityInfo.getTransferProjectInfo().getBusinessTypeCd(), dcitMap.get(OpportunityConstant.ZTE_OPPTY_BUS_TYPE));
		String finalUsageName = getFinalUsageName(opportunityInfo.getTransferProjectInfo().getFinalUsage());
		String salesTypeName = DictUtils.getName(opportunityInfo.getTransferProjectInfo().getSalesType(), dcitMap.get(OpportunityConstant.ZTE_OPTY_SALES));
		String endUserTypeName = DictUtils.getName(opportunityInfo.getTransferProjectInfo().getEndUserType(), dcitMap.get(OpportunityConstant.END_USER_TYPE_DICTIONARY));
		String endUseOfEndUserName = DictUtils.getName(opportunityInfo.getTransferProjectInfo().getEndUseOfEndUser(), dcitMap.get(OpportunityConstant.END_USE_OF_END_USER_DICTIONARY));
		//商机来源
		String dataSourceName = DictUtils.getName(opportunityInfo.getOpptyBaseInfo().getDataSource(),dcitMap.get(OpportunityConstant.SOURCE_OF_OPPORTUNITY));
		opportunityInfo.getOpptyBaseInfo().setWinRateName(CommonMapUtil.SUCCESS_TATE_NAME_MAP.getOrDefault(opportunityInfo.getOpptyBaseInfo().getWinRate(), opportunityInfo.getOpptyBaseInfo().getWinRate()));
		opportunityInfo.getOpptyBaseInfo().setTenderTypeName(tenderTypeName);
		opportunityInfo.getOpptyBaseInfo().setProjectPhasesName(projectPhasesName);
		opportunityInfo.getOpptyBaseInfo().setDataSourceName(dataSourceName);
		opportunityInfo.setStatusCdName(OptyStatusEnum.getStatusName(opportunityInfo.getStatusCd(), CommonUtils.getxLangId()));
		opportunityInfo.getTransferProjectInfo().setBusinessTypeName(businessTypeName);
		opportunityInfo.getTransferProjectInfo().setFinalUsageName(finalUsageName);
		opportunityInfo.getTransferProjectInfo().setSalesTypeName(salesTypeName);
		opportunityInfo.getTransferProjectInfo().setEndUserTypeName(endUserTypeName);
		opportunityInfo.getTransferProjectInfo().setEndUseOfEndUserName(endUseOfEndUserName);
	}

	public String getFinalUsageName(String code){
		if(code != null){
			String langId = CommonUtils.getxLangId();
			List<PrmQuickCodeValueVO> quickCodeValueVOList = prmService.getEffectiveAndHistoryByCodeType(FINAL_USAGE_CODE_TYPE);
			if (CollectionUtils.isNotEmpty(quickCodeValueVOList)) {
				for (PrmQuickCodeValueVO vo : quickCodeValueVOList) {
					if (StringUtils.equals(vo.getValueCode(), code)) {
						return CommonConst.ZH_CN.equalsIgnoreCase(langId)? vo.getCodeValueZh() : vo.getCodeValueEn();
					}
				}
			}
		}
		return code;
	}

	private List<OpportunityProduct> getProductInfosForNewOpportunity(String rowId) {
		Map<String,Object> params = Maps.newHashMap();
		params.put("opptyId", rowId);
		params.put("businessTypes", Arrays.asList(NEW_OPPORTUNITY, PDM_PROD));
		return opportunityProductDao.getList(params);
	}


	private List<OpportunityInfo.ProductInfo> getProductInfos(String rowId, boolean isOld){
		List<OpportunityProduct> opportunityProducts = new ArrayList<>();
		if(isOld){
			opportunityProducts = opportunityProductDao.getOldOpportunityProductsByRowId(rowId);
		}else{
			opportunityProducts = getProductInfosForNewOpportunity(rowId);
		}
		Map<String, List<OpportunityProduct>> productMap = opportunityProducts.stream().filter(e -> StringUtils.isNotBlank(e.getBusinessType())).collect(Collectors.groupingBy(OpportunityProduct::getBusinessType));
		List<OpportunityProduct> newProducts = productMap.getOrDefault(NEW_OPPORTUNITY, new ArrayList<>());
		List<OpportunityProduct> pdmProducts = productMap.getOrDefault(PDM_PROD, new ArrayList<>());
		Map<String, OpportunityProduct> pdmProductMap = pdmProducts.stream().filter(e -> StringUtils.isNotBlank(e.getParProdId())).collect(Collectors.toMap(e -> e.getParProdId(), Function.identity(), (k1, k2) -> k1));
		List<OpportunityInfo.ProductInfo> productInfos  = Lists.newArrayList();
		for (OpportunityProduct product : newProducts) {
			OpportunityInfo.ProductInfo productInfo = new OpportunityInfo.ProductInfo();
			BeanUtils.copyProperties(product,productInfo);
			OpportunityProduct pdmProduct = pdmProductMap.getOrDefault(product.getRowId(), new OpportunityProduct());
			OpportunityPdmProductVO pdmProductVO = new OpportunityPdmProductVO();
			BeanUtils.copyProperties(pdmProduct, pdmProductVO);
			productInfo.setPdmProd(pdmProductVO);
			productInfo.setZteMainProduct(pdmProductVO.getZteMainProduct());
			productInfo.setForSignDate(pdmProduct.getForSignDate());
			productInfo.setEnableFlagShow(MapperUtil.transOptionsToName(pdmProduct.getEnableFlag()));
			productInfo.setSuccessRateShow(MapperUtil.transOptionsToName(pdmProduct.getSuccessRateJson()));
			productInfos.add(productInfo);
		}
		return productInfos;
	}


	private OpportunityInfo.ConsumerInfo getConsumerInfo(Opportunity opportunity, OpportunityDetail opportunityDetail) throws Exception {
		OpportunityInfo.ConsumerInfo consumerInfo = new OpportunityInfo.ConsumerInfo();
		BeanUtils.copyProperties(opportunity,consumerInfo);
		BeanUtils.copyProperties(opportunityDetail,consumerInfo);
		String parentTrade = consumerInfo.getFinalCustomerParentTrade();
		String childTrade = consumerInfo.getFinalCustomerChildTrade();
		List<IndustryTreeDataVO> industryTrees = prmService.getIndustryNames(Lists.newArrayList(parentTrade,childTrade), "Y");
		consumerInfo.setFinalCustomerParentTradeName(getIndustryName(parentTrade,industryTrees));
		consumerInfo.setFinalCustomerChildTradeName(getIndustryName(childTrade,industryTrees));
		consumerInfo.setRestrictedParty(opportunityDetail.getFinalCustomerRestrictionFlag());
		return consumerInfo;
	}

	private OpportunityInfo.ConsumerInfo getConsumerInfo(OldOpportunityInfoEntity oldOpportunityInfoEntity) throws Exception {
		OpportunityInfo.ConsumerInfo consumerInfo = new OpportunityInfo.ConsumerInfo();
		BeanUtils.copyProperties(oldOpportunityInfoEntity,consumerInfo);
		AccountInfo accountInfo = customerInfoService.getAccountByCodeOrId(oldOpportunityInfoEntity.getLastAccId());
		consumerInfo.setLastAccName(accountInfo.getAccountName());
		consumerInfo.setLastAccId(StringUtils.isNotBlank(accountInfo.getCustNo())? accountInfo.getCustNo() : oldOpportunityInfoEntity.getLastAccId());
		String parentTrade = consumerInfo.getFinalCustomerParentTrade();
		String childTrade = consumerInfo.getFinalCustomerChildTrade();
		List<IndustryTreeDataVO> industryTrees = prmService.getIndustryNames(Lists.newArrayList(parentTrade,childTrade), "Y");
		consumerInfo.setFinalCustomerParentTradeName(getIndustryName(parentTrade,industryTrees));
		consumerInfo.setFinalCustomerChildTradeName(getIndustryName(childTrade,industryTrees));
		return consumerInfo;
	}


	private OpportunityInfo.OpptyBaseInfo getOpptyBaseInfo(Opportunity opportunity, OpportunityDetail opportunityDetail) {
		OpportunityInfo.OpptyBaseInfo opptyBaseInfo = new OpportunityInfo.OpptyBaseInfo();
		BeanUtils.copyProperties(opportunity, opptyBaseInfo);
		BeanUtils.copyProperties(opportunityDetail, opptyBaseInfo);
		opptyBaseInfo.setDeptName(getOrganizationName(opptyBaseInfo.getDeptNo(), opportunity.getDataSource()));
		return opptyBaseInfo;
	}

	private OpportunityInfo.OpptyBaseInfo getOpptyBaseInfo(OldOpportunityInfoEntity oldOpportunityInfoEntity) {
		OpportunityInfo.OpptyBaseInfo opptyBaseInfo = new OpportunityInfo.OpptyBaseInfo();
		BeanUtils.copyProperties(oldOpportunityInfoEntity, opptyBaseInfo);
		opptyBaseInfo.setDeptName(getOrganizationName(opptyBaseInfo.getDeptNo(), oldOpportunityInfoEntity.getDataSource()));
		return opptyBaseInfo;
	}


	private OpportunityInfo getOpportunityInfo(Opportunity opportunity, OpportunityDetail opportunityDetail) {
		OpportunityInfo opportunityInfo = new OpportunityInfo();
		BeanUtils.copyProperties(opportunity, opportunityInfo);
		opportunityInfo.setBusinessManagerId(opportunityDetail.getBusinessManagerId());
		opportunityInfo.setBusinessManagerName(opportunityDetail.getBusinessManagerName());
		opportunityInfo.setStatusCd(opportunity.getStatusCd());
		return opportunityInfo;
	}

	private String getIndustryName(String industryCode,List<IndustryTreeDataVO> industryTrees) {
		String industryName =industryCode;
		if (CollectionUtils.isNotEmpty(industryTrees)) {
			for(IndustryTreeDataVO industryTree:industryTrees){
				if (StringUtils.equals(industryTree.getLookupCode(), industryCode)) {
					industryName = industryTree.getMeaning();
					break;
				}
			}
		}
		return industryName;
	}

	private String getOrganizationName(String organizationCode, String dataSource) {
		String organizationName = organizationCode;
		try {
			List<OrganizationNode> organizationNodes= prmService.getOrganizationNames(Lists.newArrayList(organizationCode));
			if (CollectionUtils.isNotEmpty(organizationNodes)) {
				OrganizationNode organizationNode = organizationNodes.get(0);
				organizationName = organizationNode.getOrganizationAlias();
			} else {
				List<IHolOrgVO> iHolOrgVOS = commonRemoteUtils.queryOrg(Lists.newArrayList(organizationCode));
				if (CollectionUtils.isEmpty(iHolOrgVOS)) {
					return organizationName;
				}
				Map<String, String> orgMap = iHolOrgVOS.stream().collect(Collectors.toMap(IHolOrgVO::getCode, IHolOrgVO::getName, (k1, k2) -> k1));
				organizationName = orgMap.getOrDefault(organizationCode, organizationCode);
			}
		} catch (Exception e) {
			logger.info(" organization interface error: {}",e.getMessage());
		}
		return organizationName;
	}

	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public int softDeleteDraft(String rowId){
		int i = opportunityDao.softDeleteDraft(rowId);
		if (i > 0) {
			opportunityDetailDao.softDelete(rowId);

			String enableFlag = JSONObject.toJSONString(RadioFieldConverterUtils
					.converterRadioField(com.zte.opty.sync.domain.constants.CommonConstant.TABLE_FIELD_ENABLE_FLAG,
							 com.zte.opty.sync.domain.constants.CommonConstant.N));
			opportunityProductDao.softDeleteByOpptyId(rowId,enableFlag);
		}
		return i;
	}

	/**
	 * 新增指定记录
	 *
	 * @param entity            实体对象
	 * @param opportunityDetail
	 * @return 新增的记录对象, 注意是提交数据库之前的实体对象
	 * <AUTHOR>
	 * @date 2021/09/14
	 */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public Opportunity insert(Opportunity entity, OpportunityDetail opportunityDetail){
		String emp = HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
		if (StringUtils.isNotBlank(HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_TENANT_ID))){
			int tenantId = Integer.parseInt(HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_TENANT_ID));
			entity.setTenantId(tenantId);
		}
		Date now = new Date();
		entity.setCreatedBy(emp);
		entity.setLastUpdBy(emp);
        entity.setCreated(now);
		entity.setLastUpd(now);
        if (null==entity.getRowId()) {
            entity.setRowId(String.valueOf(iKeyIdService.getKeyLongId()));
        }
		sOpportunityRepository.insert(LcapConverterUtil.buildSopty(entity, opportunityDetail));
		return entity;
	}

	/**
	 * 保存或更新商机主表信息
	 *
	 * @param opportunity       商机主表信息
	 * @param opportunityDetail
	 * @return
	 */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public Opportunity insertOrUpdate(Opportunity opportunity, OpportunityDetail opportunityDetail) throws Exception {
		if(null == opportunity){
			return new Opportunity();
		}
		List<IHolOrgVO> organizationNames = commonRemoteUtils.queryOrg(Collections.singletonList(opportunityDetail.getDeptNo()));
		Map<String, String> orgMap = organizationNames.stream().collect(Collectors.toMap(IHolOrgVO::getCode, IHolOrgVO::getCodeFullPath, (e1, e2) -> e1));
		opportunity.setOrgNamePath(orgMap.getOrDefault(opportunityDetail.getDeptNo(), opportunityDetail.getDeptNo()));
		Opportunity opportunityTemp = opportunityDao.get(opportunity.getRowId());
		if(null != opportunityTemp){
			opportunity.setOptyCd(opportunityTemp.getOptyCd());
			opportunity.setCreated(opportunityTemp.getCreated());
			opportunity.setCreatedBy(opportunityTemp.getCreatedBy());
			opportunity.setEnabledFlag(opportunityTemp.getEnabledFlag());
			opportunity.setBusinessTypeCd(CommonMapUtil.DELIVERY_INTERFACE_MAP.inverse().getOrDefault(opportunity.getBusinessTypeCd(), opportunity.getBusinessTypeCd()));
			return updateAll(opportunity, opportunityDetail);
		}
		return insert(opportunity, opportunityDetail);
	}

	/**
	 * 修改指定记录
	 * @param entity 实体对象
	 * @return 修改的记录对象,注意是提交数据库之前的实体对象
	 * <AUTHOR>
     * @date 2021/09/14
     */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public Opportunity update(Opportunity entity){
		String emp = HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
		entity.setLastUpdBy(emp);
		entity.setLastUpd(new Date());
		sOpportunityRepository.updateById(LcapConverterUtil.buildSopty(entity, null));
		return entity;
	}

	@Override
	public void updateSubmitDate(String rowId, Date submitDate){
		Opportunity opportunity =  new Opportunity();
		opportunity.setRowId(rowId);
		opportunity.setSubmitDate(submitDate);
		updateOnTheQuiet(opportunity);
	}

	@Override
	public int updateCurrentStatus(String rowId, OpportunityCurrentStatus currentStatus) {
		if (null == currentStatus){
			return 0;
		}
		Opportunity entity = new Opportunity();
		String emp = HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
		entity.setLastUpdBy(emp);
		entity.setLastUpd(new Date());
		entity.setCurrentStatus(currentStatus.getValue());
		entity.setRowId(rowId);
		if (OpportunityCurrentStatus.BID_LOSS == currentStatus) {
			// 商机当前状态维护为丢标后，商机状态直接更新为：丢单
			entity.setStatusCd(OptyStatusEnum.TICKET_LOSS.getCode());
		} else if (OpportunityCurrentStatus.CANCEL == currentStatus) {
			// 商机当前状态维护为取消后，商机状态直接更新为：取消
			entity.setStatusCd(OptyStatusEnum.OPTY_CLOSED.getCode());
		}

		return sOpportunityRepository.updateById(LcapConverterUtil.buildSopty(entity, null));
	}

	/**
	 * 修改指定记录
	 * @param entity 实体对象
	 * @return 修改的记录对象,注意是提交数据库之前的实体对象
	 * <AUTHOR>
	 * @date 2021/09/14
	 */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public Opportunity updateOnTheQuiet(Opportunity entity){
		sOpportunityRepository.update(Wrappers.lambdaUpdate(SOptyBO.class).eq(SOptyBO::getId,entity.getRowId()).set(SOptyBO::getSubmitDate,entity.getSubmitDate()));
		return entity;
	}

	/**
	 * 商机主表全量更新
	 *
	 * @param entity            实体对象
	 * @param opportunityDetail
	 * @return 修改的记录对象, 注意是提交数据库之前的实体对象
	 * <AUTHOR>
	 * @date 2021/09/14
	 */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public Opportunity updateAll(Opportunity entity, OpportunityDetail opportunityDetail){
		String emp = HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
		entity.setLastUpdBy(emp);
		entity.setLastUpd(new Date());
		if(StringUtils.isEmpty(entity.getEnabledFlag())){
			entity.setEnabledFlag("Y");
		}
		sOpportunityRepository.updateById(LcapConverterUtil.buildSopty(entity, opportunityDetail));
		return entity;
	}


	/**
	 * 获取符合条件的实体列表,按指定属性排序
	 * @param map 参数集合
	 * @return 实体集合
	 * <AUTHOR>
     * @date 2021/09/14
     */
	@Override
	public List<Opportunity> getList(Map<String, Object> map){
		return opportunityDao.getList(map);
	}

	/**
	 * 统计
	 * @param map 参数集合
	 * @return 统计总数
	 * <AUTHOR>
     * @date 2021/09/14
     */
	@Override
	public long getCount(Map<String, Object> map){
        return opportunityDao.getCount(map);
	}

	/**
	 * @param form
	 * @param isExport 是否导出
	 * @return
	 */
	@Override
	public PageRows<OpportunityVO> getOpportunityVoPageRows(FormData<OpportunityQueryDTO> form, Boolean isExport) throws Exception {
		Map<String,Object> map = getQueryParamsMap(form, isExport);
		PageRows<OpportunityVO> page = new PageRows<>();
		if(!(boolean) map.get(OpportunityConstant.IS_PERMISSION)){
			return emptyPage(page);
		}
		boolean manageAuthFlag = (boolean) map.get(OpportunityConstant.MANAGE_AUTH_FLAG);
		long total = getCount(map);
		List<OpportunityVO> list = total>0 ? getPage(map, manageAuthFlag) : Collections.emptyList();
		page.setCurrent(form.getPage());
		page.setTotal(total);
		page.setRows(list);
		return page;
	}

	/**
	 * 获取当前月还未更新月报的商机列表
	 *
	 * @param form
	 * @param empNo           工号
	 * @return
	 */
	@Override
	public PageRows<OpportunityVO> getMonthReportOpportunity(FormData<OpportunityQueryDTO> form, String empNo) {
		Map<String,Object> map = getQueryParamsMap(form, false);
		PageRows<OpportunityVO> page = new PageRows<>();
		if(!(boolean) map.get(OpportunityConstant.IS_PERMISSION)){
			return emptyPage(page);
		}
		PageMethod.startPage(FormDataHelpUtil.getPageNum(form), FormDataHelpUtil.getPageSize(form));
		List<OpportunityVO> monthReportOpportunity = opportunityDao.getMonthReportOpportunity(map);
		if (CollectionUtils.isNotEmpty(monthReportOpportunity)) {
			for (OpportunityVO opportunityVO : monthReportOpportunity) {
				opportunityVO.setTendTypeCode(CommonMapUtil.BIDDING_TYPE_MAP.inverse().getOrDefault(opportunityVO.getTendTypeCode(), opportunityVO.getTendTypeCode()));
			}
		}
		PageInfo<OpportunityVO> pageInfo = new PageInfo<>(monthReportOpportunity);
		page.setTotal(pageInfo.getTotal());
		page.setRows(monthReportOpportunity);
		page.setCurrent(FormDataHelpUtil.getPageNum(form));
		return page;
	}

	@Override
	public Map<String,Object> getTotalOpportunity(String crmCustomerCode){
		Map<String,Object> map = new HashMap<>();
		map.put("crmCustomerCode", crmCustomerCode);
		long total = 0;
		if(StringUtils.isNotBlank(crmCustomerCode)) {
			total = getCount(map);
		}
		map.put("total", total);
		return map;
	}

	private Map<String,Object> getQueryParamsMap(FormData<OpportunityQueryDTO> form, Boolean isExport){
		Map<String,Object> map = FormDataHelpUtil.getPageQueryMap(form);
		map.put(OpportunityConstant.IS_EXPORT,isExport);
		// 查询账号角色权限
		Set<String> roleCodes = channelAuthService.getRoleCodes();
		map.put(OpportunityConstant.IS_PERMISSION, isPermission(map,roleCodes));
		boolean manageAuthFlag = Boolean.FALSE;
		if (roleCodes.contains(OpportunityConstant.COMPANY_ACCOUNT)){
			// 管理员权限
			manageAuthFlag = Boolean.TRUE;
		}
		map.put(OpportunityConstant.MANAGE_AUTH_FLAG, manageAuthFlag);
		String empNo = CommonUtils.getEmpNo();
		map.put("empNo", empNo);
		// 当前月的月报归属期
		SimpleDateFormat parser = new SimpleDateFormat(OpportunityConstant.DATE_FORMAT_YYYYMM);
		String reportMonth = parser.format(new Date());
		map.put("month", reportMonth);
		// 处理参数
		setParamMap(form, map);
		return map;
	}

	/**
	 * ichannel 商机导出
	 *
	 * @param form
	 * @param response
	 */
	@Override
	public void exportOpportunityList(FormData<OpportunityQueryDTO> form, HttpServletResponse response) throws Exception {
		List<OpportunityVO> rows = getOpportunitiesAccordingToCondition(form);
		if (CollectionUtils.isEmpty(rows)) {
			writeDataToExcel(response, Lists.newArrayList());
			return;
		}
		List<ExportOppotrunityVO> exportOpportunities = getExportOpportunityVOS(rows);
		writeDataToExcel(response, exportOpportunities);
	}

	private List<ExportOppotrunityVO> getExportOpportunityVOS(List<OpportunityVO> rows) {
		List<ExportOppotrunityVO> exportOppotrunitys = new ArrayList<>();
		// 从行业树获取行业列表
		List<AuthConstraintDTO> subIndustryList = prmService.getSubIndustryListWithNoException("Y");
		Map<String, String> mapSubIndustry = subIndustryList.stream().collect(Collectors.toMap(AuthConstraintDTO::getShowValue, AuthConstraintDTO::getShowName));
		rows.stream().filter(Objects::nonNull).forEach(x -> {
			ExportOppotrunityVO exportOppotrunityVO = new ExportOppotrunityVO();
			BeanUtils.copyProperties(x, exportOppotrunityVO);
			exportOppotrunitys.add(exportOppotrunityVO);
			exportOppotrunityVO.setOpportunityName(x.getAttrib46());
			exportOppotrunityVO.setProductNames(x.getProdLv2Name());
			exportOppotrunityVO.setStatusName(x.getStatusCdName());
			exportOppotrunityVO.setEstimatedBiddingTime(DateFormatUtil.dateFormat(x.getEstimatedBiddingTime()));
			exportOppotrunityVO.setBiddingDeadline(DateFormatUtil.dateFormat(x.getBiddingDeadline()));
			// 设置最终用户行业字段中文名称
			exportOppotrunityVO.setFinalCustomerTradeName(mapSubIndustry.getOrDefault(x.getFinalCustomerTradeChildCode(), x.getFinalCustomerTradeChildCode()));
		});
		return exportOppotrunitys;
	}

	private List<OpportunityVO> getOpportunitiesAccordingToCondition(FormData<OpportunityQueryDTO> form) throws Exception {
		form.setRows(OpportunityConstant.EXPORT_MAX_ROW);
		PageRows<OpportunityVO> pageRows = getOpportunityVoPageRows(form, Boolean.TRUE);
		List<OpportunityVO> rows = pageRows.getRows();
		return rows;
	}

	private void writeDataToExcel(HttpServletResponse response, List<ExportOppotrunityVO> exportOppotrunitys) throws Exception {
		List<ComDictionaryMaintainVO> dictionaryMaintains = comDictionaryMaintainService.queryByType(ComDictionaryMaintainConsts.ICHANNEL_EXPORT_OPPORTUNITY_TITLE);
		LinkedHashMap<String, String> titleCodeToName = new LinkedHashMap<>();
		String langId = CommonUtils.getxLangId();
		boolean isEn = CommonConst.EN_US.equalsIgnoreCase(langId);
		dictionaryMaintains.stream().forEach(x -> {
			titleCodeToName.put(x.getCode(), isEn ? x.getEnglishName() : x.getChineseName());
		});
		ExportExcel exportExcel = new ExportListObjectExcel<>(titleCodeToName, exportOppotrunitys, LocalMessageUtils.getMessage("business.opportunity.export") + System.currentTimeMillis());
		ExportExcelHandle excelHandle = new ExportExcelHandle(exportExcel);
		response.setCharacterEncoding("UTF-8");
		response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
		excelHandle.process(response);
	}


	/**
	 * 校验是否具有查看详情权限
	 *
	 * @param rowId
	 * @return
	 */
	@Override
	public Boolean checkDetailReviewPermission(String rowId) {
		Boolean result = Boolean.FALSE;
		String accountId= CommonUtils.getEmpNo();
		if(StringUtils.isBlank(accountId)){
			return Boolean.FALSE;
		}
		String crmCustomerCode = null;
		try {
			ChannelAccountDetailDTO res = prmService.getChannelAccountBasicInfo(accountId);
			crmCustomerCode = res.getCrmCustomerCode();
		}catch (Exception e){
			logger.error("查询渠道商帐号基本信息报错,accountId:{}, rowId:{}", accountId, rowId, e);
			return Boolean.FALSE;
		}
		OpportunityQueryDTO opportunityQueryDTO = new OpportunityQueryDTO();
		opportunityQueryDTO.setCrmCustomerCode(crmCustomerCode);
		opportunityQueryDTO.setRowId(rowId);
		FormData<OpportunityQueryDTO> form = new FormData<>();
		form.setBo(opportunityQueryDTO);

		Map<String, Object> paramMap = getQueryParamsMap(form, false);
		// 查询账号角色权限
		if(!(boolean) paramMap.get(OpportunityConstant.IS_PERMISSION)){
			return Boolean.FALSE;
		}
		List<OpportunityVO> opportunityVOs = opportunityDao.getPage(paramMap);
		if(CollectionUtils.isNotEmpty(opportunityVOs)){
			result = Boolean.TRUE;
			for (OpportunityVO opportunityVO : opportunityVOs) {
				opportunityVO.setTendTypeCode(CommonMapUtil.BIDDING_TYPE_MAP.inverse().getOrDefault(opportunityVO.getTendTypeCode(), opportunityVO.getTendTypeCode()));
			}
		}
		return result;
	}

	private boolean isPermission(Map<String,Object> map ,Set<String> roleCodes ) {
		if (!map.containsKey(OpportunityConstant.CRM_CUSTOMER_CODE) || Objects.isNull(map.get(OpportunityConstant.CRM_CUSTOMER_CODE))){
			return false;
		}
		if (CollectionUtils.isEmpty(roleCodes)){
			// 无权限
			return false;
		}
		if (!roleCodes.contains(OpportunityConstant.COMPANY_ACCOUNT)
				&& !roleCodes.contains(OpportunityConstant.BUSINESS_MANAGER)
				&& !roleCodes.contains(OpportunityConstant.SERVICE_MANAGER)){
			// 无权限
			return false;
		}
		return true;
	}

	private PageRows<OpportunityVO>  emptyPage(PageRows<OpportunityVO> page){
		page.setTotal(0);
		page.setRows(Collections.emptyList());
		return page;
	}


	private void setParamMap(FormData<OpportunityQueryDTO>  form, Map<String,Object> map) {
		if (map.containsKey(OpportunityConstant.KEYWORD) && Objects.nonNull(map.get(OpportunityConstant.KEYWORD))){
			map.put(OpportunityConstant.KEYWORD, map.get(OpportunityConstant.KEYWORD).toString().trim());
		}
		if (null == form.getBo()) {
			return;
		}
		// 处理商机来源
		List<String> dataSources = form.getBo().getDataSources();
		processingDataSources(dataSources, map);
		// 处理月报状态
		List<String> reportStatus = form.getBo().getStatusReports();
		processingReportStatus(reportStatus, map);

		List<String> statusCd = form.getBo().getStatusCds();
		// 老商机的closed状态对应新商机的报备失效
		if(CollectionUtils.isNotEmpty(statusCd)){
			List<String> oldStatusCd = statusCd.stream().filter(s -> !s.equalsIgnoreCase(OptyStatusEnum.TICKET_LOSS.getCode())).map(s -> {
				if (s.equalsIgnoreCase(OptyStatusEnum.OPTY_SUSPEND.getCode())){
					return OptyStatusEnum.TICKET_LOSS.getCode();
				}
				return s;
			}).collect(Collectors.toList());

			map.put(OLD_STATUS_CD, oldStatusCd);
		}
	}

	/**
	 * 处理商机来源到map中
	 * @param dataSources 商机来源列表
	 * @param map 查询map
	 */
	private void processingDataSources(List<String> dataSources, Map<String, Object> map) {
		if (CollectionUtils.isEmpty(dataSources) || (dataSources.size() == OpportunityConstant.SOURCE_NUM)) {
			map.put(OpportunityConstant.SOURCE_TYPE, "");
		} else if (dataSources.size() == CommonConstant.ONE) {
			map.put(OpportunityConstant.SOURCE_TYPE, dataSources.get(0));
		}
	}

	/**
	 * 处理月报状态到map中
	 * @param reportStatus 月报状态列表
	 * @param map 查询map
	 */
	private void processingReportStatus(List<String> reportStatus, Map<String, Object> map) {
		if (CollectionUtils.isEmpty(reportStatus) || (reportStatus.size() == OpportunityConstant.REPORT_STATUS_NUM)) {
			map.put(OpportunityConstant.REPORT_STATUS_TYPE, "");
		} else if (reportStatus.size() == CommonConstant.ONE) {
			map.put(OpportunityConstant.REPORT_STATUS_TYPE, reportStatus.get(0));
		}
	}

	/**
	 * 分页查询商机列表
	 * @param map 参数集合
	 * <AUTHOR>
     * @date 2021/09/14
     */
	@Override
	public List<OpportunityVO> getPage(Map<String, Object> map, boolean manageAuthFlag) throws Exception {
		List<OpportunityVO> opportunityVOS = opportunityDao.getPage(map);
		if (CollectionUtils.isNotEmpty(opportunityVOS)){
			for (OpportunityVO opportunityVO : opportunityVOS) {
				opportunityVO.setTendTypeCode(CommonMapUtil.BIDDING_TYPE_MAP.inverse().getOrDefault(opportunityVO.getTendTypeCode(), opportunityVO.getTendTypeCode()));
			}
			// 获取字典类型值
			Map<String, List<ComDictionaryMaintainVO>> mapList = comDictionaryMaintainService.queryByTypeList(OpportunityConstant.OPPORTUNITY_TYPES);
			Boolean isExport =null==map?Boolean.FALSE:(Boolean) map.getOrDefault(OpportunityConstant.IS_EXPORT, Boolean.FALSE);
			// 填充字典类型值
			setDictionaryAndButton(mapList, opportunityVOS, manageAuthFlag,isExport);
		}
		return opportunityVOS;
	}

	/**
	 * 撤销商机
	 * @param rowId
	 * @param systemSource
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public String cancelOpportunity(String rowId, String systemSource) {
		//权限校验
		boolean verifyAuthResult = verifyAuth(rowId,systemSource);
		if(!verifyAuthResult)
		{
			logger.error("撤销商机权限校验不通过,rowId:{}", rowId);
			throw new BusinessRuntimeException(PrmRetCode.PERMISSIONDENIED_CODE, PrmRetCode.PERMISSIONDENIED_MSGID);
		}
		logger.error("撤销商机,rowId:{}", rowId);
		//查询全部审批人
		List<String> approverList = getApproverList(rowId);
		String empNO = CommonUtils.getEmpNo();
		//注销流程
		String flowInstanceId = opportunityDao.getFlowInstanceIdByBusinessId(rowId);
		if (StringUtils.isNotBlank(flowInstanceId)) {
			ApprovalRevokeParamsDTO revokeParamsDTO = new ApprovalRevokeParamsDTO();
			revokeParamsDTO.setBusinessId(rowId);
			revokeParamsDTO.setFlowInstanceId(flowInstanceId);
			revokeParamsDTO.setFlowCode(NEW_OPPORTUNITY_FLOW_CODE);
			revokeParamsDTO.setOpinion("revoke");
			approvalFlowService.revokeFlow(revokeParamsDTO);
			logger.error("撤销商机,失效流程记录,rowId:{}", rowId);
			//失效流程记录
			ComApprovalRecord approvalRecord = new ComApprovalRecord();
			approvalRecord.setLastUpdatedBy(empNO);
			approvalRecord.setWorkFlowInstanceId(flowInstanceId);
			approvalRecord.setBusinessId(rowId);
			opportunityDao.invalidApprovalRecord(approvalRecord);
		}
		//修改单据状态为“草稿”
		logger.error("撤销商机,修改单据状态为“草稿”,rowId:{}", rowId);
		Opportunity opty = new Opportunity();
		opty.setLastUpdBy(empNO);
		opty.setStatusCd(OptyStatusEnum.DRAFT.getCode());
		Date currentDate = new Date(System.currentTimeMillis());
		opty.setLastUpd(currentDate);
		opty.setRowId(rowId);
		sOpportunityRepository.updateById(LcapConverterUtil.buildSopty(opty, null));

		//发送邮件
		sendMail(rowId,approverList,OpportunityConstant.OPPORTUNITY_EMAIL_FOR_CANCEL_OPPTY, TEMPLATE_TYPE_NOTIFICATION);

		return "success";
	}

	@Override
	public void sendMail(String rowId, List<String> receivers, String modelCode, String modelType) {
		String receiverStr = getReceiverStr(rowId, receivers);
		// 发送邮件
		sendMail(rowId, receiverStr, modelCode, modelType);
	}

	private String getReceiverStr(String rowId, List<String> receivers) {
		//调用用户中心接口查询邮箱
		String receiverStr;
		ServiceData<List<UcsUserInfoDTO>> listServiceData = ichannelBaseFeign.queryAll(receivers);
		List<UcsUserInfoDTO> ucsUserInfoList = listServiceData.getBo();
		if (CollectionUtils.isEmpty(ucsUserInfoList)) {
			logger.info("调用用户中心接口查询邮箱返回为空,rowId:{}, receivers:{}", rowId, receivers);
			return null;
		}
		receiverStr = ucsUserInfoList.stream().map(UcsUserInfoDTO::getEmail).collect(Collectors.joining(","));
		logger.error("调用用户中心接口查询邮箱,rowId:{},receivers:{},receiverStr:{}", rowId, receivers, receiverStr);
		return receiverStr;
	}

	@Override
	public void sendMailWithOpinion(SendEmailPreDto sendEmailPreDto) {
		String receiverStr = getReceiverStr(sendEmailPreDto.getRowId(), sendEmailPreDto.getReceivers());
		//发送邮件
		try{
			if(StringUtils.isBlank(receiverStr))
			{
				logger.error("邮件接收人为空,rowId:{}, modelCode:{}", sendEmailPreDto.getRowId(),
							 sendEmailPreDto.getModelCode());
				return;
			}
			OpportunityMailEntity opportunityMailEntity = getOpptyMailEntity(sendEmailPreDto.getRowId());
			logger.info("商机邮件发送,opportunityMailEntity转换后:{}", opportunityMailEntity);
			opportunityMailEntity.setModelCode(sendEmailPreDto.getModelCode());
			opportunityMailEntity.setModelType(sendEmailPreDto.getModelType());
			opportunityMailEntity.setMailTo(receiverStr);
			opportunityMailEntity.setReceiverIds(Joiner.on(",").skipNulls().join(sendEmailPreDto.getReceivers()));
			opportunityMailEntity.setOpinion(sendEmailPreDto.getOpinion());
			sendMailService.sendOptyMail(opportunityMailEntity);
		}
		catch (Exception e)
		{
			String logContent = "rowId: " + sendEmailPreDto.getRowId() + " receiver: " + receiverStr + " modelCode: " +
								sendEmailPreDto.getModelCode() + " modelType: " +
								sendEmailPreDto.getModelType();
			loggerService.saveLogger(logContent, OpportunityConstant.SEND_EMAIL_LOG_TIP);
			logger.error("发送邮件异常,rowId:{}, receiver:{}", sendEmailPreDto.getRowId(), receiverStr, e);
		}
	}

	@Override
	public OpportunityMailEntity getOpptyMailEntity(String rowId) throws Exception {
		OpportunityMailEntity opportunityMailEntity = opportunityDetailDao.getOpportunityMailEntityByRowId(rowId);
		logger.info("商机邮件发送,opportunityMailEntity转换前:{}", opportunityMailEntity);
		//查询行业名称
		List<IndustryTreeDataVO> industryList = prmService.getIndustryNames(Arrays.asList(opportunityMailEntity.getFinalCustomerChildTrade(),
				opportunityMailEntity.getFinalCustomerParentTrade()), "Y");
		Map<String, String> industryMap = new HashMap<>();
		if (industryList != null && industryList.size() > 0) {
			industryMap = industryList.stream().collect(Collectors.toMap(IndustryTreeDataVO::getLookupCode, IndustryTreeDataVO::getMeaning));
		}
		//查询部门名称
		List<IHolOrgVO> orgVOList = commonRemoteUtils.queryOrg(Collections.singletonList(opportunityMailEntity.getDeptNo()));
		Map<String, String> organizationMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(orgVOList)) {
			organizationMap = orgVOList.stream().collect(Collectors.toMap(IHolOrgVO::getCode, IHolOrgVO::getName));
		}
		String deptName = organizationMap.getOrDefault(opportunityMailEntity.getDeptNo(), opportunityMailEntity.getDeptNo());
		opportunityMailEntity.setDeptName(deptName);
		String parentIndustryName = industryMap.getOrDefault(opportunityMailEntity.getFinalCustomerParentTrade(), opportunityMailEntity.getFinalCustomerParentTrade());
		String childIndustryName = industryMap.getOrDefault(opportunityMailEntity.getFinalCustomerChildTrade(), opportunityMailEntity.getFinalCustomerChildTrade());
		opportunityMailEntity.setFinalCustomerTradeName(parentIndustryName + CommonConstant.MID_LINE + childIndustryName);
		return opportunityMailEntity;
	}

	@Override
	public void sendMail(String rowId, String receiver, String modelCode,String modelType)
	{
		try{
			if(StringUtils.isBlank(receiver))
			{
				logger.error("邮件接收人为空,rowId:{}, modelCode:{}", rowId, modelCode);
				return;
			}
			OpportunityMailEntity opportunityMailEntity = getOpptyMailEntity(rowId);
			logger.info("商机邮件发送,opportunityMailEntity转换后:{}", opportunityMailEntity);
			opportunityMailEntity.setModelCode(modelCode);
			opportunityMailEntity.setModelType(modelType);
			opportunityMailEntity.setMailTo(receiver);
			opportunityMailEntity.setReceiverIds(receiver);
			sendMailService.sendOptyMail(opportunityMailEntity);
		}
		catch (Exception e)
		{
			String logContent = "rowId: " + rowId + " receiver: " + receiver + " modelCode: " + modelCode + " modelType: " + modelType;
			loggerService.saveLogger(logContent, OpportunityConstant.SEND_EMAIL_LOG_TIP);
			logger.error("发送邮件异常,rowId:{}, receiver:{}",rowId, receiver, e);
		}
	}

	@Override
	public List<String> getApproverList(String rowId)
	{
		//查询流程节点审批人
		List<String> approverList = new ArrayList<>();

		List<String> businessIdList = new ArrayList<>();
		businessIdList.add(rowId);

		List<String> flowCodeList = new ArrayList<>();
		flowCodeList.add(NEW_OPPORTUNITY_FLOW_CODE);

		ApprovalActiveTaskParamsDTO approvalParam = new ApprovalActiveTaskParamsDTO();
		approvalParam.setAppCode(null);
		approvalParam.setBusinessIds(businessIdList);
		approvalParam.setFlowCodes(flowCodeList);
		approvalParam.setPageNo(OpportunityConstant.ONE);
		approvalParam.setPageSize(OpportunityConstant.NUMBER_FIVE_HUNDRED);
		PageRows<FlowActiveTaskInfo> pageRows = approvalFlowService.getApprovingTaskList(approvalParam);
		logger.info("[getApproverList],approvalParam:{},return:{}", approvalParam, JSON.toJSONString(pageRows));
		if(pageRows!=null && pageRows.getTotal()>0)
		{
			List<FlowActiveTaskInfo> flowList = pageRows.getRows();
			if(flowList!=null)
			{
				FlowActiveTaskInfo activeTaskInfo = flowList.get(0);
				List<ApprovalTaskInfo> taskInfoList = activeTaskInfo.getActiveTasks();
				if(taskInfoList!=null&&taskInfoList.size()>0)
				{
					for(ApprovalTaskInfo task:taskInfoList)
					{
						approverList.add(task.getApprover());
					}
				}
			}
		}
		//查询中兴业务经理
		String businessManagerNo = opportunityDao.getBusinessManagerIdByRowId(rowId);
		if(StringUtils.isNotBlank(businessManagerNo))
		{
			approverList.add(businessManagerNo);
		}
		return approverList;
	}

	/**
	 * 撤销商机的权限校验
	 * @param rowId
	 * @param systemSource
	 * @return
	 */
	@Override
	public boolean verifyAuth(String rowId, String systemSource)
	{
		//查询当前登录人是否就是该单据创建人
		String empNO = CommonUtils.getEmpNo();
		Opportunity opportunity = opportunityDao.get(rowId);
		if(opportunity==null)
		{
			return false;
		}
		opportunity.setBusinessTypeCd(CommonMapUtil.DELIVERY_INTERFACE_MAP.inverse().getOrDefault(opportunity.getBusinessTypeCd(), opportunity.getBusinessTypeCd()));
		String createdBy = opportunity.getCreatedBy();
		if(StringUtils.equals(empNO, createdBy))
		{
			return true;
		}
		//查询当前登录人的角色权限
		Set<String> roleCodes = new HashSet<>();
		if (OpportunityConstant.PRM.equals(systemSource)) {
			roleCodes = salesOpportunitiesAuthService.getRoleCodes();
		}else if (OpportunityConstant.ICHANNEL.equals(systemSource)){
			roleCodes = channelAuthService.getRoleCodes();
		}
		if (CollectionUtils.isEmpty(roleCodes)){
			//没有权限撤销单据
			return false;
		}
		if(OpportunityConstant.ICHANNEL.equals(systemSource)&&roleCodes.contains(OpportunityConstant.COMPANY_ACCOUNT))
		{
			return true;
		}
		else if (OpportunityConstant.PRM.equals(systemSource)&& roleCodes.contains(OpportunityRoleEnum.GEC_OPPORTUNITY_ADMIN.getCode()))
		{
			return true;
		}
		return false;

	}

	@Override
	public String urgeOpportunity(String rowId){
		ComApprovalRecord comApprovalRecord = comApprovalRecordDao.getByBusinessId(rowId);
		//如果流程Id存在场景
		if (comApprovalRecord != null && StringUtils.isNotBlank(comApprovalRecord.getWorkFlowInstanceId())){
			FlowTaskParameter flowTaskParameter = new FlowTaskParameter();
			flowTaskParameter.setFlowInstanceId(comApprovalRecord.getWorkFlowInstanceId());
				approvalFlowService.urgeTask(flowTaskParameter);
			return "success";
		}
		//查询商机信息
		OpportunityDetail opportunityDetail = opportunityDetailDao.get(rowId);
		Assert.notNull(opportunityDetail, LocalMessageUtils.getMessage("services.are.abnormal"));
		CreateCustomerParamVO createCustomerParamVO = new CreateCustomerParamVO();
		createCustomerParamVO.setRowId(rowId);
		createCustomerParamVO.setBusinessManagerId(opportunityDetail.getBusinessManagerId());
		opportunityInfoServiceImpl.sendCreateCustomerDraftEmail(createCustomerParamVO);
		return "success";
	}
	@Override
	public String activateOpportunity(String rowId, String systemSource) {
		//权限检查
		boolean verifyAuthResult = verifyAuth(rowId,systemSource);
		if(!verifyAuthResult)
		{
			throw new BusinessRuntimeException(PrmRetCode.PERMISSIONDENIED_CODE, PrmRetCode.PERMISSIONDENIED_MSGID);
		}
		//激活次数和商机状态校验
		OpportunityDetail opportunityDetail = opportunityDetailDao.get(rowId);
		// accId渲染accCode保持的一致
		opportunityDetail.setLastAccId(opportunityDetail.getLastAccCode());

		int activeCount = null != opportunityDetail.getActiveCount()?opportunityDetail.getActiveCount():0;
		//1个商机只能激活2次
		if(activeCount >= OpportunityConstant.MAX_ACTIVE_NUM)
		{
			//激活次数已达上限
			throw new BusinessRuntimeException(LocalMessageUtils.getMessage("opportunity.activation.activationTimesFull"));
		}
		Opportunity opportunity = opportunityDao.get(rowId);
		if(null == opportunity){
			throw new BusiException(RetCode.BUSINESSERROR_CODE, LocalMessageUtils.getMessage("opportunity.activation.rowIdNotExisted"));
		}
		if(!OptyStatusEnum.OPTY_SUSPEND.getCode().equals(opportunity.getStatusCd()))
		{
			//状态非报备失效，不能激活
			throw new BusinessRuntimeException(LocalMessageUtils.getMessage("opportunity.activation.statusError"));
		}
		opportunity.setBusinessTypeCd(CommonMapUtil.DELIVERY_INTERFACE_MAP.inverse().getOrDefault(opportunity.getBusinessTypeCd(), opportunity.getBusinessTypeCd()));
		Map<String, Object> map = new HashMap<>(2);
		map.put("opptyId",rowId);
		map.put("businessType",OpportunityConstant.NEW_OPPORTUNITY);
		List<OpportunityProduct> productList = opportunityProductDao.getList(map);
		productList.forEach(opportunityProduct -> {
			opportunityProduct.setOpptyId("");
			opportunityProduct.setRowId("");
		});
		//填充新商机参数，保存成草稿
 		OpportunityInfoDTO opportunityInfoDTO = new OpportunityInfoDTO();
		opportunityDetail.setRowId("");
		opportunityDetail.setFromActiveFlag("Y");
		opportunityDetail.setFromActiveOpty(rowId);
		opportunityDetail.setActiveCount(0);
		opportunityDetail.setActivatedBy(CommonUtils.getEmpNo());
		opportunityInfoDTO.setOpportunityDetail(opportunityDetail);
		opportunity.setRowId("");
		opportunityInfoDTO.setOpportunity(opportunity);
		opportunityInfoDTO.setOpportunityProducts(productList);
		OpportunityInfoDTO optyResult = null;
		try {
			optyResult = opportunityInfoService.storageOpportunityInfo(opportunityInfoDTO);
		} catch (Exception e) {
			logger.error("[IOpportunityServiceImpl.activateOpportunity]保存商机草稿异常,被激活商机id:{}", rowId, e);
			throw new BusiException(RetCode.BUSINESSERROR_CODE, LocalMessageUtils.getMessage("opportunity.activate.error"));
		}
		if(optyResult!=null)
		{
			//修改原商机的激活次数
			OpportunityDetail oldOpty = new OpportunityDetail();
			oldOpty.setRowId(rowId);
			activeCount += 1;
			oldOpty.setActiveCount(activeCount);
			oldOpty.setLastUpdBy(CommonUtils.getEmpNo());
			oldOpty.setLastUpd(new Date());
//			opportunityDetailDao.update(oldOpty);

			//修改政企融合
			SOptyXBO sOptyXBO = LcapConverterUtil.buildSoptyx(oldOpty);
			sOptyXDao.updateById(sOptyXBO);

			return optyResult.getOpportunity().getRowId();
		}
		return null;
	}

	private void setDictionaryAndButton(Map<String, List<ComDictionaryMaintainVO>> mapList, List<OpportunityVO> opportunityVOS,
										boolean manageAuthFlag, Boolean isExport) throws Exception {
		List<ComDictionaryMaintainVO> dataSourceList = mapList.get(OpportunityConstant.SOURCE_OF_OPPORTUNITY);
		List<ComDictionaryMaintainVO> statusCdList = mapList.get(OpportunityConstant.OPPORTUNITY_STATUS);
		List<ComDictionaryMaintainVO> statusReportList = mapList.get(OpportunityConstant.MONTHLY_REPORT_STATUS);
		List<ComDictionaryMaintainVO> statusAuthList = mapList.get(OpportunityConstant.AUTHORIZATION_STATUS);
		List<String> deptNos = opportunityVOS.stream().map(OpportunityVO::getDeptNo).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
		// 根据新组织(ORG打头)编码去HR查询组织信息
		List<PersonAndOrgInfoVO> orgList = PersonAndOrgInfoUtil.getOrgAndChildCompanyInfo(deptNos);

		Map<String, String> mapDataSource = dataSourceList.stream().collect(Collectors.toMap(ComDictionaryMaintainVO::getCode, ComDictionaryMaintainVO::getChineseName));
		Map<String, String> mapStatusCd = statusCdList.stream().collect(Collectors.toMap(ComDictionaryMaintainVO::getCode, ComDictionaryMaintainVO::getChineseName));
		Map<String, String> mapStatusReport = statusReportList.stream().collect(Collectors.toMap(ComDictionaryMaintainVO::getCode, ComDictionaryMaintainVO::getChineseName));
		Map<String, String> mapStatusAuth = statusAuthList.stream().collect(Collectors.toMap(ComDictionaryMaintainVO::getCode, ComDictionaryMaintainVO::getChineseName));
		Map<String, String> mapOrganizationNames = orgList.stream().filter(Objects::nonNull)
				.collect(Collectors.toMap(PersonAndOrgInfoVO::getHrOrgID, PersonAndOrgInfoVO::getHrOrgName));
		// 赢率

		List<ComDictionaryMaintainVO> winRateTypeList = Optional.ofNullable(mapList.get(OpportunityConstant.WIN_RATE_TYPE)).orElse(Collections.emptyList());
		boolean isZh = CommonConst.ZH_CN.equalsIgnoreCase(CommonUtils.getxLangId());
		Map<String, String> winRateTypeMap = winRateTypeList.stream().collect(Collectors.toMap(ComDictionaryMaintainVO::getCode, isZh ? ComDictionaryMaintainVO::getChineseName : ComDictionaryMaintainVO::getEnglishName));
		// 招标类型
		List<ComDictionaryMaintainVO> tenderTypeList = Optional.ofNullable(mapList.get(OpportunityConstant.TYPE_FOR_TENDER_TYPE)).orElse(Collections.emptyList());
		Map<String, String> tenderTypeMap = tenderTypeList.stream().collect(Collectors.toMap(ComDictionaryMaintainVO::getCode, isZh ? ComDictionaryMaintainVO::getChineseName : ComDictionaryMaintainVO::getEnglishName));
		// 当前阶段
		List<ComDictionaryMaintainVO> currentPhaseTypeList =Optional.ofNullable(mapList.get(OpportunityConstant.CURRENT_PHASES_TYPE)).orElse(Collections.emptyList());
		Map<String, String> currentPhaseTypeMap = currentPhaseTypeList.stream().collect(Collectors.toMap(ComDictionaryMaintainVO::getCode, isZh ? ComDictionaryMaintainVO::getChineseName : ComDictionaryMaintainVO::getEnglishName));
		List<String> nonDraftOpportunityCodes = opportunityVOS.stream()
				.filter(opportunityVO -> !OptyStatusEnum.DRAFT.getCode().equals(opportunityVO.getStatusCd()))
				.map(OpportunityVO::getOptyCd).collect(Collectors.toList());
		Map<String, ProjectAuthInfoDto> projectAuthInfoDtoMap = getProjectAuthInfoDtoMap(nonDraftOpportunityCodes);

		for (OpportunityVO vo:opportunityVOS){
			if (StringUtils.isBlank(vo.getStatusReport())){
				vo.setStatusReport(ReportStatusEnum.UNFILLED.getValue());
			}
			// 设置操作按钮
			setOperationButtonAndExpectSignMoneyModify(vo, manageAuthFlag, isExport);
			// 设置状态名
			vo.setDataSourceName(mapDataSource.containsKey(vo.getDataSource()) ?
					mapDataSource.get(vo.getDataSource()):vo.getDataSource());
			vo.setStatusCdName(OptyStatusEnum.getStatusName(vo.getStatusCd(), CommonUtils.getxLangId()));
			vo.setStatusReportName(mapStatusReport.containsKey(vo.getStatusReport()) ?
					mapStatusReport.get(vo.getStatusReport()):vo.getStatusReport());
			vo.setStatusAuthName(mapStatusAuth.containsKey(vo.getStatusAuth()) ?
					mapStatusAuth.get(vo.getStatusAuth()):vo.getStatusAuth());
			// 设置商机所属部门
			vo.setDeptName(mapOrganizationNames.getOrDefault(vo.getDeptNo(), vo.getDeptNo()));
			// 设置当前阶段字段中文名称
			vo.setProjectPhasesName(currentPhaseTypeMap.getOrDefault(vo.getProjectPhasesCode(), vo.getProjectPhasesCode()));
			// 设置招标类型字段中文名称
			vo.setTendTypeName(tenderTypeMap.getOrDefault(CommonMapUtil.BIDDING_TYPE_MAP.inverse().getOrDefault(vo.getTendTypeCode(), vo.getTendTypeCode()), vo.getTendTypeCode()));
			// 设置赢率
			vo.setWinRateName(CommonMapUtil.SUCCESS_TATE_NAME_MAP.getOrDefault(vo.getWinRate(), vo.getWinRate()));
			setProjectAuthInfoToOpportunityVO(vo, projectAuthInfoDtoMap, isZh);
		}
	}

	private void setOldFinalCustomerNameAndCode(OpportunityVO vo, Map<String, AccountInfo> oldAccountInfoMap){
		if(StringUtils.isBlank(vo.getFinalCustomerName()) && StringUtils.isNotBlank(vo.getFinalCustomerCode())){
			AccountInfo accountInfo = oldAccountInfoMap.getOrDefault(vo.getFinalCustomerCode(), new AccountInfo());
			vo.setFinalCustomerCode(accountInfo.getAccountName());
			vo.setFinalCustomerName(accountInfo.getAccountNum());
		}
	}

	private void setOperationButtonAndExpectSignMoneyModify(OpportunityVO vo, boolean manageAuthFlag, Boolean isExport){
		boolean isNeedButton = vo.getOptyCd().startsWith(OpportunityConstant.OPPORTUNITY_PREFIX) && !isExport;
		if (isNeedButton) {
			setOperationButton(vo, manageAuthFlag);
		} else if (StringUtils.isNotBlank(vo.getOptyCd()) && !Pattern.matches(OpportunityConstant.OPTY_CD_REGEX, vo.getOptyCd())) {
			// 老系统商机按钮不显示
			vo.setOperationButton(new OperationButtonVO());
			// 老系统商机签单金额转换（万元->元）
			if (vo.getTotalAmount() != null) {
				vo.setTotalAmount(vo.getTotalAmount().multiply(BigDecimal.valueOf(10000L)));
			}
			if(OptyStatusEnum.TICKET_LOSS.getCode().equals(vo.getStatusCd())){
				vo.setStatusCd(OptyStatusEnum.OPTY_SUSPEND.getCode());
			}
		}

	}

	private Map<String, ProjectAuthInfoDto> getProjectAuthInfoDtoMap(List<String> optyCds){
		Map<String, ProjectAuthInfoDto> projectAuthInfoDtoMap = new HashMap<>(optyCds.size());
		try {
			projectAuthInfoDtoMap = ProjectAuthorizationUtils.getProjectAuthInfo(optyCds);
		}catch (Exception e){
			logger.error("iChannel侧商机查询获取授权信息失败", e);
		}
		return projectAuthInfoDtoMap;
	}

	private void setProjectAuthInfoToOpportunityVO(OpportunityVO opportunityVO, Map<String, ProjectAuthInfoDto> projectAuthInfoDtoMap, boolean isZh){
		if(null == projectAuthInfoDtoMap){
			return;
		}
		ProjectAuthInfoDto projectAuthInfoDto = projectAuthInfoDtoMap.get(opportunityVO.getOptyCd());
		if(Objects.nonNull(projectAuthInfoDto)) {
			ProjectAuthStatusEnum projectAuthStatusEnum = projectAuthInfoDto.getStatusAuth();
			if (Objects.nonNull(projectAuthStatusEnum)) {
				String statusAuthName = isZh ? projectAuthStatusEnum.getCnName() : projectAuthStatusEnum.getEnName();
				opportunityVO.setStatusAuth(projectAuthStatusEnum.getCode());
				opportunityVO.setStatusAuthName(statusAuthName);
			}
		}else{
			opportunityVO.setStatusAuth(ProjectAuthStatusEnum.NOT_APPLIED.getCode());
			String statusAuthName = isZh ? ProjectAuthStatusEnum.NOT_APPLIED.getCnName() : ProjectAuthStatusEnum.NOT_APPLIED.getEnName();
			opportunityVO.setStatusAuthName(statusAuthName);
		}
	}

	private void setOperationButton(OpportunityVO vo, boolean manageAuthFlag){
		OperationButtonVO button = new OperationButtonVO();
		boolean isCreatedBy = StringUtils.equals(vo.getCreatedBy(), CommonUtils.getEmpNo());
		if (manageAuthFlag) {
			isCreatedBy = Boolean.TRUE;
		}
		// 设置 “查看月报” 按钮
		setReadReportFlag(vo, button);
		if (isCreatedBy && OptyStatusEnum.DRAFT.getCode().equals(vo.getStatusCd())){
			// 修改
			button.setEditFlag(Boolean.TRUE);
			// 删除
			button.setDeleteFlag(Boolean.TRUE);
		}
		if (isCreatedBy && OptyStatusEnum.APPROVING.getCode().equals(vo.getStatusCd())){
			// 撤销
			button.setCancelFlag(Boolean.TRUE);
		}

		List<String> currentStatusList = Arrays.asList(OpportunityCurrentStatus.CANCEL.getValue(),
				OpportunityCurrentStatus.WINNING.getValue(),
				OpportunityCurrentStatus.BID_LOSS.getValue());
		if (isCreatedBy
			&& OptyStatusEnum.OPTY_RENEWING.getCode().equals(vo.getStatusCd())
			&& SourceOfOpportunityEnum.CHANNEL_FILING.getValue().equals(vo.getDataSource())
			&& !currentStatusList.contains(vo.getCurrentStatus())){
			// 更新月报
			button.setUpdateReportFlag(Boolean.TRUE);
		}
		if (OptyStatusEnum.OPTY_SUSPEND.getCode().equals(vo.getStatusCd())){
			// 激活：1个商机激活2次后，不再显示激活按钮；非管理员只能激活自建的
			if (isCreatedBy && (vo.getActiveCount() == null
					|| vo.getActiveCount() < OpportunityConstant.MAX_ACTIVE_NUM)){
				button.setActivationFlag(Boolean.TRUE);
			}
		}
		vo.setOperationButton(button);
	}

	private void setReadReportFlag(OpportunityVO vo, OperationButtonVO button){
		if ((OptyStatusEnum.OPTY_RENEWING.getCode().equals(vo.getStatusCd())
			 || OptyStatusEnum.OPTY_SUSPEND.getCode().equals(vo.getStatusCd())
			 || OptyStatusEnum.OPTY_PROJ_SUBMIT.getCode().equals(vo.getStatusCd())
			 || OptyStatusEnum.OPTY_TRANSFERRED.getCode().equals(vo.getStatusCd())
			 || OptyStatusEnum.TICKET_WIN.getCode().equals(vo.getStatusCd())
			 || OptyStatusEnum.TICKET_LOSS.getCode().equals(vo.getStatusCd())
			 || OptyStatusEnum.OPTY_CLOSED.getCode().equals(vo.getStatusCd()))
				&& ReportStatusEnum.FILLED.getValue().equals(vo.getStatusReport())){
			//  “查看月报” 按钮
			button.setReadReportFlag(Boolean.TRUE);
		}
	}
	/**
	 * 根据 rowId 来更新 主表状态
	 * @param rowId 主键
	 * @return 更新总数
	 */
	@Override
	public int updateStatusByRowId(String rowId, String status){
		return opportunityDao.updateStatusByRowId(rowId, status);
	}

	@Override
	public void dailyClosingTasksHandleComplianceStatus(){
		List<String> opportunityIdRestrictedList = opportunityDao.getOpportunityIdRestricted();
		if(CollectionUtils.isEmpty(opportunityIdRestrictedList)){
			return;
		}

		opportunityIdRestrictedList.forEach(ele ->{
			try {
				this.refreshComplianceStatus(ele);
				taskExecutor.execute(() ->{
					this.sendRestrictedEntityMail(ele);
				});
			}catch (Exception e){
				logger.error("dailyClosingTasksHandleComplianceStatus,rowId is {}",ele,e);
			}
		});
	}


	@Override
	public Boolean refreshComplianceStatus(String rowId) throws Exception {
		// 查询客户编码等信息
		OpportunityDetail opportunityDetail = opportunityDetailDao.get(rowId);

		if(opportunityDetail == null){
			logger.error("opportunityDetail is null,rowId is {}",rowId);
			return Boolean.FALSE;
		}

		String finalCustomerRestrictionFlag = opportunityDetail.getFinalCustomerRestrictionFlag();
		String agencyRestrictionFlag = opportunityDetail.getAgencyRestrictionFlag();

		// 不满足，【审批中，是受限制主体】 条件，直接返回
		if(!isRestrictedParty( finalCustomerRestrictionFlag, agencyRestrictionFlag)){
			return Boolean.FALSE;
		}

		String crmCustomerCode = opportunityDetail.getCrmCustomerCode();
		String lastAccId = opportunityDetail.getLastAccId();
		RestrictedPartyVO restrictedParty = iPrmOpportunityApprovalService.getRestrictedPartyV2(lastAccId, crmCustomerCode,opportunityDetail.getDeptNo());
		// 判断受限制主体状态是否有变化
		if (!finalCustomerRestrictionFlag.equalsIgnoreCase(restrictedParty.getEndUserRestrictedPartyCode())
				|| !agencyRestrictionFlag.equalsIgnoreCase(restrictedParty.getCustomerRestrictedPartyCode())) {
			// 首先更新数据
			this.updateCompliaceStatus(rowId,restrictedParty);
			// 刷新moa显示
			this.refreshMoaRestrictedSubjectParams(rowId,restrictedParty);
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}


	private void refreshMoaRestrictedSubjectParams(String rowId,RestrictedPartyVO restrictedParty){
		ComApprovalRecord comApprovalRecord = comApprovalRecordDao.getByBusinessId(rowId);
		if(comApprovalRecord == null || comApprovalRecord.getWorkFlowInstanceId() == null){
			return;
		}
		HashMap<String,Object> parameterMap = new HashMap<>();
		// 最终用户受限制主体
		parameterMap.put("consumerRestrictedParty",RestrictedPartyEnum.getNameByCode(restrictedParty.getEndUserRestrictedPartyCode()));
		//渠道商是否受限制主体
		parameterMap.put("channelRestrictedParty",RestrictedPartyEnum.getNameByCode(restrictedParty.getCustomerRestrictedPartyCode()));
		approvalInfoService.refreshMoaParams(comApprovalRecord.getWorkFlowInstanceId(),parameterMap);
	}

	@Override
	public void sendRestrictedEntityMail(String rowId) {
		// 查询业务经理，受限制主体等信息
		MailOpportunityInfoEntity mailOpportunityInfoEntity = opportunityDao.getMailOpportunityInfoEntity(rowId);
		if (mailOpportunityInfoEntity == null){
			logger.error("IOpportunityServiceImpl#sendRestrictedEntityMail,mailOpportunityInfoEntity is null,rowId is {}",rowId);
			return;
		}


		String receiver = mailOpportunityInfoEntity.getBusinessManagerId();
		if (StringUtils.isBlank(receiver)){
			logger.error("IOpportunityServiceImpl#sendRestrictedEntityMail,receiver is null,rowId is {}",rowId);
			return;
		}
		String agencyRestrictionFlag = mailOpportunityInfoEntity.getAgencyRestrictionFlag();
		String finalCustomerRestrictionFlag = mailOpportunityInfoEntity.getFinalCustomerRestrictionFlag();
		List<String> restrictionFlagList = Arrays.asList(agencyRestrictionFlag, finalCustomerRestrictionFlag);


		OpportunityMailEntity opportunityMailEntity = new OpportunityMailEntity();
		opportunityMailEntity.setMailTo(receiver);
		opportunityMailEntity.setReceiverIds(receiver);
		opportunityMailEntity.setModelType(TEMPLATE_TYPE_NOTIFICATION);
		opportunityMailEntity.setOptyCd(mailOpportunityInfoEntity.getOptyCd());
		opportunityMailEntity.setOptyName(mailOpportunityInfoEntity.getAttrib46());
		opportunityMailEntity.setDataSource(mailOpportunityInfoEntity.getDataSource());
		opportunityMailEntity.setRowId(mailOpportunityInfoEntity.getRowId());

		if (restrictionFlagList.contains(RestrictedPartyEnum.YES.getCode())){
			opportunityMailEntity.setModelCode(OPPORTUNITY_EMAIL_FOR_RESTRICTED_ENTITY_YES);
		}else if(CollectionUtils.containsAny(restrictionFlagList,Arrays.asList(RestrictedPartyEnum.PENDING.getCode(),null,""))){
			opportunityMailEntity.setModelCode(OPPORTUNITY_EMAIL_FOR_RESTRICTED_ENTITY_NULL_OR_PENDING);
		}else if(restrictionFlagList.contains(RestrictedPartyEnum.EMBARGO.getCode())){
			opportunityMailEntity.setModelCode(OPPORTUNITY_EMAIL_FOR_RESTRICTED_ENTITY_EMBARGO);
		}else {
			return;
		}
		sendMailService.sendOptyMail(opportunityMailEntity);
	}

	private  Boolean isRestrictedParty(String finalCustomerRestrictionFlag,String agencyRestrictionFlag){
		//审批中，是受限制主体，才继续操作
		if (RestrictedPartyEnum.isRestrictedParty(finalCustomerRestrictionFlag)
				|| RestrictedPartyEnum.isRestrictedParty(agencyRestrictionFlag)){
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	private int updateCompliaceStatus(String rowId,RestrictedPartyVO restrictedParty){
		OpportunityDetail newOpportunityDetail = new OpportunityDetail();
		newOpportunityDetail.setRowId(rowId);
		newOpportunityDetail.setFinalCustomerRestrictionFlag(restrictedParty.getEndUserRestrictedPartyCode());
		newOpportunityDetail.setAgencyRestrictionFlag(restrictedParty.getCustomerRestrictedPartyCode());
		newOpportunityDetail.setLastUpd(new Date());
		opportunityInfoServiceImpl.getCustomerId(newOpportunityDetail,CommonUtils.getSysGlobalConstVo());
		SOptyXBO sOptyXBO = LcapConverterUtil.buildSoptyx(newOpportunityDetail);

		return sOptyXDao.updateById(sOptyXBO);
//		return opportunityDetailDao.update(newOpportunityDetail);
	}

	@Override
	public PageRows<OpptyCustomerCreateRecordVO> queryCustomerRecordWithIChannel(FormData<IChannelOpptyCustomerCreateRecordQuery> formData) throws Exception {
		checkParam(formData);
		return opptyCustomerCreateRecordService.queryCustomerRecordWithIchannel(formData);
	}

	private void checkParam(FormData<IChannelOpptyCustomerCreateRecordQuery> formData) {
		if (formData.getPage() < 1) {
			formData.setPage(CommonConst.DEFAULT_PAGE_NUM);
		}
		if (formData.getRows() < 1 ) {
			formData.setRows(CommonConst.DEFAULT_PAGE_SIZE);
		}
		if (formData.getRows() > CommonConst.MAX_PAGE_SIZE) {
			formData.setRows(CommonConst.MAX_PAGE_SIZE);
		}
		if (formData.getBo() == null || StringUtils.isBlank(formData.getBo().getCrmCustomerCode())) {
			throw new BusiException(RetCode.VALIDATIONERROR_CODE, "crmCustomerCode is blank!");
		}
	}

	@Override
	public Map<String, Boolean> judgeNeedMaintainMainProds(List<String> rowIds, String createdTimeStart, String createdTimeEnd) {
		Map<String, Boolean> map = new HashMap<>();
		// 获取商机状态=报备成功，赢率是【50%-70%】或【70以上】，商机来源=中兴自建 的商机列表; 如果没传参，就查询全部的数据
		List<String> opptyIds = opportunityDao.getPrmRenewingHighWinRateOpptyIds(rowIds, createdTimeStart, createdTimeEnd);
		Optional.ofNullable(rowIds).orElse(Lists.newArrayList()).forEach(rowId -> map.put(rowId, false));
		Optional.ofNullable(opptyIds).orElse(Lists.newArrayList()).forEach(rowId -> map.put(rowId, false));
		if (CollectionUtils.isEmpty(opptyIds)) {
			return map;
		}

		// 获取商机产品列表，判断商机政企产品是否维护了公司主产品
		List<OpportunityProduct> opptyRelationsByOptyIds = opportunityProductDao.getMainProdsRelationByOptyIds(opptyIds);
		Map<String, List<OpportunityProduct>> opptyProdsMap = opptyRelationsByOptyIds.stream()
				.filter(prod -> StringUtils.isNotBlank(prod.getOpptyId()))
				.collect(Collectors.groupingBy(OpportunityProduct::getOpptyId));

		opptyProdsMap.forEach((opptyId, products) -> {
			// 商机关联主产品的关联的政企产品id列表
			Set<String> relationParProdIds = products.stream()
					.filter(prod -> PDM_PROD.equalsIgnoreCase(prod.getBusinessType()))
					.map(OpportunityProduct::getParProdId)
					.filter(StringUtils::isNotBlank)
					.collect(Collectors.toSet());
			// 商机关联的政企产品id列表
			Set<String> newOpportunityProdIds = products.stream()
					.filter(prod -> NEW_OPPORTUNITY.equalsIgnoreCase(prod.getBusinessType()))
					.map(OpportunityProduct::getRowId)
					.filter(StringUtils::isNotBlank)
					.collect(Collectors.toSet());

			// 比对商品的政企产品和商机主产品关联的政企产品是否完全一致
			map.put(opptyId, !relationParProdIds.equals(newOpportunityProdIds));
		});
		return map;
	}

	@Override
	public List<PrmQuickCodeValueVO> queryByCodeType(List<String> codeTypeList) {
		List<BulkCodeValuesByOneTypeVo> codeValuesByCodeTypeList = ichannelBaseAdapter.getCodeValuesByCodeTypeList(codeTypeList);
		return codeValuesByCodeTypeList.stream().flatMap(e -> e.getCodeValueVOList().stream()).collect(Collectors.toList());
	}
}
