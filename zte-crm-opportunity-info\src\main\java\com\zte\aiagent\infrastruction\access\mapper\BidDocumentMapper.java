package com.zte.aiagent.infrastruction.access.mapper;

import com.zte.aiagent.infrastruction.access.po.BidDocumentPO;
import com.zte.aiagent.ui.dto.request.BidDocumentPageQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import com.zte.itp.msa.core.model.FormData;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface BidDocumentMapper {
    /**
     * 插入招标文件记录
     * @param bidDocument 招标文件PO对象
     * @return 影响的行数
     */
    int insert(BidDocumentPO bidDocument);

    /**
     * 根据ID查询招标文件
     * @param rowId 主键ID
     * @return 招标文件PO对象
     */
    BidDocumentPO selectByPrimaryKey(String rowId);

    /**
     * 根据ID更新招标文件
     * @param bidDocument 招标文件PO对象
     * @return 影响的行数
     */
    int updateByPrimaryKey(BidDocumentPO bidDocument);

    /**
     * 根据ID删除招标文件
     * @param rowId 主键ID
     * @return 影响的行数
     */
    int deleteByPrimaryKey(String rowId);

    /**
     * 分页查询招标文件列表
     * @param request 查询请求对象
     * @return 招标文件列表
     */
    List<BidDocumentPO> selectPageList(FormData<BidDocumentPageQueryDTO> request);

    /**
     * 查询总记录数
     * @param request 查询请求对象
     * @return 总记录数
     */
    Long selectPageCount(FormData<BidDocumentPageQueryDTO> request);
}
