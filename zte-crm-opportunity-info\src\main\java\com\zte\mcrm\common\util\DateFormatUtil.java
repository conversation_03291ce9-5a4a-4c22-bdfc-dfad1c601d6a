package com.zte.mcrm.common.util;

import com.zte.mcrm.common.consts.CommonConstant;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.Calendar;
import java.util.Date;

/**
 * @Author: <EMAIL>
 * @Date: 2021/10/28
 * @Description:
 */
public class DateFormatUtil {
    /**
     * 日期格式化成 yyyy-MM-dd
     * @param date
     * @return
     */
    public static String dateFormat(Date date) {
        if (null != date) {
            return DateFormatUtils.format(date, CommonConstant.YYYY_MM_DD_GAP);
        }
        return StringUtils.EMPTY;
    }
    /**
     * 日期格式化成 yyyy-MM-dd HH:mm:ss
     * @param date
     * @return
     */
    public static String dateFormatHMS(Date date) {
        if (null != date) {
            return DateFormatUtils.format(date, CommonConstant.YYYY_MM_DD_HH_MM_SS24);
        }
        return StringUtils.EMPTY;
    }
    /**
     * 日期格式化成 当天开始时间
     * @param date
     * @return
     */
    public static Date dateToBegin(Date date) {
        if(date==null){
            return null;
        }
        Calendar c=Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY,0);
        c.set(Calendar.MINUTE,0);
        c.set(Calendar.SECOND,0);
        return c.getTime();
    }
    /**
     * 日期格式化成 当天结束时间
     * @param date
     * @return
     */
    public static Date dateToEnd(Date date) {
        if(date==null){
            return null;
        }
        Calendar c=Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY,23);
        c.set(Calendar.MINUTE,59);
        c.set(Calendar.SECOND,59);
        return c.getTime();
    }
}
