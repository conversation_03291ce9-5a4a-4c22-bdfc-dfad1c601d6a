package com.zte.mcrm.common.model;

import org.apache.commons.lang3.builder.ToStringBuilder;

/**
 * @ClassName: CountryAndCityVo
 * @program: zte-crm-account-info服务
 * @author: 何志权**********
 * @create: 2019/10/22 9:24
 * @description:
 **/
public class CountryAndCityVo {

    /**
     * 表主键id
     */
    private String rowId;

    /**国家城市编码*/
    private String id;

    /**创建时间*/
    private String created;

    /**创建人*/
    private String createBy;

    /**最后更新时间*/
    private String lastUpdate;

    /**最后更新人*/
    private String lastUpdateBy;
    /**国家或城市名称(中文)*/
    private String nameZh;
    /**国家或城市名称(英文)*/
    private String nameEn;

    /**国家二位英文简写*/
    private String countryForShort;

    /**是否受制裁国家*/
    private String sancCountry;

    public String getCountryForShort() {
        return countryForShort;
    }

    public void setCountryForShort(String countryForShort) {
        this.countryForShort = countryForShort;
    }

    public String getSancCountry() {
        return sancCountry;
    }

    public void setSancCountry(String sancCountry) {
        this.sancCountry = sancCountry;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreated() {
        return created;
    }

    public void setCreated(String created) {
        this.created = created;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getLastUpdate() {
        return lastUpdate;
    }

    public void setLastUpdate(String lastUpdate) {
        this.lastUpdate = lastUpdate;
    }

    public String getLastUpdateBy() {
        return lastUpdateBy;
    }

    public void setLastUpdateBy(String lastUpdateBy) {
        this.lastUpdateBy = lastUpdateBy;
    }


    public String getRowId() {
        return rowId;
    }

    public void setRowId(String rowId) {
        this.rowId = rowId;
    }

    public String getNameZh() {
        return nameZh;
    }

    public void setNameZh(String nameZh) {
        this.nameZh = nameZh;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("rowId", rowId)
                .append("id", id)
                .append("created", created)
                .append("createBy", createBy)
                .append("lastUpdate", lastUpdate)
                .append("lastUpdateBy", lastUpdateBy)
                .append("nameZh", nameZh)
                .append("nameEn", nameEn)
                .append("countryForShort", countryForShort)
                .append("sancCountry", sancCountry)
                .toString();
    }
}
