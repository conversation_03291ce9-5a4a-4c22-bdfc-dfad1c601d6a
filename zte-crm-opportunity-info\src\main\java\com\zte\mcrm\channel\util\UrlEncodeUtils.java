package com.zte.mcrm.channel.util;

import com.zte.mcrm.channel.constant.OpportunityConstant;
import org.apache.http.Consts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
@Component
public class UrlEncodeUtils {

    private static final Logger logger = LoggerFactory.getLogger(UrlEncodeUtils.class);
    private String mailFrom;
    public static String base64hash;

    @Value("${mcrm.channel.util.base64.hash}")
    private String base64hashValue;

    @PostConstruct
    public void init() {
        base64hash = base64hashValue;
    }

    public static String escape(String src) {
        int i;
        char j;
        StringBuffer tmp = new StringBuffer();
        tmp.ensureCapacity(src.length() * 6);
        for (i = 0; i < src.length(); i++) {
            j = src.charAt(i);
            if (Character.isDigit(j) || Character.isLowerCase(j)
                    || Character.isUpperCase(j)) {
                tmp.append(j);
            }else if (j < 256) {
                tmp.append("%");
                if (j < 16) {
                    tmp.append("0");
                }
                tmp.append(Integer.toString(j, 16));
            } else {
                tmp.append("%u");
                tmp.append(Integer.toString(j, 16));
            }
        }
        return tmp.toString();
    }

    public static String unescape(String src) {
        StringBuffer tmp = new StringBuffer();
        tmp.ensureCapacity(src.length());
        int lastPos = 0, pos = 0;
        char ch;
        while (lastPos < src.length()) {
            pos = src.indexOf("%", lastPos);
            if (pos == lastPos) {
                if (src.charAt(pos + 1) == 'u') {
                    ch = (char) Integer.parseInt(src
                            .substring(pos + 2, pos + 6), 16);
                    tmp.append(ch);
                    lastPos = pos + 6;
                } else {
                    ch = (char) Integer.parseInt(src
                            .substring(pos + 1, pos + 3), 16);
                    tmp.append(ch);
                    lastPos = pos + 3;
                }
            } else {
                if (pos == -1) {
                    tmp.append(src.substring(lastPos));
                    lastPos = src.length();
                } else {
                    tmp.append(src, lastPos, pos);
                    lastPos = pos;
                }
            }
        }
        return tmp.toString();
    }

    /**
     * @param src
     * @return
     * @disc 对字符串重新编码
     */
    public static String isoToGB(String src) {
        String strRet = null;
        try {
            strRet = new String(src.getBytes(Consts.ISO_8859_1), "GB2312");
        } catch (Exception e) {
            logger.error("isoToGB error", e);
        }
        return strRet;
    }

    /**
     * @param src
     * @return
     * @disc 对字符串重新编码
     */
    public static String isoToUTF(String src) {
        String strRet = null;
        try {
            strRet = new String(src.getBytes( Consts.ISO_8859_1), Consts.UTF_8);
        } catch (Exception e) {
            logger.error("isoToUTF error", e);
        }
        return strRet;
    }

    /**
     * 正则化匹配
     *
     * @param str 要匹配的数据
     * @param reg 正则匹配的规则
     * @return
     */
    public static boolean isMatcher(String str, String reg) {
        //编译成一个正则表达式模式
        Pattern pattern = Pattern.compile(reg);
        //匹配模式
        Matcher matcher = pattern.matcher(str);
        if (matcher.matches()) {
            return true;
        }
        return false;
    }

    public static String botaEncodePassword(String pwd) {
        if (pwd == null || isMatcher(pwd, OpportunityConstant.URL_CODE_REGEX)) {
            throw new Error("INVALID_CHARACTER_ERR");
        }
        int i = 0, prev = 0, ascii, mod = 0;
        StringBuilder result = new StringBuilder();
        while (i < pwd.length()) {
            ascii = pwd.charAt(i);
            mod = i % 3;
            switch (mod) {
                //第一个6位只需要让8位二进制右移两位
                case 0:
                    result.append(base64hash.charAt(ascii >> 2));
                    break;
                //第二个6位=第一个8位的后两位+第二个八位的前四位
                case 1:
                    result.append(base64hash.charAt((prev & 3) << 4 | (ascii >> 4)));
                    break;
                //第三个6位=第二个8位的后四位+第三个8位的前两位
                //第四个6位 = 第三个8位的后6位
                case 2:
                    result.append(base64hash.charAt((prev & 0x0f) << 2 | (ascii >> 6)));
                    result.append(base64hash.charAt(ascii & 0x3f));
                    break;
                default:
                    break;
            }
            prev = ascii;
            i++;
        }
        //循环结束后看mod, 为0 证明需补3个6位，第一个为最后一个8位的最后两位后面补4个0。另外两个6位对应的是异常的“=”；
        // mod为1，证明还需补两个6位，一个是最后一个8位的后4位补两个0，另一个对应异常的“=”
        if (mod == 0) {
            result.append(base64hash.charAt((prev & 3) << 4));
            result.append("==");
        } else if (mod == 1) {
            result.append(base64hash.charAt((prev & 0x0f) << 2));
            result.append("=");
        }

        return result.toString();

    }

    public static String unescapeAndBtoa(String var0){
        return botaEncodePassword(unescape(var0));
    }
}

