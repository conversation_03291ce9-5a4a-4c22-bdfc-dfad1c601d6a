package com.zte.aiagent.infrastruction.access.po;

import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * 系统参数配置类型表PO类
 * 对应数据库表：sys_conf_type
 * 用于存储系统参数的类型定义
 */
@Data
public class SysConfTypePO {
    /**
     * 主键ID
     */
    private String rowId;

    /**
     * 编码类型
     */
    private String codeType;

    /**
     * 编码类型中文名称
     */
    private String codeTypeCnName;

    /**
     * 编码类型英文名称
     */
    private String codeTypeEnName;

    /**
     * 树形标记 Y/N
     */
    private String treeFlag;

    /**
     * 备注
     */
    private String memo;

    /**
     * 最后更新时间
     */
    private Date lastUpdatedDate;

    /**
     * 最后更新人
     */
    private String lastUpdatedBy;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 有效标记(Y/N)，用于逻辑删除
     */
    private String enabledFlag;

    /**
     * 租户ID
     */
    private Long tenantId;
}

