package com.zte.aiagent.app.command;

import com.zte.aiagent.common.exception.BidDocumentErrorCode;
import com.zte.mcrm.common.framework.exception.ErrorCodeException;
import lombok.Builder;
import lombok.Value;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * 创建文档命令
 * 封装文档创建所需的所有参数
 */
@Value
@Builder
public class CreateDocumentCommand {

    /** 文件大小限制：200MB */
    private static final long MAX_FILE_SIZE = 200 * 1024 * 1024L;

    /** 上传文件 */
    MultipartFile file;

    /** 解析模板ID */
    String parseTemplateId;

    /** 解析模板编码 */
    String parseTemplateCode;

    /** 操作人工号 */
    String operator;

    /** 租户ID */
    String tenantId;

    /**
     * 命令验证
     * 使用ErrorCodeException替代IllegalArgumentException
     */
    public void validate() {
        // 文件验证
        if (file == null || file.isEmpty()) {
            throw new ErrorCodeException(BidDocumentErrorCode.BD2103);
        }

        // 文件大小验证
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new ErrorCodeException(BidDocumentErrorCode.BD2123, MAX_FILE_SIZE / (1024 * 1024));
        }

        // 解析模板验证
        if (StringUtils.isBlank(parseTemplateId)) {
            throw new ErrorCodeException(BidDocumentErrorCode.BD2133);
        }
        if (StringUtils.isBlank(parseTemplateCode)) {
            throw new ErrorCodeException(BidDocumentErrorCode.BD2143);
        }

        // 操作人验证
        if (StringUtils.isBlank(operator)) {
            throw new ErrorCodeException(BidDocumentErrorCode.BD2163);
        }

        // 租户验证
        if (StringUtils.isBlank(tenantId)) {
            throw new ErrorCodeException(BidDocumentErrorCode.BD2153);
        }

        // 文件类型验证
        if (!isSupportedFileType()) {
            throw new ErrorCodeException(BidDocumentErrorCode.BD2113);
        }
    }

    /**
     * 获取文件名
     */
    public String getFileName() {
        return file != null ? file.getOriginalFilename() : "";
    }

    /**
     * 获取文件大小
     */
    public Long getFileSize() {
        return file != null ? file.getSize() : 0L;
    }

    /**
     * 获取文件类型
     */
    public String getFileType() {
        String filename = getFileName();
        if (StringUtils.isBlank(filename) || !filename.contains(".")) {
            return "";
        }
        return filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
    }

    /**
     * 检查是否为支持的文件类型
     */
    public boolean isSupportedFileType() {
        String fileType = getFileType();
        if (StringUtils.isBlank(fileType)) {
            return false;
        }
        String lowerType = fileType.toLowerCase();
        return "pdf".equals(lowerType) || "doc".equals(lowerType) || "docx".equals(lowerType);
    }
}
