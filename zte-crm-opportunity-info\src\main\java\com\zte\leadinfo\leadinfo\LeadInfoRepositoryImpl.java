package com.zte.leadinfo.leadinfo;

import cn.hutool.core.thread.ThreadUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.leadinfo.common.entity.CxApprOpHead;
import com.zte.leadinfo.common.entity.CxApprOppLn;
import com.zte.leadinfo.common.entity.CxDocItemDO;
import com.zte.leadinfo.common.entity.CxDocumentDO;
import com.zte.leadinfo.common.service.*;
import com.zte.leadinfo.leadinfo.entity.*;
import com.zte.leadinfo.leadinfo.service.*;
import com.zte.mcrm.account.access.vo.Account;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.enumeration.BusiEnum;
import com.zte.mcrm.common.util.AccountUtil;
import com.zte.opty.sync.domain.dto.OptySyncQueryDTO;
import com.zte.springbootframe.common.exception.BusiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.kie.api.command.Command;
import org.springframework.beans.factory.annotation.Autowired;
import com.zte.mcrm.lov.access.vo.ListOfValue;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * LeadInfoRepositoryImpl
 *
 * <AUTHOR> suntugui
 * @date 2024/6/4 10:15
 */
@Repository
@Slf4j
public class LeadInfoRepositoryImpl implements LeadInfoRepository {

    /** 商机对应老系统的审批类型， */
    private static final List<String> APPROVE_OBJECT_LIST = Arrays.asList("Operator Opportunity Approve", "Goverment Opportunity Approve");

    @Autowired
    private SOptyService optyService;
    @Autowired
    private SOptyXOldService optyXOldService;
    @Autowired
    private CxOpptyProdService opptyProdService;
    @Autowired
    private CxOptyEmpService cxOptyEmpService;
    @Autowired
    private CxDocumentService cxDocumentService;
    @Autowired
    private CxDocItemService cxDocItemService;
    @Autowired
    private LstValService lstValService;
    @Autowired
    private CxApprOpHeadService cxApprOpHeadService;
    @Autowired
    private CxApprOppLnService cxApprOppLnService;
    @Autowired
    private CustomerDictionaryService customerDictionaryService;
    @Resource(name="commonThreadPool")
    ThreadPoolTaskExecutor taskExecutor;
    private static final String CUSTOMER_KEY = "OPPTY_CUSTOMER_DICTIONARY";

    @Override
    public List<LeadInfoDTO> listByIds(Collection<String> optyRowIds) {
        if (CollectionUtils.isEmpty(optyRowIds)) {
            return Lists.newArrayList();
        }
        List<String> optyIds = optyRowIds.stream().filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<SOptyDO> optys = optyService.listByIds(optyIds);
        List<SOptyXDO> optyXs = optyXOldService.listByIds(optyIds);
        List<CxOpptyProdDO> prods = opptyProdService.listByOpptyIds(optyIds);
        List<CxOptyEmpDO> emps = cxOptyEmpService.listByOpptyIds(optyIds);
        List<CxDocumentDO> docs = cxDocumentService.listByOpptyIds(optyIds);
        List<String> billCodes = docs.stream().map(CxDocumentDO::getBillCode).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        List<CxDocItemDO> docItems = cxDocItemService.listByBillCodes(billCodes);
        List<CxApprOpHead> apprOpHeads = cxApprOpHeadService.listByOpptyIds(optyIds, APPROVE_OBJECT_LIST);
        List<CxApprOppLn> apprOppLns = cxApprOppLnService.listByOpptyIds(optyIds, APPROVE_OBJECT_LIST);

        Map<String, SOptyDO> optyMap = optys.stream().filter(opty -> StringUtils.isNotEmpty(opty.getRowId()))
                .collect(Collectors.toMap(SOptyDO::getRowId, Function.identity(), (v1, v2) -> v1));
        Map<String, SOptyXDO> optyXMap = optyXs.stream().filter(optyX -> StringUtils.isNotEmpty(optyX.getRowId()))
                .collect(Collectors.toMap(SOptyXDO::getRowId, Function.identity(), (v1, v2) -> v1));
        Map<String, List<CxOpptyProdDO>> prodMap = prods.stream().filter(prod -> StringUtils.isNotEmpty(prod.getOpptyId()))
                .collect(Collectors.groupingBy(CxOpptyProdDO::getOpptyId));
        Map<String, List<CxOptyEmpDO>> empMap = emps.stream().filter(emp -> StringUtils.isNotEmpty(emp.getOpptyId()))
                .collect(Collectors.groupingBy(CxOptyEmpDO::getOpptyId));
        Map<String, CxDocumentDO> docMap = docs.stream().filter(prod -> StringUtils.isNotEmpty(prod.getBusinessId()))
                .collect(Collectors.toMap(CxDocumentDO::getBusinessId, Function.identity(), (v1, v2) -> v1));
        Map<String, List<CxDocItemDO>> itemsMap = this.obtainOptyIdToItemsMap(docs, docItems);
        Map<String, List<CxApprOpHead>> apprOpHeadMap = this.obtainOptyApprOpHeads(apprOpHeads, apprOppLns);

        List<LeadInfoDTO> res = Lists.newArrayList();
        for (String optyRowId : optyIds) {
            LeadInfoDTO leadInfoDTO = new LeadInfoDTO();
            leadInfoDTO.setSOpty(optyMap.get(optyRowId));
            leadInfoDTO.setSOptyX(optyXMap.get(optyRowId));
            leadInfoDTO.setCxOpptyProds(prodMap.getOrDefault(optyRowId, Collections.emptyList()));
            leadInfoDTO.setCxOptyEmps(empMap.getOrDefault(optyRowId, Collections.emptyList()));
            leadInfoDTO.setCxDocument(docMap.get(optyRowId));
            leadInfoDTO.setCxDocItems(itemsMap.getOrDefault(optyRowId, Collections.emptyList()));
            leadInfoDTO.setCxApprOpHeads(apprOpHeadMap.getOrDefault(optyRowId, Collections.emptyList()));
            res.add(leadInfoDTO);
        }
        return res;
    }

    private Map<String, List<CxApprOpHead>> obtainOptyApprOpHeads(List<CxApprOpHead> apprOpHeads, List<CxApprOppLn> apprOppLns) {
        Map<String, List<CxApprOppLn>> apprOppLnMap = apprOppLns.stream().collect(Collectors.groupingBy(CxApprOppLn::getApprOprHeadId));
        apprOpHeads.forEach(s -> s.setCxApprOppLnList(apprOppLnMap.getOrDefault(s.getRowId(), Collections.emptyList())));
        return apprOpHeads.stream().collect(Collectors.groupingBy(CxApprOpHead::getObjectId));
    }

    private Map<String, List<CxDocItemDO>> obtainOptyIdToItemsMap(List<CxDocumentDO> documentDOList, List<CxDocItemDO> docItems) {
        Map<String, String> billCodeToOptyIdMap = documentDOList.stream().filter(doc -> StringUtils.isNotEmpty(doc.getBillCode()))
                .collect(Collectors.toMap(CxDocumentDO::getBillCode, CxDocumentDO::getBusinessId, (v1, v2) -> v1));
        Map<String, List<CxDocItemDO>> billCodeToItemsMap = docItems.stream().filter(item -> StringUtils.isNotEmpty(item.getBillCode()))
                .collect(Collectors.groupingBy(CxDocItemDO::getBillCode));

        Map<String, List<CxDocItemDO>> optyIdToItemsMap = Maps.newHashMap();
        for (String billCode : billCodeToOptyIdMap.keySet()) {
            optyIdToItemsMap.put(billCodeToOptyIdMap.get(billCode), billCodeToItemsMap.getOrDefault(billCode, Collections.emptyList()));
        }
        return optyIdToItemsMap;
    }

    @Override
    public boolean saveSyncResult(List<String> rowIds, String result, String message) {
        return optyService.updateMigrationResult(rowIds, result, message) > 0;
    }

    @Override
    public List<ListOfValue> getLovTypeValue(String lovType) {
        return lstValService.getLovTypeValue(lovType);
    }

    @Override
    public List<String> selectIdsByConditions(OptySyncQueryDTO params) {
        return optyService.selectIdsByConditions(params);
    }

    @Override
    public Map<String, CustomerDictionaryDO> selectCustomerId(Set<String> params) {
        List<CustomerDictionaryDO> dictionaryDOList = customerDictionaryService.selectByIds(new ArrayList<>(params));
        return dictionaryDOList.stream().collect(Collectors.toMap(CustomerDictionaryDO::getInputParam, Function.identity(), (e1, e2) -> e1));
    }

    @Override
    public Boolean saveBatchCustomer() throws BusiException, InterruptedException {
        // 查询所有的数据，异步开启查询调用，每次10条数据，插入之前先考虑数据库是否存在
        RedisCacheUtils.set(CUSTOMER_KEY, LocalDate.now() + " " + LocalTime.now(), 864000L);
        List<CustomerInfoDO> infoDOList = optyService.selectAll();
        List<String> list = new ArrayList<>();
        infoDOList.forEach(e -> list.addAll(e.buildAll()));
        List<String> filterList = list.stream().filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<List<String>> partition = Lists.partition(filterList, 10);
        CompletableFuture.runAsync(() -> {
            try {
                for (List<String> part : partition) {
                    Map<String, CustomerDictionaryDO> dictionaryDOMap = selectCustomerId(new HashSet<>(part));
                    List<String> filteList = part.stream().filter(e -> !dictionaryDOMap.containsKey(e)).collect(Collectors.toList());
                    List<String> updateList = dictionaryDOMap.values().stream().filter(CustomerDictionaryDO::filterStatus).map(CustomerDictionaryDO::getInputParam)
                            .collect(Collectors.toList());
                    filteList.addAll(updateList);
                    if (CollectionUtils.isEmpty(filteList)) {
                        continue;
                    }
                    if (saveCustomer(filteList)) {
                        continue;
                    }
                    ThreadUtil.sleep(60000);
                }
            } finally {
                RedisCacheUtils.del(CUSTOMER_KEY);
            }
        }, taskExecutor);
        return true;
    }

    public boolean saveCustomer(List<String> querylist) {
        // 查询接口获取转换值
        List<CustomerDictionaryDO> saveList = new ArrayList<>();
        List<Account> accountList = null;
        try {
            accountList = AccountUtil.getAccountListByids(querylist);
        } catch (Exception e) {
            // 保存异常数据
            log.error(e.getMessage());
            buildDictionary(querylist, new HashMap<>(), saveList, e.getMessage());
            customerDictionaryService.batchInsert(saveList);
            return true;
        }
        Map<String, String> idMap = Optional.ofNullable(accountList).orElse(Collections.emptyList()).stream().collect(Collectors.toMap(Account::getId, Account::getCustNo, (e1, e2) -> e1));
        buildDictionary(querylist, idMap, saveList, null);
        customerDictionaryService.batchInsert(saveList);
        return false;
    }

    private static void buildDictionary(List<String> filteList, Map<String, String> idMap, List<CustomerDictionaryDO> saveList, String errorMsg) {
        filteList.forEach(e -> {
            CustomerDictionaryDO customerDictionaryDO = new CustomerDictionaryDO();
            customerDictionaryDO.buildSaveParam(idMap, e, errorMsg);
            saveList.add(customerDictionaryDO);
        });
    }

    @Override
    public String queryRedis(String key) {
        return RedisCacheUtils.get(key, String.class);
    }

    @Override
    public Boolean saveCustomerByIdList(List<String> idList) {
        return saveCustomer(idList);
    }
}
