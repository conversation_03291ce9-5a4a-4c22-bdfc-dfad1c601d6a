package com.zte.mcrm.adapter.approval.model.dto;

import com.zte.mcrm.adapter.approval.model.AttachedFile;
import com.zte.mcrm.common.annotation.EmpName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: 10243305
 * @date: 2021/8/25 下午4:07
 */
@Data
public class ApprovedTask implements Serializable {
    @ApiModelProperty(value = "审批任务ID")
    private String taskId;
    @ApiModelProperty(value = "审批节点ID")
    private String nodeId;
    @ApiModelProperty(value = "审批节点类型")
    private String nodeType;
    @ApiModelProperty(value = "审批节点名称")
    private String nodeName;
    @EmpName
    @ApiModelProperty(value = "转交来源人")
    private String fromApprover;
    @EmpName
    @ApiModelProperty(value = "转交目的人")
    private String toApprover;
    @EmpName
    @ApiModelProperty(value = "审批人")
    private String approver;
    @ApiModelProperty(value = "处理方式")
    private String handler;
    @ApiModelProperty(value = "审批单创建时间")
    private Date createdDate;
    @ApiModelProperty(value = "审批完成时间")
    private Date approvalDate;
    @ApiModelProperty(value = "任务状态")
    private String taskStatus;
    @ApiModelProperty(value = "审批结果")
    private String result;
    @ApiModelProperty(value = "审批结果")
    private String resultCN;
    @ApiModelProperty(value = "审批意见描述")
    private String opinion;
    @ApiModelProperty(value = "审批扩展意见描述")
    private String extOpinion;
    @ApiModelProperty(value = "附件")
    private List<AttachedFile> attachedFiles;
}
