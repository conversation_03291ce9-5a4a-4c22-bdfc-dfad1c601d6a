package com.zte.mcrm.channel.model.dto;

import com.zte.mcrm.channel.model.mapper.OpportunityInfoVOMapper;
import com.zte.mcrm.common.upload.model.entity.SimpleUploadFileInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 客户信息
 */
@Getter
@Setter
public class CustomerInfoDTO {
    /**
     * 商机id
     */
    @ApiModelProperty(value = "商机id")
    @NotBlank(message = "{optId.null}")
    private String optyId;
    /**
     * 渠道商名
     */
    @ApiModelProperty(value = "渠道商名")
    private String customerName;
    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    private String crmCustomerCode;
    /**
     * 最终用户名
     */
    @ApiModelProperty(value = "最终用户名")
    @NotBlank(message = "{lastAccName.null}")
    private String lastAccName;
    /**
     * 最终用户编码
     */
    @ApiModelProperty(value = "最终用户名编码")
    private String lastAccId;
    /**
     * 部门编码
     */
    @ApiModelProperty(value = "部门编码")
    @NotBlank(message = "{deptNo.null}")
    private String deptNo;
    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    private String deptPath;
    /**
     * 主行业
     */
    @ApiModelProperty(value = "主行业")
    @NotBlank(message = "{finalCustomerParentTrade.null}")
    private String finalCustomerParentTrade;
    /**
     * 子行业
     */
    @ApiModelProperty(value = "子行业")
    @NotBlank(message = "{finalCustomerChildTrade.null}")
    private String finalCustomerChildTrade;
    /**
     * 业务经理工号
     */
    @NotBlank(message = "{businessManagerId.null}")
    @ApiModelProperty(value = "业务经理工号")
    private String businessManagerId;
    /**
     * 业务经理姓名
     */
    @ApiModelProperty(value = "业务经理姓名")
    @NotBlank(message = "{businessManagerName.null}")
    private String businessManagerName;
    /**
     * 最终用户联系人姓名
     */
    @ApiModelProperty(value = "联系人姓名")
    @NotBlank(message = "{finalCustomerContactName.empty}")
    private String finalCustomerContactName;
    /**
     * 最终用户联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    @NotBlank(message = "{finalCustomerContactPhone.empty}")
    private String finalCustomerContactPhone;
    /**
     * 资质证明
     */
    @ApiModelProperty(value = "资质证明")
    @NotNull(message = "{orgAttachments.null}")
    @Size(min = 1, max = 1, message = "{orgAttachments.size}")
    private List<SimpleUploadFileInfo> orgAttachments;

    /**
     * 获取客户的营业执照或官网证明，目前只有一张
     * @return
     */
    public String getCustomerFirstAttachment() {
        return orgAttachments.get(0).getDmeKey();
    }



}
