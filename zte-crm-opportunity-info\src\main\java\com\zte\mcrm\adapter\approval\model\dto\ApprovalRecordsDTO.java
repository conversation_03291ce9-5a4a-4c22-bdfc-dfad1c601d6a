package com.zte.mcrm.adapter.approval.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: 10243305
 * @date: 2021/8/25 下午4:06
 */
@Data
public class ApprovalRecordsDTO implements Serializable {
    @ApiModelProperty(value = "审批流实例ID")
    private String flowInstanceId;
    @ApiModelProperty(value = "审批流程实例的状态")
    private String status;
    @ApiModelProperty(value = "审批任务记录列表")
    private List<ApprovedTask> approvalTaskRecordList;
}
