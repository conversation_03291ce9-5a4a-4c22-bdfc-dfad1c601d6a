package com.zte.leadinfo.leadinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.zte.leadinfo.leadinfo.entity.CustomerInfoDO;
import com.zte.leadinfo.leadinfo.entity.SOptyDO;
import com.zte.leadinfo.leadinfo.mapper.SOptyMapper;
import com.zte.leadinfo.leadinfo.service.SOptyService;
import com.zte.opty.sync.domain.dto.OptySyncQueryDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.zte.mcrm.common.consts.CommonConstant.COMMON_FLAG_R;

/**
 * 服务接口实现
 *
 * <AUTHOR>
 * @description 由 Mybatisplus Code Generator 创建
 * @since 2024-06-04 10:52:11
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SOptyServiceImpl extends ServiceImpl<SOptyMapper, SOptyDO> implements SOptyService {

    @Override
    public List<SOptyDO> listByIds(List<String> optyRowIds) {
        List<SOptyDO> res = Lists.newArrayList();
        if (CollectionUtils.isEmpty(optyRowIds)) {
            return res;
        }

        optyRowIds = optyRowIds.stream().filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        return this.getBaseMapper().selectBatchIds(optyRowIds);
    }

    /**
     * 更新迁移结果
     * @param rowIds 主键ID
     * @param result 迁移结果
     * @param message 迁移信息
     * @return
     */
    @Override
    public int updateMigrationResult(List<String> rowIds, String result, String message) {
        if (CollectionUtils.isEmpty(rowIds)) {
            return 0;
        }
        LambdaUpdateWrapper<SOptyDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(SOptyDO::getMigrationStatus, result)
                .set(SOptyDO::getMigrationTime, new Date())
                .set(SOptyDO::getMigrationMessage, message)
                .in(SOptyDO::getRowId, rowIds);
        return this.getBaseMapper().update(updateWrapper);
    }

    /**
     * 动态sql，根据入参条件查询id
     * @param params
     * @return
     */
    @Override
    public List<String> selectIdsByConditions(OptySyncQueryDTO params) {
        return this.baseMapper.selectIdsByConditions(params);
    }

    @Override
    public synchronized List<String> loadUndoOptyIds(int batchSize) {
        // 获取待处理（N）的商机id，并修改为处理中（R）
        List<String> rowIds = this.baseMapper.loadUndoOptyIds(batchSize);
        this.updateMigrationResult(rowIds, COMMON_FLAG_R, "");
        return rowIds;
    }

    @Override
    public List<CustomerInfoDO> selectAll() {
        return baseMapper.selectAll();
    }
}