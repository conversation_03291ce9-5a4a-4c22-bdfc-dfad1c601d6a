package com.zte.mcrm.channel.cron;

import com.zte.itp.timedjobs.annotation.ZTEJobWorker;
import com.zte.itp.timedjobs.api.ZteJobContext;
import com.zte.itp.timedjobs.api.ZteJobInterface;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.channel.model.entity.OpportunityKeyInfoEntity;
import com.zte.mcrm.channel.service.channel.ApprovalInfoService;
import com.zte.mcrm.channel.service.channel.IOpportunityMonthReportService;
import com.zte.mcrm.channel.service.channel.IOpportunityService;
import com.zte.mcrm.channel.util.ExceptionMsgUtils;
import com.zte.mcrm.common.access.vo.ComDictionaryMaintainVO;
import com.zte.mcrm.common.business.service.IComDictionaryMaintainService;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.logger.bussiness.service.LoggerService;
import com.zte.opty.common.enums.OptyStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
@ZTEJobWorker(value = "MonthlyOpportunityInvalidTask")
/*
  商机失效未及时更新月报的商机定时任务，每月1号00:05触发
 */
public class MonthlyOpportunityInvalidTask implements ZteJobInterface {
    @Autowired
    IOpportunityMonthReportService monthReportService;

    @Autowired
    IOpportunityService opportunityService;

    @Autowired
    IComDictionaryMaintainService comDictionaryMaintainService;

    @Autowired
    private ApprovalInfoService approvalInfoService;

    @Autowired
    private LoggerService loggerService;

    /**
     * -事件触发接口，执行回调
     *
     * @param context
     */
    @Override
    public void execute(ZteJobContext context) {
        int shardingItem = context.getShardingItem();
        if (shardingItem != OpportunityConstant.FIRST_SHARDING) {
            return;
        }

        try {
            log.info("---------------------MonthlyOpportunityInvalidTask开始执行---------------" + "当前时间:{}", new Date());
            monthlyOpportunityInvalidAndReminder();
            log.info("---------------------MonthlyOpportunityInvalidTask执行完成---------------" + "当前时间:{}", new Date());
        } catch (Exception e) {
            log.error("MonthlyOpportunityInvalidTask执行出错, Exception:", e);
        }
    }

    public void monthlyOpportunityInvalidAndReminderWithVerification(){
        // 当前月的月报归属期
        LocalDateTime localDateTime = LocalDateTime.now();
        if (localDateTime.getDayOfMonth() > 1){
            log.info("当前时间不是月初1号，不执行定时任务");
            return;
        }

        monthlyOpportunityInvalidAndReminder();

    }

    public void monthlyOpportunityInvalidAndReminder(){

        if (!taskSwitch()){
            log.info("MonthlyOpportunityInvalidTask开关未打开");
            return;
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(OpportunityConstant.DATE_FORMAT_YYYYMM);
        String lastReportMonth = LocalDateTime.now().plusMonths(-1L).withDayOfMonth(1).format(formatter);
        List<OpportunityKeyInfoEntity> opportunityKeyInfoEntities = monthReportService.getMonthlyInvalidOptys(lastReportMonth);
        log.info("getMonthlyInvalidOptys,opportunityKeyInfoEntities:{}", opportunityKeyInfoEntities.size());
        List<String> flowInstances = opportunityKeyInfoEntities.stream().map(OpportunityKeyInfoEntity::getFlowInstanceId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        Map<String, List<String>> approverList = approvalInfoService.getApproverListMap(flowInstances);
        for (OpportunityKeyInfoEntity opportunityKeyInfoEntity : opportunityKeyInfoEntities) {
            try {
                opportunityService.updateStatus(opportunityKeyInfoEntity.getRowId(), OptyStatusEnum.OPTY_SUSPEND);
                List<String> internalReceivers = approverList.getOrDefault(opportunityKeyInfoEntity.getFlowInstanceId(), new ArrayList<>());
                internalReceivers.add(opportunityKeyInfoEntity.getBusinessManagerId());
                opportunityService.sendMail(opportunityKeyInfoEntity.getRowId(),
                        internalReceivers,
                        OpportunityConstant.OPPORTUNITY_EMAIL_FOR_MONTHLY_INVALID_REMINDER_INTERNAL,
                        OpportunityConstant.TEMPLATE_TYPE_NOTIFICATION);
                opportunityService.sendMail(opportunityKeyInfoEntity.getRowId(),
                        Collections.singletonList(opportunityKeyInfoEntity.getCreatedBy()),
                        OpportunityConstant.OPPORTUNITY_EMAIL_FOR_MONTHLY_INVALID_REMINDER_EXTERNAL,
                        OpportunityConstant.TEMPLATE_TYPE_NOTIFICATION);
            }catch (Exception e){
                log.error("monthlyOpportunityInvalidAndReminder error,rowId:{}", opportunityKeyInfoEntity.getRowId(), e);
                String logStr = "rowId: " + opportunityKeyInfoEntity.getRowId() + "Error,Exception: " + ExceptionMsgUtils.getStackTrace(e, 2000);
                loggerService.saveLogger(logStr, "MonthlyOpportunityInvalidTask");
            }
        }
    }

    private boolean taskSwitch(){
        // 从数据库获取任务开关值
        List<ComDictionaryMaintainVO> comDictionaryMaintains = comDictionaryMaintainService.queryByType("monthlyOpportunityInvalidTaskSwitch");
        if (CollectionUtils.isNotEmpty(comDictionaryMaintains)) {
            String taskSwitchCode = comDictionaryMaintains.get(0).getCode();
            return CommonConst.Y.equals(taskSwitchCode);
        }
        return false;
    }
}
