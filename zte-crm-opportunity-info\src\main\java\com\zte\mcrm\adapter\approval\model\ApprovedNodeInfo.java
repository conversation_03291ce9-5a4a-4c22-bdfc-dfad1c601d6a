package com.zte.mcrm.adapter.approval.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.Map;

/**
 * @description:
 * @author: 10243305
 * @date: 2021/8/23 上午10:36
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ApprovedNodeInfo extends ApprovingNodeInfo {
    @ApiModelProperty(value = "流程编码")
    private String flowCode;

    @ApiModelProperty(value = "审批任务ID")
    private String taskId;

    @ApiModelProperty(value = "流程实例ID")
    private String flowInstanceId;

    @ApiModelProperty(value = "关联业务ID")
    private String businessId;

    @ApiModelProperty(value = "节点ID")
    private String nodeId;

    @ApiModelProperty(value = "节点类型")
    private String nodeType;

    @ApiModelProperty(value = "节点名称")
    private String nodeName;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "业务字段")
    private Map<String, Object> businessParam;

    @ApiModelProperty(value = "审批结果")
    private String result;
    @ApiModelProperty(value = "审批意见")
    private String opinion;
    @ApiModelProperty(value = "审批扩展意见")
    private String extOpinion;
    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpdateDate;
}
