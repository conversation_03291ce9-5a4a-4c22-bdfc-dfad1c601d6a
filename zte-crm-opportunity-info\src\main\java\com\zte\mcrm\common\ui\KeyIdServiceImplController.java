package com.zte.mcrm.common.ui;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import com.zte.mcrm.common.business.service.KeyIdServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 快速编码类型 spring mvc控制类
 * <AUTHOR> 10269210
 * @date 2021/06/01
 */
@Api(tags = "主键id API")
@RestController
@RequestMapping("/keyid")
public class KeyIdServiceImplController {

	/** 日志对象 */
	private static final Logger log = LoggerFactory.getLogger(KeyIdServiceImplController.class);
	
	/** 服务对象，SPRING自动装配 */
	@Autowired
	KeyIdServiceImpl keyIdServiceImpl ;
	
	@ApiOperation("获取下一个主键id | 曾吉祥10269210")
    @GetMapping(value="/next")
    public ServiceData<String> get() {
        //返回统一的服务端数据
        return ServiceResultUtil.success(keyIdServiceImpl.getKeyId());
	}


}