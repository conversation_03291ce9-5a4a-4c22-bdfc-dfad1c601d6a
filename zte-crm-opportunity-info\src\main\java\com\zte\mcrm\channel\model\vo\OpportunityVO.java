package com.zte.mcrm.channel.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@ToString
public class OpportunityVO {

    @ApiModelProperty(value = "主键")
    private String rowId;

    @ApiModelProperty(value = "商机编码")
    private String optyCd;

    @ApiModelProperty(value = "报备时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date created;

    @ApiModelProperty(value = "商机创建人")
    private String createdBy;

    @ApiModelProperty(value = "商机状态编码")
    private String statusCd;

    @ApiModelProperty(value = "商机状态")
    private String statusCdName;

    @ApiModelProperty(value = "商机来源编码")
    private String dataSource;

    @ApiModelProperty(value = "商机来源")
    private String dataSourceName;

    @ApiModelProperty(value = "商机名称")
    private String attrib46;

    @ApiModelProperty(value = "预计签单金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "产品名称")
    private String prodLv2Name;

    @ApiModelProperty(value = "最终客户名称")
    private String finalCustomerName;

    @ApiModelProperty(value = "最终客户编码")
    private String finalCustomerCode;

    @ApiModelProperty(value = "授权状态编码")
    private String statusAuth;

    @ApiModelProperty(value = "授权状态")
    private String statusAuthName;

    @ApiModelProperty(value = "月报状态编码")
    private String statusReport;

    @ApiModelProperty(value = "月报状态")
    private String statusReportName;

    @ApiModelProperty("商机当前状态")
    private String currentStatus;

    @ApiModelProperty(value = "操作按钮")
    private OperationButtonVO operationButton;

    @ApiModelProperty(value = "商机所属部门编号")
    private String deptNo;

    @ApiModelProperty(value = "商机所属部门名")
    private String deptName;

    @ApiModelProperty(value = "激活次数")
    private Integer activeCount;


    /**
     * 最终用户子行业编码
     */
    @JsonIgnore
    private String finalCustomerTradeChildCode;

    /**
     * 竞标截止日期
     */
    @JsonIgnore
    private Date biddingDeadline;
    /**
     * 赢率编码
     */
    @JsonIgnore
    private String winRate;
    /**
     * 赢率
     */
    @JsonIgnore
    private String winRateName;
    /**
     * 招标方全称
     */
    @JsonIgnore
    private String bidProviderName;

    @ApiModelProperty(value = "当前阶段编码")
    @JsonIgnore
    private String projectPhasesCode;

    @ApiModelProperty(value = "当前阶段")
    @JsonIgnore
    private String projectPhasesName;

    @ApiModelProperty(value = "招标类型编码")
    @JsonIgnore
    private String tendTypeCode;

    @ApiModelProperty(value = "招标类型名称")
    @JsonIgnore
    private String tendTypeName;
    @ApiModelProperty(value = "预计发标/议标日期")
    @JsonIgnore
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date estimatedBiddingTime;
    @ApiModelProperty(value = "渠道商")
    @JsonIgnore
    private String channelBusiness;
    @ApiModelProperty(value = "业务经理姓名")
    private String businessManagerName;
    @JsonIgnore
    @ApiModelProperty(value = "业务经理id")
    private String businessManagerId;

}
