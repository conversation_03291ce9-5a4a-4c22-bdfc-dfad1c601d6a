package com.zte.mcrm.adapter.constant;

import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.util.CommonUtils;

public enum ProjectAuthStatusEnum {

    /** 草稿 */
    DRAFT("draft", "草稿", "Draft"),
    /** 授权审批中 */
    AUTH_APPROVING("auth_approving", "授权审批中","Being approved"),
    /** 已授权 */
    AUTHORIZED("authorized", "已授权", "Authorized"),
    /** 审批不通过 */
    AUTH_REJECTED("auth_rejected", "审批不通过", "Not approved"),
    /** 授权已过期 */
    INVALID("invalid", "授权已过期", "Authorization has expired"),
    /** 撤销审批中 */
    WITHDRAWAL_APPROVING("withdrawal_approving", "撤销审批中", "Revoking approval in progress"),
    /** 授权已撤销 */
    WITHDRAWAL_APPROVED("withdrawal_approved", "授权已撤销", "Authorization has been revoked"),
    /** 已关闭 */
    CLOSED("closed", "已关闭", "Closed"),
    /** 未申请  没有对应的授权申请单，或者有对应的授权申请，授权状态全部为：草稿、审批不通过 */
    NOT_APPLIED("notApplied", "未申请", "Not applied"),
    /** 申请中 有对应的授权申请，授权状态全部为：授权审批中 */
    APPLYING("applying", "申请中", "Applying"),
    /** 已申请 有对应的授权申请，授权状态有其中1个：已授权、撤销审批中、已关闭、授权已过期、授权已撤销 */
    APPLIED("applied", "已申请", "Applied");


    private final String code;

    private final String cnName;

    private final String enName;

    ProjectAuthStatusEnum(String code, String cnName, String enName) {
        this.code = code;
        this.cnName = cnName;
        this.enName = enName;
    }

    public String getCode() {
        return code;
    }

    public String getCnName() {
        return cnName;
    }

    public String getEnName() {
        return enName;
    }

    public static ProjectAuthStatusEnum valueOfCode(String code){
        for (ProjectAuthStatusEnum value : ProjectAuthStatusEnum.values()) {
            if(value.code.equals(code)){
                return value;
            }
        }
        return null;
    }

    public static String getNameByCode(String code){
        boolean isZh = CommonConst.ZH_CN.equalsIgnoreCase(CommonUtils.getxLangId());
        for (ProjectAuthStatusEnum value : ProjectAuthStatusEnum.values()) {
            if(value.code.equals(code)){
                return isZh? value.getCnName() : value.getEnName();
            }
        }
        return code;
    }

    public static boolean judgeProjectAuthStatus1(String code){
        return DRAFT.getCode().equals(code)
                || AUTH_REJECTED.getCode().equals(code);
    }

    public static boolean judgeProjectAuthStatus2(String code){
        return AUTH_APPROVING.getCode().equals(code);
    }

    public static boolean judgeProjectAuthStatus3(String code){
        return AUTHORIZED.getCode().equals(code)
                || WITHDRAWAL_APPROVING.getCode().equals(code)
                || CLOSED.getCode().equals(code)
                || INVALID.getCode().equals(code)
                || WITHDRAWAL_APPROVED.getCode().equals(code);
    }

    public static boolean judgeProjectAuthStatus4(String code){
        return AUTHORIZED.getCode().equals(code)
                || WITHDRAWAL_APPROVING.getCode().equals(code)
                || AUTH_APPROVING.getCode().equals(code)
                || CLOSED.getCode().equals(code)
                || INVALID.getCode().equals(code)
                || WITHDRAWAL_APPROVED.getCode().equals(code);
    }

}
