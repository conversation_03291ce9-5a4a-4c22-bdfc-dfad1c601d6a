package com.zte.mcrm.adapter.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;


/**
 * 根据 快码类型　批量查询　快码值 数据结构
 * <AUTHOR> 10305348
 * @date 2021/08/16
 */

@Setter
@Getter
@ToString
@ApiModel(description = "批量查询快码值数据结构")
public class BulkCodeValuesByOneTypeVo {

    @ApiModelProperty(value = "主键codeTypeid")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long codeTypeOid;

    @ApiModelProperty(value = "编码类型")
    private String codeType;

    @ApiModelProperty(value = "编码名称")
    private String codeTypeName;

    private List<PrmQuickCodeValueVO> codeValueVOList;

}
