package com.zte.aiagent.domain.event;

import com.zte.aiagent.common.enums.EventTypeEnum;
import com.zte.aiagent.common.util.IdUtils;
import com.zte.aiagent.domain.shared.event.DomainEvent;
import com.zte.aiagent.domain.valueobject.DocumentSliceInfo;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文档创建事件
 * 当新文档被创建时发布
 */
@Getter
public class DocumentSlicedEvent implements DomainEvent {

    private final String eventId;
    private final String documentId;
    private final List<DocumentSliceInfo> sliceInfos;
    private final Long tenantId;
    private final LocalDateTime occurredOn;

    public DocumentSlicedEvent(String documentId,
                               List<DocumentSliceInfo> sliceInfos,
                               Long tenantId) {
        this.eventId = IdUtils.generateNewId();
        this.documentId = documentId;
        this.sliceInfos = sliceInfos;
        this.tenantId = tenantId;
        this.occurredOn = LocalDateTime.now();
    }

    @Override
    public String getAggregateId() {
        return this.documentId;
    }

    @Override
    public String getEventType() {
        return EventTypeEnum.DOCUMENT_SLICED.getCode();
    }
}
