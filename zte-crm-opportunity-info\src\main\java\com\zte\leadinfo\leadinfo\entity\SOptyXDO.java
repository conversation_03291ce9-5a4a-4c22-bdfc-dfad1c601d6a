package com.zte.leadinfo.leadinfo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@TableName(value = "s_opty_x")
@Data
public class SOptyXDO implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "ROW_ID")
    private String rowId;

    /**
     * 商机名称
     */
    @TableField(value = "ATTRIB_46")
    private String attrib46;

    /**
     *
     */
    @TableField(value = "NATIONAL_AREA_ID")
    private String nationalAreaId;

    /**
     *
     */
    @TableField(value = "NATIONAL_AREA_PROVINCE_ID")
    private String nationalAreaProvinceId;

    /**
     *
     */
    @TableField(value = "NATIONAL_AREA_NAME")
    private String nationalAreaName;

    /**
     *
     */
    @TableField(value = "NATIONAL_AREA_CITY_ID")
    private String nationalAreaCityId;

    /**
     * 预计签单金额
     */
    @TableField(value = "X_TOTAL_AMOUNT")
    private BigDecimal xTotalAmount;

    /**
     * 币种Id
     */
    @TableField(value = "CURRENCY_ID")
    private String currencyId;

    /**
     * 币种code
     */
    @TableField(value = "CURRENCY_CODE")
    private String currencyCode;

    /**
     * 客户类型
     */
    @TableField(value = "OPTY_TYPE")
    private String optyType;

    /**
     * 销售模式
     */
    @TableField(value = "SALES_TYPE")
    private String salesType;

    /**
     * 最终用途
     */
    @TableField(value = "FINAL_USAGE")
    private String finalUsage;

    /**
     * 最终用户类型
     */
    @TableField(value = "end_user_type")
    private String endUserType;

    /**
     * 最终用户的最终用途
     */
    @TableField(value = "enduse_of_enduser")
    private String enduseOfEnduser;

    /**
     * 具体客户描述
     */
    @TableField(value = "specific_customer_desc")
    private String specificCustomerDesc;

    /**
     * 具体用途描述
     */
    @TableField(value = "specific_usage_desc")
    private String specificUsageDesc;

    /**
     * 最终客户Id
     */
    @TableField(value = "X_LAST_ACC_ID")
    private String xLastAccId;

    /**
     * 国内国际
     */
    @TableField(value = "X_AREA")
    private String xArea;

    /**
     * 子行业
     */
    @TableField(value = "CHILD_TRADE")
    private String childTrade;

    /**
     * 行业
     */
    @TableField(value = "PARENT_TRADE")
    private String parentTrade;

    /**
     * 市场类型
     */
    @TableField(value = "MARKET_TYPE")
    private String marketType;

    /**
     * 服务属性
     */
    @TableField(value = "X_PROJECT_LABEL")
    private String xProjectLabel;

    /**
     *
     */
    @TableField(value = "GROUP_FLG")
    private String groupFlg;

    /**
     * 客户对服务销售的接受程度
     */
    @TableField(value = "SER_ACCEPT")
    private String serAccept;

    /**
     * 产品&方案竞争能力-运营商系统
     */
    @TableField(value = "PROD_ABILITY")
    private String prodAbility;

    /**
     * 产品&方案竞争能力-运营商服务
     */
    @TableField(value = "PROD_ABILITY_2")
    private String prodAbility2;

    /**
     * 产品&方案竞争能力-政企网系统
     */
    @TableField(value = "PROD_ABILITY_3")
    private String prodAbility3;

    /**
     * 产品&方案竞争能力-政企网服务
     */
    @TableField(value = "PROD_ABILITY_4")
    private String prodAbility4;

    /**
     * 客户关系-运营商系统
     */
    @TableField(value = "ACCNT_RELATION")
    private String accntRelation;

    /**
     * 客户关系-运营商服务
     */
    @TableField(value = "ACCNT_RELATION_2")
    private String accntRelation2;

    /**
     * 客户关系-政企网系统
     */
    @TableField(value = "ACCNT_RELATION_3")
    private String accntRelation3;

    /**
     * 客户关系-政企网服务
     */
    @TableField(value = "ACCNT_RELATION_4")
    private String accntRelation4;

    /**
     * 竞争难度-运营商系统
     */
    @TableField(value = "HARD_1")
    private String hard1;

    /**
     * 竞争难度-运营商服务
     */
    @TableField(value = "HARD_2")
    private String hard2;

    /**
     * 竞争难度-政企网系统
     */
    @TableField(value = "HARD_3")
    private String hard3;

    /**
     * 竞争难度-政企网服务
     */
    @TableField(value = "HARD_4")
    private String hard4;

    /**
     * 预计毛利率-运营商系统
     */
    @TableField(value = "RATE")
    private BigDecimal rate;

    /**
     * 预计毛利率-运营商服务
     */
    @TableField(value = "RATE_2")
    private BigDecimal rate2;

    /**
     * 预计毛利率-政企网系统
     */
    @TableField(value = "RATE_3")
    private BigDecimal rate3;

    /**
     * 预计毛利率-政企网服务
     */
    @TableField(value = "RATE_4")
    private BigDecimal rate4;

    /**
     * 运营商系统-成功概率（人工评估）
     */
    @TableField(value = "ATTRIB_19")
    private BigDecimal attrib19;

    /**
     * 运营商服务-成功概率（人工评估）
     */
    @TableField(value = "ATTRIB_20")
    private BigDecimal attrib20;

    /**
     * 政企网系统-成功概率（人工服务）
     */
    @TableField(value = "ATTRIB_21")
    private BigDecimal attrib21;

    /**
     * 政企网服务-成功概率（人工服务）
     */
    @TableField(value = "ATTRIB_22")
    private BigDecimal attrib22;

    /**
     *
     */
    @TableField(value = "MUL_DIVISION_FLG")
    private String mulDivisionFlg;

    /**
     * 商机等级-手工
     */
    @TableField(value = "OPPTY_LEVEL_MANUAL")
    private String opptyLevelManual;

    /**
     * 商机等级-系统
     */
    @TableField(value = "OPPTY_LEVEL_SYSTEM")
    private String opptyLevelSystem;

    /**
     * 客户投资规模(万)
     */
    @TableField(value = "X_CUST_INVEST_AMOUNT")
    private BigDecimal xCustInvestAmount;

    /**
     * 预计签单时间
     */
    @TableField(value = "DATE_2")
    private Date date2;

    /**
     * 商机背景
     */
    @TableField(value = "NOTES_2")
    private String notes2;

    /**
     *
     */
    @TableField(value = "IS_FROM_PRM")
    private String isFromPrm;

    /**
     * 预计发标/议标时间
     */
    @TableField(value = "DATE_1")
    private Date date1;

    /**
     * 商机来源
     */
    @TableField(value = "OPPTY_SOURCE")
    private String opptySource;

    /**
     * 是否融资项目
     */
    @TableField(value = "FUND_FLG")
    private String fundFlg;

    /**
     * 扩容/新建
     */
    @TableField(value = "NET_TYPE")
    private String netType;

    /**
     * MTO联调属性
     */
    @TableField(value = "X_MTO_UNION")
    private String xMtoUnion;

    /**
     * 成功概率-运营商系统
     */
    @TableField(value = "SUCC_PROB")
    private BigDecimal succProb;

    /**
     * 成功概率-运营商服务
     */
    @TableField(value = "SUCC_PROB_2")
    private BigDecimal succProb2;

    /**
     * 成功概率- 政企网系统
     */
    @TableField(value = "SUCC_PROB_3")
    private BigDecimal succProb3;

    /**
     * 成功概率-政企网服务
     */
    @TableField(value = "SUCC_PROB_4")
    private BigDecimal succProb4;

    /**
     * 商机阶段-OPTY_PHASE
     */
    @TableField(value = "X_OPTY_PHASE")
    private String xOptyPhase;

    /**
     * 商机排名
     */
    @TableField(value = "OPPTY_RANGE")
    private BigDecimal opptyRange;

    /**
     * 商机排名意见
     */
    @TableField(value = "OPPTY_RECOM")
    private String opptyRecom;

    /**
     * 线索Id
     */
    @TableField(value = "LEAD_ID")
    private String leadId;

    /**
     * 招标类型-ZTE_INVITE_TYPE
     */
    @TableField(value = "TEND_TYPE")
    private String tendType;

    /**
     * 关联商机，关闭商机需要选择商机
     */
    @TableField(value = "X_OPTY_ID")
    private String xOptyId;

    /**
     * 关闭原因ZTE_OPPTY_CLOSED_REASON
     */
    @TableField(value = "NOTES_4")
    private String notes4;

    /**
     *
     */
    @TableField(value = "DATA_SOURCE")
    private String dataSource;

    /**
     * 值内容等同于row_id，用于oracle odi
     */
    @TableField(value = "PAR_ROW_ID")
    private String parRowId;

    /**
     *
     */
    @TableField(value = "PROJECT_TYPE")
    private String projectType;

    /**
     * 项目执行地
     */
    @TableField(value = "BU_ID_2")
    private String buId2;

    /**
     * 商机等级Siebel系统使用
     */
    @TableField(value = "OPPTY_LEVEL")
    private String opptyLevel;

    /**
     * 运营商系统签单金额
     */
    @TableField(value = "AMT")
    private BigDecimal amt;

    /**
     * 运营商服务签单金额
     */
    @TableField(value = "AMT_2")
    private BigDecimal amt2;

    /**
     * 政企网系统签单金额
     */
    @TableField(value = "AMT_3")
    private BigDecimal amt3;

    /**
     * 政企网服务签单金额
     */
    @TableField(value = "AMT_4")
    private BigDecimal amt4;

    /**
     * 客户属性
     */
    @TableField(value = "ACCOUNT_ATTRIBUTE")
    private String accountAttribute;

    /**
     * 潜在融资模式
     */
    @TableField(value = "POTENTIAL_MODEL")
    private String potentialModel;

    /**
     * 二级经销商
     */
    @TableField(value = "SECOND_DEALER_ID")
    private String secondDealerId;

    /**
     * 最终用户名称
     */
    @TableField(value = "x_last_acc_name")
    private String xLastAccName;

    /**
     * 最终用户状态字段，默认值为0-无状态；1:待创建客户草稿；2已创建客户草稿；3：创建客户草稿后生效的客户；4 客户系统本身已存在生效客户；
     */
    @TableField(value = "last_acc_status")
    private Integer lastAccStatus;

    /**
     * 最终用户地址
     */
    @TableField(value = "final_customer_address")
    private String finalCustomerAddress;

    /**
     * 最终用户行业编码
     */
    @TableField(value = "final_customer_parent_trade")
    private String finalCustomerParentTrade;

    /**
     * 最终用户子行业编码
     */
    @TableField(value = "final_customer_child_trade")
    private String finalCustomerChildTrade;

    /**
     * 最终用户联系人姓名
     */
    @TableField(value = "final_customer_contact_name")
    private String finalCustomerContactName;

    /**
     * 最终用户联系人电话
     */
    @TableField(value = "final_customer_contact_phone")
    private String finalCustomerContactPhone;

    /**
     * 最终用户联系人邮箱
     */
    @TableField(value = "final_customer_contact_email")
    private String finalCustomerContactEmail;

    /**
     * 代_x表处/办事处
     */
    @TableField(value = "dept_no")
    private String deptNo;

    /**
     * 项目当前阶段编码
     */
    @TableField(value = "project_phases_code")
    private String projectPhasesCode;

    /**
     * 赢率
     */
    @TableField(value = "win_rate")
    private String winRate;

    /**
     * 招标类型编码
     */
    @TableField(value = "tender_type_code")
    private String tenderTypeCode;

    /**
     * 招标方全称
     */
    @TableField(value = "bid_provider_name")
    private String bidProviderName;

    /**
     * 竞标截止日期
     */
    @TableField(value = "bidding_deadline")
    private Date biddingDeadline;

    /**
     * 报备人姓名
     */
    @TableField(value = "agency_name")
    private String agencyName;

    /**
     * 报备人电话
     */
    @TableField(value = "agency_phone")
    private String agencyPhone;

    /**
     * 报备人邮箱
     */
    @TableField(value = "agency_email")
    private String agencyEmail;

    /**
     * 中兴业务经理id
     */
    @TableField(value = "business_manager_id")
    private String businessManagerId;

    /**
     * 中兴业务经理名字
     */
    @TableField(value = "business_manager_name")
    private String businessManagerName;

    /**
     * 项目指委会主任
     */
    @TableField(value = "director_of_psc")
    private String directorOfPsc;

    /**
     * 商机概况
     */
    @TableField(value = "project_desc")
    private String projectDesc;

    /**
     * 确认设备自用
     */
    @TableField(value = "self_use_flag")
    private String selfUseFlag;

    /**
     * 渠道商名称
     */
    @TableField(value = "customer_name")
    private String customerName;

    /**
     * 报备代理商级别
     */
    @TableField(value = "agency_level_name")
    private String agencyLevelName;

    /**
     * 代理商级别编码
     */
    @TableField(value = "agency_level_code")
    private String agencyLevelCode;

    /**
     * 预留字段1
     */
    @TableField(value = "reserved_field1")
    private String reservedField1;

    /**
     * 预留字段2
     */
    @TableField(value = "reserved_field2")
    private String reservedField2;

    /**
     * 预留字段3
     */
    @TableField(value = "reserved_field3")
    private String reservedField3;

    /**
     * 预留字段4
     */
    @TableField(value = "reserved_field4")
    private String reservedField4;

    /**
     * 预留字段5
     */
    @TableField(value = "reserved_field5")
    private String reservedField5;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id")
    private Long tenantId;

    /**
     * 有效标记(Y/N)
     */
    @TableField(value = "enabled_flag")
    private String enabledFlag;

    /**
     * 最终用户是否受限制主体
     */
    @TableField(value = "final_customer_restriction_flag")
    private String finalCustomerRestrictionFlag;

    /**
     * 渠道商是否受限制主体
     */
    @TableField(value = "agency_restriction_flag")
    private String agencyRestrictionFlag;

    /**
     * TS审批单号
     */
    @TableField(value = "ts_approval_number")
    private String tsApprovalNumber;

    /**
     * 激活次数
     */
    @TableField(value = "active_count")
    private Integer activeCount;

    /**
     * 是否属于激活报备(Y/N)
     */
    @TableField(value = "from_active_flag")
    private String fromActiveFlag;

    /**
     * 从哪个商机激活的(记录商机id)
     */
    @TableField(value = "from_active_opty")
    private String fromActiveOpty;

    /**
     * 渠道商客户编码
     */
    @TableField(value = "crm_customer_code")
    private String crmCustomerCode;

    /**
     *
     */
    @TableField(value = "CREATED")
    private Date created;

    /**
     *
     */
    @TableField(value = "CREATED_BY")
    private String createdBy;

    /**
     *
     */
    @TableField(value = "LAST_UPD")
    private Date lastUpd;

    /**
     *
     */
    @TableField(value = "LAST_UPD_BY")
    private String lastUpdBy;

    /**
     * 变更前渠道商名称
     */
    @TableField(value = "source_customer_name")
    private String sourceCustomerName;

    /**
     * 变更前渠道商客户编码
     */
    @TableField(value = "source_crm_customer_code")
    private String sourceCrmCustomerCode;

}