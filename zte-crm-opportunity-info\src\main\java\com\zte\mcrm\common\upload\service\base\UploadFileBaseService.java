package com.zte.mcrm.common.upload.service.base;

import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.common.upload.model.entity.ComUploadFile;

import java.util.List;
import java.util.Map;


/**
 *  服务接口类
 * <AUTHOR>
 * @date 2021/06/05  
 */
public interface UploadFileBaseService{
    /**
     * 根据ID查询
     * @param oid 主键ID
     * @return 实体
     * <AUTHOR>
     * @date 2021/06/05
     */
    ComUploadFile get(Long oid);

    /**
     * 查询列表
     * @param entity 参数集合
     * @return 实体集合
     * <AUTHOR>
     * @date 2021/06/05
     */
	List<ComUploadFile> getList(ComUploadFile entity);

    /**
     * 删除
     * @param oid 主键ID
     * @return 删除记录个数
     * <AUTHOR>
     * @date 2021/06/05
     */
	int delete(Long oid);

    /**
     * 新增
     * @param entity 实体对象
     * @return 新增记录个数
     * <AUTHOR>
     * @date 2021/06/05
     */
	int insert(ComUploadFile entity);

    /**
     * 更新
     * @param entity 实体对象
     * @return 修改记录个数
     * <AUTHOR>
     * @date 2021/06/05
     */
	int update(ComUploadFile entity);

    /**
     * 统计
     * @param map 参数集合
     * @return 统计总数
     * <AUTHOR>
     * @date 2021/06/05
     */
	long getCount(Map<String, Object> map);

   /**
    * 分页查询
    * @param map 参数集合
    * @return 实体集合
    * <AUTHOR>
    * @date 2021/06/05
    */
	List<ComUploadFile> getPage(Map<String, Object> map);

    /**
    * 分页查询
    * @param form 参数集合
    * @return 实体集合
    * <AUTHOR>
    * @date 2021/06/05
    */
    PageRows<ComUploadFile> getPageRows(FormData<ComUploadFile> form);

    /**
     * 根据给定的已上传附件的OID更新附件次序
     * <AUTHOR>
     * @param list
     *
     * 已上传附件的OID列表
     * @return
     */
    int updateUploadFileOrder(List<ComUploadFile> list);

    /**
     * 批量查询业务附件
     * @param uploadType
     * @param billIds
     * @return
     */
    List<ComUploadFile> queryUploadFileInBatch(String uploadType, List<String> billIds);

    /**
     * 模糊统计
     * @param map 参数集合
     * @return 统计总数
     * <AUTHOR>
     * @date 2021/06/05
     */
    long getFuzzyCount(Map<String, Object> map);

    /**
     * 模糊分页查询
     * @param map 参数集合
     * @return 实体集合
     * <AUTHOR>
     * @date 2021/06/05
     */
    List<ComUploadFile> getFuzzyPage(Map<String, Object> map) throws Exception;


    /**
     * @param entity
     * @return
     */
     int deleteByDmeKey(ComUploadFile entity);

//    /**
//     * 批量更新文件密级
//     * @param dmeKeyList, secretLevel
//     * @return 是否成功修改
//     * <AUTHOR> 10305348
//     * @date 2021/09/02
//     */
//    Boolean updateUploadFileSecretLevel(List<String> dmeKeyList, int secretLevel) throws Exception;
}