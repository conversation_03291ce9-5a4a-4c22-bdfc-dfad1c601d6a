package com.zte.leadinfo.common.service.impl;

import com.zte.leadinfo.common.mapper.LstValMapper;
import com.zte.leadinfo.common.service.LstValService;
import com.zte.mcrm.lov.access.vo.ListOfValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Author:  刘自强10288407
 */
@Service
public class LstValServiceImpl implements LstValService {

    @Autowired
    LstValMapper lstValMapper;

    @Override
    public List<ListOfValue> getLovTypeValue(String lovType) {
        return lstValMapper.getLovTypeValue(lovType);
    }
}
