package com.zte.mcrm.common.enumeration;

/****
 *
 * <AUTHOR> @date 2021/2/10
 **/

public enum ServerEnum {
	//成功状态
	Success("0000"),
	//服务器错误
	ServerError("0001"),
	//校验失败
	AuthFailed("0002"),
	//拒绝许可
	PermissionDenied("0003"),
	//验证错误
	ValidationError("0004"),
	//业务错误
	BusinessError("0005"),
	//数据没有找到
	DataNotFound("0006"),
	//Siebe校验失败
	SiebelErrorAuthDenied("0007"),
	//siebel数据存储失败
	SiebelErrorDataCUDDenied("0008"),
	//siebel数据查找失败
	SiebelErrorDataNotFound("0009");
	private String code;
	/**
	 * 補上注釋，編號
	 * @param code
	 */
	private ServerEnum(String code){
		this.code = code;
	}
	public String getCode() {
		return code;
	}
}
