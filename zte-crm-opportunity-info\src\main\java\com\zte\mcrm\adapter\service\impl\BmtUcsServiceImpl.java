package com.zte.mcrm.adapter.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.crm.eva.base.infrastructure.client.IchannelBaseFeign;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.constant.UcsConstant;
import com.zte.mcrm.adapter.constant.UcsSpringProperties;
import com.zte.mcrm.adapter.model.dto.UcsUserInfoDTO;
import com.zte.mcrm.adapter.service.BmtUserServiceApi;
import com.zte.mcrm.adapter.service.IBmtUcsService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户中心接口调用实现类
 * <AUTHOR>
 *
 */
@Service
public class BmtUcsServiceImpl implements IBmtUcsService {
	/** 日志对象 */
	private static final Logger logger = LoggerFactory.getLogger(BmtUcsServiceImpl.class);

	@Autowired
	private BmtUserServiceApi bmtUcsServiceApi;
	@Autowired
	private IchannelBaseFeign ichannelBaseFeign;
	@Autowired
    private UcsSpringProperties ucsSpringProperties;

	/**
	 * 批量获取用户信息（包括内外部用户）
	 * <AUTHOR>
	 * @date 2021-06-04
	 * @param accountIdList accountId列表
	 * @return
	 */
	@Override
	public List<UcsUserInfoDTO> getUserInfoByAccountIdList(List<String> accountIdList) {
		try {
			logger.info("=====批量获取用户信息（包括内外部用户）====接口请求参数：" + accountIdList);
			List<UcsUserInfoDTO> userList = Lists.newArrayList();
			List<List<String>> parts = Lists.partition(accountIdList, UcsConstant.USER_CONTER_ACCOUNTID_BATCH_MAX);
			parts.forEach(list -> {
				// 添加到userList中
				ServiceData<List<UcsUserInfoDTO>> serviceData = ichannelBaseFeign.queryAll(list);
				if (serviceData != null && CollectionUtils.isNotEmpty(serviceData.getBo())) {
					logger.info("=====批量获取用户信息（包括内外部用户）====接口返回值：" + serviceData.getBo());
					List<UcsUserInfoDTO> ucsUserInfoList = serviceData.getBo();
					userList.addAll(ucsUserInfoList);
				} else {
					logger.error("getUserInfoByAccountIdList接口返回异常：{}", JSON.toJSONString(serviceData));
				}
			});
			return userList;
		} catch (Exception e) {
			logger.error("根据账号列表批量查询账号信息异常", e);
			return null;
		}
	}
}
