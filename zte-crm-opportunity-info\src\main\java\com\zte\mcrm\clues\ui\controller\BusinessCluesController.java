package com.zte.mcrm.clues.ui.controller;

import com.zte.itp.msa.core.locale.LocaleMessageSourceBean;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.clues.access.vo.BusinessClues;
import com.zte.mcrm.clues.access.vo.BusinessCluesInfoVO;
import com.zte.mcrm.clues.business.service.BusinessCluesService;
import com.zte.mcrm.clues.business.service.PCBusinessCluesService;
import com.zte.mcrm.clues.common.CluesSysConst;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.util.FormDataHelpUtil;
import com.zte.mcrm.common.util.RequestMessage;
import com.zte.mcrm.opportunity.common.OppSysConst;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.common.exception.SiebelErrorAuthDeniedException;
import com.zte.springbootframe.common.exception.ValidationException;
import com.zte.springbootframe.common.model.ServiceDataCopy;
import com.zte.springbootframe.common.model.ServiceDataUtil;
import com.zte.springbootframe.util.page.PageQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/****
 *
 * <AUTHOR> @date 2021/2/2
 **/
@RestController
@Api("线索相关API")
@Slf4j
public class BusinessCluesController {

	@Autowired
	private BusinessCluesService businessCluesService;
	@Autowired
	private PCBusinessCluesService pcbusinessCluesService;
	@Autowired
	private LocaleMessageSourceBean localeMessageSourceBean;
	
	@SuppressWarnings("unchecked")
	@ApiOperation("客户关联线索")
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", name = "acctId", dataType="String", dataTypeClass=String.class, required = false, value = "CRM系统客户ID", defaultValue = "1-130O07"),
			@ApiImplicitParam(paramType = "query", name = "acctNum", dataType="String", dataTypeClass=String.class, required = false, value = "客户编码", defaultValue = "GB000000003922"),
			@ApiImplicitParam(paramType = "query", name = "clueMsg", dataType="String", dataTypeClass=String.class, required = false, value = "线索名称/线索编号/归属经理", defaultValue = ""),
			@ApiImplicitParam(paramType = "query", name = "pageSize", dataType = "int", dataTypeClass = int.class, required = true, value = "页大小", defaultValue = "10"),
			@ApiImplicitParam(paramType = "query", name = "currentPage", dataType = "int", dataTypeClass = int.class, required = true, value = "当前页", defaultValue = "1"),
			@ApiImplicitParam(paramType = "query", name = "empNo", dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "6011000145"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "********"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, dataType="String", dataTypeClass=String.class, required = true, value = "租户ID", defaultValue = "pcOpportunity"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, dataType="String", dataTypeClass=String.class, required = true, value = "语言", defaultValue = "zh_CN") })
	@RequestMapping(value = "account/clue/page", method = RequestMethod.GET)
	public ServiceDataCopy<List<BusinessClues>> getaccountclueList(
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, required = true) String empShorNo,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, required = true) String tenantId,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, required = true) String lang,
			@RequestParam(name = "acctId", required = false) String acctId,
			@RequestParam(name = "acctNum", required = false) String acctNum,
			@RequestParam(name = "empNo", required = true) String empNo,
			@RequestParam(name = "clueMsg", required = false) String clueMsg,
			@RequestParam(name = "currentPage", required = true) int currentPage,
			@RequestParam(name = "pageSize", required = true) int pageSize) throws Exception {
		BusinessClues entity = new BusinessClues();
		entity.setEmpNo(empNo);
		entity.setClueMsg(clueMsg);
		entity.setAcctId(acctId);
		entity.setAcctNum(acctNum);
		entity.setEmpId(empNo);
		// 分页查询
		List<BusinessClues> businessCluesList = new ArrayList<>();
		PageQuery<BusinessClues> pageQuery = new PageQuery<>(entity, currentPage, pageSize);
		int countAllType = businessCluesService.accountCluesCount(pageQuery);
		if (countAllType > 0) {
			businessCluesList = businessCluesService.getAccountClues(pageQuery);
		}
		pageQuery.setCount(countAllType);
		return ServiceDataUtil.success(businessCluesList, pageQuery.getMapOfPageQuery());
	}
	
	@SuppressWarnings("unchecked")
	@ApiOperation("分页查看线索")
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", name = "clueMsg", dataType="String", dataTypeClass=String.class, required = false, value = "线索名称/线索编号/归属经理", defaultValue = ""),
			@ApiImplicitParam(paramType = "query", name = "pageSize", dataType = "int", dataTypeClass = int.class, required = true, value = "页大小", defaultValue = "10"),
			@ApiImplicitParam(paramType = "query", name = "currentPage", dataType = "int", dataTypeClass = int.class, required = true, value = "当前页", defaultValue = "1"),
			@ApiImplicitParam(paramType = "query", name = "empNo", dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "********"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "********"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, dataType="String", dataTypeClass=String.class, required = true, value = "租户ID", defaultValue = "pcOpportunity"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, dataType="String", dataTypeClass=String.class, required = true, value = "语言", defaultValue = "zh_CN") })
	@RequestMapping(value = "/clue/page", method = RequestMethod.GET)
	public ServiceDataCopy<List<BusinessClues>> getclueList(
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, required = true) String empShorNo,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, required = true) String tenantId,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, required = true) String lang,
			@RequestParam(name = "empNo", required = true) String empNo,
			@RequestParam(name = "clueMsg", required = false) String clueMsg,
			@RequestParam(name = "currentPage", required = true) int currentPage,
			@RequestParam(name = "pageSize", required = true) int pageSize) throws Exception {
		// 线索查询实体
		BusinessClues entity = new BusinessClues();
		entity.setEmpNo(empNo);
		entity.setClueMsg(clueMsg);
		entity.setEmpId(empNo);
		// 分页查询
		List<BusinessClues> businessCluesList = new ArrayList<>();
		PageQuery<BusinessClues> pageQuery = new PageQuery<>(entity, currentPage, pageSize);
		int count = businessCluesService.countClues(pageQuery);
		if (count > 0) {
			businessCluesList = businessCluesService.getCluesWithAuth(pageQuery);
		}
		pageQuery.setCount(count);
		return ServiceDataUtil.success(businessCluesList, pageQuery.getMapOfPageQuery());
	}

	@ApiOperation("查看线索详情")
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", name = "id", dataType="String", dataTypeClass=String.class, required = true, value = "线索ID", defaultValue = "1-3ARTTA"),
			@ApiImplicitParam(paramType = "query", name = "empNo", dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "********"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "6011000145"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, dataType="String", dataTypeClass=String.class, required = true, value = "租户ID", defaultValue = "pcOpportunity"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, dataType="String", dataTypeClass=String.class, required = true, value = "语言", defaultValue = "zh_CN") })
	@RequestMapping(value = "/clueDetail", method = RequestMethod.GET)
	public ServiceDataCopy<BusinessClues> getclueDetail(
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, required = true) String empShorNo,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, required = true) String tenantId,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, required = true) String lang,
			@RequestParam(name = "empNo", required = true) String empNo,
			@RequestParam(name = "id", required = true) String id) throws BusiException{
		BusinessClues entity = new BusinessClues();
		entity.setxEmpNo(empNo);
		entity.setId(id);
		entity.setEmpId(empNo);
		entity.setxLangId(lang);
		entity.setxTenantId(tenantId);
		//是否有查看权限
		boolean checkSelectAuth = businessCluesService.checkHasAuthToReadClue(empNo, id);
		if(!checkSelectAuth){
			return ServiceDataUtil.noAuthInBusiness(null);
		}
		BusinessClues businessClues = businessCluesService.selectBaseInfo(entity);
		//数据未找到
		if(null == businessClues){
			throw new BusiException("", localeMessageSourceBean.getMessage(CommonConst.DATA_NOT_FOUND));
		}
		return ServiceDataUtil.success(businessClues);
	}

	@ApiOperation("新建修改线索初始化")
    @ApiImplicitParams({
    	@ApiImplicitParam(paramType="header",name=SysGlobalConst.HTTP_HEADER_X_TENANT_ID,dataType="String", dataTypeClass=String.class, required=true,value="租户ID",defaultValue="siebel"),
    	@ApiImplicitParam(paramType="header",name=SysGlobalConst.HTTP_HEADER_X_LANG_ID,dataType="String", dataTypeClass=String.class, required=true,value="语言",defaultValue="zh"),
    	@ApiImplicitParam(paramType="header",name=SysGlobalConst.HTTP_HEADER_X_EMP_NO,dataType="String", dataTypeClass=String.class, required=true,value="工号",defaultValue="********"),
    	@ApiImplicitParam(paramType="query",name="empNo",dataType="String", dataTypeClass=String.class, required=true, value="工号",defaultValue="********"),
    	@ApiImplicitParam(paramType="query",name="clueId",dataType="String", dataTypeClass=String.class, required=false, value="线索ID",defaultValue="1-3ARTTA"),
    })
	@RequestMapping(value="/clues/init",method=RequestMethod.GET)
	public ServiceDataCopy<BusinessCluesInfoVO> initClues(
			@RequestHeader(SysGlobalConst.HTTP_HEADER_X_TENANT_ID)String tenantId,
			@RequestHeader(SysGlobalConst.HTTP_HEADER_X_LANG_ID)String lang,
			@RequestHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO)String xEmpNo, 
			@RequestParam(name="empNo",required = true)String empNo,
			@RequestParam(name="clueId",required = false)String clueId
			)throws BusiException{
		//初始化信息
		BusinessCluesInfoVO entity = new BusinessCluesInfoVO();
		entity.setId(clueId);
		entity.setxEmpNo(empNo);
		entity.setxLangId(lang);
		entity.setxTenantId(tenantId);
		return ServiceDataUtil.success(businessCluesService.initBusinessCluesInfo(entity));
	}
	

	@ApiOperation("新建线索")
    @ApiImplicitParams({
    	@ApiImplicitParam(paramType="header",name=SysGlobalConst.HTTP_HEADER_X_TENANT_ID,dataType="String", dataTypeClass=String.class, required=true,value="租户ID",defaultValue="pcOpportunity"),
    	@ApiImplicitParam(paramType="header",name=SysGlobalConst.HTTP_HEADER_X_LANG_ID,dataType="String", dataTypeClass=String.class, required=true,value="语言",defaultValue="zh_CN"),
    	@ApiImplicitParam(paramType="header",name=SysGlobalConst.HTTP_HEADER_X_EMP_NO,dataType="String", dataTypeClass=String.class, required=true,value="工号",defaultValue="********"),
    	@ApiImplicitParam(paramType="query",name="empNo",dataType="String", dataTypeClass=String.class, required=true, value="工号",defaultValue="********"),
    })
	@RequestMapping(value="/clues/post",method=RequestMethod.POST)
	public ServiceDataCopy<?> postClues(
			@RequestHeader(SysGlobalConst.HTTP_HEADER_X_TENANT_ID)String tenantId,
			@RequestHeader(SysGlobalConst.HTTP_HEADER_X_LANG_ID)String lang,
			@RequestHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO)String xEmpNo, 
			@RequestParam(name="empNo",required = true)String empNo,
			@Validated @RequestBody(required=true) BusinessClues businessClues,
			BindingResult bindingResult ) throws BusiException,ValidationException{ 
		businessClues.setxTenantId(tenantId);
        // 员工编号
		businessClues.setxEmpNo(empNo);
        // 语言
		businessClues.setxLangId(lang);
		businessClues.setEmpId(empNo);
		if(bindingResult != null && bindingResult.hasErrors())
		{
			throw new ValidationException(bindingResult);
		}
		businessCluesService.saveClues(businessClues);
	    return ServiceDataUtil.success(null);
	}
	
	@ApiOperation("线索认领")
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", name = "id", dataType="String", dataTypeClass=String.class, required = true, value = "线索ID", defaultValue = "1-3ARTTA"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "6011000145"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, dataType="String", dataTypeClass=String.class, required = true, value = "租户ID", defaultValue = "siebel"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, dataType="String", dataTypeClass=String.class, required = true, value = "语言", defaultValue = "zh_CN") })
	@RequestMapping(value = "/clue/claim", method = RequestMethod.POST)
	public ServiceDataCopy<String> claimClue(
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, required = true) String empShorNo,
			@RequestParam(name = "id", required = true) String id) throws BusiException, SiebelErrorAuthDeniedException {
		// 查询实体
		BusinessClues entity = new BusinessClues();
		entity.setxEmpNo(empShorNo);
		entity.setId(id);
		entity.setEmpId(empShorNo);
        // 待客户经理更新
		entity.setStatusCode(OppSysConst.RENEWING);
        // 归属客户经理
		entity.setOwnerMgr(empShorNo);
		// 认领
		businessCluesService.claimClue(entity);
		return ServiceDataUtil.success(null);
	}
	
	@ApiOperation("线索认领-->PC")
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", name = "clueNum", dataType="String", dataTypeClass=String.class, required = true, value = "线索编号", defaultValue = "L16122113964"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "6011000145"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, dataType="String", dataTypeClass=String.class, required = true, value = "租户ID", defaultValue = "pcOpportunity"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, dataType="String", dataTypeClass=String.class, required = true, value = "语言", defaultValue = "zh_CN") })
	@RequestMapping(value = "/clue/claimpc", method = RequestMethod.POST)
	public ServiceDataCopy<String> claimCluePC(
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, required = true) String empShorNo,
			@RequestParam(name = "clueNum", required = true) String clueNum) throws BusiException, SiebelErrorAuthDeniedException {
		BusinessClues entity = new BusinessClues();
		entity.setxEmpNo(empShorNo);
		entity.setClueNum(clueNum);
		entity.setEmpId(empShorNo);
        // 待客户经理更新
		entity.setStatusCode(OppSysConst.RENEWING);
        // 归属客户经理
		entity.setOwnerMgr(empShorNo);
		boolean transferAuth = pcbusinessCluesService.checkClaimAuthWithClue(entity);
		if(!transferAuth){
			// 无权限转商机 //,返回0003，无业务权限
			return ServiceDataUtil.noAuthInBusiness(null);
		}
		pcbusinessCluesService.claim(entity);
		return ServiceDataUtil.success(null);
	}
	
	@SuppressWarnings("unchecked")
	@ApiOperation("分页查询线索-->PC")
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", name = "clueMsg", dataType="String", dataTypeClass=String.class, required = false, value = "线索名称/线索编号/归属经理", defaultValue = ""),
			@ApiImplicitParam(paramType = "query", name = "clueType", dataType="String", dataTypeClass=String.class, required = true, value = "业务类型（01：所有线索，02：我创建的线索，03：我负责的线索，04：线索池，05：回收站）", defaultValue = "01"),
			@ApiImplicitParam(paramType = "query", name = "pageSize", dataType = "int", dataTypeClass = int.class, required = true, value = "页大小", defaultValue = "10"),
			@ApiImplicitParam(paramType = "query", name = "currentPage", dataType = "int", dataTypeClass = int.class, required = true, value = "当前页", defaultValue = "1"),
			@ApiImplicitParam(paramType = "query", name = "empNo", dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "********"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "********"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, dataType="String", dataTypeClass=String.class, required = true, value = "租户ID", defaultValue = "pcOpportunity"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, dataType="String", dataTypeClass=String.class, required = true, value = "语言", defaultValue = "zh_CN") })
	@RequestMapping(value = "/clue/pages", method = RequestMethod.GET)
	public ServiceDataCopy<List<BusinessClues>> getcluesforPCList(
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, required = true) String empShorNo,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, required = true) String tenantId,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, required = true) String lang,
			@RequestParam(name = "empNo", required = true) String empNo,
			@RequestParam(name = "clueMsg", required = false) String clueMsg,
			@RequestParam(name = "clueType", required = true) String clueType,
			@RequestParam(name = "currentPage", required = true) int currentPage,
			@RequestParam(name = "pageSize", required = true) int pageSize) throws Exception {
		BusinessClues entity = new BusinessClues();
		entity.setEmpNo(empNo);
		entity.setClueMsg(clueMsg);
		entity.setClueType(clueType);
		// 获取登录人empId
		entity.setEmpId(empNo);
		List<BusinessClues> businessCluesList = new ArrayList<>();
		PageQuery<BusinessClues> pageQuery = new PageQuery<>(entity, currentPage, pageSize);
		// 查询我的所有线索 分页
        if(CluesSysConst.ALL_CLUES.equals(clueType)) {
            long startFlowTime = System.currentTimeMillis();
            ServiceDataCopy<List<BusinessClues>> serviceData = pcbusinessCluesService.getBusinessCluesByPage(pageQuery);
            long endFlowTime = System.currentTimeMillis();
            log.info("/pc/opportunities/page  pcbusinessCluesService.getOpportunitiesByPage ........调用耗时: {} count:{}" , (endFlowTime - startFlowTime), pageQuery.getCount());
			Map<String, Object> map = serviceData.getOther();
			// 权限
			map.put("authFlag", FormDataHelpUtil.checkObjAllFieldsIsNull(pageQuery.getEntity().getAuth()) ? "N" : "Y");
			serviceData.setOther(map);
            return serviceData;
        }

		int countAllType = pcbusinessCluesService.countClues(pageQuery);
		if (countAllType > 0) {
			businessCluesList = pcbusinessCluesService.getCluesWithAuth(pageQuery);
		}
		pageQuery.setCount(countAllType);
		// 分页
		Map<String, Object> map = pageQuery.getMapOfPageQuery();
		// 权限
		map.put("authFlag", FormDataHelpUtil.checkObjAllFieldsIsNull(pageQuery.getEntity().getAuth()) ? "N" : "Y");
		return ServiceDataUtil.success(businessCluesList, map);
	}
	
	@ApiOperation("客户360-我负责的线索计数")
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", name = "empNo", dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "********"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "********"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, dataType="String", dataTypeClass=String.class, required = true, value = "租户ID", defaultValue = "pcOpportunity"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, dataType="String", dataTypeClass=String.class, required = true, value = "语言", defaultValue = "zh_CN") })
	@RequestMapping(value = "/myClues/count", method = RequestMethod.GET)
	public ServiceDataCopy<Integer> getMyCluesCount(
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, required = true) String empShorNo,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, required = true) String tenantId,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, required = true) String lang,
			@RequestParam(name = "empNo", required = true) String empNo) throws Exception{
		return ServiceDataUtil.success(pcbusinessCluesService.getMyClueCount());
	}
	
	@ApiOperation("最近查询的线索-->PC")
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", name = "empNo", dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "********"),
			@ApiImplicitParam(paramType = "query", name = "pageSize", dataType = "int", dataTypeClass = int.class, required = true, value = "页大小", defaultValue = "15"),
			@ApiImplicitParam(paramType = "query", name = "currentPage", dataType = "int", dataTypeClass = int.class, required = true, value = "当前页", defaultValue = "1"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "********"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, dataType="String", dataTypeClass=String.class, required = true, value = "租户ID", defaultValue = "pcOpportunity"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, dataType="String", dataTypeClass=String.class, required = true, value = "语言", defaultValue = "zh_CN") })
	@RequestMapping(value = "/clue/recent", method = RequestMethod.GET)
	public ServiceDataCopy<List<BusinessClues>> getcluesforPCRecent(
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, required = true) String empShorNo,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, required = true) String tenantId,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, required = true) String lang,
			@RequestParam(name = "empNo", required = true) String empNo,
			@RequestParam(name = "currentPage", required = true) int currentPage,
			@RequestParam(name = "pageSize", required = true) int pageSize) throws Exception {
		BusinessClues entity = new BusinessClues();
		entity.setEmpNo(empNo);
		// 获取登录人empId
		Map<String, String> paramMap = new HashMap<>(3);
		paramMap.put("empNo", empNo);
		paramMap.put("tenantId", tenantId);
		paramMap.put("lang", lang);
		entity.setEmpId(empNo);
		PageQuery<BusinessClues> pageQuery = new PageQuery<>(entity, currentPage, pageSize);
		List<BusinessClues> businessCluesList = pcbusinessCluesService.getRecentCluesWithAuth(pageQuery,paramMap);
		return ServiceDataUtil.success(businessCluesList);
	}
	
	@ApiOperation("查看线索详情-->PC")
	@ApiImplicitParams({
		@ApiImplicitParam(paramType = "query", name = "clueNum", dataType="String", dataTypeClass=String.class, required = true, value = "线索编码", defaultValue = "L15032705692"),
		@ApiImplicitParam(paramType = "query", name = "empNo", dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "********"),
		@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "6011000145"),
		@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, dataType="String", dataTypeClass=String.class, required = true, value = "租户ID", defaultValue = "pcOpportunity"),
		@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, dataType="String", dataTypeClass=String.class, required = true, value = "语言", defaultValue = "zh") })
	@RequestMapping(value = "/clueDetailpc", method = RequestMethod.GET)
	public ServiceDataCopy<BusinessClues> getclueDetailPc(
		@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, required = true) String empShorNo,
		@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, required = true) String tenantId,
		@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, required = true) String lang,
		@RequestParam(name = "clueNum", required = true) String clueNum,
		@RequestParam(name = "empNo", required = true) String empNo) throws Exception{
		BusinessClues entity = new BusinessClues();
		entity.setxEmpNo(empNo);
		entity.setClueNum(clueNum);
		entity.setEmpId(empNo);
		entity.setxLangId(lang);
		entity.setxTenantId(tenantId);
		boolean checkSelectAuth = pcbusinessCluesService.checkHasAuthToReadClue(empNo, clueNum);
		if(!checkSelectAuth){
            //,返回0003，无业务权限
			return ServiceDataUtil.noAuthInBusiness(null);
		}
		BusinessClues businessClues = pcbusinessCluesService.selectBaseInfo(entity);

		return ServiceDataUtil.success(businessClues);
	}
	
	@ApiOperation("PC端线索删除")
	@ApiImplicitParams({
		    @ApiImplicitParam(paramType = "query", name = "empNo", dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "********"),
			@ApiImplicitParam(paramType = "query", name = "id", dataType="String", dataTypeClass=String.class, required = true, value = "线索ID", defaultValue = "1-3ARTTA"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, dataType="String", dataTypeClass=String.class, required = true, value = "员工工号", defaultValue = "********"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, dataType="String", dataTypeClass=String.class, required = true, value = "租户ID", defaultValue = "pcOpportunity"),
			@ApiImplicitParam(paramType = "header", name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, dataType="String", dataTypeClass=String.class, required = true, value = "语言", defaultValue = "zh") })
	@RequestMapping(value = "/deleteClue", method = RequestMethod.DELETE)
	public ServiceDataCopy<?> deleteClue(
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_EMP_NO, required = true) String empShorNo,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_TENANT_ID, required = true) String tenantId,
			@RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_LANG_ID, required = true) String lang,
			@RequestParam(name = "empNo", required = true) String empNo,
			@RequestParam(name = "id", required = true) String id) throws BusiException {
		BusinessClues entity = new BusinessClues();
		entity.setxEmpNo(empNo);
		entity.setId(id);
		entity.setxLangId(lang);
		entity.setxTenantId(tenantId);
		entity.setEmpId(empNo);
		boolean deleteAuth = pcbusinessCluesService.checkDeleteAuthClue(entity);
		if(!deleteAuth){
			//无权限删除//,返回0003，无业务权限
			return ServiceDataUtil.noAuthInBusiness(null);
		}
		// 删除
		if (pcbusinessCluesService.delteClue(entity)) {
			return ServiceDataUtil.success(null);
		}
		return ServiceDataUtil.busiError(localeMessageSourceBean.getMessage(CommonConst.CLUE_DELETE_FAIL));
	}

	@ApiOperation("线索高级查询")
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "query", name = "pageSize", dataType = "int", dataTypeClass = int.class, required = true, value = "页大小", defaultValue = "10"),
			@ApiImplicitParam(paramType = "query", name = "currentPage", dataType = "int", dataTypeClass = int.class, required = true, value = "当前页", defaultValue = "1"),
	})
	@GetMapping("/advance/query")
	public ServiceDataCopy<List<BusinessClues>> advanceQuery(
			@RequestParam(name = "statusCode") String statusCode,
			@RequestParam(name = "deptId") String deptId,
			@RequestParam(name = "bigProdcutLineId") String bigProdcutLineId,
			@RequestParam(name = "predictSignDateFrom") String predictSignDateFrom,
			@RequestParam(name = "predictSignDateTo") String predictSignDateTo,
			@RequestParam(name = "pageSize") int pageSize,
			@RequestParam(name = "currentPage") int currentPage
	) {
		//获取登录人信息
		String emptyId = RequestMessage.getEmpNo();
		// 查询参数
		BusinessClues entity = new BusinessClues();
		entity.setStatusCode(statusCode);
		entity.setDeptId(deptId);
		entity.setBigProdcutLineId(bigProdcutLineId);
		entity.setPredictSignDateFrom(predictSignDateFrom);
		entity.setPredictSignDateTo(predictSignDateTo);
		entity.setEmpId(emptyId);
		// 查询
		PageQuery pageQuery = new PageQuery<>(entity, currentPage, pageSize);
		List<BusinessClues> clues =  businessCluesService.advanceQuery(pageQuery);

		return ServiceDataUtil.success(clues, pageQuery.getMapOfPageQuery());
	}

    @ApiOperation("更新部门ORG编码")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "orgCode", dataType="String", dataTypeClass=String.class, value = "部门ORG编码"),
            @ApiImplicitParam(paramType = "query", name = "clueNum", dataType="String", dataTypeClass=String.class, value = "线索编码")
    })
    @GetMapping("/clue/updateOrgCodePath")
    public ServiceData<Integer> updateOrgCodePath(
            @RequestParam(name = "orgCode", required = false) String orgCode,
            @RequestParam(name = "clueNum", required = false) String clueNum) throws Exception {
        ServiceData resultServiceData = new ServiceData();
        List<BusinessClues> businessCluesList = pcbusinessCluesService.updateOrgCodePath(orgCode, clueNum);
        resultServiceData.setBo(businessCluesList.size());
        return resultServiceData;
    }
	
}
