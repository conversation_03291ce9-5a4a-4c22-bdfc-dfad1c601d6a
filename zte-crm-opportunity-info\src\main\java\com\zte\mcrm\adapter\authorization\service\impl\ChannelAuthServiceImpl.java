package com.zte.mcrm.adapter.authorization.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.zte.itp.authorityclient.client.AuthorityClient;
import com.zte.itp.authorityclient.entity.input.CommonModuleIdEntity;
import com.zte.itp.authorityclient.entity.input.CommonRoleEntity;
import com.zte.itp.authorityclient.entity.output.ServiceData;
import com.zte.mcrm.adapter.authorization.dto.RoleInfoDTO;
import com.zte.mcrm.adapter.authorization.model.RoleInfo;
import com.zte.mcrm.adapter.authorization.service.ChannelAuthService;
import com.zte.mcrm.adapter.authorization.service.PrmAuthService;
import com.zte.mcrm.adapter.model.dto.RoleDTO;
import com.zte.mcrm.common.util.CommonUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: 10245190 DengZiqiao
 * @Date: 2021年9月28日 18:55:18
 * @Version: V1.0
 */
@Service
public class ChannelAuthServiceImpl implements ChannelAuthService {
    private static final Logger logger = LoggerFactory.getLogger(ChannelAuthServiceImpl.class);
    @Autowired
    private PrmAuthService prmAuthService;
    /**
     * ichannel产品ID
     **/
    @Value("${ichannel.upp.auth.productId}")
    private Long ichannelProductId;
    /**
     * ichannel模块ID
     **/
    @Value("${ichannel.upp.auth.moduleId}")
    private String ichannelModuleId;
    /**
     * ichannel租户ID
     **/
    @Value("${ichannel.upp.auth.tenantId}")
    private Long ichannelTenantId;
    /**
     * ichannel产品密钥key
     **/
    @Value("${ichannel.upp.auth.productSecretKey}")
    private String ichannelProductSecretKey;

    /**
     * 查询当前登录人的所有角色信息
     *
     * @return
     */
    @Override
    public ServiceData getRoleList() {
        // 权限平台需要的参数对象
        CommonRoleEntity entity = new CommonRoleEntity();
        addIchannelConstraints(entity);
        //当前用户
        entity.setEmpidui(CommonUtils.getEmpNo());
        //token
        entity.setToken(CommonUtils.getAuthValue());
        // 调用权限平台接口
        return AuthorityClient.getRoleList(entity);
    }

    /**
     * 添加ichannel侧约束条件
     */
    private void addIchannelConstraints(CommonModuleIdEntity entity) {
        //产品Id，该值保持固定，需要变动联系权限平台
        entity.setProductId(ichannelProductId);
        //模块Id，该值请咨询权限平台
        entity.setModuleId(ichannelModuleId);
        //租户Id
        entity.setTenantId(ichannelTenantId);
        //产品密钥key
        entity.setSecretKey(ichannelProductSecretKey);
    }

    /**
     * 根据角色Id、模块Id、行业Id、组织Id查询用户信息
     *
     * @param   entity
     * @param   isPrm
     * @return
     * <AUTHOR>
     * @date 2021/10/30
     */
    @Override
    public List<RoleInfo> getUserByRoleAndData(RoleInfoDTO entity, Boolean isPrm) {
        addIchannelConstraints(entity);
        return prmAuthService.getUserByRoleAndData(entity, isPrm);
    }

    /**
     * 查询当前登录人的角色编码集合
     */
    @Override
    public Set<String> getRoleCodes() {
        ServiceData ret = this.getRoleList();
        String str = JSONObject.toJSONString(ret.getBo());
        List<RoleDTO> roleDTOS = JSONObject.parseObject(str, new TypeReference<List<RoleDTO>>() {});
        Set<String> roleCodes = roleDTOS.stream().map(RoleDTO::getRoleCode).collect(Collectors.toSet());
        return roleCodes;
    }

    @Override
    public ServiceData getRoleByRoleCodeAndModuleId(String moduleId, String roleCode, String itpValue) {
        CommonRoleEntity entity = new CommonRoleEntity();
        addIchannelConstraints(entity);
        //当前用户
        entity.setEmpidui(CommonUtils.getEmpNo());
        entity.setRoleNameEN(roleCode);
        //token
        entity.setToken(CommonUtils.getAuthValue());
        if(StringUtils.isNotBlank(itpValue)) {
            entity.setItpValue(itpValue);
        }

        return AuthorityClient.queryRoleByNameEn(entity);
    }


}
