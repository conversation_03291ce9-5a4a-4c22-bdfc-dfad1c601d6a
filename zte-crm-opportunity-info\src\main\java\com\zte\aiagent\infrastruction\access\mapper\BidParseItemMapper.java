package com.zte.aiagent.infrastruction.access.mapper;

import com.zte.aiagent.infrastruction.access.po.BidParseItemPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface BidParseItemMapper {
    /**
     * 插入解析条目记录
     * @param bidParseItem 解析条目PO对象
     * @return 影响的行数
     */
    int insert(BidParseItemPO bidParseItem);

    /**
     * 批量插入解析条目记录
     * @param items 解析条目列表
     * @return 影响的行数
     */
    int batchInsert(@Param("items") List<BidParseItemPO> items);

    /**
     * 根据ID查询解析条目
     * @param rowId 主键ID
     * @return 解析条目PO对象
     */
    BidParseItemPO selectByPrimaryKey(String rowId);

    /**
     * 根据解析记录ID查询条目列表
     * @param parseRecordId 解析记录ID
     * @return 解析条目列表
     */
    List<BidParseItemPO> selectByParseRecordId(String parseRecordId);

    /**
     * 根据解析记录ID和条目编码查询条目
     * @param parseRecordId 解析记录ID
     * @param itemCode 条目编码
     * @return 解析条目PO对象
     */
    BidParseItemPO selectByRecordIdAndItemCode(
            @Param("parseRecordId") String parseRecordId,
            @Param("itemCode") String itemCode);

    /**
     * 根据ID更新解析条目
     * @param bidParseItem 解析条目PO对象
     * @return 影响的行数
     */
    int updateByPrimaryKey(BidParseItemPO bidParseItem);

    /**
     * 根据ID删除解析条目
     * @param rowId 主键ID
     * @return 影响的行数
     */
    int deleteByPrimaryKey(String rowId);

    /**
     * 根据解析记录ID删除所有关联条目
     * @param parseRecordId 解析记录ID
     * @return 影响的行数
     */
    int deleteByParseRecordId(String parseRecordId);
}
