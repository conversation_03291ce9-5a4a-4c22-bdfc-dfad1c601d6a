package com.zte.crm.eva.base.infrastructure.adapter.impl;

import com.zte.crm.eva.base.domain.pseudocode.model.MapBO;
import com.zte.crm.eva.base.domain.pseudocode.model.PseudocodeRule;
import com.zte.crm.eva.base.domain.pseudocode.model.RuleResultBO;
import com.zte.crm.eva.base.infrastructure.adapter.PseudocodeExecutor;
import com.zte.ta.rule.domain.RuleRecord;
import com.zte.ta.rule.service.IRuleRecordService;
import org.drools.core.impl.StatefulKnowledgeSessionImpl;
import org.kie.api.runtime.KieSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Map;

/**
 * drools脚本执行
 * <AUTHOR>
 * @date 2023-04-28
 */
@Component
public class DroolsPseudocodeExecutorImpl implements PseudocodeExecutor, ApplicationContextAware {

    private static final Logger LOGGER = LoggerFactory.getLogger(DroolsPseudocodeExecutorImpl.class);

    private static final String SUCCESS = "success";

    @Autowired
    private IRuleRecordService ruleRecordService;

    private ApplicationContext applicationContext;

    /**
     * 执行脚本
     * @param rule
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    public String executePseudocode(PseudocodeRule rule, Map<String, Object> param) throws Exception {
        KieSession session = null;
        try {
            RuleRecord ruleRecord = new RuleRecord();
            ruleRecord.setRuleContent(rule.getRuleContent());
            session = ruleRecordService.createRuleSession(ruleRecord);
            injectBeans(session);
            executeRule(session, param);
        } catch (Exception e) {
            LOGGER.error("drools run ruleName:{} failed, error:{}", rule.getRuleName(), e.getMessage(), e);
            throw e;
        } finally {
            if (null != session){
                session.dispose();
            }
        }
        return SUCCESS;
    }

    /**
     * 执行drools脚本
     * @param session
     * @param param
     */
    private void executeRule(KieSession session, Map<String, Object> param) {
        MapBO ruleMap = new MapBO(param, new RuleResultBO());
        session.insert(ruleMap);
        session.fireAllRules();
    }

    /**
     * 注入bean
     * @param session
     */
    private void injectBeans(KieSession session) {
        if (ObjectUtils.isEmpty(session)) {
            return;
        }
        Map<String, Class<?>> globalMap =  ((StatefulKnowledgeSessionImpl) session).getKnowledgeBase().getGlobals();
        for (Map.Entry<String, Class<?>> entry : globalMap.entrySet()) {
            String globalName = entry.getKey();
            Object obj = applicationContext.getBean(globalName);
            if (obj != null) {
                session.setGlobal(globalName, obj);
            }
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
