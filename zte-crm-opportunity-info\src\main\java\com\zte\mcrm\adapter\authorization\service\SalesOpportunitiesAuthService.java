package com.zte.mcrm.adapter.authorization.service;

import com.zte.itp.authorityclient.entity.output.ServiceData;

import java.util.Set;

/**
 * 描述：
 * 创建时间：2021/10/8
 *
 * @author：王丹凤6396000572
 */
public interface SalesOpportunitiesAuthService {

    /**
     * 查询当前登录人的所有角色信息
     *
     * @return
     */
    ServiceData getRoleList();

    /**
     * 查询当前登录人的角色编码集合
     */
    Set<String> getRoleCodes();
}
