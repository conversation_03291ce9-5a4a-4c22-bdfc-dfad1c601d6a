package com.zte.mcrm.channel.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.opty.model.bo.OptionBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
*  实体类
* <AUTHOR>
* @date 2021/09/14
*/

@Setter @Getter @ToString
@ApiModel(description="")
public class OpportunityProduct implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private String rowId;
    @ApiModelProperty(value = "创建人")
    private String createdBy;
    @ApiModelProperty(value = "最后更新人")
    private String lastUpdBy;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpd;
    @ApiModelProperty(value = "体系内部分类Id")
    private String prodLv1Id;
    @ApiModelProperty(value = "")
    private String prodLv1Name;
    @ApiModelProperty(value = "大产品线Id")
    private String prodLv2Id;
    @ApiModelProperty(value = "")
    private String prodLv2Name;
    @ApiModelProperty(value = "产品线Id")
    private String prodLv21Id;
    @ApiModelProperty(value = "")
    private String prodLv21Name;
    @ApiModelProperty(value = "产品大类Id")
    private String prodLv3Id;
    @ApiModelProperty(value = "")
    private String prodLv3Name;
    @ApiModelProperty(value = "产品小类Id")
    private String prodLv4Id;
    @ApiModelProperty(value = "")
    private String prodLv4Name;
    @ApiModelProperty(value = "")
    private String opptyId;
    @ApiModelProperty(value = "是否主产品")
    private String zteMainProduct;
    @ApiModelProperty(value = "业务类型（新建商机/商机转立项/公司主产品）")
    private String businessType;
    @ApiModelProperty(value = "记录是否可用 ENABLE_FLAG 记录是否可用JSON")
    private String activeFlg;
    @ApiModelProperty(value = " 记录是否可用 ENABLE_FLAG_EXT 记录是否可用(Y,N)")
    private String enableFlagExt;
    @ApiModelProperty(value = "产品签单金额")
    private BigDecimal productAmount;
    @JsonFormat(pattern ="yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "预计签单时间")
    private Date forSignDate;
    @ApiModelProperty(value = "")
    private String dataSource;
    @ApiModelProperty(value = "PDM产品信息")
    private OpportunityProduct pdmProd;
    @ApiModelProperty(value = "businessType=pdm_prod公司主产品,关联政企产品的ROW_ID")
    private String parProdId;
    @ApiModelProperty(value = "是否主产品")
    private String mainProd;
    @ApiModelProperty(value = "币种 默认 CNY")
    private String currency;
    @ApiModelProperty(value = "成功率")
    private String successRate;
    @ApiModelProperty(value = "市场类型")
    private String marketType;
    @ApiModelProperty(value = "预计签单金额（万CNY）")
    private BigDecimal forSignAmount;
    @ApiModelProperty(value = "成功率拓展")
    private String successRateExt;
    private List<OptionBO> enableFlag;
    private List<OptionBO> successRateJson;
}
