# SOC应答系统数据库变更日志

## 变更信息
- **变更日期**: 2024-07-30
- **变更类型**: 数据库表结构优化
- **影响表**: soc_item_product
- **变更原因**: 通过对比soc_item_history表发现soc_item_product表缺少关键字段

## 变更详情

### 新增字段

| 字段名 | 数据类型 | 默认值 | 说明 | 位置 |
|--------|----------|--------|------|------|
| additional_info | TEXT | NULL | 补充信息 | response_content字段后 |
| remark | TEXT | NULL | 备注 | additional_info字段后 |
| current_version | INT | 1 | 当前版本号 | remark字段后 |

### 变更前后对比

#### 变更前
```sql
CREATE TABLE soc_item_product (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    product_code VARCHAR(200) NOT NULL COMMENT '产品编码',
    product_name VARCHAR(200) NOT NULL COMMENT '产品名称',
    satisfaction VARCHAR(10) COMMENT '满足度：FC,PC,NC',
    response_method VARCHAR(20) COMMENT '应答方式：AI-AI应答,MANUAL-手工应答',
    response_content LONGTEXT COMMENT '应答说明（支持富文本）',
    source VARCHAR(50) COMMENT '应答来源：GBBS,文档库等',
    source_index VARCHAR(200) COMMENT '索引链接',
    match_score DECIMAL(5,2) COMMENT '匹配度分数',
    item_product_status VARCHAR(20) DEFAULT '未应答' COMMENT '应答状态',
    -- 审计字段...
);
```

#### 变更后
```sql
CREATE TABLE soc_item_product (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    item_id BIGINT NOT NULL COMMENT '条目ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    product_code VARCHAR(200) NOT NULL COMMENT '产品编码',
    product_name VARCHAR(200) NOT NULL COMMENT '产品名称',
    satisfaction VARCHAR(10) COMMENT '满足度：FC,PC,NC',
    response_method VARCHAR(20) COMMENT '应答方式：AI-AI应答,MANUAL-手工应答',
    response_content LONGTEXT COMMENT '应答说明（支持富文本）',
    additional_info TEXT COMMENT '补充信息',
    remark TEXT COMMENT '备注',
    current_version INT DEFAULT 1 COMMENT '当前版本号',
    source VARCHAR(50) COMMENT '应答来源：GBBS,文档库等',
    source_index VARCHAR(200) COMMENT '索引链接',
    match_score DECIMAL(5,2) COMMENT '匹配度分数',
    item_product_status VARCHAR(20) DEFAULT '未应答' COMMENT '应答状态',
    -- 审计字段...
);
```

## 变更影响

### 正面影响
1. **功能完整性**: 支持用户添加补充信息和备注
2. **版本管理**: 实现主表与历史表的版本号对应
3. **数据一致性**: 主表和历史表字段结构更加一致
4. **用户体验**: 提供更丰富的信息录入能力

### 潜在影响
1. **存储空间**: 新增字段会增加存储空间占用
2. **应用程序**: 需要更新相关的实体类、DAO、Service等代码
3. **前端界面**: 需要在应答编辑界面添加对应的输入框

## 执行步骤

### 1. 数据库变更
```bash
# 执行数据库脚本
mysql -u username -p database_name < alter_soc_item_product_add_fields.sql
```

### 2. 代码变更
- [ ] 更新实体类 (SocItemProductDO)
- [ ] 更新DAO接口和XML映射
- [ ] 更新Service业务逻辑
- [ ] 更新Controller接口
- [ ] 更新前端页面组件

### 3. 测试验证
- [ ] 单元测试
- [ ] 集成测试
- [ ] 功能测试
- [ ] 性能测试

## 回滚方案

如果需要回滚此次变更，可以执行以下SQL：
```sql
ALTER TABLE soc_item_product DROP COLUMN additional_info;
ALTER TABLE soc_item_product DROP COLUMN remark;
ALTER TABLE soc_item_product DROP COLUMN current_version;
```

## 相关文档
- [数据库设计与数据模型.md](../设计文档_by需求_aug/04_数据库设计与数据模型.md)
- [alter_soc_item_product_add_fields.sql](../数据库脚本/alter_soc_item_product_add_fields.sql)

## 审批记录
- **提出人**: AI Assistant
- **审批人**: [待填写]
- **审批时间**: [待填写]
- **执行人**: [待填写]
- **执行时间**: [待填写]
