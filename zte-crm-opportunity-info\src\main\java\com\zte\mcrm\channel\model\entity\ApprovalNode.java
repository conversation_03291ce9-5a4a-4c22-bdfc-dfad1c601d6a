package com.zte.mcrm.channel.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.mcrm.common.annotation.EmpName;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public  class ApprovalNode{
    @ApiModelProperty(value = "任务id")
    private String taskId ;
    @ApiModelProperty(value = "节点id")
    private String nodeId ;
    @ApiModelProperty(value = "节点名")
    private String nodeName ;
    @ApiModelProperty(value = "转交人")
    @EmpName
    private String fromApprover;
    @ApiModelProperty(value = "被转交人")
    @EmpName
    private String toApprover;
    @ApiModelProperty(value = "审批人")
    @EmpName
    private String approver;
    @ApiModelProperty(value = "创建日期")
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;
    @ApiModelProperty(value = "审批日期")
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approvalDate;
    @ApiModelProperty(value = "任务转态  ACTIVE-待处理  REASSIGN-已转交 COMPLETED-已处理")
    private String taskStatus;
    @ApiModelProperty(value = "审批结论  Y-通过  N-不通过")
    private String result;
    @ApiModelProperty(value = "审批结论  Y-通过  N-不通过")
    private String resultCN;


    @ApiModelProperty(value = "审批意见")
    private String opinion;
    @ApiModelProperty(value = "转交  true:能转交  false:不能转交")
    private boolean  reassignOp;
    @ApiModelProperty(value = "流程实例id")
    private String flowInstanceId;

    public Boolean getReassignOp() {
        return reassignOp;
    }

    public void setReassignOp(Boolean reassignOp) {
        this.reassignOp = reassignOp;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getNodeId() {
        return nodeId;
    }

    public String getResultCN() {
        return resultCN;
    }

    public void setResultCN(String resultCN) {
        this.resultCN = resultCN;
    }
    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getFromApprover() {
        return fromApprover;
    }

    public void setFromApprover(String fromApprover) {
        this.fromApprover = fromApprover;
    }

    public String getToApprover() {
        return toApprover;
    }

    public void setToApprover(String toApprover) {
        this.toApprover = toApprover;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getApprovalDate() {
        return approvalDate;
    }

    public void setApprovalDate(Date approvalDate) {
        this.approvalDate = approvalDate;
    }

    public String getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(String taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }

    public String getApprover() {
        return approver;
    }

    public void setApprover(String approver) {
        this.approver = approver;
    }

    public String getFlowInstanceId() {
        return flowInstanceId;
    }

    public void setFlowInstanceId(String flowInstanceId) {
        this.flowInstanceId = flowInstanceId;
    }

    @Override
    public String toString() {
        return "ApprovalNode{" +
                "taskId='" + taskId + '\'' +
                ", nodeId='" + nodeId + '\'' +
                ", nodeName='" + nodeName + '\'' +
                ", fromApprover='" + fromApprover + '\'' +
                ", toApprover='" + toApprover + '\'' +
                ", approver='" + approver + '\'' +
                ", createdDate=" + createdDate +
                ", approvalDate=" + approvalDate +
                ", taskStatus='" + taskStatus + '\'' +
                ", result='" + result + '\'' +
                ", resultCN='" + resultCN + '\'' +
                ", opinion='" + opinion + '\'' +
                ", reassignOp=" + reassignOp +
                ", flowInstanceId='" + flowInstanceId + '\'' +
                '}';
    }
}
