package com.zte.mcrm.channel.service.common;

import com.zte.mcrm.channel.constant.OpportunityNotifyMsgEnum;
import com.zte.mcrm.channel.model.dto.OpportunityMsgNotifyDTO;
import com.zte.mcrm.channel.model.entity.OpportunityMailEntity;

import java.util.List;

/**
 * 消息通知服务
 * 包括：系统消息、邮件
 */
public interface IMessageNotifyService {

    void sendMessage(OpportunityMsgNotifyDTO opportunityMsgNotifyDTO);

    /**
     * 发送邮件和消息
     * @param opportunityMsgNotifyDTO
     */
    Boolean sendMessageThroughMailAndNotice(OpportunityMsgNotifyDTO opportunityMsgNotifyDTO);

}
