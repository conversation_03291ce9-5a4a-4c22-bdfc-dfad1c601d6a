package com.zte.mcrm.adapter.authorization.service;

import com.zte.itp.authorityclient.entity.input.CommonModuleIdEntity;
import com.zte.itp.authorityclient.entity.output.RoleVO;
import com.zte.itp.authorityclient.entity.output.ServiceData;
import com.zte.itp.authorityclient.entity.output.UserVO;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.mcrm.adapter.authorization.dto.RoleInfoDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/9/29
 */
public interface UppAuthorityService {
    /**
     * 根据模块id、角色id、约束id查询对应的负责人
     * @param entity
     * @return
     */
    ServiceData getUserByRoleAndData(RoleInfoDTO entity);

    /**
     * 根据用户工号 模块id， 获取用户拥有角色
     * @param entity 入参（包括工号、模块id、产品id等）
     * @return 角色列表
     * @throws BusiException 业务异常
     */
    List<RoleVO> getRoleListModule(CommonModuleIdEntity entity) throws BusiException;

    /**
     * 根据角色编号集合查询用户集合
     * @param entity
     * @param roleCodes
     * @return
     */
    Map<String, List<UserVO>> queryUsersByRoleCode(CommonModuleIdEntity entity, List<String> roleCodes);

    /**
     * 根据用户工号 场景编码， 获取用户拥有角色
     * @param entity 入参（包括工号、模块id、产品id等）
     * @return 返回角色和角色信息的键值对map
     * @throws BusiException 业务异常
     */
    Map<String, RoleVO> getRoleMap(CommonModuleIdEntity entity) throws BusiException;

    /**
     * 根据工号, 角色ID列表及约束编码查询对应约束下的权限数据
     * @param roleIdList 需要查询约束权限的角色id列表
     * @param constraintCode 需要查询的约束编码
     * @param entity 其他参数，如模块id、工号、token等，其中模块id、产品id、密钥等如果不传则自动从配置文件里面取。
     * @return java.util.List<java.lang.String> 返回权限数据列表（已去重）
     */
    List<String> getAuthInfoByUserAndRoleId(List<String> roleIdList, String constraintCode,  CommonModuleIdEntity entity);

    /**
     * 根据约束编码获取约束id
     * @param constraintCode 约束编码，如“ORG”等
     * @param commEntity  其他参数，如模块id、工号、token等，其中模块id、产品id、密钥等如果不传则自动从配置文件里面取。
     * @return String 约束id
     */
    String getConstraintId(String constraintCode, CommonModuleIdEntity commEntity);

    /**
     * 校验当前用户是否拥有指定角色，只要满足其中一种角色即可
     *
     * @param roleCodes 角色编码列表
     * @return  如果没有，抛出0003异常
     * <AUTHOR>
     * @date    2023/5/12
     */
    Boolean hasPermissionInRoleCodes(List<String> roleCodes);
}
