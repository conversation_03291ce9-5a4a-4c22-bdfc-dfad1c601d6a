package com.zte.mcrm.common.access.dao;

import feign.Param;
import org.springframework.stereotype.Repository;

import com.zte.mcrm.common.access.vo.LockVO;

import java.util.List;

/****
 *
 * <AUTHOR> @date 2021/2/10
 **/

@Repository
public interface LockDao {
	/**
	 * 加锁
	 * @param lock
	 */
	void addLock(Lock<PERSON> lock);
	/**
	 * 释放锁
	 * @param methodName
	 */
	void deleteLock(String methodName);


	/**
		* 获取审批人
		* @param approveId
		* @return
		*/
	List<String> getApprovedBy(String approveId);

	/****
	 * 失效最近查询记录
	 * @param bizType
	 * @return
	 */
	int invalidOftenSearchByBizType(@Param("bizType") String bizType);
}
