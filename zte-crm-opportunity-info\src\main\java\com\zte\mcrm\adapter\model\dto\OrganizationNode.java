package com.zte.mcrm.adapter.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;


@Getter
@Setter
@ToString
public class OrganizationNode implements Serializable {
    /**
     * 组织ID
     */
    @ApiModelProperty("组织ID")
    private String organizationId;
    /**
     * 组织父ID
     */
    @ApiModelProperty("组织父ID")
    private String organizationParentId;
    /**
     * 行政层级
     */
    @ApiModelProperty("行政层级")
    private Integer hrLevel;
    /**
     * 组织名
     */
    @ApiModelProperty("组织名")
    private String organizationName;
    /**
     * 组织别名
     */
    @ApiModelProperty("组织别名")
    private String organizationAlias;
    /**
     * 是否可见
     */
    @ApiModelProperty("是否可见")
    private String isVisible;
    /**
     * 组织名全路径
     */
    @ApiModelProperty("组织名全路径")
    private String organizationNamePath;
    /**
     * 组织ID路径
     */
    @ApiModelProperty("组织ID路径")
    private String organizationIdPath;
    /**
     * 组织状态ID
     */
    @ApiModelProperty("组织状态ID")
    private Integer organizationStatusId;
    /**
     * 组织层级
     */
    @ApiModelProperty("组织层级")
    private String organizationLevel;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private String createdDate;
    /**
     * 最新更新人
     */
    @ApiModelProperty("最新更新人")
    private String lastUpdatedBy;
    /**
     * 最新更新时间
     */
    @ApiModelProperty("最新更新时间")
    private String lastUpdatedDate;
    /**
     * 是否有效
     */
    @ApiModelProperty("是否有效")
    private String enabledFlag;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
    /**
     * 第一层级编码
     */
    @ApiModelProperty("第一层级编码")
    private String firstCode;
    /**
     * 第一层级编码
     */
    @ApiModelProperty("第二层级编码")
    private String secondCode;
    /**
     * 第一层级编码
     */
    @ApiModelProperty("第三层级编码")
    private String thirdCode;
    /**
     * 第一层级编码
     */
    @ApiModelProperty("第四层级编码")
    private String fourthCode;
    /**
     * 第一层级编码
     */
    @ApiModelProperty("第五层级编码")
    private String fifthcode;
    /**
     * 语言编码
     */
    @ApiModelProperty("语言编码")
    private String languageCode;

}
