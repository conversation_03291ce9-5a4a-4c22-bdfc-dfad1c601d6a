package com.zte.leadinfo.common.service;

import com.zte.leadinfo.common.entity.CxApprOpHead;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cx_appr_op_head】的数据库操作Service
* @createDate 2024-07-12 23:30:33
*/
public interface CxApprOpHeadService extends IService<CxApprOpHead> {

    List<CxApprOpHead> listByOpptyIds(List<String> optyIds, List<String> approveObjects);
}
