package com.zte.itp.demo.ui.controller;

import com.zte.springbootframe.common.model.ServiceDataCopy;
import com.zte.springbootframe.common.model.ServiceDataUtil;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by 10207653 on 2017/10/16.
 */

/**
 * <AUTHOR>
 * */
@RestController
@RequestMapping("/gray")
public class GrayShowCaseDemo
{
    @RequestMapping(value = "information", method = RequestMethod.GET, produces = "application/json; charset=utf-8")
    public ServiceDataCopy<?> getInfo()
    {
        String res = "This is test string!";

        GrayDemoBO bo = new GrayDemoBO();
        bo.setName(res);


        return ServiceDataUtil.success(bo); 
    }

    private class GrayDemoBO
    {
        String name;

        public String getName()
        {
            return name;
        }

        public void setName(String name)
        {
            this.name = name;
        }

        @Override
        public String toString() {
            return "GrayDemoBO{" +
                    "name='" + name + '\'' +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "GrayShowCaseDemo{}";
    }
}

