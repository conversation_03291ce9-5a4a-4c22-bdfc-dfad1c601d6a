package com.zte.aiagent.infrastruction.access.po;

import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * 解析数据存储表PO类
 * 对应数据库表：bid_parse_data
 * 用于存储解析过程中的请求、响应和文档内容等数据，支持大内容分块存储
 */
@Data
public class BidParseDataPO {
    /**
     * 主键ID
     */
    private String rowId;

    /**
     * 解析记录ID，关联bid_parse_record.row_id
     */
    private String parseRecordId;

    /**
     * 数据类型: REQUEST-请求参数, RESPONSE-响应数据, DOCUMENT-文档内容
     */
    private String dataType;

    /**
     * 数据块索引(从0开始)，用于大内容分块存储
     */
    private Integer chunkIndex;

    /**
     * 总块数，标识当前数据被分成了多少块
     */
    private Integer chunkTotal;

    /**
     * 当前块大小(字节)
     */
    private Integer chunkSize;

    /**
     * 分块数据内容，存储实际的解析数据
     */
    private String dataContent;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人
     */
    private String lastUpdatedBy;

    /**
     * 最后更新时间
     */
    private Date lastUpdatedDate;

    /**
     * 有效标记(Y/N)
     */
    private String enabledFlag;

    /**
     * 租户ID
     */
    private Long tenantId;
}

