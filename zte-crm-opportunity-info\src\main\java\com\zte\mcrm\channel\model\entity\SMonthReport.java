package com.zte.mcrm.channel.model.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
* 商机月报 实体类
* <AUTHOR>
* @date 2021/10/20
*/

@Setter @Getter @ToString
@ApiModel(description="商机月报")
public class SMonthReport implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    private String id;
    @ApiModelProperty(value = "商机id")
    private String optyId;
    @ApiModelProperty(value = "当前月报归属期")
    private String reportMonth;
    @ApiModelProperty(value = "月报状态")
    private String reportStatus;
    @ApiModelProperty(value = "商机当前状态")
    private String optyCurrentStatus;
    @ApiModelProperty(value = "原因代码")
    private String reasonCode;
    @ApiModelProperty(value = "本月进展")
    private String reportInfo;
    @ApiModelProperty(value = "创建人")
    private String createdBy;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createdDate;
    @ApiModelProperty(value = "最后更新人")
    private String lastUpdatedBy;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpdatedDate;
    @ApiModelProperty(value = "有效性(Y/N)")
    private String enabledFlag;
    @ApiModelProperty(value = "备注")
    private String memo;
    @ApiModelProperty(value = "业务类型（更新状态/更新月报/首次提交月报时商机详情备份）")
    private String businessType;
    @ApiModelProperty(value = "月报详情")
    private String monthReportDetailJson;
}
