package com.zte.mcrm.common.framework.exception;

import com.alibaba.fastjson.JSON;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.common.errorcode.util.ErrorCodeMessageUtil;
import org.jetbrains.annotations.PropertyKey;

/**
 * 错误码远程调用异常类
 */
public class ErrorCodeRemoteException extends RuntimeException {
    /**
     * 错误码
     */
    private String errorCode;
    private String errorCodeMessage;
    private String errorCodeTip;
    private ServiceData errorCodeServiceData;
    private String remoteErrorResponse;
    /**
     * 业务异常编码
     */
    private ErrorCode errorCodeEnum;
    /**
     * 业务可变参数
     */
    private Object[] arguments;

    public ServiceData getErrorCodeServiceData() {
        return errorCodeServiceData;
    }

    public String getRemoteErrorResponse() {
        return remoteErrorResponse;
    }

    public String getErrorCodeMessage() {
        return errorCodeMessage;
    }

    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 远程调用没有返回ServiceData格式的响应，但抛出异常需要带出响应消息时使用
     *
     * @param remoteErrorResponse
     * @param errorCodeEnum
     * @param arguments
     */
    public ErrorCodeRemoteException(String remoteErrorResponse, ErrorCode errorCodeEnum, Object... arguments) {
        super(errorCodeEnum.code());
        setFieldValue(remoteErrorResponse, errorCodeEnum.code(), arguments);
    }

    /**
     * 远程调用没有返回ServiceData格式的响应，但抛出异常需要带出响应消息时使用
     *
     * @param remoteErrorResponse
     * @param errorCode
     * @param arguments
     */
    public ErrorCodeRemoteException(String remoteErrorResponse, @PropertyKey(resourceBundle = ErrorCodeMessageUtil.ERRORCODEMESSAGES_BUNDLE)String errorCode, Object... arguments) {
        super(errorCode);
        setFieldValue(remoteErrorResponse, errorCode, arguments);
    }
    /**
     * 调用内部系统需要直接抛出外部异常响应的用此构造函数
     *
     * @param errorCodeServiceData
     */
    public ErrorCodeRemoteException(ServiceData errorCodeServiceData) {
        if (null == errorCodeServiceData) {
            return;
        }
        this.errorCodeServiceData = errorCodeServiceData;
        RetCode code = errorCodeServiceData.getCode();
        if (null != code) {
            this.errorCodeTip = code.getMsg();
            this.errorCode = code.getCode();
            Object bo = errorCodeServiceData.getBo();
            this.errorCodeMessage = bo instanceof String ? (String) bo : JSON.toJSONString(bo);
            this.remoteErrorResponse = JSON.toJSONString(errorCodeServiceData);
        }
    }

    /**
     * 远程调用抛异常时，需要记录异常信息时使用，异常信息不会返回到前台
     *
     * @param e
     * @param errorCode
     * @param arguments
     */
    public ErrorCodeRemoteException(Throwable e, @PropertyKey(resourceBundle = ErrorCodeMessageUtil.ERRORCODEMESSAGES_BUNDLE)String errorCode, Object... arguments) {
        super(e.getMessage(), e, false, false);
        if(e instanceof ErrorCodeRemoteException){
            this.remoteErrorResponse = ((ErrorCodeRemoteException) e).remoteErrorResponse;
        }
        setFieldValue(remoteErrorResponse, errorCode, arguments);
    }
    /**
     * 远程调用抛异常时，需要记录异常信息时使用，异常信息不会返回到前台
     *
     * @param e
     * @param errorCodeEnum
     * @param arguments
     */
    public ErrorCodeRemoteException(Throwable e, ErrorCode errorCodeEnum, Object... arguments) {
        super(e.getMessage(), e, false, false);
        if(e instanceof ErrorCodeRemoteException){
            this.remoteErrorResponse = ((ErrorCodeRemoteException) e).remoteErrorResponse;
        }
        setFieldValue(remoteErrorResponse, errorCodeEnum.code(), arguments);
    }
    private void setFieldValue(String remoteErrorResponse, String errorCode, Object[] arguments) {
        this.remoteErrorResponse = remoteErrorResponse;
        this.errorCode = errorCode;
        this.arguments = arguments;
        this.errorCodeMessage = ErrorCodeMessageUtil.getErrorCodeMessage(errorCode, arguments);
        this.errorCodeTip= ErrorCodeMessageUtil.getErrorCodeTipByErrorMsg(errorCodeMessage);
        this.errorCodeServiceData =ErrorCodeMessageUtil. getErrorCodeServiceData(errorCode,errorCodeTip,errorCodeMessage);;
    }

}
