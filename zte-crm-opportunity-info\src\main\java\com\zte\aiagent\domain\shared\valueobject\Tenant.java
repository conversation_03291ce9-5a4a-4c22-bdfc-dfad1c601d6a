package com.zte.aiagent.domain.shared.valueobject;

import io.micrometer.core.instrument.util.StringUtils;
import lombok.Value;

/**
 * 租户ID值对象
 * 封装租户标识的业务逻辑
 */
@Value
public class Tenant {

    Long value;

    /** 默认租户ID */
    private static final Long DEFAULT_TENANT_ID = 10001L;

    private Tenant(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("非法租户ID");
        }
        this.value = value;
    }

    /**
     * 从Long创建
     */
    public static Tenant of(Long value) {
        return new Tenant(value);
    }

    /**
     * 从Long创建
     */
    public static Tenant of(String value) {
        if (StringUtils.isBlank(value)) {
            throw new IllegalArgumentException("非法租户ID");
        }
        try {
            Long tenantId = Long.parseLong(value);
            return new Tenant(tenantId);
        } catch (Exception e) {
            throw new IllegalArgumentException("非法租户ID");
        }
    }

    /**
     * 默认租户
     */
    public static Tenant defaultTenant() {
        return new Tenant(DEFAULT_TENANT_ID);
    }

    /**
     * 是否为默认租户
     */
    public boolean isDefault() {
        return DEFAULT_TENANT_ID.equals(this.value);
    }
}
