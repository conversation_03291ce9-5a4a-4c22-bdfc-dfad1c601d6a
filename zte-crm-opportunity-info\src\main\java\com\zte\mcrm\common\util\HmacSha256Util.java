package com.zte.mcrm.common.util;

/* Started by AICoder, pid:fa9a9le2cd763ad147170be1f0887e4b0554eec8 */
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * HMAC-SHA256加密工具类。
 */
public class HmacSha256Util {

    private static final String HMAC_SHA256_ALGORITHM = "HmacSHA256";

    /**
     * 生成HMAC-SHA256签名。
     *
     * @param data     要签名的数据
     * @param key      密钥
     * @return         签名的Base64编码字符串
     * @throws NoSuchAlgorithmException 如果算法不可用
     * @throws InvalidKeyException      如果密钥无效
     */
    public static String generateHmacSha256(String data, String key) throws NoSuchAlgorithmException, InvalidKeyException {
        byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
        SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, HMAC_SHA256_ALGORITHM);
        Mac mac = Mac.getInstance(HMAC_SHA256_ALGORITHM);
        mac.init(secretKeySpec);
        byte[] hmacBytes = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hmacBytes);
    }
}
/* Ended by AICoder, pid:fa9a9le2cd763ad147170be1f0887e4b0554eec8 */