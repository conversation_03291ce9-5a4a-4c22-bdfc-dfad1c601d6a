package com.zte.mcrm.clues.model;

import com.zte.itp.msa.core.model.HeaderData;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

import org.hibernate.validator.constraints.Length;
import javax.validation.constraints.NotBlank;

import com.fasterxml.jackson.annotation.JsonFormat;

/****
 *
 * <AUTHOR> @date 2021/2/8
 **/
public class CluesSaveDTO extends HeaderData {
	@NotBlank(message="请提供线索名称")
	@ApiModelProperty("线索名称")
	private String clueName;

	@NotBlank(message="请提供业务范围独立源代码")
	@ApiModelProperty("业务范围独立源代码")
	private String businessTypeCode;
	
	@NotBlank(message="请提供代表处ID")
	@ApiModelProperty("代表处id")
	private String deptId;
	
	
	@ApiModelProperty("客户ＩＤ")
	@NotBlank(message="请提供客户ID")
	private String acctId;
	
	
	@ApiModelProperty("客户类型语言代码")
	@NotBlank(message="请提供客户类型独立语言代码")
	private String acctTypeCode;
	

	@ApiModelProperty("线索来源语言代码")
	@NotBlank(message="请提供线索来源")
	private String clueSourceCode;

	@ApiModelProperty("币种")
	private String currency;
	
	@ApiModelProperty("归属客户经理")
	private String ownerMgr;
	
	
	
	@ApiModelProperty("客户投资规模")
	private Double investmentScaleOfAcct;
	
	
	@ApiModelProperty("预计签单金额")
	private Double predictSignAmt;
	
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@ApiModelProperty("预计签单时间")
	private Date predictSignDate;

	@ApiModelProperty("销售模式语言代码")
	private String saleModelCode;

	@ApiModelProperty("线索背景")
	@Length(max=499,message="线索背景请不要超过499个字")
	private String background;
	
	
	@ApiModelProperty("市场类型")
	private String marketTypeCode;
	@ApiModelProperty(value="是否夸事业部运作",name="value",allowableValues="Y,N")
	private String mulDivisionFlg;
	
	@ApiModelProperty("技术经理ID")
	private String techMgrId;
	
	@ApiModelProperty(value="请求方式",allowableValues="submit,save")
	private String postType;
	
	
	@ApiModelProperty("大产品线ID")
	private String bigProdcutLineId;
	@ApiModelProperty("大产品线name")
	private String bigProductLine;
	@ApiModelProperty("产品体系分类ID")
	private String prodSystemId;
	@ApiModelProperty("产品体系分类name")
	private String prodSystem;
	@ApiModelProperty("是否融资")
	private String foundFlg;
	private String foundFlgCode;
	@ApiModelProperty("客户属性")
	private String accountAttribute;
	private String accountAttributeCode;
	@ApiModelProperty("潜在融资模式")
	private String potentialModel;
	private String potentialModelCode;
	@ApiModelProperty("行业")
	private String parentTradeCode;
	@ApiModelProperty("子行业")
	private String childTradeCode;
	@ApiModelProperty("最终客户id")
	private String lastAcctId;
	
	public String getLastAcctId() {
		return lastAcctId;
	}
	public void setLastAcctId(String lastAcctId) {
		this.lastAcctId = lastAcctId;
	}
	public String getParentTradeCode() {
		return parentTradeCode;
	}
	public void setParentTradeCode(String parentTradeCode) {
		this.parentTradeCode = parentTradeCode;
	}
	public String getChildTradeCode() {
		return childTradeCode;
	}
	public void setChildTradeCode(String childTradeCode) {
		this.childTradeCode = childTradeCode;
	}
	public String getFoundFlg() {
		return foundFlg;
	}
	public void setFoundFlg(String foundFlg) {
		this.foundFlg = foundFlg;
	}
	public String getFoundFlgCode() {
		return foundFlgCode;
	}
	public void setFoundFlgCode(String foundFlgCode) {
		this.foundFlgCode = foundFlgCode;
	}
	public String getAccountAttribute() {
		return accountAttribute;
	}
	public void setAccountAttribute(String accountAttribute) {
		this.accountAttribute = accountAttribute;
	}
	public String getAccountAttributeCode() {
		return accountAttributeCode;
	}
	public void setAccountAttributeCode(String accountAttributeCode) {
		this.accountAttributeCode = accountAttributeCode;
	}
	public String getPotentialModel() {
		return potentialModel;
	}
	public void setPotentialModel(String potentialModel) {
		this.potentialModel = potentialModel;
	}
	public String getPotentialModelCode() {
		return potentialModelCode;
	}
	public void setPotentialModelCode(String potentialModelCode) {
		this.potentialModelCode = potentialModelCode;
	}
	public String getTechMgrId() {
		return techMgrId;
	}
	public void setTechMgrId(String techMgrId) {
		this.techMgrId = techMgrId;
	}
	public String getClueName() {
		return clueName;
	}
	public void setClueName(String clueName) {
		this.clueName = clueName;
	}
	public String getBusinessTypeCode() {
		return businessTypeCode;
	}
	public void setBusinessTypeCode(String businessTypeCode) {
		this.businessTypeCode = businessTypeCode;
	}
	public String getDeptId() {
		return deptId;
	}
	public void setDeptId(String deptId) {
		this.deptId = deptId;
	}
	public String getOwnerMgr() {
		return ownerMgr;
	}
	public void setOwnerMgr(String ownerMgr) {
		this.ownerMgr = ownerMgr;
	}
	public String getCurrency() {
		return currency;
	}
	public void setCurrency(String currency) {
		this.currency = currency;
	}
	public Double getInvestmentScaleOfAcct() {
		return investmentScaleOfAcct;
	}
	public void setInvestmentScaleOfAcct(Double investmentScaleOfAcct) {
		this.investmentScaleOfAcct = investmentScaleOfAcct;
	}
	public Double getPredictSignAmt() {
		return predictSignAmt;
	}
	public Date getPredictSignDate() {
		return predictSignDate;
	}
	public void setPredictSignDate(Date predictSignDate) {
		this.predictSignDate = predictSignDate;
	}
	public String getAcctId() {
		return acctId;
	}
	public void setAcctId(String acctId) {
		this.acctId = acctId;
	}
	public String getAcctTypeCode() {
		return acctTypeCode;
	}
	public void setAcctTypeCode(String acctTypeCode) {
		this.acctTypeCode = acctTypeCode;
	}
	public String getSaleModelCode() {
		return saleModelCode;
	}
	public void setSaleModelCode(String saleModelCode) {
		this.saleModelCode = saleModelCode;
	}
	public String getClueSourceCode() {
		return clueSourceCode;
	}
	public void setClueSourceCode(String clueSourceCode) {
		this.clueSourceCode = clueSourceCode;
	}
	public String getBackground() {
		return background;
	}
	public void setBackground(String background) {
		this.background = background;
	}

	
	public String getMarketTypeCode() {
		return marketTypeCode;
	}
	public void setMarketTypeCode(String marketTypeCode) {
		this.marketTypeCode = marketTypeCode;
	}
	public String getMulDivisionFlg() {
		return mulDivisionFlg;
	}
	public void setMulDivisionFlg(String mulDivisionFlg) {
		this.mulDivisionFlg = mulDivisionFlg;
	}
	public String getPostType() {
		return postType;
	}
	public void setPostType(String postType) {
		this.postType = postType;
	}
	public String getBigProdcutLineId() {
		return bigProdcutLineId;
	}
	public void setBigProdcutLineId(String bigProdcutLineId) {
		this.bigProdcutLineId = bigProdcutLineId;
	}
	public String getBigProductLine() {
		return bigProductLine;
	}
	public void setBigProductLine(String bigProductLine) {
		this.bigProductLine = bigProductLine;
	}
	public String getProdSystemId() {
		return prodSystemId;
	}
	public void setProdSystemId(String prodSystemId) {
		this.prodSystemId = prodSystemId;
	}
	public void setPredictSignAmt(Double predictSignAmt) {
		this.predictSignAmt = predictSignAmt;
	}
	public String getProdSystem() {
		return prodSystem;
	}
	public void setProdSystem(String prodSystem) {
		this.prodSystem = prodSystem;
	}

	@Override
	public String toString() {
		return "CluesSaveDTO{" +
				"clueName='" + clueName + '\'' +
				", businessTypeCode='" + businessTypeCode + '\'' +
				", deptId='" + deptId + '\'' +
				", acctId='" + acctId + '\'' +
				", acctTypeCode='" + acctTypeCode + '\'' +
				", clueSourceCode='" + clueSourceCode + '\'' +
				", currency='" + currency + '\'' +
				", ownerMgr='" + ownerMgr + '\'' +
				", investmentScaleOfAcct=" + investmentScaleOfAcct +
				", predictSignAmt=" + predictSignAmt +
				", predictSignDate=" + predictSignDate +
				", saleModelCode='" + saleModelCode + '\'' +
				", background='" + background + '\'' +
				", marketTypeCode='" + marketTypeCode + '\'' +
				", mulDivisionFlg='" + mulDivisionFlg + '\'' +
				", techMgrId='" + techMgrId + '\'' +
				", postType='" + postType + '\'' +
				", bigProdcutLineId='" + bigProdcutLineId + '\'' +
				", bigProductLine='" + bigProductLine + '\'' +
				", prodSystemId='" + prodSystemId + '\'' +
				", prodSystem='" + prodSystem + '\'' +
				", foundFlg='" + foundFlg + '\'' +
				", foundFlgCode='" + foundFlgCode + '\'' +
				", accountAttribute='" + accountAttribute + '\'' +
				", accountAttributeCode='" + accountAttributeCode + '\'' +
				", potentialModel='" + potentialModel + '\'' +
				", potentialModelCode='" + potentialModelCode + '\'' +
				", parentTradeCode='" + parentTradeCode + '\'' +
				", childTradeCode='" + childTradeCode + '\'' +
				", lastAcctId='" + lastAcctId + '\'' +
				'}';
	}
}
