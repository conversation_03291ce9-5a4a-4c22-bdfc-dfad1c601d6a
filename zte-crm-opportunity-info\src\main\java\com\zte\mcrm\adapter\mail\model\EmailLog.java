package com.zte.mcrm.adapter.mail.model;

import lombok.Data;

import java.util.Date;

@Data
public class EmailLog {

    private Long emailLogId;

    /**
     * 邮件类型
     */
    private String emailType;

    /**
     * 邮件标题-中文
     */
    private String emailTitleCn;

    /**
     * 邮件标题-英文
     */
    private String emailTitleEn;

    /**
     * 邮件发送人
     */
    private String emailFrom;

    /**
     * 邮件接收人（可能工号、邮箱，多个用分号隔开）
     */
    private String emailTo;

    /**
     * 是否需要重试(Y是N否)
     */
    private String retryFlag;

    /**
     * 重试次数
     */
    private Long retryTimes;

    /**
     * 邮件抄送人
     */
    private String emailCc;

    /**
     * 邮件密送人
     */
    private String emailBcc;

    /**
     * 邮件内容-中文
     */
    private String emailContentCn;

    /**
     * 邮件内容-英文
     */
    private String emailContentEn;

    /**
     * 点击查看-中文
     */
    private String clickLookCn;

    /**
     * 点击查看-英文
     */
    private String clickLookEn;

    /**
     * 点击查看的链接-中文
     */
    private String linkAddCn;

    /**
     * 点击查看的链接-英文
     */
    private String linkAddEn;

    /**
     * 温馨提示-中文
     */
    private String warmCallCn;

    /**
     * 温馨提示-英文
     */
    private String warmCallEn;

    /**
     * 扩展字段1
     */
    private String standbyFieldOne;

    private String standbyFieldTwo;

    private String standbyFieldThree;

    private String standbyFieldFour;

    private String standbyFieldFive;

    private Date createdDate;

    private String createdBy;

    private Date lastUpdatedDate;

    private String lastUpdatedBy;

    private String enabledFlag;

    private Long tenantId;
}