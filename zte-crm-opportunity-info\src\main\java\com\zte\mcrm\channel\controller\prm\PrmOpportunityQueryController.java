package com.zte.mcrm.channel.controller.prm;


import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import com.zte.mcrm.channel.model.dto.OpportunityTransferredProjectDTO;
import com.zte.mcrm.channel.model.dto.PrmOpportunityPendingDTO;
import com.zte.mcrm.channel.model.dto.PrmOpportunityQueryDTO;
import com.zte.mcrm.channel.model.dto.WinOpportunityQueryDTO;
import com.zte.mcrm.channel.model.entity.OpportunityInfo;
import com.zte.mcrm.channel.model.entity.PrmOpptyCustomerCreateRecordQuery;
import com.zte.mcrm.channel.model.vo.OpptyCustomerCreateRecordVO;
import com.zte.mcrm.channel.model.vo.PrmOpportunityPendingVO;
import com.zte.mcrm.channel.model.vo.PrmOpportunityVO;
import com.zte.mcrm.channel.model.vo.WinOpportunityVO;
import com.zte.mcrm.channel.service.prm.IPrmOpportunityService;
import com.zte.mcrm.common.util.ValidationGroups;
import com.zte.mcrm.opportunity.access.vo.ConversionProjectOrdinary;
import com.zte.mcrm.predict.access.vo.BusinessOrderProdsVO;
import com.zte.opty.model.dto.IHolOrgQueryDTO;
import com.zte.opty.model.dto.OptyPlmProjectQueryDTO;
import com.zte.opty.model.vo.IHolOrgVO;
import com.zte.opty.model.vo.OptyPlmProjectVO;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import com.zte.springbootframe.util.product.ProductUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags ="PRM侧商机查询API")
@RestController
@RequestMapping("/prm")
public class PrmOpportunityQueryController {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /** 服务对象，SPRING自动装配 */
    @Autowired
    IPrmOpportunityService prmOpportunityService;

    @ApiOperation("分页查询商机列表")
    @PostMapping(value = "/getOpportunityPage")
    public ServiceData<PageRows<PrmOpportunityVO>> getOpportunityList(@RequestHeader(value = SysGlobalConst.HTTP_HEADER_X_EMP_NO, required = true) String empNo,
                                                                      @RequestBody FormData<PrmOpportunityQueryDTO> formData) throws Exception {
        PageRows<PrmOpportunityVO> page = new PageRows<>();
        // empNo为空则直接返回为空
        if(StringUtils.isBlank(empNo)){
            return ServiceResultUtil.success(page);
        }
        page= prmOpportunityService.getPrmOpportunityPage(formData,Boolean.FALSE );
        return ServiceResultUtil.success(page);
    }

    @ApiOperation("商机待我审批列表-分页查询 POST请求")
    @PostMapping(value = "/getOpportunityPenging")
    public ServiceData<PageRows<PrmOpportunityPendingVO>> getOpportunityPenging(@RequestBody FormData<PrmOpportunityPendingDTO> formData) throws Exception {
        return ServiceResultUtil.success(prmOpportunityService.getOpportunityPendingPage(formData));
    }

    @ApiOperation("查询商机详情 -Get方式")
    @GetMapping(value="/opportunityinfo/{rowId}")
    public ServiceData<OpportunityInfo> getOpportunityInfo(@PathVariable("rowId") String rowId) throws Exception {
        //业务操作可以不捕获异常,由统一的异常处理方法处理
        OpportunityInfo sOpty = prmOpportunityService.getOpportunity(rowId);

        //返回统一的服务端数据null
        return ServiceResultUtil.success(sOpty);
    }
    @ApiOperation("导出商机")
    @PostMapping(value = "/exportOpportunityList")
    public void exportOpportunityList(HttpServletResponse response, HttpServletRequest request,
                                      @ApiParam(value = "导出请求对象", required = true) @RequestBody FormData<PrmOpportunityQueryDTO>  form) throws Exception {
        HttpSession session=request.getSession(true);
        session.setMaxInactiveInterval(360000);
        prmOpportunityService.exportOpportunityList(form,response);
    }
    @ApiOperation("查询产品树")
    @GetMapping(value="/opportunities/producttree")
    public ServiceData<BusinessOrderProdsVO> getProductTree() throws Exception {
        return ServiceResultUtil.success(ProductUtil.getFiveLevelProd());
    }

    @ApiOperation("商机转立项")
    @PostMapping(value = "/opportunities/transfer/project")
    public ServiceData<ConversionProjectOrdinary> transferProject(@Validated @RequestBody OpportunityTransferredProjectDTO entity,
                                                                  BindingResult bindingResult) throws Exception {
        // 数据校验
        if (bindingResult != null && bindingResult.hasErrors()) {
            throw new com.zte.springbootframe.common.exception.ValidationException(bindingResult);
        }
        return ServiceResultUtil.success(prmOpportunityService.transferProject(entity));
    }

    @ApiOperation("商机快速转立项")
    @PostMapping(value = "/opportunities/fasttransfer/project")
    public ServiceData<ConversionProjectOrdinary> fastTransferProject(@Validated(ValidationGroups.Fasttransfer.class) @RequestBody OpportunityTransferredProjectDTO entity,
                                                                      BindingResult bindingResult) throws Exception {
        // 数据校验
        if (bindingResult != null && bindingResult.hasErrors()) {
            throw new com.zte.springbootframe.common.exception.ValidationException(bindingResult);
        }
        return ServiceResultUtil.success(prmOpportunityService.fastTransferProject(entity));
    }

    @ApiOperation("商机转立项跳转验证")
    @GetMapping(value = "/transfer/project/validate/{rowId}")
    public ServiceData<ConversionProjectOrdinary> transferProjectValidate(@PathVariable("rowId") String rowId) throws Exception {
        return ServiceResultUtil.success(prmOpportunityService.transferProjectValidate(rowId));
    }

    @ApiOperation("渠道商创建客户记录查询接口")
    @PostMapping(value = "/createCustomer/query")
    public ServiceData<PageRows<OpptyCustomerCreateRecordVO>> queryCustomerRecord(@RequestBody FormData<PrmOpptyCustomerCreateRecordQuery> formData) throws Exception{
        return ServiceResultUtil.success(prmOpportunityService.queryCustomerRecord(formData));
    }

    @ApiOperation("导入公司主产品")
    @PostMapping(value = "/importPdm")
    public ServiceData<Void> importPdm(@ApiParam(value = "导入",required = true) @RequestPart("file") MultipartFile file) throws IOException {
        prmOpportunityService.importPdm(file);
        return ServiceResultUtil.success(null);
    }

    @ApiOperation("查询组织数据")
    @PostMapping(value = "/queryOrg")
    public ServiceData<PageRows<IHolOrgVO>> queryOrg(@RequestBody IHolOrgQueryDTO queryDTO)  {
        return prmOpportunityService.queryOrg(queryDTO);
    }

    @ApiOperation("查询产品数据")
    @PostMapping(value = "/queryProduct")
    public ServiceData<PageRows<OptyPlmProjectVO>> queryProduct(@RequestBody OptyPlmProjectQueryDTO deptPath)  {
        return prmOpportunityService.queryProduct(deptPath);
    }

    @ApiOperation("查询中标商机信息")
    @PostMapping(value = "/queryWinOpportunities")
    public ServiceData<List<WinOpportunityVO>> queryWinOpportunities(@RequestBody WinOpportunityQueryDTO queryDTO) throws Exception {
        List<WinOpportunityVO> result = prmOpportunityService.queryWinOpportunities(queryDTO);
        return ServiceResultUtil.success(result);
    }
}
