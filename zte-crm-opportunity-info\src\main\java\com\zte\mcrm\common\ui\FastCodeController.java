package com.zte.mcrm.common.ui;

import com.zte.iss.misccomponents.sdk.model.dto.BasLookupValuesDTO;
import com.zte.iss.misccomponents.sdk.model.param.LookupTypeBatchParam;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import com.zte.mcrm.common.business.IFastCodeService;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import com.zte.springbootframe.common.model.ServiceDataUtil;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName: FastCodeController
 * @Description: 快码服务API
 * @author: 10288408
 * @date: 2023/8/28
 */
@Api("快码服务API")
@RestController
@RequestMapping("/fastCode")
public class FastCodeController {
    @Autowired
    IFastCodeService fastCodeService;

    @GetMapping(value="/getLookupInfo")
    public ServiceData<List<BasLookupValuesDTO>> getLookupInfo(
            @RequestParam(name="lovType")String lovType,
            @RequestParam(name="accessSystem")String accessSystem,
            @RequestParam(name="enableFlag",required=false)String enableFlag,
            @RequestParam(name="sortOrder") String sortOrder
    ) throws Exception{

        return ServiceResultUtil.success(fastCodeService.getLookupInfo(lovType, accessSystem, enableFlag, sortOrder));
    }
}
