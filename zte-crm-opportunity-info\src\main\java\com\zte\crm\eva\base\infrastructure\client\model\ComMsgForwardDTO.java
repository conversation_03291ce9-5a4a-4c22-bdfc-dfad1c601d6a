package com.zte.crm.eva.base.infrastructure.client.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * 消息转发
 * @Author: <EMAIL>
 * @Date: 2021/12/17
 * @Description:
 */
@Getter
@Setter
@ToString
public class ComMsgForwardDTO {

    /**
     *消息编号
     */
    private String msgId;
    /**
     * 消息类型
     */
    private Integer type;
    /**
     *接收人
     */
    private List<String> to;
    /**
     *抄送人
     */
    private List<String> cc;
    /**
     *密送人
     */
    private List<String> bcc;
    /**
     * 发送人
     */
    private String sender;
    /**
     *模板中变量参数键值对 k-v
     */
    private Map<String, String> args;
    /**
     * 角色和约束
     */
    private List<RoleInfoDTO> roleConstraints;
}
