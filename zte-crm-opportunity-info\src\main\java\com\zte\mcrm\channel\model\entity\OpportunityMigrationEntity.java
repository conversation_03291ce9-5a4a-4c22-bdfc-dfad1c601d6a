package com.zte.mcrm.channel.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString
public class OpportunityMigrationEntity {
    @ApiModelProperty(value = "商机id")
    String rowId;
    @ApiModelProperty(value = "商机编号")
    private String optyCd;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "创建人")
    private String createdBy;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpd;
    @ApiModelProperty(value = "最后更新人")
    private String lastUpdBy;
    @ApiModelProperty(value = "中兴业务经理id")
    private String businessManagerId;
    @ApiModelProperty(value = "中兴业务经理id-prm")
    private String prmBusinessManagerId;
    @ApiModelProperty(value = "商机负责人id")
    private String opportunityOwnerId;
}
