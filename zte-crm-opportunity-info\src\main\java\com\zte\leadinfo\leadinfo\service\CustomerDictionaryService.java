package com.zte.leadinfo.leadinfo.service;

import com.zte.leadinfo.leadinfo.entity.CustomerDictionaryDO;

import java.util.List;

public interface CustomerDictionaryService {

    /**
     * 批量查询编码和ID，作为入参
     * @param accountList
     * @return
     */
    List<CustomerDictionaryDO> selectByIds(List<String> accountList);

    /**
     * 批量插入数据，客户信息
     * @return
     */
    Boolean batchInsert(List<CustomerDictionaryDO> list);
}
