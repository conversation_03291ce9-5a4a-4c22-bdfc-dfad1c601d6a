package com.zte.mcrm.adapter.clouddisk.util;


import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 统一返回服务结果
 * @author: 6407001936 YeZhibin
 * @date: 2019/4/3
 */
public class ServiceDataUtil {

    private ServiceDataUtil() {
        throw new IllegalStateException("ServiceDataUtil class");
    }

    public static <T> boolean isSuccess(ServiceData<T> sd) {
        return null != sd && null != sd.getCode() && RetCode.SUCCESS_CODE.equals(sd.getCode().getCode());
    }

    public static <T> boolean isFailed(ServiceData<T> sd) {
        return !isSuccess(sd);
    }

    public static <T> ServiceData<T> success() {
        return success(null);
    }

    public static <T> ServiceData<T> success(T bo) {
        return success(bo, null);
    }

    public static <T> ServiceData<T> success(T bo, Map<String, Object> other) {
        return getInstance(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID), bo, other);
    }

    /**
     * 认证失败
     */
    public static <T> ServiceData<T> authFailedError(Map<String, Object> other) {
        return authFailedError(null, other);
    }

    /**
     * 认证失败
     */
    public static <T> ServiceData<T> authFailedError(T bo, Map<String, Object> other) {
        return getInstance(new RetCode(RetCode.AUTHFAILED_CODE, RetCode.AUTHFAILED_MSGID), bo, other);
    }

    /**
     * 拒接访问
     */
    public static <T> ServiceData<T> permissionDeniedError(Map<String, Object> other) {
        return permissionDeniedError(null, other);
    }

    /**
     * 拒接访问
     */
    public static <T> ServiceData<T> permissionDeniedError(T bo, Map<String, Object> other) {
        return getInstance(new RetCode(RetCode.PERMISSIONDENIED_CODE, RetCode.PERMISSIONDENIED_MSGID), bo, other);
    }

    /**
     * 验证失败
     */
    public static <T> ServiceData<T> validationError(Map<String, Object> other) {
        return validationError(null, other);
    }

    /**
     * 验证失败
     */
    public static <T> ServiceData<T> validationError(T bo, Map<String, Object> other) {
        return getInstance(new RetCode(RetCode.VALIDATIONERROR_CODE, RetCode.VALIDATIONERROR_MSGID), bo, other);
    }

    /**
     * 业务异常
     */
    public static <T> ServiceData<T> businessError(String errorOther) {
        return businessError(null, errorOther);
    }

    /**
     * 业务异常
     */
    public static <T> ServiceData<T> businessError(Map<String, Object> other) {
        return businessError(null, other);
    }

    /**
     * 业务异常
     */
    public static <T> ServiceData<T> businessError(T bo, String errorOther) {
        return getInstance(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID), bo, errorOther);
    }

    /**
     * 业务异常
     */
    public static <T> ServiceData<T> businessError(T bo, Map<String, Object> other) {
        return getInstance(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID), bo, other);
    }

    public static <T> ServiceData<T> getInstance(RetCode code) {
        return getInstance(code, null);
    }

    public static <T> ServiceData<T> getInstance(RetCode code, T bo) {
        return getInstance(code, bo, "");
    }

    public static <T> ServiceData<T> getInstance(RetCode code, T bo, String errorOther) {
        Map<String, Object> other = new HashMap<>(16);
        other.put("error", errorOther);
        return getInstance(code, bo, other);
    }

    public static <T> ServiceData<T> getInstance(RetCode code, T bo, Map<String, Object> other) {
        ServiceData<T> serviceData = new ServiceData<T>();
        serviceData.setCode(code);
        serviceData.setBo(bo);
        serviceData.setOther(other);
        return serviceData;
    }

}
