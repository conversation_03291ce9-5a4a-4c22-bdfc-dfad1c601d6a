package com.zte.mcrm.channel.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Setter
@Getter
@ToString
public class ApprovalCallBackMsgInfo {

    /** 流程实例Id*/
    private String flowInstanceId;

    /** 业务主键Id*/
    private String businessId;

    /** 节点唯一标识*/
    private String nodeCode;

    /**
     * 节点名称
     */
    private String nodeName;

    /** 任务ID*/
    private String taskId;

    /** 创建时间*/
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /** 审批人*/
    private String approver;

    /** 审批结果*/
    private String result;

    /** 审批完成时间*/
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date approvalDate;

    /** 审批意见*/
    private String opinion;

    /** 业务经理及TS单号*/
    private String extOpinion;

    /** 流程类型编码*/
    private String flowCode;

    /** 节点扩展编码 */
    private String extendedCode;
}
