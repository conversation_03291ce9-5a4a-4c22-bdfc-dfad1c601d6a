package com.zte.mcrm.common.consts;

import com.zte.itp.msa.core.model.RetCode;

public class CommonRetCode extends RetCode {

    /**
     * 文件大小超过100MB
     */
    public static final String FILE_SIZE_BEYOND = "RetCode.file.size.beyond";

    /**
     * 加密文件不能上传
     */
    public static final String FILE_ENCRYPTION = "RetCode.file.encryption";

    /**
     *  空变量异常
     */
    public static final String VARIABLE_NULL = "RetCode.variable.encryption";

    /**
     *  文档云服务异常
     */
    public static final String DOC_CLOUD_SERVICE_ERROR = "RetCode.docCloudService.error";

    /**
     * 　列表数据超过200
     */
    public static final String CODE_TYPE_LIST_SIZE_BEYOND = "RetCode.codeTypeList.size.beyond";

    /** 加密失败*/
    public static final String ENCRYPT_FAILED = "RetCode.encrypt.failed";

    /** 解密失败*/
    public static final String DECRYPT_FAILED = "RetCode.decrypt.failed";

    /**
     * 渠道商信息为空
     */
    public static final String CUSTOMERINFO_NULL = "RetCode.customerInfo.null";

    /**
     * 附件信息为空
     */
    public static final String FILEINFO_NULL = "RetCode.fileInfo.null";

    /**
     * 商机已转立项
     */
    public static final String HAS_TRANSFER_PROJECT = "RetCode.has.transfer.project";

    /**
     * 非渠道报备不能快速转立项
     */
    public static final String OPPORTUNITY_TYPE_ERROR = "RetCode.opportunity.type.error";
}
