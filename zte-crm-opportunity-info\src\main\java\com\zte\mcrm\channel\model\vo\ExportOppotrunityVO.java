package com.zte.mcrm.channel.model.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: <EMAIL>
 * @Date: 2021/10/26
 * @Description:
 */
@Data
public class ExportOppotrunityVO {
    /**
     * 商机编码
     */
    private String optyCd;
    /**
     * 商机名称
     */
    private String opportunityName;
    /**
     * 最终用户名称
     */
    private String finalCustomerName;
    /**
     * 投资方所在地（商机所属部门）名称
     */
    private String deptName;
    /**
     * 最终用户行业(父行业-子行业形式)
     */
    private String finalCustomerTradeName;
    /**
     * 当前阶段
     */
    private String projectPhasesName;
    /**
     * 招标类型名称
     */
    private String tendTypeName;
    /**
     * 预计发标/议标日期
     */
    private String estimatedBiddingTime;
    /**
     * 竞标截止日期
     */
    private String biddingDeadline;
    /**
     * 渠道商
     */
    private String channelBusiness;
    /**
     * 商机状态
     */
    private String statusName;
    /**
     * 中兴业务经理id
     */
    private String businessManagerId;
    /**
     * 中兴业务经理名字
     */
    private String businessManagerName;
    /**
     * 赢率
     */
    private String winRateName;
    /**
     * 招标方全称
     */
    private String bidProviderName;
    /**
     * 产品名称
     */
    private String productNames;
    /**
     * 预计签单金额
     */
    private BigDecimal totalAmount;
    /**
     * 组织全路径
     */
    private String hrOrgNamePath;
    /**
     * 当前处理人
     */
    private String currentProcessor;
    /**
     * 联系人姓名
     */
    private String finalCustomerContactName;
    /**
     * 是否自用设备
     */
    private String selfUseFlag;
    /**
     * 激活次数
     */
    private String activeCount;
    /**
     * 报备时间
     */
    private String created;
    /**
     * 失效日期
     */
    private String expiryDate;
    /**
     * 授权次数
     */
    private Long numberOfAuthorizations;
    /**
     * 授权状态
     */
    private String statusAuthName;
    /**
     * 是否属于激活报备(Y/N)
     */
    private String fromActiveFlag;
    /**
     * 从哪个商机激活的(记录商机id)
     */
    private String fromActiveOpty;
    /**
     * 授权编号/授权状态
     */
    private String authIdAndStatus;
    /**
     * 激活报备编号/状态
     */
    private String activatedIdAndStatus;
    /**
     *最终用户客户编码
     */
    private String finalCustomerId;
}
