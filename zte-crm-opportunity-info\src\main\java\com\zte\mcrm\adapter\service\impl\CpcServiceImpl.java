package com.zte.mcrm.adapter.service.impl;


import com.google.common.collect.Lists;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.adapter.constant.BlackListTypeEnum;
import com.zte.mcrm.adapter.constant.ReasonCategoryEnum;
import com.zte.mcrm.adapter.model.dto.*;
import com.zte.mcrm.adapter.model.mapper.CompanyOrgInfoMapper;
import com.zte.mcrm.adapter.model.vo.CompanyOrgInfoVO;
import com.zte.mcrm.adapter.model.vo.CompanySnapshotVO;
import com.zte.mcrm.adapter.model.vo.UnregisteredCompanyVO;
import com.zte.mcrm.adapter.service.ComposeService;
import com.zte.mcrm.adapter.service.ICpcService;
import com.zte.mcrm.adapter.service.ICpcServiceApi;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2021/07/21
 * @Description:
 */
@Service
@Slf4j
public class CpcServiceImpl implements ICpcService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    ICpcServiceApi cpcServiceApi;

    @Autowired
    ComposeService composeService;

    /**
     * 通过关键字搜索公司信息
     *
     * @param form
     * @return
     */
    @Override
    public PageRows<UnregisteredCompanyVO> queryCompanyInfoList(FormData<ChannelCompanySearchDTO> form) {
        ChannelCompanySearchDTO channelCompanySearchDto = form.getBo();
        PageRows<UnregisteredCompanyVO> voPageRows = new PageRows<>();
        voPageRows.setRows(Collections.emptyList());
        voPageRows.setTotal(0L);
        if(null ==channelCompanySearchDto){
            return voPageRows;
        }
        CompanyInfoSearchDTO companyInfoSearchDto = channelCompanySearchDto.toCompanyInfoSearchDTO();
        FormData<CompanyInfoSearchDTO> searchForm =new FormData<>();
        searchForm.setRows(form.getRows());
        searchForm.setPage(form.getPage());
        searchForm.setBo(companyInfoSearchDto);
        PageRows<CompanyInfoDTO> pageRows= null;
        try {
            pageRows = cpcServiceApi.queryCompanyInfoList(searchForm);
        } catch (Exception e) {
            logger.error("CpcServiceImpl.queryCompanyInfoList excepiton：" + e.getMessage(), e);
        }
        if(null==pageRows){
            return voPageRows;
        }
        List<CompanyInfoDTO> rows = pageRows.getRows();
        if(null==rows){
            return voPageRows;
        }
        List<UnregisteredCompanyVO> vos = rows.stream().map(CompanyInfoDTO::toUnregisteredCompany).collect(Collectors.toList());
        voPageRows = new PageRows<>();
        voPageRows.setTotal(pageRows.getTotal());
        voPageRows.setCurrent(pageRows.getCurrent());
        voPageRows.setRows(vos);
        return voPageRows;
    }

    /**
     * 通过企业统一代码获取公司详细信息
     *
     * @param partnerName
     * @return
     */
    @Override
    public UnregisteredCompanyVO searchCompanyInfo(String partnerName) {
        UnregisteredCompanyVO resultVO = new UnregisteredCompanyVO();
        if(StringUtils.isBlank(partnerName)){
            return resultVO;
        }
        CompanyInfoDTO bo = null;
        try {
            bo = cpcServiceApi.searchCompanyInfo(partnerName);
        } catch (Exception e) {
            logger.error("CpcServiceImpl.searchCompanyInfo excepiton：" + e.getMessage(), e);
        }
        if (null == bo) {
            return resultVO;
        }
        log.info("接口返回值：" + bo);
        resultVO = bo.toUnregisteredCompany();
        return resultVO;
    }

    /**
     * 通过企业统一代码获取公司 黄黑名单信息
     * <AUTHOR>
     */
    @Override
    public List<BlackListDTO> getBlackListInfo(String orgUnifiedCode) throws Exception{
        CompanyBatchInfoDTO entity = new CompanyBatchInfoDTO();
        entity.setOrgUnifiedCodes(Lists.newArrayList(orgUnifiedCode));
        entity.setStatus(Lists.newArrayList(OpportunityConstant.BLACK_LIST_STATUS_EFFECTIVE));
        PageRows<BlackListDTO> blackListBatch = cpcServiceApi.getBlackListBatch(entity);
        List<BlackListDTO> rows = new ArrayList<>();
        if(null != blackListBatch && CollectionUtils.isNotEmpty(blackListBatch.getRows())){
            rows = blackListBatch.getRows();
        }
        for (BlackListDTO item : rows) {
            //  惩罚类型
            item.setListType(BlackListTypeEnum.getChineseDescriptionByKey(item.getListType()));
            // 惩罚原因
            item.setReasonCategory(ReasonCategoryEnum.getChineseDescriptionByKey(item.getReasonCategory()));
        }
        return rows;
    }


    /**
     * 通过企业统一代码获取公司 黄黑名单信息
     * <AUTHOR>
     */
    @Override
    public List<BlackListDTO> getBlackListInfoByCrmCustomerCode(String crmCustomerCode) throws Exception{
        ChannelCustomerRes channelCustomerRes = composeService.getChannelInfo(crmCustomerCode);
        String orgUnifiedCode = channelCustomerRes.getOrgUnifiedCode();
        return getBlackListInfo(orgUnifiedCode);
    }

    @Override
    public CompanyOrgInfoVO queryOrgInfoByCustomerName(CompanyInfoSearchDTO companyInfoSearch) throws BusiException, RouteException {
        CompanyOrgInfoDTO companyOrgInfoDTO = cpcServiceApi.queryOrgInfoByKeyword(companyInfoSearch);
        CompanyOrgInfoVO companyOrgInfo = CompanyOrgInfoMapper.INSTANCE.transToCompanyOrgInfoVO(companyOrgInfoDTO);
        try {
            CompanySnapshotVO snapshot = cpcServiceApi.querySnapshot(companyInfoSearch);
            companyOrgInfo.setCompanySnapshot(snapshot);
        } catch (Exception e) {
            logger.error("getSnapshotError,input:{}", companyInfoSearch, e);
        }
        return companyOrgInfo;
    }
}
