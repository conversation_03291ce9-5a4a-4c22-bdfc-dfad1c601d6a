package com.zte.mcrm.channel.model.dto;

import com.zte.mcrm.channel.model.entity.Opportunity;
import com.zte.mcrm.channel.model.entity.OpportunityDetail;

import com.zte.mcrm.channel.model.entity.OpportunityProduct;
import lombok.Data;

import java.util.List;


/**
 * 商机信息类
 * <AUTHOR>
 * @date 2021-09-15
 */
@Data
public class OpportunityInfoDTO {
    Opportunity opportunity;
    OpportunityDetail opportunityDetail;
    List<OpportunityProduct> opportunityProducts;
}
