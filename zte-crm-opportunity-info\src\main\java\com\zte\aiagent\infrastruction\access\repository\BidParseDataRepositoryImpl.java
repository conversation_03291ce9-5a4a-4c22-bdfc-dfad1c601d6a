package com.zte.aiagent.infrastruction.access.repository;

import com.zte.aiagent.domain.repository.BidParseDataRepository;
import com.zte.aiagent.infrastruction.access.mapper.BidParseDataMapper;
import com.zte.aiagent.infrastruction.access.po.BidParseDataPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 解析数据存储数据访问仓储实现类
 * 负责解析数据相关的数据访问操作
 *
 * <AUTHOR>
 */
@Service
public class BidParseDataRepositoryImpl implements BidParseDataRepository {

    @Autowired
    private BidParseDataMapper bidParseDataMapper;

    @Override
    public int insert(BidParseDataPO bidParseData) {
        return bidParseDataMapper.insert(bidParseData);
    }

    @Override
    public int batchInsert(List<BidParseDataPO> items) {
        return bidParseDataMapper.batchInsert(items);
    }

    @Override
    public BidParseDataPO selectByPrimaryKey(String rowId) {
        return bidParseDataMapper.selectByPrimaryKey(rowId);
    }

    @Override
    public List<BidParseDataPO> selectByRecordIdAndType(String parseRecordId, String dataType) {
        return bidParseDataMapper.selectByRecordIdAndType(parseRecordId, dataType);
    }

    @Override
    public List<BidParseDataPO> selectByParseRecordId(String parseRecordId) {
        return bidParseDataMapper.selectByParseRecordId(parseRecordId);
    }

    @Override
    public int updateByPrimaryKey(BidParseDataPO bidParseData) {
        return bidParseDataMapper.updateByPrimaryKey(bidParseData);
    }

    @Override
    public int deleteByPrimaryKey(String rowId) {
        return bidParseDataMapper.deleteByPrimaryKey(rowId);
    }

    @Override
    public int deleteByParseRecordId(String parseRecordId) {
        return bidParseDataMapper.deleteByParseRecordId(parseRecordId);
    }

    @Override
    public int deleteByRecordIdAndType(String parseRecordId, String dataType) {
        return bidParseDataMapper.deleteByRecordIdAndType(parseRecordId, dataType);
    }
}
