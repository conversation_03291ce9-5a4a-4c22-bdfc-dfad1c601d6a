package com.zte.mcrm.common.util;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.glassfish.jersey.internal.guava.Sets;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.*;

/**
 * @Auther: 10244932
 * @Date: 2021/6/11 09:26
 * @Description:
 */

public class ObjectProcess<T> {


    private Class<T> clazz;
    private List<T> values;
    private Class annClazz;
    private  List<Field> fields;

    public List<Field> getFields() {
        return fields;
    }

    public List<T> getValues() {
        return values;
    }

    protected ObjectProcess(List<T> objects, Class annClazz) throws Exception {
        if (CollectionUtils.isEmpty(objects)) {
            return ;
        }
        this.values = new ArrayList<>();
        for (T obj : objects) {
            if (obj != null) {
                values.add(obj);
            }
        }
        if (CollectionUtils.isEmpty(values)) {
            return ;
        }
        this.clazz = (Class<T>) values.iterator().next().getClass();
        this.annClazz = annClazz;
        this.fields = Lists.newArrayList(getAnnonationField()) ;
    }

    public  void replaceValue() throws Exception {
        if (clazz == null ) {
            return ;
        }
        preReplace();
        replaceValue(values);
        postReplace();
    }

    /**
     * 字段值替换
     */
    private void replaceValue(List<T> objects) throws Exception {

        if (CollectionUtils.isEmpty(objects)||CollectionUtils.isEmpty(fields)) {
            return ;
        }

        for (T obj : objects) {
            for (Field field: fields) {

                Object fieldValue = field.get(obj);
                // 获取需要替换的值
                Object reFieldValue = getReplaceValue(obj, fieldValue);
                if (fieldValue != reFieldValue) {
                    field.set(obj,reFieldValue);
                }

            }
        }
    }

    private void setAccessible(Field field) {
        if ((!Modifier.isPublic(field.getModifiers()) ||!Modifier.isPublic(field.getDeclaringClass().getModifiers()))
                && !field.isAccessible()) {
            field.setAccessible(true);
        }
    }

    /**
     * 获取替换值的方法
     * 需要子类实现
     * @param
     * @return
     * <AUTHOR>
     * @date 2021/6/11
     */
    public Object getReplaceValue(T obj, Object value) {
        return value;
    }

    /**
     * 获取添加注解的字段的字段和额外的自定义的字段
     */
    private  Set<Field> getAnnonationField() throws Exception {
        Set<Field> annFields = Sets.newHashSet();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            Annotation annotation = field.getAnnotation(annClazz);
            Optional.ofNullable(annotation)
                    .ifPresent((value)->{
                        if(match(field,annClazz)){
                            setAccessible(field);
                            annFields.add(field);
                        }
                    });
        }
        List<Field> addFields = additionalFields(clazz);
        if (CollectionUtils.isEmpty(addFields)) {
            return annFields;
        }
        for (Field field : addFields) {
            setAccessible(field);
            annFields.add(field);
        }
        return annFields;
    }


    /**
     * 字段是否匹配
     */
    public boolean  match(Field field ,Class annClazz) {
        return true;
    }

    /**
     * 替换前的操作
     */
    public void preReplace() throws Exception {

    }
    /**
     * 替换后的操作
     */
    public void postReplace()  throws Exception {

    }
    /**
     * 添加用户指定的字段(除注解以外的)
     */
    public List<Field> additionalFields(Class clazz)throws Exception {
        return Collections.emptyList();
    }



}
