package com.zte.mcrm.channel.model.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
* 审批记录 实体类
* <AUTHOR>
* @date 2021/10/09
*/

@Setter @Getter @ToString
@ApiModel(description="审批记录")
public class ComApprovalRecord implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long rowId;
    @ApiModelProperty(value = "业务id")
    private String businessId;
    @ApiModelProperty(value = "业务类型")
    private String businessType;
    @ApiModelProperty(value = "审批中心流程实例id")
    private String workFlowInstanceId;
    @ApiModelProperty(value = "有效标记")
    private String enabledFlag;
    @ApiModelProperty(value = "创建人")
    private String createdBy;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createdDate;
    @ApiModelProperty(value = "最后更新人")
    private String lastUpdatedBy;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpdatedDate;

}