package com.zte.crm.eva.base.common.constant.universal;

/**
 * 通用配置常量
 *
 * <AUTHOR>
 */
public class UniversalUpdateConstant {

    private UniversalUpdateConstant() {
        throw new IllegalStateException("Constant class");
    }

    /**
     * id
     */
    public static final String ID = "id";
    /**
     * 任务号
     */
    public static final String TASK_ID = "taskId";
    /**
     * 原值1
     */
    public static final String SOURCE_ONE = "sourceOne";
    /**
     * 原值2
     */
    public static final String SOURCE_TWO = "sourceTwo";
    /**
     * 原值3
     */
    public static final String SOURCE_THREE = "sourceThree";
    /**
     * 原值4
     */
    public static final String SOURCE_FOUR = "sourceFour";
    /**
     * 目标值1
     */
    public static final String TARGET_ONE = "targetOne";
    /**
     * 目标值2
     */
    public static final String TARGET_TWO = "targetTwo";
    /**
     * 目标值3
     */
    public static final String TARGET_THREE = "targetThree";
    /**
     * 目标值4
     */
    public static final String TARGET_FOUR = "targetFour";
    /**
     * 组织调整
     */
    public static final String FUNDATION_AGENCY_UPDATE = "fundationAgencyUpdate";
    /**
     * 数据库名字段
     */
    public static final String DATABASE_NAME = "databaseName";
    /**
     * 表名字段
     */
    public static final String TABLE_NAME = "tableName";
    /**
     * 值代码
     */
    public static final String VALUE_CODE = "valueCode";
    /**
     * 执行模块
     */
    public static final String EXECUTE_MODULE = "executeModule";
    /**
     * 执行状态
     */
    public static final String UPDATE_STATUS = "updateStatus";
    /**
     * 刷新前记录数
     */
    public static final String COUNT_NUM = "countNum";
    /**
     * 刷新记录数
     */
    public static final String UPDATE_NUM = "updateNum";
    /**
     * 创建人
     */
    public static final String CREATED_BY = "createdBy";
    /**
     * 创建时间
     */
    public static final String CREATED_DATE = "createdDate";
    /**
     * 更新人
     */
    public static final String LAST_UPDATED_BY = "lastUpdatedBy";
    /**
     * 更新时间
     */
    public static final String LAST_UPDATED_DATE = "lastUpdatedDate";
    /**
     * 执行状态-否
     */
    public static final String UPDATE_STATUS_10 = "10";
    /**
     * 执行状态-是
     */
    public static final String UPDATE_STATUS_20 = "20";
    /**
     * Y
     */
    public static final String Y = "Y";
    /**
     * N
     */
    public static final String N = "N";
    /**
     * 默认页大小
     */
    public static final long DEFAULT_ROWS = 500;
    /**
     * 更新字段配置index
     */
    public static final int UPDATE_TIME_COLUMN_INDEX = 2;

}
