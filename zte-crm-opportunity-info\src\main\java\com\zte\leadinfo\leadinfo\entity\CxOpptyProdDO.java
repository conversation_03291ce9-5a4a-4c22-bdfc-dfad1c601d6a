package com.zte.leadinfo.leadinfo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "cx_oppty_prod")
@Data
public class CxOpptyProdDO implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "ROW_ID")
    private String rowId;

    /**
     * 体系内部分类Id
     */
    @TableField(value = "PROD_LV1_ID")
    private String prodLv1Id;

    /**
     *
     */
    @TableField(value = "PROD_LV1_NAME")
    private String prodLv1Name;

    /**
     * 大产品线Id
     */
    @TableField(value = "PROD_LV2_ID")
    private String prodLv2Id;

    /**
     *
     */
    @TableField(value = "PROD_LV2_NAME")
    private String prodLv2Name;

    /**
     * 产品线Id
     */
    @TableField(value = "PROD_LV2_1_ID")
    private String prodLv21Id;

    /**
     *
     */
    @TableField(value = "PROD_LV2_1_NAME")
    private String prodLv21Name;

    /**
     * 产品大类Id
     */
    @TableField(value = "PROD_LV3_ID")
    private String prodLv3Id;

    /**
     *
     */
    @TableField(value = "PROD_LV3_NAME")
    private String prodLv3Name;

    /**
     * 产品小类Id
     */
    @TableField(value = "PROD_LV4_ID")
    private String prodLv4Id;

    /**
     *
     */
    @TableField(value = "PROD_LV4_NAME")
    private String prodLv4Name;

    /**
     *
     */
    @TableField(value = "OPPTY_ID")
    private String opptyId;

    /**
     * 是否主产品
     */
    @TableField(value = "ZTE_MAIN_PRODUCT")
    private String zteMainProduct;

    /**
     *
     */
    @TableField(value = "ACTIVE_FLG")
    private String activeFlg;

    /**
     * 产品预计签单金额（万）
     */
    @TableField(value = "PRODUCT_AMOUNT")
    private BigDecimal productAmount;

    /**
     * 预计签单时间
     */
    @TableField(value = "FOR_SIGN_DATE")
    private Date forSignDate;

    /**
     *
     */
    @TableField(value = "DATA_SOURCE")
    private String dataSource;

    /**
     * 业务类型（新建商机/商机转立项）
     */
    @TableField(value = "business_type")
    private String businessType;

    /**
     * 创建人
     */
    @TableField(value = "CREATED_BY")
    private String createdBy;

    /**
     * 最后更新人
     */
    @TableField(value = "LAST_UPD_BY")
    private String lastUpdBy;

    /**
     * 创建时间
     */
    @TableField(value = "CREATED")
    private Date created;

    /**
     * 最后更新时间
     */
    @TableField(value = "LAST_UPD")
    private Date lastUpd;

    /**
     * 父子产品ID
     */
    @TableField(value = "PAR_PROD_ID")
    private String parProdId;
}