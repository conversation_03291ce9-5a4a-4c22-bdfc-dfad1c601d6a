package com.zte.leadinfo.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.zte.leadinfo.common.entity.CxApprOpHead;
import com.zte.leadinfo.common.service.CxApprOpHeadService;
import com.zte.leadinfo.common.mapper.CxApprOpHeadMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cx_appr_op_head】的数据库操作Service实现
* @createDate 2024-07-12 23:30:33
*/
@Service
public class CxApprOpHeadServiceImpl extends ServiceImpl<CxApprOpHeadMapper, CxApprOpHead>
    implements CxApprOpHeadService{

    @Override
    public List<CxApprOpHead> listByOpptyIds(List<String> optyIds, List<String> approveObjects) {
        if (CollectionUtils.isEmpty(optyIds)) {
            return Lists.newArrayList();
        }

        LambdaQueryWrapper<CxApprOpHead> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CxApprOpHead::getObjectId, optyIds)
                .in(CxApprOpHead::getApproveObject, approveObjects);
        return this.list(queryWrapper);
    }
}




