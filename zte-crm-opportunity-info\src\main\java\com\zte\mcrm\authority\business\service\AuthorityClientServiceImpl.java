package com.zte.mcrm.authority.business.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.itp.authorityclient.client.AuthorityClient;
import com.zte.itp.authorityclient.entity.input.*;
import com.zte.itp.authorityclient.entity.output.ReturnConstraintEntity;
import com.zte.itp.authorityclient.entity.output.ReturnResourceEntity;
import com.zte.itp.authorityclient.entity.output.RoleVO;
import com.zte.itp.authorityclient.entity.output.ServiceData;
import com.zte.mcrm.authority.business.IAuthorityClientService;
import com.zte.mcrm.common.util.RequestMessage;
import com.zte.mcrm.opportunity.access.bo.RoleUser;
import com.zte.mcrm.opportunity.access.vo.RoleInfomation;
import com.zte.mcrm.opportunity.common.OppSysConst;
import com.zte.springbootframe.common.model.RetCodeCopy;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/****
 * 统一权限平台业务实现接口**
 * @ClassName:AuthorityClientServiceImpl
 * @Description: 这里用一句话描述这个类的作用
 * @author: 6092002949
 * @date: 2021年10月14日
 */
@Service
public class AuthorityClientServiceImpl implements IAuthorityClientService {

    /** 租户id*/
    @Value("${upp.auth.tenantId}")
    Long tenantId;
    /** 产品id*/
    @Value("${upp.auth.productId}")
    Long productId;
    /** 模块id*/
    @Value("${upp.auth.moduleId}")
    String moduleId;
    /** 产品密钥key*/
    @Value("${upp.auth.productSecretKey}")
    String productSecretKey;

    @Override
    public String getUserConstraintAuthorizationByRoleCodeAndUserId(String roleCode, String empNo) {
        ConstraintAuthorizationAInput constraintAuthorizationAInput = new ConstraintAuthorizationAInput();
        constraintAuthorizationAInput.setTenantId(tenantId);
        constraintAuthorizationAInput.setProductId(productId);
        constraintAuthorizationAInput.setRoleCode(roleCode);
        constraintAuthorizationAInput.setUserId(empNo);
        constraintAuthorizationAInput.setEmpidui(empNo);
        constraintAuthorizationAInput.setItpValue(OppSysConst.ITP_VALUE);
        constraintAuthorizationAInput.setSecretKey(productSecretKey);
        ServiceData sd = AuthorityClient.getUserConstraintAuthorizationByRoleCodeAndUserId(constraintAuthorizationAInput);
        Map<String, JSONArray> resources = (Map<String,JSONArray>) sd.getBo();
        if(null == resources) {
            return null;
        }
        JSONArray constraintList = resources.get("constraintList");
        if (null != constraintList && constraintList.size() > 0) {
            Map<String, String> constraintMap = (Map<String, String>) constraintList.get(0);
            String constraintValue = constraintMap.get("constraintValue");
            return constraintValue;
        }
        return null;
    }

    @Override
    public String getUserConstraintAuthorizationByRoleCode(String roleCode) {
        return this.getUserConstraintAuthorizationByRoleCodeAndUserId(roleCode, RequestMessage.getEmpNo());
    }

    @Override
    public Map<String, ReturnConstraintEntity> getUserPowerRole(String empNo) {
        // 获取资源下的数据授权Map
        return this.getUserPowerFunctionCodeAndUserId(OppSysConst.OPPORTUNITY_CODE, empNo, RequestMessage.getToken());
    }

    @Override
    public Map<String, ReturnConstraintEntity> getUserPowerFunctionCode(String functionCode) {
        // 获取资源下的数据授权Map
        return this.getUserPowerFunctionCodeAndUserId(functionCode, RequestMessage.getEmpNo(), RequestMessage.getToken());
    }

    @Override
    public Map<String, ReturnConstraintEntity> getUserPowerFunctionCodeAndUserId(String functionCode, String emptyId, String token) {
        CommonModuleIdEntity commonModuleIdEntity = new CommonModuleIdEntity();
        // 语言标准编码 必填
        commonModuleIdEntity.setLang(RequestMessage.getUserLang());
        //token值
        commonModuleIdEntity.setToken(token);
        //调用方微服务名
        commonModuleIdEntity.setServiceName(OppSysConst.ZTE_CRM_OPPORTUNITY_INFO);
        //该租户下的多组织ID
        commonModuleIdEntity.setOrgId(OppSysConst.ORG_ID);
        //员工工号
        commonModuleIdEntity.setEmpidui(emptyId);
        commonModuleIdEntity.setItpValue(OppSysConst.ITP_VALUE);
        commonModuleIdEntity.setProductId(productId);
        commonModuleIdEntity.setTenantId(tenantId);
        commonModuleIdEntity.setModuleId(moduleId);
        commonModuleIdEntity.setSecretKey(productSecretKey);
        ServiceData sd = AuthorityClient.getUserPower(commonModuleIdEntity);
        Map<String, ReturnResourceEntity> resources = (Map<String, ReturnResourceEntity>) sd.getBo();
        if(null == resources) {
            return null;
        }
        // 根据资源编码获取资源
        ReturnResourceEntity resource = resources.get(functionCode);
        if(null == resource) {
            return null;
        }
        // 获取资源下的数据授权Map
        return resource.getConstraintMap();
    }

    @Override
    public List<RoleInfomation> queryUserRole(String empNo) throws Exception {
        List<RoleInfomation> roleInfomationList = null;
        CommonRoleEntity entity = new CommonRoleEntity();
        entity.setTenantId(tenantId);
        entity.setProductId(productId);
        entity.setModuleId(moduleId);
        entity.setToken(RequestMessage.getToken());
        // 当前用户
        entity.setEmpidui(empNo);
        entity.setToken(RequestMessage.getToken());
        entity.setSecretKey(productSecretKey);
        ServiceData sd = AuthorityClient.getRoleList(entity);
        if (RetCodeCopy.SUCCESS_CODE.equals(sd.getCode().getCode()) && sd.getBo() != null) {
            roleInfomationList = JSON.parseArray(JSON.parseObject(JSONObject.toJSONString(sd)).getString("bo"), RoleInfomation.class);
        }
        return roleInfomationList;
    }

    @Override
    public Long queryRoleIdByCode(String roleCode) throws Exception {
        CommonRoleEntity  entity = new CommonRoleEntity();
        entity.setTenantId(tenantId);
        entity.setProductId(productId);
        entity.setModuleId(moduleId);
        // 生产环境下,才传token
        entity.setToken(RequestMessage.getToken());
        entity.setRoleNameEN(roleCode);
        entity.setEmpidui(RequestMessage.getEmpNo());
        entity.setSecretKey(productSecretKey);
        ServiceData sds = AuthorityClient.queryRoleByNameEn(entity);
        RoleVO role = (RoleVO) sds.getBo();
        if(null != role) {
            return role.getId();
        }
        return 0L;
    }

    @Override
    public List<RoleUser> queryPersonByRoleIdAndAuthData(Long roleId, String authOrg, String authProduct) throws Exception {
        CommonModuleIdEntity commonModuleIdEntity = new CommonModuleIdEntity();
        commonModuleIdEntity.setEmpidui(RequestMessage.getEmpNo());
        //传入token值
        commonModuleIdEntity.setToken(RequestMessage.getToken());
        commonModuleIdEntity.setSecretKey(productSecretKey);
        RoleDataEntity roleDataEntity = new RoleDataEntity();
        //传入token值
        roleDataEntity.setEmpidui(RequestMessage.getEmpNo());
        roleDataEntity.setToken(RequestMessage.getToken());
        List<RoleDataVo> roleDataVoList = new ArrayList<>();
        RoleDataVo roleEntity = new RoleDataVo();
        roleEntity.setRoleId(String.valueOf(roleId));
        List<DataVo> dataList = new ArrayList<>();
        // 部门
        if(StringUtils.isNotBlank(authOrg)){
            DataVo dataEntityOrg = new DataVo();
            dataEntityOrg.setData(authOrg);
            dataEntityOrg.setType("ORG");
            dataEntityOrg.setIsInclude(3);
            dataList.add(dataEntityOrg);
        }
        // 产品
        if(StringUtils.isNotBlank(authProduct)){
            DataVo dataEntityProduct = new DataVo();
            dataEntityProduct.setData(authProduct);
            dataEntityProduct.setType("PROD");
            dataEntityProduct.setIsInclude(2);
            dataList.add(dataEntityProduct);
        }
        roleEntity.setDatas(dataList);
        roleDataVoList.add(roleEntity);
        roleDataEntity.setRoleDataVoList(roleDataVoList);
        ServiceData sd = AuthorityClient.getRoleDataUser(roleDataEntity);
        Map<String, Object> result = (Map<String, Object>) sd.getBo();
        String users = JacksonJsonConverUtil.beanToJson(result.get(String.valueOf(roleId)));
        return JacksonJsonConverUtil.jsonToListBean(users, new TypeReference<List<RoleUser>>(){});
    }
}
