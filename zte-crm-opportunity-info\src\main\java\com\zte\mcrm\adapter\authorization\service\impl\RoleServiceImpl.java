package com.zte.mcrm.adapter.authorization.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.zte.itp.authorityclient.entity.output.RoleVO;
import com.zte.itp.authorityclient.entity.output.ServiceData;
import com.zte.mcrm.adapter.authorization.service.ChannelAuthService;
import com.zte.mcrm.adapter.authorization.service.PrmAuthService;
import com.zte.mcrm.adapter.authorization.service.RoleService;
import com.zte.mcrm.adapter.authorization.util.UppAuthorityUtil;
import com.zte.mcrm.adapter.constant.UcsSpringProperties;
import com.zte.mcrm.common.util.CommonUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.UUID;
/**
 * 权限相关接口
 * <AUTHOR>
 * @date 2021/10/30
 */
@Service
public class RoleServiceImpl implements RoleService {

    @Autowired
    private UcsSpringProperties ucsSpringProperties;
    @Autowired
    private ChannelAuthService channelAuthService;
    @Autowired
    private PrmAuthService prmAuthService;

    @Override
    public String getRoleByRoleCodeAndModuleId(String moduleId, String roleCode, Boolean isPrm) {
        ServiceData serviceData ;
        if(isPrm) {
            if (UppAuthorityUtil.determineTheUserIsExternal(CommonUtils.getEmpNo())) {
                String itpValue = getUcsItpValue(CommonUtils.getEmpNo(), CommonUtils.getAuthValue());
                serviceData = prmAuthService.getRoleByRoleCodeAndModuleId(moduleId, roleCode, itpValue);
            }else{
                serviceData = prmAuthService.getRoleByRoleCodeAndModuleId(moduleId, roleCode, StringUtils.EMPTY);
            }
        }else {
            if (UppAuthorityUtil.determineTheUserIsExternal(CommonUtils.getEmpNo())) {
                String itpValue = getUcsItpValue(CommonUtils.getEmpNo(), CommonUtils.getAuthValue());
                serviceData = channelAuthService.getRoleByRoleCodeAndModuleId(moduleId, roleCode, itpValue);
            }else{
                serviceData = channelAuthService.getRoleByRoleCodeAndModuleId(moduleId, roleCode, StringUtils.EMPTY);
            }
        }

        if(Objects.isNull(serviceData) || Objects.isNull(serviceData.getBo())){
            return null;
        }

        String str = JSONObject.toJSONString(serviceData.getBo());
        RoleVO roleVO = JSONObject.parseObject(str, RoleVO.class);
        return roleVO.getId().toString();
    }

    /**
     * 设置Ucs的ItpValue
     * <AUTHOR>
     * @date 2020/11/27
     * @param empNo 工号
     * @param token token
     * @return String
     */
    private String getUcsItpValue(String empNo, String token) {
        String uuid = UUID.randomUUID().toString();
        StringBuilder sb = new StringBuilder();
        sb.append(ucsSpringProperties.getSecretKey()).append(ucsSpringProperties.getAppId())
                .append(ucsSpringProperties.getAccessKey()).append(uuid);

        String authValue = DigestUtils.sha256Hex(sb.toString());
        JSONObject json = new JSONObject();
        // type是统一权限规定的关键字段，UCS用于区分是否是外部用户
        json.put("type","UCS");
        json.put("tenantId",ucsSpringProperties.getTenantId());
        json.put("appId",ucsSpringProperties.getAppId());
        json.put("authValue",authValue);
        json.put("accessKey",ucsSpringProperties.getAccessKey());
        json.put("uuid",uuid);
        json.put("token",token);
        json.put("accountId",empNo);
        return json.toString();
    }
}
