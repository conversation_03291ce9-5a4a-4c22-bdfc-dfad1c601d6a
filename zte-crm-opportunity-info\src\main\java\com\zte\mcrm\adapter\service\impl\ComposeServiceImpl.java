package com.zte.mcrm.adapter.service.impl;

import com.alibaba.fastjson.JSON;
import com.zte.crm.eva.base.common.config.IchannelSecurityProperties;
import com.zte.crm.eva.base.common.utils.CrmSecurityUtil;
import com.zte.crm.eva.base.infrastructure.client.IchannelBaseFeign;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.IDNResponseData;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.security.AESOperator;
import com.zte.itp.security.ZteSecurity;
import com.zte.mcrm.adapter.constant.CpcPropertiesConstant;
import com.zte.mcrm.adapter.constant.UcsConstant;
import com.zte.mcrm.adapter.constant.UcsSpringProperties;
import com.zte.mcrm.adapter.model.dto.*;
import com.zte.mcrm.adapter.model.mapper.AccountDetailMapper;
import com.zte.mcrm.adapter.model.vo.AccountDetail;
import com.zte.mcrm.adapter.model.vo.ChannelAccountInfo;
import com.zte.mcrm.adapter.model.vo.EnterpriseInfo;
import com.zte.mcrm.adapter.service.BmtUserServiceApi;
import com.zte.mcrm.adapter.service.ComposeService;
import com.zte.mcrm.adapter.service.PrmService;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.springbootframe.util.local.LocalMessageUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 组合业务逻辑
 * <AUTHOR>
 * @date 2021/9/16
 */
@Service
public class ComposeServiceImpl implements ComposeService {
    /** 日志对象 */
    private static  final Logger LOGGER = LoggerFactory.getLogger(ComposeServiceImpl.class);

    @Autowired
    private BmtUserServiceApi bmtUcsServiceApi;
    @Autowired
    private PrmService prmService;
    @Autowired
    private UcsSpringProperties ucsSpringProperties;
    @Resource
    private IchannelSecurityProperties ichannelSecurityProperties;
    @Autowired
    IchannelBaseFeign ichannelBaseFeign;

    @Value("${ichannel.upp.auth.moduleId}")
    private String moduleId;

    private static final String ACTIVE_STATUS = "4";

    /**
     * 查询渠道账号对应的渠道信息
     * @param accountId
     * @return ChannelAccountInfo
     */
    @Override
    public ChannelAccountDetailDTO getChannelAccountBasicInfo(String accountId) throws Exception {
        ChannelAccountDetailDTO channelAccountBasicInfo = prmService.getChannelAccountBasicInfo(accountId);
        return  channelAccountBasicInfo;
    }

    /**
     * 查询渠道账号对应的渠道信息
     *
     * @param crmCustomerCode
     * @return ChannelAccountInfo
     */
    @Override
    public ChannelAccountInfo getChannelAccountInfo(String crmCustomerCode) throws Exception {
        //查询企业详细信息
        String accountId = CommonUtils.getEmpNo();
        AccountDetailQueryDTO entity = new AccountDetailQueryDTO();
        entity.setAccountId(accountId);
        entity.setInfoBlock(UcsConstant.INFOBLOCK_ENTERPRISEINFO);
        IDNResponseData<AccountDetail> accountDetailIDNResponseData = ichannelBaseFeign.queryAccountDetail(entity);
        AccountDetail accountDetail = accountDetailIDNResponseData.getData();
        if (Objects.isNull(accountDetail)) {
            LOGGER.info("Account does not exist!");
            return null;
        }
        ChannelCustomerRes channelCustomerInfo = this.getChannelInfo(crmCustomerCode);
        ChannelAccountInfo channelAccountInfo = AccountDetailMapper.INSTANCE.transToChannelAccountInfo(accountDetail);

        if (Objects.nonNull(channelCustomerInfo)) {
            channelAccountInfo.setCustomerId(channelCustomerInfo.getCustomerId());
            channelAccountInfo.setChannelName(channelCustomerInfo.getCustomerName());
        }
        return channelAccountInfo;
    }

    @Override
    public ChannelCustomerRes getChannelInfo(String crmCustomerCode) throws Exception {
        if(StringUtils.isBlank(crmCustomerCode)){
            LOGGER.info("getChannelInfo:the input crmCustomerCode is empty");
            return new ChannelCustomerRes();
        }
        ChannelCustomerRes inputParam = new ChannelCustomerRes();
        ChannelCustomerRes outResult = new ChannelCustomerRes();
        inputParam.setCrmCustomerCode(crmCustomerCode);
        inputParam.setStatus(ACTIVE_STATUS);
        inputParam.setEnabledFlag(CommonConst.Y);
        FormData<ChannelCustomerRes> formData = new FormData<>();
        formData.setBo(inputParam);
        formData.setPage(1);
        formData.setRows(10);
        List<ChannelCustomerRes> channelCustomers = prmService.getChannelCustomerList(formData);
        LOGGER.info("getChannelInfo crmCustomerCode:{},output:{}", crmCustomerCode, channelCustomers);
        if(CollectionUtils.isNotEmpty(channelCustomers)){
            outResult = channelCustomers.get(0);
        }
        return outResult;
    }



    /**
     * 查询企业详细信息
     * @param
     */
    @Override
    public AccountDetail getAccountDetail(String accountId) throws Exception {
        AccountDetailQueryDTO entity = new AccountDetailQueryDTO();
        entity.setAccountId(accountId);
        entity.setInfoBlock(UcsConstant.INFOBLOCK_ENTERPRISEINFO);
        return prmService.getAccountDetail(entity);
    }

    /**
     * 查询企业详细信息
     * @param
     */
    @Override
    public AccountDetail getAccountDetailFromUcs(AccountDetailQueryDTO accountDetailQueryDTO) {
        ServiceData<AccountDetail> accountDetail = bmtUcsServiceApi.getAccountDetail(accountDetailQueryDTO);
        if(accountDetail != null) {
            LOGGER.info("getAccountDetailFromUcs,return:{}", JSON.toJSONString(accountDetail));
            AccountDetail accountDetailBo = accountDetail.getBo();
            decryptUcsInfo(accountDetailBo);
            return accountDetailBo;
        }
        return null;
    }

    private void decryptUcsInfo(AccountDetail accountDetail){
        if (null == accountDetail){
            return;
        }
        String personName = accountDetail.getPersonName();
        String personId = accountDetail.getPersonId();
        String email = accountDetail.getEmail();
        String phone = accountDetail.getPhone();
        try {
            String emailString= CrmSecurityUtil.zteDecryptAndIchannelEncrypt(email, ichannelSecurityProperties.getKey());
            accountDetail.setEmail(StringUtils.isBlank(emailString)? email : emailString);
            String phoneString = CrmSecurityUtil.zteDecryptAndIchannelEncrypt(phone, ichannelSecurityProperties.getKey());
            accountDetail.setPhone(StringUtils.isBlank(phoneString)? phone : phoneString);
            String personNameStr = CrmSecurityUtil.zteDecryptAndIchannelEncrypt(personName, ichannelSecurityProperties.getKey());
            accountDetail.setPersonName(StringUtils.isBlank(personNameStr)? personName : personNameStr);
            String personIdStr = CrmSecurityUtil.zteDecryptAndIchannelEncrypt(personId, ichannelSecurityProperties.getKey());
            accountDetail.setPersonId(StringUtils.isBlank(personIdStr)? personId : personIdStr);
        } catch (Exception e) {
            LOGGER.error("解密失败, accountDetail:{}",accountDetail, e);
        }
    }



}
