package com.zte.mcrm.adapter.util;

import cn.hutool.extra.pinyin.PinyinUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 多音字 拼音处理工具
 *
 * <AUTHOR>
 * @date 2021/12/20
 */
@Slf4j
public class PolyphonePinyinUtils {

    /**
     * 转换为不带音调的拼音字符串
     *
     * @param pinYinStr 需转换的汉字
     * @return 拼音字符串
     */
    public static String changeToTonePinYin(String pinYinStr) {

        String tempStr = null;

        try {
            tempStr = PinyinUtil.getPinyin(pinYinStr, "");
        } catch (Exception e) {
            log.error("convertToPinyinString error:{}", pinYinStr, e);
        }
        return tempStr;

    }

    /**
     * 转换为每个汉字对应拼音首字母字符串
     *
     * @param pinYinStr 需转换的汉字
     * @return 拼音字符串
     */
    public static String changeToGetShortPinYin(String pinYinStr) {

        String tempStr = null;

        try {
            tempStr = PinyinUtil.getFirstLetter(pinYinStr, "");
        } catch (Exception e) {
            log.error("getShortPinyin error:{}", pinYinStr, e);
        }
        return tempStr;

    }



}
