package com.zte.mcrm.channel.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Setter
@Getter
@ToString
public class CrmCustomerDetermineResultVO {
    @ApiModelProperty("渠道商客户编码")
    public String crmCustomerCode;
    @ApiModelProperty("是否有单据")
    Integer existedDocument;
    @ApiModelProperty("是否有在途单据")
    Integer inTransitDocument;

    public CrmCustomerDetermineResultVO(String crmCustomerCode) {
        this.crmCustomerCode = crmCustomerCode;
        this.existedDocument = 0;
        this.inTransitDocument = 0;
    }

    public CrmCustomerDetermineResultVO() {
        this.existedDocument = 0;
        this.inTransitDocument = 0;
    }
}
