<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <title>SOC应答系统 DEMO - 已修复</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <style>
    /* ===== 基础样式 ===== */
    * {
      box-sizing: border-box;
    }
    
    body {
      margin: 0;
      font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
      background: #f5f7fa;
      color: #333;
      font-size: 14px;
      line-height: 1.5;
    }

    /* ===== 导航栏样式 ===== */
    .soc-navbar {
      height: 56px;
      background: #ffffff;
      color: #7c7c7c;
      display: flex;
      align-items: center;
      padding: 0 24px;
      font-size: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      position: sticky;
      top: 0;
      z-index: 100;
    }

    .soc-navbar-logo {
      font-weight: bold;
      font-size: 20px;
      margin-right: 32px;
      letter-spacing: 1px;
      color: #1765d5;
    }

    .soc-navbar-menu {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 24px;
    }

    .soc-navbar-menu > div {
      cursor: pointer;
      transition: color 0.3s;
    }

    .soc-navbar-menu > div:hover {
      color: #1765d5;
    }

    .soc-navbar-search {
      background: #f5f7fa;
      border-radius: 20px;
      padding: 6px 16px;
      margin-right: 24px;
      display: flex;
      align-items: center;
      transition: all 0.3s;
    }

    .soc-navbar-search:hover {
      background: #e8ecf1;
    }

    .soc-navbar-search input {
      border: none;
      outline: none;
      background: transparent;
      font-size: 14px;
      width: 180px;
      margin-left: 8px;
    }

    .soc-navbar-user {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .soc-navbar-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: #e6eaf2;
      display: inline-block;
    }

    /* ===== 布局样式 ===== */
    .soc-layout {
      display: flex;
      height: calc(100vh - 56px);
      background: #f5f7fa;
    }

    .soc-sidebar,
    .soc-chatbar {
      background: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      z-index: 10;
      transition: all 0.3s ease;
      position: relative;
    }

    .soc-sidebar {
      width: 240px;
      min-width: 200px;
      max-width: 280px;
      border-right: 1px solid #e8eaec;
      display: flex;
      flex-direction: column;
    }

    .soc-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-width: 0;
      background: #f5f7fa;
    }

    .soc-header {
      height: 56px;
      background: #fff;
      border-bottom: 1px solid #e8eaec;
      display: flex;
      align-items: center;
      padding: 0 24px;
      font-size: 18px;
      font-weight: 500;
    }

    .soc-main {
      flex: 1;
      overflow: auto;
      padding: 20px;
    }

    /* ===== 表格卡片样式 ===== */
    #answerTableCard {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      padding: 20px;
      margin-bottom: 20px;
    }

    /* ===== 表格样式 ===== */
    table.soc-table {
      border-collapse: separate;
      border-spacing: 0;
      width: 100%;
      min-width: 1200px;
      font-size: 13px;
      background: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      border-radius: 8px;
      overflow: hidden;
    }

    table.soc-table th,
    table.soc-table td {
      border-bottom: 1px solid #e8eaec;
      border-right: 1px solid #e8eaec;
      padding: 12px 8px;
      text-align: left;
      vertical-align: middle;
    }

    table.soc-table th:last-child,
    table.soc-table td:last-child {
      border-right: none;
    }

    table.soc-table th {
      background: #fafbfc;
      font-weight: 500;
      color: #666;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    table.soc-table tbody tr {
      transition: background-color 0.2s ease;
    }

    table.soc-table tbody tr:hover {
      background-color: #f5f7fa;
    }

    table.soc-table tbody tr:last-child td {
      border-bottom: none;
    }

    /* ===== 表格内输入框样式 ===== */
    table.soc-table input[type="text"],
    table.soc-table textarea,
    table.soc-table select {
      border: 1px solid transparent;
      outline: none;
      background: transparent;
      width: 100%;
      font-family: inherit;
      font-size: inherit;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.2s;
    }

    table.soc-table input[type="text"]:hover,
    table.soc-table textarea:hover,
    table.soc-table select:hover {
      background: #f8f9fa;
      border-color: #e8eaec;
    }

    table.soc-table input[type="text"]:focus,
    table.soc-table textarea:focus,
    table.soc-table select:focus {
      background: #fff;
      border-color: #1765d5;
      box-shadow: 0 0 0 2px rgba(23, 101, 213, 0.1);
    }

    /* ===== 按钮样式 ===== */
    .soc-btn {
      height: 36px;
      padding: 0 16px;
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      background: #fff;
      color: #666;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s ease;
      outline: none;
      display: inline-flex;
      align-items: center;
      gap: 6px;
      font-weight: 400;
    }

    .soc-btn:hover {
      background: #f5f7fa;
      color: #1765d5;
      border-color: #1765d5;
    }

    .soc-btn:active {
      transform: translateY(1px);
    }

    .soc-btn-primary {
      background: #1765d5;
      color: #fff;
      border-color: #1765d5;
    }

    .soc-btn-primary:hover {
      background: #0d47a1;
      border-color: #0d47a1;
      color: #fff;
    }

    .soc-btn-success {
      background: #52c41a;
      color: #fff;
      border-color: #52c41a;
    }

    .soc-btn-success:hover {
      background: #389e0d;
      border-color: #389e0d;
      color: #fff;
    }

    .soc-btn-warning {
      background: #faad14;
      color: #fff;
      border-color: #faad14;
    }

    .soc-btn-warning:hover {
      background: #d48806;
      border-color: #d48806;
      color: #fff;
    }

    .soc-btn-danger {
      background: #ff4d4f;
      color: #fff;
      border-color: #ff4d4f;
    }

    .soc-btn-danger:hover {
      background: #d9363e;
      border-color: #d9363e;
      color: #fff;
    }

    /* ===== 侧边栏样式 ===== */
    .soc-chatbar {
      width: 320px;
      min-width: 280px;
      max-width: 400px;
      border-left: 1px solid #e8eaec;
      display: flex;
      flex-direction: column;
    }

    .soc-hide-btn {
      position: absolute;
      top: 20px;
      right: -12px;
      background: #fff;
      border: 1px solid #e8eaec;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #666;
      transition: all 0.2s;
    }

    .soc-hide-btn:hover {
      color: #1765d5;
      transform: scale(1.1);
    }

    /* ===== 工具栏样式 ===== */
    .soc-toolbar {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;
      flex-wrap: wrap;
    }

    /* ===== 标签样式 ===== */
    .answer-type-tag {
      display: inline-block;
      font-size: 12px;
      border-radius: 4px;
      padding: 2px 8px;
      font-weight: 500;
    }

    .answer-type-ai {
      background: #e6f7ff;
      color: #1765d5;
      border: 1px solid #91d5ff;
    }

    .answer-type-manual {
      background: #fff7e6;
      color: #faad14;
      border: 1px solid #ffd591;
    }

    /* ===== 应答状态样式 ===== */
    .answer-status-fc {
      background: #f6ffed;
      color: #52c41a;
      border: 1px solid #b7eb8f;
    }

    .answer-status-pc {
      background: #fff7e6;
      color: #faad14;
      border: 1px solid #ffd591;
    }

    .answer-status-nc {
      background: #fff2f0;
      color: #ff4d4f;
      border: 1px solid #ffccc7;
    }

    .answer-status-na {
      background: #f5f5f5;
      color: #666;
      border: 1px solid #d9d9d9;
    }

    /* ===== 分页样式 ===== */
    .pagination {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 16px;
    }

    .pagination select {
      height: 32px;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      padding: 0 8px;
    }

    .pagination button {
      height: 32px;
      padding: 0 12px;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      background: #fff;
      cursor: pointer;
      transition: all 0.2s;
    }

    .pagination button:hover:not(:disabled) {
      border-color: #1765d5;
      color: #1765d5;
    }

    .pagination button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    /* ===== 弹窗样式 ===== */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    .modal-content {
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.15);
      max-width: 90vw;
      max-height: 90vh;
      overflow: auto;
      position: relative;
    }

    .modal-header {
      padding: 20px 24px;
      border-bottom: 1px solid #e8eaec;
      font-size: 18px;
      font-weight: 500;
    }

    .modal-body {
      padding: 24px;
    }

    .modal-footer {
      padding: 16px 24px;
      border-top: 1px solid #e8eaec;
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }

    .modal-close {
      position: absolute;
      top: 16px;
      right: 16px;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      border: none;
      background: #f5f5f5;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s;
    }

    .modal-close:hover {
      background: #e8e8e8;
      transform: rotate(90deg);
    }

    /* ===== 大纲面板样式 ===== */
    #outline-float {
      position: fixed;
      top: 80px;
      left: 0;
      z-index: 50;
      transition: all 0.3s ease;
    }

    #outline-panel {
      width: 260px;
      background: #fff;
      border-radius: 0 12px 12px 0;
      box-shadow: 2px 0 12px rgba(0, 0, 0, 0.1);
      padding: 16px;
      overflow-y: auto;
      max-height: 80vh;
    }

    #outline-expand {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #1765d5;
      color: #fff;
      border: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      cursor: pointer;
      font-size: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s;
    }

    #outline-expand:hover {
      transform: scale(1.1);
    }

    /* ===== 任务标签样式 ===== */
    .task-tab-btn {
      flex: 1;
      padding: 8px 12px;
      border: none;
      background: #f5f5f5;
      cursor: pointer;
      transition: all 0.2s;
      font-size: 14px;
    }

    .task-tab-btn.active {
      background: #e6f7ff;
      color: #1765d5;
      font-weight: 500;
    }

    /* ===== 快捷指令样式 ===== */
    .quick-cmd-btn {
      background: #f0f5ff;
      color: #1765d5;
      border: 1px solid #d6e4ff;
      border-radius: 6px;
      padding: 4px 12px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s;
      white-space: nowrap;
    }

    .quick-cmd-btn:hover {
      background: #d6e4ff;
      transform: translateY(-1px);
    }

    /* ===== 智能提示样式 ===== */
    .smart-prompt-btn {
      background: #e6f7ff;
      color: #1765d5;
      border: 1px solid #91d5ff;
      border-radius: 6px;
      padding: 4px 12px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s;
    }

    .smart-prompt-btn:hover {
      background: #bae7ff;
      transform: translateY(-1px);
    }

    /* ===== 对话历史样式 ===== */
    .chat-message {
      margin-bottom: 12px;
      display: flex;
    }

    .chat-message-user {
      justify-content: flex-end;
    }

    .chat-message-ai {
      justify-content: flex-start;
    }

    .chat-bubble {
      max-width: 70%;
      padding: 10px 14px;
      border-radius: 16px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
      word-wrap: break-word;
    }

    .chat-bubble-user {
      background: #e6f7ff;
      color: #333;
      margin-left: 30px;
    }

    .chat-bubble-ai {
      background: #f5f5f5;
      color: #333;
      margin-right: 30px;
    }

    /* ===== 响应式设计 ===== */
    @media (max-width: 1200px) {
      .soc-sidebar {
        width: 200px;
      }

      .soc-chatbar {
        width: 280px;
      }

      table.soc-table {
        font-size: 12px;
      }
    }

    @media (max-width: 900px) {
      .soc-sidebar,
      .soc-chatbar {
        position: absolute;
        height: 100%;
        transform: translateX(-100%);
      }

      .soc-sidebar.active,
      .soc-chatbar.active {
        transform: translateX(0);
      }

      .soc-chatbar {
        right: 0;
        left: auto;
        transform: translateX(100%);
      }

      .soc-content {
        margin: 0 !important;
      }

      .soc-hide-btn {
        display: none;
      }
    }

    /* ===== 加载动画 ===== */
    .loading-spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #1765d5;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* ===== 工具提示样式 ===== */
    .tooltip {
      position: relative;
    }

    .tooltip-text {
      visibility: hidden;
      position: absolute;
      bottom: 125%;
      left: 50%;
      transform: translateX(-50%);
      background-color: rgba(0, 0, 0, 0.8);
      color: white;
      text-align: center;
      padding: 6px 12px;
      border-radius: 6px;
      font-size: 12px;
      white-space: nowrap;
      z-index: 1;
      opacity: 0;
      transition: opacity 0.3s;
    }

    .tooltip:hover .tooltip-text {
      visibility: visible;
      opacity: 1;
    }

    /* ===== 树形选择器样式 ===== */
    .tree-select-container {
      position: relative;
      min-width: 200px;
    }

    .tree-select-input {
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      padding: 8px 12px;
      min-height: 36px;
      background: #fff;
      cursor: pointer;
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      align-items: center;
      transition: border-color 0.3s;
    }

    .tree-select-input:hover {
      border-color: #40a9ff;
    }

    .tree-select-input:focus-within {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    .tree-select-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: #fff;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      z-index: 1000;
      max-height: 300px;
      overflow-y: auto;
      display: none;
    }

    .tree-node {
      padding: 8px 12px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 13px;
      transition: background-color 0.2s;
    }

    .tree-node:hover {
      background-color: #f5f5f5;
    }

    .tree-node.selected {
      background-color: #e6f7ff;
      color: #1890ff;
    }

    /* ===== 搜索下拉框样式 ===== */
    .searchable-select-container {
      position: relative;
    }

    .searchable-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: #fff;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      z-index: 1000;
      max-height: 200px;
      overflow-y: auto;
      display: none;
    }

    .dropdown-option {
      padding: 8px 12px;
      cursor: pointer;
      font-size: 13px;
      transition: background-color 0.2s;
    }

    .dropdown-option:hover {
      background-color: #f5f5f5;
    }
  </style>
</head>

<body>
  <div class="soc-navbar">
    <div class="soc-navbar-logo">SOC应答系统</div>
    <div class="soc-navbar-menu">
      <div>工作台</div>
      <div>项目管理</div>
      <div>知识库</div>
      <div>设置</div>
    </div>
    <div class="soc-navbar-search">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
        <path d="M7 13A6 6 0 107 1a6 6 0 000 12zM13.5 13.5l-2.7-2.7" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
      </svg>
      <input type="text" placeholder="搜索..." />
    </div>
    <div class="soc-navbar-user">
      <span>管理员</span>
      <span class="soc-navbar-avatar"></span>
    </div>
  </div>

  <div class="soc-layout">
    <!-- 任务区/侧边栏 -->
    <aside class="soc-sidebar" id="socSidebar">
      <div class="soc-header" style="display:flex;align-items:center;justify-content:space-between;">
        <span>任务区</span>
        <button id="taskManageBtn" class="soc-btn" style="padding: 4px 12px;height: 28px;font-size: 12px;">
          📋 任务管理
        </button>
      </div>
      <div style="padding:0;flex:1;display:flex;flex-direction:column;">
        <div id="taskTabBar" style="display:flex;border-bottom:1px solid #e8eaec;">
          <button class="task-tab-btn active" data-tab="todo">待办任务</button>
          <button class="task-tab-btn" data-tab="history">历史归档</button>
          <button class="task-tab-btn" data-tab="template">模板管理</button>
        </div>
        <div id="taskTabContent" style="flex:1;padding:12px;overflow:auto;"></div>
      </div>
      <button class="soc-hide-btn" title="收起侧边栏" id="hideSidebarBtn">&lt;</button>
    </aside>

    <!-- 内容主区 -->
    <main class="soc-content" id="socContent">
      <div class="soc-header">SOC应答任务</div>
      <div class="soc-main">
        <!-- 表格卡片 -->
        <div id="answerTableCard">
          <div class="soc-toolbar">
            <!-- 操作按钮区 -->
            <button id="importExcelBtn" class="soc-btn soc-btn-primary">导入</button>
            <button id="addSingleEntryBtn" class="soc-btn">新增单条</button>
            <input type="file" id="importExcelInput" accept=".xlsx,.xls" style="display:none;" />
            <button id="exportBtn" class="soc-btn">导出</button>
            <button id="batchReAnswerBtn" class="soc-btn">开始应答</button>
            <div style="position:relative;">
              <button id="mainActionBtn" class="soc-btn">更多 ▾</button>
              <div id="mainActionMenu" style="display:none;position:absolute;left:0;top:40px;z-index:20;background:#fff;border:1px solid #e8eaec;box-shadow:0 2px 8px rgba(0,0,0,0.1);border-radius:6px;min-width:160px;overflow:hidden;">
                <div class="main-action-item" data-act="exportSetting" style="padding:8px 16px;cursor:pointer;transition:background .2s;">⚙️ 导出设置</div>
                <div class="main-action-item" data-act="openParamSetting" style="padding:8px 16px;cursor:pointer;transition:background .2s;">⚙️ 参数设置</div>
                <div class="main-action-item" data-act="openReferenceDoc" style="padding:8px 16px;cursor:pointer;transition:background .2s;">📑 参考文档</div>
                <div class="main-action-item" data-act="priorityConfig" style="padding:8px 16px;cursor:pointer;transition:background .2s;">🔧 优先级配置</div>
                <div class="main-action-item" data-act="satisfactionCalc" style="padding:8px 16px;cursor:pointer;transition:background .2s;">📊 满足度计算</div>
                <div class="main-action-item" data-act="viewSimilar" style="padding:8px 16px;cursor:pointer;transition:background .2s;">🔍 查看相似条目</div>
                <div class="main-action-item" data-act="historyRecord" style="padding:8px 16px;cursor:pointer;transition:background .2s;">🕓 历史记录</div>
                <div style="border-top:1px solid #f0f0f0;"></div>
                <div class="main-action-item" data-act="clearAll" style="padding:8px 16px;cursor:pointer;transition:background .2s;color:#ff4d4f;">🗑️ 清空全部条目</div>
              </div>
            </div>
            <button id="batchDeleteBtn" class="soc-btn">删除</button>
            <span style="flex:1"></span>
          </div>

          <!-- 提示信息 -->
          <div style="display:flex;align-items:center;gap:12px;margin-bottom:8px;">
            <span id="similarTip" style="color:#faad14;display:none;">
              发现<span id="similarCount"></span>组相似条目，
              <a href="#" id="showSimilarBtn">查看</a>
            </span>
            <span id="productFilterTip" style="color:#1765d5;font-size:13px;display:none;">
              当前筛选产品：<span id="currentFilterProduct"></span>
            </span>
          </div>

          <!-- 数据表格 -->
          <div style="overflow-x: auto;">
            <table class="soc-table" id="answerTable">
              <thead>
                <tr>
                  <th style="width: 40px;"><input type="checkbox" id="selectAllRow" /></th>
                  <th style="width: 80px;">
                    编号
                    <input id="searchNo" type="text" placeholder="筛选" style="width:50px;font-size:12px;margin-left:4px;" />
                  </th>
                  <th style="min-width:200px;">
                    条目描述
                    <input id="searchDesc" type="text" placeholder="筛选" style="width:100px;font-size:12px;margin-left:4px;" />
                  </th>
                  <th style="width:100px;">
                    产品
                    <select id="searchProduct" style="width:80px;font-size:12px;margin-left:4px;">
                      <option value="">全部</option>
                      <option value="5GC">5GC</option>
                      <option value="VoLTE">VoLTE</option>
                      <option value="IMS">IMS</option>
                    </select>
                  </th>
                  <th style="width:80px;">
                    方式
                    <select id="searchAnswerType" style="width:60px;font-size:12px;margin-left:4px;">
                      <option value="">全部</option>
                      <option value="AI">AI</option>
                      <option value="人工">人工</option>
                    </select>
                  </th>
                  <th style="width:100px;">
                    应答
                    <select id="searchAnswer" style="width:80px;font-size:12px;margin-left:4px;">
                      <option value="">全部</option>
                      <option value="FC">FC</option>
                      <option value="PC">PC</option>
                      <option value="NC">NC</option>
                      <option value="N/A">N/A</option>
                      <option value="未应答">未应答</option>
                    </select>
                  </th>
                  <th style="min-width:200px;">
                    应答说明
                    <input id="searchExplain" type="text" placeholder="筛选" style="width:120px;font-size:12px;margin-left:4px;" />
                  </th>
                  <th style="min-width:150px;">索引</th>
                  <th style="min-width:100px;">
                    备注
                    <input id="searchRemark" type="text" placeholder="筛选" style="width:80px;font-size:12px;margin-left:4px;" />
                  </th>
                  <th style="width:120px;">操作</th>
                </tr>
              </thead>
              <tbody id="answerTableBody"></tbody>
            </table>
          </div>

          <!-- 分页条 -->
          <div class="pagination" id="tablePagination">
            <span>每页</span>
            <select id="pageSizeSelect">
              <option value="10">10</option>
              <option value="20" selected>20</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
            <span>条</span>
            <button id="prevPageBtn">上一页</button>
            <span id="pageInfo">1 / 1</span>
            <button id="nextPageBtn">下一页</button>
          </div>
        </div>
      </div>
    </main>

    <!-- 对话区/右侧栏 -->
    <aside class="soc-chatbar" id="socChatbar">
      <div class="soc-header">投标Agent</div>
      <div style="padding:0;display:flex;flex-direction:column;height:calc(100% - 56px);position:relative;">
        <!-- 对话历史区 -->
        <div style="flex:1;padding:12px;overflow:auto;" id="luiHistoryWrap">
          <div id="luiHistory"></div>
        </div>

        <!-- 快捷指令栏 -->
        <div style="padding:8px 12px;border-top:1px solid #e8eaec;">
          <div id="quickCmdHeader" style="font-weight:500;margin-bottom:8px;cursor:pointer;display:flex;justify-content:space-between;align-items:center;">
            <span>快捷指令</span>
            <span id="quickCmdToggle" style="transition: transform 0.2s;">▼</span>
          </div>
          <div id="quickCmdBar" style="display:flex;flex-wrap:wrap;gap:6px;"></div>
        </div>

        <!-- LUI输入区 -->
        <form id="luiForm" style="padding:12px;border-top:1px solid #e8eaec;">
          <div style="display:flex;gap:8px;">
            <input id="luiInput" type="text" placeholder="请输入自然语言指令..." style="flex:1;padding:8px 12px;border-radius:6px;border:1px solid #d9d9d9;font-size:14px;outline:none;" />
            <button type="submit" class="soc-btn soc-btn-primary">发送</button>
          </div>
        </form>

        <!-- 智能提示模板区域 -->
        <div id="smartPromptArea" style="position:absolute;left:12px;right:12px;bottom:70px;background:#fff;border:1px solid #e8eaec;border-radius:6px;padding:8px;display:none;box-shadow:0 -2px 8px rgba(0,0,0,0.1);">
          <div style="font-size:12px;color:#666;margin-bottom:6px;">💡 智能推荐：</div>
          <div id="smartPrompts" style="display:flex;flex-wrap:wrap;gap:6px;"></div>
        </div>
      </div>
      <button class="soc-hide-btn" title="收起对话区" id="hideChatbarBtn">&gt;</button>
    </aside>
  </div>

  <!-- 大纲面板 -->
  <div id="outline-float">
    <div id="outline-panel" style="display:none;">
      <div style="display:flex;align-items:center;justify-content:space-between;margin-bottom:12px;">
        <span style="font-weight:600;font-size:16px;">大纲</span>
        <button id="outline-collapse" style="background:none;border:none;font-size:18px;cursor:pointer;">&laquo;</button>
      </div>
      <div id="outline-tree"></div>
    </div>
    <button id="outline-expand">☰</button>
  </div>

  <script>
    // ===== 全局变量声明 =====
    let currentPage = 1;
    let pageSize = 20;
    let answerList = [];
    let filteredList = [];
    let luiContext = [];
    let currentProductTab = '5GC';
    let draggedElement = null;

    // ===== 初始化函数 =====
    function initializeApp() {
      loadDataFromStorage();
      initializeEventListeners();
      renderAnswerTable();
      renderLuiHistory();
      renderTaskTab('todo');
      updateOutline();
      initializeQuickCommands();
      checkSidebarStates();
    }

    // ===== 数据存储函数 =====
    function loadDataFromStorage() {
      answerList = JSON.parse(localStorage.getItem('socAnswerList') || '[]');
      
      // 修复数据结构，确保每个条目都有唯一ID
      answerList = answerList.map((item, index) => {
        if (!item.id) {
          item.id = 'item_' + Date.now() + '_' + index;
        }
        if (!item.products || !Array.isArray(item.products)) {
          item.products = ['5GC', 'VoLTE', 'IMS'];
        }
        if (!item.productData) {
          item.productData = {};
        }
        // 确保每个产品都有数据
        item.products.forEach(product => {
          if (!item.productData[product]) {
            item.productData[product] = {
              answer: '',
              explain: '',
              supplement: '',
              remark: '',
              index: '',
              source: '',
              answerType: 'AI'
            };
          }
        });
        return item;
      });
      
      saveAnswerList();
    }

    function saveAnswerList() {
      localStorage.setItem('socAnswerList', JSON.stringify(answerList));
    }

    // ===== 事件监听器初始化 =====
    function initializeEventListeners() {
      // 表格搜索事件
      document.getElementById('searchNo').addEventListener('input', debounce(applyFilters, 300));
      document.getElementById('searchDesc').addEventListener('input', debounce(applyFilters, 300));
      document.getElementById('searchProduct').addEventListener('change', applyFilters);
      document.getElementById('searchAnswerType').addEventListener('change', applyFilters);
      document.getElementById('searchAnswer').addEventListener('change', applyFilters);
      document.getElementById('searchExplain').addEventListener('input', debounce(applyFilters, 300));
      document.getElementById('searchRemark').addEventListener('input', debounce(applyFilters, 300));

      // 分页事件
      document.getElementById('pageSizeSelect').addEventListener('change', handlePageSizeChange);
      document.getElementById('prevPageBtn').addEventListener('click', handlePrevPage);
      document.getElementById('nextPageBtn').addEventListener('click', handleNextPage);

      // 全选事件
      document.getElementById('selectAllRow').addEventListener('change', handleSelectAll);

      // 按钮事件
      document.getElementById('importExcelBtn').addEventListener('click', handleImportExcel);
      document.getElementById('addSingleEntryBtn').addEventListener('click', handleAddSingleEntry);
      document.getElementById('exportBtn').addEventListener('click', handleExport);
      document.getElementById('batchReAnswerBtn').addEventListener('click', handleBatchReAnswer);
      document.getElementById('batchDeleteBtn').addEventListener('click', handleBatchDelete);

      // 主菜单事件
      document.getElementById('mainActionBtn').addEventListener('click', toggleMainMenu);
      document.getElementById('mainActionMenu').addEventListener('click', handleMainMenuAction);

      // 侧边栏事件
      document.getElementById('hideSidebarBtn').addEventListener('click', hideSidebar);
      document.getElementById('hideChatbarBtn').addEventListener('click', hideChatbar);

      // 任务标签事件
      document.getElementById('taskTabBar').addEventListener('click', handleTaskTabClick);
      document.getElementById('taskManageBtn').addEventListener('click', showTaskManageDialog);

      // 大纲事件
      document.getElementById('outline-expand').addEventListener('click', showOutline);
      document.getElementById('outline-collapse').addEventListener('click', hideOutline);

      // LUI表单事件
      document.getElementById('luiForm').addEventListener('submit', handleLuiSubmit);
      document.getElementById('luiInput').addEventListener('input', handleLuiInput);

      // 快捷指令栏折叠
      document.getElementById('quickCmdHeader').addEventListener('click', toggleQuickCmd);

      // 相似条目查看
      const showSimilarBtn = document.getElementById('showSimilarBtn');
      if (showSimilarBtn) {
        showSimilarBtn.addEventListener('click', (e) => {
          e.preventDefault();
          showSimilarItems();
        });
      }

      // 全局点击事件（关闭菜单）
      document.addEventListener('click', handleGlobalClick);
    }

    // ===== 表格渲染函数 =====
    function renderAnswerTable() {
      applyFilters();
      
      const tbody = document.getElementById('answerTableBody');
      const startIndex = (currentPage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const pageData = filteredList.slice(startIndex, endIndex);

      let html = '';
      pageData.forEach((item, index) => {
        const globalIndex = startIndex + index;
        if (item.products && item.products.length > 0) {
          item.products.forEach((product, productIndex) => {
            const productData = item.productData[product] || {};
            const isFirstProduct = productIndex === 0;
            const rowspan = item.products.length;

            html += `<tr data-item-id="${item.id}" data-product="${product}" data-global-index="${globalIndex}">
              <td><input type="checkbox" class="rowCheck" data-id="${item.id}" data-product="${product}" /></td>
              ${isFirstProduct ? `<td rowspan="${rowspan}">${item.no || globalIndex + 1}</td>` : ''}
              ${isFirstProduct ? `<td rowspan="${rowspan}">${escapeHtml(item.desc || '')}</td>` : ''}
              <td style="font-weight:500;color:#1765d5;">${product}</td>
              <td>
                <span class="answer-type-tag ${productData.answerType === 'AI' ? 'answer-type-ai' : 'answer-type-manual'}">
                  ${productData.answerType || 'AI'}
                </span>
              </td>
              <td>
                <select class="answer-select" data-id="${item.id}" data-product="${product}">
                  <option value="">未应答</option>
                  <option value="FC" ${productData.answer === 'FC' ? 'selected' : ''}>FC</option>
                  <option value="PC" ${productData.answer === 'PC' ? 'selected' : ''}>PC</option>
                  <option value="NC" ${productData.answer === 'NC' ? 'selected' : ''}>NC</option>
                  <option value="N/A" ${productData.answer === 'N/A' ? 'selected' : ''}>N/A</option>
                </select>
              </td>
              <td>
                <textarea class="explain-input" data-id="${item.id}" data-product="${product}" rows="2">${escapeHtml(productData.explain || '')}</textarea>
              </td>
              <td>${escapeHtml(productData.index || '')}</td>
              <td>
                <textarea class="remark-input" data-id="${item.id}" data-product="${product}" rows="1">${escapeHtml(productData.remark || '')}</textarea>
              </td>
              <td>
                <div style="display:flex;gap:4px;flex-wrap:wrap;">
                  <a href="#" class="action-detail" data-id="${item.id}" data-product="${product}">详情</a>
                  <a href="#" class="action-reanswer" data-id="${item.id}" data-product="${product}">重答</a>
                  <a href="#" class="action-delete" data-id="${item.id}" data-product="${product}" style="color:#ff4d4f;">删除</a>
                </div>
              </td>
            </tr>`;
          });
        }
      });

      tbody.innerHTML = html || '<tr><td colspan="10" style="text-align:center;color:#999;">暂无数据</td></tr>';

      // 绑定表格内事件
      bindTableEvents();

      // 更新分页信息
      updatePagination();

      // 检查相似条目
      checkSimilarItems();
    }

    // ===== 表格内事件绑定 =====
    function bindTableEvents() {
      // 应答选择
      document.querySelectorAll('.answer-select').forEach(select => {
        select.addEventListener('change', function() {
          const itemId = this.dataset.id;
          const product = this.dataset.product;
          updateProductData(itemId, product, 'answer', this.value);
          updateProductData(itemId, product, 'answerType', '人工');
          updateAnswerSelectStyle(this);
        });
      });

      // 应答说明
      document.querySelectorAll('.explain-input').forEach(textarea => {
        textarea.addEventListener('blur', function() {
          const itemId = this.dataset.id;
          const product = this.dataset.product;
          updateProductData(itemId, product, 'explain', this.value);
          updateProductData(itemId, product, 'answerType', '人工');
        });
      });

      // 备注
      document.querySelectorAll('.remark-input').forEach(textarea => {
        textarea.addEventListener('blur', function() {
          const itemId = this.dataset.id;
          const product = this.dataset.product;
          updateProductData(itemId, product, 'remark', this.value);
        });
      });

      // 操作按钮
      document.querySelectorAll('.action-detail').forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();
          showAnswerDetail(this.dataset.id, this.dataset.product);
        });
      });

      document.querySelectorAll('.action-reanswer').forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();
          reAnswerItem(this.dataset.id, this.dataset.product);
        });
      });

      document.querySelectorAll('.action-delete').forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();
          deleteProductFromItem(this.dataset.id, this.dataset.product);
        });
      });
    }

    // ===== 数据更新函数 =====
    function updateProductData(itemId, product, field, value) {
      const item = answerList.find(i => i.id === itemId);
      if (item && item.productData && item.productData[product]) {
        item.productData[product][field] = value;
        saveAnswerList();
      }
    }

    // ===== 筛选函数 =====
    function applyFilters() {
      const searchNo = document.getElementById('searchNo').value.trim();
      const searchDesc = document.getElementById('searchDesc').value.trim().toLowerCase();
      const searchProduct = document.getElementById('searchProduct').value;
      const searchAnswerType = document.getElementById('searchAnswerType').value;
      const searchAnswer = document.getElementById('searchAnswer').value;
      const searchExplain = document.getElementById('searchExplain').value.trim().toLowerCase();
      const searchRemark = document.getElementById('searchRemark').value.trim().toLowerCase();

      filteredList = answerList.filter(item => {
        // 编号筛选
        if (searchNo && !String(item.no || '').includes(searchNo)) {
          return false;
        }

        // 描述筛选
        if (searchDesc && !(item.desc || '').toLowerCase().includes(searchDesc)) {
          return false;
        }

        // 产品筛选
        let matchedProducts = item.products || [];
        if (searchProduct) {
          matchedProducts = matchedProducts.filter(p => p === searchProduct);
          if (matchedProducts.length === 0) return false;
        }

        // 其他筛选条件需要至少有一个产品匹配
        let hasMatch = false;
        for (const product of matchedProducts) {
          const productData = item.productData[product] || {};
          
          if (searchAnswerType && productData.answerType !== searchAnswerType) continue;
          if (searchAnswer) {
            if (searchAnswer === '未应答' && productData.answer) continue;
            if (searchAnswer !== '未应答' && productData.answer !== searchAnswer) continue;
          }
          if (searchExplain && !(productData.explain || '').toLowerCase().includes(searchExplain)) continue;
          if (searchRemark && !(productData.remark || '').toLowerCase().includes(searchRemark)) continue;
          
          hasMatch = true;
          break;
        }

        return hasMatch;
      });

      // 重置到第一页
      currentPage = 1;
    }

    // ===== 分页函数 =====
    function updatePagination() {
      const totalPages = Math.max(1, Math.ceil(filteredList.length / pageSize));
      currentPage = Math.min(currentPage, totalPages);

      document.getElementById('pageInfo').textContent = `${currentPage} / ${totalPages}`;
      document.getElementById('prevPageBtn').disabled = currentPage <= 1;
      document.getElementById('nextPageBtn').disabled = currentPage >= totalPages;
    }

    function handlePageSizeChange() {
      pageSize = parseInt(document.getElementById('pageSizeSelect').value);
      currentPage = 1;
      renderAnswerTable();
    }

    function handlePrevPage() {
      if (currentPage > 1) {
        currentPage--;
        renderAnswerTable();
      }
    }

    function handleNextPage() {
      const totalPages = Math.ceil(filteredList.length / pageSize);
      if (currentPage < totalPages) {
        currentPage++;
        renderAnswerTable();
      }
    }

    // ===== 全选功能 =====
    function handleSelectAll(e) {
      const isChecked = e.target.checked;
      document.querySelectorAll('.rowCheck').forEach(checkbox => {
        checkbox.checked = isChecked;
      });
    }

    // ===== 导入Excel功能 =====
    function handleImportExcel() {
      // 检查参数配置
      const currentParams = localStorage.getItem('currentParams');
      if (!currentParams) {
        showParamSettingDialog(() => {
          // 参数设置完成后继续导入流程
          simulateExcelImport();
        });
      } else {
        simulateExcelImport();
      }
    }

    function simulateExcelImport() {
      const params = JSON.parse(localStorage.getItem('currentParams') || '{}');
      const selectedProducts = params.products || ['5GC', 'VoLTE', 'IMS'];

      // 模拟Excel数据
      const excelData = [
        { no: answerList.length + 1, desc: '系统是否支持负载均衡？' },
        { no: answerList.length + 2, desc: '是否提供API接口？' },
        { no: answerList.length + 3, desc: '系统的并发处理能力如何？' },
        { no: answerList.length + 4, desc: '是否支持数据加密传输？' },
        { no: answerList.length + 5, desc: '系统是否支持容灾备份？' }
      ];

      // 显示产品选择对话框
      showProductSelectDialog(selectedProducts, (products) => {
        const newItems = excelData.map(data => {
          const productData = {};
          products.forEach(product => {
            productData[product] = {
              answer: '',
              explain: '',
              supplement: '',
              remark: '批量导入',
              index: '',
              source: '',
              answerType: 'AI'
            };
          });

          return {
            id: 'item_' + Date.now() + '_' + Math.random(),
            no: data.no,
            desc: data.desc,
            products: products,
            productData: productData
          };
        });

        answerList.push(...newItems);
        saveAnswerList();
        renderAnswerTable();
        
        showToast(`成功导入 ${newItems.length} 条数据`);
      });
    }

    // ===== 新增单条功能 =====
    function handleAddSingleEntry() {
      showAddEntryDialog();
    }

    // ===== 导出功能 =====
    function handleExport() {
      showExportDialog();
    }

    // ===== 批量重新应答 =====
    function handleBatchReAnswer() {
      const checkedItems = getCheckedItems();
      if (checkedItems.length === 0) {
        showToast('请先选择要重新应答的条目', 'warning');
        return;
      }

      showBatchReAnswerDialog(checkedItems);
    }

    // ===== 批量删除 =====
    function handleBatchDelete() {
      const checkedItems = getCheckedItems();
      if (checkedItems.length === 0) {
        showToast('请先选择要删除的条目', 'warning');
        return;
      }

      confirmBatchDelete(checkedItems);
    }

    // ===== 获取选中项 =====
    function getCheckedItems() {
      const checkedItems = [];
      document.querySelectorAll('.rowCheck:checked').forEach(checkbox => {
        checkedItems.push({
          id: checkbox.dataset.id,
          product: checkbox.dataset.product
        });
      });
      return checkedItems;
    }

    // ===== 主菜单功能 =====
    function toggleMainMenu(e) {
      e.stopPropagation();
      const menu = document.getElementById('mainActionMenu');
      menu.style.display = menu.style.display === 'none' ? 'block' : 'none';
    }

    function handleMainMenuAction(e) {
      const action = e.target.dataset.act;
      if (!action) return;

      document.getElementById('mainActionMenu').style.display = 'none';

      switch (action) {
        case 'exportSetting':
          showExportSettingDialog();
          break;
        case 'openParamSetting':
          showParamSettingDialog();
          break;
        case 'openReferenceDoc':
          showReferenceDocDialog();
          break;
        case 'priorityConfig':
          showPriorityConfigDialog();
          break;
        case 'satisfactionCalc':
          calculateSatisfaction();
          break;
        case 'viewSimilar':
          showSimilarItems();
          break;
        case 'historyRecord':
          showHistoryDialog();
          break;
        case 'clearAll':
          confirmClearAll();
          break;
      }
    }

    // ===== 侧边栏功能 =====
    function hideSidebar() {
      const sidebar = document.getElementById('socSidebar');
      sidebar.style.display = 'none';
      localStorage.setItem('sidebarHidden', '1');
    }

    function hideChatbar() {
      const chatbar = document.getElementById('socChatbar');
      chatbar.style.display = 'none';
      localStorage.setItem('chatbarHidden', '1');
    }

    function checkSidebarStates() {
      if (localStorage.getItem('sidebarHidden') === '1') {
        document.getElementById('socSidebar').style.display = 'none';
      }
      if (localStorage.getItem('chatbarHidden') === '1') {
        document.getElementById('socChatbar').style.display = 'none';
      }
    }

    // ===== 任务标签功能 =====
    function handleTaskTabClick(e) {
      if (e.target.classList.contains('task-tab-btn')) {
        document.querySelectorAll('.task-tab-btn').forEach(btn => {
          btn.classList.remove('active');
        });
        e.target.classList.add('active');
        renderTaskTab(e.target.dataset.tab);
      }
    }

    function renderTaskTab(tab) {
      const content = document.getElementById('taskTabContent');
      
      switch (tab) {
        case 'todo':
          content.innerHTML = '<div style="color:#999;text-align:center;padding:20px;">暂无待办任务</div>';
          break;
        case 'history':
          content.innerHTML = '<div style="color:#999;text-align:center;padding:20px;">暂无历史归档</div>';
          break;
        case 'template':
          renderTemplateList();
          break;
      }
    }

    // ===== 模板管理功能 =====
    function renderTemplateList() {
      const templates = JSON.parse(localStorage.getItem('socTemplates') || '[]');
      const content = document.getElementById('taskTabContent');
      
      if (templates.length === 0) {
        content.innerHTML = '<div style="color:#999;text-align:center;padding:20px;">暂无模板</div>';
        return;
      }

      let html = '<div style="padding:8px;">';
      templates.forEach(template => {
        html += `
          <div style="padding:8px;border:1px solid #e8eaec;border-radius:4px;margin-bottom:8px;">
            <div style="font-weight:500;">${template.name}</div>
            <div style="font-size:12px;color:#999;margin-top:4px;">
              产品：${template.products.join(', ')}
            </div>
            <div style="margin-top:8px;">
              <button class="soc-btn" style="padding:2px 8px;height:24px;font-size:12px;" onclick="useTemplate('${template.id}')">使用</button>
              <button class="soc-btn" style="padding:2px 8px;height:24px;font-size:12px;" onclick="deleteTemplate('${template.id}')">删除</button>
            </div>
          </div>
        `;
      });
      html += '</div>';
      
      content.innerHTML = html;
    }

    // ===== 大纲功能 =====
    function showOutline() {
      document.getElementById('outline-panel').style.display = 'block';
      document.getElementById('outline-expand').style.display = 'none';
    }

    function hideOutline() {
      document.getElementById('outline-panel').style.display = 'none';
      document.getElementById('outline-expand').style.display = 'block';
    }

    function updateOutline() {
      const tree = buildOutlineTree();
      const treeContainer = document.getElementById('outline-tree');
      treeContainer.innerHTML = renderOutlineTree(tree);
    }

    function buildOutlineTree() {
      const tree = [];
      const nodeMap = {};

      filteredList.forEach((item, index) => {
        const no = String(item.no || index + 1);
        const parts = no.split('.');
        
        const node = {
          no: no,
          desc: item.desc || '',
          id: item.id,
          children: []
        };

        nodeMap[no] = node;

        if (parts.length === 1) {
          tree.push(node);
        } else {
          const parentNo = parts.slice(0, -1).join('.');
          const parent = nodeMap[parentNo];
          if (parent) {
            parent.children.push(node);
          } else {
            tree.push(node);
          }
        }
      });

      return tree;
    }

    function renderOutlineTree(nodes, level = 0) {
      let html = '<ul style="margin:0;padding-left:' + (level * 20) + 'px;list-style:none;">';
      
      nodes.forEach(node => {
        html += `
          <li style="margin:4px 0;">
            <div class="outline-node" data-id="${node.id}" style="cursor:pointer;padding:4px 8px;border-radius:4px;transition:background 0.2s;">
              ${node.no}. ${node.desc}
            </div>
            ${node.children.length > 0 ? renderOutlineTree(node.children, level + 1) : ''}
          </li>
        `;
      });
      
      html += '</ul>';
      return html;
    }

    // ===== LUI功能 =====
    function handleLuiSubmit(e) {
      e.preventDefault();
      const input = document.getElementById('luiInput');
      const query = input.value.trim();
      
      if (!query) return;

      addLuiMessage('user', query);
      input.value = '';

      // 模拟AI响应
      setTimeout(() => {
        const response = processNaturalLanguage(query);
        addLuiMessage('ai', response);
      }, 500);
    }

    function handleLuiInput(e) {
      const value = e.target.value;
      if (value.length > 2) {
        showSmartPrompts(value);
      } else {
        hideSmartPrompts();
      }
    }

    function addLuiMessage(role, text) {
      const history = document.getElementById('luiHistory');
      const messageDiv = document.createElement('div');
      messageDiv.className = `chat-message chat-message-${role}`;
      
      const bubbleDiv = document.createElement('div');
      bubbleDiv.className = `chat-bubble chat-bubble-${role}`;
      bubbleDiv.innerHTML = escapeHtml(text);
      
      messageDiv.appendChild(bubbleDiv);
      history.appendChild(messageDiv);
      
      // 滚动到底部
      history.scrollTop = history.scrollHeight;
      
      // 保存到本地存储
      saveLuiHistory(role, text);
    }

    function saveLuiHistory(role, text) {
      const history = JSON.parse(localStorage.getItem('luiHistory') || '[]');
      history.push({ role, text, time: Date.now() });
      
      // 只保留最近100条
      if (history.length > 100) {
        history.splice(0, history.length - 100);
      }
      
      localStorage.setItem('luiHistory', JSON.stringify(history));
    }

    function renderLuiHistory() {
      const history = JSON.parse(localStorage.getItem('luiHistory') || '[]');
      const container = document.getElementById('luiHistory');
      container.innerHTML = '';
      
      history.forEach(msg => {
        addLuiMessage(msg.role, msg.text);
      });
    }

    function processNaturalLanguage(query) {
      const lowerQuery = query.toLowerCase();
      
      if (lowerQuery.includes('设置') && lowerQuery.includes('参数')) {
        showParamSettingDialog();
        return '已打开参数设置对话框';
      }
      
      if (lowerQuery.includes('导入')) {
        handleImportExcel();
        return '正在启动导入流程...';
      }
      
      if (lowerQuery.includes('导出')) {
        handleExport();
        return '已打开导出对话框';
      }
      
      if (lowerQuery.includes('满足度') || lowerQuery.includes('统计')) {
        calculateSatisfaction();
        return '正在计算满足度...';
      }
      
      if (lowerQuery.includes('相似')) {
        showSimilarItems();
        return '正在查找相似条目...';
      }
      
      return '我理解您的需求。请尝试使用更具体的指令，例如"设置参数"、"导入数据"、"导出结果"等。';
    }

    // ===== 快捷指令功能 =====
    function initializeQuickCommands() {
      const commands = [
        '设置项目参数',
        '导入Excel数据',
        '查看满足度统计',
        '批量重新应答',
        '导出应答结果'
      ];

      const container = document.getElementById('quickCmdBar');
      container.innerHTML = commands.map(cmd => 
        `<button class="quick-cmd-btn" onclick="executeQuickCommand('${cmd}')">${cmd}</button>`
      ).join('');
    }

    function executeQuickCommand(cmd) {
      document.getElementById('luiInput').value = cmd;
      document.getElementById('luiForm').dispatchEvent(new Event('submit'));
    }

    function toggleQuickCmd() {
      const bar = document.getElementById('quickCmdBar');
      const toggle = document.getElementById('quickCmdToggle');
      
      if (bar.style.display === 'none') {
        bar.style.display = 'flex';
        toggle.style.transform = 'rotate(0deg)';
      } else {
        bar.style.display = 'none';
        toggle.style.transform = 'rotate(180deg)';
      }
    }

    // ===== 智能提示功能 =====
    function showSmartPrompts(input) {
      const prompts = generateSmartPrompts(input);
      const container = document.getElementById('smartPrompts');
      
      container.innerHTML = prompts.map(prompt => 
        `<button class="smart-prompt-btn" onclick="selectSmartPrompt('${prompt}')">${prompt}</button>`
      ).join('');
      
      document.getElementById('smartPromptArea').style.display = 'block';
    }

    function hideSmartPrompts() {
      document.getElementById('smartPromptArea').style.display = 'none';
    }

    function selectSmartPrompt(prompt) {
      document.getElementById('luiInput').value = prompt;
      hideSmartPrompts();
    }

    function generateSmartPrompts(input) {
      const prompts = [];
      const lower = input.toLowerCase();
      
      if (lower.includes('设置')) {
        prompts.push('设置项目参数：产品5GC 国家泰国');
      }
      
      if (lower.includes('导入')) {
        prompts.push('导入招标文件.xlsx');
      }
      
      if (lower.includes('分析')) {
        prompts.push('分析5GC产品的安全要求');
      }
      
      if (lower.includes('导出')) {
        prompts.push('导出满足条件的应答结果');
      }
      
      return prompts.slice(0, 3);
    }

    // ===== 相似条目功能 =====
    function checkSimilarItems() {
      const groups = findSimilarGroups();
      const similarTip = document.getElementById('similarTip');
      const similarCount = document.getElementById('similarCount');
      
      if (groups.length > 0) {
        similarTip.style.display = 'inline';
        similarCount.textContent = groups.length;
      } else {
        similarTip.style.display = 'none';
      }
    }

    function findSimilarGroups() {
      const groups = [];
      const processed = new Set();
      
      filteredList.forEach((item1, index1) => {
        if (processed.has(index1)) return;
        
        const group = [index1];
        
        filteredList.forEach((item2, index2) => {
          if (index1 !== index2 && !processed.has(index2)) {
            if (isSimilar(item1.desc, item2.desc)) {
              group.push(index2);
              processed.add(index2);
            }
          }
        });
        
        if (group.length > 1) {
          groups.push(group);
          group.forEach(idx => processed.add(idx));
        }
      });
      
      return groups;
    }

    function isSimilar(str1, str2) {
      if (!str1 || !str2) return false;
      
      const clean1 = str1.toLowerCase().replace(/[？?]/g, '').trim();
      const clean2 = str2.toLowerCase().replace(/[？?]/g, '').trim();
      
      // 简单的相似度判断
      if (clean1 === clean2) return true;
      
      // 计算编辑距离
      const distance = levenshteinDistance(clean1, clean2);
      const maxLen = Math.max(clean1.length, clean2.length);
      const similarity = 1 - distance / maxLen;
      
      return similarity > 0.8;
    }

    function levenshteinDistance(str1, str2) {
      const m = str1.length;
      const n = str2.length;
      const dp = Array(m + 1).fill(null).map(() => Array(n + 1).fill(0));
      
      for (let i = 0; i <= m; i++) dp[i][0] = i;
      for (let j = 0; j <= n; j++) dp[0][j] = j;
      
      for (let i = 1; i <= m; i++) {
        for (let j = 1; j <= n; j++) {
          if (str1[i - 1] === str2[j - 1]) {
            dp[i][j] = dp[i - 1][j - 1];
          } else {
            dp[i][j] = Math.min(
              dp[i - 1][j] + 1,
              dp[i][j - 1] + 1,
              dp[i - 1][j - 1] + 1
            );
          }
        }
      }
      
      return dp[m][n];
    }

    // ===== 对话框函数 =====
    function showToast(message, type = 'success') {
      const toast = document.createElement('div');
      toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 24px;
        background: ${type === 'success' ? '#52c41a' : type === 'warning' ? '#faad14' : '#ff4d4f'};
        color: white;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        z-index: 10000;
        animation: slideIn 0.3s ease;
      `;
      toast.textContent = message;
      
      document.body.appendChild(toast);
      
      setTimeout(() => {
        toast.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => document.body.removeChild(toast), 300);
      }, 3000);
    }

    function createModal(title, content, footer) {
      const modal = document.createElement('div');
      modal.className = 'modal-overlay';
      modal.innerHTML = `
        <div class="modal-content">
          <div class="modal-header">
            ${title}
            <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">×</button>
          </div>
          <div class="modal-body">${content}</div>
          ${footer ? `<div class="modal-footer">${footer}</div>` : ''}
        </div>
      `;
      
      document.body.appendChild(modal);
      return modal;
    }

    function showParamSettingDialog(callback) {
      const content = `
        <form id="paramForm">
          <div style="display:grid;grid-template-columns:1fr 1fr;gap:16px;">
            <div>
              <label>产品选择 <span style="color:red;">*</span></label>
              <div class="tree-select-container">
                <div class="tree-select-input" onclick="toggleProductTree(this)">
                  <span class="tree-select-placeholder" style="color:#999;">请选择产品</span>
                </div>
                <div class="tree-select-dropdown">
                  <div style="padding:8px;">
                    <label><input type="checkbox" value="5GC" /> 5GC</label><br/>
                    <label><input type="checkbox" value="VoLTE" /> VoLTE</label><br/>
                    <label><input type="checkbox" value="IMS" /> IMS</label><br/>
                    <label><input type="checkbox" value="核心网" /> 核心网</label><br/>
                    <label><input type="checkbox" value="接入网" /> 接入网</label>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <label>国家</label>
              <input type="text" id="paramCountry" style="width:100%;" placeholder="请输入国家" />
            </div>
            <div>
              <label>客户</label>
              <input type="text" id="paramOperator" style="width:100%;" placeholder="请输入客户" />
            </div>
            <div>
              <label>项目名称</label>
              <input type="text" id="paramProject" style="width:100%;" placeholder="请输入项目名称" />
            </div>
          </div>
        </form>
      `;

      const footer = `
        <button class="soc-btn soc-btn-primary" onclick="saveParams()">确定</button>
        <button class="soc-btn" onclick="this.closest('.modal-overlay').remove()">取消</button>
      `;

      const modal = createModal('参数设置', content, footer);
      
      // 设置全局回调
      window.paramSettingCallback = callback;
    }

    function showAddEntryDialog() {
      const content = `
        <form id="addEntryForm">
          <div style="margin-bottom:16px;">
            <label>编号</label>
            <input type="text" id="entryNo" style="width:100%;" placeholder="自动生成或手动输入" />
          </div>
          <div style="margin-bottom:16px;">
            <label>条目描述 <span style="color:red;">*</span></label>
            <textarea id="entryDesc" style="width:100%;height:80px;" placeholder="请输入条目描述" required></textarea>
          </div>
          <div style="margin-bottom:16px;">
            <label>产品选择</label>
            <div>
              <label><input type="checkbox" value="5GC" checked /> 5GC</label>
              <label style="margin-left:16px;"><input type="checkbox" value="VoLTE" checked /> VoLTE</label>
              <label style="margin-left:16px;"><input type="checkbox" value="IMS" checked /> IMS</label>
            </div>
          </div>
          <div>
            <label>备注</label>
            <input type="text" id="entryRemark" style="width:100%;" placeholder="可选" />
          </div>
        </form>
      `;

      const footer = `
        <button class="soc-btn soc-btn-primary" onclick="addEntry()">确定</button>
        <button class="soc-btn" onclick="this.closest('.modal-overlay').remove()">取消</button>
      `;

      createModal('新增应答条目', content, footer);
    }

    function showAnswerDetail(itemId, product) {
      const item = answerList.find(i => i.id === itemId);
      if (!item) return;

      const productData = item.productData[product] || {};
      
      const content = `
        <div style="width:800px;max-width:90vw;">
          <div style="margin-bottom:20px;">
            <h3>条目信息</h3>
            <div style="background:#f5f7fa;padding:12px;border-radius:6px;">
              <div><strong>编号：</strong>${item.no}</div>
              <div><strong>描述：</strong>${item.desc}</div>
              <div><strong>产品：</strong>${product}</div>
            </div>
          </div>
          
          <div style="margin-bottom:20px;">
            <h3>应答信息</h3>
            <div style="display:grid;grid-template-columns:1fr 1fr;gap:16px;">
              <div>
                <label>应答状态</label>
                <select id="detailAnswer" style="width:100%;">
                  <option value="">未应答</option>
                  <option value="FC" ${productData.answer === 'FC' ? 'selected' : ''}>FC - 完全满足</option>
                  <option value="PC" ${productData.answer === 'PC' ? 'selected' : ''}>PC - 部分满足</option>
                  <option value="NC" ${productData.answer === 'NC' ? 'selected' : ''}>NC - 不满足</option>
                  <option value="N/A" ${productData.answer === 'N/A' ? 'selected' : ''}>N/A - 不适用</option>
                </select>
              </div>
              <div>
                <label>应答方式</label>
                <input type="text" value="${productData.answerType || 'AI'}" readonly style="width:100%;" />
              </div>
            </div>
          </div>
          
          <div style="margin-bottom:20px;">
            <label>应答说明</label>
            <textarea id="detailExplain" style="width:100%;height:100px;">${productData.explain || ''}</textarea>
          </div>
          
          <div style="margin-bottom:20px;">
            <label>补充信息</label>
            <textarea id="detailSupplement" style="width:100%;height:60px;">${productData.supplement || ''}</textarea>
          </div>
          
          <div style="display:grid;grid-template-columns:1fr 1fr;gap:16px;">
            <div>
              <label>索引</label>
              <input type="text" id="detailIndex" value="${productData.index || ''}" style="width:100%;" />
            </div>
            <div>
              <label>来源</label>
              <input type="text" id="detailSource" value="${productData.source || ''}" style="width:100%;" />
            </div>
          </div>
        </div>
      `;

      const footer = `
        <button class="soc-btn soc-btn-primary" onclick="saveDetail('${itemId}', '${product}')">保存</button>
        <button class="soc-btn soc-btn-warning" onclick="aiPolish('${itemId}', '${product}')">AI润色</button>
        <button class="soc-btn" onclick="this.closest('.modal-overlay').remove()">关闭</button>
      `;

      createModal('应答详情', content, footer);
    }

    function showExportDialog() {
      const content = `
        <div style="width:500px;">
          <div style="margin-bottom:20px;">
            <h4>导出类型</h4>
            <div>
              <label><input type="checkbox" class="export-type" value="FC" checked /> FC - 完全满足</label><br/>
              <label><input type="checkbox" class="export-type" value="PC" checked /> PC - 部分满足</label><br/>
              <label><input type="checkbox" class="export-type" value="NC" checked /> NC - 不满足</label><br/>
              <label><input type="checkbox" class="export-type" value="N/A" checked /> N/A - 不适用</label><br/>
              <label><input type="checkbox" class="export-type" value="" /> 未应答</label>
            </div>
          </div>
          
          <div>
            <h4>导出格式</h4>
            <div>
              <label><input type="radio" name="exportFormat" value="excel" checked /> Excel格式</label><br/>
              <label><input type="radio" name="exportFormat" value="csv" /> CSV格式</label><br/>
              <label><input type="radio" name="exportFormat" value="json" /> JSON格式</label>
            </div>
          </div>
        </div>
      `;

      const footer = `
        <button class="soc-btn soc-btn-primary" onclick="doExport()">导出</button>
        <button class="soc-btn" onclick="this.closest('.modal-overlay').remove()">取消</button>
      `;

      createModal('导出设置', content, footer);
    }

    function showSimilarItems() {
      const groups = findSimilarGroups();
      if (groups.length === 0) {
        showToast('没有发现相似条目', 'info');
        return;
      }

      let content = '<div style="width:600px;max-height:500px;overflow:auto;">';
      
      groups.forEach((group, groupIndex) => {
        content += `<div style="margin-bottom:20px;padding:12px;border:1px solid #e8eaec;border-radius:6px;">`;
        content += `<h4>相似组 ${groupIndex + 1}</h4>`;
        
        group.forEach(index => {
          const item = filteredList[index];
          content += `<div style="padding:8px;margin:4px 0;background:#f5f7fa;border-radius:4px;">`;
          content += `<strong>${item.no}.</strong> ${item.desc}`;
          content += `</div>`;
        });
        
        content += `<button class="soc-btn" style="margin-top:8px;" onclick="mergeSimilar(${groupIndex})">合并处理</button>`;
        content += `</div>`;
      });
      
      content += '</div>';

      createModal('相似条目', content);
    }

    // ===== 满足度计算 =====
    function calculateSatisfaction() {
      let stats = {
        total: 0,
        fc: 0,
        pc: 0,
        nc: 0,
        na: 0,
        unanswered: 0
      };

      filteredList.forEach(item => {
        item.products.forEach(product => {
          stats.total++;
          const productData = item.productData[product] || {};
          const answer = productData.answer || '';
          
          switch (answer) {
            case 'FC': stats.fc++; break;
            case 'PC': stats.pc++; break;
            case 'NC': stats.nc++; break;
            case 'N/A': stats.na++; break;
            default: stats.unanswered++; break;
          }
        });
      });

      const satisfaction = stats.total > 0 ? ((stats.fc + stats.pc) / stats.total * 100).toFixed(1) : 0;
      const unsatisfaction = stats.total > 0 ? (stats.nc / stats.total * 100).toFixed(1) : 0;

      const message = `
        📊 满足度统计结果：
        - 总条目数：${stats.total}
        - 完全满足(FC)：${stats.fc} (${(stats.fc / stats.total * 100).toFixed(1)}%)
        - 部分满足(PC)：${stats.pc} (${(stats.pc / stats.total * 100).toFixed(1)}%)
        - 不满足(NC)：${stats.nc} (${(stats.nc / stats.total * 100).toFixed(1)}%)
        - 不适用(N/A)：${stats.na} (${(stats.na / stats.total * 100).toFixed(1)}%)
        - 未应答：${stats.unanswered} (${(stats.unanswered / stats.total * 100).toFixed(1)}%)
        
        满足度：${satisfaction}%
        不满足度：${unsatisfaction}%
      `;

      addLuiMessage('ai', message);
    }

    // ===== 工具函数 =====
    function escapeHtml(text) {
      const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
      };
      return text.replace(/[&<>"']/g, m => map[m]);
    }

    function debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }

    function updateAnswerSelectStyle(select) {
      const value = select.value;
      const styles = {
        'FC': { bg: '#f6ffed', color: '#52c41a' },
        'PC': { bg: '#fff7e6', color: '#faad14' },
        'NC': { bg: '#fff2f0', color: '#ff4d4f' },
        'N/A': { bg: '#f5f5f5', color: '#666' },
        '': { bg: '#ffffff', color: '#333' }
      };
      
      const style = styles[value] || styles[''];
      select.style.background = style.bg;
      select.style.color = style.color;
    }

    // ===== 全局函数（供HTML调用） =====
    window.toggleProductTree = function(element) {
      const dropdown = element.nextElementSibling;
      dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
    };

    window.saveParams = function() {
      const form = document.getElementById('paramForm');
      const checkboxes = form.querySelectorAll('.tree-select-dropdown input[type="checkbox"]:checked');
      const products = Array.from(checkboxes).map(cb => cb.value);
      
      if (products.length === 0) {
        showToast('请至少选择一个产品', 'warning');
        return;
      }

      const params = {
        products: products,
        country: document.getElementById('paramCountry').value,
        operator: document.getElementById('paramOperator').value,
        projectName: document.getElementById('paramProject').value,
        timestamp: new Date().toISOString()
      };

      localStorage.setItem('currentParams', JSON.stringify(params));
      document.querySelector('.modal-overlay').remove();
      showToast('参数设置已保存');

      // 执行回调
      if (window.paramSettingCallback) {
        window.paramSettingCallback();
        window.paramSettingCallback = null;
      }
    };

    window.addEntry = function() {
      const desc = document.getElementById('entryDesc').value.trim();
      if (!desc) {
        showToast('请输入条目描述', 'warning');
        return;
      }

      const checkboxes = document.querySelectorAll('#addEntryForm input[type="checkbox"]:checked');
      const products = Array.from(checkboxes).map(cb => cb.value);
      
      if (products.length === 0) {
        showToast('请至少选择一个产品', 'warning');
        return;
      }

      const productData = {};
      products.forEach(product => {
        productData[product] = {
          answer: '',
          explain: '',
          supplement: '',
          remark: document.getElementById('entryRemark').value,
          index: '',
          source: '',
          answerType: 'AI'
        };
      });

      const newItem = {
        id: 'item_' + Date.now(),
        no: document.getElementById('entryNo').value || answerList.length + 1,
        desc: desc,
        products: products,
        productData: productData
      };

      answerList.push(newItem);
      saveAnswerList();
      renderAnswerTable();
      document.querySelector('.modal-overlay').remove();
      showToast('条目添加成功');
    };

    window.saveDetail = function(itemId, product) {
      const item = answerList.find(i => i.id === itemId);
      if (!item) return;

      item.productData[product] = {
        answer: document.getElementById('detailAnswer').value,
        explain: document.getElementById('detailExplain').value,
        supplement: document.getElementById('detailSupplement').value,
        index: document.getElementById('detailIndex').value,
        source: document.getElementById('detailSource').value,
        answerType: '人工',
        remark: item.productData[product]?.remark || ''
      };

      saveAnswerList();
      renderAnswerTable();
      document.querySelector('.modal-overlay').remove();
      showToast('保存成功');
    };

    window.aiPolish = function(itemId, product) {
      const explain = document.getElementById('detailExplain');
      explain.value = explain.value + '\n\n[AI润色] 该系统完全满足相关要求，具备良好的扩展性和稳定性。';
      showToast('AI润色完成');
    };

    window.doExport = function() {
      const types = Array.from(document.querySelectorAll('.export-type:checked')).map(cb => cb.value);
      const format = document.querySelector('input[name="exportFormat"]:checked').value;
      
      if (types.length === 0) {
        showToast('请选择要导出的类型', 'warning');
        return;
      }

      // 过滤数据
      const exportData = [];
      filteredList.forEach(item => {
        item.products.forEach(product => {
          const productData = item.productData[product] || {};
          const answer = productData.answer || '';
          
          if (types.includes(answer)) {
            exportData.push({
              no: item.no,
              desc: item.desc,
              product: product,
              answer: answer,
              explain: productData.explain || '',
              index: productData.index || '',
              remark: productData.remark || ''
            });
          }
        });
      });

      // 根据格式导出
      if (format === 'csv') {
        exportAsCSV(exportData);
      } else if (format === 'json') {
        exportAsJSON(exportData);
      } else {
        showToast('Excel导出功能开发中，请使用CSV格式', 'info');
      }

      document.querySelector('.modal-overlay').remove();
    };

    function exportAsCSV(data) {
      const headers = ['编号', '条目描述', '产品', '应答', '应答说明', '索引', '备注'];
      const rows = data.map(item => [
        item.no,
        item.desc,
        item.product,
        item.answer,
        item.explain,
        item.index,
        item.remark
      ]);

      let csv = headers.join(',') + '\n';
      rows.forEach(row => {
        csv += row.map(cell => `"${cell}"`).join(',') + '\n';
      });

      const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `SOC应答_${new Date().toISOString().split('T')[0]}.csv`;
      link.click();
      
      showToast(`成功导出 ${data.length} 条数据`);
    }

    function exportAsJSON(data) {
      const json = JSON.stringify(data, null, 2);
      const blob = new Blob([json], { type: 'application/json' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `SOC应答_${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      
      showToast(`成功导出 ${data.length} 条数据`);
    }

    window.deleteProductFromItem = function(itemId, product) {
      if (!confirm('确定要删除该产品的应答吗？')) return;

      const item = answerList.find(i => i.id === itemId);
      if (!item) return;

      // 从产品列表中移除
      const productIndex = item.products.indexOf(product);
      if (productIndex > -1) {
        item.products.splice(productIndex, 1);
      }

      // 删除产品数据
      delete item.productData[product];

      // 如果没有产品了，删除整个条目
      if (item.products.length === 0) {
        const itemIndex = answerList.indexOf(item);
        if (itemIndex > -1) {
          answerList.splice(itemIndex, 1);
        }
      }

      saveAnswerList();
      renderAnswerTable();
      showToast('删除成功');
    };

    window.reAnswerItem = function(itemId, product) {
      const item = answerList.find(i => i.id === itemId);
      if (!item) return;

      // 模拟AI重新应答
      item.productData[product] = {
        answer: ['FC', 'PC', 'NC', 'N/A'][Math.floor(Math.random() * 4)],
        explain: `[AI重新生成] ${product}系统的应答说明...`,
        supplement: 'AI分析补充信息',
        index: `${product}-${Math.floor(Math.random() * 10) + 1}.${Math.floor(Math.random() * 10) + 1}`,
        source: 'AI分析',
        answerType: 'AI',
        remark: item.productData[product]?.remark || ''
      };

      saveAnswerList();
      renderAnswerTable();
      showToast('AI重新应答完成');
    };

    window.confirmBatchDelete = function(checkedItems) {
      if (!confirm(`确定要删除选中的 ${checkedItems.length} 项吗？`)) return;

      // 按产品分组
      const itemGroups = {};
      checkedItems.forEach(({ id, product }) => {
        if (!itemGroups[id]) {
          itemGroups[id] = [];
        }
        itemGroups[id].push(product);
      });

      // 处理删除
      Object.entries(itemGroups).forEach(([itemId, products]) => {
        const item = answerList.find(i => i.id === itemId);
        if (!item) return;

        products.forEach(product => {
          const productIndex = item.products.indexOf(product);
          if (productIndex > -1) {
            item.products.splice(productIndex, 1);
          }
          delete item.productData[product];
        });

        // 如果没有产品了，删除整个条目
        if (item.products.length === 0) {
          const itemIndex = answerList.indexOf(item);
          if (itemIndex > -1) {
            answerList.splice(itemIndex, 1);
          }
        }
      });

      saveAnswerList();
      renderAnswerTable();
      showToast(`成功删除 ${checkedItems.length} 项`);
    };

    window.confirmClearAll = function() {
      if (!confirm('确定要清空所有条目吗？此操作不可恢复！')) return;
      
      answerList = [];
      saveAnswerList();
      renderAnswerTable();
      showToast('已清空所有条目');
    };

    window.showProductSelectDialog = function(products, callback) {
      const content = `
        <div>
          <p>请选择要应答的产品：</p>
          <div>
            ${products.map(product => `
              <label style="display:block;margin:8px 0;">
                <input type="checkbox" value="${product}" checked /> ${product}
              </label>
            `).join('')}
          </div>
        </div>
      `;

      const footer = `
        <button class="soc-btn soc-btn-primary" onclick="confirmProductSelect()">确定</button>
        <button class="soc-btn" onclick="this.closest('.modal-overlay').remove()">取消</button>
      `;

      createModal('选择产品', content, footer);
      
      window.productSelectCallback = callback;
    };

    window.confirmProductSelect = function() {
      const checkboxes = document.querySelectorAll('.modal-overlay input[type="checkbox"]:checked');
      const selected = Array.from(checkboxes).map(cb => cb.value);
      
      if (selected.length === 0) {
        showToast('请至少选择一个产品', 'warning');
        return;
      }

      document.querySelector('.modal-overlay').remove();
      
      if (window.productSelectCallback) {
        window.productSelectCallback(selected);
        window.productSelectCallback = null;
      }
    };

    window.showBatchReAnswerDialog = function(checkedItems) {
      const content = `
        <div>
          <p>选中了 ${checkedItems.length} 项，请选择重新应答方式：</p>
          <div>
            <label style="display:block;margin:8px 0;">
              <input type="radio" name="reanswerMode" value="all" checked /> 重新应答所有内容
            </label>
            <label style="display:block;margin:8px 0;">
              <input type="radio" name="reanswerMode" value="empty" /> 仅应答空白项
            </label>
            <label style="display:block;margin:8px 0;">
              <input type="radio" name="reanswerMode" value="nc" /> 仅重新应答NC项
            </label>
          </div>
        </div>
      `;

      const footer = `
        <button class="soc-btn soc-btn-primary" onclick="doBatchReAnswer()">开始应答</button>
        <button class="soc-btn" onclick="this.closest('.modal-overlay').remove()">取消</button>
      `;

      createModal('批量重新应答', content, footer);
      
      window.batchReAnswerItems = checkedItems;
    };

    window.doBatchReAnswer = function() {
      const mode = document.querySelector('input[name="reanswerMode"]:checked').value;
      const items = window.batchReAnswerItems;
      
      let count = 0;
      items.forEach(({ id, product }) => {
        const item = answerList.find(i => i.id === id);
        if (!item) return;

        const productData = item.productData[product] || {};
        
        if (mode === 'all' || 
            (mode === 'empty' && !productData.answer) || 
            (mode === 'nc' && productData.answer === 'NC')) {
          
          item.productData[product] = {
            answer: ['FC', 'PC', 'NC', 'N/A'][Math.floor(Math.random() * 4)],
            explain: `[AI批量生成] ${product}系统的应答说明...`,
            supplement: 'AI分析补充信息',
            index: `${product}-${Math.floor(Math.random() * 10) + 1}.${Math.floor(Math.random() * 10) + 1}`,
            source: 'AI分析',
            answerType: 'AI',
            remark: productData.remark || ''
          };
          count++;
        }
      });

      saveAnswerList();
      renderAnswerTable();
      document.querySelector('.modal-overlay').remove();
      showToast(`成功重新应答 ${count} 项`);
    };

    window.handleGlobalClick = function(e) {
      // 关闭主菜单
      if (!e.target.closest('#mainActionBtn') && !e.target.closest('#mainActionMenu')) {
        document.getElementById('mainActionMenu').style.display = 'none';
      }
      
      // 关闭产品树下拉框
      if (!e.target.closest('.tree-select-container')) {
        document.querySelectorAll('.tree-select-dropdown').forEach(dropdown => {
          dropdown.style.display = 'none';
        });
      }
    };

    // ===== 初始化应用 =====
    window.addEventListener('DOMContentLoaded', initializeApp);

    // ===== 添加必要的CSS动画 =====
    const style = document.createElement('style');
    style.textContent = `
      @keyframes slideIn {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }
      
      @keyframes slideOut {
        from {
          transform: translateX(0);
          opacity: 1;
        }
        to {
          transform: translateX(100%);
          opacity: 0;
        }
      }
    `;
    document.head.appendChild(style);
  </script>
  
  <!-- 引入补充修复脚本 -->
  <script src="demo1_additional_fixes.js"></script>
</body>
</html>