package com.zte.mcrm.clues.ui.controller;

import com.zte.mcrm.clues.access.vo.BusinessClues;
import com.zte.mcrm.clues.access.vo.BusinessCluesInfoVO;
import com.zte.mcrm.clues.business.service.CluesSaveService;
import com.zte.mcrm.clues.business.service.PCBusinessCluesService;
import com.zte.mcrm.clues.mapper.AbstractCluesDataBeanMapper;
import com.zte.mcrm.clues.model.CluesSaveDTO;
import com.zte.mcrm.clues.model.CluesUpdateDTO;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.common.exception.ValidationException;
import com.zte.springbootframe.common.model.ServiceDataCopy;
import com.zte.springbootframe.common.model.ServiceDataUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/****
 *
 * <AUTHOR> @date 2021/2/10
 **/

@RestController
@Api("线索相关API")
public class CluesSaveController {

    @Autowired
    private CluesSaveService cluesSaveService;

    @Autowired
    private PCBusinessCluesService pcBusinessCluesService;

    @RequestMapping(value = "/pc/clues/init", method = RequestMethod.GET)
    public ServiceDataCopy<BusinessCluesInfoVO> initClues(
            @RequestHeader(SysGlobalConst.HTTP_HEADER_X_TENANT_ID) String tenantId,
            @RequestHeader(SysGlobalConst.HTTP_HEADER_X_LANG_ID) String lang,
            @RequestHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO) String xEmpNo,
            @RequestParam(name = "empNo", required = true) String empNo,
            @RequestParam(name = "clueId", required = false) String clueId
    ) throws BusiException {
        // 初始化信息
        BusinessCluesInfoVO entity = new BusinessCluesInfoVO();
        entity.setId(clueId);
        entity.setxEmpNo(empNo);
        entity.setxLangId(lang);
        entity.setxTenantId(tenantId);
        return ServiceDataUtil.success(pcBusinessCluesService.initBusinessCluesInfo(entity));
    }

    @ApiOperation("PC端新建线索")
    @RequestMapping(value = "/pc/clues", method = RequestMethod.POST)
    public ServiceDataCopy<BusinessClues> postClues(
            @RequestHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO) String xEmpNo,
            @Validated @RequestBody(required = true) CluesSaveDTO dto,
            BindingResult bindingResult) throws BusiException, ValidationException {
        if (bindingResult != null && bindingResult.hasErrors()) {
            throw new ValidationException(bindingResult);
        }
        // DTO(数据传输对象)转为VO(持久化对象)
        BusinessClues businessClues = AbstractCluesDataBeanMapper.dtoToVo(dto);
        BusinessClues result = businessClues;
        // 员工编号
        if(businessClues != null) {
        	businessClues.setxEmpNo(xEmpNo);	
        	result = cluesSaveService.save(businessClues);
        }
        return ServiceDataUtil.success(result);
    }

    @ApiOperation("PC端线索状态为待客户经理更新")

    @RequestMapping(value = "/pc/clues/renewing", method = RequestMethod.POST)
    public ServiceDataCopy<Object> renewingClues(
            @RequestHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO) String xEmpNo,
            @Validated @RequestBody(required = true) CluesUpdateDTO dto,
            BindingResult bindingResult) throws BusiException, ValidationException {
        if (bindingResult != null && bindingResult.hasErrors()) {
            throw new ValidationException(bindingResult);
        }
        if (dto == null) {
            return null;
        }
        BusinessClues businessClues = new BusinessClues();
        BeanUtils.copyProperties(dto, businessClues);
        // 员工编号
        businessClues.setxEmpNo(xEmpNo);
        cluesSaveService.update(businessClues);
        return ServiceDataUtil.success(null);
    }
}
