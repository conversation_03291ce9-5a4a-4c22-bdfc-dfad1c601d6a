package com.zte.mcrm.channel.service.common;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zte.crm.eva.base.common.constant.universal.UniversalUpdateConstant;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.channel.model.entity.Opportunity;
import com.zte.mcrm.channel.model.entity.OpportunityDetail;
import com.zte.mcrm.channel.service.channel.IOpportunityRedundancyService;
import com.zte.opty.dao.SOptyTeamDao;
import com.zte.opty.model.bo.OptionBO;
import com.zte.opty.model.bo.SOptyRedundancyBO;
import com.zte.opty.model.bo.SOptyTeamBO;
import com.zte.opty.model.vo.OptyEmployeeVO;
import com.zte.opty.model.vo.SOptyTeamVO;
import com.zte.opty.sync.domain.constants.ConvertConstants;
import com.zte.opty.sync.domain.converter.SOptyTeamConverter;
import com.zte.opty.sync.util.LcapConverterUtil;
import com.zte.opty.sync.util.RadioFieldConverterUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class TeamConverter {

    @Autowired
    private SOptyTeamConverter sOptyTeamConverter;

    public static final String ROLE_MEMBER_CODE = "70";

    public static final String ROLE_OWNER_CODE = "50";

    @Autowired
    private SOptyTeamDao sOptyTeamDao;

    @Autowired
    private IOpportunityRedundancyService opportunityRedundancyService;




    public SOptyTeamBO teamUtil(List<OptyEmployeeVO> employeeVOS,OpportunityDetail entity, String roleCode) {

        String code = ROLE_OWNER_CODE.equals(roleCode)?entity.getBusinessManagerId():entity.getDirectorOfPsc();

        if(CollectionUtils.isEmpty(employeeVOS)) {
             employeeVOS = sOptyTeamConverter.convertOptyEmployeeVo(Arrays.asList(code));
        }

        SOptyTeamBO sOptyTeamBO = new SOptyTeamVO();
        List<OptionBO> roleList = RadioFieldConverterUtils.converterRadioField(ConvertConstants.SoptyTeamConvertConstants.ROLE,
                roleCode);
        sOptyTeamBO.setPId(entity.getRowId());
        sOptyTeamBO.setRole(roleList);
        sOptyTeamBO.setRoleExt(Collections.singletonList(roleCode));
        sOptyTeamBO.setEmployee(employeeVOS);
        sOptyTeamBO.setEmployeeExt(Collections.singletonList(code));
        sOptyTeamBO.setCoreInfoFlag(RadioFieldConverterUtils.converterRadioField(
                ConvertConstants.SoptyTeamConvertConstants.CORE_INFO_FLAG, UniversalUpdateConstant.Y));
        sOptyTeamBO.setLastModifiedBy(entity.getLastUpdBy());
        sOptyTeamBO.setLastModifiedTime(entity.getLastUpd());
        sOptyTeamBO.setCreateBy(entity.getCreatedBy());
        sOptyTeamBO.setCreateTime(entity.getCreated());
        sOptyTeamBO.setEmployeeType(ROLE_OWNER_CODE.equals(roleCode)?OpportunityConstant.EMPLOYEE_OWNER:OpportunityConstant.EMPLOYEE_DESC);
        return sOptyTeamBO;
    }
    public SOptyTeamBO teamUtil(List<OptyEmployeeVO> employeeVOS,Opportunity entity, String roleCode, String employeeType) {
        if(CollectionUtils.isEmpty(employeeVOS)) {
            employeeVOS = sOptyTeamConverter.convertOptyEmployeeVo(Arrays.asList(entity.getCreatedBy()));
        }

        SOptyTeamBO sOptyTeamBO = new SOptyTeamVO();
        List<OptionBO> roleList = RadioFieldConverterUtils.converterRadioField(ConvertConstants.SoptyTeamConvertConstants.ROLE,
                roleCode);
        sOptyTeamBO.setPId(entity.getRowId());
        sOptyTeamBO.setRole(roleList);
        sOptyTeamBO.setEmployee(employeeVOS);
        sOptyTeamBO.setEmployeeExt(employeeVOS.stream().map(OptyEmployeeVO::getEmpUIID).collect(Collectors.toList()));
        sOptyTeamBO.setCoreInfoFlag(RadioFieldConverterUtils.converterRadioField(
                ConvertConstants.SoptyTeamConvertConstants.CORE_INFO_FLAG, UniversalUpdateConstant.Y));
        sOptyTeamBO.setLastModifiedBy(entity.getLastUpdBy());
        sOptyTeamBO.setLastModifiedTime(entity.getLastUpd());
        sOptyTeamBO.setCreateBy(entity.getCreatedBy());
        sOptyTeamBO.setCreateTime(entity.getCreated());
        sOptyTeamBO.setEmployeeType(employeeType);
        return sOptyTeamBO;
    }

    /**
     * 团队新增或者修改方法
     * @return
     */
    public int saveOrUpdate(SOptyTeamBO sOptyTeamBO){
       List<SOptyTeamBO> sOptyTeamBOList = sOptyTeamDao.selectList(Wrappers.lambdaQuery(SOptyTeamBO.class).eq(SOptyTeamBO::getPId,sOptyTeamBO.getPId())
                .eq(SOptyTeamBO::getEmployeeType,sOptyTeamBO.getEmployeeType()));
       int i;
       if(CollectionUtils.isEmpty(sOptyTeamBOList)){
           i = sOptyTeamDao.insert(sOptyTeamBO);
       }else {
           i = sOptyTeamDao.update(sOptyTeamBO, Wrappers.lambdaUpdate(SOptyTeamBO.class).eq(SOptyTeamBO::getPId,sOptyTeamBO.getPId())
                   .eq(SOptyTeamBO::getEmployeeType,sOptyTeamBO.getEmployeeType()));
       }
       SOptyRedundancyBO sOptyRedundancyBO = LcapConverterUtil.builtRedundancy(Collections.singletonList(sOptyTeamBO),
               Optional.empty(), sOptyTeamBO.getPId(),null);
       opportunityRedundancyService.saveOrUpdate(sOptyRedundancyBO, sOptyTeamBO.getPId());
       return i;
    }

}
