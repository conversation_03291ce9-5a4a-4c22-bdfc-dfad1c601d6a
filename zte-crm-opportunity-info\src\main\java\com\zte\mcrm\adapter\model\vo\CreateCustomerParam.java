package com.zte.mcrm.adapter.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 描述：创建客户草稿参数对象
 * 创建时间：2021/10/13
 *
 * @author：王丹凤**********
 */
@Data
public class CreateCustomerParam {

    @ApiModelProperty(value = "客户名称")
    private String accountName;

    @ApiModelProperty(value = "业务客户分类编码 （ZTE客户分类）")
    private String businessAccntTypeCode;

    @ApiModelProperty(value = "城市主编码")
    private String cityId;

    @ApiModelProperty(value = "国家码")
    private String countryCode;

    @ApiModelProperty(value = "国家主编码")
    private String countryId;

    @ApiModelProperty(value = "客户范围编码")
    private String custRangeCode;

    @ApiModelProperty(value = "财务类型")
    private String financeTypeCode;

    @ApiModelProperty(value = "电话号码")
    private String phoneNum;

    @ApiModelProperty(value = "客户子类型编码")
    private String subTypeCode;

    @ApiModelProperty(value = "是否子公司编码")
    private String subcompanyCode;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "是否非营销客户")
    private String isNoMktCode;

    @ApiModelProperty(value = "主管部门")
    private String buId;

    @ApiModelProperty(value = "所属部门")
    private List<OrganizationVo> orgList;

    /**
     * 法人ID
     */
    private  String corporateNo;

    @ToString
    @Setter
    @Getter
    public static class OrganizationVo {

        @ApiModelProperty(value = "部门名称")
        private String deptName;
        @ApiModelProperty(value = "部门编码")
        private String deptNo;
        @ApiModelProperty(value = "全称")
        private String fullname;
        @ApiModelProperty(value = "部门siebel编码")
        private String id;
        @ApiModelProperty(value = "是否为主管部门")
        private String isMainDept;
    }
}
