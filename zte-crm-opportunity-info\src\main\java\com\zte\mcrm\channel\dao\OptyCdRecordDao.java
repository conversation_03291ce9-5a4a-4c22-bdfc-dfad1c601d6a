package com.zte.mcrm.channel.dao;

import com.zte.mcrm.channel.model.dto.OptyCdRecordDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OptyCdRecordDao {
    /****
     * 查询一条记录**
     * @date 2019年4月25日
     * <AUTHOR>
     * @param id
     * @return
     */
    public OptyCdRecordDto get(@Param("id")String id);

    /****
     * 保存一条记录**
     * @date 2019年4月25日
     * <AUTHOR>
     * @param entity
     * @return
     */
    public int insert(OptyCdRecordDto entity);

    int insertByBatch(List<OptyCdRecordDto> list);

    /****
     * 更新一条记录
     * @param entity
     * @return
     */
    public int update(OptyCdRecordDto entity);

    /****
     * 获取同类型的最近一个评审单号
     * @param optyCd
     * @return
     */
    public String getLatelyOptyCd(String optyCd);

    /**
     * 功能描述:获取最近评审单号
     */
    String getLatelyOptyCdForUpdate(String optyCd);
    
}
