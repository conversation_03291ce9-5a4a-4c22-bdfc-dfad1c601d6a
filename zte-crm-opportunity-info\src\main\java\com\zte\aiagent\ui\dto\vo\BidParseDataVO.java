package com.zte.aiagent.ui.dto.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 解析数据存储视图对象
 * 对应PO: BidParseDataPO
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class BidParseDataVO {
    /**
     * 主键ID
     */
    private String rowId;

    /**
     * 解析记录ID，关联bid_parse_record.row_id
     */
    private String parseRecordId;

    /**
     * 数据类型: REQUEST-请求参数, RESPONSE-响应数据, DOCUMENT-文档内容
     */
    private String dataType;

    /**
     * 数据块索引(从0开始)，用于大内容分块存储
     */
    private Integer chunkIndex;

    /**
     * 总块数，标识当前数据被分成了多少块
     */
    private Integer chunkTotal;

    /**
     * 当前块大小(字节)
     */
    private Integer chunkSize;

    /**
     * 分块数据内容，存储实际的解析数据
     */
    private String dataContent;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人
     */
    private String lastUpdatedBy;

    /**
     * 最后更新时间
     */
    private Date lastUpdatedDate;

    /**
     * 有效标记(Y/N)
     */
    private String enabledFlag;

    /**
     * 租户ID
     */
    private Long tenantId;
} 