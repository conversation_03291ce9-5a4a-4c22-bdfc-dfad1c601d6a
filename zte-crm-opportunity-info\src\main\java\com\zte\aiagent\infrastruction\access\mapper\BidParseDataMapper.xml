<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.aiagent.infrastruction.access.mapper.BidParseDataMapper">
    <!-- 基础结果集映射，定义表字段与PO类属性的映射关系 -->
    <resultMap id="BaseResultMap" type="com.zte.aiagent.infrastruction.access.po.BidParseDataPO">
        <id column="row_id" property="rowId" jdbcType="VARCHAR"/>
        <result column="parse_record_id" property="parseRecordId" jdbcType="VARCHAR"/>
        <result column="data_type" property="dataType" jdbcType="VARCHAR"/>
        <result column="chunk_index" property="chunkIndex" jdbcType="INTEGER"/>
        <result column="chunk_total" property="chunkTotal" jdbcType="INTEGER"/>
        <result column="chunk_size" property="chunkSize" jdbcType="INTEGER"/>
        <result column="data_content" property="dataContent" jdbcType="VARCHAR"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_date" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR"/>
        <result column="last_updated_date" property="lastUpdatedDate" jdbcType="TIMESTAMP"/>
        <result column="enabled_flag" property="enabledFlag" jdbcType="VARCHAR"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 插入单条解析数据记录 -->
    <insert id="insert" parameterType="com.zte.aiagent.infrastruction.access.po.BidParseDataPO">
        INSERT INTO bid_parse_data (
            row_id,
            parse_record_id,
            data_type,
            chunk_index,
            chunk_total,
            chunk_size,
            data_content,
            created_by,
            created_date,
            last_updated_by,
            last_updated_date,
            enabled_flag,
            tenant_id
        ) VALUES (
                     #{rowId,jdbcType=VARCHAR},
                     #{parseRecordId,jdbcType=VARCHAR},
                     #{dataType,jdbcType=VARCHAR},
                     #{chunkIndex,jdbcType=INTEGER},
                     #{chunkTotal,jdbcType=INTEGER},
                     #{chunkSize,jdbcType=INTEGER},
                     #{dataContent,jdbcType=VARCHAR},
                     #{createdBy,jdbcType=VARCHAR},
                     #{createdDate,jdbcType=TIMESTAMP},
                     #{lastUpdatedBy,jdbcType=VARCHAR},
                     #{lastUpdatedDate,jdbcType=TIMESTAMP},
                     #{enabledFlag,jdbcType=VARCHAR},
                     #{tenantId,jdbcType=BIGINT}
                 )
    </insert>

    <!-- 批量插入解析数据记录，适用于大内容分块存储场景 -->
    <insert id="batchInsert" parameterType="java.util.Map">
        INSERT INTO bid_parse_data (
        row_id,
        parse_record_id,
        data_type,
        chunk_index,
        chunk_total,
        chunk_size,
        data_content,
        created_by,
        created_date,
        last_updated_by,
        last_updated_date,
        enabled_flag,
        tenant_id
        ) VALUES
        <foreach collection="items" item="item" separator=",">
            (
            #{item.rowId,jdbcType=VARCHAR},
            #{item.parseRecordId,jdbcType=VARCHAR},
            #{item.dataType,jdbcType=VARCHAR},
            #{item.chunkIndex,jdbcType=INTEGER},
            #{item.chunkTotal,jdbcType=INTEGER},
            #{item.chunkSize,jdbcType=INTEGER},
            #{item.dataContent,jdbcType=VARCHAR},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdDate,jdbcType=TIMESTAMP},
            #{item.lastUpdatedBy,jdbcType=VARCHAR},
            #{item.lastUpdatedDate,jdbcType=TIMESTAMP},
            #{item.enabledFlag,jdbcType=VARCHAR},
            #{item.tenantId,jdbcType=BIGINT}
            )
        </foreach>
    </insert>

    <!-- 根据ID查询解析数据 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
            row_id,
            parse_record_id,
            data_type,
            chunk_index,
            chunk_total,
            chunk_size,
            data_content,
            created_by,
            created_date,
            last_updated_by,
            last_updated_date,
            enabled_flag,
            tenant_id
        FROM bid_parse_data
        WHERE row_id = #{rowId,jdbcType=VARCHAR}
    </select>

    <!-- 根据解析记录ID和数据类型查询所有分块数据，按块索引升序排列 -->
    <select id="selectByRecordIdAndType" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
            row_id,
            parse_record_id,
            data_type,
            chunk_index,
            chunk_total,
            chunk_size,
            data_content,
            created_by,
            created_date,
            last_updated_by,
            last_updated_date,
            enabled_flag,
            tenant_id
        FROM bid_parse_data
        WHERE parse_record_id = #{parseRecordId,jdbcType=VARCHAR}
          AND data_type = #{dataType,jdbcType=VARCHAR}
          AND enabled_flag = 'Y'
        ORDER BY chunk_index ASC
    </select>

    <!-- 根据解析记录ID查询所有关联的解析数据 -->
    <select id="selectByParseRecordId" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
            row_id,
            parse_record_id,
            data_type,
            chunk_index,
            chunk_total,
            chunk_size,
            data_content,
            created_by,
            created_date,
            last_updated_by,
            last_updated_date,
            enabled_flag,
            tenant_id
        FROM bid_parse_data
        WHERE parse_record_id = #{parseRecordId,jdbcType=VARCHAR}
          AND enabled_flag = 'Y'
        ORDER BY data_type ASC, chunk_index ASC
    </select>

    <!-- 根据ID更新解析数据 -->
    <update id="updateByPrimaryKey" parameterType="com.zte.aiagent.infrastruction.access.po.BidParseDataPO">
        UPDATE bid_parse_data
        SET
            parse_record_id = #{parseRecordId,jdbcType=VARCHAR},
            data_type = #{dataType,jdbcType=VARCHAR},
            chunk_index = #{chunkIndex,jdbcType=INTEGER},
            chunk_total = #{chunkTotal,jdbcType=INTEGER},
            chunk_size = #{chunkSize,jdbcType=INTEGER},
            data_content = #{dataContent,jdbcType=VARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            created_date = #{createdDate,jdbcType=TIMESTAMP},
            last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            last_updated_date = #{lastUpdatedDate,jdbcType=TIMESTAMP},
            enabled_flag = #{enabledFlag,jdbcType=VARCHAR},
            tenant_id = #{tenantId,jdbcType=BIGINT}
        WHERE row_id = #{rowId,jdbcType=VARCHAR}
    </update>

    <!-- 根据ID删除解析数据 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        DELETE FROM bid_parse_data
        WHERE row_id = #{rowId,jdbcType=VARCHAR}
    </delete>

    <!-- 根据解析记录ID删除所有关联的解析数据 -->
    <delete id="deleteByParseRecordId" parameterType="java.lang.String">
        DELETE FROM bid_parse_data
        WHERE parse_record_id = #{parseRecordId,jdbcType=VARCHAR}
    </delete>

    <!-- 根据解析记录ID和数据类型删除关联数据 -->
    <delete id="deleteByRecordIdAndType" parameterType="java.util.Map">
        DELETE FROM bid_parse_data
        WHERE parse_record_id = #{parseRecordId,jdbcType=VARCHAR}
          AND data_type = #{dataType,jdbcType=VARCHAR}
    </delete>
</mapper>
