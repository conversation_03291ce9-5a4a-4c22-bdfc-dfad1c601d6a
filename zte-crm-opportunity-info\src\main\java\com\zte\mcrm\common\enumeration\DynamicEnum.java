/**
 *  Copyright  2018 赵世光 All rights reserved.
 *  @author: 6011000197 
 *  @date: 2018年7月2日 上午11:21:13 
 */
package com.zte.mcrm.common.enumeration;

import com.zte.springbootframe.util.local.LocalMessageUtils;

/**  
 * <p>Title: DynamicEnum</p>  
 * <p>Description: </p>  
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date 2018年7月2日  
 */
public enum DynamicEnum {
	/**
	 * 提供中文字段
	 */
	FROM(LocalMessageUtils.getMessage("oppertunity.from")),CHANGEINO(LocalMessageUtils.getMessage("oppertunity.change"));
	/**
	 * 补上注释
	 */
	private final String name;
	private DynamicEnum(String name){
		this.name = name;
	}
	public String getName(){
		return name;
	}

}
