package com.zte.aiagent.domain.repository;

import com.zte.aiagent.infrastruction.access.po.SysConfValuePO;
import java.util.Date;
import java.util.List;

/**
 * 系统参数配置值数据访问仓储接口
 * 负责系统参数配置值相关的数据访问操作
 *
 * <AUTHOR>
 */
public interface SysConfValueRepository {

    /**
     * 插入系统参数配置值记录
     * @param sysConfValue 系统参数配置值PO对象
     * @return 影响的行数
     */
    int insert(SysConfValuePO sysConfValue);

    /**
     * 批量插入系统参数配置值记录
     * @param items 系统参数配置值列表
     * @return 影响的行数
     */
    int batchInsert(List<SysConfValuePO> items);

    /**
     * 根据ID查询系统参数配置值
     * @param rowId 主键ID
     * @return 系统参数配置值PO对象
     */
    SysConfValuePO selectByPrimaryKey(String rowId);

    /**
     * 根据编码类型ID查询参数值列表
     * @param codeTypeId 编码类型ID
     * @param tenantId 租户ID
     * @return 系统参数配置值列表
     */
    List<SysConfValuePO> selectByCodeTypeId(String codeTypeId, Long tenantId);

    /**
     * 根据编码类型ID、代码和租户ID查询参数值
     * @param codeTypeId 编码类型ID
     * @param code 代码
     * @param tenantId 租户ID
     * @return 系统参数配置值PO对象
     */
    SysConfValuePO selectByTypeIdCodeAndTenant(String codeTypeId, String code, Long tenantId);

    /**
     * 根据父值ID查询子参数列表
     * @param parentId 父值ID
     * @param tenantId 租户ID
     * @return 系统参数配置值列表
     */
    List<SysConfValuePO> selectByParentId(String parentId, Long tenantId);

    /**
     * 根据ID更新系统参数配置值
     * @param sysConfValue 系统参数配置值PO对象
     * @return 影响的行数
     */
    int updateByPrimaryKey(SysConfValuePO sysConfValue);

    /**
     * 逻辑删除系统参数配置值（将enabled_flag设为N）
     * @param rowId 主键ID
     * @param lastUpdatedBy 最后更新人
     * @param lastUpdatedDate 最后更新时间
     * @return 影响的行数
     */
    int logicDelete(String rowId, String lastUpdatedBy, Date lastUpdatedDate);

    /**
     * 批量逻辑删除系统参数配置值
     * @param rowIds 主键ID列表
     * @param lastUpdatedBy 最后更新人
     * @param lastUpdatedDate 最后更新时间
     * @return 影响的行数
     */
    int batchLogicDelete(List<String> rowIds, String lastUpdatedBy, Date lastUpdatedDate);
}
