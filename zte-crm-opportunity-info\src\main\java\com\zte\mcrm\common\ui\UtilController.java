/**
 *  Copyright  2018 赵世光 All rights reserved.
 *  @author: 6011000197 
 *  @date: 2018年5月29日 上午9:50:58 
 */
package com.zte.mcrm.common.ui;
import com.zte.mcrm.common.business.IOftenSearchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.zte.mcrm.common.util.RowIdUtil;
import com.zte.springbootframe.common.model.ServiceDataCopy;
import com.zte.springbootframe.common.model.ServiceDataUtil;

/**  
 * <p>Title: UtilController</p>  
 * <p>Description: </p>  
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date 2018年5月29日  
 */

@RestController
@Api("工具相关API")
public class UtilController {

	@Autowired
	IOftenSearchService oftenSearchService;
	
	@ApiOperation("生成ROWID")
	@RequestMapping(value = "/util/rowId", method = RequestMethod.GET)
	public ServiceDataCopy<String> getProjectsByPage() throws Exception {
		return ServiceDataUtil.success(RowIdUtil.generateRowId());
	}

	@ApiOperation("失效最近查询记录")
	@RequestMapping(value = "/util/invalidOftenSearchByBizType", method = RequestMethod.GET)
	public ServiceDataCopy<Integer> invalidOftenSearchByBizType(
			@RequestParam(name = "bizType") String bizType
	) throws Exception {
		return ServiceDataUtil.success(oftenSearchService.invalidOftenSearchByBizType(bizType));
	}
}
