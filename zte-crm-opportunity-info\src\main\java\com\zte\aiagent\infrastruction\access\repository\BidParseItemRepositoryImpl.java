package com.zte.aiagent.infrastruction.access.repository;

import com.zte.aiagent.domain.repository.BidParseItemRepository;
import com.zte.aiagent.infrastruction.access.mapper.BidParseItemMapper;
import com.zte.aiagent.infrastruction.access.po.BidParseItemPO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 解析条目数据访问仓储实现类
 * 负责解析条目相关的数据访问操作
 *
 * <AUTHOR>
 */
@Service
public class BidParseItemRepositoryImpl implements BidParseItemRepository {

    @Autowired
    private BidParseItemMapper bidParseItemMapper;

    @Override
    public int insert(BidParseItemPO bidParseItem) {
        return bidParseItemMapper.insert(bidParseItem);
    }

    @Override
    public int batchInsert(List<BidParseItemPO> items) {
        return bidParseItemMapper.batchInsert(items);
    }

    @Override
    public BidParseItemPO selectByPrimaryKey(String rowId) {
        return bidParseItemMapper.selectByPrimaryKey(rowId);
    }

    @Override
    public List<BidParseItemPO> selectByParseRecordId(String parseRecordId) {
        return bidParseItemMapper.selectByParseRecordId(parseRecordId);
    }

    @Override
    public BidParseItemPO selectByRecordIdAndItemCode(String parseRecordId, String itemCode) {
        return bidParseItemMapper.selectByRecordIdAndItemCode(parseRecordId, itemCode);
    }

    @Override
    public int updateByPrimaryKey(BidParseItemPO bidParseItem) {
        return bidParseItemMapper.updateByPrimaryKey(bidParseItem);
    }

    @Override
    public int deleteByPrimaryKey(String rowId) {
        return bidParseItemMapper.deleteByPrimaryKey(rowId);
    }

    @Override
    public int deleteByParseRecordId(String parseRecordId) {
        return bidParseItemMapper.deleteByParseRecordId(parseRecordId);
    }
}
