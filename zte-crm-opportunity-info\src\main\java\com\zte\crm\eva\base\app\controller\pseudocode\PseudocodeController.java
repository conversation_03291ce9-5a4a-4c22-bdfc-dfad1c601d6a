package com.zte.crm.eva.base.app.controller.pseudocode;

import com.google.common.collect.Maps;
import com.zte.crm.eva.base.service.pseudocode.PseudocodeService;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.Map;

/**
 * spring mvc控制类
 * <AUTHOR>
 * @date 2022/11/14
 */
@Api(tags = "伪代码")
@RestController
@RequestMapping("/pseudocode")
public class PseudocodeController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PseudocodeController.class);

    @Autowired
    private PseudocodeService pseudocodeService;

    @ApiOperation("执行伪代码")
    @GetMapping(value = "/{module}/{ruleName}", produces = "application/json; charset=utf-8")
    public ServiceData<String> pseudocodeTask(@PathVariable(name = "module") String module,
                                              @PathVariable(name = "ruleName") String ruleName, HttpServletRequest request) {
        try {
            Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(16);
            Enumeration<String> pNames = request.getParameterNames();
            while (pNames.hasMoreElements()) {
                String name = pNames.nextElement();
                String value = request.getParameter(name);
                paramMap.put(name, value);
            }
            return ServiceResultUtil.success(pseudocodeService.pseudocodeTask(module, ruleName, paramMap));
        } catch (Exception e) {
            LOGGER.error("execute pseudocode failure, module: {}, reuleName: {}, error:{}", module, ruleName, e.getMessage(), e);
            throw new BusiException(RetCode.BUSINESSERROR_CODE, "execute pseudocode error");
        }
    }

}
