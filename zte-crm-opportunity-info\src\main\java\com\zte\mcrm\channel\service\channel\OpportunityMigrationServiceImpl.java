package com.zte.mcrm.channel.service.channel;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.mcrm.adapter.approval.model.ApprovalResponseBO;
import com.zte.mcrm.adapter.approval.model.FlowActiveTaskInfo;
import com.zte.mcrm.adapter.approval.model.dto.ApprovalActiveTaskParamsDTO;
import com.zte.mcrm.adapter.approval.model.dto.ApprovalRevokeParamsDTO;
import com.zte.mcrm.adapter.approval.service.ApprovalFlowService;
import com.zte.mcrm.adapter.approval.service.ApprovalHeaderBuilder;
import com.zte.mcrm.adapter.model.dto.AccountInfo;
import com.zte.mcrm.adapter.service.CustomerInfoService;
import com.zte.mcrm.channel.constant.*;
import com.zte.mcrm.channel.dao.OpportunityDao;
import com.zte.mcrm.channel.dao.OpportunityDetailDao;
import com.zte.mcrm.channel.dao.OpportunityMonthReportDao;
import com.zte.mcrm.channel.dao.OpportunityProductDao;
import com.zte.mcrm.channel.model.dto.HistApproveDataDTO;
import com.zte.mcrm.channel.model.dto.InsFlow;
import com.zte.mcrm.channel.model.dto.InsTask;
import com.zte.mcrm.channel.model.entity.*;
import com.zte.mcrm.channel.model.vo.OpportunityOpinionVO;
import com.zte.mcrm.channel.service.prm.IComApprovalRecordService;
import com.zte.mcrm.channel.util.ExceptionMsgUtils;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.model.PersonAndOrgInfoVO;
import com.zte.mcrm.common.model.SysGlobalConstVo;
import com.zte.mcrm.common.upload.dao.base.ComUploadFileDao;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.mcrm.common.util.EmployeeUtil;
import com.zte.mcrm.common.util.HttpClientUtil;
import com.zte.mcrm.opportunity.common.OppSysConst;
import com.zte.opty.common.enums.OptyStatusEnum;
import com.zte.opty.dao.SOpportunityRepository;
import com.zte.opty.sync.util.LcapConverterUtil;
import com.zte.springbootframe.common.exception.BusiException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.ImmutableList;
import org.apache.http.entity.mime.content.ByteArrayBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.OutputStream;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OpportunityMigrationServiceImpl implements IOpportunityMigrationService {

    private static final Logger log = LoggerFactory.getLogger(OpportunityMigrationServiceImpl.class);
    @Autowired
    OpportunityDao opportunityDao ;
    @Autowired
    private SOpportunityRepository sOpportunityRepository ;
    @Autowired
    IOpportunityService opportunityService ;
    @Autowired
    OpportunityDetailDao opportunityDetailDao ;
    @Autowired
    IOpportunityDetailService opportunityDetailService ;
    @Autowired
    ComUploadFileDao comUploadFileDao ;
    @Autowired
    OpportunityProductDao opportunityProductDao ;
    @Autowired
    IOpportunityProductService opportunityProductService;
    @Autowired
    private CustomerInfoService customerInfoService;
    @Autowired
    private ApprovalInfoService approvalInfoService;
    @Autowired
    private ApprovalFlowService approvalFlowService;
    @Autowired
    IOpportunityInfoService opportunityInfoService;
    @Autowired
    OpportunityMonthReportDao opportunityMonthReportDao;
    @Autowired
    IComApprovalRecordService comApprovalRecordService;

    public static String RESULT = "成功条数：";
    private static final int BATCH_SIZE = 500;
    private static final int CUSTOMER_BATCH_SIZE = 100;
    private static final int MIGRATION_BATCH_SIZE = 1000;
    private final static String INS_FLOW = "ins_flow";
    private final static String INS_TASK = "ins_task";
    private final static String INS_REASSIGN = "ins_reassign";
    private final static String HIST_FLOW = "hist_flow";
    private final static String OLD_OPP = "old";
    private final static String NEW_OPP = "new";


    ThreadFactory nameFactory = new ThreadFactoryBuilder().setNameFormat("update-businessManager-%d").build();
    ThreadPoolExecutor executor = new ThreadPoolExecutor(4, 10, 10,
            TimeUnit.SECONDS, new ArrayBlockingQueue<>(1200), nameFactory,
            new ThreadPoolExecutor.DiscardOldestPolicy());

    @Override
    public List<String> refreshCrmCustomerCode(String type) throws BusiException {
        List<String> resultList = new ArrayList<>();
        List<OpportunityDetail> opportunityDetailList = opportunityDetailDao.getNoCrmCustomerCodeList(type);
        resultList.add("size:" + opportunityDetailList.size());
        log.info("getNoCrmCustomerCodeList.size:{}", opportunityDetailList.size());
        if (OLD_OPP.equals(type)) {
            List<String> customerIds = opportunityDetailList.stream()
                    .map(OpportunityDetail::getSecondDealerId)
                    .filter(Objects::nonNull)
                    .distinct().collect(Collectors.toList());
            List<AccountInfo> accountInfos = customerInfoService.getCustomerInformationBatch(customerIds);
            if (CollectionUtils.isEmpty(accountInfos)) {
                return resultList;
            }
            log.info("getCustomerInformationBatch.size:{}", accountInfos.size());
            Map<String, AccountInfo> accountInfoMap = accountInfos.stream().collect(Collectors.toMap(AccountInfo::getId, Function.identity()));
            Map<String, AccountInfo> accountInfoNumMap = accountInfos.stream().collect(Collectors.toMap(AccountInfo::getAccountNum, Function.identity()));
            accountInfoMap.putAll(accountInfoNumMap);
            for (OpportunityDetail opportunityDetail : opportunityDetailList) {
                AccountInfo accountInfo = accountInfoMap.getOrDefault(opportunityDetail.getSecondDealerId(), new AccountInfo());
                opportunityDetail.setCrmCustomerCode(accountInfo.getAccountNum());
                opportunityDetail.setCustomerName(accountInfo.getAccountName());
                //opportunityDetail.setAgencyRestrictionFlag(accountInfo.getRestrictedPartyCode());
                //TODO 数据迁移使用
//                opportunityDetailDao.update(opportunityDetail);
            }
        }else if (NEW_OPP.equals(type)){
            for (OpportunityDetail opportunityDetail : opportunityDetailList) {
                try {
                    List<AccountInfo> accountInfos = customerInfoService.getCustomerInformationByNameV2(opportunityDetail.getDeptNo(),opportunityDetail.getCustomerName());
                    OpportunityDetail opportunityDetailTemp = new OpportunityDetail();
                    opportunityDetailTemp.setRowId(opportunityDetail.getRowId());
                    if (CollectionUtils.isEmpty(accountInfos)){
                        String exceptionDetail = "rowId:" + opportunityDetail.getRowId() + ";customerName:"+opportunityDetail.getCustomerName() + "; is not existed";
                        resultList.add(exceptionDetail);
                    }else {
                        accountInfos.stream().filter(a -> StringUtils.equals(a.getAccountName(), opportunityDetail.getCustomerName()))
                                .filter(a -> AccountStatusEnum.ACCT_ACTIVE_STATUS.getValue().equals(a.getActiveStatusCode()))
                                .filter(a -> OpportunityConstant.CUSTOMER_COUNTRY_ID_CHINA.equals(a.getCountryId()))
                                .findFirst().ifPresent(customerInfo -> {
                            opportunityDetailTemp.setCrmCustomerCode(customerInfo.getAccountNum());

                            //TODO 数据迁移使用
//                            opportunityDetailDao.update(opportunityDetailTemp);
                        });
                    }
                }catch (Exception e){
                    log.error("updateCrmCustomerCodeByOldOpp Exception", e);
                    String exceptionDetail = "rowId:" + opportunityDetail.getRowId() + ",Error:" + ExceptionMsgUtils.getStackTrace(e, 300);
                    resultList.add(exceptionDetail);
                }
            }
        }

        return resultList;
    }

    @Override
    public Integer updateLastAccNameByOldOpp() throws BusiException {
        int result = 0;
        List<OpportunityDetail> noLastAccNameList = opportunityDetailDao.getNoLastAccNameList();
        log.info("getNoCrmCustomerCodeList.size:{}", noLastAccNameList.size());
        List<String> lastAccIds = noLastAccNameList.stream()
                .map(OpportunityDetail::getLastAccId)
                .filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
        List<AccountInfo> lastAccInfos = getCustomerInformationByBatch(lastAccIds);
        if (CollectionUtils.isEmpty(lastAccInfos)){
            return result;
        }
        log.info("getCustomerInformationBatch.size:{}", lastAccInfos.size());
        Map<String, AccountInfo> accountInfoMap = lastAccInfos.stream().collect(Collectors.toMap(AccountInfo::getId, Function.identity()));
        Map<String, AccountInfo> accountInfoNumMap = lastAccInfos.stream().collect(Collectors.toMap(AccountInfo::getAccountNum, Function.identity()));
        accountInfoMap.putAll(accountInfoNumMap);
        for (OpportunityDetail opportunityDetail : noLastAccNameList) {
            AccountInfo accountInfo = accountInfoMap.getOrDefault(opportunityDetail.getLastAccId(), new AccountInfo());
            opportunityDetail.setLastAccName(accountInfo.getAccountName());
            opportunityDetail.setReservedField1("2");
            //opportunityDetail.setFinalCustomerRestrictionFlag(accountInfo.getRestrictedPartyCode());

//            result += opportunityDetailDao.update(opportunityDetail);
        }
        return result;
    }

    private List<AccountInfo> getCustomerInformationByBatch(List<String> lastAccIds) throws BusiException {
        List<AccountInfo> lastAccInfos = new ArrayList<>();
        for (int i = 0; i < lastAccIds.size(); i+=CUSTOMER_BATCH_SIZE){
            List<String> lastAccIdsBatch = lastAccIds.subList(i, Math.min(i+CUSTOMER_BATCH_SIZE, lastAccIds.size()));
            List<AccountInfo> accountInfosBatch = customerInfoService.getCustomerInformationBatch(lastAccIdsBatch);
            lastAccInfos.addAll(accountInfosBatch);
        }
        return lastAccInfos;
    }

    private void deleteOpportunityByBatch(List<String> dataMigrationOptyId, boolean isSoft){
        int sumCountDetail = 0;
        int sumCountOpty = 0;
        int sumCountProd = 0;
        int sumCountFile = 0;
        for (int i = 0; i < dataMigrationOptyId.size(); i+=BATCH_SIZE)
        {
            List<String> opportunityIdsBatch = dataMigrationOptyId.subList(i, Math.min(i+BATCH_SIZE, dataMigrationOptyId.size()));
            if (isSoft){
                int countDetail = opportunityDetailDao.softDeleteByOptyIds(opportunityIdsBatch);
                sumCountDetail += countDetail;

                int countOpty = opportunityDao.softDeleteByOptyIds(opportunityIdsBatch);
                sumCountOpty += countOpty;

                int countProd = opportunityProductService.batchSoftDelete(opportunityIdsBatch);
                sumCountProd += countProd;

                int countFile = comUploadFileDao.softDeleteByOpptyId(opportunityIdsBatch);
                sumCountFile += countFile;
            }else {
                int countDetail = opportunityDetailDao.deleteByOptyIds(opportunityIdsBatch);
                sumCountDetail += countDetail;

                int countOpty = opportunityDao.deleteByOptyIds(opportunityIdsBatch);
                sumCountOpty += countOpty;

                int countProd = opportunityProductDao.deleteByOpptyId(opportunityIdsBatch);
                sumCountProd += countProd;

                int countFile = comUploadFileDao.deleteByOpptyId(opportunityIdsBatch);
                sumCountFile += countFile;
            }
        }
        log.info("删除/失效商机详情表条数:{},删除商机主表条数:{},删除商机产品表条数:{},删除商机附件表条数:{}",
                sumCountDetail,sumCountOpty,sumCountProd,sumCountFile);
    }

    /**
     * 根据流水号从审批中心获取导入失败数据
     * @param serialNumber
     * @param type
     * @return
     */
    @Override
    public ByteArrayBody getFailLogAndRollBack(String serialNumber, String type) {
        ApprovalHeaderBuilder approvalHeaderBuilder =
                (ApprovalHeaderBuilder) SpringContextUtil.getBean("ApprovalHeaderBuilder");
        String approvalPreFixPath = approvalHeaderBuilder.getPrefixpath();
        String url = approvalPreFixPath + "/approval/flow/excel/export/fail-logs";
        Map<String, String> headerParams = new HashMap<>(16);
        headerParams = approvalHeaderBuilder.getHeaderParams(headerParams);
        Map<String, String> params = new HashMap<>(1);
        params.put("serialNumber", serialNumber);
        ByteArrayBody res = HttpClientUtil.httpGetFile(url, params, headerParams, null);
        if(null == res) {
            return res;
        }
        Assert.notNull(res, "res.is.null");
        File file = new File(res.getFilename());
        try {
            OutputStream out = FileUtils.openOutputStream(file, false);
            res.writeTo(out);
            if (INS_FLOW.equals(type)) {
                ExcelReaderBuilder excelReaderBuilder = EasyExcelFactory.read(file, InsFlow.class, null);
                List<InsFlow> rollBackInsFlows = excelReaderBuilder.head(InsFlow.class).sheet().doReadSync();
                rollBackByFlow(rollBackInsFlows);
            } else if (INS_TASK.equals(type)) {
                ExcelReaderBuilder excelReaderBuilder = EasyExcelFactory.read(file, InsTask.class, null);
                List<InsTask> rollBackInsTasks = excelReaderBuilder.head(InsTask.class).sheet().doReadSync();
                rollBackByTask(rollBackInsTasks);
            }
        } catch (Exception e) {
            log.error("rollBackFail,Exception", e);
        } finally {
            FileUtils.deleteQuietly(file);
        }
        return res;
    }

    /**
     * 回滚任务
     * @param rollBackInsTask
     */
    @Override
    public void rollBackByTask(List<InsTask> rollBackInsTask){
        for (InsTask insTask : rollBackInsTask) {
            String taskId = insTask.getTaskId();
            String rowId = taskId.substring(taskId.indexOf("_") + 1, taskId.lastIndexOf("_"));
            log.info("rollBack,taskid:{},rowId:{}", taskId, rowId);
            comApprovalRecordService.softDeleteByBusinessIdAndFlowId(rowId, insTask.getInsFlowId());
        }
    }

    /**
     * 回滚流程
     * @param rollBackInsFlow
     */
    @Override
    public void rollBackByFlow(List<InsFlow> rollBackInsFlow){
        for (InsFlow insFlow : rollBackInsFlow) {
            comApprovalRecordService.softDeleteByBusinessIdAndFlowId(insFlow.getBusinessId(), insFlow.getInsFlowId());
        }
    }


    private void doGenerateApprovalCenterImportData(HistApproveDataDTO histApproveDataDTO, List<InsFlow> insFlowListSafe, List<InsTask> insTaskListSafe, List<HistApproveDataDTO> histApproveDataSafeList){
        Opportunity opportunity = opportunityService.getByOptyCd(histApproveDataDTO.getOptyCd());

        List<String> inTransitOpportunityStatusList = ImmutableList.of(OptyStatusEnum.DRAFT.getCode(), OptyStatusEnum.APPROVING.getCode(),
                                                                       OptyStatusEnum.OPTY_SUSPEND.getCode());

        if (null == opportunity) {
            // 商机不存在
            histApproveDataDTO.setNotImportResult("opportunity not existed");
            histApproveDataSafeList.add(histApproveDataDTO);
            return;
        }
        if(!SourceOfOpportunityEnum.CHANNEL_FILING.getValue().equals(opportunity.getDataSource())){
            // 商机来源不为渠道报备
            histApproveDataDTO.setNotImportResult("opportunity dataSource is not iChannel");
            histApproveDataSafeList.add(histApproveDataDTO);
            return;
        }
        if (inTransitOpportunityStatusList.contains(opportunity.getStatusCd())) {
            // 商机状态为在途状态
            histApproveDataDTO.setNotImportResult("Existing opportunity status is " + opportunity.getStatusCd()
                    + ", please pay attention to the identification before processing");
            histApproveDataSafeList.add(histApproveDataDTO);
            return;
        }
        if (StringUtils.isBlank(histApproveDataDTO.getApproveResult())) {
            // 审批结论为空
            histApproveDataDTO.setNotImportResult("The business opportunity approval opinion is empty or transferred, and the data does not need to be migrated");
            histApproveDataSafeList.add(histApproveDataDTO);
            return;
        }

        ComApprovalRecord comApprovalRecord = comApprovalRecordService.getByBusinessId(opportunity.getRowId());
        if (null != comApprovalRecord){
            if(!comApprovalRecord.getWorkFlowInstanceId().startsWith(HIST_FLOW)) {
                // 存在在途审批流程
                histApproveDataDTO.setNotImportResult("There is an in-transit approval process");
                histApproveDataSafeList.add(histApproveDataDTO);
                return;
            }
            comApprovalRecordService.softDeleteByBusinessIdAndFlowId(comApprovalRecord.getBusinessId(), comApprovalRecord.getWorkFlowInstanceId());
        }

        InsFlow insFlow = getInsFlow(histApproveDataDTO, opportunity);
        insFlowListSafe.add(insFlow);
        InsTask insTask = getInsTask(histApproveDataDTO, insFlow, opportunity);
        insTaskListSafe.add(insTask);
        // 插入审批流程记录表
        insertToRecord(insFlow);
    }

    private void insertToRecord(InsFlow insFlow){
        ComApprovalRecord comApprovalRecordNew = new ComApprovalRecord();
        comApprovalRecordNew.setWorkFlowInstanceId(insFlow.getInsFlowId());
        comApprovalRecordNew.setBusinessType("newOpportunity");
        comApprovalRecordNew.setBusinessId(insFlow.getBusinessId());
        comApprovalRecordNew.setLastUpdatedDate(insFlow.getLastUpdateDate());
        comApprovalRecordNew.setCreatedDate(insFlow.getCreateDate());
        comApprovalRecordNew.setCreatedBy(insFlow.getCreatedBy());
        comApprovalRecordNew.setLastUpdatedBy(insFlow.getLastUpdateBy());
        comApprovalRecordNew.setEnabledFlag(CommonConst.Y);
        comApprovalRecordService.insertAny(comApprovalRecordNew);
    }

    private InsFlow getInsFlow(HistApproveDataDTO histApproveDataDTO, Opportunity opportunity){
        InsFlow insFlow = new InsFlow();
        insFlow.setAppCode(OpportunityConstant.NEW_OPPORTUNITY_APP_CODE);
        insFlow.setBusinessId(opportunity.getRowId());
        insFlow.setCreateDate(opportunity.getCreated());
        insFlow.setCreatedBy(opportunity.getCreatedBy());
        insFlow.setEnableFlag(CommonConst.Y);
        insFlow.setFlowCode(OpportunityConstant.NEW_OPPORTUNITY_FLOW_CODE);
        insFlow.setInsFlowId(getHistUuid());
        insFlow.setLastUpdateBy(histApproveDataDTO.getApprover());
        insFlow.setLastUpdateDate(histApproveDataDTO.getApproveDate());
        insFlow.setProcessInstanceId(insFlow.getInsFlowId());
        //这个字段存在疑问
        insFlow.setStatusCode(ApprovalStatusEnum.COMPLETE.getDescEn());
        insFlow.setTenantId("10001");
        return insFlow;
    }

    private InsTask getInsTask(HistApproveDataDTO histApproveDataDTO, InsFlow insFlow, Opportunity opportunity){
        InsTask insTask = new InsTask();

        insTask.setAppCode(OpportunityConstant.NEW_OPPORTUNITY_APP_CODE);
        insTask.setApprover(histApproveDataDTO.getApprover());
        insTask.setCreateDate(opportunity.getCreated());
        insTask.setCreatedBy(opportunity.getCreatedBy());
        insTask.setEnableFlag("Y");
        insTask.setInsFlowId(insFlow.getInsFlowId());
        insTask.setLastUpdateBy(histApproveDataDTO.getApprover());
        insTask.setLastUpdateDate(histApproveDataDTO.getApproveDate());
        insTask.setOpinionContent(histApproveDataDTO.getOpinion());
        OpportunityOpinionVO opportunityOpinionVO = new OpportunityOpinionVO();
        opportunityOpinionVO.setFailureReason(ArbitrationTypeEnum.getArbitrationTypeCodeByName(histApproveDataDTO.getArbitrationType()));
        insTask.setExtOpinion(JSON.toJSONString(opportunityOpinionVO));
        insTask.setOpinionResult(histApproveDataDTO.getApproveResult());
        //任务状态码存在疑问
        insTask.setStatusCode(ApprovalNodeStatusEnum.COMPLETED.getDescEn());
        insTask.setTaskId("taskid_"+opportunity.getRowId()+"_"+histApproveDataDTO.getOptyCd());
        insTask.setTenantId("10001");
        insTask.setEngineTaskDefinitionKey("Activity_0086ddc");
        return insTask;
    }

    private static String getHistUuid(){
        String prefix = HIST_FLOW;
        UUID uuid = UUID.randomUUID();
        return prefix +uuid;
    }

    @Override
    public ServiceData<ApprovalResponseBO> sendApprovalCenterExcel(MultipartFile excelFile, String type) {
        if (StringUtils.isBlank(type) || null == excelFile) {
            return null;
        }
        ApprovalHeaderBuilder approvalHeaderBuilder =
                (ApprovalHeaderBuilder) SpringContextUtil.getBean("ApprovalHeaderBuilder");
        Map<String, String> headerParams = new HashMap<>(16);
        String approvalPreFixPath = approvalHeaderBuilder.getPrefixpath();
        headerParams = approvalHeaderBuilder.getHeaderParams(headerParams);
        String result = "";
        log.info("request approval center, type=" + type);
        if (INS_FLOW.equalsIgnoreCase(type)) {
            result = HttpClientUtil.httpPostWithFile(approvalPreFixPath + "/approval/flow/excel/import/flow-instance", excelFile, headerParams);
        } else if (INS_TASK.equalsIgnoreCase(type)) {
            result = HttpClientUtil.httpPostWithFile(approvalPreFixPath + "/approval/flow/excel/import/task", excelFile, headerParams);
        } else if (INS_REASSIGN.equalsIgnoreCase(type)) {
            result = HttpClientUtil.httpPostWithFile(approvalPreFixPath + "/approval/flow/excel/import/reassign", excelFile, headerParams);
        }
        ServiceData<ApprovalResponseBO> serviceData = JSON.parseObject(result, new TypeReference<ServiceData<ApprovalResponseBO>>() {});
        log.info("=============发送给审批中心的,type={},结果是：{}=============",  type, JSON.toJSONString(serviceData));
        if (serviceData.getCode().getCode().equals(RetCode.SUCCESS_CODE) && !ObjectUtils.isEmpty(serviceData.getBo())) {
            ApprovalResponseBO bo = JSON.parseObject(JSON.toJSONString(serviceData.getBo()), ApprovalResponseBO.class);
            bo.setExcelPath(excelFile.getOriginalFilename());
            bo.setType(type);
            serviceData.setBo(bo);
        }
        return serviceData;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_UNCOMMITTED, rollbackFor = Exception.class)
    public String invalid(List<String> rowIds) {
        log.info("迁移回滚商机总条目：{}", rowIds);
        if(CollectionUtils.isNotEmpty(rowIds)) {
            deleteOpportunityByBatch(rowIds, false);
        }
        return RESULT + rowIds.size();
    }

    /**
     * 更新报备成功时间
     * @return
     * @throws BusiException
     */
    @Override
    public List<String> updateApproveDate(String type, int limit) {
        List<String> typeList = Arrays.asList(OpportunityConstant.SUCCESS_DATE, OpportunityConstant.SUBMIT_DATE);
        List<String> resultList = new ArrayList<>();
        if (!typeList.contains(type)){
            resultList.add("type error!");
            return resultList;
        }
        List<OpportunityKeyInfoEntity> opportunities = opportunityMonthReportDao.getReNewStatusOptys(type, limit);
        String result = "sumCount=" + opportunities.size();
        resultList.add(result);
        for (int i = 0; i < opportunities.size(); i+=CUSTOMER_BATCH_SIZE){
            List<OpportunityKeyInfoEntity> opportunitiesBatch = opportunities.subList(i, Math.min(i+CUSTOMER_BATCH_SIZE, opportunities.size()));
            List<String> flowInstances = opportunitiesBatch.stream().map(OpportunityKeyInfoEntity::getFlowInstanceId)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            Map<String, Date> approveDateMap = approvalInfoService.getApproveDateMap(flowInstances, type);
            for (OpportunityKeyInfoEntity vo : opportunitiesBatch) {
                Opportunity opportunity = new Opportunity();
                opportunity.setRowId(vo.getRowId());
                Date approveDate = approveDateMap.getOrDefault(vo.getFlowInstanceId(), null);
                if (null == approveDate){
                    resultList.add(vo.getOptyCd() + "," + vo.getFlowInstanceId());
                    continue;
                }
                if (OpportunityConstant.SUCCESS_DATE.equals(type)) {
                    opportunity.setSuccessDate(approveDate);
                }else if(OpportunityConstant.SUBMIT_DATE.equals(type)){
                    opportunity.setSubmitDate(approveDate);
                }
                sOpportunityRepository.updateById(LcapConverterUtil.buildSopty(opportunity, null));
            }
        }
        return resultList;
    }

    /**
     * 更新中兴业务经理
     *
     * @return
     */
    @Override
    public String updateBusinessManager() throws BusiException {
        List<OpportunityMigrationEntity> opportunityMigrationEntities = new ArrayList<>();
        String result = "sumCount=" + opportunityMigrationEntities.size();
        for (int i = 0; i < opportunityMigrationEntities.size(); i+=MIGRATION_BATCH_SIZE){
            List<OpportunityMigrationEntity> opportunityMigrationEntityBatch = opportunityMigrationEntities.subList(i,
                    Math.min(i + MIGRATION_BATCH_SIZE, opportunityMigrationEntities.size()));
            updateBusinessManagerBatch(opportunityMigrationEntityBatch);
        }
        return result;
    }

    private void updateBusinessManagerBatch(List<OpportunityMigrationEntity> opportunityMigrationEntityBatch) throws BusiException {
        Set<String> prmBusinessManagerIds = opportunityMigrationEntityBatch.stream().map(OpportunityMigrationEntity::getPrmBusinessManagerId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        List<PersonAndOrgInfoVO> prmPersonInfos = EmployeeUtil.getEmployeesByEmpNos(prmBusinessManagerIds);
        // 获取prm中兴业务经理(为电脑号，需转换为短工号并查询姓名)
        Set<String> createdByOrOwnerIds = opportunityMigrationEntityBatch.stream().map(OpportunityMigrationEntity::getOpportunityOwnerId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        createdByOrOwnerIds.addAll(opportunityMigrationEntityBatch.stream().map(OpportunityMigrationEntity::getCreatedBy).filter(createdBy -> !StringUtils.equalsIgnoreCase(createdBy, OppSysConst.SADMIN))
                .filter(createdBy -> !StringUtils.startsWithIgnoreCase(createdBy,"A")).collect(Collectors.toSet()));
        // 获取商机负责人和创建人(短工号，需要查询姓名)
        List<PersonAndOrgInfoVO> createdByOrOwnerPersonInfos = EmployeeUtil.getEmployeesByEmpNos(createdByOrOwnerIds);

        Map<String, PersonAndOrgInfoVO> personInfoMap = prmPersonInfos.stream()
                .filter(p -> StringUtils.isNotBlank(p.getEmpNO()) && StringUtils.isNotBlank(p.getComputerNum()))
                .collect(Collectors.toMap(PersonAndOrgInfoVO::getComputerNum, Function.identity()));
        personInfoMap.putAll(createdByOrOwnerPersonInfos.stream()
                .filter(p -> StringUtils.isNotBlank(p.getEmpNO()) && StringUtils.isNotBlank(p.getComputerNum()))
                .collect(Collectors.toMap(PersonAndOrgInfoVO::getEmpNO, Function.identity())));

        CompletableFuture.allOf(opportunityMigrationEntityBatch.stream().map(
                migrationEntityBatch -> CompletableFuture.runAsync(() -> {
                    OpportunityDetail opportunityDetail = new OpportunityDetail();
                    opportunityDetail.setRowId(migrationEntityBatch.getRowId());
                    PersonAndOrgInfoVO businessMangerInfo = getBusinessMangerInfo(migrationEntityBatch, personInfoMap);
                    log.info("businessMangerInfo of {} is {}", migrationEntityBatch.getRowId(), JSON.toJSONString(businessMangerInfo));
                    if (null != businessMangerInfo) {
                        opportunityDetail.setBusinessManagerId(businessMangerInfo.getEmpNO());
                        opportunityDetail.setBusinessManagerName(businessMangerInfo.getEmpName());
                        opportunityDetail.setReservedField1("businessManager");
                        opportunityDetailService.updateWithNoDate(opportunityDetail, false);
                    }
                }, executor)).toArray(CompletableFuture[]::new)).join();
    }

    private PersonAndOrgInfoVO getBusinessMangerInfo(OpportunityMigrationEntity migrationEntityBatch, Map<String, PersonAndOrgInfoVO> personInfoMap){
        PersonAndOrgInfoVO result = null;
        if (null != personInfoMap.get(migrationEntityBatch.getPrmBusinessManagerId())){
            result = personInfoMap.get(migrationEntityBatch.getPrmBusinessManagerId());
        }else if (null != personInfoMap.get(migrationEntityBatch.getOpportunityOwnerId())){
            result = personInfoMap.get(migrationEntityBatch.getOpportunityOwnerId());
        }else if (null != personInfoMap.get(migrationEntityBatch.getCreatedBy())){
            result = personInfoMap.get(migrationEntityBatch.getCreatedBy());
        }
        return result;
    }

    @Override
    public List<OpportunityKeyInfoEntity> adaptToTheNewProcess() throws Exception {
        List<OpportunityKeyInfoEntity> opportunityKeyInfoEntities =  opportunityMonthReportDao.getOldAccDraftProcessOptys();
        for (OpportunityKeyInfoEntity entity : opportunityKeyInfoEntities) {
            ApprovalActiveTaskParamsDTO paramsDTO = new ApprovalActiveTaskParamsDTO();
            paramsDTO.setPageNo(1);
            paramsDTO.setPageSize(10);
            paramsDTO.setInsFlowIds(Arrays.asList(entity.getFlowInstanceId()));
            PageRows<FlowActiveTaskInfo> flowActiveTaskInfoPageRows = approvalFlowService.getApprovingTaskList(paramsDTO);
            List<FlowActiveTaskInfo> flowActiveTaskInfos = flowActiveTaskInfoPageRows.getRows();
            if (flowActiveTaskInfoPageRows.getTotal() > 0 && CollectionUtils.isNotEmpty(flowActiveTaskInfos)){
                if(flowActiveTaskInfos.stream().map(FlowActiveTaskInfo::getNodeName).anyMatch(OpportunityConstant.CUSTOMER_DRAFT_CREATED::equals)){
                    createCustomerAndStartFlow(entity);
                }else{
                    // 不存在创建客户草稿节点
                    entity.setStatus("nodeDoesNotExist");
                }
            }else{
                // 没有待办节点（流程已撤销或已关闭）
                entity.setStatus("approvalCenterHasNoPending");
            }
        }
        return opportunityKeyInfoEntities;
    }

    @Override
    public void createCustomerAndStartFlow(OpportunityKeyInfoEntity entity) throws Exception {
        AccountInfo accountInfo = opportunityInfoService.queryActivatedLastAccByName(entity.getLastAccName());
        if (null != accountInfo) {
                startFlowAndUpdateStatus(entity, accountInfo);
        }else {
            if (StringUtils.isNotBlank(entity.getBusinessManagerId())){
                createDraftAndRevoke(entity);
            }else{
                entity.setStatus("noBusinessManagerId");
            }
        }
    }

    private void startFlowAndUpdateStatus(OpportunityKeyInfoEntity entity, AccountInfo accountInfo){
        //最终用户ID存在,更新数据库
        OpportunityDetail opportunityDetail = new OpportunityDetail();
        opportunityDetail.setLastAccId(accountInfo.getAccountNum());
        opportunityDetail.setFinalCustomerRestrictionFlag(accountInfo.getRestrictedPartyCode());
        //TODO 无须修改
//        opportunityDetailService.update(opportunityDetail, false);
        try {
            revokeFlow(entity.getFlowInstanceId(), entity.getRowId());
        }catch (Exception e){
            log.error("revokeFlowFail,entity:{}", entity, e);
            entity.setStatus("revokeFlowFail:" + ExceptionMsgUtils.getStackTrace(e, 1000));
            return;
        }
        //启动流程
        try {
            SysGlobalConstVo sysGlobalConstVo = new SysGlobalConstVo();
            sysGlobalConstVo.setxEmpNo(entity.getCreatedBy());
            CommonUtils.setSysGlobalConstVo(sysGlobalConstVo);
            opportunityInfoService.startFlowTask(entity.getRowId());
            entity.setStatus("startFlowTaskSuccess");
        }catch (Exception e){
            log.error("startFlowTaskFail,entity:{}", entity, e);
            entity.setStatus("startFlowTaskFail");
        }finally {
            CommonUtils.remove();
        }
        opportunityDetail.setLastAccStatus( LastAccStatusEnum.EFFECTIVE_CUSTOMER.getKey());
        //TODO 无须修改
       // opportunityDetailService.update(opportunityDetail, false);
    }

    private void revokeFlow(String flowInstanceId, String rowId){
        ApprovalRevokeParamsDTO approvalRevokeParamsDTO = new ApprovalRevokeParamsDTO();
        approvalRevokeParamsDTO.setFlowInstanceId(flowInstanceId);
        approvalFlowService.revokeFlow(approvalRevokeParamsDTO);
        comApprovalRecordService.softDeleteByBusinessIdAndFlowId(rowId, flowInstanceId);
    }

    private void createDraftAndRevoke(OpportunityKeyInfoEntity entity){
        OpportunityDetail opportunityDetail = new OpportunityDetail();
        opportunityDetail.setLastAccName(entity.getLastAccName());
        opportunityDetail.setBusinessManagerId(entity.getBusinessManagerId());
        opportunityDetail.setRowId(entity.getRowId());
        try {
            opportunityInfoService.creatCustomer(opportunityDetail);
            entity.setStatus("creatCustomerSuccess");
        }catch (Exception e){
            log.error("creatCustomerFail,entity:{}", entity);
            entity.setStatus("creatCustomerFail:" + ExceptionMsgUtils.getStackTrace(e, 1000));
            return;
        }
        try {
            revokeFlow(entity.getFlowInstanceId(), entity.getRowId());
        }catch (Exception e){
            log.error("creatCustomerSuccessThenRevokeFlowFail,entity:{}", entity);
            entity.setStatus("creatCustomerSuccessThenRevokeFlowFail:" + ExceptionMsgUtils.getStackTrace(e, 1000));
        }
    }
}
