package com.zte.aiagent.domain.repository;

import com.zte.aiagent.domain.aggregate.BidDocument;
import com.zte.aiagent.domain.shared.valueobject.Tenant;
import com.zte.aiagent.ui.dto.request.BidDocumentPageQueryDTO;
import com.zte.aiagent.ui.dto.vo.BidDocumentVO;
import com.zte.itp.msa.core.model.FormData;

import java.util.List;
import java.util.Optional;

/**
 * 招标文档仓储接口
 * 定义文档聚合的持久化操作
 */
public interface BidDocumentRepository {

    /**
     * 保存文档聚合
     */
    void save(BidDocument bidDocument);

    /**
     * 根据ID查找文档
     */
    Optional<BidDocument> findById(String documentId);

    /**
     * 根据ID和租户查找文档
     */
    Optional<BidDocument> findByIdAndTenant(String documentId, Tenant tenantId);

    /**
     * 检查文档是否存在
     */
    boolean exists(String documentId);

    /**
     * 分页查询招标文件列表
     * @param request 查询请求参数
     * @return 招标文件VO列表
     */
    List<BidDocumentVO> selectPageList(FormData<BidDocumentPageQueryDTO> request);

    /**
     * 查询总记录数
     * @param request 查询请求参数
     * @return 总记录数
     */
    Long selectPageCount(FormData<BidDocumentPageQueryDTO> request);
}
