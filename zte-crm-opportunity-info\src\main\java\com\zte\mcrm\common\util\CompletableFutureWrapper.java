package com.zte.mcrm.common.util;

import com.zte.mcrm.common.model.SysGlobalConstVo;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Supplier;

/**
 * completableFuture的包装，避免多线程请求时获取不到请求头信息。
 * 改进版本：只传递必要的上下文信息，避免请求对象回收问题
 */
public class CompletableFutureWrapper {

    /**
     * 异步执行，只传递SysGlobalConstVo，避免请求对象回收问题
     */
    public static <U> CompletableFuture<U> supplyAsync(Supplier<U> supplier) {
        SysGlobalConstVo sysGlobalConstVo = CommonUtils.getSysGlobalConstVo();
        return CompletableFuture.supplyAsync(() -> {
            CommonUtils.setSysGlobalConstVo(sysGlobalConstVo);
            return supplier.get();
        });
    }

    /**
     * 异步执行，只传递SysGlobalConstVo，避免请求对象回收问题
     */
    public static <U> CompletableFuture<U> supplyAsync(Supplier<U> supplier,
            Executor executor) {
        SysGlobalConstVo sysGlobalConstVo = CommonUtils.getSysGlobalConstVo();
        return CompletableFuture.supplyAsync(() -> {
            CommonUtils.setSysGlobalConstVo(sysGlobalConstVo);
            return supplier.get();
        }, executor);
    }

    /**
     * 异步执行，只传递SysGlobalConstVo，避免请求对象回收问题
     */
    public static CompletableFuture<Void> runAsync(Runnable runnable) {
        SysGlobalConstVo sysGlobalConstVo = CommonUtils.getSysGlobalConstVo();
        return CompletableFuture.runAsync(() -> {
            CommonUtils.setSysGlobalConstVo(sysGlobalConstVo);
            runnable.run();
        });
    }

    /**
     * 异步执行，只传递SysGlobalConstVo，避免请求对象回收问题
     */
    public static CompletableFuture<Void> runAsync(Runnable runnable,
            Executor executor) {
        SysGlobalConstVo sysGlobalConstVo = CommonUtils.getSysGlobalConstVo();
        return CompletableFuture.runAsync(() -> {
            CommonUtils.setSysGlobalConstVo(sysGlobalConstVo);
            runnable.run();
        }, executor);
    }

}
