package com.zte.mcrm.adapter.model.dto;

/* Started by AICoder, pid:8a826ff3b0204ed59a2ec910340bea6a */
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@ApiModel(description = "客户查询条件模型")
@Getter
@Setter
@ToString
public class CustomerQueryCondition {

    @ApiModelProperty(value = "客户名称，最大100个", example = "[]")
    private List<String> nameList;

    @ApiModelProperty(value = "客户编码，最大100个", example = "[\"CN000000003192\"]")
    private List<String> customerCodeList;

    @ApiModelProperty(value = "需要的信息类型（还在完善中），默认会返回【客户基本信息】", example = "[]")
    private List<String> contentType;

    @ApiModelProperty(value = "过滤被合并客户。true-过滤，false-不过滤。【默认true】", example = "true")
    private boolean filterMerge;

    @ApiModelProperty(value = "过滤冻结客户。true-不查询被合并客户，false-不限制。【默认true】", example = "true")
    private boolean filterFrozen;

    @ApiModelProperty(value = "是否只要生效的客户。true-仅查生效状态客户，false-不限制客户状态查询。【默认true】", example = "true")
    private boolean onlyEffect;
}
/* Ended by AICoder, pid:8a826ff3b0204ed59a2ec910340bea6a */