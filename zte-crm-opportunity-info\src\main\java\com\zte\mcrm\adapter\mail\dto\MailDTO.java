package com.zte.mcrm.adapter.mail.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 * <AUTHOR>
 */
@Getter
@Setter
public class MailDTO {

    @ApiModelProperty(value = "邮件日志ID")
    private Long emailLogId;
    @ApiModelProperty(value = "是否需要重试(Y是N否)")
    private String retryFlag;
    @ApiModelProperty(value = "重试次数")
    private Long retryTimes;

    /** 邮件标题 **/
    @ApiModelProperty(value = "邮件标题")
    private String mailSubject;
    /** 发件人 **/
    @ApiModelProperty(value = "发件人 ")
    private String mailFrom;
    /** 收件人 **/
    @ApiModelProperty(value = "收件人 ")
    private String mailTo;
    /** 抄送人 **/
    @ApiModelProperty(value = "抄送人")
    private String mailcc;
    /** 密送人 **/
    @ApiModelProperty(value = "密送人")
    private String mailbcc;

    /** 点击查看-中文 **/
    @ApiModelProperty(value = "点击查看-中文")
    private String clickLookCn;
    /** 点击查看的链接-中文 **/
    @ApiModelProperty(value = "点击查看的链接-中文")
    private String linkAddCn;
    /** 邮件内容-中文 **/
    @ApiModelProperty(value = "邮件内容-中文")
    private String mainTextCn;
    /** 温馨提示 **/
    @ApiModelProperty(value = "温馨提示-中文")
    private String warmCallCn;
    /** 点击查看英文 **/
    @ApiModelProperty(value = "点击查看-英文")
    private String clickLookEn;
    /** 点击查看的链接英文 **/
    @ApiModelProperty(value = "点击查看的链接-英文")
    private String linkAddEn;
    /** 邮件内容英文 **/
    @ApiModelProperty(value = "邮件内容-英文")
    private String mainTextEn;
    /** 温馨提示 **/
    @ApiModelProperty(value = "温馨提示-英文")
    private String warmCallEn;
    /** 邮件类型 **/
    @ApiModelProperty(value = "邮件类型")
    private String mailType;

    @Override
    public String toString() {
        return "MailDTO{" +
                "mailSubject='" + mailSubject + '\'' +
                ", mailFrom='" + mailFrom + '\'' +
                ", mailTo='" + mailTo + '\'' +
                ", mailcc='" + mailcc + '\'' +
                ", mailType='" + mailType + '\'' +
                ", mailbcc='" + mailbcc + '\'' +
                ", clickLookCn='" + clickLookCn + '\'' +
                ", linkAddCn='" + linkAddCn + '\'' +
                ", mainTextCn='" + mainTextCn + '\'' +
                ", warmCallCn='" + warmCallCn + '\'' +
                ", clickLookEn='" + clickLookEn + '\'' +
                ", linkAddEn='" + linkAddEn + '\'' +
                ", mainTextEn='" + mainTextEn + '\'' +
                ", warmCallEn='" + warmCallEn + '\'' +
                '}';
    }


}
