
package com.zte.mcrm.channel.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@ApiModel("审批中心moa审批回调校验")
public class ApprovalVerificationCallback {

    @ApiModelProperty("业务id")
    private String businessId;
    @ApiModelProperty("扩展意见")
    private String extOpinion;
    @ApiModelProperty("流程实例id")
    private String flowInstanceId;
    @ApiModelProperty("节点名称")
    private String nodeName;
    @ApiModelProperty("评审意见")
    private String opinion;
    @ApiModelProperty("结论")
    private String result;
    @ApiModelProperty("任务id")
    private String taskId;
}
