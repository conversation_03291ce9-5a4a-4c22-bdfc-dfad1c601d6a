package com.zte.mcrm.channel.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.mcrm.common.util.ValidationGroups;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@ToString
public class MonthReportDetailVO {
    @ApiModelProperty(value = "商机编号")
    @NotBlank(message = "{opportunity.code.blank}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    private String optyCd;
    @NotBlank(message = "{opportunity.name.blank}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    @Length(max = 100, message = "{opportunity.name.limit}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    @ApiModelProperty(value = "商机名称")
    private String attrib46;
    @ApiModelProperty(value = "项目当前阶段编码")
    @NotBlank(message = "{projectPhasesCode.null}", groups = {ValidationGroups.Submit.class})
    private String projectPhasesCode;
    @ApiModelProperty(value = "项目当前阶段名称")
    private String projectPhasesName;
    @ApiModelProperty(value = "赢率-编码")
    @NotBlank(message = "{winRate.null}", groups = {ValidationGroups.Submit.class})
    private String winRate;
    @ApiModelProperty(value = "赢率-名称")
    private String winRateName;
    @ApiModelProperty(value = "招标类型编码")
    @NotBlank(message = "{tenderTypeCode.null}", groups = {ValidationGroups.Submit.class})
    private String tenderTypeCode;
    @ApiModelProperty(value = "招标类型名称")
    private String tenderTypeName;
    @ApiModelProperty(value = "招标方全称")
    @Length(max = 100, message = "{bidProviderName.length.limit}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    private String bidProviderName;
    @ApiModelProperty(value = "预计发标/签约时间")
    @JsonFormat(pattern ="yyyy-MM-dd", timezone = "GMT+8")
    private Date date1;
    @ApiModelProperty(value = "预计签约时间")
    @JsonFormat(pattern ="yyyy-MM-dd", timezone = "GMT+8")
    private Date date2;
    @JsonFormat(pattern ="yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "竞标截止日期")
    private Date biddingDeadline;
    @ApiModelProperty(value = "报备人姓名")
    @Length(max = 50, message = "{agencyName.length.limit}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    private String agencyName;
    @ApiModelProperty(value = "报备人电话")
    @Length(max = 20, message = "{agencyPhone.length.limit}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    private String agencyPhone;
    @ApiModelProperty(value = "报备人邮箱")
    @Length(max = 100, message = "{agencyEmail.length.limit}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    @Email(message = "{agencyEmail.format.error}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    private String agencyEmail;
    @ApiModelProperty(value = "商机概况")
    @Length(max=1500, message = "{projectDesc.length}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    @NotBlank(message = "{projectDesc.null}", groups = {ValidationGroups.Submit.class})
    private String projectDesc;
    @ApiModelProperty(value = "最终用户联系人姓名")
    @NotBlank(message = "{finalCustomerContactName.empty}", groups = {ValidationGroups.Submit.class})
    @Length(max = 50, message = "{finalCustomerContactName.length.limit}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    private String finalCustomerContactName;
    @ApiModelProperty(value = "最终用户联系人电话")
    @Length(max = 20, message = "{finalCustomerContactPhone.length.limit}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    @NotBlank(message = "{finalCustomerContactPhone.empty}", groups = {ValidationGroups.Submit.class})
    private String finalCustomerContactPhone;
    @ApiModelProperty(value = "最终用户联系人邮箱")
    @Length(max = 100, message = "{finalCustomerContactEmail.length.limit}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    @Email(message = "{finalCustomerContactEmail.format.error}", groups = {ValidationGroups.Storage.class, ValidationGroups.Submit.class})
    private String finalCustomerContactEmail;
    @ApiModelProperty(value = "预计签单金额")
    @Min(value = 5000, message = "{totalAmount.lower}", groups = {ValidationGroups.Submit.class})
    @NotNull(message = "{totalAmount.empty}", groups = {ValidationGroups.Submit.class})
    private BigDecimal totalAmount;
}
