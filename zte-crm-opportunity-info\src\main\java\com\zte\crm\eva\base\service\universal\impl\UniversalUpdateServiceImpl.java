package com.zte.crm.eva.base.service.universal.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.zte.crm.eva.base.common.constant.universal.UniversalUpdateConstant;
import com.zte.crm.eva.base.common.utils.UniversalCommonUtil;
import com.zte.crm.eva.base.infrastructure.access.dao.UniversalDao;
import com.zte.crm.eva.base.infrastructure.adapter.IchannelBaseAdapter;
import com.zte.crm.eva.base.infrastructure.client.model.PrmQuickCodeValue;
import com.zte.crm.eva.base.service.universal.UniversalUpdateService;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.common.business.KeyIdBusiness;
import com.zte.mcrm.common.consts.CommonConstant;
import com.zte.mcrm.common.errorcode.util.ExceptionLogOperateUtil;
import com.zte.mcrm.common.framework.exception.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.zte.crm.eva.base.common.constant.universal.FrontEndUniveralConsts.ORG_ADJUST_CONFIGURE_IS_EMPTY;
import static com.zte.crm.eva.base.common.constant.universal.FrontEndUniveralConsts.ORG_ADJUST_TASK_IS_EMPTY;

@Service
@Slf4j
public class UniversalUpdateServiceImpl implements UniversalUpdateService {

    @Autowired
    private UniversalDao universalDao;
    @Autowired
    private IchannelBaseAdapter ichannelBaseAdapter;

    @Override
    public Map<String, Object> universalUpdate(String executeModule) {
        Map<String, Object> resultMap = Maps.newHashMap();
        // 1、组织调整任务与表关联关系查询
        List<PrmQuickCodeValue> prmQuickCodeValueVOList = ichannelBaseAdapter.getCodeValuesByCodeType(UniversalUpdateConstant.FUNDATION_AGENCY_UPDATE);
        if (CollectionUtils.isEmpty(prmQuickCodeValueVOList)) {
            log.info(ORG_ADJUST_CONFIGURE_IS_EMPTY);
            resultMap.put("result", ORG_ADJUST_CONFIGURE_IS_EMPTY);
            return resultMap;
        }
        // 2、组织调整任务查询接口
        PageRows<Map<String, Object>> taskPageRows = ichannelBaseAdapter.getFoundationUpdate(getFoundationUpdateFormData(executeModule));
        List<Map<String, Object>> taskList = taskPageRows.getRows();
        if (CollectionUtils.isEmpty(taskList)) {
            log.info(ORG_ADJUST_TASK_IS_EMPTY);
            resultMap.put("result", ORG_ADJUST_TASK_IS_EMPTY);
            return resultMap;
        }
        // 更新操作：更目的表组织、更新组织调整任务、记录组织调整流水
        // 取表信息
        Map<String, Map<String, String>> tableInfos = UniversalCommonUtil.convertTableInfo(prmQuickCodeValueVOList);
        // 逐条处理
        taskList.forEach(taskData -> {
            try {
                String valueCode = Convert.toStr(taskData.get(UniversalUpdateConstant.VALUE_CODE));
                if (tableInfos.containsKey(valueCode)) {
                    Map<String, String> tableInfo = tableInfos.get(valueCode);
                    // 数据准备
                    List<String> columnList = getColumnList(tableInfo);
                    List<String> sourceList = getSourceList(taskData);
                    List<String> targetList = getTargetList(taskData);
                    List<String> flagList = getFlagList(tableInfo);
                    // 表名、更新字段、条件字段
                    String tableName = tableInfo.get(UniversalUpdateConstant.DATABASE_NAME) + "." + tableInfo.get(UniversalUpdateConstant.TABLE_NAME);
                    Map<String, Object> fieldMap = getFieldMap(columnList, targetList);
                    Map<String, Object> conditionMap = getConditionMap(columnList, sourceList);
                    // 更新时间字段、标记
                    String updateTimeColumn = columnList.get(UniversalUpdateConstant.UPDATE_TIME_COLUMN_INDEX);
                    String updateTimeFlag = flagList.get(UniversalUpdateConstant.UPDATE_TIME_COLUMN_INDEX);
                    Map<String, Object> updateTimeMap = getUpdateTimeMap(updateTimeColumn, updateTimeFlag);
                    // 更新字段判空
                    Assert.notEmpty(fieldMap);
                    // 条件字段判空
                    Assert.notEmpty(conditionMap);
                    // 刷新前count（*）记录数
                    int countNum = universalDao.selectEqCommonCount(tableName, conditionMap);
                    // 刷新记录数
                    int updateNum = universalDao.updateSingleCommon(tableName, fieldMap, conditionMap, updateTimeFlag, updateTimeMap);
                    // 记录流水表
                    Long taskId = KeyIdBusiness.getID();
                    Boolean recordFlag = ichannelBaseAdapter.recordFoundation(getRecordExecuteInfo(taskId, tableInfo, taskData));
                    String key = tableName + taskId;
                    resultMap.put(key, recordFlag);
                    // 更新任务表
                    ichannelBaseAdapter.updateFoundation(getImportUpdateInfo(taskId, taskData, countNum, updateNum));
                }
            } catch (Exception e) {
                log.error("组织调整任务{}", JSON.toJSONString(taskData), e);
                ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
                ExceptionLogOperateUtil.saveExceptionLog(servletRequestAttributes.getRequest(), e, ErrorCode.UniversalErrorEnum.OU3113.code());
            }
        });
        return resultMap;
    }

    /**
     * 获取任务表formData
     *
     * @param executeModule
     * @return
     */
    private FormData<Map<String, Object>> getFoundationUpdateFormData(String executeModule) {
        FormData<Map<String, Object>> formData = new FormData<>();
        formData.setPage(1);
        formData.setRows(UniversalUpdateConstant.DEFAULT_ROWS);
        Map<String, Object> boMap = Maps.newHashMap();
        boMap.put(UniversalUpdateConstant.EXECUTE_MODULE, executeModule);
        boMap.put(UniversalUpdateConstant.UPDATE_STATUS, UniversalUpdateConstant.UPDATE_STATUS_10);
        formData.setBo(boMap);
        return formData;
    }

    /**
     * 系统参数字段列表
     *
     * @param tableInfo
     * @return
     */
    private List<String> getColumnList(Map<String, String> tableInfo) {
        List<String> columnList = new ArrayList<>();
        tableInfo.forEach((column, flag) -> columnList.add(column));
        return columnList;
    }

    /**
     * 系统参数标记列表
     *
     * @param tableInfo
     * @return
     */
    private List<String> getFlagList(Map<String, String> tableInfo) {
        List<String> flagList = new ArrayList<>();
        tableInfo.forEach((column, flag) -> flagList.add(flag));
        return flagList;
    }

    /**
     * 原值列表
     *
     * @param taskData
     * @return
     */
    private List<String> getSourceList(Map<String, Object> taskData) {
        List<String> sourceList = new ArrayList<>();
        sourceList.add(Convert.toStr(taskData.get(UniversalUpdateConstant.SOURCE_ONE)));
        sourceList.add(Convert.toStr(taskData.get(UniversalUpdateConstant.SOURCE_TWO)));
        sourceList.add(Convert.toStr(taskData.get(UniversalUpdateConstant.SOURCE_THREE)));
        sourceList.add(Convert.toStr(taskData.get(UniversalUpdateConstant.SOURCE_FOUR)));
        return sourceList;
    }

    /**
     * 目标值列表
     *
     * @param taskData
     * @return
     */
    private List<String> getTargetList(Map<String, Object> taskData) {
        List<String> targetList = new ArrayList<>();
        targetList.add(Convert.toStr(taskData.get(UniversalUpdateConstant.TARGET_ONE)));
        targetList.add(Convert.toStr(taskData.get(UniversalUpdateConstant.TARGET_TWO)));
        targetList.add(Convert.toStr(taskData.get(UniversalUpdateConstant.TARGET_THREE)));
        targetList.add(Convert.toStr(taskData.get(UniversalUpdateConstant.TARGET_FOUR)));
        return targetList;
    }

    /**
     * 更新字段
     *
     * @param columnList
     * @param targetList
     * @return
     */
    private Map<String, Object> getFieldMap(List<String> columnList, List<String> targetList) {
        Map<String, Object> fieldMap = Maps.newHashMap();
        List<String> columnListSub = columnList.subList(3, columnList.size());
        for (int i = 0; i < columnListSub.size(); i++) {
            fieldMap.put(columnListSub.get(i), targetList.get(i));
        }
        return fieldMap;
    }

    /**
     * 更新条件
     *
     * @param columnList
     * @param sourceList
     * @return
     */
    private Map<String, Object> getConditionMap(List<String> columnList, List<String> sourceList) {
        Map<String, Object> conditionMap = Maps.newHashMap();
        List<String> columnListSub = columnList.subList(3, columnList.size());
        for (int i = 0; i < columnListSub.size(); i++) {
            conditionMap.put(columnListSub.get(i), sourceList.get(i));
        }
        return conditionMap;
    }

    /**
     * 更新时间字段
     *
     * @param updateTimeColumn
     * @param updateTimeFlag
     * @return
     */
    private Map<String, Object> getUpdateTimeMap(String updateTimeColumn, String updateTimeFlag) {
        Map<String, Object> updateTimeMap = Maps.newHashMap();
        if (UniversalUpdateConstant.Y.equals(updateTimeFlag)) {
            updateTimeMap.put(updateTimeColumn, DateUtil.formatDateTime(new Date()));
        }
        if (UniversalUpdateConstant.N.equals(updateTimeFlag)) {
            updateTimeMap.put(updateTimeColumn, updateTimeColumn);
        }
        return updateTimeMap;
    }

    /**
     * 流水表信息
     *
     * @param taskId
     * @param tableInfo
     * @param taskData
     * @return
     */
    private Map<String, Object> getRecordExecuteInfo(Long taskId, Map<String, String> tableInfo, Map<String, Object> taskData) {
        Map<String, Object> recordExecuteInfo = Maps.newHashMap();
        recordExecuteInfo.put(UniversalUpdateConstant.TASK_ID, taskId);
        recordExecuteInfo.put(UniversalUpdateConstant.DATABASE_NAME, tableInfo.get(UniversalUpdateConstant.DATABASE_NAME));
        recordExecuteInfo.put(UniversalUpdateConstant.TABLE_NAME, tableInfo.get(UniversalUpdateConstant.TABLE_NAME));
        recordExecuteInfo.put(UniversalUpdateConstant.SOURCE_ONE, Convert.toStr(taskData.get(UniversalUpdateConstant.SOURCE_ONE)));
        recordExecuteInfo.put(UniversalUpdateConstant.SOURCE_TWO, Convert.toStr(taskData.get(UniversalUpdateConstant.SOURCE_TWO)));
        recordExecuteInfo.put(UniversalUpdateConstant.SOURCE_THREE, Convert.toStr(taskData.get(UniversalUpdateConstant.SOURCE_THREE)));
        recordExecuteInfo.put(UniversalUpdateConstant.SOURCE_FOUR, Convert.toStr(taskData.get(UniversalUpdateConstant.SOURCE_FOUR)));
        recordExecuteInfo.put(UniversalUpdateConstant.VALUE_CODE, Convert.toStr(taskData.get(UniversalUpdateConstant.VALUE_CODE)));
        recordExecuteInfo.put(UniversalUpdateConstant.EXECUTE_MODULE, Convert.toStr(taskData.get(UniversalUpdateConstant.EXECUTE_MODULE)));
        recordExecuteInfo.put(UniversalUpdateConstant.CREATED_BY, Convert.toStr(taskData.get(UniversalUpdateConstant.CREATED_BY)));
        recordExecuteInfo.put(UniversalUpdateConstant.LAST_UPDATED_BY, taskData.get(UniversalUpdateConstant.LAST_UPDATED_BY));
        String date = new SimpleDateFormat(CommonConstant.YYYY_MM_DD_HH_MM_SS24_SLASH).format(new Date());
        recordExecuteInfo.put(UniversalUpdateConstant.CREATED_DATE, date);
        recordExecuteInfo.put(UniversalUpdateConstant.LAST_UPDATED_DATE, date);
        return recordExecuteInfo;
    }

    /**
     * 任务表更新信息
     *
     * @param taskId
     * @param taskData
     * @param countNum
     * @param updateNum
     * @return
     */
    private Map<String, Object> getImportUpdateInfo(Long taskId, Map<String, Object> taskData, int countNum, int updateNum) {
        Map<String, Object> queryParam = Maps.newHashMap();
        String id = Convert.toStr(taskData.get(UniversalUpdateConstant.ID));
        queryParam.put(UniversalUpdateConstant.ID, id);
        queryParam.put(UniversalUpdateConstant.TASK_ID, taskId);
        queryParam.put(UniversalUpdateConstant.UPDATE_STATUS, UniversalUpdateConstant.UPDATE_STATUS_20);
        queryParam.put(UniversalUpdateConstant.COUNT_NUM, countNum);
        queryParam.put(UniversalUpdateConstant.UPDATE_NUM, updateNum);
        return queryParam;
    }
}

