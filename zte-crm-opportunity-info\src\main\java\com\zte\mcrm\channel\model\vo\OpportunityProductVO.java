package com.zte.mcrm.channel.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.channel.model.entity.OpportunityProduct;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
*  实体类
* <AUTHOR>
* @date 2021/09/14
*/

@Setter @Getter @ToString
@ApiModel(description="")
public class OpportunityProductVO implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private String rowId;
    @ApiModelProperty(value = "创建人")
    private String createdBy;
    @ApiModelProperty(value = "最后更新人")
    private String lastUpdBy;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpd;

    @ApiModelProperty(value ="商机id")
    private String opptyId;
    @ApiModelProperty(value = "产品Id")
    private String prodLv2Id;
    @ApiModelProperty(value = "产品名称")
    private String prodLv2Name;
    @ApiModelProperty(value = "产品签单金额")
    private BigDecimal productAmount;
    @ApiModelProperty(value = "公司主产品")
    private OpportunityPdmProductVO pdmProd;
    @ApiModelProperty(value = "主产品标识")
    private String zteMainProduct;

    // 主产品转换为商机产品行
    public OpportunityProduct pdmProdToOpportunityProduct() {
        OpportunityProduct product = new OpportunityProduct();
        BeanUtils.copyProperties(this.getPdmProd(), product);
        product.setBusinessType(OpportunityConstant.PDM_PROD);
        return product;
    }
}
