package com.zte.mcrm.adapter.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 查询渠道商认证信息入参
 * <AUTHOR>
 * @date 2021/11/19
 */
@Data
public class CertificationQueryInfo {

    @ApiModelProperty(value = "所属系统 System")
    private String fromSystem;

    @ApiModelProperty(value = "公司名称 company name")
    private String customerName;

    @ApiModelProperty(value = "公司名称 模糊查询 customer_name")
    private String fuzzyCustomerName;

    @ApiModelProperty(value = "合作伙伴id  Partner ID")
    private String partnerId;


    @ApiModelProperty(value = "客户编码 Customer Code")
    private String crmCustomerCode;

    @ApiModelProperty(value = "客户编码 Customer Code")
    private String customerType;

    @ApiModelProperty(value = "资质类型,快码:认证信息快码 Authentication Information Quick Code （qualificationCategory）")
    private String qualificationCategory;

    @ApiModelProperty(value = "认证分类,快码:认证信息快码 Authentication Information Quick Code （certificationClassification）")
    private String authenType;

    @ApiModelProperty(value = "认证等级,快码:认证信息快码 Authentication Information Quick Code, （certificationLevel）")
    private String authenLevel;

    @ApiModelProperty(value = "认证产品 Certification Product")
    private String authenProduct;

    @ApiModelProperty(value = "认证状态,快码:认证信息快码 Authentication Information Quick Code （certificationStatus）")
    private String authenStatus;

    @ApiModelProperty(value = "最后更新开始时间  createdDate")
    private Date updateBeginDate;

    @ApiModelProperty(value = "最后更新时间 lastUpdatedDate")
    private Date updateEndDate;

    @ApiModelProperty(value = "认证开始时间-最小值 Certification start time")
    private Date authenBeginDateMin;

    @ApiModelProperty(value = "认证开始时间-最大值 Certification endtime")
    private Date authenBeginDateMax;

    @ApiModelProperty(value = "认证结束时间-最小值 Certification start time")
    private Date authenEndDateMin;

    @ApiModelProperty(value = "认证结束时间-最大值 Certification endtime")
    private Date authenEndDateMax;

    @ApiModelProperty(value = "页码 page")
    private Integer page;

    @ApiModelProperty(value = "分页大小,不超过100条 rows: no more 100")
    private Integer rows;

    @ApiModelProperty(value = "customerIds")
    private List<Long> customerIds;
}