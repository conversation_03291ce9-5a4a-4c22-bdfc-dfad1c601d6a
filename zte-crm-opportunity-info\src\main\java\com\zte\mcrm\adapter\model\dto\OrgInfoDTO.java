package com.zte.mcrm.adapter.model.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * HR组织信息
 *
 * <AUTHOR>
 * @date 2025.04.15 下午3:20
 */
@Getter
@Setter
public class OrgInfoDTO {
    /**
     * 统一组织编码
     */
    private  String hrOrgID;
    /**
     * 统一组织编码父节点
     */
    private  String hrOrgPID;
    /**
     *人事旧部门编号
     */
    private  String hrOldDeptNO;
    //人事旧部门编号父节点
    private  String hrOldDeptPNO;
    private  String hrLevel;
    private  String hrOrgName;
    //组织全路径
    private  String hrOrgNamePath;
    //组织编码全路径
    private  String orgIDPath;
    private  String enabled;
    private  String orgLevel;
    private  String orgStatusID;
    //是否参与业绩统计 1:业绩单位，0:非业绩单位
    private  String isPartakePerfStats;
    //组织成立时间
    private  String orgEstDate;
    private  String remark;
    /**
     * 组织所属公司编码
     */
    private  String companyId;
    private  String companyName;
    /**
     * 类型 T0001：股份组织,，特指股份公司
     * T0002：子公司，特指ZTE的子公司
     * T0003：合作方公司
     */
    private  String type;
    /**
     * 租户id
     */
    private  String tenantId;
}
