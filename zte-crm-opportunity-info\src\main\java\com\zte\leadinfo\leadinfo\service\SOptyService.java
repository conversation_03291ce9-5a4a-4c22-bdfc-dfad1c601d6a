package com.zte.leadinfo.leadinfo.service;

import com.zte.leadinfo.leadinfo.entity.CustomerInfoDO;
import com.zte.leadinfo.leadinfo.entity.SOptyDO;
import com.zte.opty.sync.domain.dto.OptySyncQueryDTO;

import java.util.List;

/**
 * 服务接口
 *
 * <AUTHOR>
 * @description 由 Mybatisplus Code Generator 创建
 * @since 2024-06-04 10:52:11
 */
public interface SOptyService {

    List<SOptyDO> listByIds(List<String> optyRowIds);

    /**
     * 更新迁移结果
     * @param rowId 主键ID
     * @param result 迁移结果
     * @param message 迁移信息
     * @return
     */
    int updateMigrationResult(List<String> rowIds, String result, String message);

    /**
     * 动态sql，根据入参条件查询id
     * @param params
     * @return
     */
    List<String> selectIdsByConditions(OptySyncQueryDTO params);

    List<String> loadUndoOptyIds(int batchSize);

    List<CustomerInfoDO> selectAll();
}
