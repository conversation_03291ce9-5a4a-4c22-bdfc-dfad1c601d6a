package com.zte.mcrm.channel.util;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.concurrent.CompletionException;

/**
 * <AUTHOR>
 * 异常信息处理工具类
 */
public class ExceptionMsgUtils {

    private ExceptionMsgUtils(){
        throw new IllegalStateException("Utility class");
    }

    /**
     * 将异常堆栈转换为字符串形式，方便插入数据库或打印日志。
     * @param throwable 异常
     * @param maxLength 转换后字符串最大长度
     * @return 返回转换后的字符串
     */
    public static String getStackTrace(Throwable throwable, int maxLength){
        if (throwable instanceof CompletionException){
            throwable = throwable.getCause();
        }
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        try {
            throwable.printStackTrace(pw);
            String exceptionMsg = sw.toString();
            if(exceptionMsg.length() > maxLength){
                return exceptionMsg.substring(0, maxLength);
            }
            return exceptionMsg;
        } finally {
            pw.close();
        }
    }
}
