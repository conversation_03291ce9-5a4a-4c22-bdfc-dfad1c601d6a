package com.zte.mcrm.common.ui;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.common.access.vo.ComDictionaryMaintainVO;
import com.zte.mcrm.common.business.service.IComDictionaryMaintainService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Author: 10245190 DengZiqiao
 * @Date: 2021/9/9 11:37
 * @Version: V1.0
 */
@Api("数据字典表API")
@RestController
@RequestMapping("/dictionary")
public class ComDictionaryMaintainController {

    @Autowired
    private IComDictionaryMaintainService comDictionaryMaintainService;

    @ApiOperation("根据types查询测算数据字典")
    @GetMapping(value = "/queryByTypes")
    public ServiceData<Map<String, List<ComDictionaryMaintainVO>>> queryByTypes(
            @RequestParam @ApiParam(value = "类型s,英文逗号分隔", name = "types", required = true, defaultValue = "demo1,demo2") String types) {
        ServiceData<Map<String, List<ComDictionaryMaintainVO>>> serviceData = new ServiceData<>();
        Map<String, List<ComDictionaryMaintainVO>> resultMap = comDictionaryMaintainService.queryByTypeList(Arrays.asList(types.split(",")));
        serviceData.setBo(resultMap);
        return serviceData;
    }

    @ApiOperation("插入数据字典")
    @PostMapping(value = "/insertComDict")
    public ServiceData<String> insertComDict(
            @RequestBody ComDictionaryMaintainVO comDictionaryMaintainVO) {
        ServiceData<String> serviceData = new ServiceData<>();
        comDictionaryMaintainService.insert(comDictionaryMaintainVO);
        serviceData.setBo("success");
        return serviceData;
    }

    @ApiOperation("更新数据字典")
    @PostMapping(value = "/updateComDictData")
    public ServiceData<String> updateComDictData(
            @RequestBody ComDictionaryMaintainVO comDictionaryMaintainVO) {
        ServiceData<String> serviceData = new ServiceData<>();
        comDictionaryMaintainService.update(comDictionaryMaintainVO);
        serviceData.setBo("success");
        return serviceData;
    }

}

