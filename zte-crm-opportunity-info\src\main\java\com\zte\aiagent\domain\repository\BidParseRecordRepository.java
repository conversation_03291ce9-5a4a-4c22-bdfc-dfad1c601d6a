package com.zte.aiagent.domain.repository;

import com.zte.aiagent.infrastruction.access.po.BidParseRecordPO;
import com.zte.aiagent.ui.dto.vo.BidParseRecordVO;

import java.util.List;

/**
 * 解析项记录数据访问仓储接口
 * 负责解析项记录相关的数据访问操作
 *
 * <AUTHOR>
 */
public interface BidParseRecordRepository {

    /**
     * 插入解析项记录
     * @param bidParseRecord 解析项记录PO对象
     * @return 影响的行数
     */
    int insert(BidParseRecordPO bidParseRecord);

    /**
     * 根据ID查询解析项记录
     * @param rowId 主键ID
     * @return 解析项记录PO对象
     */
    BidParseRecordPO selectByPrimaryKey(String rowId);

    /**
     * 根据ID更新解析项记录
     * @param bidParseRecord 解析项记录PO对象
     * @return 影响的行数
     */
    int updateByPrimaryKey(BidParseRecordPO bidParseRecord);

    /**
     * 根据ID删除解析项记录
     * @param rowId 主键ID
     * @return 影响的行数
     */
    int deleteByPrimaryKey(String rowId);

    /**
     * 根据文档ID查询解析项记录列表
     * @param documentId 文档ID
     * @return 解析项记录列表
     */
    List<BidParseRecordVO> selectByDocumentId(String documentId);
}
