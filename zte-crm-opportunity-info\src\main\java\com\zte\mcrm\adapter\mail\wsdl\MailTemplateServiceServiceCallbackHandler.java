/**
 * MailTemplateServiceServiceCallbackHandler.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis2 version: 1.7.9  Built on : Nov 16, 2018 (12:05:37 GMT)
 */

package com.zte.mcrm.adapter.mail.wsdl;

/**
 *  MailTemplateServiceServiceCallbackHandler Callback class, Users can extend this class and implement
 *  their own receiveResult and receiveError methods.
 */
public abstract class MailTemplateServiceServiceCallbackHandler {
    protected Object clientData;

    /**
     * User can pass in any object that needs to be accessed once the NonBlocking
     * Web service call is finished and appropriate method of this CallBack is called.
     * @param clientData Object mechanism by which the user can pass in user data
     * that will be avilable at the time this callback is called.
     */
    public MailTemplateServiceServiceCallbackHandler(Object clientData) {
        this.clientData = clientData;
    }

    /**
     * Please use this constructor if you don't want to set any clientData
     */
    public MailTemplateServiceServiceCallbackHandler() {
        this.clientData = null;
    }

    /**
     * Get the client data
     */
    public Object getClientData() {
        return clientData;
    }

    /**
     * auto generated Axis2 call back method for sendMail method
     * override this method for handling normal response from sendMail operation
     */
    public void receiveResultsendMail(
            MailTemplateServiceServiceStub.SendMailResponse result) {
    }

    /**
     * auto generated Axis2 Error handler
     * override this method for handling error response from sendMail operation
     */
    public void receiveErrorsendMail(Exception e) {
    }

    /**
     * auto generated Axis2 call back method for refreshCache method
     * override this method for handling normal response from refreshCache operation
     */
    public void receiveResultrefreshCache(
            MailTemplateServiceServiceStub.RefreshCacheResponse result) {
    }

    /**
     * auto generated Axis2 Error handler
     * override this method for handling error response from refreshCache operation
     */
    public void receiveErrorrefreshCache(Exception e) {
    }

    /**
     * auto generated Axis2 call back method for sendMailHex method
     * override this method for handling normal response from sendMailHex operation
     */
    public void receiveResultsendMailHex(
            MailTemplateServiceServiceStub.SendMailHexResponse result) {
    }

    /**
     * auto generated Axis2 Error handler
     * override this method for handling error response from sendMailHex operation
     */
    public void receiveErrorsendMailHex(Exception e) {
    }

    /**
     * auto generated Axis2 call back method for sendMailByTemplates method
     * override this method for handling normal response from sendMailByTemplates operation
     */
    public void receiveResultsendMailByTemplates(
            MailTemplateServiceServiceStub.SendMailByTemplatesResponse result) {
    }

    /**
     * auto generated Axis2 Error handler
     * override this method for handling error response from sendMailByTemplates operation
     */
    public void receiveErrorsendMailByTemplates(Exception e) {
    }
}

