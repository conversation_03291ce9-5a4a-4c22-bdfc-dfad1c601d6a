package com.zte.mcrm.channel.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * 中标商机查询参数DTO
 * <AUTHOR>
 * @date 2024/01/01
 */
@Setter
@Getter
public class WinOpportunityQueryDTO {

    @ApiModelProperty(value = "渠道商客户编码", required = true)
    @NotBlank(message = "渠道商客户编码不能为空")
    private String crmCustomerCode;

}