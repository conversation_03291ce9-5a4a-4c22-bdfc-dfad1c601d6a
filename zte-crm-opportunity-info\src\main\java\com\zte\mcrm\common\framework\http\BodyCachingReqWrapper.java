package com.zte.mcrm.common.framework.http;

/**
 * @Author: <EMAIL>
 * @Date: 2022/01/06
 * @Description:
 */


import org.apache.commons.io.IOUtils;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;

public class BodyCachingReqWrapper extends HttpServletRequestWrapper{

    private byte[] bodyNew;

    private BufferedReader bufferedReader;

    private ServletInputStream servletInputStream;

    public BodyCachingReqWrapper(HttpServletRequest httpServletRequest) throws IOException{
        super(httpServletRequest);
        loadBody(httpServletRequest);
    }

    private void loadBody(HttpServletRequest request) throws IOException{
        bodyNew = IOUtils.toByteArray(request.getInputStream());
        servletInputStream = new RequestCachingInputStream(bodyNew);
    }

    public byte[] getBody() {
        return bodyNew;
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        return servletInputStream != null ? servletInputStream : super.getInputStream();
    }

    @Override
    public BufferedReader getReader() throws IOException {
        if (bufferedReader == null) {
            bufferedReader = new BufferedReader(new InputStreamReader(servletInputStream, getCharacterEncoding()));
        }
        return bufferedReader;
    }

    private static class RequestCachingInputStream extends ServletInputStream {

        private final ByteArrayInputStream inputStream;

        public RequestCachingInputStream(byte[] bytes) {
            inputStream = new ByteArrayInputStream(bytes);
        }
        @Override
        public int read() throws IOException {
            return inputStream.read();
        }

        @Override
        public boolean isFinished() {
            return inputStream.available() == 0;
        }

        @Override
        public boolean isReady() {
            return true;
        }

        @Override
        public void setReadListener(ReadListener readlistener) {
        }

    }

}
