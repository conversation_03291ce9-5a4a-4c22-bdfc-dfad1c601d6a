package com.zte.mcrm.adapter.controller;


import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import com.zte.mcrm.adapter.model.dto.UcsUserInfoDTO;
import com.zte.mcrm.adapter.service.IBmtUcsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户中心管理
 *
 * <AUTHOR>
 * @date 2021/03/22
 */
@Api(tags = "用户中心管理")
@RestController
@RequestMapping("/ucs")
public class BmtUcsController {

    private Logger logger = LoggerFactory.getLogger(BmtUcsController.class);

    @Autowired
    private IBmtUcsService iBmtUcsService;

    @ApiOperation("批量查询内外部用户信息接口")
    @ApiImplicitParam(dataType="String", dataTypeClass=String.class, name = "accountIdList", paramType = "body", value = "用户中心账号ID或工号的集合", allowMultiple=true, required = true)

    @PostMapping(value = "/account/queryall")
    public ServiceData<List<UcsUserInfoDTO>> queryUserInfo(@RequestBody List<String> accountIdList) throws Exception {
        return ServiceResultUtil.success(iBmtUcsService.getUserInfoByAccountIdList(accountIdList));
    }
}
