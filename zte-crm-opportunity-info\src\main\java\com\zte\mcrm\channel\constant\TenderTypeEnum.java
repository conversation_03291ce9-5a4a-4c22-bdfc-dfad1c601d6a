package com.zte.mcrm.channel.constant;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 招标类型枚举
 * <AUTHOR>
 */

public enum TenderTypeEnum {
    /** 招标项目 */
    TENDER_PROJECT("tenderProject"),
    /** 议标项目 */
    BID_NEGOTIATION_PROJECT("bidNegotiationProject"),
    /** 暂不确定 */
    NOT_SURE("notSure"),
    /** 其他 */
    OTHERS("others");

    String value;

    TenderTypeEnum(String value){
        this.value = value;
    }

    @JsonValue
    public String getValue() {
        return value;
    }
}
