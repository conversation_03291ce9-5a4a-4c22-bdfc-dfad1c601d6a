package com.zte.mcrm.adapter.pdm.service;

import com.zte.mcrm.adapter.pdm.model.PdmProductTreeVO;

import java.util.List;

public interface IPdmLabelService {
    /**
     * /zte-plm-pdm-label/queryProductClassTree
     * PDM产品结构树（含终端和系统设备，不含产品，不分页）
     * @return
     */
    List<PdmProductTreeVO> queryProductClassTree(boolean includeDisabled);
    /**
     * 根据体系内部分类过滤产品树
     * @return
     */
    List<PdmProductTreeVO> queryProductClassTreeFilterByInnerClass(List<String> innerClassNames);

    /**
     * 根据默认配置的体系内部分类过滤产品树
     * @return
     */
    List<PdmProductTreeVO> queryProductClassTreeFilterForProject();
}
