package com.zte.mcrm.adapter.emdm.controller;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import com.zte.mcrm.adapter.emdm.service.EmdmService;
import com.zte.mcrm.adapter.model.vo.CreateCustomerParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 描述：
 * 创建时间：2021/10/21
 *
 * @author：王丹凤6396000572
 */
@Api(tags ="EMDM信息查询API")
@RestController
@RequestMapping("/emdm")
public class EmdmQueryController {

    @Autowired
    private EmdmService emdmService;

    @ApiOperation("查询城市编码")
    @GetMapping(value = "/getCityCode")
    public ServiceData<String> getCityCode(@RequestParam(name = "cityName") String cityName) {
        String result = emdmService.getCityCode(cityName);
        return ServiceResultUtil.success(result);
    }
}
