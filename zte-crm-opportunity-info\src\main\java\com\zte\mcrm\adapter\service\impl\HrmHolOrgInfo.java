package com.zte.mcrm.adapter.service.impl;

import cn.hutool.core.net.url.UrlPath;
import cn.hutool.core.net.url.UrlQuery;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.constant.CompanyEnum;
import com.zte.mcrm.adapter.constant.HrmConstant;
import com.zte.mcrm.adapter.model.dto.OrgInfoDTO;
import com.zte.mcrm.adapter.service.IHrmHolOrgInfo;
import com.zte.mcrm.feign.InOneAdapterHelper;
import com.zte.mcrm.feign.InOneOrgInfoClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 获取HR组织信息
 *
 * <AUTHOR>
 * @date 2025.04.18 下午3:33
 */
@Slf4j
@Service
public class HrmHolOrgInfo implements IHrmHolOrgInfo {

    @Autowired
    private InOneAdapterHelper inOneAdapterHelper;

    public static final String PGINFO_USERCENTER_GETORGINFO = "/ihol/usercenter/orginfo/usercenter/getOrgInfo";

    @Override
    public OrgInfoDTO getOrgInfo(String deptNo) {
        OrgInfoDTO orgInfoDTO =  new OrgInfoDTO();
        if(StringUtils.isEmpty(deptNo)){
            //如果组织编码为空，默认ZTE租户id和公司id
            orgInfoDTO.setTenantId(CompanyEnum.ZTE.getTenantId());
            orgInfoDTO.setCompanyId(CompanyEnum.ZTE.getCompanyId());
            return orgInfoDTO;
        }
        UrlPath urlPath = UrlPath.of(PGINFO_USERCENTER_GETORGINFO, StandardCharsets.UTF_8);
        // body参数
        Map<String, Object> reqMap = Maps.newHashMap();
        List<String> ids = Lists.newArrayList();
        ids.add(deptNo);
        reqMap.put("ids", ids);
        reqMap.put("idType", HrmConstant.ID_TYPES);
        reqMap.put("ver", HrmConstant.QUERY_VERSION_V3);
        reqMap.put("msname", HrmConstant.MS_NAME);
        // 执行请求
        try {
            String body = JSON.toJSONString(reqMap);
            Optional<Object> boOptional = inOneAdapterHelper.doPost(urlPath, new UrlQuery(), body);
            if(!boOptional.isPresent()) {
                throw new BusiException(RetCode.BUSINESSERROR_CODE, deptNo + " organization code HR organization information does not exist.");
            }
            JSONObject resultJsonObj = JSON.parseObject(JSON.toJSONString(boOptional.get()));
            String result = resultJsonObj.getString(deptNo);
            return JSON.parseObject(result,OrgInfoDTO.class);
        }catch (Exception e){
            log.error("getOrgInfo error, rowId:{}", JSON.toJSONString(reqMap), e);
            throw new BusiException(RetCode.BUSINESSERROR_CODE, " HR organization Service Error!");
        }
    }
}
