# 第2章 系统架构设计

> **子代理2负责**: 作为系统架构师，设计系统的整体架构，包括技术架构、部署架构和安全架构，确保系统的可扩展性、高可用性和安全性。

## 2.1 架构设计原则

### 2.1.1 设计理念

#### 微服务架构
采用微服务架构模式，将系统拆分为多个独立的服务单元，每个服务专注于特定的业务功能，支持独立开发、部署和扩展。

#### 云原生设计
基于云原生理念设计，充分利用容器化、服务网格、DevOps等现代技术，提升系统的弹性和运维效率。

#### 领域驱动设计（DDD）
采用领域驱动设计方法，以业务领域为核心组织代码结构，确保系统架构与业务模型的一致性。

### 2.1.2 架构原则

#### 高可用性原则
- **无单点故障**: 所有关键组件都有冗余备份
- **故障隔离**: 单个服务故障不影响整体系统运行
- **快速恢复**: 故障发生后能够快速自动恢复

#### 高性能原则
- **水平扩展**: 支持通过增加节点提升系统处理能力
- **缓存优化**: 多层缓存策略减少数据库压力
- **异步处理**: 耗时操作采用异步处理模式

#### 安全性原则
- **纵深防御**: 多层安全防护机制
- **最小权限**: 用户和服务只拥有必要的最小权限
- **数据保护**: 敏感数据加密存储和传输

#### 可维护性原则
- **模块化设计**: 清晰的模块边界和接口定义
- **标准化**: 统一的开发规范和技术标准
- **可观测性**: 完善的日志、监控和链路追踪

## 2.2 技术架构设计

### 2.2.1 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        用户层                                │
├─────────────────────────────────────────────────────────────┤
│  Web前端     │  移动端     │  Agent交互   │  第三方集成      │
│  (Vue.js)    │  (H5)       │  (Chat UI)   │  (API)          │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      接入层                                  │
├─────────────────────────────────────────────────────────────┤
│           API Gateway (Kong/Nginx)                          │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
│  │ 路由转发     │ 负载均衡     │ 限流熔断     │ 安全认证     │   │
│  └─────────────┴─────────────┴─────────────┴─────────────┘   │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      业务层                                  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
│  │ 任务管理服务 │ 条目管理服务 │ AI应答服务  │ 用户管理服务 │   │
│  │ (Task)      │ (Item)      │ (AI)        │ (User)      │   │
│  └─────────────┴─────────────┴─────────────┴─────────────┘   │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
│  │ 数据分析服务 │ 文件管理服务 │ 通知服务     │ Agent服务   │   │
│  │ (Analytics) │ (File)      │ (Notify)    │ (Agent)     │   │
│  └─────────────┴─────────────┴─────────────┴─────────────┘   │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      数据层                                  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
│  │ MySQL       │ Redis       │ Elasticsearch│ MinIO      │   │
│  │ (主数据库)   │ (缓存)      │ (搜索引擎)   │ (文件存储)  │   │
│  └─────────────┴─────────────┴─────────────┴─────────────┘   │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    外部服务层                                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
│  │ GBBS系统    │ 华为云AI    │ 企业认证     │ 邮件服务     │   │
│  │ (数据源)    │ (大模型)    │ (SSO)       │ (SMTP)      │   │
│  └─────────────┴─────────────┴─────────────┴─────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### 2.2.2 微服务拆分

#### 核心业务服务

**1. 任务管理服务 (soc-task-service)**
- 职责：任务的创建、编辑、删除、查询
- 技术栈：Spring Boot + MySQL + Redis
- 端口：8081

**2. 条目管理服务 (soc-item-service)**
- 职责：条目的增删改查、批量操作、状态管理
- 技术栈：Spring Boot + MySQL + Redis
- 端口：8082

**3. AI应答服务 (soc-ai-service)**
- 职责：AI智能匹配、应答生成、结果评估
- 技术栈：Spring Boot + Python + 华为云AI
- 端口：8083

**4. 用户管理服务 (soc-user-service)**
- 职责：用户认证、权限管理、用户信息维护
- 技术栈：Spring Boot + MySQL + Redis
- 端口：8084

#### 支撑服务

**5. 数据分析服务 (soc-analytics-service)**
- 职责：数据统计、报表生成、趋势分析
- 技术栈：Spring Boot + Elasticsearch + MySQL
- 端口：8085

**6. 文件管理服务 (soc-file-service)**
- 职责：文件上传、下载、存储管理
- 技术栈：Spring Boot + MinIO
- 端口：8086

**7. 通知服务 (soc-notification-service)**
- 职责：消息推送、邮件通知、WebSocket通信
- 技术栈：Spring Boot + RabbitMQ + WebSocket
- 端口：8087

**8. Agent服务 (soc-agent-service)**
- 职责：自然语言处理、对话管理、工具调用
- 技术栈：Spring Boot + Python + 大模型API
- 端口：8088

### 2.2.3 技术选型详解

#### 后端技术栈

**开发框架**
- **Spring Boot 3.2**: 主流Java开发框架，生态完善
- **Spring Cloud 2023**: 微服务治理框架
- **Spring Security 6**: 安全认证框架
- **MyBatis Plus 3.5**: ORM框架，简化数据库操作

**数据存储**
- **MySQL 8.0**: 主数据库，支持JSON字段和全文索引
- **Redis 7.0**: 缓存和会话存储
- **Elasticsearch 8.0**: 全文搜索和数据分析
- **MinIO**: 对象存储，兼容S3协议

**消息中间件**
- **RabbitMQ 3.12**: 消息队列，支持异步处理
- **WebSocket**: 实时通信

#### 前端技术栈

**开发框架**
- **Vue 3.4**: 渐进式JavaScript框架
- **TypeScript 5.0**: 类型安全的JavaScript
- **Vite 5.0**: 快速构建工具

**UI组件**
- **Ant Design Vue 4.0**: 企业级UI组件库
- **ECharts 5.0**: 数据可视化图表库

**状态管理**
- **Pinia 2.0**: Vue状态管理库
- **VueUse**: Vue组合式API工具集

#### 基础设施

**容器化**
- **Docker 24.0**: 容器化平台
- **Kubernetes 1.28**: 容器编排平台
- **Helm 3.0**: Kubernetes包管理工具

**服务网格**
- **Istio 1.20**: 服务网格，提供流量管理、安全、观测性

**监控运维**
- **Prometheus**: 监控数据收集
- **Grafana**: 监控数据可视化
- **Jaeger**: 分布式链路追踪
- **ELK Stack**: 日志收集和分析

## 2.3 部署架构设计

### 2.3.1 环境规划

#### 开发环境 (DEV)
```
┌─────────────────────────────────────────┐
│              开发环境                    │
├─────────────────────────────────────────┤
│  服务器配置：                            │
│  - CPU: 4核                            │
│  - 内存: 8GB                           │
│  - 存储: 100GB SSD                     │
│  - 数量: 2台                           │
│                                        │
│  部署方式：                             │
│  - Docker Compose                     │
│  - 单机部署                            │
│  - 开发调试优化                         │
└─────────────────────────────────────────┘
```

#### 测试环境 (TEST)
```
┌─────────────────────────────────────────┐
│              测试环境                    │
├─────────────────────────────────────────┤
│  服务器配置：                            │
│  - CPU: 8核                            │
│  - 内存: 16GB                          │
│  - 存储: 200GB SSD                     │
│  - 数量: 3台                           │
│                                        │
│  部署方式：                             │
│  - Kubernetes                         │
│  - 模拟生产环境                         │
│  - 自动化测试                          │
└─────────────────────────────────────────┘
```

#### 生产环境 (PROD)
```
┌─────────────────────────────────────────┐
│              生产环境                    │
├─────────────────────────────────────────┤
│  服务器配置：                            │
│  - CPU: 16核                           │
│  - 内存: 32GB                          │
│  - 存储: 500GB SSD                     │
│  - 数量: 6台（3主3备）                  │
│                                        │
│  部署方式：                             │
│  - Kubernetes集群                      │
│  - 高可用部署                          │
│  - 自动扩缩容                          │
└─────────────────────────────────────────┘
```

### 2.3.2 网络架构

#### 网络拓扑图
```
Internet
    │
┌───▼────┐    ┌─────────────┐
│  CDN   │    │  负载均衡    │
│        │    │  (F5/Nginx) │
└───┬────┘    └─────┬───────┘
    │               │
┌───▼───────────────▼───┐
│      DMZ区域          │
│  ┌─────────────────┐  │
│  │   Web服务器     │  │
│  │   (Nginx)       │  │
│  └─────────────────┘  │
└───────────┬───────────┘
            │
┌───────────▼───────────┐
│     应用服务区         │
│  ┌─────────────────┐  │
│  │  Kubernetes     │  │
│  │  集群           │  │
│  │  (微服务)       │  │
│  └─────────────────┘  │
└───────────┬───────────┘
            │
┌───────────▼───────────┐
│     数据服务区         │
│  ┌─────────────────┐  │
│  │  MySQL集群      │  │
│  │  Redis集群      │  │
│  │  ES集群         │  │
│  └─────────────────┘  │
└───────────────────────┘
```

#### 网络安全策略
1. **防火墙规则**: 严格控制端口开放，只允许必要的通信
2. **网络隔离**: 不同层级之间通过VLAN隔离
3. **VPN接入**: 管理员通过VPN访问内网资源
4. **流量监控**: 实时监控网络流量异常

### 2.3.3 容器化部署

#### Kubernetes集群配置

**Master节点配置**
```yaml
apiVersion: v1
kind: Node
metadata:
  name: k8s-master
spec:
  capacity:
    cpu: "8"
    memory: "16Gi"
    storage: "200Gi"
  roles:
    - master
    - etcd
```

**Worker节点配置**
```yaml
apiVersion: v1
kind: Node
metadata:
  name: k8s-worker
spec:
  capacity:
    cpu: "16"
    memory: "32Gi"
    storage: "500Gi"
  roles:
    - worker
```

#### 服务部署配置

**微服务部署模板**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: soc-task-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: soc-task-service
  template:
    metadata:
      labels:
        app: soc-task-service
    spec:
      containers:
      - name: soc-task-service
        image: soc/task-service:latest
        ports:
        - containerPort: 8081
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: MYSQL_HOST
          value: "mysql-service"
        - name: REDIS_HOST
          value: "redis-service"
```

## 2.4 安全架构设计

### 2.4.1 安全体系架构

#### 多层安全防护
```
┌─────────────────────────────────────────┐
│           网络安全层                     │
│  防火墙 + WAF + DDoS防护 + VPN          │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│           接入安全层                     │
│  API网关 + 限流 + 熔断 + SSL/TLS        │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│           应用安全层                     │
│  身份认证 + 权限控制 + 数据验证          │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│           数据安全层                     │
│  数据加密 + 备份 + 审计日志              │
└─────────────────────────────────────────┘
```

### 2.4.2 身份认证与授权

#### 认证机制
1. **SSO单点登录**: 集成企业统一认证系统
2. **JWT Token**: 无状态的令牌认证机制
3. **多因子认证**: 支持短信、邮箱等多种验证方式

#### 权限模型
```
用户 (User)
  │
  ├── 角色 (Role)
  │    │
  │    └── 权限 (Permission)
  │         │
  │         ├── 功能权限 (Function)
  │         └── 数据权限 (Data)
  │
  └── 产品权限 (Product)
       │
       └── 产品目录树 (Product Tree)
```

### 2.4.3 数据安全

#### 数据分类分级
- **公开数据**: 系统配置、产品目录等
- **内部数据**: 任务信息、条目内容等
- **敏感数据**: 用户密码、认证信息等
- **机密数据**: 客户信息、商业机密等

#### 加密策略
1. **传输加密**: HTTPS/TLS 1.3
2. **存储加密**: AES-256数据库字段加密
3. **密钥管理**: 使用专业密钥管理系统

---

**本章小结**: 系统架构设计采用微服务架构模式，确保系统的可扩展性和高可用性。通过容器化部署和多层安全防护，为SOC智能应答系统提供稳定、安全、高效的技术基础。
