# 第3章 功能模块详细设计

> **子代理3负责**: 作为功能设计专家，详细设计系统的各个功能模块，确保每个功能模块都能满足业务需求，提供清晰的功能规格说明。

## 3.1 功能模块总览

### 3.1.1 模块架构图

```
SOC智能应答系统
├── 任务管理模块
│   ├── 任务创建
│   ├── 任务编辑
│   ├── 任务复制
│   ├── 任务删除
│   └── 任务查询
├── 条目管理模块
│   ├── 条目录入
│   ├── 批量导入
│   ├── 条目查询
│   ├── 批量操作
│   └── 条目导出
├── AI应答模块
│   ├── 智能匹配
│   ├── 应答生成
│   ├── 结果评估
│   └── 匹配详情
├── 人工应答模块
│   ├── 应答编辑
│   ├── 内容审核
│   ├── 历史版本
│   └── AI辅助工具
├── 数据分析模块
│   ├── 进度统计
│   ├── 满足度分析
│   ├── 产品维度分析
│   └── 趋势分析
├── 快捷应答模块
│   ├── 快速录入
│   ├── 即时应答
│   └── 个人任务区
├── Agent交互模块
│   ├── 自然语言理解
│   ├── 对话管理
│   ├── 工具调用
│   └── 上下文维护
└── 系统管理模块
    ├── 用户管理
    ├── 权限管理
    ├── 系统配置
    └── 操作日志
```

### 3.1.2 模块依赖关系

```mermaid
graph TD
    A[任务管理模块] --> B[条目管理模块]
    B --> C[AI应答模块]
    B --> D[人工应答模块]
    A --> E[数据分析模块]
    B --> E
    C --> E
    D --> E
    F[快捷应答模块] --> B
    F --> C
    G[Agent交互模块] --> A
    G --> B
    G --> C
    G --> D
    H[系统管理模块] --> A
    H --> B
    H --> C
    H --> D
```

## 3.2 任务管理模块

### 3.2.1 功能概述

任务管理模块是系统的核心入口，负责SOC应答任务的全生命周期管理，包括任务的创建、编辑、复制、删除和查询等功能。

### 3.2.2 详细功能设计

#### 3.2.1 任务创建功能

**功能描述**: 用户可以创建新的SOC应答任务，设置任务基本信息和配置参数。

**输入参数**:
- 任务名称（必填，不可重复）
- 国家/MTO（可选，支持搜索下拉）
- MTO分支（可选，支持搜索下拉）
- 客户（可选，支持搜索下拉）
- 项目（可选，支持搜索和手工输入）
- 数据源（必填，默认GBBS）
- 应答条目文件（可选，支持Excel格式）

**处理流程**:
1. 验证输入参数的有效性
2. 检查任务名称的唯一性
3. 生成唯一的任务编码（格式：TASK001）
4. 创建任务记录并保存到数据库
5. 如果上传了条目文件，触发批量导入流程
6. 返回任务创建结果

**输出结果**:
```json
{
  "taskId": 123,
  "taskCode": "TASK001",
  "message": "任务创建成功"
}
```

#### 3.2.2 任务查询功能

**功能描述**: 支持多条件查询任务列表，提供分页和排序功能。

**查询条件**:
- 任务编码（精确匹配）
- 任务名称（模糊匹配）
- 国家（精确匹配）
- 客户（精确匹配）
- 项目（精确匹配）
- 创建时间范围

**列表字段**:
- 任务编码
- 任务名称
- 国家/MTO
- 客户
- 项目
- 应答条目数
- 应答进度（已应答/总数）
- 总满足度（FC+PC占比）
- 创建人
- 创建时间
- 最近更新时间
- 操作按钮

**权限控制**:
- 任务创建人：可查看和操作所有相关数据
- 条目指派人：只能查看指派给自己的条目相关数据

#### 3.2.3 任务复制功能

**功能描述**: 基于现有任务创建新任务，支持选择是否复制应答结果。

**复制选项**:
- 复制任务基本信息（任务名称自动添加"_复制"后缀）
- 可选择是否复制条目应答结果
- 支持重新上传条目文件

**处理逻辑**:
1. 复制原任务的基本信息
2. 如果选择复制应答结果，则复制所有条目和应答数据
3. 如果不复制应答结果，只复制条目基本信息，应答状态重置为"未应答"
4. 生成新的任务编码和ID

### 3.2.3 业务规则

#### 权限规则
1. 只有具备"SOC智能应答-普通用户"权限的用户才能创建任务
2. 任务创建人拥有任务的完全控制权
3. 条目指派人只能查看和操作指派给自己的条目

#### 数据验证规则
1. 任务名称长度限制：1-200个字符
2. 任务名称不能重复
3. 上传文件大小限制：最大50MB
4. 支持的文件格式：.xls, .xlsx

## 3.3 条目管理模块

### 3.3.1 功能概述

条目管理模块负责SOC应答条目的全生命周期管理，包括条目的录入、查询、批量操作和导出等功能。

### 3.3.2 详细功能设计

#### 3.3.1 条目录入功能

**单条录入**:
- 条目编号（必填）
- 条目描述（必填）
- 产品选择（可选，支持树形选择）
- 标签设置（可选，支持多标签）
- 应答满足度（可选：FC/PC/NC）
- 指派给（可选，默认为当前用户）
- 应答说明（可选，支持富文本）
- 补充信息（可选）
- 自动应答开关（默认开启）
- 重复时覆盖开关（默认开启）
- 备注信息（可选）

**批量导入**:
- 支持Excel文件导入
- 提供标准模板下载
- 实时显示导入进度
- 支持错误数据提示和跳过
- 导入完成后显示成功和失败统计

#### 3.3.2 条目查询功能

**查询条件**:
- 条目编号（精确匹配）
- 条目描述（模糊匹配）
- 产品（精确匹配）
- 应答状态（未应答/应答中/已应答）
- 标签（精确匹配）
- 应答满足度（FC/PC/NC）
- 指派给（精确匹配）
- 应答方式（AI/手工）
- 应答来源（GBBS等）

**列表展示**:
- 支持按条目分组显示，同一条目的不同产品展示在一起
- 支持列的显示/隐藏配置
- 默认显示列：编号、条目描述、产品、应答状态、应答、应答说明、索引、操作
- 支持定时刷新（5秒/次）

#### 3.3.3 批量操作功能

**支持的批量操作**:
1. **开始应答**: 批量触发AI应答
2. **批量删除**: 删除选中的条目
3. **批量添加标签**: 为选中条目添加标签
4. **批量移除标签**: 从选中条目移除标签
5. **设置产品**: 批量修改条目的产品归属
6. **指派给**: 批量修改条目的负责人

**操作权限**:
- 任务创建人：拥有所有批量操作权限
- 条目指派人：只能操作指派给自己的条目，不能进行单条录入和批量录入

#### 3.3.4 条目导出功能

**导出配置**:
- 支持按产品筛选导出
- 支持按条目ID筛选导出
- 导出格式：Excel文件
- 文件命名规则：{任务名称}_导出_{日期}.xlsx

**导出内容**:
- 条目基本信息
- 应答结果
- 匹配详情
- 操作历史

### 3.3.3 状态管理

#### 条目状态定义
1. **未应答**: 条目刚创建，还未开始应答
2. **应答中**: 条目正在进行AI应答处理
3. **已应答**: 条目的所有产品都已完成应答

#### 状态转换规则
```
未应答 → 应答中 → 已应答
  ↑                  ↓
  └──── 重新应答 ←────┘
```

#### 并发控制
- 状态为"应答中"的条目不支持编辑和重复应答
- 通过数据库乐观锁防止并发修改冲突

## 3.4 AI应答模块

### 3.4.1 功能概述

AI应答模块是系统的核心智能功能，负责基于GBBS等数据源进行智能匹配和应答生成，显著提升应答效率和质量。

### 3.4.2 详细功能设计

#### 3.4.1 智能匹配功能

**匹配策略**:
1. **产品筛选**: 首先基于产品进行数据筛选
2. **语义匹配**: 使用大模型进行条目描述的语义相似度计算
3. **上下文匹配**: 结合国家、分支、客户等上下文信息
4. **历史匹配**: 参考历史应答数据进行匹配优化

**匹配评分**:
- 语义相似度：占权重60%
- 上下文匹配度：占权重30%
- 历史匹配度：占权重10%
- 综合评分范围：0-100分

**匹配结果**:
- 每个条目返回Top10匹配结果
- 按匹配度降序排列
- 包含匹配详情和评分说明

#### 3.4.2 应答生成功能

**生成流程**:
1. 获取最佳匹配结果
2. 提取匹配内容的关键信息
3. 结合条目描述和补充信息
4. 调用大模型生成应答内容
5. 进行内容质量检查和优化
6. 返回生成结果

**生成策略**:
- 优先使用匹配度最高的结果
- 如果匹配度低于阈值（70%），标记为需要人工审核
- 支持多个匹配结果的融合生成

#### 3.4.3 结果评估功能

**评估维度**:
1. **匹配度评估**: 基于语义相似度和上下文匹配
2. **完整性评估**: 检查应答内容的完整性
3. **准确性评估**: 基于历史数据验证准确性
4. **一致性评估**: 与同类条目应答的一致性

**质量分级**:
- A级（90-100分）：高质量，可直接使用
- B级（70-89分）：良好，建议人工审核
- C级（50-69分）：一般，需要人工修改
- D级（<50分）：较差，建议重新应答

### 3.4.3 异步处理机制

#### 任务队列设计
```
AI应答请求 → 任务队列 → 工作进程 → 结果回写
     ↓           ↓          ↓         ↓
   请求验证   任务调度   AI处理   状态更新
```

#### 进度跟踪
- 实时更新处理进度
- 通过WebSocket推送进度信息
- 支持任务取消和重试机制

## 3.5 人工应答模块

### 3.5.1 功能概述

人工应答模块为用户提供手工编辑和优化AI应答结果的能力，确保应答质量和准确性。

### 3.5.2 详细功能设计

#### 3.5.1 应答编辑功能

**编辑界面**:
- 条目信息展示区
- 应答结果编辑区
- 匹配详情查看区
- 历史版本对比区

**编辑功能**:
- 满足度修改（FC/PC/NC）
- 应答说明编辑（富文本编辑器）
- 补充信息添加
- 索引链接设置
- 备注信息记录

**AI辅助工具**:
- AI润色：优化应答内容的表达
- AI翻译：支持中英文互译
- 智能提示：基于上下文提供编辑建议

#### 3.5.2 匹配详情查看

**详情展示**:
- 按数据源分组展示匹配结果
- 每个匹配结果显示：匹配度、上下文匹配情况、条目描述、应答内容、索引链接
- 支持匹配结果的筛选和排序

**应用功能**:
- 支持选择任意匹配结果应用到当前应答
- 应用前提示确认，避免误操作
- 应用后自动记录操作历史

#### 3.5.3 版本管理功能

**版本记录**:
- 自动记录每次修改的版本
- 记录修改人、修改时间、修改内容
- 支持版本间的对比查看

**版本恢复**:
- 支持恢复到任意历史版本
- 恢复前显示差异对比
- 恢复后创建新版本记录

## 3.6 数据分析模块

### 3.6.1 功能概述

数据分析模块为用户提供任务执行情况的全面分析，包括进度统计、满足度分析、产品维度分析等，帮助用户了解应答质量和效率。

### 3.6.2 详细功能设计

#### 3.6.1 进度统计功能

**总体进度指标**:
- 总条目数：任务包含的条目总数
- 已应答数：状态为"已应答"的条目数
- 未应答数：状态为"未应答"的条目数
- 应答中数：状态为"应答中"的条目数
- 应答完成率：已应答数/总条目数 × 100%

**满足度统计**:
- FC数量：满足度为"完全满足"的条目数
- PC数量：满足度为"部分满足"的条目数
- NC数量：满足度为"不满足"的条目数
- 总满足度：(FC数量 + PC数量) / 总条目数 × 100%

#### 3.6.2 产品维度分析

**产品统计表**:
- 产品名称
- 总条目数
- 已应答数
- FC数量
- PC数量
- NC数量
- 满足度（按产品计算）

**可视化图表**:
- 产品应答进度柱状图
- 产品满足度饼图
- 应答趋势折线图

#### 3.6.3 权限控制

**数据权限**:
- 任务创建人：可查看全部数据，支持按指派人筛选
- 普通用户：只能查看指派给自己的条目相关数据

## 3.7 快捷应答模块

### 3.7.1 功能概述

快捷应答模块为用户提供快速应答单个条目的能力，无需创建完整任务，适用于临时性和测试性的应答需求。

### 3.7.2 详细功能设计

#### 3.7.1 快速录入功能

**输入界面**:
- 产品选择（必填）
- 国家/MTO（可选）
- MTO分支（可选）
- 客户（可选）
- 条目描述（必填）

**处理流程**:
1. 验证输入参数
2. 自动创建或使用现有个人任务
3. 创建条目记录
4. 触发AI应答流程
5. 跳转到任务详情页面

#### 3.7.2 个人任务区

**个人任务特点**:
- 系统自动创建，用户无法删除
- 任务名称格式："{用户名}的个人任务区"
- 只有创建人可以查看和操作

**管理功能**:
- 支持条目的增删改查
- 支持批量操作
- 支持导出功能

## 3.8 Agent交互模块

### 3.8.1 功能概述

Agent交互模块提供自然语言交互界面，用户可以通过对话方式完成各种操作，提升系统的易用性和智能化水平。

### 3.8.2 详细功能设计

#### 3.8.1 对话管理功能

**对话上下文**:
- 维护用户的对话历史
- 记录当前操作的任务和条目上下文
- 支持多轮对话的上下文传递

**意图识别**:
- 任务管理意图：创建任务、查询任务等
- 条目管理意图：添加条目、查询条目等
- 应答操作意图：开始应答、查看结果等
- 数据分析意图：查看进度、分析满足度等

#### 3.8.2 工具调用功能

**支持的工具**:
1. **创建任务工具**: 解析自然语言创建任务
2. **查询工具**: 查询任务、条目、应答结果
3. **应答工具**: 触发单条或批量应答
4. **导入工具**: 处理Excel文件导入
5. **导出工具**: 生成导出文件
6. **分析工具**: 生成数据分析报告
7. **指派工具**: 批量指派条目

**工具调用流程**:
1. 解析用户输入，识别意图和参数
2. 调用相应的后端API
3. 处理API返回结果
4. 生成用户友好的回复

#### 3.8.3 智能提示功能

**上下文感知提示**:
- 基于当前操作状态提供相关建议
- 智能推荐下一步操作
- 提供快捷操作按钮

**错误处理和引导**:
- 识别用户输入错误，提供纠正建议
- 引导用户完成复杂操作流程
- 提供帮助文档和操作示例

## 3.9 系统管理模块

### 3.9.1 功能概述

系统管理模块提供用户管理、权限管理、系统配置等管理功能，确保系统的安全性和可管理性。

### 3.9.2 详细功能设计

#### 3.9.1 用户管理功能

**用户信息管理**:
- 用户基本信息维护
- 用户角色分配
- 用户状态管理（激活/禁用）

**权限管理**:
- 产品权限分配
- 功能权限控制
- 数据权限隔离

#### 3.9.2 系统配置功能

**数据源配置**:
- GBBS连接配置
- 其他数据源配置
- 数据源状态监控

**系统参数配置**:
- AI应答参数调优
- 系统性能参数
- 业务规则配置

#### 3.9.3 操作日志功能

**日志记录**:
- 用户操作日志
- 系统运行日志
- 错误异常日志

**日志查询**:
- 多条件查询
- 日志导出
- 统计分析

## 3.10 模块间协作机制

### 3.10.1 事件驱动架构

**事件类型**:
- 任务创建事件
- 条目状态变更事件
- AI应答完成事件
- 用户操作事件

**事件处理**:
- 异步事件处理
- 事件重试机制
- 事件审计跟踪

### 3.10.2 数据一致性保证

**事务管理**:
- 分布式事务处理
- 数据一致性检查
- 冲突解决机制

**缓存同步**:
- 缓存更新策略
- 缓存失效机制
- 数据同步监控

---

**本章小结**: 功能模块详细设计涵盖了SOC智能应答系统的所有核心功能，每个模块都有清晰的功能边界和详细的实现规格。通过模块化设计和事件驱动架构，确保系统的可维护性、可扩展性和数据一致性，为用户提供完整、智能、高效的标书应答解决方案。
