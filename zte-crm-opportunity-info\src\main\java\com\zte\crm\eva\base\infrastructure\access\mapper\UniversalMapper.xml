<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zte.crm.eva.base.infrastructure.access.mapper.UniversalMapper">

    <sql id="base_where">
        <if test="whereBO.inMap != null and whereBO.inMap.size() > 0">
            <foreach collection="whereBO.inMap.entrySet()" index="key" item="values">
                and ${key} in
                <foreach collection="values" item="value" open="(" separator="," close=")">
                    #{value}
                </foreach>
            </foreach>
        </if>
        <if test="whereBO.betweenMap != null and whereBO.betweenMap.size() > 0">
            <foreach collection="whereBO.betweenMap.entrySet()" index="key" item="values">
                and ${key} between
                <foreach collection="values" item="value" separator=" and ">
                    #{value}
                </foreach>
            </foreach>
        </if>
        <if test="whereBO.likeMap != null and whereBO.likeMap.size() > 0">
            <foreach collection="whereBO.likeMap.entrySet()" index="key" item="value">
                and ${key} like concat('%', #{value}, '%')
            </foreach>
        </if>
    </sql>

    <select id="selectCommonByPage" resultType="java.util.Map">
        select ${fields}
        from ${tableRealName}
        where 1 = 1
        <include refid="base_where"/>
        <if test="whereBO.orderByMap != null and whereBO.orderByMap.size() == 2">
            order by ${whereBO.orderByMap['sort']} ${whereBO.orderByMap['order']}
        </if>
        <if test="whereBO.limitMap != null and whereBO.limitMap.size() == 2">
            limit #{whereBO.limitMap[startRow]},#{whereBO.limitMap[rowSize]}
        </if>
    </select>

    <select id="selectCommonCount" resultType="java.lang.Long">
        select count(*)
          from ${tableRealName}
         where 1 = 1
         <include refid="base_where"/>
    </select>

    <insert id="insertCommon">
        insert into ${tableName}
        <foreach item="value" collection="map" index="key" open="(" separator="," close=")">
            ${key}
        </foreach>
        values
        <foreach item="value" collection="map" index="key" open="(" separator="," close=")">
            #{value}
        </foreach>
    </insert>
    <insert id="insertBatchCommon">
        <foreach item="item" collection="list" index="key" separator=";">
            insert into ${tableName}
                <foreach item="value" collection="item" index="key" open="(" separator="," close=")">
                    ${key}
                </foreach>
            values
                <foreach item="value" collection="item" index="key" open="(" separator="," close=")">
                    #{value}
                </foreach>
        </foreach>
    </insert>

    <update id="deleteCommons">
        update ${tableName}
        set
        <foreach item="value" collection="map" index="key" separator=",">
            ${key} = #{value}
        </foreach>
        where ${idName} in
        <foreach item="item" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateCommon">
        update ${tableName}
        set
        <foreach item="value" collection="map" index="key" separator=",">
            ${key} = #{value}
        </foreach>
        where ${idName} = #{idValue}
    </update>

    <select id="selectEqCommonCount" resultType="java.lang.Integer">
        select count(*)
        from ${tableName}
        where 1 = 1
        <foreach collection="conditionMap" item="value" index="key">
            <if test="value != null">
                and ${key} = #{value}
            </if>
        </foreach>
    </select>

    <update id="updateSingleCommon">
        update ${tableName} set
        <foreach collection="fieldMap" item="value" index="key" separator=",">
            ${key} = #{value}
        </foreach>
        <if test="updateTimeFlag == 'Y'.toString()">
            <foreach collection="updateTimeMap" item="value" index="key" separator=",">
                ,${key} = #{value}
            </foreach>
        </if>
        <if test="updateTimeFlag == 'N'.toString()">
            <foreach collection="updateTimeMap" item="value" index="key" separator=",">
                ,${key} = ${value}
            </foreach>
        </if>
        where 1 = 1
        <foreach collection="conditionMap" item="value" index="key">
            <if test="value != null">
                and ${key} = #{value}
            </if>
        </foreach>
    </update>

    <select id="selectAggregationByParams" resultType="java.util.Map">
        <if test="aggregationParams != null">
            select
            <foreach collection="aggregationParams.fields" item="field" index="index" separator=",">
                ${field}
            </foreach>
            from ${aggregationParams.tableName}
            where 1=1
            <if test="aggregationParams.whereCondition != null">
                <foreach collection="aggregationParams.whereCondition" item="value" index="key">
                    <if test="value != null">
                        and  ${key} = #{value}
                    </if>
                </foreach>
            </if>
            <if test="aggregationParams.whereConditionFunction != null">
                <foreach collection="aggregationParams.whereConditionFunction" item="value" index="key">
                    <if test="value != null">
                        and  ${key} = ${value}
                    </if>
                </foreach>
            </if>

            <if test="aggregationParams.groupByColumns != null and aggregationParams.groupByColumns.size() > 0">
                group by
                <foreach collection="aggregationParams.groupByColumns" item="column" separator=",">
                    ${column}
                </foreach>
            </if>
            <if test="aggregationParams.havingConditions != null and aggregationParams.havingConditions != ''">
                having ${aggregationParams.havingConditions}
            </if>
            <if test="aggregationParams.orderColumns != null and aggregationParams.orderColumns.size() > 0">
                order by
                <foreach collection="aggregationParams.orderColumns" item="column" separator=",">
                    ${column}
                </foreach>
            </if>
            <if test="aggregationParams.limitParams != null and aggregationParams.limitParams != ''">
                limit ${aggregationParams.limitParams}
            </if>
        </if>
    </select>

</mapper>