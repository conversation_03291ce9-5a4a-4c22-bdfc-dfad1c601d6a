package com.zte.mcrm.channel.dao;

import com.zte.mcrm.channel.model.entity.OpportunityProduct;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 *  数据访问接口类 
 * <AUTHOR>
 * @date 2021/09/14 
 */
@Mapper
public interface OpportunityProductDao {
    /**
     * 根据主键查询
     * <AUTHOR>
     * @param rowId 主键
     * @date 2021/09/14 
     * @return 实体
     */
	OpportunityProduct get(@Param("rowId")String rowId);

	List<OpportunityProduct> getOldOpportunityProductsByRowId(@Param("rowId")String rowId);

	/**
	 *
	 * @param optyIds
	 * @return
	 */
	List<OpportunityProduct> getMainProdsRelationByOptyIds(@Param("optyIds") List<String> optyIds);

	/**
	 * 根据商机id获取商机产品信息
	 * @param opptyId
	 * @return
	 */
	int batchLogicDeleteByOptyId(@Param("opptyId")String opptyId, @Param("businessType")String businessType);
	
    /**
     * 查询列表
     * <AUTHOR>
     * @param map 查询条件
     * @date 2021/09/14 
     * @return 实体集合
     */
	List<OpportunityProduct> getList(Map<String, Object> map);
	
	/**
     * 软删除，enabled_flag字段更新为N
     * <AUTHOR>
     * @param rowId 主键
     * @date 2021/09/14 
     * @return 删除总数
     */
	int softDelete(@Param("rowId")String rowId,@Param("enableFlag")String enableFlag);


	/**
	 * 批量软删除，enabled_flag字段更新为N
	 * <AUTHOR>
	 * @date 2021/09/14
	 * @return 删除总数
	 */
	int softDeleteByOpptyId(@Param("opptyId")String opptyId,@Param("enableFlag")String enableFlag);


	/**
     * 删除
     * <AUTHOR>
     * @param rowId 主键
     * @date 2021/09/14 
     * @return 删除总数
     */	
	int delete(@Param("rowId")String rowId);

	/**
	 * 删除
	 * <AUTHOR>
	 * @date 2021/09/14
	 * @return 删除总数
	 */
	int deleteByOpptyId(@Param("optyIds")List<String> optyIds);


    /**
     * 动态新增
     * <AUTHOR>
     * @param entity 新增实体
     * @date 2021/09/14 
     * @return 新增总数
     */	
	int insert(OpportunityProduct entity);

    /**
     * 批量新增
     * <AUTHOR>
     * @param list 新增实体集合
     * @date 2021/09/14 
     * @return 新增总数
     */	
	int insertByBatch(List<OpportunityProduct> list);

    /**
     * 更新
     * <AUTHOR>
     * @param entity 更新条件
     * @date 2021/09/14 
     * @return 更新影响总数
     */		
	int update(OpportunityProduct entity);

    /**
     * 统计
     * <AUTHOR>
     * @param map 查询条件
     * @date 2021/09/14 
     * @return 统计总数
     */	
	long getCount(Map<String, Object> map);

    /**
     * 分页查询
     * <AUTHOR>
     * @param map 查询条件
     * @date 2021/09/14 
     * @return 实体集合
     */	
	List<OpportunityProduct> getPage(Map<String, Object> map);
}
