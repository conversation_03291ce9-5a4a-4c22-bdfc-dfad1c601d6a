package com.zte.mcrm.account.access.vo;

import java.io.Serializable;
import java.text.DecimalFormat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;


/**
 * <AUTHOR>
 * @Created by **********
 * @Date 2019/8/7
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreditVO implements Serializable {

	private static final long serialVersionUID = -3108148375161726600L;
	@ApiModelProperty("关联客户")
	private Account account;
	
	/**
	 * 财务评级
	 * */
	@ApiModelProperty("财务评级")
	private String finacialLevel;
	private String finacialLevelCode;
	/**
	 * 信用评级
	 * */
	@ApiModelProperty("信用评级")
	private String creditLevel;
	private String creditLevelCode;
	/**
	 * 综合评级
	 * */
	@ApiModelProperty("综合评级")
	private String totalRating;
	private String totalRatingCode;
	/**
	 * 评级分数
	 * */
	@ApiModelProperty("评级分数")
	private String ratingSocre;
	/**
	 * 信用额度（万）
	 * */
	@ApiModelProperty("信用额度（万）")
	private String creditLimit;
	/**
	 * 可用信用额度（万）
	 * */
	@ApiModelProperty("可信息额度")
	private String availableCredit;
	/**
	 * 生效日期
	 * */
	@ApiModelProperty("生效日期")
	private String effectDate;
	/**
	 * 失效日期
	 * */
	@ApiModelProperty("失效日期")
	private String closeDate;
	/**
	 * 币种
	 * */
	@ApiModelProperty("币种")
	private String currencyCode;
	/**
	 * 总资产(万原币):
	 * */
	@ApiModelProperty("总资产(万原币)")
	private String totalAsset;
	/**
	 * 净资产(万原币):
	 * */
	@ApiModelProperty("净资产(万原币)")
	private String netAsset;
	/**
	 * 总收入(万原币):
	 * */
	@ApiModelProperty("总收入(万原币)")
	private String totalIncome;
	/**
	 * 财务评级
     * EBITDA(万原币)
	 * */
	@ApiModelProperty("EBITDA(万原币)")
	@JsonProperty("EBITDA")
	private String ebitda;
	/**
	 * 净利润(万原币)
	 * */
	@ApiModelProperty("净利润(万原币)")
	private String netProfit;
	/**
	 * 总资产(万USD):
	 * */
	@ApiModelProperty("总资产(万USD)")
	private String totalAssetUSD;
	/**
	 * 净资产(万USD):
	 * */
	@ApiModelProperty("净资产(万USD)")
	private String netAssetUSD;
	/**
	 * 总收入(万USD):
	 * */
	@ApiModelProperty("总收入(万USD)")
	private String totalIncomeUSD;
	/**
	 * EBITDA(万USD)
	 * */
	@ApiModelProperty("EBITDA(万USD)")
	@JsonProperty("EBITDAUSD")
	private String ebitdaUsd;
	/**
	 * 财务评级
     * 净利润(万USD)
	 * */
	@ApiModelProperty("净利润(万USD)")
	private String netProfitUSD;
	/**
	 * 财年截止日
	 * */
	@ApiModelProperty("财年截止日")
	private String deadLine;
	/**
	 * 汇率
	 * */
	@ApiModelProperty("汇率")
	private String exchangeRate;
	/**
	 * 财务弹性
	 * */
	@ApiModelProperty("/财务弹性")
	private String financeFex;
	/**
	 * EBITDA占收入比
	 * */
	@ApiModelProperty("EBITDA占收入比")
	@JsonProperty("EBITDARate")
	private String ebitdaRate;
	/**
	 * 资产负债率
	 * */
	@ApiModelProperty("资产负债率")
	private String debtRate;
	/**
	 * 净利润率:
	 * */
	@ApiModelProperty("净利润率")
	private String netProfitRate;
	/**
	 * 牌照覆盖范围
	 * */
	@ApiModelProperty("牌照覆盖范围")
	private String licenceCover;
	private String licenceCoverCode;
	/**
	 * 移动网_用户数(万)
	 * */
	@ApiModelProperty("移动网_用户数(万)")
	private String mobileUser;
	/**
	 * 移动网_市场排名
	 * */
	@ApiModelProperty("移动网_市场排名")
	private String mobileRank;
	/**
	 * 移动网_市场份额(%)
	 * */
	@ApiModelProperty("移动网_市场份额(%)")
	private String mobileRate;
	/**
	 * 移动网_ARPU值(USD)
	 * */
	@ApiModelProperty("移动网_ARPU值(USD)")
	private String mobileArpu;
	/**
	 * 移动网_渗透率
	 * */
	@ApiModelProperty("移动网_渗透率")
	private String mobilePenetrance;
	/**
	 * 移动网_用户增长率(%)
	 * */
	@ApiModelProperty("移动网_用户增长率(%)")
	private String mobileUserIncrement;
	/**
	 * 固网_用户数(万)
	 * */
	@ApiModelProperty("固网_用户数(万)")
	private String fixUser;
	/**
	 * 固网_市场排名
	 * */
	@ApiModelProperty("固网_市场排名")
	private String fixRank;
	/**
	 * 固网_市场份额(%)
	 * */
	@ApiModelProperty("固网_市场份额(%)")
	private String fixRate;
	/**
	 * 固网_ARPU值(USD)
	 * */
	@ApiModelProperty("固网_ARPU值(USD)")
	private String fixArpu;
	/**
	 * 固网_渗透率(%)
	 * */
	@ApiModelProperty("固网_渗透率(%)")
	private String fixPenetrance;
	/**
	 * 固网_用户增长率(%)
	 * */
	@ApiModelProperty("固网_用户增长率(%)")
	private String fixUserIncrement;
	/**
	 * 宽带_用户数(万)
	 * */
	@ApiModelProperty("宽带_用户数(万)")
	private String broadUser;
	/**
	 * 宽带_市场排名
	 * */
	private String broadRank;
	/**
	 * 宽带_市场份额(%)
	 * */
	@ApiModelProperty("宽带_市场份额(%)")
	private String broadRate;
	/**
	 * 宽带_ARPU值(USD)
	 * */
	@ApiModelProperty("宽带_ARPU值(USD)")
	private String broadArpu;
	/**
	 * 宽带_渗透率(%)
	 * */
	@ApiModelProperty("宽带_渗透率(%)")
	private String broadPenetrance;
	/**
	 * 宽带_用户增长率(%)
	 * */
	@ApiModelProperty("宽带_用户增长率(%)")
	private String broadUserIncrement;
	/**
	 * 有线电视/IPTV_用户数(万)
	 * */
	@ApiModelProperty("有线电视/IPTV_用户数(万)")
	private String wiredUser;
	/**
	 * 有线电视/IPTV_市场排名
	 * */
	@ApiModelProperty("有线电视/IPTV_市场排名")
	private String wiredRank;
	/**
	 * 有线电视/IPTV_市场份额(%)
	 * */
	@ApiModelProperty("有线电视/IPTV_市场份额(%)")
	private String wiredRate;
	/**
	 * 有线电视/IPTV_ARPU值(USD)
	 * */
	@ApiModelProperty("有线电视/IPTV_ARPU值(USD)")
	private String wiredArpu;
	/**
	 * 有线电视/IPTV_用户增长率(%)
	 * */
	@ApiModelProperty("有线电视/IPTV_用户增长率(%)")
	private String wiredUserIncrements;
	/**
	 * 有线电视/IPTV_渗透率(%)
	 * */
	@ApiModelProperty("有线电视/IPTV_渗透率(%)")
	private String wiredPenetrance;
	/**
	 * 状态码
	 * */
	@ApiModelProperty("状态码")
	private String statusCode;
	/**
	 * 状态
	 * */
	@ApiModelProperty("状态")
	private String status;
	/**
	 * 是否当前版本
	 * */
	@ApiModelProperty("是否当前版本")
	private String activeFlag;
	/**
	 * 历史交易情况 ZTE_TRANSACTION_RECORD
	 * */
	@ApiModelProperty("历史交易情况 ZTE_TRANSACTION_RECORD")
	private String transactionCode;   
	private String transaction;

	/**
	 * 子公司欠款总额   RECORD_19
	 * */
	@ApiModelProperty("子公司欠款总额   RECORD_19")
	private String	totalArrearsOfSubsidiaries;
	/**
	 * 系统_0-3 (含)   RECORD_01
	 * */
	@ApiModelProperty("系统_0-3 (含)   RECORD_01")
	@JsonProperty("system0_3")
	private String system03;
	/**
	 * 系统_4-6 (含)   RECORD_04
	 * */
	@ApiModelProperty("系统_4-6 (含)   RECORD_04")
	@JsonProperty("system4_6")
	private String system46;
	/**
	 * 系统_7-12 (含)  RECORD_07
	 * */
	@ApiModelProperty("系统_7-12 (含)  RECORD_07")
	private String system712;
	/**
	 * 系统_13-24 (含) RECORD_10
	 * */
	@ApiModelProperty("系统_13-24 (含) RECORD_10")
	@JsonProperty("system13_24")
	private String system1324;
	/**
	 * 系统_24-36 (含) RECORD_13
	 * */
	@ApiModelProperty("系统_24-36 (含) RECORD_13")
	@JsonProperty("system24_36")
	private String system2436;
	/**
	 * 系统_应收总金额 RECORD_01 + RECORD_04 + RECORD_07 + RECORD_10 + RECORD_13
	 * */
	@ApiModelProperty("系统_应收总金额 RECORD_01 + RECORD_04 + RECORD_07 + RECORD_10 + RECORD_13")
	private String totalAmountSystemReceivable;
	/**
	 * 手机_0-3 (含)   RECORD_02
	 * */
	@ApiModelProperty("手机_0-3 (含)   RECORD_02")
	@JsonProperty("mobile0_3")
	private String mobile03;
	/**
	 * 手机_4-6 (含)   RECORD_05
	 * */
	@ApiModelProperty("手机_4-6 (含)   RECORD_05")
	@JsonProperty("mobile4_6")
	private String mobile46;
	/**
	 * 手机_7-12 (含)  RECORD_08
	 * */
	@ApiModelProperty("手机_7-12 (含)  RECORD_08")
	private String mobile712;
	/**
	 * 手机_13-24 (含) RECORD_11
	 * */
	@ApiModelProperty("手机_13-24 (含) RECORD_11")
	@JsonProperty("mobile13_24")
	private String mobile1324;
	/**
	 * 手机_24-36 (含) RECORD_14
	 * */
	@ApiModelProperty("手机_24-36 (含) RECORD_14")
	@JsonProperty("mobile24_36")
	private String mobile2436;
	/**
	 * 手机_应收总金额 RECORD_02 + RECORD_05 + RECORD_08 + RECORD_11 + RECORD_14
	 * */
	@ApiModelProperty("手机_应收总金额 RECORD_02 + RECORD_05 + RECORD_08 + RECORD_11 + RECORD_14")
	private String totalAmountMobileReceivable;
	/**
	 * 应收工程合约款_0-3 (含) RECORD_03
	 * */
	@ApiModelProperty("应收工程合约款_0-3 (含) RECORD_03")
	@JsonProperty("engineering0_3")
	private String engineering03;
	/**
	 * 财务评级
     * 应收工程合约款_4-6 (含) RECORD_06
	 * */
	@ApiModelProperty("应收工程合约款_4-6 (含) RECORD_06")
	@JsonProperty("engineering4_6")
	private String engineering46;
	/**
	 * 应收工程合约款_7-12 (含) RECORD_09
	 * */
	@ApiModelProperty("应收工程合约款_7-12 (含) RECORD_09")
	@JsonProperty("engineering7_12")
	private String engineering712;
	/**
	 * 应收工程合约款_13-24 (含) RECORD_12
	 * */
	@ApiModelProperty("应收工程合约款_13-24 (含) RECORD_12")
	@JsonProperty("engineering13_24")
	private String engineering1324;
	/**
	 * 应收工程合约款_24-36 (含) RECORD_15
	 * */
	@ApiModelProperty("应收工程合约款_24-36 (含) RECORD_15")
	@JsonProperty("engineering24_36")
	private String engineering2436;
	/**
	 * 应收工程合约款_应收总金额 RECORD_03 + RECORD_06 + RECORD_09 + RECORD_12 + RECORD_15
	 * */
	@ApiModelProperty("应收工程合约款_应收总金额 RECORD_03 + RECORD_06 + RECORD_09 + RECORD_12 + RECORD_15")
	private String totalAmountEngineeringReceivable;
	
	@ApiModelProperty("客户信用风险提示")
	private String accntRistTip;

	public String getEbitdaRate() {
		return ebitdaRate;
	}

	public void setEbitdaRate(String ebitdaRate) {
		this.ebitdaRate = ebitdaRate;
	}

	public String getEbitdaUsd() {
		return ebitdaUsd;
	}

	public void setEbitdaUsd(String ebitdaUsd) {
		this.ebitdaUsd = ebitdaUsd;
	}

	public String getAccntRistTip() {
		return accntRistTip;
	}
	public void setAccntRistTip(String accntRistTip) {
		this.accntRistTip = accntRistTip;
	}
	public String getFinacialLevelCode() {
		return finacialLevelCode;
	}
	public void setFinacialLevelCode(String finacialLevelCode) {
		this.finacialLevelCode = finacialLevelCode;
	}
	public String getCreditLevelCode() {
		return creditLevelCode;
	}
	public void setCreditLevelCode(String creditLevelCode) {
		this.creditLevelCode = creditLevelCode;
	}
	public String getTotalRatingCode() {
		return totalRatingCode;
	}
	public void setTotalRatingCode(String totalRatingCode) {
		this.totalRatingCode = totalRatingCode;
	}
	public String getLicenceCoverCode() {
		return licenceCoverCode;
	}
	public void setLicenceCoverCode(String licenceCoverCode) {
		this.licenceCoverCode = licenceCoverCode;
	}
	public String getTransactionCode() {
		return transactionCode;
	}
	public void setTransactionCode(String transactionCode) {
		this.transactionCode = transactionCode;
	}
	public String getTransaction() {
		return transaction;
	}
	public void setTransaction(String transaction) {
		this.transaction = transaction;
	}
	public String getTotalArrearsOfSubsidiaries() {
		return totalArrearsOfSubsidiaries;
	}
	public void setTotalArrearsOfSubsidiaries(String totalArrearsOfSubsidiaries) {
		this.totalArrearsOfSubsidiaries = totalArrearsOfSubsidiaries;
	}

	public String getSystem03() {
		return system03;
	}

	public void setSystem03(String system03) {
		this.system03 = system03;
	}

	public String getSystem46() {
		return system46;
	}

	public void setSystem46(String system46) {
		this.system46 = system46;
	}

	public String getSystem712() {
		return system712;
	}
	public void setSystem712(String system712) {
		this.system712 = system712;
	}
	
	public String getTotalAmountSystemReceivable() {
		return totalAmountSystemReceivable;
	}
	public void setTotalAmountSystemReceivable(String totalAmountSystemReceivable) {
		this.totalAmountSystemReceivable = totalAmountSystemReceivable;
	}
	
	public String getMobile712() {
		return mobile712;
	}
	public void setMobile712(String mobile712) {
		this.mobile712 = mobile712;
	}
	
	public String getTotalAmountMobileReceivable() {
		return totalAmountMobileReceivable;
	}
	public void setTotalAmountMobileReceivable(String totalAmountMobileReceivable) {
		this.totalAmountMobileReceivable = totalAmountMobileReceivable;
	}
	
	public String getTotalAmountEngineeringReceivable() {
		return totalAmountEngineeringReceivable;
	}
	public void setTotalAmountEngineeringReceivable(
			String totalAmountEngineeringReceivable) {
		this.totalAmountEngineeringReceivable = totalAmountEngineeringReceivable;
	}
	public void setRatingSocre(String ratingSocre) {
		this.ratingSocre = ratingSocre;
	}
	public void setCreditLimit(String creditLimit) {
		this.creditLimit = creditLimit;
	}
	public void setAvailableCredit(String availableCredit) {
		this.availableCredit = availableCredit;
	}
	public void setTotalAsset(String totalAsset) {
		this.totalAsset = totalAsset;
	}
	public void setNetAsset(String netAsset) {
		this.netAsset = netAsset;
	}
	public void setTotalIncome(String totalIncome) {
		this.totalIncome = totalIncome;
	}

	public String getEbitda() {
		return ebitda;
	}

	public void setEbitda(String ebitda) {
		this.ebitda = ebitda;
	}

	public void setNetProfit(String netProfit) {
		this.netProfit = netProfit;
	}
	public void setTotalAssetUSD(String totalAssetUSD) {
		this.totalAssetUSD = totalAssetUSD;
	}
	public void setNetAssetUSD(String netAssetUSD) {
		this.netAssetUSD = netAssetUSD;
	}
	public void setTotalIncomeUSD(String totalIncomeUSD) {
		this.totalIncomeUSD = totalIncomeUSD;
	}

	public void setNetProfitUSD(String netProfitUSD) {
		this.netProfitUSD = netProfitUSD;
	}
	public void setExchangeRate(String exchangeRate) {
		this.exchangeRate = exchangeRate;
	}
	public void setDebtRate(String debtRate) {
		this.debtRate = debtRate;
	}
	public void setNetProfitRate(String netProfitRate) {
		this.netProfitRate = netProfitRate;
	}
	public void setMobileUser(String mobileUser) {
		this.mobileUser = mobileUser;
	}
	public void setMobileRank(String mobileRank) {
		this.mobileRank = mobileRank;
	}
	public void setMobileRate(String mobileRate) {
		this.mobileRate = mobileRate;
	}
	public void setMobileArpu(String mobileArpu) {
		this.mobileArpu = mobileArpu;
	}
	public void setMobilePenetrance(String mobilePenetrance) {
		this.mobilePenetrance = mobilePenetrance;
	}
	public void setMobileUserIncrement(String mobileUserIncrement) {
		this.mobileUserIncrement = mobileUserIncrement;
	}
	public void setFixUser(String fixUser) {
		this.fixUser = fixUser;
	}
	public void setFixRank(String fixRank) {
		this.fixRank = fixRank;
	}
	public void setFixRate(String fixRate) {
		this.fixRate = fixRate;
	}
	public void setFixArpu(String fixArpu) {
		this.fixArpu = fixArpu;
	}
	public void setFixPenetrance(String fixPenetrance) {
		this.fixPenetrance = fixPenetrance;
	}
	public void setFixUserIncrement(String fixUserIncrement) {
		this.fixUserIncrement = fixUserIncrement;
	}
	public void setBroadUser(String broadUser) {
		this.broadUser = broadUser;
	}
	public void setBroadRank(String broadRank) {
		this.broadRank = broadRank;
	}
	public void setBroadRate(String broadRate) {
		this.broadRate = broadRate;
	}
	public void setBroadArpu(String broadArpu) {
		this.broadArpu = broadArpu;
	}
	public void setBroadPenetrance(String broadPenetrance) {
		this.broadPenetrance = broadPenetrance;
	}
	public void setBroadUserIncrement(String broadUserIncrement) {
		this.broadUserIncrement = broadUserIncrement;
	}
	public void setWiredUser(String wiredUser) {
		this.wiredUser = wiredUser;
	}
	public void setWiredRank(String wiredRank) {
		this.wiredRank = wiredRank;
	}
	public void setWiredRate(String wiredRate) {
		this.wiredRate = wiredRate;
	}
	public void setWiredArpu(String wiredArpu) {
		this.wiredArpu = wiredArpu;
	}
	public void setWiredUserIncrements(String wiredUserIncrements) {
		this.wiredUserIncrements = wiredUserIncrements;
	}
	public void setWiredPenetrance(String wiredPenetrance) {
		this.wiredPenetrance = wiredPenetrance;
	}
	public String getLicenceCover() {
		return licenceCover;
	}
	public void setLicenceCover(String licenceCover) {
		this.licenceCover = licenceCover;
	}
	public String getMobileUser() {
		return mobileUser;
	}
	public void setMobileUser(double mobileUser) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.mobileUser = df.format(mobileUser);
	}
	public String getMobileRank() {
		return mobileRank;
	}
	public void setMobileRank(double mobileRank) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.mobileRank =df.format(mobileRank);
	}
	public String getMobileRate() {
		return mobileRate;
	}
	public void setMobileRate(double mobileRate) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.mobileRate = df.format(mobileRate);
	}

	public String getMobileArpu() {
		return mobileArpu;
	}
	public void setMobileArpu(double mobileArpu) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.mobileArpu = df.format(mobileArpu);
	}
	public String getMobilePenetrance() {
		return mobilePenetrance;
	}
	public void setMobilePenetrance(double mobilePenetrance) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.mobilePenetrance = df.format(mobilePenetrance);
	}
	public String getMobileUserIncrement() {
		return mobileUserIncrement;
	}
	public void setMobileUserIncrement(double mobileUserIncrement) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.mobileUserIncrement = df.format(mobileUserIncrement);
	}
	public String getFixUser() {
		return fixUser;
	}
	public void setFixUser(double fixUser) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.fixUser = df.format(fixUser);
	}
	public String getFixRank() {
		return fixRank;
	}
	public void setFixRank(double fixRank) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.fixRank = df.format(fixRank);
	}
	public String getFixRate() {
		return fixRate;
	}
	public void setFixRate(double fixRate) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.fixRate = df.format(fixRate);
	}
	public String getFixArpu() {
		return fixArpu;
	}
	public void setFixArpu(double fixArpu) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.fixArpu = df.format(fixArpu);
	}
	public String getFixPenetrance() {
		return fixPenetrance;
	}
	public void setFixPenetrance(double fixPenetrance) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.fixPenetrance = df.format(fixPenetrance);
	}
	public String getFixUserIncrement() {
		return fixUserIncrement;
	}
	public void setFixUserIncrement(double fixUserIncrement) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.fixUserIncrement = df.format(fixUserIncrement);
	}
	public String getBroadUser() {
		return broadUser;
	}
	public void setBroadUser(double broadUser) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.broadUser = df.format(broadUser);
	}
	public String getBroadRank() {
		return broadRank;
	}
	public void setBroadRank(double broadRank) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.broadRank = df.format(broadRank);
	}
	public String getBroadRate() {
		return broadRate;
	}
	public void setBroadRate(double broadRate) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.broadRate = df.format(broadRate);
	}
	public String getBroadArpu() {
		return broadArpu;
	}
	public void setBroadArpu(double broadArpu) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.broadArpu = df.format(broadArpu);
	}
	public String getBroadPenetrance() {
		return broadPenetrance;
	}
	public void setBroadPenetrance(double broadPenetrance) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.broadPenetrance = df.format(broadPenetrance);
	}
	public String getBroadUserIncrement() {
		return broadUserIncrement;
	}
	public void setBroadUserIncrement(double broadUserIncrement) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.broadUserIncrement = df.format(broadUserIncrement);
	}
	public String getWiredUser() {
		return wiredUser;
	}
	public void setWiredUser(double wiredUser) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.wiredUser = df.format(wiredUser);
	}
	public String getWiredRank() {
		return wiredRank;
	}
	public void setWiredRank(double wiredRank) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.wiredRank = df.format(wiredRank);
	}
	public String getWiredRate() {
		return wiredRate;
	}
	public void setWiredRate(double wiredRate) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.wiredRate = df.format(wiredRate);
	}
	public String getWiredArpu() {
		return wiredArpu;
	}
	public void setWiredArpu(double wiredArpu) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.wiredArpu = df.format(wiredArpu);
	}
	public String getWiredUserIncrements() {
		return wiredUserIncrements;
	}
	public void setWiredUserIncrements(double wiredUserIncrements) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.wiredUserIncrements = df.format(wiredUserIncrements);
	}
	public String getWiredPenetrance() {
		return wiredPenetrance;
	}
	public void setWiredPenetrance(double wiredPenetrance) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.wiredPenetrance = df.format(wiredPenetrance);
	}
	public String getCloseDate() {
		return closeDate;
	}
	public void setCloseDate(String closeDate) {
		this.closeDate = closeDate;
	}
	public String getCurrencyCode() {
		return currencyCode;
	}
	public void setCurrencyCode(String currencyCode) {
		this.currencyCode = currencyCode;
	}
	public String getTotalAsset() {
		return totalAsset;
	}
	public void setTotalAsset(double totalAsset) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.totalAsset = df.format(totalAsset);
	}
	public String getNetAsset() {
		return netAsset;
	}
	public void setNetAsset(double netAsset) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.netAsset = df.format(netAsset);
	}
	public String getTotalIncome() {
		return totalIncome;
	}
	public void setTotalIncome(double totalIncome) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.totalIncome = df.format(totalIncome);
	}
	public String getNetProfit() {
		return netProfit;
	}
	public void setNetProfit(double netProfit) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.netProfit = df.format(netProfit);
	}
	public String getTotalAssetUSD() {
		return totalAssetUSD;
	}
	public void setTotalAssetUSD(double totalAssetUSD) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.totalAssetUSD = df.format(totalAssetUSD);
	}
	public String getNetAssetUSD() {
		return netAssetUSD;
	}
	public void setNetAssetUSD(double netAssetUSD) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.netAssetUSD = df.format(netAssetUSD);
	}
	public String getTotalIncomeUSD() {
		return totalIncomeUSD;
	}
	public void setTotalIncomeUSD(double totalIncomeUSD) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.totalIncomeUSD = df.format(totalIncomeUSD);
	}
	public String getNetProfitUSD() {
		return netProfitUSD;
	}
	public void setNetProfitUSD(double netProfitUSD) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.netProfitUSD = df.format(netProfitUSD);
	}
	public String getDeadLine() {
		return deadLine;
	}
	public void setDeadLine(String deadLine) {
		this.deadLine = deadLine;
	}
	public String getExchangeRate() {
		return exchangeRate;
	}
	public void setExchangeRate(double exchangeRate) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.exchangeRate = df.format(exchangeRate);
	}
	public String getFinanceFex() {
		return financeFex;
	}
	public void setFinanceFex(String financeFex) {
		this.financeFex = financeFex;
	}


	public String getDebtRate() {
		return debtRate;
	}
	public void setDebtRate(double debtRate) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.debtRate = df.format(debtRate);
	}
	public String getNetProfitRate() {
		return netProfitRate;
	}
	public void setNetProfitRate(double netProfitRate) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.netProfitRate = df.format(netProfitRate);
	}
	public void setRatingSocre1(double ratingSocre) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.ratingSocre = df.format(ratingSocre);
	}
	public void setCreditLimit1(double creditLimit) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.creditLimit =df.format(creditLimit);
	}
	public void setAvailableCredit1(double availableCredit) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.availableCredit = df.format(availableCredit);
	}
	public String getFinacialLevel() {
		return finacialLevel;
	}
	public void setFinacialLevel(String finacialLevel) {
		this.finacialLevel = finacialLevel;
	}
	public String getCreditLevel() {
		return creditLevel;
	}
	public void setCreditLevel(String creditLevel) {
		this.creditLevel = creditLevel;
	}
	public String getTotalRating() {
		return totalRating;
	}
	public void setTotalRating(String totalRating) {
		this.totalRating = totalRating;
	}
	public String getRatingSocre() {
		return ratingSocre;
	}
	public void setRatingSocre(double ratingSocre) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.ratingSocre = df.format(ratingSocre);
	}
	public String getCreditLimit() {
		return creditLimit;
	}
	public void setCreditLimit(double creditLimit) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.creditLimit = df.format(creditLimit);
	}
	public String getAvailableCredit() {
		return availableCredit;
	}
	public void setAvailableCredit(double availableCredit) {
		DecimalFormat df = new DecimalFormat("0.#######");
		this.availableCredit = df.format(availableCredit);
	}
	public String getEffectDate() {
		return effectDate;
	}
	public void setEffectDate(String effectDate) {
		this.effectDate = effectDate;
	}
	public String getStatusCode() {
		return statusCode;
	}
	public void setStatusCode(String statusCode) {
		this.statusCode = statusCode;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getActiveFlag() {
		return activeFlag;
	}
	public void setActiveFlag(String activeFlag) {
		this.activeFlag = activeFlag;
	}
	public Account getAccount() {
		return account;
	}
	public void setAccount(Account account) {
		this.account = account;
	}


	@Override
	public String toString() {
		return "CreditVO{" +
				"account=" + account +
				", finacialLevel='" + finacialLevel + '\'' +	", finacialLevelCode='" + finacialLevelCode + '\'' +	", creditLevel='" + creditLevel + '\'' +
				", creditLevelCode='" + creditLevelCode + '\'' +	", totalRating='" + totalRating + '\'' +	", totalRatingCode='" + totalRatingCode + '\'' +
				", ratingSocre='" + ratingSocre + '\'' +	", creditLimit='" + creditLimit + '\'' +	", availableCredit='" + availableCredit + '\'' +
				", effectDate='" + effectDate + '\'' +	", closeDate='" + closeDate + '\'' +	", currencyCode='" + currencyCode + '\'' +
				", totalAsset='" + totalAsset + '\'' +	", netAsset='" + netAsset + '\'' +	", totalIncome='" + totalIncome + '\'' +
				", ebitda='" + ebitda + '\'' +	", netProfit='" + netProfit + '\'' +	", totalAssetUSD='" + totalAssetUSD + '\'' +
				", netAssetUSD='" + netAssetUSD + '\'' +	", totalIncomeUSD='" + totalIncomeUSD + '\'' +	", ebitdaUsd='" + ebitdaUsd + '\'' +
				", netProfitUSD='" + netProfitUSD + '\'' +	", deadLine='" + deadLine + '\'' +	", exchangeRate='" + exchangeRate + '\'' +
				", financeFex='" + financeFex + '\'' +	", ebitdaRate='" + ebitdaRate + '\'' +	", debtRate='" + debtRate + '\'' +
				", netProfitRate='" + netProfitRate + '\'' +	", licenceCover='" + licenceCover + '\'' +	", licenceCoverCode='" + licenceCoverCode + '\'' +
				", mobileUser='" + mobileUser + '\'' +	", mobileRank='" + mobileRank + '\'' +	", mobileRate='" + mobileRate + '\'' +
				", mobileArpu='" + mobileArpu + '\'' +	", mobilePenetrance='" + mobilePenetrance + '\'' +	", mobileUserIncrement='" + mobileUserIncrement + '\'' +
				", fixUser='" + fixUser + '\'' +	", fixRank='" + fixRank + '\'' +	", fixRate='" + fixRate + '\'' +
				", fixArpu='" + fixArpu + '\'' +	", fixPenetrance='" + fixPenetrance + '\'' +	", fixUserIncrement='" + fixUserIncrement + '\'' +
				", broadUser='" + broadUser + '\'' +	", broadRank='" + broadRank + '\'' +	", broadRate='" + broadRate + '\'' +
				", broadArpu='" + broadArpu + '\'' +	", broadPenetrance='" + broadPenetrance + '\'' +	", broadUserIncrement='" + broadUserIncrement + '\'' +
				", wiredUser='" + wiredUser + '\'' +	", wiredRank='" + wiredRank + '\'' +	", wiredRate='" + wiredRate + '\'' +
				", wiredArpu='" + wiredArpu + '\'' +	", wiredUserIncrements='" + wiredUserIncrements + '\'' +	", wiredPenetrance='" + wiredPenetrance + '\'' +
				", statusCode='" + statusCode + '\'' +	", status='" + status + '\'' +	", activeFlag='" + activeFlag + '\'' +
				", transactionCode='" + transactionCode + '\'' +	", transaction='" + transaction + '\'' +	", totalArrearsOfSubsidiaries='" + totalArrearsOfSubsidiaries + '\'' +
				", system03='" + system03 + '\'' +	", system46='" + system46 + '\'' +	", system712='" + system712 + '\'' +
				", system13_24='" + system1324 + '\'' +	", system24_36='" + system2436 + '\'' +	", totalAmountSystemReceivable='" + totalAmountSystemReceivable + '\'' +
				", mobile0_3='" + mobile03 + '\'' +	", mobile4_6='" + mobile46 + '\'' +	", mobile712='" + mobile712 + '\'' +
				", mobile13_24='" + mobile1324 + '\'' +	", mobile24_36='" + mobile2436 + '\'' +	", totalAmountMobileReceivable='" + totalAmountMobileReceivable + '\'' +
				", engineering0_3='" + engineering03 + '\'' + ", engineering4_6='" + engineering46 + '\'' +	", engineering7_12='" + engineering712 + '\'' +
				", engineering13_24='" + engineering1324 + '\'' +	", engineering24_36='" + engineering2436 + '\'' +
				", totalAmountEngineeringReceivable='" + totalAmountEngineeringReceivable + '\'' +	", accntRistTip='" + accntRistTip + '\'' +
				'}';
	}
}