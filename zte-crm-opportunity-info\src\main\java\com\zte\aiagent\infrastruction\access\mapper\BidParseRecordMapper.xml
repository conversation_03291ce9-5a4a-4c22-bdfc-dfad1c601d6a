<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.aiagent.infrastruction.access.mapper.BidParseRecordMapper">
    <!-- 基础结果集映射 -->
    <resultMap id="BaseResultMap" type="com.zte.aiagent.infrastruction.access.po.BidParseRecordPO">
        <id column="row_id" property="rowId" jdbcType="VARCHAR"/>
        <result column="document_id" property="documentId" jdbcType="VARCHAR"/>
        <result column="parse_type" property="parseType" jdbcType="VARCHAR"/>
        <result column="parse_status" property="parseStatus" jdbcType="VARCHAR"/>
        <result column="parse_start_time" property="parseStartTime" jdbcType="TIMESTAMP"/>
        <result column="parse_end_time" property="parseEndTime" jdbcType="TIMESTAMP"/>
        <result column="parse_duration" property="parseDuration" jdbcType="INTEGER"/>
        <result column="error_message" property="errorMessage" jdbcType="VARCHAR"/>
        <result column="extracted_count" property="extractedCount" jdbcType="INTEGER"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_date" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR"/>
        <result column="last_updated_date" property="lastUpdatedDate" jdbcType="TIMESTAMP"/>
        <result column="enabled_flag" property="enabledFlag" jdbcType="VARCHAR"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
        <result column="parse_type_value_cn" property="parseTypeValueCn" jdbcType="VARCHAR"/>
        <result column="parse_type_value_en" property="parseTypeValueEn" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 插入解析项记录 -->
    <insert id="insert" parameterType="com.zte.aiagent.infrastruction.access.po.BidParseRecordPO">
        INSERT INTO bid_parse_record (
            row_id,
            document_id,
            parse_type,
            parse_status,
            parse_start_time,
            parse_end_time,
            parse_duration,
            error_message,
            extracted_count,
            created_by,
            created_date,
            last_updated_by,
            last_updated_date,
            enabled_flag,
            tenant_id,
            parse_type_value_cn,
            parse_type_value_en
        ) VALUES (
            #{rowId,jdbcType=VARCHAR},
            #{documentId,jdbcType=VARCHAR},
            #{parseType,jdbcType=VARCHAR},
            #{parseStatus,jdbcType=VARCHAR},
            #{parseStartTime,jdbcType=TIMESTAMP},
            #{parseEndTime,jdbcType=TIMESTAMP},
            #{parseDuration,jdbcType=INTEGER},
            #{errorMessage,jdbcType=VARCHAR},
            #{extractedCount,jdbcType=INTEGER},
            #{createdBy,jdbcType=VARCHAR},
            #{createdDate,jdbcType=TIMESTAMP},
            #{lastUpdatedBy,jdbcType=VARCHAR},
            #{lastUpdatedDate,jdbcType=TIMESTAMP},
            #{enabledFlag,jdbcType=VARCHAR},
            #{tenantId,jdbcType=BIGINT},
            #{parseTypeValueCn,jdbcType=VARCHAR},
            #{parseTypeValueEn,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 根据ID查询解析项记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
            row_id,
            document_id,
            parse_type,
            parse_status,
            parse_start_time,
            parse_end_time,
            parse_duration,
            error_message,
            extracted_count,
            created_by,
            created_date,
            last_updated_by,
            last_updated_date,
            enabled_flag,
            tenant_id,
            parse_type_value_cn,
            parse_type_value_en
        FROM bid_parse_record
        WHERE row_id = #{rowId,jdbcType=VARCHAR}
    </select>

    <!-- 根据ID更新解析项记录 -->
    <update id="updateByPrimaryKey" parameterType="com.zte.aiagent.infrastruction.access.po.BidParseRecordPO">
        UPDATE bid_parse_record
        SET
            document_id = #{documentId,jdbcType=VARCHAR},
            parse_type = #{parseType,jdbcType=VARCHAR},
            parse_type_value_cn = #{parseTypeValueCn,jdbcType=VARCHAR},
            parse_type_value_en = #{parseTypeValueEn,jdbcType=VARCHAR}
            parse_status = #{parseStatus,jdbcType=VARCHAR},
            parse_start_time = #{parseStartTime,jdbcType=TIMESTAMP},
            parse_end_time = #{parseEndTime,jdbcType=TIMESTAMP},
            parse_duration = #{parseDuration,jdbcType=INTEGER},
            error_message = #{errorMessage,jdbcType=VARCHAR},
            extracted_count = #{extractedCount,jdbcType=INTEGER},
            created_by = #{createdBy,jdbcType=VARCHAR},
            created_date = #{createdDate,jdbcType=TIMESTAMP},
            last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            last_updated_date = #{lastUpdatedDate,jdbcType=TIMESTAMP},
            enabled_flag = #{enabledFlag,jdbcType=VARCHAR},
            tenant_id = #{tenantId,jdbcType=BIGINT}
        WHERE row_id = #{rowId,jdbcType=VARCHAR}
    </update>

    <!-- 根据ID删除解析项记录 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        DELETE FROM bid_parse_record
        WHERE row_id = #{rowId,jdbcType=VARCHAR}
    </delete>

    <!-- 根据文档ID查询解析项记录列表 -->
    <select id="selectByDocumentId" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
            row_id,
            document_id,
            parse_type,
            parse_type_value_cn,
            parse_type_value_en,
            parse_status,
            parse_start_time,
            parse_end_time,
            parse_duration,
            error_message,
            extracted_count,
            created_by,
            created_date,
            last_updated_by,
            last_updated_date,
            enabled_flag,
            tenant_id
        FROM bid_parse_record
        WHERE document_id = #{documentId,jdbcType=VARCHAR}
        AND enabled_flag = 'Y'
        ORDER BY created_date DESC
    </select>
</mapper>
