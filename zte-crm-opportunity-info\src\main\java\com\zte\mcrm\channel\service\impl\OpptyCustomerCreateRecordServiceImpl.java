package com.zte.mcrm.channel.service.impl;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.crm.eva.base.common.utils.PageRowsUtil;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.adapter.model.AuthConstraintDTO;
import com.zte.mcrm.adapter.model.dto.AccountInfo;
import com.zte.mcrm.adapter.model.dto.UcsUserInfoDTO;
import com.zte.mcrm.adapter.service.CustomerInfoService;
import com.zte.mcrm.adapter.service.IBmtUcsService;
import com.zte.mcrm.adapter.service.PrmService;
import com.zte.mcrm.channel.constant.AccountStatusEnum;
import com.zte.mcrm.channel.constant.FrozenFlagEnum;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.channel.dao.OpptyCustomerCreateRecordDao;
import com.zte.mcrm.channel.model.dto.OpportunityMsgNotifyDTO;
import com.zte.mcrm.channel.model.entity.IChannelOpptyCustomerCreateRecordQuery;
import com.zte.mcrm.channel.model.entity.OpptyCustomerCreateRecord;
import com.zte.mcrm.channel.model.entity.OpptyCustomerCreateRecordQuery;
import com.zte.mcrm.channel.model.entity.PrmOpptyCustomerCreateRecordQuery;
import com.zte.mcrm.channel.model.vo.OpptyCustomerCreateRecordVO;
import com.zte.mcrm.channel.service.OpptyCustomerCreateRecordService;
import com.zte.mcrm.channel.service.channel.IOpportunityInfoService;
import com.zte.mcrm.channel.service.common.IMessageNotifyService;
import com.zte.mcrm.common.business.service.IKeyIdService;
import com.zte.mcrm.common.consts.CommonConstant;
import com.zte.mcrm.common.model.PersonAndOrgInfoVO;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.mcrm.common.util.EmpProcessUtil;
import com.zte.mcrm.common.util.FormDataHelpUtil;
import com.zte.mcrm.opportunity.utils.PersonAndOrgInfoUtil;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zte.mcrm.channel.constant.OpportunityNotifyMsgEnum.*;


/**
 * 渠道商新建最终客户记录 服务类 
 * <AUTHOR>
 * @date 2023/05/10
 */
@Service
@Slf4j
public class OpptyCustomerCreateRecordServiceImpl implements OpptyCustomerCreateRecordService {
    @Autowired
    private IKeyIdService iKeyIdService;
	@Autowired
	private CustomerInfoService customerInfoService;
	@Autowired
	PrmService prmService;
	@Autowired
	private OpptyCustomerCreateRecordDao opptyCustomerCreateRecordDao;
	@Resource
	private IMessageNotifyService messageNotifyService;

	@Resource
	private IBmtUcsService iBmtUcsService;

	@Resource
	private IOpportunityInfoService iOpportunityInfoService;



	private final static String EFFECTED_FLAG = "effectedFlag";

	private final static String N = "N";

	// 生效
	private final static String Y = "Y";

	// 驳回
	private final static String R = "R";

	/**
	 * 根据主键获取实体对象
	 * @param rowId 主键ID
	 * @return
	 * <AUTHOR>
     * @date 2023/05/10
     */
	@Override
	public OpptyCustomerCreateRecord get(String rowId) {
		return opptyCustomerCreateRecordDao.get(rowId);
	}
	
	/**
	 * 软删除指定记录
	 * @param rowId 主键ID
	 * @return 删除记录个数
	 * <AUTHOR>
     * @date 2023/05/10
     */
	@Override
	@Transactional(propagation = Propagation.REQUIRED,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public int softDelete(String rowId){
		return opptyCustomerCreateRecordDao.softDelete(rowId);
	}

	/**
	 * 删除指定记录
	 * @param rowId 主键ID
	 * @return 删除记录个数
	 * <AUTHOR>
     * @date 2023/05/10
     */
	@Override
	@Transactional(propagation = Propagation.REQUIRED,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public int delete(String rowId){
		return opptyCustomerCreateRecordDao.delete(rowId);
	}

	/**
	 * 新增指定记录
	 * @param entity 实体对象
	 * @return 新增的记录对象,注意是提交数据库之前的实体对象
	 * <AUTHOR>
     * @date 2023/05/10
     */
	@Override
	@Transactional(propagation = Propagation.REQUIRED,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public OpptyCustomerCreateRecord insert(OpptyCustomerCreateRecord entity){
		String emp = CommonUtils.getEmpNo();
		Date now = new Date();
		entity.setCreatedBy(emp);
		entity.setLastUpdatedBy(emp);
        entity.setCreatedDate(now);
		entity.setLastUpdatedDate(now);
        if (null==entity.getRowId()) {
            entity.setRowId(iKeyIdService.getKeyId());
        }
        opptyCustomerCreateRecordDao.insert(entity);		
		return entity;
	}

	/**
	 * 修改指定记录
	 * @param entity 实体对象
	 * @return 修改的记录对象,注意是提交数据库之前的实体对象
	 * <AUTHOR>
     * @date 2023/05/10
     */
	@Override
	@Transactional(propagation = Propagation.REQUIRED,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public OpptyCustomerCreateRecord update(OpptyCustomerCreateRecord entity){
		entity.setLastUpdatedBy(CommonUtils.getEmpNo());
		entity.setLastUpdatedDate(new Date());
		opptyCustomerCreateRecordDao.update(entity);
		return entity;
	}

	/**
	 * 获取符合条件的实体列表,按指定属性排序
	 * @param map 参数集合
	 * @return 实体集合
	 * <AUTHOR>
     * @date 2023/05/10
     */
	@Override
	public List<OpptyCustomerCreateRecord> getList(Map<String, Object> map){
		return opptyCustomerCreateRecordDao.getList(map);
	}

	/**
	 * 统计
	 * @param map 参数集合
	 * @return 统计总数
	 * <AUTHOR>
     * @date 2023/05/10
     */
	@Override
	public long getCount(Map<String, Object> map){
        return opptyCustomerCreateRecordDao.getCount(map);
	}

	/**
	 * 获取符合条件的记录列表,先按指定属性排序,在分页
	 * @param map 参数集合
	 * @return 实体集合
	 * <AUTHOR>
     * @date 2023/05/10
     */
	@Override
	public List<OpptyCustomerCreateRecord> getPage(Map<String, Object> map){
		return opptyCustomerCreateRecordDao.getPage(map);
	}

	/**
	* 获取符合条件的记录列表,先按指定属性排序,在分页
	* @param form 参数集合
	* @return 实体集合
	* <AUTHOR>
	* @date 2023/05/10
    */
	@Override
	public PageRows<OpptyCustomerCreateRecord> getPageRows(FormData<OpptyCustomerCreateRecord> form){
		Map<String, Object> map = FormDataHelpUtil.getPageQueryMap(form);
		long total = this.getCount(map);
		List<OpptyCustomerCreateRecord> result = this.getPage(map);
		return FormDataHelpUtil.getPageRowsResult(form, total, result);
	}

	@Override
	public PageRows<OpptyCustomerCreateRecordVO> queryCustomerRecordWithPrm(FormData<PrmOpptyCustomerCreateRecordQuery> formData) throws Exception {
		PageHelper.startPage((int)formData.getPage(), (int)formData.getRows());
		OpptyCustomerCreateRecordQuery queryParam = new OpptyCustomerCreateRecordQuery();
		String keyword = formData.getBo() != null ? formData.getBo().getKeyword() : null;
		queryParam.setCustomerName(keyword);
		queryParam.setLastAccName(keyword);
		List<OpptyCustomerCreateRecord> opptyCustomerCreateRecords = opptyCustomerCreateRecordDao.queryCustomerRecordByCondition(queryParam);
		return getOpptyCustomerCreateRecordVOPageRows(opptyCustomerCreateRecords);
	}

	@Override
	public PageRows<OpptyCustomerCreateRecordVO> queryCustomerRecordWithIchannel(FormData<IChannelOpptyCustomerCreateRecordQuery> formData) throws Exception {
		PageHelper.startPage((int)formData.getPage(), (int)formData.getRows());
		OpptyCustomerCreateRecordQuery queryParam = new OpptyCustomerCreateRecordQuery();
		queryParam.setCrmCustomerCode(formData.getBo().getCrmCustomerCode());
		queryParam.setLastAccName(formData.getBo().getLastAccName());
		List<OpptyCustomerCreateRecord> opptyCustomerCreateRecords = opptyCustomerCreateRecordDao.queryCustomerRecordByCondition(queryParam);
		return getOpptyCustomerCreateRecordVOPageRows(opptyCustomerCreateRecords);
	}

	@Override
	public void refreshCustomerStatus() {
		// 获取effected_flag 为 N [待提交，审批中] 的客户——未生效客户
		List<OpptyCustomerCreateRecord> notEffectedCustomersList = this.queryNotEffectedCustomers();
		if (CollectionUtils.isEmpty(notEffectedCustomersList)){
			log.info("notEffectedCustomersList is null");
			return ;
		}

		// 获取有效的客户，更新表
		List<OpptyCustomerCreateRecord> effectiveAndRefuseCustomers = this.getEffectiveAndRefuseCustomers(notEffectedCustomersList);
		if (CollectionUtils.isEmpty(effectiveAndRefuseCustomers)){
			log.info("effectiveOpptyCustomerCreateRecord is null");
			return;
		}
		opptyCustomerCreateRecordDao.updateByBatch(effectiveAndRefuseCustomers);

		// 发送邮件和消息
		sendEffectiveAndRefusedSendMailAndNotice(effectiveAndRefuseCustomers);

	}


	private void sendEffectiveAndRefusedSendMailAndNotice(List<OpptyCustomerCreateRecord> effectiveAndRefuseCustomers) {
		// 员工工号转换
		Map<String, UcsUserInfoDTO> userInfoByAccountIdMap = this.getUserInfoByAccountIdMap(effectiveAndRefuseCustomers);
		if (userInfoByAccountIdMap == null) {
			return;
		}

		// 循环调用发送邮件和消息
		effectiveAndRefuseCustomers.forEach(ele ->{
			this.sendNotice(ele);
			this.sendMail(userInfoByAccountIdMap, ele);
		});
	}

	private Map<String, UcsUserInfoDTO> getUserInfoByAccountIdMap(List<OpptyCustomerCreateRecord> effectiveAndRefuseCustomers) {
		List<String> effectiveAndRefuseCustomersCreateBy = effectiveAndRefuseCustomers.stream()
				.map(OpptyCustomerCreateRecord::getCreatedBy)
				.filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

		if(CollectionUtils.isEmpty(effectiveAndRefuseCustomersCreateBy)){
			return null;
		}

		List<UcsUserInfoDTO> userInfoByAccountIdList = iBmtUcsService.getUserInfoByAccountIdList(effectiveAndRefuseCustomersCreateBy);

		if (CollectionUtils.isEmpty(userInfoByAccountIdList)) {
			return null;
		}

		return userInfoByAccountIdList.stream().
				collect(Collectors.toMap(UcsUserInfoDTO::getAccountId, Function.identity(), (oldValue, newValue) -> newValue));
	}

	private void sendNotice(OpptyCustomerCreateRecord ele){
		if(ele.getOptyId() == null || ele.getCreatedBy() == null){
			return;
		}

		OpportunityMsgNotifyDTO opportunityMsgNotifyDTO = new OpportunityMsgNotifyDTO();
		opportunityMsgNotifyDTO.setRowId(ele.getOptyId());
		opportunityMsgNotifyDTO.setType(OpportunityConstant.MSG_NOTIFY_TYPE);
		opportunityMsgNotifyDTO.setMailToList(Arrays.asList(ele.getCreatedBy()));
		if (Y.equals(ele.getEffectedFlag())){
			opportunityMsgNotifyDTO.setMsgId(OPPORTUNITY_EMAIL_FOR_CUSTOMER_EFFECTED_REMINDER_EXTERNAL);
		}else if (R.equals(ele.getEffectedFlag())){
			opportunityMsgNotifyDTO.setMsgId(OPPORTUNITY_EMAIL_FOR_CUSTOMER_FAIL_REMINDER);
		}
		messageNotifyService.sendMessage(opportunityMsgNotifyDTO);
	}

	private void sendMail(Map<String, UcsUserInfoDTO> userInfoByAccountIdMap, OpptyCustomerCreateRecord ele) {
		if(ele.getOptyId() == null || ele.getXLastAccName() == null){
			return;
		}
		UcsUserInfoDTO ucsUserInfoDTO = userInfoByAccountIdMap.get(ele.getCreatedBy());
		if (ucsUserInfoDTO == null  || ucsUserInfoDTO.getEmail() == null){
			return;
		}
		OpportunityMsgNotifyDTO opportunityMsgNotifyDTO = new OpportunityMsgNotifyDTO();
		opportunityMsgNotifyDTO.setRowId(ele.getOptyId());
		opportunityMsgNotifyDTO.setType(OpportunityConstant.MSG_MAIL_TYPE);
		opportunityMsgNotifyDTO.setMailToList(Arrays.asList(ucsUserInfoDTO.getEmail()));
		if (Y.equals(ele.getEffectedFlag())){
			opportunityMsgNotifyDTO.setMsgId(OPPORTUNITY_EMAIL_FOR_CUSTOMER_EFFECTED_REMINDER_EXTERNAL);
		}else if (R.equals(ele.getEffectedFlag())){
			opportunityMsgNotifyDTO.setMsgId(OPPORTUNITY_EMAIL_FOR_CUSTOMER_FAIL_REMINDER);
		}
		messageNotifyService.sendMessage(opportunityMsgNotifyDTO);
	}

	/**
	 * 获取有效的和失效的记录数据
	 * */
	private List<OpptyCustomerCreateRecord> getEffectiveAndRefuseCustomers(List<OpptyCustomerCreateRecord> notEffectedCustomersList) {
		// 调用接口判断状态
		List<String> customerList = notEffectedCustomersList.stream().map(OpptyCustomerCreateRecord::getXLastAccName).filter(StringUtils::isNotBlank).collect(Collectors.toList());
		Map<String, AccountInfo> customerInformationMap = customerInfoService.getAccountInfoByBatch(customerList);

		//生效 和 驳回 客户
		List<OpptyCustomerCreateRecord> effectiveAndRefuseCustomers = new ArrayList<>();
		for (OpptyCustomerCreateRecord opptyCustomerCreateRecord : notEffectedCustomersList) {
			AccountInfo accountInfo = customerInformationMap.get(opptyCustomerCreateRecord.getXLastAccName());

			if (accountInfo == null){
				continue;
			}

			// 只有有效，且为不为沉默客户 就设置为 Y
			if (AccountStatusEnum.ACCT_ACTIVE_STATUS.getValue().equals(accountInfo.getActiveStatusCode()) && !FrozenFlagEnum.silentCustomer(accountInfo.getFrozenFlag())){
				opptyCustomerCreateRecord.setEffectedFlag(Y);
				effectiveAndRefuseCustomers.add(opptyCustomerCreateRecord);
			}else if(AccountStatusEnum.REFUSE.getValue().equals(accountInfo.getActiveStatusCode())){
				opptyCustomerCreateRecord.setEffectedFlag(R);
				effectiveAndRefuseCustomers.add(opptyCustomerCreateRecord);
			}

		}
		return effectiveAndRefuseCustomers;
	}

	private List<OpptyCustomerCreateRecord> queryNotEffectedCustomers() {
		Map<String, Object> map = new HashMap<>();
		map.put(EFFECTED_FLAG,N);
		return opptyCustomerCreateRecordDao.getNotEffectedCustomers(map);
	}


	private PageRows<OpptyCustomerCreateRecordVO> getOpptyCustomerCreateRecordVOPageRows(List<OpptyCustomerCreateRecord> opptyCustomerCreateRecords) throws Exception {
		if (CollectionUtils.isEmpty(opptyCustomerCreateRecords)) {
			return PageRowsUtil.buildEmptyPage(1L);
		}
		// 查询最终客户状态
		List<String> lastAccNameList = opptyCustomerCreateRecords.stream()
				.map(OpptyCustomerCreateRecord::getXLastAccName)
				.filter(StringUtils::isNotBlank).collect(Collectors.toList());
		Map<String, AccountInfo> accountInfoMap = customerInfoService.getAccountInfoByBatch(lastAccNameList);
		// 映射客户状态
		for (OpptyCustomerCreateRecord opptyCustomerCreateRecord : opptyCustomerCreateRecords) {
			AccountInfo accountInfo = accountInfoMap.get(opptyCustomerCreateRecord.getXLastAccName());
			String activeStatus = accountInfo != null ? accountInfo.getActiveStatus() : Strings.EMPTY;
			opptyCustomerCreateRecord.setStatus(activeStatus);
		}
		// 返回客户创建记录列表
		return convert2PageVO(opptyCustomerCreateRecords);
	}

	private PageRows<OpptyCustomerCreateRecordVO> convert2PageVO(List<OpptyCustomerCreateRecord> opptyCustomerCreateRecords) throws Exception {
		PageRows<OpptyCustomerCreateRecordVO> resultPageRows = new PageRows<>();
		if (CollectionUtils.isEmpty(opptyCustomerCreateRecords)) {
			return PageRowsUtil.buildEmptyPage(1L);
		}

		PageInfo<OpptyCustomerCreateRecord> pageInfo = new PageInfo<>(opptyCustomerCreateRecords);
		resultPageRows.setCurrent(pageInfo.getPageNum());
		resultPageRows.setTotal(pageInfo.getTotal());
		resultPageRows.setRows(transToOpptyCustomerCreateRecordVOList(opptyCustomerCreateRecords, getOrgNameMap(opptyCustomerCreateRecords), getIndustryMap()));
		return resultPageRows;
	}

	private Map<String, String> getIndustryMap() {
		// 从行业树获取行业列表
		List<AuthConstraintDTO> subIndustryList = prmService.getSubIndustryListWithNoException("Y");
		return subIndustryList.stream()
				.collect(Collectors.toMap(AuthConstraintDTO::getShowValue, AuthConstraintDTO::getShowName));
	}

	private static Map<String, String> getOrgNameMap(List<OpptyCustomerCreateRecord> opptyCustomerCreateRecords) throws RouteException {
		// 根据新组织(ORG打头)编码去HR查询组织信息
		List<String> deptNos = opptyCustomerCreateRecords.stream().map(OpptyCustomerCreateRecord::getDeptNo)
				.filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
		List<PersonAndOrgInfoVO> orgList = PersonAndOrgInfoUtil.getOrgAndChildCompanyInfo(deptNos);
		return orgList.stream().filter(Objects::nonNull)
				.collect(Collectors.toMap(PersonAndOrgInfoVO::getHrOrgID, PersonAndOrgInfoVO::getHrOrgName));
	}

	/**
	 * 类型转换方法
	 *
	 * @param recordList 客户创建记录
	 * @param industryMap 行业Map
	 * @param orgNameMap 组织Map
	 * <AUTHOR>
	 * @date 2023/5/11
	 */
	private List<OpptyCustomerCreateRecordVO> transToOpptyCustomerCreateRecordVOList(List<OpptyCustomerCreateRecord> recordList, Map<String, String> orgNameMap, Map<String, String> industryMap) throws Exception {
		List<OpptyCustomerCreateRecordVO> voList = Lists.newArrayList();
		EmpProcessUtil.replaceEmpNo(recordList);
		for (OpptyCustomerCreateRecord customerCreateRecord : recordList) {
			OpptyCustomerCreateRecordVO recordVO = new OpptyCustomerCreateRecordVO();
			recordVO.setRowId(customerCreateRecord.getRowId());
			recordVO.setCustomerName(customerCreateRecord.getCustomerName());
			recordVO.setLastAccName(customerCreateRecord.getXLastAccName());
			recordVO.setDeptNo(customerCreateRecord.getDeptNo());
			// 设置投资方所在地字段中文名称
			recordVO.setDeptName(orgNameMap.getOrDefault(customerCreateRecord.getDeptNo(), customerCreateRecord.getDeptNo()));
			recordVO.setFinalCustomerParentTrade(customerCreateRecord.getFinalCustomerParentTrade());
			recordVO.setFinalCustomerChildTrade(customerCreateRecord.getFinalCustomerChildTrade());
			// 设置最终用户行业字段中文名称
			String defaultTrade = StringUtils.isNotBlank(customerCreateRecord.getFinalCustomerChildTrade()) ?
					customerCreateRecord.getFinalCustomerParentTrade() + CommonConstant.MID_LINE + customerCreateRecord.getFinalCustomerChildTrade() : null;
			recordVO.setFinalCustomerTradeName(industryMap.getOrDefault(customerCreateRecord.getFinalCustomerChildTrade(), defaultTrade));
			recordVO.setBusinessManager(customerCreateRecord.getBusinessManagerName()+customerCreateRecord.getBusinessManagerId());
			recordVO.setApplicant(customerCreateRecord.getCreatedBy());
			recordVO.setCreatedDate(customerCreateRecord.getCreatedDate());
			recordVO.setStatus(customerCreateRecord.getStatus());
			voList.add(recordVO);
		}
		return voList;
	}
}
