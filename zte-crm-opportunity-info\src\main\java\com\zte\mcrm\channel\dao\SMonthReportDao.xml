<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zte.mcrm.channel.dao.SMonthReportDao" >
    <!-- 屏蔽mybatis的2级缓存  <cache />  -->
  
    <!-- 记录和实体映射配置 -->
    <resultMap id="BaseMap" type="com.zte.mcrm.channel.model.entity.SMonthReport" >
        <id column="id" property="id" jdbcType="VARCHAR" />
		<result column="opty_id" property="optyId" jdbcType="VARCHAR" />	
		<result column="report_month" property="reportMonth" jdbcType="VARCHAR" />	
		<result column="report_status" property="reportStatus" jdbcType="VARCHAR" />	
		<result column="opty_current_status" property="optyCurrentStatus" jdbcType="VARCHAR" />	
		<result column="reason_code" property="reasonCode" jdbcType="VARCHAR" />	
		<result column="report_info" property="reportInfo" jdbcType="VARCHAR" />	
		<result column="created_by" property="createdBy" jdbcType="VARCHAR" />	
		<result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />	
		<result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR" />	
		<result column="last_updated_date" property="lastUpdatedDate" jdbcType="TIMESTAMP" />	
		<result column="enabled_flag" property="enabledFlag" jdbcType="CHAR" />	
		<result column="memo" property="memo" jdbcType="VARCHAR" />
        <result column="business_type" property="businessType" jdbcType="VARCHAR" />
        <result column="month_report_detail" property="monthReportDetailJson" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="base_column">
        t.id ,
        t.opty_id ,
        t.report_month ,
        t.report_status ,
        t.opty_current_status ,
        t.reason_code ,
        t.report_info ,
        t.created_by ,
        t.created_date ,
        t.last_updated_by ,
        t.last_updated_date ,
        t.enabled_flag ,
        t.memo ,
        t.business_type ,
        t.month_report_detail
    </sql>

    <sql id="base_where">
        <if test="id != null and id != ''"> and t.id = #{id}</if>
        <if test="optyId != null and optyId != ''"> and t.opty_id = #{optyId}</if>
        <if test="reportMonth != null and reportMonth != ''"> and t.report_month = #{reportMonth}</if>
        <if test="reportStatus != null and reportStatus != ''"> and t.report_status = #{reportStatus}</if>
        <if test="optyCurrentStatus != null and optyCurrentStatus != ''"> and t.opty_current_status = #{optyCurrentStatus}</if>
        <if test="reasonCode != null and reasonCode != ''"> and t.reason_code = #{reasonCode}</if>
        <if test="reportInfo != null and reportInfo != ''"> and t.report_info = #{reportInfo}</if>
        <if test="createdBy != null and createdBy != ''"> and t.created_by = #{createdBy}</if>
        <if test="createdDate != null"> and t.created_date = #{createdDate}</if>
        <if test="lastUpdatedBy != null and lastUpdatedBy != ''"> and t.last_updated_by = #{lastUpdatedBy}</if>
        <if test="lastUpdatedDate != null"> and t.last_updated_date = #{lastUpdatedDate}</if>
        <if test="enabledFlag != null and enabledFlag != ''"> and t.enabled_flag = #{enabledFlag}</if>
        <if test="memo != null and memo != ''"> and t.memo = #{memo}</if>
        <if test="businessType != null and businessType != ''"> and t.business_type = #{businessType}</if>
    and t.tenant_id = #{headerTenantId}
    </sql>

    <!-- 获取一条记录 -->
    <select id="get"  resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM s_month_report t
        WHERE
        t.id=#{id, jdbcType=VARCHAR}
    </select>
 
    <!-- 获取符合条件的记录列表 -->
    <select id="getList" parameterType="java.util.Map" resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM s_month_report t
        WHERE 1=1
        <include refid="base_where"/>
    </select>
  
    <!-- 软删除一条记录 -->
    <update id="softDelete" >
        UPDATE s_month_report
        SET enable_flag = 'N'
        WHERE
        id = #{id, jdbcType=VARCHAR}
    </update>
    
    <!-- 删除一条记录 -->
    <delete id="delete" >
        DELETE FROM s_month_report
        WHERE
        id = #{id, jdbcType=VARCHAR}
    </delete>

    <!--添加一条记录 -->
    <insert id="insert" parameterType="com.zte.mcrm.channel.model.entity.SMonthReport" >
        INSERT INTO s_month_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
		    <if test="id != null">id ,</if>
		    <if test="optyId != null">opty_id ,</if>
		    <if test="reportMonth != null">report_month ,</if>
		    <if test="reportStatus != null">report_status ,</if>
		    <if test="optyCurrentStatus != null">opty_current_status ,</if>
		    <if test="reasonCode != null">reason_code ,</if>
		    <if test="reportInfo != null">report_info ,</if>
		    <if test="createdBy != null">created_by ,</if>
		    <if test="createdDate != null">created_date ,</if>
		    <if test="lastUpdatedBy != null">last_updated_by ,</if>
		    <if test="lastUpdatedDate != null">last_updated_date ,</if>
		    <if test="enabledFlag != null">enabled_flag ,</if>
		    <if test="memo != null">memo ,</if>
            <if test="businessType != null">business_type ,</if>
            <if test="monthReportDetailJson != null">month_report_detail ,</if>
    		tenant_id
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
    	    <if test="id != null">#{id, jdbcType=VARCHAR} ,</if>
    	    <if test="optyId != null">#{optyId, jdbcType=VARCHAR} ,</if>
    	    <if test="reportMonth != null">#{reportMonth, jdbcType=VARCHAR} ,</if>
    	    <if test="reportStatus != null">#{reportStatus, jdbcType=VARCHAR} ,</if>
    	    <if test="optyCurrentStatus != null">#{optyCurrentStatus, jdbcType=VARCHAR} ,</if>
    	    <if test="reasonCode != null">#{reasonCode, jdbcType=VARCHAR} ,</if>
    	    <if test="reportInfo != null">#{reportInfo, jdbcType=VARCHAR} ,</if>
    	    <if test="createdBy != null">#{createdBy, jdbcType=VARCHAR} ,</if>
    	    <if test="createdDate != null">#{createdDate, jdbcType=TIMESTAMP} ,</if>
    	    <if test="lastUpdatedBy != null">#{lastUpdatedBy, jdbcType=VARCHAR} ,</if>
    	    <if test="lastUpdatedDate != null">#{lastUpdatedDate, jdbcType=TIMESTAMP} ,</if>
    	    <if test="enabledFlag != null">#{enabledFlag, jdbcType=CHAR} ,</if>
    	    <if test="memo != null">#{memo, jdbcType=VARCHAR} ,</if>
            <if test="businessType != null">#{businessType, jdbcType=VARCHAR} ,</if>
            <if test="monthReportDetailJson != null">#{monthReportDetailJson, jdbcType=VARCHAR} ,</if>
			#{headerTenantId, jdbcType=BIGINT}
        </trim>
    </insert>

    <!--批量添加记录 -->
    <insert id="insertByBatch" parameterType="java.util.List" >
        INSERT INTO s_month_report
        (
		    id ,
    	    opty_id ,
    	    report_month ,
    	    report_status ,
    	    opty_current_status ,
    	    reason_code ,
    	    report_info ,
    	    created_by ,
    	    created_date ,
    	    last_updated_by ,
    	    last_updated_date ,
    	    enabled_flag ,
    	    memo ,
            business_type ,
            month_report_detail ,
    		tenant_id
        )
        VALUES
        <foreach collection ="list" item="item" index= "index" separator =",">
        (
    	    #{item.id, jdbcType=VARCHAR} ,
    	    #{item.optyId, jdbcType=VARCHAR} ,
    	    #{item.reportMonth, jdbcType=VARCHAR} ,
    	    #{item.reportStatus, jdbcType=VARCHAR} ,
    	    #{item.optyCurrentStatus, jdbcType=VARCHAR} ,
    	    #{item.reasonCode, jdbcType=VARCHAR} ,
    	    #{item.reportInfo, jdbcType=VARCHAR} ,
    	    #{item.createdBy, jdbcType=VARCHAR} ,
    	    #{item.createdDate, jdbcType=TIMESTAMP} ,
    	    #{item.lastUpdatedBy, jdbcType=VARCHAR} ,
    	    #{item.lastUpdatedDate, jdbcType=TIMESTAMP} ,
    	    #{item.enabledFlag, jdbcType=CHAR} ,
    	    #{item.memo, jdbcType=VARCHAR} ,
            #{item.businessType, jdbcType=VARCHAR} ,
            #{item.monthReportDetailJson, jdbcType=VARCHAR} ,
			#{headerTenantId, jdbcType=BIGINT}
        )
        </foreach>
    </insert>
  
    <!-- 更新一条记录 -->
    <update id="update" parameterType="com.zte.mcrm.channel.model.entity.SMonthReport" >
        UPDATE s_month_report
        <set>
		    <if test="optyId != null">opty_id=#{optyId, jdbcType=VARCHAR} ,</if>
		    <if test="reportMonth != null">report_month=#{reportMonth, jdbcType=VARCHAR} ,</if>
		    <if test="reportStatus != null">report_status=#{reportStatus, jdbcType=VARCHAR} ,</if>
		    <if test="optyCurrentStatus != null">opty_current_status=#{optyCurrentStatus, jdbcType=VARCHAR} ,</if>
		    <if test="reasonCode != null">reason_code=#{reasonCode, jdbcType=VARCHAR} ,</if>
		    <if test="reportInfo != null">report_info=#{reportInfo, jdbcType=VARCHAR} ,</if>
		    <if test="createdBy != null">created_by=#{createdBy, jdbcType=VARCHAR} ,</if>
		    <if test="createdDate != null">created_date=#{createdDate, jdbcType=TIMESTAMP} ,</if>
		    <if test="lastUpdatedBy != null">last_updated_by=#{lastUpdatedBy, jdbcType=VARCHAR} ,</if>
		    <if test="lastUpdatedDate != null">last_updated_date=#{lastUpdatedDate, jdbcType=TIMESTAMP} ,</if>
		    <if test="enabledFlag != null">enabled_flag=#{enabledFlag, jdbcType=CHAR} ,</if>
		    <if test="memo != null">memo=#{memo, jdbcType=VARCHAR} ,</if>
            <if test="businessType != null">business_type=#{businessType, jdbcType=VARCHAR} ,</if>
            <if test="monthReportDetailJson != null">month_report_detail=#{monthReportDetailJson, jdbcType=VARCHAR} ,</if>
        </set>
        WHERE
        id=#{id, jdbcType=VARCHAR}
    </update>
  
    <!-- 翻页函数:获取符合条件的记录数 -->
    <select id="getCount" parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT count(*) FROM s_month_report t
        WHERE 1=1
        <include refid="base_where"/>
    </select>
	
    <!-- 翻页函数:获取一页的记录集 -->
    <select id="getPage" parameterType="java.util.Map" resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM s_month_report t
        WHERE 1=1
        <include refid="base_where"/>
        <if test="orderField != null and orderField == 'id'"> order by t.id
            <if test="order != null and order == 'desc'"> desc </if>
        </if>
        <if test="startRow != null and rowSize != null"> limit #{startRow},#{rowSize} </if>
    </select>
	
</mapper>
