
package com.zte.mcrm.adapter.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class PartnerListVO {

    @ApiModelProperty(value = "城市")
    private String city;
    @ApiModelProperty(value = "国家/地区")
    private String country;
    @ApiModelProperty(value = "成立日期(时间戳，毫秒数)")
    private Long establishTime;
    @ApiModelProperty(value = "传真")
    private Object faxNumber;
    @ApiModelProperty(value = "GTS标签")
    private String gtsFlag;
    @ApiModelProperty(value = "GTS标签名称")
    private String gtsFlagName;
    @ApiModelProperty(value = "国民经济行业分类门类")
    private String industryCategory;
    @ApiModelProperty(value = "行业门类code")
    private Object industryCategoryCode;
    @ApiModelProperty(value = "是否监控企业(N-没监控Y-被监控)")
    private Object isMonitored;
    @ApiModelProperty(value = "上一年营业额")
    private String lastYearTurnover;
    @ApiModelProperty(value = "法人姓名")
    private String legalPersonName;
    @ApiModelProperty(value = "合作伙伴名称")
    private String name;
    @ApiModelProperty(value = "合作伙伴英文名称")
    private String nameEn;
    @ApiModelProperty(value = "办公地址")
    private Object officeAddress;
    @ApiModelProperty(value = "合作伙伴大类")
    private String partnerBigCategory;
    @ApiModelProperty(value = "合作伙伴ID")
    private String partnerId;
    @ApiModelProperty(value = "合作伙伴小类")
    private String partnerSmallCategory;
    @ApiModelProperty(value = "公司电话")
    private String phoneNumber;
    @ApiModelProperty(value = "邮政编码")
    private Object postcode;
    @ApiModelProperty(value = "省份")
    private String province;
    @ApiModelProperty(value = "注册资金")
    private String regCapital;
    @ApiModelProperty(value = "注册资本币种")
    private String regCapitalCurrency;
    @ApiModelProperty(value = "注册地址")
    private String regLocation;
    @ApiModelProperty(value = "注册状态")
    private String regStatus;
    @ApiModelProperty(value = "来源编码")
    private String sourceCode;
    @ApiModelProperty(value = "人员规模")
    private String staffNumRange;
    @ApiModelProperty(value = "伙伴状态(伙伴身份表中的“伙伴状态”)")
    private String status;
    @ApiModelProperty(value = "公司主页")
    private String website;
    @ApiModelProperty(value = "合作伙伴大类编码")
    private String partnerBigCategoryId;
}
