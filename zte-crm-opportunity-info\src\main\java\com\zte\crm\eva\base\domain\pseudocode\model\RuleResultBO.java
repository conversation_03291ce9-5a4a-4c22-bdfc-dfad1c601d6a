package com.zte.crm.eva.base.domain.pseudocode.model;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 规则执行结果类
 * @author: 10243305
 * @date: 2021/11/23 上午10:42
 */
@Data
public class RuleResultBO {
    /**
     * 执行结果标识，最后一位标识是否拦截，倒数第二位标识是软硬拦截
     * 一下以最后两位进行演示
     * 00：不拦截（默认）
     * 01：软拦截
     * 11：硬拦截
     */
    private Integer flag;
    /**
     * 软拦截下需要下传的参数
     */
    private Map<String, Object> kcpParam;
    /**
     * 规则执行提示信息
     */
    private Map<String, String> message;

    public RuleResultBO() {
        this.flag = 0;
        this.kcpParam = new HashMap<>();
        this.message = new HashMap<>();
    }
}
