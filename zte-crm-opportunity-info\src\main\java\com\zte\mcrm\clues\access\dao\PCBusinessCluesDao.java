package com.zte.mcrm.clues.access.dao;

import com.zte.mcrm.clues.access.vo.BusinessClues;
import com.zte.mcrm.opportunity.access.vo.BusinessOppPositionVo;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.util.page.PageQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/****
 *
 * <AUTHOR> @date 2021/1/22
 **/
@Repository
public interface PCBusinessCluesDao {

	/**
	 *  分页查询线索
	 * @param pageQuery
	 * @return
	 * @throws BusiException
	 */
	List<BusinessClues> getCluesWithAuth(PageQuery<BusinessClues> pageQuery) throws BusiException;

	/**
	 *  计数线索条数
	 * @param pageQuery
	 * @return
	 * @throws Exception
	 */
	int countClues(PageQuery<BusinessClues> pageQuery) throws Exception;
	
	/**
	 *  查询最近查询的线索
	 * @param pageQuery
	 * @return
	 * @throws Exception
	 */
	List<BusinessClues> getRecentCluesWithAuth(PageQuery<BusinessClues> pageQuery) throws Exception;
    /**
	 * 查询线索详情
	 * @param businessClues
	 * @return
	 */
	BusinessClues selectBaseInfo(BusinessClues businessClues);
	
	/**
	 * 根据员工ID 查询员工角色
	 * @param entity
	 * @return
	 * @throws Exception
	 */
	List<BusinessOppPositionVo> selectBusinessOppUserPosition(BusinessClues entity) throws Exception;
	/**
	 * 线索状态是否为待客户经理更新
	 * @param clue
	 * @return
	 */
	int isLeadUpdateByNum(BusinessClues clue);

	/**
	 * 根据线索ID获取线索信息
	 * <AUTHOR>
	 * @createDate 2018-04-18
	 * @param entity
	 * @return
     * @throws BusiException
	 */
	BusinessClues selectBaseInfoByClueId(BusinessClues entity) throws BusiException;
	
	/**
	 * 根据线索ID删除线索
	 * <AUTHOR>
	 * @createDate 2018-04-18
	 * @param entity
	 * @return
     * @throws BusiException
	 */
	boolean deleteClue(BusinessClues entity) throws BusiException;

	/***
	 * 查询我是归属客户经理的线索计数
	 * @param empNo
	 * @return
	 */
	int getMyClueCount(String empNo);

	/****
	 * 根据部门ORG编码、线索编码查询商机
	 * @methodName getCluesListByOrgCodeClueNum
	 * @param orgCode
	 * @param clueNum
	 * @return java.util.List<com.zte.mcrm.clues.access.vo.BusinessClues>
	 * <AUTHOR>
	 * @date 2021/2/3
	**/
    List<BusinessClues> getCluesListByOrgCodeClueNum(@Param("orgCode") String orgCode, @Param("clueNum") String clueNum);

    /****
     * 更新部门ORG编码全路径
     * @methodName updateOrgCodePathList
     * @param businessCluesList
     * @return int
     * <AUTHOR>
     * @date 2021/2/3
    **/
    int updateOrgCodePathList(List<BusinessClues> businessCluesList);

    /****
     * 我的所有线索总数量查询
     * @methodName countAllTypeClue
     * @param pageQuery
     * @return int
     * <AUTHOR>
     * @date 2021/2/4
    **/
    int countAllTypeClue(PageQuery<BusinessClues> pageQuery);

    /****
     * 我的所有线索查询
     * @methodName getAllTypeClueList
     * @param pageQuery
     * @return java.util.List<com.zte.mcrm.clues.access.vo.BusinessClues>
     * <AUTHOR>
     * @date 2021/2/5
    **/
    List<BusinessClues> getAllTypeClueList(PageQuery<BusinessClues> pageQuery);
}
