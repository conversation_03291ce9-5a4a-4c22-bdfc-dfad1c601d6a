package com.zte.mcrm.channel.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
*  实体类
* <AUTHOR>
* @date 2021/09/14
*/

@Setter @Getter @ToString
@ApiModel(description="")
public class OpportunityPdmProductVO implements Serializable{

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private String rowId;
    @ApiModelProperty(value = "体系内部分类Id")
    private String prodLv1Id;
    @ApiModelProperty(value = "")
    private String prodLv1Name;
    @ApiModelProperty(value = "大产品线Id")
    private String prodLv2Id;
    @ApiModelProperty(value = "")
    private String prodLv2Name;
    @ApiModelProperty(value = "产品线Id")
    private String prodLv21Id;
    @ApiModelProperty(value = "")
    private String prodLv21Name;
    @ApiModelProperty(value = "产品大类Id")
    private String prodLv3Id;
    @ApiModelProperty(value = "")
    private String prodLv3Name;
    @ApiModelProperty(value = "产品小类Id")
    private String prodLv4Id;
    @ApiModelProperty(value = "")
    private String prodLv4Name;
    @ApiModelProperty(value = "businessType=pdm_prod公司主产品,关联政企产品的ROW_ID")
    private String parProdId;
    @ApiModelProperty(value = "是否主产品")
    private String zteMainProduct;
}
