* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

/* 顶部导航栏 */
.top-navbar {
    height: 60px;
    background: #fff;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.logo-section {
    display: flex;
    align-items: center;
}

.logo {
    width: 32px;
    height: 32px;
    background: #1890ff;
    border-radius: 4px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.system-name {
    font-size: 18px;
    font-weight: 500;
    color: #262626;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-info {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.user-info:hover {
    background-color: #f5f5f5;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #87d068;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

/* 主体布局 */
.main-container {
    display: flex;
    min-height: 100vh;
}

/* 左侧导航栏 */
.sidebar {
    width: 240px;
    background: #fff;
    border-right: 1px solid #e8e8e8;
    overflow-y: auto;
}

.nav-menu {
    padding: 16px 0;
}

.nav-item {
    padding: 12px 24px;
    cursor: pointer;
    transition: all 0.3s;
    border-left: 3px solid transparent;
    display: flex;
    align-items: center;
}

.nav-item:hover {
    background-color: #f5f5f5;
}

.nav-item.active {
    background-color: #e6f7ff;
    border-left-color: #1890ff;
    color: #1890ff;
}

.nav-icon {
    width: 16px;
    height: 16px;
    margin-right: 12px;
    display: inline-block;
}

/* 内容区域 */
.content-area {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
}

.page-header {
    margin-bottom: 24px;
}

.page-title {
    font-size: 24px;
    font-weight: 500;
    color: #262626;
    margin-bottom: 8px;
}

.page-description {
    color: #8c8c8c;
    font-size: 14px;
}

/* 卡片容器 */
.card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    margin-bottom: 16px;
}

.card-header {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
}

.card-body {
    padding: 24px;
}

/* 面包屑 */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    font-size: 14px;
    color: #8c8c8c;
}

.breadcrumb a {
    color: #1890ff;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

/* 任务信息头部 */
.task-info-header {
    background: #f8f9fa;
    padding: 16px 24px;
    border-bottom: 1px solid #e8e8e8;
}

.task-info-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    align-items: start;
}

.task-info-item {
    display: flex;
    flex-direction: column;
    min-height: 60px;
    justify-content: flex-start;
}

.task-info-label {
    font-size: 12px;
    color: #8c8c8c;
    margin-bottom: 6px;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.task-info-value {
    font-size: 14px;
    color: #262626;
    font-weight: 500;
    line-height: 1.4;
    word-break: break-word;
}

/* 标签页 */
.tabs {
    display: flex;
    border-bottom: 1px solid #f0f0f0;
}

.tab {
    padding: 12px 24px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s;
}

.tab:hover {
    color: #40a9ff;
}

.tab.active {
    color: #1890ff;
    border-bottom-color: #1890ff;
}

/* 任务详情布局 */
.task-detail-container {
    display: flex;
    height: calc(100vh - 200px);
}

.content-panel {
    flex: 1;
    background: #fff;
    overflow-y: auto;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: #fff;
    color: #262626;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    min-height: 32px;
}

.btn:hover {
    border-color: #40a9ff;
    color: #40a9ff;
}

.btn-primary {
    background: #1890ff;
    border-color: #1890ff;
    color: #fff;
}

.btn-primary:hover {
    background: #40a9ff;
    border-color: #40a9ff;
    color: #fff;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
    min-height: 28px;
}

.btn-danger {
    background: #ff4d4f;
    border-color: #ff4d4f;
    color: #fff;
}

.btn-danger:hover {
    background: #ff7875;
    border-color: #ff7875;
    color: #fff;
}

/* 表单样式 */
.form-row {
    display: flex;
    gap: 24px;
    margin-bottom: 16px;
}

.form-item {
    flex: 1;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #262626;
}

.form-label.required::after {
    content: '*';
    color: #ff4d4f;
    margin-left: 4px;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s;
}

.form-control:focus {
    border-color: #40a9ff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-control::placeholder {
    color: #bfbfbf;
}

/* 复选框样式 */
.checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.checkbox input[type="checkbox"] {
    margin: 0;
}

/* 查询面板优化 */
.query-panel {
    background: #fafafa;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
}

.query-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 12px;
    margin-bottom: 12px;
}

.query-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

/* 操作栏样式 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 0 16px;
}

.toolbar-left {
    display: flex;
    gap: 8px;
}

.toolbar-right {
    display: flex;
    gap: 16px;
    align-items: center;
}

.search-box {
    width: 280px;
}

/* 表格样式 */
.table-container {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
}

.table th,
.table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: top;
}

.table th {
    background: #fafafa;
    font-weight: 500;
    color: #262626;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table tbody tr:hover {
    background-color: #f5f5f5;
}

.table-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* 统一操作按钮样式 */
.table-actions .btn {
    min-width: 70px;
    height: 28px;
    padding: 4px 8px;
    font-size: 12px;
    line-height: 1.2;
    text-align: center;
    white-space: nowrap;
}

/* 条目分组样式 */
.item-group {
    border-left: 3px solid #1890ff;
    margin-bottom: 1px;
}

.item-group .first-row {
    background: #f0f9ff;
}

/* 标签样式 */
.tag {
    display: inline-block;
    padding: 2px 8px;
    background: #f0f0f0;
    border-radius: 12px;
    font-size: 12px;
    margin-right: 4px;
    margin-bottom: 4px;
}

.tag.primary {
    background: #e6f7ff;
    color: #1890ff;
}

/* 状态标签 */
.status-tag {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-tag.processing {
    background: #fff7e6;
    color: #fa8c16;
}

.status-tag.completed {
    background: #f6ffed;
    color: #52c41a;
}

.status-tag.pending {
    background: #f0f0f0;
    color: #8c8c8c;
}

/* 满足度标签 */
.satisfaction-tag {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.satisfaction-tag.fc {
    background: #f6ffed;
    color: #52c41a;
}

.satisfaction-tag.pc {
    background: #fff7e6;
    color: #fa8c16;
}

.satisfaction-tag.nc {
    background: #fff2f0;
    color: #ff4d4f;
}

/* 进度条 */
.progress {
    display: flex;
    align-items: center;
    gap: 8px;
}

.progress-bar {
    width: 60px;
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #1890ff;
    transition: width 0.3s;
}

.progress-text {
    font-size: 12px;
    color: #8c8c8c;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 16px;
    gap: 16px;
    border-top: 1px solid #f0f0f0;
}

.pagination-info {
    color: #8c8c8c;
    font-size: 14px;
}

.pagination-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* 弹窗样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.45);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-dialog {
    background: #fff;
    border-radius: 8px;
    max-width: 800px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: 16px;
    font-weight: 500;
    color: #262626;
}

.modal-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #8c8c8c;
    padding: 4px;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

/* 文件上传 */
.upload-area {
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    padding: 24px;
    text-align: center;
    background: #fafafa;
    cursor: pointer;
    transition: all 0.3s;
}

.upload-area:hover {
    border-color: #40a9ff;
}

.upload-icon {
    font-size: 24px;
    color: #8c8c8c;
    margin-bottom: 8px;
}

.upload-text {
    color: #8c8c8c;
    font-size: 14px;
}

/* 富文本编辑器样式 */
.rich-editor {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    min-height: 120px;
}

.editor-toolbar {
    border-bottom: 1px solid #f0f0f0;
    padding: 8px 12px;
    background: #fafafa;
    display: flex;
    gap: 8px;
}

.editor-content {
    padding: 12px;
    min-height: 80px;
    outline: none;
}

/* 自动刷新提示 */
.auto-refresh {
    position: fixed;
    top: 80px;
    right: 24px;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 12px;
    color: #8c8c8c;
    z-index: 100;
}

/* 统计卡片样式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 24px;
}

.stat-item {
    text-align: center;
    padding: 16px;
    background: #fafafa;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
}

.stat-value {
    font-size: 32px;
    font-weight: bold;
    color: #1890ff;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 14px;
    color: #8c8c8c;
}

/* 快捷应答样式优化 */
#quick-response .form-row:last-child {
    margin-top: 32px;
}

#quick-response textarea {
    min-height: 120px;
    resize: vertical;
}

.help-text {
    font-size: 12px;
    color: #8c8c8c;
    margin-top: 8px;
    line-height: 1.4;
}

/* 数据分析样式优化 */
#data-analysis .card {
    border: 1px solid #f0f0f0;
}

#data-analysis .card-header {
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
}

#data-analysis .card-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #262626;
}

/* 人工应答页面样式 */
.match-source-section {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    margin-bottom: 16px;
    background: #fff;
}

.match-source-header {
    padding: 16px 20px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 8px 8px 0 0;
}

.match-source-header:hover {
    background: #f5f5f5;
}

.match-source-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #262626;
}

.toggle-icon {
    color: #8c8c8c;
    transition: transform 0.3s;
}

.toggle-icon.collapsed {
    transform: rotate(-90deg);
}

.match-source-content {
    padding: 16px 20px;
}

.match-source-content.collapsed {
    display: none;
}

.match-item {
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    margin-bottom: 16px;
    background: #fff;
    transition: all 0.3s;
}

.match-item:hover {
    border-color: #d9d9d9;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
}

.match-item-header {
    padding: 12px 16px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 6px 6px 0 0;
}

.match-score {
    display: flex;
    align-items: center;
    gap: 16px;
}

.match-percentage {
    font-size: 18px;
    font-weight: bold;
    color: #1890ff;
}

.match-label {
    font-size: 12px;
    color: #8c8c8c;
    margin-left: 8px;
}

.match-item-content {
    padding: 16px;
}

.match-field {
    margin-bottom: 12px;
    line-height: 1.5;
}

.match-field:last-child {
    margin-bottom: 0;
}

.match-field strong {
    color: #262626;
    margin-right: 8px;
}

/* 富文本编辑器增强 */
.editor-content[contenteditable="true"]:empty::before {
    content: "请输入应答说明...";
    color: #bfbfbf;
}

.editor-content[contenteditable="true"]:focus::before {
    content: "";
}

/* AI功能按钮样式 */
.ai-result-panel {
    margin-top: 16px;
    padding: 16px;
    background: #f6ffed;
    border: 1px solid #b7eb8f;
    border-radius: 6px;
    display: none;
}

.ai-result-panel.show {
    display: block;
}

.ai-result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.ai-result-title {
    font-weight: 500;
    color: #52c41a;
}

.ai-result-actions {
    display: flex;
    gap: 8px;
}

.ai-result-content {
    background: #fff;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid #d9f7be;
    white-space: pre-wrap;
    line-height: 1.6;
}

/* 版本对比样式 */
.version-compare-modal .modal-dialog {
    max-width: 1000px;
}

.version-compare-content {
    display: flex;
    gap: 24px;
}

.version-panel {
    flex: 1;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
}

.version-header {
    padding: 12px 16px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    font-weight: 500;
}

.version-content {
    padding: 16px;
    max-height: 400px;
    overflow-y: auto;
}

/* 匹配统计样式 */
.match-stats {
    display: flex;
    gap: 12px;
    align-items: center;
}

.match-stats .stat-item {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    background: #f0f0f0;
    color: #666;
}

.match-stats .stat-item.fc {
    background: #f6ffed;
    color: #52c41a;
}

.match-stats .stat-item.pc {
    background: #fff7e6;
    color: #fa8c16;
}

.match-stats .stat-item.nc {
    background: #fff2f0;
    color: #ff4d4f;
}

/* 匹配卡片网格布局 */
.match-cards-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 24px;
}

/* 匹配卡片样式 */
.match-card {
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 16px;
    background: #ffffff;
    transition: box-shadow 0.2s ease;
}

.match-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.match-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.match-score {
    display: flex;
    align-items: center;
    gap: 8px;
}

.match-percentage {
    font-size: 18px;
    font-weight: 600;
    color: #1890ff;
}

.match-card-content {
    font-size: 14px;
    line-height: 1.5;
}

.match-field {
    word-break: break-word;
}

/* 匹配分页样式 */
.match-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    margin-top: 24px;
    padding: 16px 0;
}

.match-pagination .pagination-info {
    font-size: 14px;
    color: #666;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .sidebar {
        width: 200px;
    }
    
    .content-area {
        padding: 16px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 16px;
    }
    
    .toolbar {
        flex-direction: column;
        gap: 16px;
    }
    
    .search-box {
        width: 100%;
    }

    .task-detail-container {
        flex-direction: column;
        height: auto;
    }

    .outline-panel {
        width: 100%;
        height: 200px;
    }

    .outline-panel.collapsed {
        height: 0;
        width: 100%;
    }

    .query-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }
    
    .stat-item {
        padding: 12px;
    }
    
    .stat-value {
        font-size: 24px;
    }

    .match-item-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .match-score {
        justify-content: space-between;
    }

    .version-compare-content {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .query-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* 编辑任务弹窗样式 */
.edit-task-modal .modal-dialog {
    max-width: 900px;
}

/* 列筛选样式 */
.column-filter {
    position: relative;
    display: inline-block;
}

.column-filter-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: #fff;
    color: #262626;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
    min-height: 32px;
}

.column-filter-btn:hover {
    border-color: #40a9ff;
    color: #40a9ff;
}

.column-filter-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 4px;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 100;
    min-width: 200px;
    display: none;
}

.column-filter-dropdown.show {
    display: block;
}

.column-filter-header {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    font-weight: 500;
    color: #262626;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.column-filter-content {
    padding: 8px 0;
    max-height: 300px;
    overflow-y: auto;
}

.column-filter-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.column-filter-item:hover {
    background-color: #f5f5f5;
}

.column-filter-item input[type="checkbox"] {
    margin-right: 8px;
}

.column-filter-actions {
    padding: 8px 16px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    gap: 8px;
}

.column-filter-actions .btn {
    margin-left: 8px;
} 

/* 权限管理弹窗样式 */
.radio-item {
    display: flex;
    align-items: center;
    gap: 6px;
}

.radio-item input[type="radio"] {
    margin: 0;
}

.personnel-selector {
    position: relative;
}

.personnel-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
}

.personnel-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
}

.personnel-item:hover {
    background-color: #f5f5f5;
}

.personnel-item:last-child {
    border-bottom: none;
}

.selected-personnel-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    min-height: 40px;
    padding: 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background-color: #fafafa;
}

.readonly-personnel-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    min-height: 40px;
    padding: 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background-color: #fafafa;
}

.personnel-tag {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    background-color: #1890ff;
    color: white;
    border-radius: 4px;
    font-size: 12px;
    gap: 6px;
    transition: all 0.3s ease;
}

.personnel-tag.to-remove {
    background-color: #ff4d4f;
    opacity: 0.8;
}

.personnel-tag .remove-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 14px;
    padding: 0;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
}

.personnel-tag .remove-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* 条目编辑状态样式 */
.table tbody tr.editing {
    background-color: #fff7e6;
}

.table tbody tr.editing td {
    border-color: #ffd591;
}

.editable-cell {
    position: relative;
}

.editable-input {
    width: 100%;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    background-color: white;
}

.editable-textarea {
    width: 100%;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    background-color: white;
    resize: vertical;
    min-height: 60px;
}

.editable-select {
    width: 100%;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    background-color: white;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    color: #bfbfbf;
}

.btn:disabled:hover {
    background: #f5f5f5;
    border-color: #d9d9d9;
    color: #bfbfbf;
}

/* 应答策略标签样式 */
.strategy-tag {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    background: #f0f9ff;
    color: #1e40af;
    border: 1px solid #bfdbfe;
}

/* 匹配位置信息样式 */
.match-location {
    font-size: 12px;
    color: #1890ff;
    margin-top: 4px;
    font-weight: 400;
} 