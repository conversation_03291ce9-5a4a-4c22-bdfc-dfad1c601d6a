package com.zte.mcrm.adapter.approval.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * @description: 待审批信息节点
 * @author: 10243305
 * @date: 2021/6/24 下午2:58
 */
@Data
public class ApprovingNodeInfo {
    @ApiModelProperty(value = "流程编码")
    private String flowCode;

    @ApiModelProperty(value = "审批任务ID")
    private String taskId;

    @ApiModelProperty(value = "流程实例ID")
    private String flowInstanceId;

    @ApiModelProperty(value = "关联业务ID")
    private String businessId;

    @ApiModelProperty(value = "节点ID")
    private String nodeId;

    @ApiModelProperty(value = "节点类型")
    private String nodeType;

    @ApiModelProperty(value = "节点名称")
    private String nodeName;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "业务字段")
    private Map<String, Object> businessParam;
}
