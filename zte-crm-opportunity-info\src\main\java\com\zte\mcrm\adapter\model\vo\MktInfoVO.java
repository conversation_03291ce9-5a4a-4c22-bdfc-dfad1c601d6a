package com.zte.mcrm.adapter.model.vo;

import lombok.Data;

@Data
public class MktInfoVO {

    private String customerLevel;

    private String customerLevelName;

    private String customerLevelNameEn;

    private String customerTypeCode;

    private String customerTypeCodeName;

    private String customerTypeCodeNameEn;

    private String customerSubTypeCode;

    private String customerSubTypeCodeName;

    private String customerSubTypeCodeNameEn;

    private String subcompanyCode;

    private String subcompanyName;

    private String subcompanyNameEn;

    private String zteBizCustomerType;

    private String zteBizCustomerTypeName;

    private String zteBizCustomerTypeNameEn;

    private String mtoCode;

    private String mtoName;

    private String mtoNameEn;

    private String mtoProperty;

    private String mtoPropertyName;

    private String mtoPropertyNameEn;

    private String mktCode;

    private String mktName;

    private String mktId;

    private String mvcFlag;

    private String mvcFlagName;

    private String mvcFlagNameEn;

    private String subMktCode;

    private String subMktName;

    private String subMktId;

    private String financeType;

    private String financeTypeName;

    private String financeTypeNameEn;
}
