package com.zte.mcrm.channel.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
* 渠道商新建最终客户记录 实体类
* <AUTHOR>
* @date 2023/05/10
*/

@Setter @Getter @ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description="渠道商新建最终客户记录")
public class OpptyCustomerCreateRecord implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    private String rowId;
    @ApiModelProperty(value = "商机报备主键id")
    private String optyId;
    @ApiModelProperty(value = "渠道商名称")
    private String customerName;
    @ApiModelProperty(value = "渠道商客户编码")
    private String crmCustomerCode;
    @ApiModelProperty(value = "最终客户名称")
    private String xLastAccName;
    @ApiModelProperty(value = "最终客户编码")
    private String xLastAccId;
    @ApiModelProperty(value = "所属省份/办事处编码")
    private String deptNo;
    @ApiModelProperty(value = "最终用户创建生效标识")
    private String effectedFlag;
    @ApiModelProperty(value = "渠道商提交商机时的客户状态")
    private String status;
    @ApiModelProperty(value = "最终用户行业编码")
    private String finalCustomerParentTrade;
    @ApiModelProperty(value = "最终用户子行业编码")
    private String finalCustomerChildTrade;
    @ApiModelProperty(value = "中兴业务经理短工号")
    private String businessManagerId;
    @ApiModelProperty(value = "中兴业务经理姓名")
    private String businessManagerName;
    @ApiModelProperty(value = "创建人")
    private String createdBy;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createdDate;
    @ApiModelProperty(value = "最后更新人")
    private String lastUpdatedBy;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "最后更新人")
    private Date lastUpdatedDate;
    @ApiModelProperty(value = "有效标识")
    private String enabledFlag;

}