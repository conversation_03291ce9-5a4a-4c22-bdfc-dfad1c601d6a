package com.zte.mcrm.channel.constant;

/**
 * <AUTHOR>
 * @date 2021/10/9
 */
public enum LastAccStatusEnum {
    /*
     * 无状态
     */
    NO_STATUS(0),
    /*
     * 待创建客户草稿
     */
    WAIT_CREATE_CUSTOMER_DRAFT(1),
    /*
     * 已创建客户草稿
     */
    CUSTOMER_DRAFT_CREATED(2),
    /*
     * 创建客户草稿后生效的客户
     */
    EFFECTIVE_CUSTOMER(3),
    /*
     * 客户系统本身已存在生效客户
     */
    INDIGENOUS_EFFECTIVE_CUSTOMER(4);

    private Integer key;

    LastAccStatusEnum(Integer key) {
        this.key = key;
    }

    public Integer getKey() {
        return this.key;
    }
}
