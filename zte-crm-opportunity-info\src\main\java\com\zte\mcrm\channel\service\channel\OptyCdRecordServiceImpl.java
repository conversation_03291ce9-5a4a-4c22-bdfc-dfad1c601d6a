package com.zte.mcrm.channel.service.channel;

import com.zte.mcrm.channel.dao.OptyCdRecordDao;
import com.zte.mcrm.channel.model.dto.OptyCdRecordDto;
import com.zte.mcrm.channel.util.ExceptionMsgUtils;
import com.zte.mcrm.common.util.CommonUtils;
import lombok.Synchronized;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OptyCdRecordServiceImpl implements IOptyCdRecordService{

    @Autowired
    OptyCdRecordDao optyCdRecordDao;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**** 用0做补充位 ***/
    public static final String FORMAT_0 = "%0";

    /**** 参数类型为正整数 ***/
    public static final String FORMAT_D = "d";

    /**
     * 根据前缀生成新的单据编号
     * @param prefix 前缀
     * @param digit 序列号长度
     * @return
     */
    @Override
    public String getNewOptyCd(String prefix, Integer digit) throws InterruptedException {
        logger.info("getNewOptyCd,prefix:{},digit:{}", prefix, digit);
        //最新流水
        String newOptyCdSerial = getNewOptyCdSerial(prefix, digit);

        String newOptyCd = prefix + newOptyCdSerial;
        String empNo = CommonUtils.getEmpNo();
        logger.info("getNewOptyCd,newOptyCd:{}", newOptyCd);
        try{
            insertOptyRecord(newOptyCd, empNo);
        }catch (Exception e){
            logger.error("getNewOptyCd_insert error,Exception:{}", ExceptionMsgUtils.getStackTrace(e, 2000));
            Thread.sleep(200);
            newOptyCdSerial = getNewOptyCdSerial(prefix, digit);
            newOptyCd = prefix + newOptyCdSerial;
            insertOptyRecord(newOptyCd, empNo);
        }

        return newOptyCd;
    }

    private String getNewOptyCdSerial(String prefix, Integer digit){
        //最新流水
        String newOptyCdSerial = "";
        //根据前缀获取最近一个评审单号
        String latelyEvaApplynumber = optyCdRecordDao.getLatelyOptyCdForUpdate(prefix);
        if(StringUtils.isNotEmpty(latelyEvaApplynumber)){
            //当前流水号
            String oldOptySerial = latelyEvaApplynumber.substring(
                    latelyEvaApplynumber.lastIndexOf(prefix)+prefix.length());
            //新流水号 当前流水号+number 0补位
            String format = FORMAT_0 + oldOptySerial.length() + FORMAT_D;
            int args = Integer.parseInt(oldOptySerial) + 1;
            newOptyCdSerial = String.format(format, args);
        }else{
            String format = FORMAT_0 + digit + FORMAT_D;
            newOptyCdSerial = String.format(format, 1);
        }
        return newOptyCdSerial;
    }

    private void insertOptyRecord(String newOptyCd, String empNo) {
        //保存评审单号
        OptyCdRecordDto optyCdRecordDto = OptyCdRecordDto.builder()
                .id(newOptyCd)
                .optyCd(newOptyCd)
                .createdBy(empNo).build();
        optyCdRecordDao.insert(optyCdRecordDto);
    }

    @Override
    public int update(OptyCdRecordDto optyCdRecordDto) {
        return optyCdRecordDao.update(optyCdRecordDto);
    }

    @Override
    public int insertBatch(List<OptyCdRecordDto> optyCdRecordDtoList) {
        return optyCdRecordDao.insertByBatch(optyCdRecordDtoList);
    }
}
