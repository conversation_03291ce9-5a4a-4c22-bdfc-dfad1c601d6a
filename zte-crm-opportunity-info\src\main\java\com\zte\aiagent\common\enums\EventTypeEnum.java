package com.zte.aiagent.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * 事件类型枚举
 */
@Getter
public enum EventTypeEnum {

    /** 文档创建事件 */
    DOCUMENT_CREATED("DocumentCreated", "文档创建事件"),
    /** 文档切片完成事件 */
    DOCUMENT_SLICED("DocumentSliced", "文档切片完成")
    ;

    private final String code;
    private final String description;

    EventTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

}
