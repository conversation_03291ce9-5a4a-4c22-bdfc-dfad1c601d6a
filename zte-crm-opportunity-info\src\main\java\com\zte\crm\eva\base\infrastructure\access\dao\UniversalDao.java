package com.zte.crm.eva.base.infrastructure.access.dao;

import com.zte.crm.eva.base.domain.universal.AggregationParams;
import com.zte.crm.eva.base.domain.universal.CommonTableMap;
import com.zte.crm.eva.base.domain.universal.WhereBO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-04-20
 */
public interface UniversalDao {

    /**
     * 保存记录
     * @param tableMap
     * @param rawMap
     */
    void saveRecord(CommonTableMap tableMap, Map<String, Object> rawMap);

    /**
     * 更新记录
     * @param tableMap
     * @param rawMap
     */
    void updateRecord(CommonTableMap tableMap, Map<String, Object> rawMap);

    /**
     * 删除记录
     * @param tableMap
     * @param rawMap
     */
    void deleteRecord(CommonTableMap tableMap, Map<String, Object> rawMap);

    /**
     * 查询记录总数
     * @param tableRealName
     * @param whereDTO
     * @return
     */
    Long countRecord(String tableRealName, WhereBO whereDTO);

    /**
     * 查询记录
     * @param fields
     * @param tableRealName
     * @param whereDTO
     * @return
     */
    List<Map<String, Object>> findRecordByPage(String fields, String tableRealName, WhereBO whereDTO);

    /**
     * 聚合查询
     * @param aggregationParams
     * @return
     */
    List<Map<String, Object>> selectAggregationByParams(AggregationParams aggregationParams);

    /**
     * 查询总条数
     * @param tableName 表名
     * @param conditionMap 条件列表
     * @return int
     */
    int selectEqCommonCount(@Param("tableName") String tableName, @Param("conditionMap") Map<String, Object> conditionMap);

    /**
     * 通用单独更新
     * @param tableName 表名
     * @param fieldMap  字段列表
     * @param conditionMap 条件列表
     * @param updateTimeFlag 更新时间标记
     * @param updateTimeMap 更新时间列表
     * @return int
     */
    int updateSingleCommon(@Param("tableName") String tableName, @Param("fieldMap") Map<String, Object> fieldMap,
                           @Param("conditionMap") Map<String, Object> conditionMap, @Param("updateTimeFlag") String updateTimeFlag, @Param("updateTimeMap") Map<String, Object> updateTimeMap);

}
