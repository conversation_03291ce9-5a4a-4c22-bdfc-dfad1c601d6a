package com.zte.mcrm.channel.constant;

/**
 * <AUTHOR>
 */

public enum AcctMergeFlagEnum {
    /**
     * M:主客户
     **/
    MAIN_CUSTOMER("M"),
    /**
     * H:被合并客户
     **/
    MERGED_CUSTOMER("H"),
    /**
     * HA标识被合并客户审批中
     **/
    MERGE_APPROVING_CUSTOMER("HA"),
    /**
     * HU--解除合并审批中
     **/
    UNMERGE_APPROVING_CUSTOMER("HU");

    private String value;

    AcctMergeFlagEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    /**
     * 根据标记判断是否合并客户
     * @param acctMergeFlag
     * @return
     */
    public static boolean mergeCustomer(String acctMergeFlag) {
        return AcctMergeFlagEnum.MERGED_CUSTOMER.getValue().equals(acctMergeFlag) || AcctMergeFlagEnum.UNMERGE_APPROVING_CUSTOMER.getValue().equals(acctMergeFlag);
    }
}
