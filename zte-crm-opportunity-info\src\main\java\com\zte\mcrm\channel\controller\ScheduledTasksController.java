package com.zte.mcrm.channel.controller;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.clouddisk.util.ServiceDataUtil;
import com.zte.mcrm.channel.service.OpptyCustomerCreateRecordService;
import com.zte.mcrm.channel.service.channel.IOpportunityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@Api(tags = "定时任务相关API")
@RestController
@RequestMapping("/scheduled/tasks")
public class ScheduledTasksController {

    @Resource
    @Lazy
    OpptyCustomerCreateRecordService opptyCustomerCreateRecordService;



    @Resource
    IOpportunityService iOpportunityService;



    @ApiOperation("更新客户状态")
    @GetMapping(value = "/refreshCustomerStatus")
    public ServiceData<Void> refreshCustomerStatus(){
        opptyCustomerCreateRecordService.refreshCustomerStatus();
        return ServiceDataUtil.success(null);
    }



    @ApiOperation("根据合规状态")
    @GetMapping("/dailyClosingTasksHandleComplianceStatus")
    public ServiceData<Void> sendRestrictedEntityMail( ){
        iOpportunityService.dailyClosingTasksHandleComplianceStatus();
        return ServiceDataUtil.success(null);
    }
}
