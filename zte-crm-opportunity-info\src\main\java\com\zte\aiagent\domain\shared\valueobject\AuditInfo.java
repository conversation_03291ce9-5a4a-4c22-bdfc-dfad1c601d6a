package com.zte.aiagent.domain.shared.valueobject;

import com.ctrip.framework.apollo.core.utils.StringUtils;
import lombok.Value;

import java.time.LocalDateTime;

/**
 * 审计信息值对象
 * 封装创建和更新的审计信息
 */
@Value
public class AuditInfo {

    /** 创建人 */
    String createdBy;

    /** 创建时间 */
    LocalDateTime createdDate;

    /** 最后更新人 */
    String lastUpdatedBy;

    /** 最后更新时间 */
    LocalDateTime lastUpdatedDate;

    private AuditInfo(String createdBy, LocalDateTime createdDate, String lastUpdatedBy, LocalDateTime lastUpdatedDate) {
        this.createdBy = createdBy;
        this.createdDate = createdDate;
        this.lastUpdatedBy = lastUpdatedBy;
        this.lastUpdatedDate = lastUpdatedDate;
    }

    /**
     * 工厂方法 - 创建新的审计信息
     */
    public static AuditInfo create(String operator) {
        if (StringUtils.isBlank(operator)) {
            throw new IllegalArgumentException("操作人不能为空");
        }
        LocalDateTime now = LocalDateTime.now();
        return new AuditInfo(operator, now, operator, now);
    }

    /**
     * 更新审计信息
     */
    public AuditInfo updateBy(String operator) {
        if (StringUtils.isBlank(operator)) {
            throw new IllegalArgumentException("操作人不能为空");
        }
        return new AuditInfo(this.createdBy, this.createdDate, operator, LocalDateTime.now());
    }
}
