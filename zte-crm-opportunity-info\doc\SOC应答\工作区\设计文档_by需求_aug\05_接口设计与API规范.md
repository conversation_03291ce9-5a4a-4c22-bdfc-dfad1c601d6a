# 第5章 接口设计与API规范

> **子代理5负责**: 作为接口设计专家，设计系统的所有对外接口，包括RESTful API、WebSocket实时通信接口和Agent交互接口，确保接口的标准化、易用性和安全性。

## 5.1 接口设计原则

### 5.1.1 设计理念

#### RESTful架构风格
采用REST（Representational State Transfer）架构风格，通过HTTP协议的标准方法（GET、POST、PUT、DELETE）操作资源，确保接口的语义清晰和易于理解。

#### 统一接口规范
制定统一的接口规范，包括URL命名、参数格式、响应结构等，确保所有接口的一致性和可预测性。

#### 版本化管理
支持API版本化管理，通过URL路径或请求头指定版本，确保向后兼容性和平滑升级。

### 5.1.2 设计原则

#### 易用性原则
- **直观的URL设计**: 资源路径清晰，符合业务逻辑
- **合理的参数设计**: 必填参数明确，可选参数有默认值
- **友好的错误信息**: 提供详细的错误描述和解决建议
- **完善的文档**: 提供详细的API文档和示例

#### 性能原则
- **分页查询**: 大数据量查询支持分页
- **字段筛选**: 支持按需返回字段，减少数据传输
- **缓存策略**: 合理使用HTTP缓存机制
- **压缩传输**: 支持gzip等压缩算法

#### 安全原则
- **身份认证**: 所有接口都需要身份认证
- **权限控制**: 基于角色和资源的细粒度权限控制
- **数据验证**: 严格的输入参数验证
- **安全传输**: 强制使用HTTPS协议

#### 可靠性原则
- **幂等性**: GET、PUT、DELETE操作保证幂等性
- **事务性**: 相关操作支持事务一致性
- **容错性**: 优雅处理异常情况
- **监控性**: 提供接口调用监控和告警

## 5.2 RESTful API设计

### 5.2.1 API架构概览

```
┌─────────────────────────────────────────┐
│              API网关层                   │
├─────────────────────────────────────────┤
│  路由转发 │ 负载均衡 │ 限流熔断 │ 安全认证 │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│              业务API层                   │
├─────────────────────────────────────────┤
│  任务API │ 条目API │ AI应答API │ 用户API │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│              服务层                      │
├─────────────────────────────────────────┤
│  业务逻辑 │ 数据访问 │ 外部集成 │ 缓存处理 │
└─────────────────────────────────────────┘
```

### 5.2.2 URL设计规范

#### 基础URL结构
```
https://api.soc.company.com/v1/{resource}
```

#### 资源命名规范
- **使用名词**: 资源名使用名词，不使用动词
- **复数形式**: 集合资源使用复数形式
- **层级关系**: 通过URL路径体现资源层级关系
- **小写字母**: 使用小写字母和连字符

#### URL示例
```
# 任务相关
GET    /api/v1/tasks                    # 获取任务列表
POST   /api/v1/tasks                    # 创建任务
GET    /api/v1/tasks/{taskId}           # 获取任务详情
PUT    /api/v1/tasks/{taskId}           # 更新任务
DELETE /api/v1/tasks/{taskId}           # 删除任务
POST   /api/v1/tasks/{taskId}/copy      # 复制任务

# 条目相关
GET    /api/v1/tasks/{taskId}/items     # 获取条目列表
POST   /api/v1/tasks/{taskId}/items     # 创建条目
GET    /api/v1/items/{itemId}           # 获取条目详情
PUT    /api/v1/items/{itemId}           # 更新条目
DELETE /api/v1/items/{itemId}           # 删除条目

# 应答相关
POST   /api/v1/items/{itemId}/products/{productId}/ai-response    # AI应答
PUT    /api/v1/items/{itemId}/products/{productId}/manual-response # 手工应答
GET    /api/v1/items/{itemId}/products/{productId}/ai-matches     # 获取匹配详情
```

### 5.2.3 HTTP方法使用规范

#### 标准HTTP方法
- **GET**: 获取资源，幂等操作，不修改服务器状态
- **POST**: 创建资源，非幂等操作，可能修改服务器状态
- **PUT**: 更新资源，幂等操作，完整替换资源
- **PATCH**: 部分更新资源，非幂等操作，部分修改资源
- **DELETE**: 删除资源，幂等操作，删除指定资源

#### 方法选择原则
```
操作类型          HTTP方法    幂等性    安全性
获取单个资源      GET         是        是
获取资源列表      GET         是        是
创建资源          POST        否        否
完整更新资源      PUT         是        否
部分更新资源      PATCH       否        否
删除资源          DELETE      是        否
```

### 5.2.4 请求参数设计

#### 查询参数规范
```
GET /api/v1/tasks?page=1&size=20&sort=createTime,desc&filter=status:active
```

**通用查询参数**:
- `page`: 页码，从1开始，默认1
- `size`: 每页大小，默认20，最大200
- `sort`: 排序字段和方向，格式：field,direction
- `filter`: 过滤条件，格式：field:value
- `fields`: 返回字段筛选，格式：field1,field2

#### 请求体设计
```json
{
  "taskName": "华为云Stack解决方案技术标",
  "country": "中国",
  "mtoBranch": "华为技术有限公司",
  "customer": "某银行",
  "project": "云计算平台建设项目",
  "dataSource": "GBBS"
}
```

**设计原则**:
- 使用驼峰命名法
- 必填字段明确标注
- 提供字段说明和示例
- 支持嵌套对象和数组

### 5.2.5 响应格式设计

#### 统一响应结构
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 业务数据
  },
  "timestamp": "2024-01-15T14:30:00Z",
  "requestId": "req_20240115_001"
}
```

#### 分页响应结构
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "size": 20,
    "pages": 5,
    "list": [
      // 数据列表
    ]
  },
  "timestamp": "2024-01-15T14:30:00Z",
  "requestId": "req_20240115_002"
}
```

#### 错误响应结构
```json
{
  "code": 400,
  "message": "参数错误",
  "error": {
    "type": "VALIDATION_ERROR",
    "details": [
      {
        "field": "taskName",
        "message": "任务名称不能为空",
        "code": "REQUIRED"
      }
    ]
  },
  "timestamp": "2024-01-15T14:30:00Z",
  "requestId": "req_20240115_003"
}
```

## 5.3 核心业务API设计

### 5.3.1 任务管理API

#### 创建任务
```
POST /api/v1/tasks
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
  "taskName": "华为云Stack解决方案技术标",
  "country": "中国",
  "mtoBranch": "华为技术有限公司",
  "customer": "某银行",
  "project": "云计算平台建设项目",
  "dataSource": "GBBS"
}

Response:
{
  "code": 200,
  "message": "success",
  "data": {
    "taskId": 123,
    "taskCode": "TASK001",
    "taskName": "华为云Stack解决方案技术标",
    "status": "未开始",
    "createTime": "2024-01-15T09:00:00Z"
  }
}
```

#### 查询任务列表
```
GET /api/v1/tasks?page=1&size=20&sort=createTime,desc&filter=status:active
Authorization: Bearer {token}

Response:
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "size": 20,
    "pages": 5,
    "list": [
      {
        "id": 123,
        "taskCode": "TASK001",
        "taskName": "华为云Stack解决方案技术标",
        "country": "中国",
        "customer": "某银行",
        "project": "云计算平台建设项目",
        "itemCount": 25,
        "completedCount": 20,
        "satisfaction": 85.5,
        "status": "进行中",
        "createUser": "张三（123456）",
        "createTime": "2024-01-15T09:00:00Z",
        "updateTime": "2024-01-15T16:30:00Z"
      }
    ]
  }
}
```

### 5.3.2 条目管理API

#### 批量导入条目
```
POST /api/v1/tasks/{taskId}/items/batch-import
Content-Type: multipart/form-data
Authorization: Bearer {token}

Request:
- file: Excel文件
- autoResponse: true
- overwriteWhenDuplicate: true

Response:
{
  "code": 200,
  "message": "success",
  "data": {
    "importJobId": "import_20240115_001",
    "totalRows": 50,
    "successCount": 48,
    "failureCount": 2,
    "failures": [
      {
        "row": 5,
        "itemCode": "CODE005",
        "error": "条目编号重复"
      },
      {
        "row": 12,
        "itemCode": "CODE012",
        "error": "产品权限不足"
      }
    ]
  }
}
```

#### 条目查询
```
GET /api/v1/tasks/{taskId}/items?page=1&size=20&filter=status:未应答
Authorization: Bearer {token}

Response:
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 50,
    "page": 1,
    "size": 20,
    "list": [
      {
        "id": 456,
        "itemCode": "CODE001",
        "description": "云平台基础架构能力要求...",
        "status": "已应答",
        "assignUser": "张三（123456）",
        "tags": ["基础架构", "云平台"],
        "products": [
          {
            "id": 789,
            "product": "华为云Stack",
            "status": "已应答",
            "satisfaction": "FC",
            "responseMethod": "AI",
            "responseContent": "华为云Stack提供...",
            "source": "GBBS",
            "sourceIndex": "GBBS-001",
            "matchScore": 95.5,
            "updateTime": "2024-01-15T10:30:00Z"
          }
        ],
        "createTime": "2024-01-15T09:00:00Z",
        "updateTime": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

### 5.3.3 AI应答API

#### 单个AI应答
```
POST /api/v1/items/{itemId}/products/{productId}/ai-response
Authorization: Bearer {token}

Response:
{
  "code": 200,
  "message": "success",
  "data": {
    "jobId": "job_20240115_001",
    "status": "pending",
    "estimatedTime": 30
  }
}
```

#### 批量AI应答
```
POST /api/v1/tasks/{taskId}/items/start-response
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
  "itemIds": [456, 457, 458],
  "products": ["华为云Stack", "FusionSphere"]
}

Response:
{
  "code": 200,
  "message": "success",
  "data": {
    "jobId": "job_20240115_002",
    "totalCount": 15,
    "estimatedTime": 300
  }
}
```

#### AI应答状态查询
```
GET /api/v1/ai/jobs/{jobId}/status
Authorization: Bearer {token}

Response:
{
  "code": 200,
  "message": "success",
  "data": {
    "jobId": "job_20240115_001",
    "status": "processing",
    "progress": 75,
    "totalCount": 20,
    "completedCount": 15,
    "failedCount": 1,
    "currentItem": "正在处理条目CODE015...",
    "startTime": "2024-01-15T14:00:00Z",
    "estimatedEndTime": "2024-01-15T14:05:00Z",
    "errors": [
      {
        "itemId": 460,
        "itemCode": "CODE010",
        "error": "匹配失败：数据源无相关数据"
      }
    ]
  }
}
```

### 5.3.4 数据分析API

#### 获取任务分析数据
```
GET /api/v1/tasks/{taskId}/analysis?assignee={userId}
Authorization: Bearer {token}

Response:
{
  "code": 200,
  "message": "success",
  "data": {
    "overview": {
      "totalCount": 25,
      "completedCount": 20,
      "pendingCount": 3,
      "processingCount": 2,
      "completionRate": 80.0,
      "fcCount": 15,
      "pcCount": 4,
      "ncCount": 1,
      "satisfactionRate": 85.0
    },
    "productStats": [
      {
        "product": "华为云Stack",
        "totalCount": 15,
        "completedCount": 12,
        "fcCount": 10,
        "pcCount": 2,
        "ncCount": 0,
        "satisfactionRate": 90.0
      }
    ],
    "trendData": [
      {
        "date": "2024-01-15",
        "completedCount": 5,
        "satisfactionRate": 88.0
      }
    ]
  }
}
```

### 5.3.5 文件管理API

#### 文件上传
```
POST /api/v1/files/upload
Content-Type: multipart/form-data
Authorization: Bearer {token}

Request:
- file: 文件内容
- type: 文件类型 (excel, image, document)
- category: 文件分类 (task_attachment, response_image)

Response:
{
  "code": 200,
  "message": "success",
  "data": {
    "fileId": "file_20240115_001",
    "fileName": "条目导入模板.xlsx",
    "fileSize": 1024000,
    "fileType": "excel",
    "downloadUrl": "https://files.soc.company.com/download/file_20240115_001",
    "uploadTime": "2024-01-15T14:30:00Z"
  }
}
```

#### 文件下载
```
GET /api/v1/files/{fileId}/download
Authorization: Bearer {token}

Response:
HTTP/1.1 200 OK
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
Content-Disposition: attachment; filename="条目导入模板.xlsx"
Content-Length: 1024000

[文件二进制内容]
```

## 5.4 WebSocket实时通信接口

### 5.4.1 WebSocket连接设计

#### 连接建立
```
WebSocket URL: wss://api.soc.company.com/v1/ws/connect
Headers:
  Authorization: Bearer {token}
  Sec-WebSocket-Protocol: soc-protocol-v1

连接成功响应:
{
  "type": "connection_established",
  "data": {
    "connectionId": "conn_20240115_001",
    "userId": "123456",
    "serverTime": "2024-01-15T14:30:00Z"
  }
}
```

#### 心跳机制
```
客户端发送:
{
  "type": "ping",
  "timestamp": "2024-01-15T14:30:00Z"
}

服务端响应:
{
  "type": "pong",
  "timestamp": "2024-01-15T14:30:01Z"
}
```

### 5.4.2 消息类型定义

#### AI应答进度推送
```json
{
  "type": "ai_answer_progress",
  "data": {
    "jobId": "job_20240115_001",
    "taskId": 123,
    "progress": 75,
    "completedCount": 15,
    "totalCount": 20,
    "currentItem": {
      "itemId": 456,
      "itemCode": "CODE015",
      "description": "正在处理网络安全防护能力要求..."
    },
    "estimatedEndTime": "2024-01-15T14:05:00Z",
    "errors": []
  }
}
```

#### 导入进度推送
```json
{
  "type": "import_progress",
  "data": {
    "importJobId": "import_20240115_001",
    "taskId": 123,
    "progress": 60,
    "processedRows": 30,
    "totalRows": 50,
    "currentRow": {
      "rowNumber": 30,
      "itemCode": "CODE030",
      "status": "processing"
    },
    "successCount": 28,
    "failureCount": 2,
    "errors": [
      {
        "row": 15,
        "itemCode": "CODE015",
        "error": "条目编号重复"
      }
    ]
  }
}
```

#### 任务状态变更通知
```json
{
  "type": "task_status_change",
  "data": {
    "taskId": 123,
    "taskName": "华为云Stack解决方案技术标",
    "oldStatus": "进行中",
    "newStatus": "已完成",
    "completionRate": 100,
    "satisfactionRate": 88.5,
    "changeTime": "2024-01-15T16:30:00Z",
    "message": "恭喜！任务已全部完成，总满足度88.5%"
  }
}
```

#### 条目指派通知
```json
{
  "type": "item_assigned",
  "data": {
    "taskId": 123,
    "taskName": "华为云Stack解决方案技术标",
    "assignedBy": {
      "userId": "123456",
      "userName": "张三（123456）"
    },
    "assignedTo": {
      "userId": "789012",
      "userName": "李四（789012）"
    },
    "items": [
      {
        "itemId": 456,
        "itemCode": "CODE001",
        "description": "网络安全防护能力要求"
      }
    ],
    "itemCount": 5,
    "assignTime": "2024-01-15T15:00:00Z",
    "message": "您有5个新的条目需要应答"
  }
}
```

### 5.4.3 客户端订阅机制

#### 订阅消息类型
```json
{
  "type": "subscribe",
  "data": {
    "channels": [
      "task:123",           // 订阅特定任务的消息
      "user:789012",        // 订阅用户相关消息
      "ai_jobs",            // 订阅AI任务消息
      "system_notifications" // 订阅系统通知
    ]
  }
}
```

#### 取消订阅
```json
{
  "type": "unsubscribe",
  "data": {
    "channels": [
      "task:123"
    ]
  }
}
```

## 5.5 Agent交互接口设计

### 5.5.1 Agent对话接口

#### 发送消息
```
POST /api/v1/agent/chat
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
  "message": "帮我创建一个中国电信的招标任务",
  "conversationId": "conv_20240115_001",
  "context": {
    "currentTaskId": 123,
    "currentItemId": 456
  },
  "attachments": [
    {
      "type": "file",
      "fileId": "file_20240115_001",
      "fileName": "条目清单.xlsx"
    }
  ]
}

Response:
{
  "code": 200,
  "message": "success",
  "data": {
    "reply": "好的，我来帮您创建中国电信的招标任务。请提供以下信息：\n1. 任务名称\n2. MTO分支\n3. 项目名称\n4. 是否需要上传条目文件？",
    "conversationId": "conv_20240115_001",
    "messageId": "msg_20240115_001",
    "nextActions": [
      {
        "action": "create_task",
        "label": "创建任务",
        "parameters": {
          "customer": "中国电信"
        }
      }
    ],
    "toolCalls": [],
    "timestamp": "2024-01-15T14:30:00Z"
  }
}
```

### 5.5.2 Agent工具调用接口

#### 创建任务工具
```
POST /api/v1/agent/tools/create-task
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
  "taskName": "中国电信5G基站建设项目SOC应答",
  "country": "中国",
  "mtoBranch": "北京分公司",
  "customer": "中国电信",
  "projectName": "5G基站建设项目",
  "dataSource": "GBBS"
}

Response:
{
  "code": 200,
  "message": "success",
  "data": {
    "taskId": 124,
    "taskCode": "TASK002",
    "callId": "call_20240115_001",
    "message": "任务创建成功"
  }
}
```

#### 查询任务工具
```
POST /api/v1/agent/tools/query-tasks
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
  "filters": {
    "taskName": "电信",
    "customer": "中国电信",
    "assignedToMe": true
  },
  "limit": 10
}

Response:
{
  "code": 200,
  "message": "success",
  "data": {
    "tasks": [
      {
        "id": 124,
        "taskCode": "TASK002",
        "taskName": "中国电信5G基站建设项目SOC应答",
        "status": "进行中",
        "itemCount": 50,
        "completedCount": 30,
        "assignedItemCount": 10,
        "createTime": "2024-01-15T09:00:00Z"
      }
    ],
    "totalCount": 5,
    "summary": "找到5个相关任务，其中1个正在进行中"
  }
}
```

### 5.5.3 对话上下文管理

#### 获取对话历史
```
GET /api/v1/agent/conversations/{conversationId}/history?limit=20
Authorization: Bearer {token}

Response:
{
  "code": 200,
  "message": "success",
  "data": {
    "conversationId": "conv_20240115_001",
    "messages": [
      {
        "messageId": "msg_20240115_001",
        "role": "user",
        "content": "帮我创建一个中国电信的招标任务",
        "timestamp": "2024-01-15T14:30:00Z"
      },
      {
        "messageId": "msg_20240115_002",
        "role": "assistant",
        "content": "好的，我来帮您创建中国电信的招标任务...",
        "timestamp": "2024-01-15T14:30:01Z"
      }
    ],
    "context": {
      "currentTaskId": 124,
      "currentItemId": null
    }
  }
}
```

## 5.6 错误处理与状态码

### 5.6.1 HTTP状态码使用规范

#### 成功状态码
- **200 OK**: 请求成功，返回数据
- **201 Created**: 资源创建成功
- **202 Accepted**: 请求已接受，异步处理中
- **204 No Content**: 请求成功，无返回数据

#### 客户端错误状态码
- **400 Bad Request**: 请求参数错误
- **401 Unauthorized**: 未认证或认证失败
- **403 Forbidden**: 无权限访问
- **404 Not Found**: 资源不存在
- **409 Conflict**: 资源冲突
- **422 Unprocessable Entity**: 参数验证失败
- **429 Too Many Requests**: 请求频率超限

#### 服务端错误状态码
- **500 Internal Server Error**: 服务器内部错误
- **502 Bad Gateway**: 网关错误
- **503 Service Unavailable**: 服务不可用
- **504 Gateway Timeout**: 网关超时

### 5.6.2 业务错误码定义

#### 通用错误码 (1000-1999)
- **1001**: 参数缺失
- **1002**: 参数格式错误
- **1003**: 参数值无效
- **1004**: 权限不足
- **1005**: 资源不存在

#### 任务相关错误码 (2000-2999)
- **2001**: 任务名称已存在
- **2002**: 任务状态不允许此操作
- **2003**: 任务不存在
- **2004**: 无权限访问该任务

#### 条目相关错误码 (3000-3999)
- **3001**: 条目编号重复
- **3002**: 条目状态不允许此操作
- **3003**: 产品权限不足
- **3004**: 条目正在应答中

#### AI服务相关错误码 (4000-4999)
- **4001**: AI服务不可用
- **4002**: 匹配失败
- **4003**: 应答生成失败
- **4004**: 任务队列已满

### 5.6.3 错误响应示例

#### 参数验证错误
```json
{
  "code": 422,
  "message": "参数验证失败",
  "error": {
    "type": "VALIDATION_ERROR",
    "errorCode": 1003,
    "details": [
      {
        "field": "taskName",
        "message": "任务名称长度不能超过200个字符",
        "code": "LENGTH_EXCEEDED",
        "value": "这是一个非常长的任务名称..."
      },
      {
        "field": "customer",
        "message": "客户名称不能为空",
        "code": "REQUIRED"
      }
    ]
  },
  "timestamp": "2024-01-15T14:30:00Z",
  "requestId": "req_20240115_001"
}
```

#### 业务逻辑错误
```json
{
  "code": 409,
  "message": "任务名称已存在",
  "error": {
    "type": "BUSINESS_ERROR",
    "errorCode": 2001,
    "details": {
      "conflictTaskId": 123,
      "conflictTaskName": "华为云Stack解决方案技术标",
      "suggestion": "请修改任务名称或选择其他名称"
    }
  },
  "timestamp": "2024-01-15T14:30:00Z",
  "requestId": "req_20240115_002"
}
```

---

**本章小结**: 接口设计遵循RESTful架构风格和统一规范，提供完整的API接口体系。WebSocket实时通信支持进度推送和状态通知，Agent交互接口提供智能对话能力。完善的错误处理机制确保接口的健壮性和用户体验。所有接口都经过精心设计，确保易用性、安全性和可维护性。
