package com.zte.mcrm.adapter.controller;

import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import com.zte.mcrm.adapter.model.AuthConstraintDTO;
import com.zte.mcrm.adapter.model.dto.IndustryTreeDTO;
import com.zte.mcrm.adapter.model.dto.OrganizationNode;
import com.zte.mcrm.adapter.model.dto.ProductTreeData;
import com.zte.mcrm.adapter.model.vo.IndustryTreeDataVO;
import com.zte.mcrm.adapter.model.vo.OrgConditionVO;
import com.zte.mcrm.adapter.model.vo.TreeNodeVO;
import com.zte.mcrm.adapter.service.PrmService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 对接三棵树 API
 * <AUTHOR>
 * @date 2021/09/16
 */
@Api(tags = "PRM侧 相关接口")
@RestController
@RequestMapping("/channel/tree")
public class PrmController {

    @Autowired
    PrmService prmService;

    @ApiOperation("查询行业树 -Get方式")
    @GetMapping("/getIndustryList")
    public ServiceData<IndustryTreeDataVO> listIndustry() throws Exception {
        IndustryTreeDataVO res = prmService.listIndustry("N");
        return ServiceResultUtil.success(res);
    }

    @ApiOperation("查询行业树形服务 -Get方式")
    @PostMapping("/getIndustryTree")
    public ServiceData<PageRows<TreeNodeVO>> getIndustryTree(@RequestBody IndustryTreeDTO industryTreeDTO) throws Exception {
        PageRows<TreeNodeVO> res = prmService.getIndustryTree(industryTreeDTO);
        return ServiceResultUtil.success(res);
    }

    @ApiOperation("查询所有子行业列表（权限平台子行业约束的回调接口） -Get方式")
    @GetMapping("/getSubIndustryList")
    public ServiceData<List<AuthConstraintDTO>> getSubIndustryList(@RequestParam(required = false, defaultValue = "N") String needInvalid) throws Exception {
        List<AuthConstraintDTO> resultList = prmService.getSubIndustryList(needInvalid);
        return ServiceResultUtil.success(resultList);
    }

    @ApiOperation("获取投资方所在地实体列表，-Get方式")
    @GetMapping(value="/organization/getorglist")
    public ServiceData<List<OrgConditionVO>> getOrganizationNodeList(@RequestParam @ApiParam("是否过滤渠道业务部") Boolean filterChannelDepartment) throws Exception {
        List<OrgConditionVO> organizationNodes = prmService.getOrganizationNodeListWithSorted(filterChannelDepartment);
        //返回统一的服务端数据
        return ServiceResultUtil.success(organizationNodes);
    }

    @ApiOperation("获取产品树列表，-Get方式")
    @GetMapping(value="/product/getproductlist")
    public ServiceData<List<ProductTreeData>> getProductList() throws Exception {
        List<ProductTreeData> products = prmService.getProductList();
        //返回统一的服务端数据
        return ServiceResultUtil.success(products);
    }

    @ApiOperation("根据行业id集合批量获取行业名称, -POST方式")
    @PostMapping(value="/getIndustryNames")
    public ServiceData<List<IndustryTreeDataVO>> getIndustryNames(@RequestBody List<String> param) throws Exception {
        List<IndustryTreeDataVO> industyrList = prmService.getIndustryNames(param, "Y");
        //返回统一的服务端数据
        return ServiceResultUtil.success(industyrList);
    }

    @ApiOperation("根据组织机构id批量获取组织机构名称, -POST方式")
    @PostMapping(value="/getOrganizationNames")
    public ServiceData<List<OrganizationNode>> getOrganizationNames(@RequestBody List<String> param) throws Exception {
        List<OrganizationNode> organizationList = prmService.getOrganizationNames(param);
        //返回统一的服务端数据
        return ServiceResultUtil.success(organizationList);
    }
}
