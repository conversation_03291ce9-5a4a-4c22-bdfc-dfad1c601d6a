package com.zte.aiagent.domain.repository;

import com.zte.aiagent.infrastruction.access.po.BidParseItemPO;
import java.util.List;

/**
 * 解析条目数据访问仓储接口
 * 负责解析条目相关的数据访问操作
 *
 * <AUTHOR>
 */
public interface BidParseItemRepository {

    /**
     * 插入解析条目记录
     * @param bidParseItem 解析条目PO对象
     * @return 影响的行数
     */
    int insert(BidParseItemPO bidParseItem);

    /**
     * 批量插入解析条目记录
     * @param items 解析条目列表
     * @return 影响的行数
     */
    int batchInsert(List<BidParseItemPO> items);

    /**
     * 根据ID查询解析条目
     * @param rowId 主键ID
     * @return 解析条目PO对象
     */
    BidParseItemPO selectByPrimaryKey(String rowId);

    /**
     * 根据解析记录ID查询条目列表
     * @param parseRecordId 解析记录ID
     * @return 解析条目列表
     */
    List<BidParseItemPO> selectByParseRecordId(String parseRecordId);

    /**
     * 根据解析记录ID和条目编码查询条目
     * @param parseRecordId 解析记录ID
     * @param itemCode 条目编码
     * @return 解析条目PO对象
     */
    BidParseItemPO selectByRecordIdAndItemCode(String parseRecordId, String itemCode);

    /**
     * 根据ID更新解析条目
     * @param bidParseItem 解析条目PO对象
     * @return 影响的行数
     */
    int updateByPrimaryKey(BidParseItemPO bidParseItem);

    /**
     * 根据ID删除解析条目
     * @param rowId 主键ID
     * @return 影响的行数
     */
    int deleteByPrimaryKey(String rowId);

    /**
     * 根据解析记录ID删除所有关联条目
     * @param parseRecordId 解析记录ID
     * @return 影响的行数
     */
    int deleteByParseRecordId(String parseRecordId);
}
