package com.zte.mcrm.common.util;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class ListUtil {

    public static <T> List<List<T>> subList(List<T> tList, Integer subNum) {
        // 新的截取到的list集合
        List<List<T>> tNewList = new ArrayList<List<T>>();
        // 要截取的下标上限
        Integer priIndex = 0;
        // 要截取的下标下限
        Integer lastIndex = 0;
        // 查询出来list的总数目
        Integer totalNum = tList.size();
        // 总共需要插入的次数
        Integer insertTimes = totalNum / subNum;
        List<T> subNewList ;
        for (int i = 0; i <= insertTimes; i++) {
            priIndex = subNum * i;
            lastIndex = priIndex + subNum;
            // 判断是否是最后一次
            if (i == insertTimes) {
                subNewList = tList.subList(priIndex, tList.size());
            } else {
                // 非最后一次
                subNewList = tList.subList(priIndex, lastIndex);

            }
            if (subNewList.size() > 0) {
                tNewList.add(subNewList);
            }
        }
        return tNewList;
    }

    /**
     * list转换为set
     * @param tList
     * @param <T>
     * @return
     */
    public static <T> Set<T> list2Set(List<T> tList){
        if(null==tList){
            return null;
        }
        Set<T> result=new HashSet<>();
        for(T t:tList){
            if(null!=t){
                result.add(t);
            }
        }
        return result;
    }

    /**
     * set转换为list
     * @param set
     * @param <T>
     * @return
     */
    public static <T> List<T> set2List(Set<T> set){
        if(null==set){
            return null;
        }

        List<T> result=new ArrayList<>(set);
        return result;
    }
}