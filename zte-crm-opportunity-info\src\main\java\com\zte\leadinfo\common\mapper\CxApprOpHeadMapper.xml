<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.leadinfo.common.mapper.CxApprOpHeadMapper">

    <resultMap id="BaseResultMap" type="com.zte.leadinfo.common.entity.CxApprOpHead">
            <id property="rowId" column="ROW_ID" jdbcType="VARCHAR"/>
            <result property="created" column="CREATED" jdbcType="TIMESTAMP"/>
            <result property="createdBy" column="CREATED_BY" jdbcType="VARCHAR"/>
            <result property="lastUpd" column="LAST_UPD" jdbcType="TIMESTAMP"/>
            <result property="lastUpdBy" column="LAST_UPD_BY" jdbcType="VARCHAR"/>
            <result property="curApprNodeId" column="CUR_APPR_NODE_ID" jdbcType="VARCHAR"/>
            <result property="descText" column="DESC_TEXT" jdbcType="VARCHAR"/>
            <result property="name" column="NAME" jdbcType="VARCHAR"/>
            <result property="submitUserId" column="SUBMIT_USER_ID" jdbcType="VARCHAR"/>
            <result property="approveTreeId" column="APPROVE_TREE_ID" jdbcType="VARCHAR"/>
            <result property="projManagerId" column="PROJ_MANAGER_ID" jdbcType="VARCHAR"/>
            <result property="objectId" column="OBJECT_ID" jdbcType="VARCHAR"/>
            <result property="deptFullPath" column="DEPT_FULL_PATH" jdbcType="VARCHAR"/>
            <result property="deptId" column="DEPT_ID" jdbcType="VARCHAR"/>
            <result property="deptName" column="DEPT_NAME" jdbcType="VARCHAR"/>
            <result property="approveObject" column="APPROVE_OBJECT" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ROW_ID,CREATED,CREATED_BY,
        LAST_UPD,LAST_UPD_BY,CUR_APPR_NODE_ID,
        DESC_TEXT,NAME,SUBMIT_USER_ID,
        APPROVE_TREE_ID,PROJ_MANAGER_ID,OBJECT_ID,
        DEPT_FULL_PATH,DEPT_ID,DEPT_NAME,
        APPROVE_OBJECT
    </sql>
</mapper>
