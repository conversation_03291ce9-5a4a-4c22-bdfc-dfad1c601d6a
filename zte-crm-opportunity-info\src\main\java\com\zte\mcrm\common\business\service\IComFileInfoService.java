package com.zte.mcrm.common.business.service;

import java.util.Map;

/**
 * @Author: 10245190 DengZ<PERSON>o
 * @Date: 2021年9月14日 16:01:58
 * @Version: V1.0
 */
public interface IComFileInfoService {

    /**
     * 获取附件上传下载地址
     * @param params
     *  xEmpNo        工号
     *  xLangId       语言
     *  businessId    业务ID
     *  businessType  业务类型
     *  operationFlag 操作标识（1：可以上传下载，0：只读）
     *  cipherGrade   是否加密（-99：不加密，2：可编辑，1：只读）
     * @return 附件上传下载地址
     * @throws Exception
     */
    String getUploadUrl(Map<String, String> params) throws Exception;

}
