package com.zte.leadinfo.common.mapper;

import com.zte.mcrm.lov.access.vo.ListOfValue;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Author:  刘自强10288407
 */
@Mapper
public interface LstValMapper {

    /**
     * 根据类型数据快码数据
     * @param lovType
     * @return
     */
    List<ListOfValue> getLovTypeValue(@Param("lovType") String lovType);
}
