package com.zte.mcrm.common.errorcode.enums;

import com.zte.mcrm.common.errorcode.util.ErrorCodeMessageUtil;
import org.jetbrains.annotations.PropertyKey;

import java.util.Arrays;
import java.util.Optional;

/**微服务名对应的请求无响应错误码
 * @Author: <EMAIL>
 * @Date: 2022/04/24
 * @Description:
 */
public enum ServiceNameToNoResponseErrorCodeEnum {
    /**
     * BY3113=服务器开小差了，请稍候重试---远程调用base服务无响应异常
     */
    BASE_SERVICE("zte-crm-ichannel-base", "OY3113"),
    /**
     * BY3123=服务器开小差了，请稍候重试---远程调用certification服务无响应异常
     */
    CERTIFICATION_SERVICE("zte-crm-ichannel-certification", "OY3123"),
    /**
     * BY3133=服务器开小差了，请稍候重试---远程调用opersupportmanage服务无响应异常
     */
    OPERSUPPORTMANAGE_SERVICE("zte-crm-ichannel-opersupportmanage", "OY3133"),
    /**
     * BY3143=服务器开小差了，请稍候重试---远程调用rebate服务无响应异常
     */
    REBATE_SERVICE("zte-crm-ichannel-rebate", "OY3143"),
    /**
     * 远程调用zte-bmt-ucs-api服务无响应异常
     */
    UCS_SERVICE ( "zte-bmt-ucs-api", "OY4103"),
    /**
     * 远程调用zte-iss-cpc-partnerservice服务无响应异常
     */
    CPC_SERVICE ( "zte-iss-cpc-partnerservice", "OY4203"),
    /**
     *远程调用zte-iss-approval-manage服务无响应异常
     */
    APPROVAL_SERVICE ( "zte-iss-approval-manage", "OY4303"),
    /**
     *远程调用其它第三方服务无响应异常
     */
    OTHER_SERVICE ( "otherThirdServiceName", "OY4903")
    ;

    public static String getByServiceName(String callerServiceName){
        ServiceNameToNoResponseErrorCodeEnum[] values = ServiceNameToNoResponseErrorCodeEnum.values();
        Optional<ServiceNameToNoResponseErrorCodeEnum> value = Arrays.stream(values).filter(x -> x.serviceName.equals(callerServiceName)).findFirst();
        return value.orElse(OTHER_SERVICE).errorCode;
    }


    private String serviceName;
    private String errorCode;

    ServiceNameToNoResponseErrorCodeEnum(String serviceName,  @PropertyKey(resourceBundle = ErrorCodeMessageUtil.ERRORCODEMESSAGES_BUNDLE) String errorCode) {
        this.serviceName = serviceName;
        this.errorCode = errorCode;
    }
}
