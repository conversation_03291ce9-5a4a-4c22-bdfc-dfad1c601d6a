package com.zte.mcrm.adapter.model.vo;

/**
 * <AUTHOR> by 10265625
 * @date 2021/9/16
 * @description
 */
public class OrgConditionVO {
    /*
     * 机构id
     **/
    private String organizationId;
    /*
     * 机构父id
     **/
    private String organizationParentId;
    /*
     * 机构层级
     **/
    private Integer hrLevel;
    /*
     * 机构名称
     **/
    private String organizationName;
    /*
     * 机构名称拼音简写
     **/
    private String organizationNameSimplifiedPinyin;

    /*
     * 机构别名
     **/
    private String organizationAlias;
    /*
     * 是否有效 1代表有效 0代表无效
     **/
    private String enabledFlag;
    /*
     * 是否业绩单位 1是 0不是
     **/
    private String isPerformanceDept;

    /*
     * 是否可见 Y N
     **/
    private String isVisible;

    /*
     * 组织状态ID（1有效，2待撤销，0已撤销）
     **/
    private Integer orgStatusId;

    /*
     * 机构标签 1标识总监办 2标识办事处
     **/
    private String orgLabel;

    public String getIsVisible() {
        return isVisible;
    }

    public void setIsVisible(String isVisible) {
        this.isVisible = isVisible;
    }

    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    public String getOrganizationParentId() {
        return organizationParentId;
    }

    public void setOrganizationParentId(String organizationParentId) {
        this.organizationParentId = organizationParentId;
    }

    public Integer getHrLevel() {
        return hrLevel;
    }

    public void setHrLevel(Integer hrLevel) {
        this.hrLevel = hrLevel;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getOrganizationAlias() {
        return organizationAlias;
    }

    public void setOrganizationAlias(String organizationAlias) {
        this.organizationAlias = organizationAlias;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag;
    }

    public String getIsPerformanceDept() {
        return isPerformanceDept;
    }

    public void setIsPerformanceDept(String isPerformanceDept) {
        this.isPerformanceDept = isPerformanceDept;
    }

    public String getOrgLabel() {
        return orgLabel;
    }

    public void setOrgLabel(String orgLabel) {
        this.orgLabel = orgLabel;
    }

    public Integer getOrgStatusId() {
        return orgStatusId;
    }

    public void setOrgStatusId(Integer orgStatusId) {
        this.orgStatusId = orgStatusId;
    }

    public String getOrganizationNameSimplifiedPinyin() {
        return organizationNameSimplifiedPinyin;
    }

    public void setOrganizationNameSimplifiedPinyin(String organizationNameSimplifiedPinyin) {
        this.organizationNameSimplifiedPinyin = organizationNameSimplifiedPinyin;
    }
}
