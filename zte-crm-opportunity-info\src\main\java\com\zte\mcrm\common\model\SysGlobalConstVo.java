package com.zte.mcrm.common.model;

/**
 * 请求头基础数据信息
 * <AUTHOR>
 * @date 2021/01/16
 */
public class SysGlobalConstVo {

	/**终端主机*/
	private String host;
	
	/**用户终端浏览器等信息*/
	private String userAgent;
	
	/**存放多语言ID的HTTP头*/
	private String xLangId;
	
	/**存放租户ID的HTTP头*/
	private String xTenantId;
	
	/**是否令牌验证的HTTP头*/
	private String xOrgId;
	
	/**存放令牌数据验证的HTTP头*/
	private String xAuthValue;
	
	/**存放员工短工号的HTTP头*/
	private String xEmpNo;
	
	/**存放调用方的服务名*/
	private String xOriginServiceName;
	
	/**加密标准*/
	private String xCryptoFlag;
	
	/**加密*/
	private String xCryptoAlgo;
	
	/**加密通道Hash*/
	private String xCryptoChannelHash;

    private String xItpValue;

    /**
     * 子公司租戶
     */
    private String xSubTenantId;

    private String authNotice;
    private String timeStampStr;
    private String authAccessKey;
    private String authSignature;
    /**
     * 公司ID
     */
    private String companyId;

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getxLangId() {
        return xLangId;
    }

    public void setxLangId(String xLangId) {
        this.xLangId = xLangId;
    }

    public String getxTenantId() {
        return xTenantId;
    }

    public void setxTenantId(String xTenantId) {
        this.xTenantId = xTenantId;
    }

    public String getxOrgId() {
        return xOrgId;
    }

    public void setxOrgId(String xOrgId) {
        this.xOrgId = xOrgId;
    }

    public String getxAuthValue() {
        return xAuthValue;
    }

    public void setxAuthValue(String xAuthValue) {
        this.xAuthValue = xAuthValue;
    }

    public String getxEmpNo() {
        return xEmpNo;
    }

    public void setxEmpNo(String xEmpNo) {
        this.xEmpNo = xEmpNo;
    }

    public String getxOriginServiceName() {
        return xOriginServiceName;
    }

    public void setxOriginServiceName(String xOriginServiceName) {
        this.xOriginServiceName = xOriginServiceName;
    }

    public String getxCryptoFlag() {
        return xCryptoFlag;
    }

    public void setxCryptoFlag(String xCryptoFlag) {
        this.xCryptoFlag = xCryptoFlag;
    }

    public String getxCryptoAlgo() {
        return xCryptoAlgo;
    }

    public void setxCryptoAlgo(String xCryptoAlgo) {
        this.xCryptoAlgo = xCryptoAlgo;
    }

    public String getxCryptoChannelHash() {
        return xCryptoChannelHash;
    }

    public void setxCryptoChannelHash(String xCryptoChannelHash) {
        this.xCryptoChannelHash = xCryptoChannelHash;
    }

    public String getxItpValue() {
        return xItpValue;
    }

    public void setxItpValue(String xItpValue) {
        this.xItpValue = xItpValue;
    }

    public String getAuthNotice() {
        return authNotice;
    }

    public void setAuthNotice(String authNotice) {
        this.authNotice = authNotice;
    }

    public String getTimeStampStr() {
        return timeStampStr;
    }

    public void setTimeStampStr(String timeStampStr) {
        this.timeStampStr = timeStampStr;
    }

    public String getAuthAccessKey() {
        return authAccessKey;
    }

    public void setAuthAccessKey(String authAccessKey) {
        this.authAccessKey = authAccessKey;
    }

    public String getAuthSignature() {
        return authSignature;
    }

    public void setAuthSignature(String authSignature) {
        this.authSignature = authSignature;
    }

    @Override
    public String toString() {
        return "SysGlobalConstVo{" +
                "host='" + host + '\'' +
                ", userAgent='" + userAgent + '\'' +
                ", xLangId='" + xLangId + '\'' +
                ", xTenantId='" + xTenantId + '\'' +
                ", xOrgId='" + xOrgId + '\'' +
                ", xAuthValue='" + xAuthValue + '\'' +
                ", xEmpNo='" + xEmpNo + '\'' +
                ", xOriginServiceName='" + xOriginServiceName + '\'' +
                ", xCryptoFlag='" + xCryptoFlag + '\'' +
                ", xCryptoAlgo='" + xCryptoAlgo + '\'' +
                ", xCryptoChannelHash='" + xCryptoChannelHash + '\'' +
                ", xItpValue='" + xItpValue + '\'' +
                ", authNotice='" + authNotice + '\'' +
                ", timeStampStr='" + timeStampStr + '\'' +
                ", authAccessKey='" + authAccessKey + '\'' +
                ", authSignature='" + authSignature + '\'' +
                '}';
    }

    public void setSubTenantId(String subTenantId) {
        this.xSubTenantId = subTenantId;
    }

    public String getxSubTenantId() {
        return xSubTenantId;
    }


}
