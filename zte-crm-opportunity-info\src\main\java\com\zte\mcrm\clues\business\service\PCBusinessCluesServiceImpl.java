package com.zte.mcrm.clues.business.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.zte.itp.authorityclient.entity.output.ReturnConstraintEntity;
import com.zte.itp.msa.core.locale.LocaleMessageSourceBean;
import com.zte.mcrm.account.access.vo.Account;
import com.zte.mcrm.account.business.service.AccountService;
import com.zte.mcrm.authority.business.IAuthorityClientService;
import com.zte.mcrm.clues.access.dao.CluesSaveDao;
import com.zte.mcrm.clues.access.dao.PCBusinessCluesDao;
import com.zte.mcrm.clues.access.vo.BusinessClues;
import com.zte.mcrm.clues.access.vo.BusinessCluesInfoVO;
import com.zte.mcrm.clues.common.CluesSysConst;
import com.zte.mcrm.clues.model.CluesToOptyDTO;
import com.zte.mcrm.clues.util.CluesAuthUtil;
import com.zte.mcrm.common.business.IOftenSearchService;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.model.PersonAndOrgInfoVO;
import com.zte.mcrm.common.util.*;
import com.zte.mcrm.lov.business.service.LovService;
import com.zte.mcrm.number.business.service.NumberService;
import com.zte.mcrm.oftensearch.access.vo.OftensearchVO;
import com.zte.mcrm.opportunity.access.dao.PCOpportunityDao;
import com.zte.mcrm.opportunity.access.vo.*;
import com.zte.mcrm.opportunity.business.service.PCBusinessOpporunityService;
import com.zte.mcrm.opportunity.common.CollectionGroupUtil;
import com.zte.mcrm.opportunity.common.OppSysConst;
import com.zte.mcrm.opportunity.utils.OppAuthUtils;
import com.zte.mcrm.opportunity.utils.PersonAndOrgInfoUtil;
import com.zte.mcrm.org.business.service.OrganizationService;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.common.model.HttpResultData;
import com.zte.springbootframe.common.model.RetCodeCopy;
import com.zte.springbootframe.common.model.ServiceDataCopy;
import com.zte.springbootframe.common.model.ServiceDataUtil;
import com.zte.springbootframe.common.serviceregister.MicroServiceWithConfigUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.page.PageQuery;
import com.zte.springbootframe.util.string.StringHelper;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/****
 *
 * <AUTHOR> @date 2021/1/22
 **/
@Service
public class PCBusinessCluesServiceImpl implements PCBusinessCluesService {

	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	@Autowired
	private PCOpportunityDao pcOpportunityDao;
	@Autowired
	private PCBusinessCluesDao pcbusinessCluesDao;
	@Autowired
	private LovService lovService;
	@Autowired
	private OrganizationService organizationService;
	@Autowired
	private CluesSaveDao cluesSaveDao;
	@Autowired
	private NumberService numberService;
	@Autowired
	private PCBusinessOpporunityService pcBusinessOpporunityService;
	@Autowired
	private AccountService accountService;
	@Autowired
	private LocaleMessageSourceBean localeMessageSourceBean;
	@Autowired
  	MessageSource messageSource;
	@Autowired
    IClueAuthService clueAuthService;
    @Autowired
    CluesSaveService cluesSaveService;
	@Autowired
	IOftenSearchService oftenSearchService;
	@Autowired
	IAuthorityClientService authorityClientService;

	@Override
	public List<BusinessClues> getCluesWithAuth(PageQuery<BusinessClues> pageQuery) throws Exception {
		getAuth(pageQuery);

		List<BusinessClues> businessClues = pcbusinessCluesDao.getCluesWithAuth(pageQuery);
		if(CollectionUtils.isEmpty(businessClues)){
            return businessClues;
        }
		// 客户名称 最终客户名称
		this.setAcctNameClueList(businessClues);
		// 信息填报人 归属客户经理
		this.setEmpNameNoClueList(businessClues);
		// 代表处
		this.setDeptNameClueList(businessClues);
        for (BusinessClues busClues : businessClues) {
            if(Objects.isNull(busClues)){
                continue;
            }
            // 设置其他
            setOtherInfo(busClues);
        }
		return businessClues;
	}

	private void getAuth(PageQuery<BusinessClues> pageQuery) {
		Auth authOrgs = clueAuthService.getAuthOrgs();
		pageQuery.getEntity().setAuth(authOrgs);
	}

	public String getKeyVal(Map<String, String> map, String code) {
		for (Map.Entry<String, String> m : map.entrySet()) {
			if (m.getKey().equals(code)) {
				return m.getValue();
			}
		}
		return null;
	}

	@Override
	public int countClues(PageQuery<BusinessClues> pageQuery) throws Exception {
		getAuth(pageQuery);
		return pcbusinessCluesDao.countClues(pageQuery);
	}

	/**
	 * 查询线索详情
	 *
	 * @param businessClues
	 * @return
	 * @throws Exception
	 */
	@Override
	public BusinessClues selectBaseInfo(BusinessClues businessClues) throws BusiException {
		BusinessClues result = new BusinessClues();
		try {
			result = pcbusinessCluesDao.selectBaseInfo(businessClues);
			if (null != result) {
				businessClues.setId(result.getId());
			} else {
				throw new BusiException("", localeMessageSourceBean.getMessage(CommonConst.CLUE_NOT_FOUND));
			}

			// 转换相关值信息
			setLovValue(result);

			//获取客户名称、运营商类型、集团客户简称、ZTE业务客户分类
			queryAccountByMicroService(result);
			// 设置代表处名字
			setDeptName(result);

			// 设置最终客户
            result.setLastAcctName(this.getAccountNameByID(result.getLastAcctId()));

			// 获取归属客户经理
			setOwnerInfo(result);

			setEmpInfo(result);

			setBackPerson(result);

			setTechMgr(result);

			// 设置权限
			setAuth(businessClues, result);

		} catch (Exception e) {
			logger.error("线索详情查询出现异常：" + e.getMessage(), e);
		}
		return result;
	}

	private void setAuth(BusinessClues businessClues, BusinessClues result) throws BusiException, RouteException {
		//当前人是否可以线索转立项
		boolean transferAuth = this.checktransferAuthClue(businessClues);
		//当前人是否可以线索分配
		boolean assignedAuth = this.checkAssignedAuthClue(businessClues);
		//当前登录人是否可以回退线索
		boolean backLeadAuth = this.isBelongCustomerMgr(businessClues) && this.isLeadUpdate(businessClues);

		if (transferAuth) {
			// 设置是否可转商机
			result.setIsTransfer(OppSysConst.FLAG_Y);
		}
		if (assignedAuth) {
			// 设置是否分配可线索
			result.setIsAssigned(OppSysConst.FLAG_Y);
		}
		if (backLeadAuth) {
			result.setIsBack(OppSysConst.FLAG_Y);
			result.setBackReason(lovService.getLovsAllByHttp(CluesSysConst.ZTE_LEAD_REFUSED_REASON, OppSysConst.FLAG_Y));
		}
		//只有线索状态为待客户经理更新，且当前人是“归属客户经理” 或职位为 “商机管理员”
        boolean mgrFlag = isBelongCustomerMgr(businessClues) || isOptyMgr(businessClues);
		if (isLeadUpdate(businessClues) && mgrFlag) {
			result.setIsRenewing(OppSysConst.FLAG_Y);
		}
		//判断是否有关闭权限：职务+我创建的+我负责+状态为：待客户经理更新/被退回/待分配/已发起转商机（可再次转商机）的线索
		if (checkClosedAuthClue(businessClues)) {
			result.setIsClosedAuth(OppSysConst.FLAG_Y);
		}
		//判断是否有还原权限：职务+我创建的+我负责+状态为:已关闭
		if (checkRestoreAuthClue(businessClues)) {
			result.setIsRestoreAuth(OppSysConst.FLAG_Y);
		}
	}

	/**
	 * 设置值列表信息
	 *
	 * @param result
	 * @throws Exception
	 */
	private void setLovValue(BusinessClues result) throws Exception {
        // 状态
        result.setStatus(lovService.getlovVal(CluesSysConst.ZTE_LEAD_STATUS, result.getStatusCode()));
        // 业务范围
        result.setBusinessType(lovService.getlovVal(CluesSysConst.ZTE_OPPTY_BUS_TYPE, result.getBusinessTypeCode()));
        // 业务范围Id
        result.setBusinessTypeId(lovService.getlovId(CluesSysConst.ZTE_OPPTY_BUS_TYPE, result.getBusinessTypeCode()));
        // 销售模式
        result.setSaleMode(lovService.getlovValByCode(CluesSysConst.ZTE_OPTY_SALES, result.getSaleModelCode(), lovService.getlovId(CluesSysConst.ZTE_OPPTY_TYPE, result.getAcctTypeCode())));
        // 销售模式Id
        result.setSaleModelId(lovService.getlovIdByCode(CluesSysConst.ZTE_OPTY_SALES, result.getSaleModelCode(), lovService.getlovId(CluesSysConst.ZTE_OPPTY_TYPE, result.getAcctTypeCode())));
        // 线索来源
        result.setClueSource(lovService.getlovVal(CluesSysConst.ZTE_OPPORTUNITY_SOURCE, pcBusinessOpporunityService.optySouceCodeToNewCode(CluesSysConst.ZTE_OPPORTUNITY_SOURCE, result.getClueSourceCode())));
        // 线索来源id
        result.setClueSourceId(lovService.getlovId(CluesSysConst.ZTE_OPPORTUNITY_SOURCE, pcBusinessOpporunityService.optySouceCodeToNewCode(CluesSysConst.ZTE_OPPORTUNITY_SOURCE, result.getClueSourceCode())));
        result.setClueSourceCode(pcBusinessOpporunityService.optySouceCodeToNewCode(CluesSysConst.ZTE_OPPORTUNITY_SOURCE, result.getClueSourceCode()));
        // 客户类型
        result.setAcctType(lovService.getlovVal(CluesSysConst.ZTE_OPPTY_TYPE, result.getAcctTypeCode()));
        // 客户类型id
        result.setAcctTypeId(lovService.getlovId(CluesSysConst.ZTE_OPPTY_TYPE, result.getAcctTypeCode()));
        // 国内国际
        result.setArea(lovService.getlovVal(CluesSysConst.ZTE_ACC_MARKET_SCOPE, result.getAreaCode()));
        // 行业
        result.setParentTrade(lovService.getlovVal(CluesSysConst.ZTE_PARENT_TRADE, result.getParentTradeCode()));
        // 行业id
        result.setParentTradeId(lovService.getlovId(CluesSysConst.ZTE_PARENT_TRADE, result.getParentTradeCode()));
        // 子行业
        result.setChildTrade(lovService.getlovVal(CluesSysConst.ZTE_CHILD_TRADE, result.getChildTradeCode()));
        // 子行业id
        result.setChildTradeId(lovService.getlovId(CluesSysConst.ZTE_CHILD_TRADE, result.getChildTradeCode()));
        // 关闭原因
        result.setReason(lovService.getlovVal(CluesSysConst.ZTE_LEAD_CLOSED_REASON, result.getReasonCode()));
        // 关闭原因Id
        result.setReasonId(lovService.getlovId(CluesSysConst.ZTE_LEAD_CLOSED_REASON, result.getReasonCode()));
        //退回原因
        result.setBackReasonVal(lovService.getlovVal(CluesSysConst.ZTE_LEAD_REFUSED_REASON, result.getBackReasonCode()));
		result.setMarketType(lovService.getlovVal(CluesSysConst.ZTE_MARKET_TYPE, result.getMarketTypeCode()));
		//是否融资
		result.setFoundFlg(lovService.getlovVal(CluesSysConst.ZTE_BOOLEAN_STATUS, result.getFoundFlgCode()));
		//客户属性
		result.setAccountAttribute(lovService.getlovVal(CluesSysConst.ZTE_ACCOUNT_ATTRIBUTE, result.getAccountAttributeCode()));
		//潜在融资模式
		result.setPotentialModel(lovService.getlovVal(CluesSysConst.ZTE_POTENTIAL_FOUND_MODEL, result.getPotentialModelCode()));
		//获取关闭原因值列表
		result.setReasonList(removeCloseReason(lovService.getLovsAllByHttp(CluesSysConst.ZTE_LEAD_CLOSED_REASON, OppSysConst.FLAG_Y)));

	}

	private void setTechMgr(BusinessClues result) throws BusiException {
		if (null != result.getTechMgrId() && !result.getTechMgrId().isEmpty()) {
			PersonAndOrgInfoVO employee = EmployeeUtil.getEmployeeByEmpNo(result.getTechMgrId());
			if(null != employee) {
				result.setTechMgrName(employee.getEmpName());
				result.setTechMgrNo(employee.getEmpNO());	
			}
		}
	}

	private void setBackPerson(BusinessClues result) throws BusiException {
		if (null != result.getBackPersonId() && !result.getBackPersonId().isEmpty()) {
			PersonAndOrgInfoVO employee = EmployeeUtil.getEmployeeByEmpNo(result.getBackPersonId());
			if(employee != null) {
                // 工号
				result.setBackPersonNum(employee.getEmpNO());
				result.setBackPerson(employee.getEmpName());	
			}
		}
	}

	@Override
	public boolean isBelongCustomerMgr(BusinessClues businessClues) {
		//当前登录人是否为归属客户经理
		BusinessClues result = pcbusinessCluesDao.selectBaseInfo(businessClues);
		if (null != result) {
			if (!businessClues.getEmpId().equals(result.getOwnerMgr())) {
				return false;
			}
		}
		return true;
	}

	@Override
	public List<BusinessClues> getRecentCluesWithAuth(PageQuery<BusinessClues> pageQuery, Map map) throws Exception {
		List<BusinessClues> businessClues = new ArrayList<>();
		//获取最近查询信息
		List<OftensearchVO> list = oftenSearchService.getOftenSearchList(RequestMessage.getEmpNo(), "020802", 1, 15);
		if (null != list && list.size() > 0) {
			businessClues = getBusinessClues(pageQuery, list);
			if (StringHelper.isNotEmpty(businessClues)) {

				for (BusinessClues busClues : businessClues) {

					if (busClues != null) {

						// 获取客户名称
                        busClues.setAcctName(this.getAccountNameByID(busClues.getAcctId()));
						// 设置最终客户
                        busClues.setLastAcctName(this.getAccountNameByID(busClues.getLastAcctId()));
						// 设置代表处
						setDeptName(busClues);
						// 获取信息填报人
						setEmpInfo(busClues);
						// 获取归属客户经理
						setOwnerInfo(busClues);
						//设置其他信息
						setOtherInfo(busClues);

					}
				}
			}
		}
		return businessClues;
	}

    private String getAccountNameByID(String acctId) throws BusiException {
        Account account = accountService.getAccount(acctId);
        if(null == account){
            return "";
        }
        return account.getAccountName();
    }

    private void setDeptName(BusinessClues busClues) throws BusiException {
		busClues.setOverseasArea(Boolean.FALSE);
		if (StringHelper.isEmpty(busClues.getDeptId())) {
			return;
		}
		// 部门查询
		PersonAndOrgInfoVO org = OrganizationUtil.getOrgByOrgNo(busClues.getDeptId());
		if(null == org){
			return;
		}
		// 设置代表处
		busClues.setDeptName(org.getHrOrgName());
		// 海外部门取消线索商机中销售模式填写
		List<String> overseasAreaOrgList = Arrays.asList("ORG0008312", "ORG0007128", "ORG0007137", "ORG2223727");
		List<String> orgIDPathList = Arrays.asList(org.getOrgIDPath().split("－"));
		for(String orgID : orgIDPathList) {
			if(overseasAreaOrgList.contains(orgID)) {
				busClues.setSaleMode("");
				busClues.setSaleModelCode("");
				busClues.setOverseasArea(Boolean.TRUE);
				return;
			}
		}
	}
	private List<BusinessClues> getBusinessClues(PageQuery<BusinessClues> pageQuery, List<OftensearchVO> list) throws Exception {
		List<BusinessClues> businessClues;
		String cluesMsg = "";
		BusinessOppFilterInitVo vo = new BusinessOppFilterInitVo();
		for (OftensearchVO oftensearchVO : list) {
			cluesMsg = oftensearchVO.getBizCode() + "," + cluesMsg;
		}
		vo.setOpptyCodeArr(reverseSelf(removeNullString(cluesMsg.split(","))));
		vo.setZteOpptyCode(cluesMsg.substring(0, cluesMsg.length() - 1));
		pageQuery.setFilterInitVo(vo);

		//获取权限
		getAuth(pageQuery);
		//2，根据线索编号查询有权限的线索
		businessClues = pcbusinessCluesDao.getRecentCluesWithAuth(pageQuery);
		return businessClues;
	}

	private void setOtherInfo(BusinessClues busClues) throws BusiException {
		try {

			// 销售模式
			Map<String, String> saleModeType = lovService.getlovValList(CluesSysConst.ZTE_ORDER_SALES);
			// 业务范围
			Map<String, String> busType = lovService.getlovValList(CluesSysConst.ZTE_OPPTY_BUS_TYPE);
			// 线索状态
			Map<String, String> clueStatus = lovService.getlovValList(CluesSysConst.ZTE_LEAD_STATUS);
			//线索来源
			Map<String, String> clueSource = lovService.getlovValList(CluesSysConst.ZTE_OPPORTUNITY_SOURCE);
			//客户类型
			Map<String, String> acctType = lovService.getlovValList(CluesSysConst.ZTE_OPPTY_TYPE);

			busClues.setSaleMode(getKeyVal(saleModeType, busClues.getSaleModelCode()));
			busClues.setBusinessType(getKeyVal(busType, busClues.getBusinessTypeCode()));
			busClues.setStatus(getKeyVal(clueStatus, busClues.getStatusCode()));
			busClues.setClueSource(getKeyVal(clueSource, pcBusinessOpporunityService.optySouceCodeToNewCode(CluesSysConst.ZTE_OPPORTUNITY_SOURCE, busClues.getClueSourceCode())));
			busClues.setClueSourceCode(pcBusinessOpporunityService.optySouceCodeToNewCode(CluesSysConst.ZTE_OPPORTUNITY_SOURCE, busClues.getClueSourceCode()));
			busClues.setAcctType(getKeyVal(acctType, busClues.getAcctTypeCode()));
		} catch (Exception e) {
			throw new BusiException(RetCodeCopy.BUSINESSERROR_CODE, messageSource.getMessage("list.of.values.get.exception",null,LocaleContextHolder.getLocale()));
		}
	}

	private void setOwnerInfo(BusinessClues busClues) throws BusiException {
		if (StringHelper.isNotEmpty(busClues.getOwnerMgr())) {
			PersonAndOrgInfoVO employee = EmployeeUtil.getEmployeeByEmpNo(busClues.getOwnerMgr());
			if(employee != null) {
                // 姓名
				busClues.setOwnerName(employee.getEmpName());
                // 工号
				busClues.setOwnerNo(employee.getEmpNO());
			}
		}
	}

	private void setEmpInfo(BusinessClues busClues) throws BusiException {
		if (null != busClues.getCreatedBy()) {
			PersonAndOrgInfoVO employee = EmployeeUtil.getEmployeeByEmpNo(busClues.getCreatedBy());
			if(null != employee) {
                // 姓名
				busClues.setCreateName(employee.getEmpName());
                //工号
				busClues.setCreatedBy(employee.getEmpNO());
			}
		}
	}

	public String[] removeNullString(String[] arr) {
		List<String> tmp = new ArrayList<>();
		for (String str : arr) {
			if (StringUtils.isNotBlank(str)) {
				tmp.add(str);
			}
		}
		arr = tmp.toArray(new String[0]);

		return arr;
	}

	@Override
	public boolean isLeadUpdate(BusinessClues clue) {
		//线索状态是否为待客户经理更新
		if (pcbusinessCluesDao.isLeadUpdateByNum(clue) > 0) {
			return true;
		}
		return false;
	}

	@Override
	public boolean checkClaimAuthWithClue(BusinessClues businessClues) throws BusiException {
		BusinessClues result = pcbusinessCluesDao.selectBaseInfo(businessClues);
		// 获取当前操作人的职位信息
		try {
			List<RoleInfomation> roleInfomations = authorityClientService.queryUserRole(businessClues.getEmpId());
			boolean addFlag = checkClaimFlagByPositionWithClues(roleInfomations);
			boolean statusFlag = result.getStatusCode().equalsIgnoreCase(OppSysConst.ASSIGNING) || result.getStatusCode().equalsIgnoreCase(OppSysConst.REFUSED);
			if (addFlag && statusFlag) {
				// 设置是否可转商机
				return true;
			}
		} catch (Exception e) {
			logger.error("获取操作人出现异常：" + e.getMessage(),e);
		}
		return false;
	}

	private Boolean checkClaimFlagByPositionWithClues(List<RoleInfomation> businessOppPositionList) {
		if (businessOppPositionList != null && !businessOppPositionList.isEmpty()) {
			for (RoleInfomation roleInfomation : businessOppPositionList) {
				if (checkPositionClaim(roleInfomation.getRoleNameEN())) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 线索认领：
	 * 	 商机管理员
	 * 	 运营商查看
	 * 	 政企网查看
	 * 	 运营商服务查看
	 * 	 政企网服务查看
	 * 	 服务查看
	 * 	 国代/办事处经理
	 * 	 副国代/办事处经理
	 * 	 商机综合查询员
	 * 	 商机总监
	 * 	 服务商机总监
	 * 	 片区技术负责人
	 * 	 大国CTO/片区CMO
	 * */
	private Boolean checkPositionClaim(String positionCode) {
		return OppSysConst.OPERATOR_VIEWER.equalsIgnoreCase(positionCode) ||
				OppSysConst.GOVERMENT_VIEWER.equalsIgnoreCase(positionCode) ||
				OppSysConst.OPERATOR_SERVICE_VIEWER.equalsIgnoreCase(positionCode) ||
				OppSysConst.GOVERMENT_SERVICE_VIEWER.equalsIgnoreCase(positionCode) ||
				OppSysConst.SERVICE_VIEWER.equalsIgnoreCase(positionCode) ||
				OppSysConst.OFFICE_MANAGER.equalsIgnoreCase(positionCode) ||
				OppSysConst.VICE_OFFICE_MANAGER.equalsIgnoreCase(positionCode) ||
				OppSysConst.OPPORTUNITY_QUERY.equalsIgnoreCase(positionCode) ||
				OppSysConst.OPPORTUNITY_DIRECTOR.equalsIgnoreCase(positionCode) ||
				OppSysConst.SERVICE_OPPORTUNITY_DIRECTOR.equalsIgnoreCase(positionCode) ||
				OppSysConst.AREA_TECH_MANAGER.equalsIgnoreCase(positionCode) ||
				OppSysConst.CTO.equalsIgnoreCase(positionCode);
	}

	@Override
	public boolean checkAssignedAuthWithClue(BusinessClues businessClues) throws BusiException {
		BusinessClues result = pcbusinessCluesDao.selectBaseInfo(businessClues);
		try {
			// 线索状态要为带客户经理更新或已发起转商机
			if(!result.getStatusCode().equalsIgnoreCase(OppSysConst.RENEWING) && !result.getStatusCode().equalsIgnoreCase(OppSysConst.OPPTY)) {
				return false;
			}
			if(RequestMessage.getEmpNo().equals(result.getOwnerMgr())) {
				return true;
			}
			// 只有服务商机总监和商机总监可以转商机
			List<RoleInfomation> roleInfomations = authorityClientService.queryUserRole(businessClues.getEmpId());
			return checkAssignedFlagByPositionWithClues(roleInfomations);

		} catch (Exception e) {
			logger.error("获取操作人出现异常：" + e.getMessage(),e);
		}
		return false;
	}

	@Override
	public boolean checktransferAuthClue(BusinessClues businessClues) throws BusiException, RouteException {
		BusinessClues result = pcbusinessCluesDao.selectBaseInfo(businessClues);
		// 获取归属客户经理
		setOwnerInfo(result);
		if(StringUtils.isBlank(result.getStatusCode())) {
			return false;
		}
		// 线索状态要为待客户经理更新和已发起转商机
		boolean statusFlag = OppSysConst.RENEWING.equalsIgnoreCase(result.getStatusCode()) || OppSysConst.OPPTY.equalsIgnoreCase(result.getStatusCode());
		if(!statusFlag) {
			return false;
		}
		// 是否为归属客户经理
		if(RequestMessage.getEmpNo().equals(result.getOwnerNo())) {
			return true;
		}
		// 当前登录人是否为商机总监
		return isOpportunityAdmin(result.getDeptId());
	}

	/**
     * 是否为商机管理员
     * */
	private boolean isOpportunityAdmin(String orgNo) throws RouteException {
		Map<String, ReturnConstraintEntity> authData = authorityClientService.getUserPowerFunctionCode(OppSysConst.OPPORTUNITY_CODE);
		if(null == authData) {
			return false;
		}
		// 获取授权组织
		List<String> orgs = OppAuthUtils.getAuthDataByConstrainCode(authData, OppSysConst.OPPORTUNITY_DIRECTOR_ORG);
		// 判断组织是否有线索权限
		if(CollectionUtils.isEmpty(orgs)) {
			return false;
		}
		// 查询线索的组织
		List<String> leadOrg = new ArrayList<>();
		leadOrg.add(orgNo);
		List<PersonAndOrgInfoVO> hrOrg = PersonAndOrgInfoUtil.getOrgInfo(leadOrg);
		if(CollectionUtils.isEmpty(hrOrg) || null == hrOrg.get(0)) {
			return false;
		}
		for(String authOrg : orgs) {
			if(hrOrg.get(0).getOrgIDPath().indexOf(authOrg) > 0) {
				return true;
			}
		}
		return false;
	}
	private Boolean checkAddFlagByPosition(List<BusinessOppPositionVo> businessOppPositionList) {
		if (businessOppPositionList != null && !businessOppPositionList.isEmpty()) {
			for (BusinessOppPositionVo businessOppPositionVo : businessOppPositionList) {
				if (checkPositionAdd(businessOppPositionVo.getPositionType())) {
					return true;
				}
			}
		}
		return false;
	}

	@Override
	public boolean checkAssignedAuthClue(BusinessClues businessClues) throws BusiException {
		BusinessClues result = pcbusinessCluesDao.selectBaseInfo(businessClues);
		// 获取当前操作人的职位信息
		try {
			List<RoleInfomation> roleInfomations = authorityClientService.queryUserRole(businessClues.getEmpId());
			boolean addFlag = checkAssignedFlagByPositionWithClues(roleInfomations);
			// 无归属客户经理且权限满足分配
            boolean statusFlag = result.getStatusCode().equalsIgnoreCase(OppSysConst.ASSIGNING) ||result.getStatusCode().equalsIgnoreCase(OppSysConst.REFUSED);
			if (addFlag && StringUtils.isEmpty(result.getOwnerMgr()) && statusFlag) {
				// 设置是否可转商机
				return true;
			}
		} catch (Exception e) {
			logger.error("获取操作人出现异常：" + e.getMessage(), e);
		}
		return false;
	}

	private Boolean checkAssignedFlagByPositionWithClues(List<RoleInfomation> businessOppPositionList) {
		if (businessOppPositionList != null && !businessOppPositionList.isEmpty()) {
			for (RoleInfomation roleInfomation : businessOppPositionList) {
				if (checkPositionAssigned(roleInfomation.getRoleNameEN())) {
					return true;
				}
			}
		}
		return false;
	}

    /**
     * 商机管理员
     * @param positionCode
     * @return
     */
	private Boolean checkPositionAdd(String positionCode) {
		return OppSysConst.OPPTY_ADMIN.equalsIgnoreCase(positionCode);
	}

    /**
     * 商机分配：商机总监、国代/办事处经理可分配
     * @param positionCode
     * @return
     */
	private Boolean checkPositionAssigned(String positionCode) {
		return OppSysConst.OPPORTUNITY_DIRECTOR.equalsIgnoreCase(positionCode) || OppSysConst.OFFICE_MANAGER.equalsIgnoreCase(positionCode)
				|| OppSysConst.SERVICE_OPPORTUNITY_DIRECTOR.equalsIgnoreCase(positionCode);
	}

	private void queryAccountByMicroService(BusinessClues result) throws BusiException {
		if (null != result.getAcctId()) {
			Account accountDetail = accountService.getAccount(result.getAcctId());
			if (null != accountDetail) {
				result.setAcctName(accountDetail.getAccountName());
				result.setMtoName(accountDetail.getMtoName());
				result.setZteBusiAccountCategory(accountDetail.getBusinessAccntTypeName());
				result.setOperatorType(accountDetail.getOperatorType());
				result.setOperatorLevel(accountDetail.getAccountLevel());
				// 国内国际
				result.setAreaCode(accountDetail.getCustRangeCode());
			}
		}
	}

	@Override
	public boolean checkHasAuthToReadClue(String empId, String clueNum) throws BusiException {
		BusinessClues businessClues = new BusinessClues();
		businessClues.setClueNum(clueNum);
 		businessClues = pcbusinessCluesDao.selectBaseInfo(businessClues);
		if (null == businessClues) {
			throw new BusiException("", localeMessageSourceBean.getMessage(CommonConst.DATA_NOT_FOUND));
		}
		String ownerId = Strings.nullToEmpty(businessClues.getOwnerMgr());
		// 如果负责人或者线索创建人是自己，则可以读取线索
		if (ownerId.equals(empId) || empId.equals(businessClues.getCreatedBy())) {
			return true;
		} else if (null != businessClues.getDeptId()) {
			// 如果负责人或者线索创建人不是自己，则查看线索所属组织是否是自己组织下级
			Auth authOrgs = clueAuthService.getAuthOrgs();
			businessClues.setAuth(authOrgs);
			return organizationService.checkLowerOrganizationByDeptIdAndUserId(businessClues);
		}
		return false;
	}

	/**
	 * 当前登录人是否为商机管理员
	 */
	@Override
	public boolean isOptyMgr(BusinessClues businessClues) throws BusiException {
		// 获取当前操作人的职位信息
		List<BusinessOppPositionVo> businessOppPositionList;
		try {
			businessOppPositionList = pcbusinessCluesDao.selectBusinessOppUserPosition(businessClues);
			boolean addFlag = checkAddFlagByPosition(businessOppPositionList);
			if (addFlag) {
				return true;
			}
		} catch (Exception e) {
			logger.error("获取操作人出现异常：" + e.getMessage(), e);
		}
		return false;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void assignedSavePC(BusinessClues businessClues) throws BusiException {
        // 部门ORG编码
        cluesSaveService.setOrgCodeClue(businessClues);
		// 更新主表字段：状态、最后更新人、最后更新时间
		cluesSaveDao.updateInfoInMainTable(businessClues);
		// 根据线索编号获取线索id
		BusinessClues entity = pcbusinessCluesDao.selectBaseInfo(businessClues);
		// 更新扩展表字段：归属客户经理Id
		if (null != entity) {
			businessClues.setSaleModelCode(entity.getSaleModelCode());
			businessClues.setPotentialModelCode(entity.getPotentialModelCode());
			businessClues.setAccountAttributeCode(entity.getAccountAttributeCode());
			businessClues.setId(entity.getId());
			businessClues.setParentTradeCode(entity.getParentTradeCode());
			businessClues.setChildTradeCode(entity.getChildTradeCode());
			businessClues.setLastAcctId(entity.getLastAcctId());
			cluesSaveDao.updateInfoInExTable(businessClues);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void claim(BusinessClues clue) throws BusiException {
        // 部门ORG编码
        cluesSaveService.setOrgCodeClue(clue);
		//更新主表：状态、最后更新时间、最后更新人
		cluesSaveDao.updateInfoInMainTable(clue);
		//获取主表id
		BusinessClues entity = pcbusinessCluesDao.selectBaseInfo(clue);
		if (null != entity) {
			clue.setId(entity.getId());
			clue.setSaleModelCode(entity.getSaleModelCode());
			clue.setPotentialModelCode(entity.getPotentialModelCode());
			clue.setAccountAttributeCode(entity.getAccountAttributeCode());
			clue.setParentTradeCode(entity.getParentTradeCode());
			clue.setChildTradeCode(entity.getChildTradeCode());
			clue.setLastAcctId(entity.getLastAcctId());
			//更新扩展表：归属客户经理id、退回原因、退回人、最后更新时间、最后更新人
			cluesSaveDao.updateInfoInExTable(clue);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void backClue(BusinessClues clue) throws BusiException {
        // 部门ORG编码
        cluesSaveService.setOrgCodeClue(clue);
		//更新主表：状态、最后更新时间、最后更新人
		cluesSaveDao.updateInfoInMainTable(clue);
		//获取主表id
		BusinessClues entity = pcbusinessCluesDao.selectBaseInfo(clue);
		if (null != entity) {
			clue.setId(entity.getId());
			clue.setSaleModelCode(entity.getSaleModelCode());
			clue.setPotentialModelCode(entity.getPotentialModelCode());
			clue.setAccountAttributeCode(entity.getAccountAttributeCode());
			clue.setParentTradeCode(entity.getParentTradeCode());
			clue.setChildTradeCode(entity.getChildTradeCode());
			clue.setLastAcctId(entity.getLastAcctId());
			//更新扩展表：归属客户经理id、退回原因、退回人、最后更新时间、最后更新人
			cluesSaveDao.updateInfoInExTable(clue);
		}
	}


	public List<ListOfValueOpty> getSpecialLovs(String type, BusinessCluesInfoVO businessCluesInfoVO) throws BusiException, RouteException {
		Map<String, String> header = new HashMap<>(3);
		header.put(SysGlobalConst.HTTP_HEADER_X_TENANT_ID, CluesSysConst.SIEBEL);
		if (StringUtils.isNotBlank(businessCluesInfoVO.getxLangId())) {
			header.put(SysGlobalConst.HTTP_HEADER_X_LANG_ID, businessCluesInfoVO.getxLangId());
		} else {
			header.put(SysGlobalConst.HTTP_HEADER_X_LANG_ID, CluesSysConst.ZH_CN);
		}

		header.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO, RequestMessage.getEmpNo());
		Map<String, Object> getParams = new HashMap<>(1);
		getParams.put(CluesSysConst.PARAM_LOVTYPE, type);
		return getHttpData(header, getParams);
	}

	@Override
	@Transactional(rollbackFor = BusiException.class)
	public BusinessCluesInfoVO initBusinessCluesInfo(BusinessCluesInfoVO businessCluesInfoVO) throws BusiException {
		try {
			// 获取业务范围值列表
			businessCluesInfoVO.setBusinessTypeList(getSpecialLovs(CluesSysConst.ZTE_OPPTY_BUS_TYPE, businessCluesInfoVO));
			// 获取线索来源值列表
			businessCluesInfoVO.setClueSourceList(getSpecialLovs(CluesSysConst.ZTE_OPPORTUNITY_SOURCE, businessCluesInfoVO));
			// 获取销售模式值列表
			businessCluesInfoVO.setSaleModeList(getSpecialLovs(CluesSysConst.ZTE_OPTY_SALES, businessCluesInfoVO));
			// 获取客户类型值列表
			businessCluesInfoVO.setCustTypeList(getSpecialLovs(CluesSysConst.ZTE_OPPTY_TYPE, businessCluesInfoVO));
			// 获取子行业值列表
			businessCluesInfoVO.setChildTradeList(getSpecialLovs(CluesSysConst.ZTE_CHILD_TRADE, businessCluesInfoVO));
			// 获取行业值列表
			businessCluesInfoVO.setParentTradeList(getSpecialLovs(CluesSysConst.ZTE_PARENT_TRADE, businessCluesInfoVO));
			businessCluesInfoVO.setMakeTypeList(getSpecialLovs(CluesSysConst.ZTE_MARKET_TYPE, businessCluesInfoVO));
			//是否融资
			businessCluesInfoVO.setFoundFlagList(getSpecialLovs(CluesSysConst.ZTE_BOOLEAN_STATUS, businessCluesInfoVO));
			//客户属性
			businessCluesInfoVO.setAccountAttributeList(getSpecialLovs(CluesSysConst.ZTE_ACCOUNT_ATTRIBUTE, businessCluesInfoVO));
			//潜在融资模式
			businessCluesInfoVO.setPotentialFoundModelList(getSpecialLovs(CluesSysConst.ZTE_POTENTIAL_FOUND_MODEL, businessCluesInfoVO));
			// 2，如果线索ID不为空，初始化线索详情信息
			if (null != businessCluesInfoVO.getId() && !businessCluesInfoVO.getId().isEmpty()) {
				BusinessClues businessClues = new BusinessClues();
				String emptyId = businessCluesInfoVO.getxEmpNo();
				businessClues.setId(businessCluesInfoVO.getId());
				businessClues.setEmpId(emptyId);
				businessClues.setxEmpNo(businessCluesInfoVO.getxEmpNo());
				businessClues.setxLangId(businessCluesInfoVO.getxLangId());
				businessClues.setxTenantId(businessCluesInfoVO.getxTenantId());
				businessCluesInfoVO.setBusinessClues(this.selectBaseInfo(businessClues));
			} else {
				String clueNum = numberService.generateClueCode();
				businessCluesInfoVO.setClueNum(clueNum);
			}
		} catch (Exception e) {
			logger.error("初始化线索出现异常：" + e.getMessage(), e);
		}
		return businessCluesInfoVO;
	}

	public List<ListOfValueOpty> getHttpData(Map<String, String> headerParamsMap, Map<String, Object> getParams) {
		List<ListOfValueOpty> valueTypeList = new ArrayList<>();
		try {
			HttpResultData httpResultLovs = MicroServiceWithConfigUtil.invokeServiceWithGetMethod("4",
					CluesSysConst.URL_LOVALL, getParams, headerParamsMap);
			if (httpResultLovs != null) {

				String resultBo = JacksonJsonConverUtil.beanToJson(httpResultLovs.getBo());
				List<ListOfValueOpty> valueTypeListTmp = JacksonJsonConverUtil.jsonToListBean(resultBo,
						new TypeReference<List<ListOfValueOpty>>() {
						});
				setCode(headerParamsMap, valueTypeList, valueTypeListTmp);
			}
		} catch (Exception e) {
			logger.error("初始化线索转换值列表出现异常：" + e.getMessage(), e);
		}
		return valueTypeList;
	}

	private void setCode(Map<String, String> headerParamsMap, List<ListOfValueOpty> valueTypeList, List<ListOfValueOpty> valueTypeListTmp) {
		if (StringHelper.isNotEmpty(valueTypeListTmp)) {
			String lang = getLangString(headerParamsMap);
			for (ListOfValueOpty listOfValue : valueTypeListTmp) {
				if (listOfValue != null) {

					addListOfValue(valueTypeList, lang, listOfValue);
				}
			}
		}
	}

	private void addListOfValue(List<ListOfValueOpty> valueTypeList, String lang, ListOfValueOpty listOfValue) {
		if (OppSysConst.FLAG_Y.equalsIgnoreCase(listOfValue.getActive()) && lang.equalsIgnoreCase(listOfValue.getLang())) {
			if (OppSysConst.OPERATOR_CODE.equals(listOfValue.getLovCode())) {
				listOfValue.setLovCode(OppSysConst.ACCT_TYPE_OPERATOR);
			} else if (OppSysConst.GOVERNMENT_CODE.equals(listOfValue.getLovCode())) {
				listOfValue.setLovCode(OppSysConst.GOVERMENT);
			}
			valueTypeList.add(listOfValue);
		}
	}

	private String getLangString(Map<String, String> headerParamsMap) {
		String lang = CluesSysConst.CHS;
		if (headerParamsMap.get(SysGlobalConst.HTTP_HEADER_X_LANG_ID).equals(CluesSysConst.EN_US)) {
			lang = CluesSysConst.ENU;
		}
		return lang;
	}

	public String[] reverseSelf(String[] param) throws Exception {
		if (param.length > 0) {
			String[] strings = param;
			for (int start = 0, end = strings.length - 1; start < end; start++, end--) {
				String temp = strings[end];
				strings[end] = strings[start];
				strings[start] = temp;
			}
			return strings;
		} else {
			return null;
		}
	}

	/**
	 * 线索转商机 PC
	 *
	 * @param businessClues
	 * @return
	 * @throws Exception
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public String transferSave(BusinessClues businessClues) throws Exception {
		//2，更新商机的信息和状态
		if (StringUtils.isNotBlank(businessClues.getParentTradeId()) && StringUtils.isNotBlank(businessClues.getParentTradeCode())) {
			businessClues.setParentTradeId(null);
		}
		if (StringUtils.isNotBlank(businessClues.getChildTradeId()) && StringUtils.isNotBlank(businessClues.getChildTradeCode())) {
			businessClues.setChildTradeId(null);
		}
        //状态
		businessClues.setStatusCode(CluesSysConst.STATUS_CODE_OPPTY);
        // 部门ORG编码
        cluesSaveService.setOrgCodeClue(businessClues);
		cluesSaveDao.updateInfoInMainTable(businessClues);
		cluesSaveDao.updateInfoInExTable(businessClues);
        //金额更新
		cluesSaveDao.updateAmountInExTable(businessClues);
		return this.transferFormApprove(businessClues);
	}

	/**
	 * PC线索转商机根据审批结果
	 *
	 * @param businessClues
	 * @return
	 * @throws Exception
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public String transferFormApprove(BusinessClues businessClues) throws Exception {
		//获取主表id
		BusinessClues entity = pcbusinessCluesDao.selectBaseInfo(businessClues);
		//转换成商机数据并保存
		BusinessOptyPCAddVO optyVo = this.buildClueToOptyVO(entity);
		String optyId = pcBusinessOpporunityService.saveOpportunityInfo(optyVo, businessClues.getEmpId());
		return optyId;
	}

	public BusinessOptyPCAddVO buildClueToOptyVO(BusinessClues businessClues) throws BusiException {
		BusinessOptyPCAddVO vo = new BusinessOptyPCAddVO();
		//业务范围、商机名称、商机来源、客户名称、客户类型、大产品线、最终用途、预计签单金额（万）、预计签单时间、代表处/办事处
		vo.setBusinessTypeCode(businessClues.getBusinessTypeCode());
		vo.setClueId(businessClues.getId());
		vo.setCurrencyId(businessClues.getCurrency());
		vo.setCurrencyCode(businessClues.getCurrency());
		vo.setName(businessClues.getClueName());
		vo.setOptySource(pcBusinessOpporunityService.optySouceCodeToNewCode(CluesSysConst.ZTE_OPPORTUNITY_SOURCE, businessClues.getClueSourceCode()));
		vo.setAcctId(businessClues.getAcctId());
        //最终客户默认当前客户
		vo.setLastAcctId(businessClues.getLastAcctId());
		vo.setAcctTypeCode(businessClues.getAcctTypeCode());
		vo.setBigProdLineId(businessClues.getBigProdcutLineId());
		vo.setBigProdLineName(businessClues.getBigProductLine());
		vo.setArchitectId(businessClues.getProdSystemId());
		vo.setArchitectName(businessClues.getProdSystem());
		vo.setPredictSignAmt(businessClues.getPredictSignAmt());
		vo.setPredictSignDate(businessClues.getPredictSignDate());
		vo.setDeptId(businessClues.getDeptId());
		vo.setDeptNo(businessClues.getDeptId());
		vo.setIsFundFlg(businessClues.getFoundFlgCode());
		vo.setAccountAttributeCode(businessClues.getAccountAttributeCode());
		vo.setPotentialModelCode(businessClues.getPotentialModelCode());
		vo.setParentTradeCode(businessClues.getParentTradeCode());
		vo.setChildTradeCode(businessClues.getChildTradeCode());
		//国内国际 根据客户带出
		if (StringUtils.isNotBlank(businessClues.getAcctId())) {
			Account account = accountService.getAccount(businessClues.getAcctId());
			if (null != account) {
				vo.setAreaCode(account.getCustRangeCode());
			}
		}
		//运营商、国际政企需要审批
		if (CluesSysConst.ACCT_TYPE_OPERATOR.equals(businessClues.getAcctTypeCode())) {
			vo.setStatusCode(CluesSysConst.STATUS_CODE_APPROVE);
		} else if (OppSysConst.GOVERMENT.equals(businessClues.getAcctTypeCode()) && OppSysConst.FLAG_Y.equals(vo.getAreaCode())) {
			vo.setStatusCode(CluesSysConst.STATUS_CODE_APPROVE);
		} else {
			vo.setStatusCode(CluesSysConst.STATUS_CODE_RENEWING);
		}
		vo.setCode(numberService.generateOptyCode());
        //市场类型
		vo.setMarketTypeCode(businessClues.getMarketTypeCode());
        //销售模式
		vo.setSaleModelId(businessClues.getSaleModelCode());
        //客户投资规模
		vo.setInvestmentScaleOfAcct(businessClues.getInvestmentScaleOfAcct());
		vo.setDatasouece(OppSysConst.DATASOURCE_PC);
		return vo;
	}

	@Override
	public boolean checkClosedAuthClue(BusinessClues businessClues) throws BusiException {
		BusinessClues result = pcbusinessCluesDao.selectBaseInfo(businessClues);
		// 职位满足关闭线索权限
		if (null == result) {
			return false;
		}
		if (!result.getStatusCode().equalsIgnoreCase(OppSysConst.ASSIGNING) && !result.getStatusCode().equalsIgnoreCase(OppSysConst.REFUSED)
				&& !result.getStatusCode().equalsIgnoreCase(OppSysConst.OPPTY) && !result.getStatusCode().equalsIgnoreCase(OppSysConst.RENEWING)) {
			// 设置是否可关闭
			return false;
		}
		try {
            // 获取当前操作人的职位信息
            List<BusinessOppPositionVo> businessOppPositionList = pcbusinessCluesDao.selectBusinessOppUserPosition(businessClues);
			//查看我创建的线索（信息填报人为自己）和查看我负责的线索（归属客户经理为自己）也能关闭
            boolean ownerMgr = null != result.getOwnerMgr() && result.getOwnerMgr().equals(businessClues.getEmpId());
			if (CollectionUtils.isNotEmpty(businessOppPositionList) || isBelongCustomerMgr(businessClues) || ownerMgr) {
				return true;
			}
		} catch (Exception e) {
			logger.error("获取操作人出现异常：" + e.getMessage(), e);
		}
		return false;
	}

	@Override
	public String closeSave(BusinessClues businessClues) throws BusiException {
		try {
			String returnMsg = "success";
			//更新主表：状态、最后更新时间、最后更新人
			businessClues.setStatusCode(CluesSysConst.STATUS_CODE_CLOSED);
            // 部门ORG编码
            cluesSaveService.setOrgCodeClue(businessClues);
			cluesSaveDao.updateInfoInMainTable(businessClues);
			//获取主表id
			BusinessClues entity = pcbusinessCluesDao.selectBaseInfo(businessClues);
			if (null != entity) {
				businessClues.setId(entity.getId());
				//更新扩展表：关闭原因、最后更新时间、最后更新人
				businessClues.setOwnerMgr(entity.getOwnerMgr());
				businessClues.setBackPerson(entity.getBackPersonId());
				businessClues.setBackReasonCode(entity.getBackReasonCode());
				businessClues.setSaleModelCode(entity.getSaleModelCode());
				businessClues.setPotentialModelCode(entity.getPotentialModelCode());
				businessClues.setAccountAttributeCode(entity.getAccountAttributeCode());
				businessClues.setParentTradeCode(entity.getParentTradeCode());
				businessClues.setChildTradeCode(entity.getChildTradeCode());
				businessClues.setLastAcctId(entity.getLastAcctId());
				cluesSaveDao.updateInfoInExTable(businessClues);
			}
			return returnMsg;
		} catch (Exception e) {
			logger.error("PC关闭线索出现异常：" + e.getMessage(), e);
			throw new BusiException(localeMessageSourceBean.getMessage(CommonConst.CLUE_CLOSE_FAIL), e.getMessage());
		}
	}

	/**
	 * 线索关闭原因去掉合并线索
	 *
	 * @param list
	 * @return
	 * @throws Exception
	 */
	public List removeCloseReason(List<com.zte.mcrm.lov.access.vo.ListOfValue> list) throws Exception {
		List<com.zte.mcrm.lov.access.vo.ListOfValue> resultList = new ArrayList<>();
		if (null != list && list.size() > 0) {
			for (com.zte.mcrm.lov.access.vo.ListOfValue listOfValue : list) {
                //线索合并去掉
				if (!"Merge".equals(listOfValue.getLovCode())) {
					resultList.add(listOfValue);
				}
			}
		}
		return resultList;
	}

	@Override
	public boolean checkRestoreAuthClue(BusinessClues businessClues) throws BusiException {
		BusinessClues result = pcbusinessCluesDao.selectBaseInfo(businessClues);
		// 职位满足关闭线索权限
		if (null == result) {
			return false;
		}
		if (!result.getStatusCode().equalsIgnoreCase(CluesSysConst.STATUS_CODE_CLOSED)) {
			//判断状态是否是已关闭
			return false;
		}
		try {
            boolean ownerMgr = null != result.getOwnerMgr() && result.getOwnerMgr().equals(businessClues.getEmpId());
            // 获取当前操作人的职位信息
            List<BusinessOppPositionVo> businessOppPositionList = pcbusinessCluesDao.selectBusinessOppUserPosition(businessClues);
			//查看我创建的线索（信息填报人为自己）和查看我负责的线索（归属客户经理为自己）也能关闭
			if (CollectionUtils.isNotEmpty(businessOppPositionList) || isBelongCustomerMgr(businessClues) || ownerMgr) {
				return true;
			}
		} catch (Exception e) {
			logger.error("获取操作人出现异常：" + e.getMessage(), e);
		}
		return false;
	}

	@Override
	public String clueRestore(BusinessClues businessClues) throws BusiException {
		String returnMsg = "success";
		//更新主表：状态、最后更新时间、最后更新人
		businessClues.setStatusCode(CluesSysConst.STATUS_CODE_DRAFT);
        // 部门ORG编码
        cluesSaveService.setOrgCodeClue(businessClues);
		cluesSaveDao.updateInfoInMainTable(businessClues);
		return returnMsg;
	}

	@Override
	public boolean checkDeleteAuthClue(BusinessClues businessClues) throws BusiException {
		BusinessClues result = pcbusinessCluesDao.selectBaseInfoByClueId(businessClues);
		// 删除人为线索填报人是可以删除
		if (null == result) {
			return false;
		}
		if (businessClues.getEmpId().equals(result.getCreatedBy())) {
			// 设置是否可删除
			return true;
		}
		return false;
	}

	@Override
	public boolean delteClue(BusinessClues businessClues) throws BusiException {
		return pcbusinessCluesDao.deleteClue(businessClues);
	}

	/**
	 * 查询客户是否是禁运国黑名单里面
	 *
	 * @param dto
	 * @return
	 * @date 2019年7月29日
	 * <AUTHOR>
	 */
	@Override
	public boolean queryForbiddon(CluesToOptyDTO dto) {
		// 客户ID 最终客户ID
		List<String> acctIdList = Arrays.asList(dto.getAcctId(), dto.getLastAcctId());
        List<Account> accountList = new ArrayList<>();
        try{
            accountList = AccountUtil.getAccountListByids(acctIdList);
        }catch (Exception e){
            e.printStackTrace();
        }
        if(CollectionUtils.isEmpty(accountList)){
            // 非禁运国
            return false;
        }
        // 客户受制裁主体编码
        List<String> sanctionedPatryCodeList = accountList.stream().map(Account::getSanctionedPatryCode).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(sanctionedPatryCodeList) || !sanctionedPatryCodeList.contains(CluesSysConst.IS_FORBIDDON)){
            // 非禁运国
            return false;
        }
        // 禁运国
        return true;
	}

	@Override
	public int getMyClueCount() {
		if(StringUtils.isBlank(RequestMessage.getCurrentEmpNo())) {
			return 0;
		}
		return pcbusinessCluesDao.getMyClueCount(RequestMessage.getCurrentEmpNo());
	}

    @Override
    public List<BusinessClues> updateOrgCodePath(String orgCode, String clueNum) throws Exception {
	    // 查询需要更新的线索
        List<BusinessClues> businessCluesList = pcbusinessCluesDao.getCluesListByOrgCodeClueNum(orgCode, clueNum);
        if(CollectionUtils.isEmpty(businessCluesList)){
            return Collections.emptyList();
        }
        // 查询ORG编码全路径
        Set<String> deptIdSet = businessCluesList.stream().map(BusinessClues::getDeptId).collect(Collectors.toSet());
        List<PersonAndOrgInfoVO> orgInfoList = OrganizationUtil.getOrgsByOrgNos(deptIdSet);
        if(CollectionUtils.isEmpty(orgInfoList)){
            return Collections.emptyList();
        }
        orgInfoList = orgInfoList.stream().filter(item->StringUtils.isNotBlank(item.getHrOrgID())).collect(Collectors.toList());
        Map<String, String> authOrgPathMap = orgInfoList.stream().collect(Collectors.toMap(PersonAndOrgInfoVO::getHrOrgID, PersonAndOrgInfoVO::getOrgIDPath, (value1, value2) -> value1));
        if(MapUtils.isEmpty(authOrgPathMap)){
            return Collections.emptyList();
        }
        for(BusinessClues businessClues : businessCluesList){
            if(authOrgPathMap.containsKey(businessClues.getDeptId())){
                businessClues.setOrgCodePath(authOrgPathMap.get(businessClues.getDeptId()));
            }
        }
        // 更新ORG编码全路径
        List businessCluesGroupList = CollectionGroupUtil.groupListByQuantity(businessCluesList, 500);
        businessCluesGroupList.stream().forEach(item -> {
            pcbusinessCluesDao.updateOrgCodePathList((List<BusinessClues>) item);
        });
        // 返回
        return businessCluesList;
    }

    @Override
    public ServiceDataCopy<List<BusinessClues>> getBusinessCluesByPage(PageQuery<BusinessClues> pageQuery) throws Exception {
	    // 权限
        Auth auth = clueAuthService.getBusinessClueListAuth();
        pageQuery.getEntity().setAuth(auth);
        // 我的所有线索总数量查询
        int countAllType = pcbusinessCluesDao.countAllTypeClue(pageQuery);
        if (countAllType == 0) {
            return ServiceDataUtil.success(Collections.emptyList(), pageQuery.getMapOfPageQuery());
        }
        pageQuery.setCount(countAllType);
        // 查询
        List<BusinessClues> businessCluesList = pcbusinessCluesDao.getAllTypeClueList(pageQuery);
        if(CollectionUtils.isEmpty(businessCluesList)){
            return ServiceDataUtil.success(Collections.emptyList(), pageQuery.getMapOfPageQuery());
        }
        // 客户名称 最终客户名称
        this.setAcctNameClueList(businessCluesList);
        // 信息填报人 归属客户经理
        this.setEmpNameNoClueList(businessCluesList);
        // 代表处
        this.setDeptNameClueList(businessCluesList);
        // 设置其他
        for (BusinessClues busClues : businessCluesList) {
            setOtherInfo(busClues);
        }
        return ServiceDataUtil.success(businessCluesList, pageQuery.getMapOfPageQuery());
    }

    private void setDeptNameClueList(List<BusinessClues> businessCluesList) throws Exception {
        // 查询部门信息
        Set<String> deptIdSet = businessCluesList.stream().map(BusinessClues::getDeptId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(deptIdSet)){
            return ;
        }
        List<PersonAndOrgInfoVO> personAndOrgInfoList = OrganizationUtil.getOrgsByOrgNos(deptIdSet);
        Map<String, String> personAndOrgInfoMap = personAndOrgInfoList.stream().collect(Collectors.toMap(PersonAndOrgInfoVO::getHrOrgID, PersonAndOrgInfoVO::getHrOrgName, (entity1, entity2) -> entity1));
        if(personAndOrgInfoMap.isEmpty()){
            return ;
        }
        for(BusinessClues businessClues : businessCluesList){
            // 代表处
            if(personAndOrgInfoMap.containsKey(businessClues.getDeptId())){
                businessClues.setDeptName(personAndOrgInfoMap.get(businessClues.getDeptId()));
            }
        }
    }

    private void setEmpNameNoClueList(List<BusinessClues> businessCluesList) throws Exception {
	    // 查询人员信息
        Set<String> empNoSet = new HashSet<>();
        empNoSet.addAll(businessCluesList.stream().map(BusinessClues::getCreatedBy).collect(Collectors.toList()));
        empNoSet.addAll(businessCluesList.stream().map(BusinessClues::getOwnerMgr).collect(Collectors.toList()));
        if(CollectionUtils.isEmpty(empNoSet)){
            return ;
        }
        List<PersonAndOrgInfoVO> personAndOrgInfoList = EmployeeUtil.getEmployeesByEmpNos(empNoSet);
        Map<String, PersonAndOrgInfoVO> personAndOrgInfoMap = personAndOrgInfoList.stream().collect(Collectors.toMap(PersonAndOrgInfoVO::getEmpNO, Function.identity(), (entity1, entity2) -> entity1));
        if(personAndOrgInfoMap.isEmpty()){
            return ;
        }
        for(BusinessClues businessClues : businessCluesList){
            // 信息填报人
            if(personAndOrgInfoMap.containsKey(businessClues.getCreatedBy())){
                businessClues.setCreateName(personAndOrgInfoMap.get(businessClues.getCreatedBy()).getEmpName());
                businessClues.setCreatedBy(personAndOrgInfoMap.get(businessClues.getCreatedBy()).getEmpNO());
            }
            // 归属客户经理
            if(personAndOrgInfoMap.containsKey(businessClues.getOwnerMgr())){
                businessClues.setOwnerName(personAndOrgInfoMap.get(businessClues.getOwnerMgr()).getEmpName());
                businessClues.setOwnerNo(personAndOrgInfoMap.get(businessClues.getOwnerMgr()).getEmpNO());
            }
        }
    }

    private void setAcctNameClueList(List<BusinessClues> businessCluesList) throws Exception {
	    if(CollectionUtils.isEmpty(businessCluesList)){
	        return;
        }
        // 查询客户名称
        Set<String> acctIdSet = new HashSet<>();
        acctIdSet.addAll(businessCluesList.stream().map(BusinessClues::getAcctId).collect(Collectors.toList()));
        acctIdSet.addAll(businessCluesList.stream().map(BusinessClues::getLastAcctId).collect(Collectors.toList()));
        if(CollectionUtils.isEmpty(acctIdSet)){
            return ;
        }
        List<Account> accountList = AccountUtil.getAccountListByids(acctIdSet);
        if(CollectionUtils.isEmpty(accountList)){
            return ;
        }
        Map<String, String> accountMap = accountList.stream().collect(Collectors.toMap(Account::getId, Account::getAccountName, (entity1, entity2) -> entity1));
        if(accountMap.isEmpty()){
            return ;
        }
        for(BusinessClues businessClues : businessCluesList){
            // 设置客户名称
            if(accountMap.containsKey(businessClues.getAcctId())){
                businessClues.setAcctName(accountMap.get(businessClues.getAcctId()));
            }
            // 设置最终客户
            if(accountMap.containsKey(businessClues.getLastAcctId())){
                businessClues.setLastAcctName(accountMap.get(businessClues.getLastAcctId()));
            }
        }
    }
}
