package com.zte.aiagent.ui.controller;

import com.zte.aiagent.app.command.CreateDocumentCommand;
import com.zte.aiagent.domain.service.BidDocumentService;
import com.zte.aiagent.domain.enums.ParseStatusEnum;
import com.zte.aiagent.infrastruction.adapter.DocCloudAdapter;
import com.zte.aiagent.ui.dto.request.BidDocumentPageQueryDTO;
import com.zte.aiagent.ui.dto.vo.BidDocumentVO;
import com.zte.aiagent.ui.dto.vo.BidParseRecordVO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import com.zte.mcrm.adapter.clouddisk.util.ServiceDataUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.common.util.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.validation.annotation.Validated;

@Api(tags = "招标文件解析相关API")
@RestController
@RequestMapping("/bid/document/parse")
@Validated
public class BidDocumentController {

    @Resource
    private BidDocumentService bidDocumentService;

    @Resource
    private DocCloudAdapter docCloudAdapter;

    @ApiOperation("上传招标文件并启动解析任务")
    @PostMapping("/upload")
    public ServiceData<Map<String, Object>> uploadBidDocument(
            @ApiParam(value = "上传文件", required = true)
            @RequestPart("file") MultipartFile file,
            @ApiParam(value = "解析模板ID", required = true)
            @RequestParam("parseTemplateId") String parseTemplateId,
            @ApiParam(value = "解析模板编码", required = true)
            @RequestParam("parseTemplateCode") String parseTemplateCode) {

        // 构建创建文档命令
        CreateDocumentCommand command = CreateDocumentCommand.builder()
                .file(file)
                .parseTemplateId(parseTemplateId)
                .parseTemplateCode(parseTemplateCode)
                .operator(CommonUtils.getEmpNo())
                .tenantId(CommonUtils.getTenantId())
                .build();

        // 执行上传文档用例
        String documentId = bidDocumentService.uploadDocument(command);

        // 构建响应结果
        Map<String, Object> result = new HashMap<>(2);
        result.put("documentId", documentId);
        result.put("parseStatus", ParseStatusEnum.PENDING.getCode());

        return ServiceDataUtil.success(result);
    }

    @ApiOperation("获取下载链接-测试用")
    @PostMapping("/getDownloadUrl")
    public ServiceData<String> getDownloadUrl(@ApiParam(value = "fileKey", required = true)
                                              @RequestParam("fileKey") String fileKey,
                                              @ApiParam(value = "fileName", required = true)
                                              @RequestParam("fileName") String fileName){
        return ServiceDataUtil.success(docCloudAdapter.getDownloadUrl(fileKey, fileName, CommonUtils.getEmpNo()));
    }

    @ApiOperation("按格式导出解析文档")
    @GetMapping("/exportParsedFile")
    public ServiceData<String> exportParsedFile(@RequestParam("documentId") String documentId,
                                                @RequestParam("format") String format) {
        String result = bidDocumentService.exportParsedFile(documentId, format);
        return ServiceDataUtil.success(result);
    }

    /**
     * 分页查询招标文件列表
     *
     * @param request 分页查询请求
     * @return 分页查询结果
     * @throws Exception 业务异常
     */
    @ApiOperation("分页查询招标文件列表")
    @PostMapping(value = "/page")
    @ResponseBody
    public ServiceData<PageRows<BidDocumentVO>> page(@RequestBody @ApiParam(value = "分页查询请求", required = true)
                                                     FormData<BidDocumentPageQueryDTO> request) throws Exception {

        return ServiceResultUtil.success(bidDocumentService.queryPageWithProgress(request));

    }

    /**
     * 查询招标文件解析项记录
     *
     * @param documentId 文档ID
     * @return 解析记录列表
     * @throws Exception 业务异常
     */
    @ApiOperation("查询招标文件解析项记录")
    @GetMapping(value = "/process/record")
    @ResponseBody
    public ServiceData<List<BidParseRecordVO>> queryParseRecords(@RequestParam("documentId") @ApiParam(value = "文档ID", required = true) String documentId) throws Exception {
        return ServiceResultUtil.success(bidDocumentService.queryParseRecords(documentId));
    }
}
