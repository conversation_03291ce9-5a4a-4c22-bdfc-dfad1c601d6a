package com.zte.mcrm.channel.model.dto;

import com.zte.mcrm.channel.constant.OpportunityNotifyMsgEnum;
import com.zte.mcrm.channel.model.entity.OpportunityMailEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 商机消息发送中间实体
 */
@Getter
@Setter
@ToString
public class OpportunityMsgNotifyDTO {
    /** 商机主键id */
    @NotBlank(message = "{optyId.null}")
    private String rowId;
    /** 模板编码 */
    @NotNull(message = "{notifyMsgEnum.null}")
    private OpportunityNotifyMsgEnum msgId;
    /** 模板类型：1：系统消息， 2：邮件 */
    @NotNull(message = "{msg.type.null}")
    private Byte type;
    /** 收件人 */
    private List<String> mailToList;

    /** 邮件抄送人列表 */
    private List<String> mailCcList;
}
