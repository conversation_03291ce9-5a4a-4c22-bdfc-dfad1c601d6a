<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.leadinfo.leadinfo.mapper.SOptyXMapper">

    <resultMap id="BaseResultMap" type="com.zte.leadinfo.leadinfo.entity.SOptyXDO">
        <id property="rowId" column="ROW_ID" jdbcType="VARCHAR"/>
        <result property="created" column="CREATED" jdbcType="TIMESTAMP"/>
        <result property="createdBy" column="CREATED_BY" jdbcType="VARCHAR"/>
        <result property="lastUpd" column="LAST_UPD" jdbcType="TIMESTAMP"/>
        <result property="lastUpdBy" column="LAST_UPD_BY" jdbcType="VARCHAR"/>
        <result property="attrib46" column="ATTRIB_46" jdbcType="VARCHAR"/>
        <result property="nationalAreaId" column="NATIONAL_AREA_ID" jdbcType="VARCHAR"/>
        <result property="nationalAreaProvinceId" column="NATIONAL_AREA_PROVINCE_ID" jdbcType="VARCHAR"/>
        <result property="nationalAreaName" column="NATIONAL_AREA_NAME" jdbcType="VARCHAR"/>
        <result property="nationalAreaCityId" column="NATIONAL_AREA_CITY_ID" jdbcType="VARCHAR"/>
        <result property="xTotalAmount" column="X_TOTAL_AMOUNT" jdbcType="DECIMAL"/>
        <result property="currencyId" column="CURRENCY_ID" jdbcType="VARCHAR"/>
        <result property="currencyCode" column="CURRENCY_CODE" jdbcType="VARCHAR"/>
        <result property="optyType" column="OPTY_TYPE" jdbcType="VARCHAR"/>
        <result property="salesType" column="SALES_TYPE" jdbcType="VARCHAR"/>
        <result property="finalUsage" column="FINAL_USAGE" jdbcType="VARCHAR"/>
        <result property="endUserType" column="end_user_type" jdbcType="VARCHAR"/>
        <result property="enduseOfEnduser" column="enduse_of_enduser" jdbcType="VARCHAR"/>
        <result property="specificCustomerDesc" column="specific_customer_desc" jdbcType="VARCHAR"/>
        <result property="specificUsageDesc" column="specific_usage_desc" jdbcType="VARCHAR"/>
        <result property="xLastAccId" column="X_LAST_ACC_ID" jdbcType="VARCHAR"/>
        <result property="xArea" column="X_AREA" jdbcType="VARCHAR"/>
        <result property="childTrade" column="CHILD_TRADE" jdbcType="VARCHAR"/>
        <result property="parentTrade" column="PARENT_TRADE" jdbcType="VARCHAR"/>
        <result property="marketType" column="MARKET_TYPE" jdbcType="VARCHAR"/>
        <result property="xProjectLabel" column="X_PROJECT_LABEL" jdbcType="VARCHAR"/>
        <result property="groupFlg" column="GROUP_FLG" jdbcType="VARCHAR"/>
        <result property="serAccept" column="SER_ACCEPT" jdbcType="VARCHAR"/>
        <result property="prodAbility" column="PROD_ABILITY" jdbcType="VARCHAR"/>
        <result property="prodAbility2" column="PROD_ABILITY_2" jdbcType="VARCHAR"/>
        <result property="prodAbility3" column="PROD_ABILITY_3" jdbcType="VARCHAR"/>
        <result property="prodAbility4" column="PROD_ABILITY_4" jdbcType="VARCHAR"/>
        <result property="accntRelation" column="ACCNT_RELATION" jdbcType="VARCHAR"/>
        <result property="accntRelation2" column="ACCNT_RELATION_2" jdbcType="VARCHAR"/>
        <result property="accntRelation3" column="ACCNT_RELATION_3" jdbcType="VARCHAR"/>
        <result property="accntRelation4" column="ACCNT_RELATION_4" jdbcType="VARCHAR"/>
        <result property="hard1" column="HARD_1" jdbcType="VARCHAR"/>
        <result property="hard2" column="HARD_2" jdbcType="VARCHAR"/>
        <result property="hard3" column="HARD_3" jdbcType="VARCHAR"/>
        <result property="hard4" column="HARD_4" jdbcType="VARCHAR"/>
        <result property="rate" column="RATE" jdbcType="DECIMAL"/>
        <result property="rate2" column="RATE_2" jdbcType="DECIMAL"/>
        <result property="rate3" column="RATE_3" jdbcType="DECIMAL"/>
        <result property="rate4" column="RATE_4" jdbcType="DECIMAL"/>
        <result property="attrib19" column="ATTRIB_19" jdbcType="DECIMAL"/>
        <result property="attrib20" column="ATTRIB_20" jdbcType="DECIMAL"/>
        <result property="attrib21" column="ATTRIB_21" jdbcType="DECIMAL"/>
        <result property="attrib22" column="ATTRIB_22" jdbcType="DECIMAL"/>
        <result property="mulDivisionFlg" column="MUL_DIVISION_FLG" jdbcType="CHAR"/>
        <result property="opptyLevelManual" column="OPPTY_LEVEL_MANUAL" jdbcType="VARCHAR"/>
        <result property="opptyLevelSystem" column="OPPTY_LEVEL_SYSTEM" jdbcType="VARCHAR"/>
        <result property="xCustInvestAmount" column="X_CUST_INVEST_AMOUNT" jdbcType="DECIMAL"/>
        <result property="date2" column="DATE_2" jdbcType="DATE"/>
        <result property="notes2" column="NOTES_2" jdbcType="VARCHAR"/>
        <result property="isFromPrm" column="IS_FROM_PRM" jdbcType="CHAR"/>
        <result property="date1" column="DATE_1" jdbcType="DATE"/>
        <result property="opptySource" column="OPPTY_SOURCE" jdbcType="VARCHAR"/>
        <result property="fundFlg" column="FUND_FLG" jdbcType="VARCHAR"/>
        <result property="netType" column="NET_TYPE" jdbcType="VARCHAR"/>
        <result property="xMtoUnion" column="X_MTO_UNION" jdbcType="VARCHAR"/>
        <result property="succProb" column="SUCC_PROB" jdbcType="DECIMAL"/>
        <result property="succProb2" column="SUCC_PROB_2" jdbcType="DECIMAL"/>
        <result property="succProb3" column="SUCC_PROB_3" jdbcType="DECIMAL"/>
        <result property="succProb4" column="SUCC_PROB_4" jdbcType="DECIMAL"/>
        <result property="xOptyPhase" column="X_OPTY_PHASE" jdbcType="VARCHAR"/>
        <result property="opptyRange" column="OPPTY_RANGE" jdbcType="DECIMAL"/>
        <result property="opptyRecom" column="OPPTY_RECOM" jdbcType="VARCHAR"/>
        <result property="leadId" column="LEAD_ID" jdbcType="VARCHAR"/>
        <result property="tendType" column="TEND_TYPE" jdbcType="VARCHAR"/>
        <result property="xOptyId" column="X_OPTY_ID" jdbcType="VARCHAR"/>
        <result property="notes4" column="NOTES_4" jdbcType="VARCHAR"/>
        <result property="dataSource" column="DATA_SOURCE" jdbcType="VARCHAR"/>
        <result property="parRowId" column="PAR_ROW_ID" jdbcType="VARCHAR"/>
        <result property="projectType" column="PROJECT_TYPE" jdbcType="VARCHAR"/>
        <result property="buId2" column="BU_ID_2" jdbcType="VARCHAR"/>
        <result property="opptyLevel" column="OPPTY_LEVEL" jdbcType="VARCHAR"/>
        <result property="amt" column="AMT" jdbcType="DECIMAL"/>
        <result property="amt2" column="AMT_2" jdbcType="DECIMAL"/>
        <result property="amt3" column="AMT_3" jdbcType="DECIMAL"/>
        <result property="amt4" column="AMT_4" jdbcType="DECIMAL"/>
        <result property="accountAttribute" column="ACCOUNT_ATTRIBUTE" jdbcType="VARCHAR"/>
        <result property="potentialModel" column="POTENTIAL_MODEL" jdbcType="VARCHAR"/>
        <result property="secondDealerId" column="SECOND_DEALER_ID" jdbcType="VARCHAR"/>
        <result property="xLastAccName" column="x_last_acc_name" jdbcType="VARCHAR"/>
        <result property="lastAccStatus" column="last_acc_status" jdbcType="TINYINT"/>
        <result property="finalCustomerAddress" column="final_customer_address" jdbcType="VARCHAR"/>
        <result property="finalCustomerParentTrade" column="final_customer_parent_trade" jdbcType="VARCHAR"/>
        <result property="finalCustomerChildTrade" column="final_customer_child_trade" jdbcType="VARCHAR"/>
        <result property="finalCustomerContactName" column="final_customer_contact_name" jdbcType="VARCHAR"/>
        <result property="finalCustomerContactPhone" column="final_customer_contact_phone" jdbcType="VARCHAR"/>
        <result property="finalCustomerContactEmail" column="final_customer_contact_email" jdbcType="VARCHAR"/>
        <result property="deptNo" column="dept_no" jdbcType="VARCHAR"/>
        <result property="projectPhasesCode" column="project_phases_code" jdbcType="VARCHAR"/>
        <result property="winRate" column="win_rate" jdbcType="VARCHAR"/>
        <result property="tenderTypeCode" column="tender_type_code" jdbcType="VARCHAR"/>
        <result property="bidProviderName" column="bid_provider_name" jdbcType="VARCHAR"/>
        <result property="biddingDeadline" column="bidding_deadline" jdbcType="TIMESTAMP"/>
        <result property="agencyName" column="agency_name" jdbcType="VARCHAR"/>
        <result property="agencyPhone" column="agency_phone" jdbcType="VARCHAR"/>
        <result property="agencyEmail" column="agency_email" jdbcType="VARCHAR"/>
        <result property="businessManagerId" column="business_manager_id" jdbcType="VARCHAR"/>
        <result property="businessManagerName" column="business_manager_name" jdbcType="VARCHAR"/>
        <result property="directorOfPsc" column="director_of_psc" jdbcType="VARCHAR"/>
        <result property="projectDesc" column="project_desc" jdbcType="VARCHAR"/>
        <result property="selfUseFlag" column="self_use_flag" jdbcType="VARCHAR"/>
        <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
        <result property="agencyLevelName" column="agency_level_name" jdbcType="VARCHAR"/>
        <result property="agencyLevelCode" column="agency_level_code" jdbcType="VARCHAR"/>
        <result property="reservedField1" column="reserved_field1" jdbcType="VARCHAR"/>
        <result property="reservedField2" column="reserved_field2" jdbcType="VARCHAR"/>
        <result property="reservedField3" column="reserved_field3" jdbcType="VARCHAR"/>
        <result property="reservedField4" column="reserved_field4" jdbcType="VARCHAR"/>
        <result property="reservedField5" column="reserved_field5" jdbcType="VARCHAR"/>
        <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
        <result property="enabledFlag" column="enabled_flag" jdbcType="CHAR"/>
        <result property="finalCustomerRestrictionFlag" column="final_customer_restriction_flag" jdbcType="VARCHAR"/>
        <result property="agencyRestrictionFlag" column="agency_restriction_flag" jdbcType="VARCHAR"/>
        <result property="tsApprovalNumber" column="ts_approval_number" jdbcType="VARCHAR"/>
        <result property="activeCount" column="active_count" jdbcType="INTEGER"/>
        <result property="fromActiveFlag" column="from_active_flag" jdbcType="VARCHAR"/>
        <result property="fromActiveOpty" column="from_active_opty" jdbcType="VARCHAR"/>
        <result property="crmCustomerCode" column="crm_customer_code" jdbcType="VARCHAR"/>
        <result property="sourceCustomerName" column="source_customer_name" jdbcType="VARCHAR"/>
        <result property="sourceCrmCustomerCode" column="source_crm_customer_code" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ROW_ID,CREATED,CREATED_BY,
        LAST_UPD,LAST_UPD_BY,ATTRIB_46,
        NATIONAL_AREA_ID,NATIONAL_AREA_PROVINCE_ID,NATIONAL_AREA_NAME,
        NATIONAL_AREA_CITY_ID,X_TOTAL_AMOUNT,CURRENCY_ID,
        CURRENCY_CODE,OPTY_TYPE,SALES_TYPE,
        FINAL_USAGE,end_user_type,enduse_of_enduser,
        specific_customer_desc,specific_usage_desc,X_LAST_ACC_ID,
        X_AREA,CHILD_TRADE,PARENT_TRADE,
        MARKET_TYPE,X_PROJECT_LABEL,GROUP_FLG,
        SER_ACCEPT,PROD_ABILITY,PROD_ABILITY_2,
        PROD_ABILITY_3,PROD_ABILITY_4,ACCNT_RELATION,
        ACCNT_RELATION_2,ACCNT_RELATION_3,ACCNT_RELATION_4,
        HARD_1,HARD_2,HARD_3,
        HARD_4,RATE,RATE_2,
        RATE_3,RATE_4,ATTRIB_19,
        ATTRIB_20,ATTRIB_21,ATTRIB_22,
        MUL_DIVISION_FLG,OPPTY_LEVEL_MANUAL,OPPTY_LEVEL_SYSTEM,
        X_CUST_INVEST_AMOUNT,DATE_2,NOTES_2,
        IS_FROM_PRM,DATE_1,OPPTY_SOURCE,
        FUND_FLG,NET_TYPE,X_MTO_UNION,
        SUCC_PROB,SUCC_PROB_2,SUCC_PROB_3,
        SUCC_PROB_4,X_OPTY_PHASE,OPPTY_RANGE,
        OPPTY_RECOM,LEAD_ID,TEND_TYPE,
        X_OPTY_ID,NOTES_4,DATA_SOURCE,
        PAR_ROW_ID,PROJECT_TYPE,BU_ID_2,
        OPPTY_LEVEL,AMT,AMT_2,
        AMT_3,AMT_4,ACCOUNT_ATTRIBUTE,
        POTENTIAL_MODEL,SECOND_DEALER_ID,x_last_acc_name,
        last_acc_status,final_customer_address,final_customer_parent_trade,
        final_customer_child_trade,final_customer_contact_name,final_customer_contact_phone,
        final_customer_contact_email,dept_no,project_phases_code,
        win_rate,tender_type_code,bid_provider_name,
        bidding_deadline,agency_name,agency_phone,
        agency_email,business_manager_id,business_manager_name,
        director_of_psc,project_desc,self_use_flag,
        customer_name,agency_level_name,agency_level_code,
        reserved_field1,reserved_field2,reserved_field3,
        reserved_field4,reserved_field5,tenant_id,
        enabled_flag,final_customer_restriction_flag,agency_restriction_flag,
        ts_approval_number,active_count,from_active_flag,
        from_active_opty,crm_customer_code,source_customer_name,source_crm_customer_code
    </sql>

</mapper>