package com.zte.mcrm.channel.service.channel;

import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.util.web.HttpHeaderUtil;
import com.zte.itp.security.AESOperator;
import com.zte.mcrm.channel.constant.*;
import com.zte.mcrm.channel.dao.OpportunityMonthReportDao;
import com.zte.mcrm.channel.model.entity.*;
import com.zte.mcrm.channel.model.mapper.OpportunityInfoVOMapper;
import com.zte.mcrm.channel.model.vo.MonthReportDetailVO;
import com.zte.mcrm.channel.model.vo.OpportunityMonthReportVO;
import com.zte.mcrm.channel.model.vo.OpportunityProductVO;
import com.zte.mcrm.channel.util.ExceptionMsgUtils;
import com.zte.mcrm.common.access.vo.ComDictionaryMaintainVO;
import com.zte.mcrm.common.business.service.IComDictionaryMaintainService;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.consts.CommonRetCode;
import com.zte.mcrm.common.framework.exception.PrmRetCode;
import com.zte.mcrm.common.upload.model.entity.ComUploadFile;
import com.zte.mcrm.common.upload.model.entity.SimpleUploadFileInfo;
import com.zte.mcrm.common.upload.model.entity.UploadFileMapper;
import com.zte.mcrm.common.upload.service.UploadFileService;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.mcrm.common.util.DictUtils;
import com.zte.mcrm.common.util.EmpInfoUtils;
import com.zte.mcrm.common.util.RowIdUtil;
import com.zte.opty.common.enums.OptyStatusEnum;
import com.zte.opty.sync.util.CommonMapUtil;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.local.LocalMessageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.ValidationException;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class IOpportunityMonthReportServiceImpl implements IOpportunityMonthReportService {

    @Autowired
    OpportunityMonthReportDao opportunityMonthReportDao;

    @Autowired
    IOpportunityService opportunityService;

    @Autowired
    IOpportunityDetailService opportunityDetailService;

    @Autowired
    IOpportunityProductService opportunityProductService;

    @Autowired
    private UploadFileService uploadFileService;

    @Autowired
    private IComDictionaryMaintainService comDictionaryMaintainService;

    @Value("${encryption.secretKeySixteen}")
    private String secretKeySixteen;

    @Value("${encryption.iv}")
    private String iv;

    /**
     * 根据 当前商机 当前归属期 查询 商机当前状态 和 原因代码
     *
     * @param currentOpportunity 当前商机
     * @param currentReportMonth 当前归属期
     * @return 实体
     * <AUTHOR>
     * @date 2021/10/20
     */
    @Override
    public OpportunityMonthReport queryOpportunityStatusAndReason(String currentOpportunity, String currentReportMonth) {
        return opportunityMonthReportDao.queryByOpportunityAndReportMonth(currentOpportunity, currentReportMonth);
    }

    /**
     * 更新 商机当前状态 和 原因代码
     *
     * @param updateBody 更新参数
     * @return 实体
     * <AUTHOR>
     * @date 2021/10/20
     */
    @Override
    public int updateOpportunityStatusAndReason(OpportunityMonthReport updateBody) throws Exception {
        int result ;
        String id = CommonUtils.getEmpNo();
        String userNameId = EmpInfoUtils.getEmpNameMap(Lists.newArrayList(id)).get(id);
        Date date = new Date();
        String currentOpportunity = updateBody.getOptyId();
        Opportunity opportunity = opportunityService.get(currentOpportunity);
        if (StringUtils.isBlank(updateBody.getOptyCurrentStatus()) || (
                OptyStatusEnum.OPTY_RENEWING.getCode().equals(opportunity.getStatusCd()) &&
                SourceOfOpportunityEnum.ZTE_SELF_BUILT.getValue().equals(opportunity.getDataSource())
                && OptyStatusEnum.TICKET_WIN.getCode().equals(updateBody.getOptyCurrentStatus()))) {
            throw new BusiException(RetCode.BUSINESSERROR_CODE, "reason.win.error");
        }
        // 更新主表的 状态
        opportunityService.updateStatus(currentOpportunity, OptyStatusEnum.getOptyStatusEnumByCode(updateBody.getOptyCurrentStatus()));
        // 插入数据
        updateBody.setId(RowIdUtil.generateRowId());
        updateBody.setCreatedBy(userNameId);
        updateBody.setCreatedDate(date);
        updateBody.setEnabledFlag("Y");
        updateBody.setBusinessType(OpportunityConstant.MONTH_REPORT_UPDATE_STATE);
        result = opportunityMonthReportDao.insert(updateBody);
        return result;
    }

    /**
     * 更新商机月报
     *
     * @param opportunityMonthReportVO 月报更新VO类
     * @return 更新月报的实体类
     * <AUTHOR>
     * @date 2022/03/01
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_UNCOMMITTED, rollbackFor = Exception.class)
    public OpportunityMonthReport updateOpportunityMonthReport(OpportunityMonthReportVO opportunityMonthReportVO) throws Exception {
        validateMonthReportInfo(opportunityMonthReportVO);

        String rowId = opportunityMonthReportVO.getOptyId();

        // 查询当前商机是否已有报备
        long count = getMonthReportCountByRowId(rowId);
        if (count == 0) {
            // 没有填写过月报则备份当前商机信息
            backUpTheOpportunityInfo(opportunityMonthReportVO);
            // 更新月报状态为”已填写“
            updateOpportunityReportStatus(rowId, ReportStatusEnum.FILLED.getValue());
        }
        // 如果附件列表为空，则取附件表内最新的附件作为本次月报的附件列表
        if (CollectionUtils.isEmpty(opportunityMonthReportVO.getFileList())){
            opportunityMonthReportVO.setFileList(getLatestSimpleFiles(rowId));
        }

        // 更新商机当前状态
        OpportunityCurrentStatus currentStatus = OpportunityCurrentStatus.fromValue(opportunityMonthReportVO.getCurrentStatus());
        opportunityService.updateCurrentStatus(rowId , currentStatus);

        // 加密敏感信息,后面保存月报信息时也要加密敏感信息，因而在这里一并做了加密
        MonthReportDetailVO monthReportDetail = opportunityMonthReportVO.getMonthReportDetail();

        // 非招标项目data1为预计发标日期，其他为预计签单日期date2，因此需要重新赋值
        boolean isTenderProject = TenderTypeEnum.TENDER_PROJECT.getValue().equals(monthReportDetail.getTenderTypeCode());
        if(BooleanUtil.and(!isTenderProject, Objects.isNull(monthReportDetail.getDate2()))){
            monthReportDetail.setDate2(monthReportDetail.getDate1());
        }

        // 更新商机信息
        updateOpportunityInfo(opportunityMonthReportVO);
        // 更新月报表
        return updateMonthReport(opportunityMonthReportVO);
    }

    private List<SimpleUploadFileInfo> getLatestSimpleFiles(String rowId){
        ComUploadFile entity = new ComUploadFile();
        entity.setBillId(rowId);
        entity.setUploadType(OpportunityConstant.UPLOAD_TYPE_NEW_OPPORTUNITIES);
        List<ComUploadFile> latestFileList = uploadFileService.getList(entity);
        List<SimpleUploadFileInfo> latestSimpleFileList = new ArrayList<>();
        for (ComUploadFile file : latestFileList) {
            latestSimpleFileList.add(UploadFileMapper.INSTANCE.uploadFileToSimpleInfo(file));
        }
        return latestSimpleFileList;
    }

    @Override
    public long getMonthReportCountByRowId(String rowId){
        Map<String, Object> map = new HashMap<>(4);
        map.put("optyId", rowId);
        map.put("businessType", OpportunityConstant.MONTH_REPORT);
        map.put("enabledFlag", CommonConst.Y);
        return opportunityMonthReportDao.getCount(map);
    }

    /**
     * 查询商机月报归属期列表
     * @param optyId 商机id
     * @return
     */
    @Override
    public List<String> getReportMonthList(String optyId){
        List<OpportunityMonthReport> opportunityMonthReportList = opportunityMonthReportDao.getMonthReportList(optyId);
        return opportunityMonthReportList.stream().map(OpportunityMonthReport::getReportMonth).collect(Collectors.toList());
    }

    @Override
    public List<OpportunityMonthReport> getMonthReportList(String rowId){
        return opportunityMonthReportDao.getMonthReportList(rowId);
    }

    private void updateOpportunityInfo(OpportunityMonthReportVO opportunityMonthReportVO){
        String rowId = opportunityMonthReportVO.getOptyId();
        OpportunityDetail opportunityDetailToUpdate = new OpportunityDetail();
        opportunityDetailToUpdate.setRowId(rowId);
        BeanUtils.copyProperties(opportunityMonthReportVO.getMonthReportDetail(), opportunityDetailToUpdate);
        List<OpportunityProductVO> opportunityProductList = opportunityMonthReportVO.getOpportunityProducts();
        List<OpportunityProduct> opportunityProducts = OpportunityInfoVOMapper
                .transOpportunityProductVOsToOpportunityProducts(opportunityProductList,
                        SourceOfOpportunityEnum.CHANNEL_FILING.getValue());
        opportunityDetailService.update(opportunityDetailToUpdate, false);
        // 根据主键查询主表获取来源字段，更新到传递参数中
        Opportunity opportunity = opportunityService.get(rowId);
        opportunityDetailToUpdate.setDataSource(opportunity.getDataSource());
        opportunityProductService.insertOrUpdateProductList(opportunityProducts, rowId, OpportunityConstant.NEW_OPPORTUNITY,
                                                            opportunityDetailToUpdate);
    }

    private OpportunityMonthReport updateMonthReport(OpportunityMonthReportVO opportunityMonthReportVO) throws BusiException {
        String empNo = HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
        OpportunityMonthReport opportunityMonthReport = new OpportunityMonthReport();
        BeanUtils.copyProperties(opportunityMonthReportVO, opportunityMonthReport);
        // 设置当月月报状态为“已提交”，这个字段是预留的，目前全部都是“已提交”，方便以后扩展“草稿“状态，。
        opportunityMonthReport.setReportStatus(OpportunityConstant.MONTH_REPORT_STATUS_SUBMITTED);
        opportunityMonthReport.setBusinessType(OpportunityConstant.MONTH_REPORT);
        opportunityMonthReport.setOptyCurrentStatus(opportunityMonthReportVO.getCurrentStatus());
        opportunityMonthReport.setMonthReportDetailJson(JSON.toJSONString(opportunityMonthReportVO, SerializerFeature.WriteDateUseDateFormat));
        Map<String, Object> paramMap = new HashMap<>(2);
        paramMap.put("opportunityMonthReport", opportunityMonthReport);
        paramMap.put("empNo", empNo);
        opportunityMonthReportDao.disAbleCurrentMonthReport(paramMap);
        insertMonthReport(opportunityMonthReport);
        return opportunityMonthReport;
    }

    private void updateOpportunityReportStatus(String rowId,String reportStatus){
        Opportunity opportunity = new Opportunity();
        opportunity.setRowId(rowId);
        opportunity.setReportStatus(reportStatus);
        opportunityService.update(opportunity);
    }

    /**
     * 校验商机月报信息
     */
    private void validateMonthReportInfo(OpportunityMonthReportVO opportunityMonthReportVO){
        OpportunityMailEntity opportunityMailEntity = opportunityDetailService.getOpportunityMailEntityByRowId(opportunityMonthReportVO.getOptyId());
        List<OpportunityProductVO> monthReportProducts = opportunityMonthReportVO.getOpportunityProducts();
        MonthReportDetailVO monthReportDetail = opportunityMonthReportVO.getMonthReportDetail();
        // 判断填写月报的商机是否存在
        if (null == opportunityMailEntity){
            throw new com.zte.itp.msa.core.exception.BusiException(RetCode.BUSINESSERROR_CODE, "OpportunityNotFound");
        }
        // 校验状态是否为“报备成功”
        if (!OptyStatusEnum.OPTY_RENEWING.getCode().equals(opportunityMailEntity.getStatusCd())){
            throw new com.zte.itp.msa.core.exception.BusiException(RetCode.BUSINESSERROR_CODE, "OpportunityStatusError");
        }
        // 商机名称不可修改
        if (!opportunityMailEntity.getOptyName().equals(monthReportDetail.getAttrib46())){
            throw new com.zte.itp.msa.core.exception.BusiException(RetCode.BUSINESSERROR_CODE, "attrib46.cannotModify");
        }
        // 校验产品信息
        if (CollectionUtils.isNotEmpty(monthReportProducts)) {
            List<String> productNames = new ArrayList<>();
            monthReportProducts.forEach(opportunityProduct -> {
                if (StringUtils.isNotEmpty(opportunityProduct.getProdLv2Name()) && productNames.contains(opportunityProduct.getProdLv2Name())) {
                    throw new ValidationException("opportunityProduct.repeated");
                }
                productNames.add(opportunityProduct.getProdLv2Name());
            });
        }else{
            throw new ValidationException("opportunityProduct.atLeastOne");
        }
        // 招标项目时校验竞标截止日期和招标方全称必填
        if (TenderTypeEnum.TENDER_PROJECT.getValue().equals(monthReportDetail.getTenderTypeCode())) {
            if (null == monthReportDetail.getBiddingDeadline()) {
                throw new ValidationException(LocalMessageUtils.getMessage("biddingDeadline.null"));
            }
            if (StringUtils.isEmpty(monthReportDetail.getBidProviderName())) {
                throw new ValidationException(LocalMessageUtils.getMessage("bidProviderName.null"));
            }
        }
    }

    /**
     * 备份第一次提交月报前的商机内容
     * @param opportunityMonthReportVO
     * @throws BusiException
     */
    private void backUpTheOpportunityInfo(OpportunityMonthReportVO opportunityMonthReportVO) throws BusiException {
        String rowId = opportunityMonthReportVO.getOptyId();
        // 查询商机信息
        OpportunityInfoEntity opportunityInfoToBak = getOpportunityInfo(rowId);
        OpportunityMonthReport opportunityMonthReportBak = new OpportunityMonthReport();
        BeanUtils.copyProperties(opportunityMonthReportVO, opportunityMonthReportBak);
        opportunityMonthReportBak.setMonthReportDetailJson(JSON.toJSONString(opportunityInfoToBak,SerializerFeature.WriteDateUseDateFormat));
        opportunityMonthReportBak.setBusinessType(OpportunityConstant.MONTH_REPORT_BAK);
        insertMonthReport(opportunityMonthReportBak);
    }

    /**
     * 根据商机id查询商机信息（商机主表、商机扩展表、产品信息）
     * @param rowId
     * @return
     */
    private OpportunityInfoEntity getOpportunityInfo(String rowId){
        Opportunity opportunity = opportunityService.get(rowId);
        OpportunityDetail opportunityDetail = opportunityDetailService.get(rowId);
        Map<String,Object> params = Maps.newHashMap();
        params.put("opptyId", rowId);
        params.put("businessType", "newOpportunity");
        List<OpportunityProduct> products = opportunityProductService.getList(params);
        ComUploadFile entity = new ComUploadFile();
        entity.setBillId(rowId);
        List<ComUploadFile> fileInfo = uploadFileService.getList(entity);
        OpportunityInfoEntity opportunityInfo = new OpportunityInfoEntity();
        opportunityInfo.setOpportunity(opportunity);
        opportunityInfo.setOpportunityDetail(opportunityDetail);
        opportunityInfo.setOpportunityProducts(products);
        opportunityInfo.setOpportunityFileList(fileInfo);
        return opportunityInfo;
    }

    @Override
    public void insertMonthReport(OpportunityMonthReport opportunityMonthReport) throws BusiException {
        String empNo = HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
        opportunityMonthReport.setCreatedBy(empNo);
        opportunityMonthReport.setLastUpdatedBy(empNo);
        opportunityMonthReport.setId(RowIdUtil.generateRowId());
        opportunityMonthReportDao.insert(opportunityMonthReport);
    }

    /**
     * 查询商机月报详情
     * @param optyId 商机id
     * @param reportMonth 商机月报归属期，如202202
     * @return
     */
    @Override
    public OpportunityMonthReportVO getMonthReportDetail(String optyId, String reportMonth){
        Map<String, Object> map = new HashMap<>(4);
        map.put("optyId", optyId);
        map.put("businessType", OpportunityConstant.MONTH_REPORT);
        map.put("reportMonth", reportMonth);
        OpportunityMonthReport opportunityMonthReport = opportunityMonthReportDao.getMonthReport(map);
        if (null == opportunityMonthReport){
            return null;
        }
        OpportunityMonthReportVO opportunityMonthReportVO = (OpportunityMonthReportVO) JacksonJsonConverUtil.jsonToBean(opportunityMonthReport.getMonthReportDetailJson(), OpportunityMonthReportVO.class);
        if (null != opportunityMonthReportVO){
            setDictValueAndDecrypt(opportunityMonthReportVO);
        }
        return opportunityMonthReportVO;
    }

    /**
     * 设置字典值并解密敏感字段
     * @param opportunityMonthReportVO
     */
    private void setDictValueAndDecrypt(OpportunityMonthReportVO opportunityMonthReportVO){
        if (null == opportunityMonthReportVO.getMonthReportDetail()){
            return;
        }
        MonthReportDetailVO monthReportDetail = opportunityMonthReportVO.getMonthReportDetail();
        if (StringUtils.isBlank(opportunityMonthReportVO.getCurrentStatus())){
            opportunityMonthReportVO.setCurrentStatus(OpportunityCurrentStatus.UNDERWAY.getValue());
        }
        // 非招标项目data1为预计发标日期，其他为预计签单日期date2，因此需要重新赋值
        boolean isTenderProject = TenderTypeEnum.TENDER_PROJECT.getValue().equals(monthReportDetail.getTenderTypeCode());
        if(BooleanUtil.and(!isTenderProject, Objects.isNull(monthReportDetail.getDate1()))){
            monthReportDetail.setDate1(monthReportDetail.getDate2());
        }

        List<String> types = Lists.newArrayList(OpportunityConstant.CURRENT_PHASES_TYPE,
                OpportunityConstant.WIN_RATE_TYPE,
                OpportunityConstant.TYPE_FOR_TENDER_TYPE,
                OpportunityConstant.OPPORTUNITY_CURRENT_STATUS
        );
        Map<String, List<ComDictionaryMaintainVO>> dictMap = comDictionaryMaintainService.queryByTypeList(types);

        String projectPhasesName = DictUtils.getName(monthReportDetail.getProjectPhasesCode(), dictMap.get(OpportunityConstant.CURRENT_PHASES_TYPE));
        String tenderTypeName = DictUtils.getName(monthReportDetail.getTenderTypeCode(), dictMap.get(OpportunityConstant.TYPE_FOR_TENDER_TYPE));
        String currentStatus = DictUtils.getName(opportunityMonthReportVO.getCurrentStatus(), dictMap.get(OpportunityConstant.OPPORTUNITY_CURRENT_STATUS));

        opportunityMonthReportVO.setCurrentStatusName(currentStatus);
        monthReportDetail.setProjectPhasesName(projectPhasesName);
        monthReportDetail.setWinRateName(CommonMapUtil.SUCCESS_TATE_NAME_MAP.getOrDefault(monthReportDetail.getWinRate(), monthReportDetail.getWinRate()));
        monthReportDetail.setTenderTypeName(tenderTypeName);
    }

    /**
     * 获取需要发送提醒邮件的商机列表
     *
     * @return
     */
    @Override
    public List<OpportunityKeyInfoEntity> getMonthReportReminders(String reportMonth) {
        return opportunityMonthReportDao.getMonthReportReminders(reportMonth);
    }

    /**
     * 获取由于未及时更新月报需要失效的商机列表
     * @return
     */
    @Override
    public List<OpportunityKeyInfoEntity> getMonthlyInvalidOptys(String reportMonth) {
        return opportunityMonthReportDao.getMonthlyInvalidOptys(reportMonth);
    }
}
