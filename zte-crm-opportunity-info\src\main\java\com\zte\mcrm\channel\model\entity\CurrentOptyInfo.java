package com.zte.mcrm.channel.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 当前商机的信息
 * <AUTHOR>
 * @date 2021/12/07
 */
@Getter
@Setter
@ToString
public class CurrentOptyInfo {

    @ApiModelProperty(value = "当前商机主键id")
    private String currentOptyRowId;

    @ApiModelProperty(value = "当前商机招标类型")
    private String currentOptyTenderType;

    @ApiModelProperty(value = "当前商机最终用户名称")
    private String currentOptyLastName;

    @ApiModelProperty(value = "当前商机客户编码")
    private String currentCrmCode;

    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "当前商机预计发标日期")
    private Date currentOptyTime;

    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "当前商机竞标截止日期")
    private Date currentDeadline;

}
