package com.zte.mcrm.adapter.clouddisk.service;


import com.alibaba.fastjson.JSON;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.km.udm.common.util.EncryptHelper;
import com.zte.mcrm.adapter.clouddisk.config.CloudDiskConfig;
import com.zte.mcrm.common.consts.CommonConstant;
import com.zte.mcrm.common.consts.CommonRetCode;
import com.zte.mcrm.common.exception.BusinessRuntimeException;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.mcrm.common.util.HmacSha256Util;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.http.*;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.ByteArrayBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 文档云抽象类
 * <AUTHOR>
 * @date 2019年7月17日
 */
@Service
@Primary
@EnableConfigurationProperties
@Getter
@Setter
public class CloudDiskBaseService {

	private static final Logger LOGGER = LoggerFactory.getLogger(CloudDiskBaseService.class);
	public static final String URL_SPER = "/";
	public static final String OBJECTS_DOWNLOAD_BY_TOKEN = "/objects/downloadByToken/";

	@Autowired
	protected CloudDiskConfig cloudDiskConfig;

	/**
	 * url连接超时,单位ms
	 */
	private static final int CONNECT_TIME_OUT = 30000;
	/**
	 * 从连接池获取Connection 超时时间单位ms
	 */
	private static final int CONNECT_REQ_TIME_OUT = 10000;

	protected static final String CLOUD_DISK_CALL_RESULT_PRE = "###cloudDisk call result:";
	
	protected static final String SERVICE_GET_ERR = "SERVICE_GET_ERR";
	protected static final String FILE_MD5 = "fileMd5";
	protected static final String URL_PRE = "url:";
	/**
	 * User-Agent
	 */
	protected static final String USER_AGENT_NAME = "User-Agent";
	/**
	 * User-Agent 默认值
	 */
	protected static final String USER_AGENT_VALUE = "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/63.0.3239.132 Safari/537.36";

	public static final int RES_STATUS_CODE_SUCESS=200;

	public static String STANDARD_TIME_FORMATE = "yyyyMMddHHmmss";
	public static String EPMS_VALID_ENCRYPTION = "file/drm/check";

	public CloseableHttpClient applyHttpClient() {
		// 可优化使用连接池方式获取httpClient
		return HttpClientBuilder.create().build();
	}

	public void setCommonHeader(HttpRequestBase method, String userId) throws NoSuchAlgorithmException, InvalidKeyException {
		RequestConfig config = RequestConfig.custom().setConnectTimeout(CONNECT_TIME_OUT)
				.setConnectionRequestTimeout(CONNECT_REQ_TIME_OUT).setSocketTimeout(cloudDiskConfig.getSocketTimeOut())
				.build();
		method.setConfig(config);
		method.setHeader("X-Emp-No", userId);
		method.setHeader("X-Origin-ServiceName", cloudDiskConfig.getxOriginServiceName());
		method.setHeader("X-Org-Id", cloudDiskConfig.getxAccessKey());
		String strTimeStamps = String.valueOf(System.currentTimeMillis()).substring(0,10);
		method.setHeader("X-Timestamp", strTimeStamps);
		method.setHeader("X-Auth-Value", EncryptHelper.getHmacSHA(cloudDiskConfig.getxOriginServiceName() + userId
				+ cloudDiskConfig.getxSecKey() + cloudDiskConfig.getxAccessKey() +strTimeStamps, cloudDiskConfig.getxSecKey()));
	}

	/**
	 * 获取文档云下载链接
	 * @param token
	 * @return
	 */
	public String getDownloadLink(String token, boolean httpsFlag) {
		StringBuilder url = new StringBuilder();
		if(httpsFlag) {
			url.append(cloudDiskConfig.getHttpsUrl()).append(OBJECTS_DOWNLOAD_BY_TOKEN)
					.append(cloudDiskConfig.getxOriginServiceName()).append(URL_SPER).append(token);
		}else {
			url.append(cloudDiskConfig.getUrl()).append(OBJECTS_DOWNLOAD_BY_TOKEN)
					.append(cloudDiskConfig.getxOriginServiceName()).append(URL_SPER).append(token);
		}
		return url.toString();
	}
	/**
	 * 	发送请求
	 */
	public String send(String requestUrl, HttpEntity httpEntity, String userId) throws Exception {
		CloseableHttpClient httpClient = applyHttpClient();
		String str = "";
		try {
			HttpPost post = new HttpPost(requestUrl);
		    post.setEntity(httpEntity);
		    setCommonHeader(post, userId);
			HttpResponse res = httpClient.execute(post);
		    if (res.getStatusLine().getStatusCode() != RES_STATUS_CODE_SUCESS) {
			    throw new BusiException(URL_PRE + requestUrl, SERVICE_GET_ERR);
		    }
			str = EntityUtils.toString(res.getEntity());
			LOGGER.info(requestUrl + CLOUD_DISK_CALL_RESULT_PRE + str);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			httpClient.close();
		}
		return str;
	}

	public ByteArrayBody getFile(String currUrl, String userId, boolean isMultiple) throws Exception {
		CloseableHttpClient httpClient = applyHttpClient();
		HttpGet get = new HttpGet(currUrl);
		setCommonHeader(get, userId);
		get.addHeader(USER_AGENT_NAME, USER_AGENT_VALUE);
		HttpResponse res = httpClient.execute(get);
		if (res.getStatusLine().getStatusCode() != RES_STATUS_CODE_SUCESS) {
			throw new BusiException(URL_PRE + currUrl, SERVICE_GET_ERR);
		}
		String downloadFileName = "";
		try {
			if (!isMultiple){
				downloadFileName = getDownloadFileName(res);
			}else {
				SimpleDateFormat format = new SimpleDateFormat(STANDARD_TIME_FORMATE);
				String currentTimeStr = format.format(new Date());
				downloadFileName = currentTimeStr.concat(CommonConstant.FILE_SUFFIX_ZIP);
			}
			return new ByteArrayBody(IOUtils.toByteArray(res.getEntity().getContent()), downloadFileName);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			httpClient.close();
		}
		return null;
	}

	/**
	 * 获取文件名
	 * @param res
	 * @return
	 */
	public static String getDownloadFileName(HttpResponse res) {
		Header contentHead = res.getFirstHeader("Content-Disposition");
		HeaderElement[] elements = contentHead.getElements();
		String filename = null;
		for (HeaderElement el : elements) {
			// 遍历，获取filename
			NameValuePair pair = el.getParameterByName("filename");
			filename = null == pair ? null : pair.getValue();
			if (null != filename) {
				break;
			}
		}
		return filename;
	}

	/**
	 * 校验文件是否加密
	 * @param file
	 * @param fileMd5
	 */
	public boolean validFileEncryption(File file, String fileMd5) throws IOException, InvalidKeyException, NoSuchAlgorithmException {
		MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create()
				.setCharset(Charset.defaultCharset()).setLaxMode();
		multipartEntityBuilder.addBinaryBody("file", file);
		multipartEntityBuilder.addTextBody("FILE_MD5", fileMd5);
		HttpEntity httpEntity = multipartEntityBuilder.build();

		String requestUrl = cloudDiskConfig.getUrl() + EPMS_VALID_ENCRYPTION;
		CloseableHttpResponse response = null;
		CloseableHttpClient httpClient = HttpClientBuilder.create().build();
		HttpPost post = new HttpPost(requestUrl);
		post.setEntity(httpEntity);
		setCommonHeader(post, CommonUtils.getEmpNo());
		try {
			response = httpClient.execute(post);
			if (response.getStatusLine().getStatusCode() != RES_STATUS_CODE_SUCESS) {
				// 清除
				FileUtils.deleteQuietly(file);
				LOGGER.info("validFileEncryption,url:{},返回code:{},reasonPhrase:{}", requestUrl,
						response.getStatusLine().getStatusCode(), response.getStatusLine().getReasonPhrase());
				throw new BusinessRuntimeException(RetCode.BUSINESSERROR_CODE, CommonRetCode.DOC_CLOUD_SERVICE_ERROR);
			}
			ServiceData<Boolean> serviceData = JSON.parseObject(EntityUtils.toString(response.getEntity()),
					new com.alibaba.fastjson.TypeReference<ServiceData<Boolean>>() {
					});
			return serviceData.getBo();
		}finally {
			httpClient.close();
			if (null != response) {
				try {
					response.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

}
