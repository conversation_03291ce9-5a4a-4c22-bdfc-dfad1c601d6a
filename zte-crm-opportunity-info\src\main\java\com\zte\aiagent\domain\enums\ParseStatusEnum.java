package com.zte.aiagent.domain.enums;

import lombok.Getter;

/**
 * 解析状态枚举
 * 作为值对象使用，封装解析状态的业务逻辑
 *
 * 枚举可以作为值对象使用，因为它们：
 * 1. 不可变（immutable）
 * 2. 基于值相等（value equality）
 * 3. 可以包含业务行为方法
 * 4. 表达业务概念
 */
@Getter
public enum ParseStatusEnum {

    /** 待解析 */
    PENDING("PENDING", "待解析"),

    /** 解析中 */
    PARSING("PARSING", "解析中"),

    /** 解析成功 */
    SUCCESS("SUCCESS", "解析成功"),

    /** 解析失败 */
    FAILED("FAILED", "解析失败");

    /**
     * -- GETTER --
     *  获取状态编码（用于数据库存储）
     */
    private final String code;
    /**
     * -- GETTER --
     *  获取状态描述
     */
    private final String description;

    ParseStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 从字符串代码创建枚举
     *
     * @param code 状态代码
     * @return ParseStatus枚举
     */
    public static ParseStatusEnum fromCode(String code) {
        if (code == null) {
            return PENDING;
        }

        for (ParseStatusEnum status : values()) {
            if (status.code.equalsIgnoreCase(code)) {
                return status;
            }
        }

        return PENDING; // 默认状态
    }

    /**
     * 是否可以开始解析
     * 业务规则：只有待解析和失败状态可以开始解析
     */
    public boolean canStartParse() {
        return this == PENDING || this == FAILED;
    }

    /**
     * 是否正在解析
     */
    public boolean isParsing() {
        return this == PARSING;
    }

    /**
     * 是否解析成功
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }

    /**
     * 是否解析失败
     */
    public boolean isFailed() {
        return this == FAILED;
    }

    /**
     * 是否为最终状态（成功或失败）
     */
    public boolean isFinalState() {
        return this == SUCCESS || this == FAILED;
    }

    /**
     * 获取下一个可能的状态集合
     */
    public ParseStatusEnum[] getNextPossibleStates() {
        switch (this) {
            case PENDING:
                return new ParseStatusEnum[]{PARSING};
            case PARSING:
                return new ParseStatusEnum[]{SUCCESS, FAILED};
            case FAILED:
                return new ParseStatusEnum[]{PARSING}; // 可以重试
            case SUCCESS:
                return new ParseStatusEnum[]{}; // 成功状态不能转换
            default:
                return new ParseStatusEnum[]{};
        }
    }

    /**
     * 检查状态转换是否合法
     */
    public boolean canTransitionTo(ParseStatusEnum targetStatus) {
        ParseStatusEnum[] possibleStates = getNextPossibleStates();
        for (ParseStatusEnum state : possibleStates) {
            if (state == targetStatus) {
                return true;
            }
        }
        return false;
    }
}
