package com.zte.mcrm.adapter.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.adapter.constant.HeaderNameConst;
import com.zte.mcrm.adapter.model.dto.CrmProjectInfo;
import com.zte.mcrm.adapter.service.ICrmProjectInfoService;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.mcrm.common.util.RequestMessage;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import com.zte.springbootframe.common.model.HttpResultData;
import com.zte.springbootframe.common.serviceregister.MicroServiceWithConfigUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class CrmProjectInfoServiceImpl implements ICrmProjectInfoService {
    private final static Logger logger = LoggerFactory.getLogger(CrmProjectInfoServiceImpl.class);

    @Value("${project.page.noauth}")
    private String  projectPageNoauth;

    @Override
    public List<CrmProjectInfo> queryProjectCode (String optyNum) throws Exception {


            Map<String ,Object> map = Maps.newHashMap();
            map.put("currentPage", 1);
            map.put("optyNums", optyNum);
            map.put("pageSize", 10);
            HttpResultData httpResult = MicroServiceWithConfigUtil.invokeServiceWithGetMethod("8", projectPageNoauth, map, getHeaderMap());

            if( httpResult.getBo()!=null) {
                String rtnStr = JacksonJsonConverUtil.beanToJson(httpResult.getBo());
                return JacksonJsonConverUtil.jsonToListBeanOther(rtnStr, new TypeReference<List<CrmProjectInfo>>() {
                });
            }
        return null;
    }

    private Map<String, String> getHeaderMap() {
        Map<String, String> headerParamsMap = new HashMap<>(8);
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO, CommonUtils.getEmpNo());
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE, RequestMessage.getToken());
        return headerParamsMap;
    }
}
