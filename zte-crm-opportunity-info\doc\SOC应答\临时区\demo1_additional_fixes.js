// ===== 补充修复：完善弹窗功能实现 =====

// 任务管理弹窗
function showTaskManageDialog() {
  const content = `
    <div style="width:600px;">
      <div style="display:flex;gap:20px;">
        <div style="flex:1;">
          <h4>待办任务</h4>
          <div id="todoListDialog" style="max-height:300px;overflow:auto;border:1px solid #e8eaec;border-radius:4px;padding:12px;">
            <div style="color:#999;text-align:center;">暂无待办任务</div>
          </div>
        </div>
        <div style="flex:1;">
          <h4>历史归档</h4>
          <div id="historyListDialog" style="max-height:300px;overflow:auto;border:1px solid #e8eaec;border-radius:4px;padding:12px;">
            <div style="color:#999;text-align:center;">暂无历史归档</div>
          </div>
        </div>
      </div>
    </div>
  `;

  const footer = `
    <button class="soc-btn soc-btn-primary" onclick="addNewTask()">新建任务</button>
    <button class="soc-btn" onclick="this.closest('.modal-overlay').remove()">关闭</button>
  `;

  createModal('任务管理', content, footer);
}

// 导出设置弹窗
function showExportSettingDialog() {
  const content = `
    <div style="width:500px;">
      <div style="margin-bottom:20px;">
        <h4>导出字段设置</h4>
        <div style="border:1px solid #e8eaec;border-radius:4px;padding:12px;">
          <label style="display:block;margin:4px 0;"><input type="checkbox" checked /> 编号</label>
          <label style="display:block;margin:4px 0;"><input type="checkbox" checked /> 条目描述</label>
          <label style="display:block;margin:4px 0;"><input type="checkbox" checked /> 产品</label>
          <label style="display:block;margin:4px 0;"><input type="checkbox" checked /> 应答方式</label>
          <label style="display:block;margin:4px 0;"><input type="checkbox" checked /> 应答</label>
          <label style="display:block;margin:4px 0;"><input type="checkbox" checked /> 应答说明</label>
          <label style="display:block;margin:4px 0;"><input type="checkbox" checked /> 索引</label>
          <label style="display:block;margin:4px 0;"><input type="checkbox" /> 备注</label>
          <label style="display:block;margin:4px 0;"><input type="checkbox" /> 补充信息</label>
          <label style="display:block;margin:4px 0;"><input type="checkbox" /> 来源</label>
        </div>
      </div>
      
      <div style="margin-bottom:20px;">
        <h4>筛选条件</h4>
        <div style="border:1px solid #e8eaec;border-radius:4px;padding:12px;">
          <div style="margin-bottom:8px;">
            <label>产品筛选：</label>
            <select multiple style="width:100%;height:80px;">
              <option value="5GC" selected>5GC</option>
              <option value="VoLTE" selected>VoLTE</option>
              <option value="IMS" selected>IMS</option>
              <option value="核心网">核心网</option>
              <option value="接入网">接入网</option>
            </select>
          </div>
          <div>
            <label>应答状态：</label>
            <select multiple style="width:100%;height:80px;">
              <option value="FC" selected>FC - 完全满足</option>
              <option value="PC" selected>PC - 部分满足</option>
              <option value="NC" selected>NC - 不满足</option>
              <option value="N/A" selected>N/A - 不适用</option>
              <option value="">未应答</option>
            </select>
          </div>
        </div>
      </div>
      
      <div>
        <h4>导出模板</h4>
        <div style="display:flex;gap:8px;">
          <select style="flex:1;">
            <option>默认模板</option>
            <option>简化模板</option>
            <option>详细模板</option>
          </select>
          <button class="soc-btn">保存为模板</button>
        </div>
      </div>
    </div>
  `;

  const footer = `
    <button class="soc-btn soc-btn-primary" onclick="saveExportSetting()">保存设置</button>
    <button class="soc-btn" onclick="this.closest('.modal-overlay').remove()">取消</button>
  `;

  createModal('导出设置', content, footer);
}

// 参考文档弹窗（补充完整实现）
function showReferenceDocDialog() {
  const content = `
    <div style="width:700px;">
      <div style="margin-bottom:20px;">
        <div style="font-weight:500;margin-bottom:12px;color:#333;">📁 文档上传</div>
        <div style="display:grid;grid-template-columns:1fr 1fr;gap:12px;">
          <div class="material-upload-area" onclick="document.getElementById('projectDocInput').click()">
            <div style="font-size:24px;margin-bottom:8px;">📄</div>
            <div style="font-weight:500;margin-bottom:4px;">项目文档</div>
            <div style="font-size:12px;color:#666;">招标文件、技术协议等</div>
            <input type="file" id="projectDocInput" accept=".pdf,.doc,.docx,.xlsx,.xls" style="display:none;" onchange="handleDocUpload(this, 'project')" />
          </div>
          <div class="material-upload-area" onclick="document.getElementById('socDocInput').click()">
            <div style="font-size:24px;margin-bottom:8px;">📋</div>
            <div style="font-weight:500;margin-bottom:4px;">历史SOC文档</div>
            <div style="font-size:12px;color:#666;">历史应答文档、经验总结</div>
            <input type="file" id="socDocInput" accept=".pdf,.doc,.docx,.xlsx,.xls" style="display:none;" onchange="handleDocUpload(this, 'soc')" />
          </div>
        </div>
      </div>
      
      <div style="margin-bottom:20px;">
        <div style="font-weight:500;margin-bottom:12px;color:#333;">📚 文档选择</div>
        <div style="display:grid;grid-template-columns:1fr 1fr;gap:12px;">
          <div class="material-select-area">
            <div style="font-weight:500;margin-bottom:8px;">📖 文档库</div>
            <div style="font-size:12px;color:#666;margin-bottom:12px;">从Studio知识库选择参考文档</div>
            <button class="soc-btn" style="width:100%;" onclick="selectFromLibrary()">选择参考文档</button>
          </div>
          <div class="material-select-area">
            <div style="font-weight:500;margin-bottom:8px;">🕓 历史文档</div>
            <div style="font-size:12px;color:#666;margin-bottom:12px;">复用历史上传的文档</div>
            <button class="soc-btn" style="width:100%;" onclick="selectFromHistory()">选择历史文档</button>
          </div>
        </div>
      </div>
      
      <div style="margin-bottom:20px;">
        <div style="font-weight:500;margin-bottom:12px;color:#333;display:flex;align-items:center;justify-content:space-between;">
          <span>📎 已选文档</span>
          <span id="docCount" style="font-size:12px;color:#666;">0个文档</span>
        </div>
        <div id="selectedDocs" style="min-height:60px;border:1px solid #e8e8e8;border-radius:6px;padding:12px;background:#fafafa;">
          <span style="color:#aaa;font-size:13px;">暂无已选文档</span>
        </div>
      </div>
      
      <div style="display:flex;gap:12px;justify-content:space-between;align-items:center;">
        <div style="display:flex;gap:8px;">
          <button class="soc-btn soc-btn-warning" onclick="validateDocs()">✓ 校验准备项</button>
          <button class="soc-btn" onclick="saveAsTemplate()">💾 保存为模板</button>
        </div>
        <div>
          <span id="docStatus" class="material-status incomplete">准备状态：未完成</span>
        </div>
      </div>
    </div>
  `;

  const footer = `
    <button class="soc-btn soc-btn-primary" onclick="confirmReferenceDoc()">确定</button>
    <button class="soc-btn" onclick="this.closest('.modal-overlay').remove()">关闭</button>
  `;

  createModal('参考文档', content, footer);
  
  // 更新已选文档显示
  updateSelectedDocs();
}

// 优先级配置弹窗
function showPriorityConfigDialog() {
  const content = `
    <div style="width:600px;">
      <div style="margin-bottom:20px;">
        <h4>条目优先级设置</h4>
        <p style="color:#666;font-size:13px;">拖拽调整优先级顺序，数字越小优先级越高</p>
      </div>
      
      <div id="priorityList" style="border:1px solid #e8eaec;border-radius:6px;padding:12px;max-height:400px;overflow:auto;">
        ${generatePriorityList()}
      </div>
      
      <div style="margin-top:20px;display:flex;gap:12px;">
        <button class="soc-btn" onclick="addPriorityRule()">+ 添加规则</button>
        <button class="soc-btn" onclick="resetPriority()">重置默认</button>
      </div>
    </div>
  `;

  const footer = `
    <button class="soc-btn soc-btn-primary" onclick="savePriorityConfig()">保存配置</button>
    <button class="soc-btn" onclick="this.closest('.modal-overlay').remove()">取消</button>
  `;

  createModal('优先级配置', content, footer);
  
  // 初始化拖拽功能
  initPriorityDrag();
}

// 历史记录弹窗
function showHistoryDialog() {
  const content = `
    <div style="width:800px;max-height:600px;overflow:auto;">
      <div style="margin-bottom:20px;">
        <div style="display:flex;gap:12px;align-items:center;">
          <input type="date" id="historyStartDate" style="padding:6px 12px;border:1px solid #d9d9d9;border-radius:4px;" />
          <span>至</span>
          <input type="date" id="historyEndDate" style="padding:6px 12px;border:1px solid #d9d9d9;border-radius:4px;" />
          <select id="historyType" style="padding:6px 12px;border:1px solid #d9d9d9;border-radius:4px;">
            <option value="">全部操作</option>
            <option value="add">新增</option>
            <option value="edit">编辑</option>
            <option value="delete">删除</option>
            <option value="import">导入</option>
            <option value="export">导出</option>
          </select>
          <button class="soc-btn soc-btn-primary" onclick="searchHistory()">查询</button>
        </div>
      </div>
      
      <table style="width:100%;border-collapse:collapse;">
        <thead>
          <tr style="background:#fafbfc;">
            <th style="padding:12px;text-align:left;border-bottom:1px solid #e8eaec;">时间</th>
            <th style="padding:12px;text-align:left;border-bottom:1px solid #e8eaec;">操作类型</th>
            <th style="padding:12px;text-align:left;border-bottom:1px solid #e8eaec;">描述</th>
            <th style="padding:12px;text-align:left;border-bottom:1px solid #e8eaec;">操作人</th>
            <th style="padding:12px;text-align:left;border-bottom:1px solid #e8eaec;">操作</th>
          </tr>
        </thead>
        <tbody id="historyTableBody">
          ${generateHistoryRows()}
        </tbody>
      </table>
    </div>
  `;

  createModal('历史记录', content);
}

// 生成优先级列表
function generatePriorityList() {
  const rules = [
    { id: 1, name: '安全相关条目', keyword: '安全,加密,认证', priority: 1 },
    { id: 2, name: '性能相关条目', keyword: '性能,并发,吞吐量', priority: 2 },
    { id: 3, name: '功能相关条目', keyword: '功能,特性,支持', priority: 3 },
    { id: 4, name: '接口相关条目', keyword: '接口,API,协议', priority: 4 },
    { id: 5, name: '运维相关条目', keyword: '运维,监控,告警', priority: 5 }
  ];
  
  return rules.map(rule => `
    <div class="priority-item" draggable="true" data-id="${rule.id}" style="padding:12px;background:#f5f7fa;margin-bottom:8px;border-radius:4px;cursor:move;display:flex;align-items:center;gap:12px;">
      <span style="width:30px;height:30px;background:#1765d5;color:#fff;border-radius:50%;display:flex;align-items:center;justify-content:center;font-weight:bold;">${rule.priority}</span>
      <div style="flex:1;">
        <div style="font-weight:500;">${rule.name}</div>
        <div style="font-size:12px;color:#666;">关键词：${rule.keyword}</div>
      </div>
      <button class="soc-btn" style="padding:4px 12px;height:28px;" onclick="editPriorityRule(${rule.id})">编辑</button>
      <button class="soc-btn" style="padding:4px 12px;height:28px;color:#ff4d4f;" onclick="deletePriorityRule(${rule.id})">删除</button>
    </div>
  `).join('');
}

// 生成历史记录行
function generateHistoryRows() {
  const history = [
    { time: '2024-01-26 14:30:25', type: '导入', desc: '导入Excel文件：招标需求.xlsx（125条）', user: '管理员' },
    { time: '2024-01-26 13:45:10', type: '编辑', desc: '修改条目#23的应答状态：NC → FC', user: '管理员' },
    { time: '2024-01-26 11:20:33', type: '新增', desc: '新增条目：系统是否支持负载均衡？', user: '管理员' },
    { time: '2024-01-26 10:15:45', type: '导出', desc: '导出应答结果：5GC产品（45条）', user: '管理员' },
    { time: '2024-01-25 16:30:20', type: '删除', desc: '删除重复条目（3条）', user: '管理员' }
  ];
  
  return history.map(item => `
    <tr>
      <td style="padding:12px;border-bottom:1px solid #f0f0f0;">${item.time}</td>
      <td style="padding:12px;border-bottom:1px solid #f0f0f0;">
        <span style="padding:2px 8px;background:#e6f7ff;color:#1765d5;border-radius:4px;font-size:12px;">${item.type}</span>
      </td>
      <td style="padding:12px;border-bottom:1px solid #f0f0f0;">${item.desc}</td>
      <td style="padding:12px;border-bottom:1px solid #f0f0f0;">${item.user}</td>
      <td style="padding:12px;border-bottom:1px solid #f0f0f0;">
        <a href="#" style="color:#1765d5;">查看详情</a>
        ${item.type === '导入' || item.type === '导出' ? ' | <a href="#" style="color:#1765d5;">下载文件</a>' : ''}
      </td>
    </tr>
  `).join('');
}

// 初始化拖拽功能
function initPriorityDrag() {
  const container = document.getElementById('priorityList');
  if (!container) return;
  
  let draggedElement = null;
  
  container.addEventListener('dragstart', (e) => {
    if (e.target.classList.contains('priority-item')) {
      draggedElement = e.target;
      e.target.style.opacity = '0.5';
    }
  });
  
  container.addEventListener('dragend', (e) => {
    if (e.target.classList.contains('priority-item')) {
      e.target.style.opacity = '';
    }
  });
  
  container.addEventListener('dragover', (e) => {
    e.preventDefault();
    const afterElement = getDragAfterElement(container, e.clientY);
    if (afterElement == null) {
      container.appendChild(draggedElement);
    } else {
      container.insertBefore(draggedElement, afterElement);
    }
  });
  
  container.addEventListener('drop', (e) => {
    e.preventDefault();
    updatePriorityNumbers();
  });
}

// 获取拖拽后的元素位置
function getDragAfterElement(container, y) {
  const draggableElements = [...container.querySelectorAll('.priority-item:not(.dragging)')];
  
  return draggableElements.reduce((closest, child) => {
    const box = child.getBoundingClientRect();
    const offset = y - box.top - box.height / 2;
    
    if (offset < 0 && offset > closest.offset) {
      return { offset: offset, element: child };
    } else {
      return closest;
    }
  }, { offset: Number.NEGATIVE_INFINITY }).element;
}

// 更新优先级数字
function updatePriorityNumbers() {
  const items = document.querySelectorAll('.priority-item');
  items.forEach((item, index) => {
    const numberSpan = item.querySelector('span');
    if (numberSpan) {
      numberSpan.textContent = index + 1;
    }
  });
}

// 处理文档上传
window.handleDocUpload = function(input, type) {
  const file = input.files[0];
  if (!file) return;
  
  // 添加到已选文档列表
  const docList = JSON.parse(localStorage.getItem('selectedDocs') || '[]');
  docList.push({
    id: Date.now(),
    name: file.name,
    type: type === 'project' ? '项目文档' : '历史SOC文档',
    size: (file.size / 1024 / 1024).toFixed(2) + 'MB',
    time: new Date().toISOString()
  });
  localStorage.setItem('selectedDocs', JSON.stringify(docList));
  
  updateSelectedDocs();
  showToast(`文档 ${file.name} 上传成功`);
};

// 更新已选文档显示
function updateSelectedDocs() {
  const docList = JSON.parse(localStorage.getItem('selectedDocs') || '[]');
  const container = document.getElementById('selectedDocs');
  const countElement = document.getElementById('docCount');
  
  if (!container) return;
  
  if (docList.length === 0) {
    container.innerHTML = '<span style="color:#aaa;font-size:13px;">暂无已选文档</span>';
  } else {
    container.innerHTML = docList.map(doc => `
      <span class="material-tag" style="background:${getDocTypeColor(doc.type)};border:1px solid ${getDocTypeBorderColor(doc.type)};">
        <span class="doc-name">${doc.name}</span>
        <span class="doc-type">${doc.type}</span>
        <span class="doc-action" onclick="previewDoc(${doc.id})" title="预览文档">👁️</span>
        <span class="doc-action" onclick="removeDoc(${doc.id})" title="删除文档">✖️</span>
      </span>
    `).join('');
  }
  
  if (countElement) {
    countElement.textContent = `${docList.length}个文档`;
  }
  
  // 更新状态
  updateDocStatus();
}

// 更新文档准备状态
function updateDocStatus() {
  const docList = JSON.parse(localStorage.getItem('selectedDocs') || '[]');
  const statusElement = document.getElementById('docStatus');
  
  if (statusElement) {
    const isComplete = docList.length > 0;
    statusElement.textContent = `准备状态：${isComplete ? '已完成' : '未完成'}`;
    statusElement.className = `material-status ${isComplete ? 'complete' : 'incomplete'}`;
  }
}

// 选择文档库文档
window.selectFromLibrary = function() {
  const libraryDocs = [
    { id: 'lib1', name: '5GC技术白皮书.pdf', type: '文档库' },
    { id: 'lib2', name: 'VoLTE解决方案.docx', type: '文档库' },
    { id: 'lib3', name: 'IMS部署指南.pdf', type: '文档库' },
    { id: 'lib4', name: '网络安全规范.xlsx', type: '文档库' },
    { id: 'lib5', name: '性能测试标准.doc', type: '文档库' }
  ];
  
  let content = '<div style="max-height:400px;overflow:auto;">';
  content += libraryDocs.map(doc => `
    <label style="display:block;padding:8px;border-bottom:1px solid #f0f0f0;cursor:pointer;">
      <input type="checkbox" value="${doc.id}" style="margin-right:8px;" />
      <span>${doc.name}</span>
    </label>
  `).join('');
  content += '</div>';
  
  const footer = `
    <button class="soc-btn soc-btn-primary" onclick="confirmLibrarySelect()">确定</button>
    <button class="soc-btn" onclick="this.closest('.modal-overlay').remove()">取消</button>
  `;
  
  createModal('选择文档库文档', content, footer);
};

// 选择历史文档
window.selectFromHistory = function() {
  const historyDocs = JSON.parse(localStorage.getItem('historyDocs') || '[]');
  
  if (historyDocs.length === 0) {
    showToast('暂无历史文档', 'info');
    return;
  }
  
  let content = '<div style="max-height:400px;overflow:auto;">';
  content += historyDocs.map(doc => `
    <label style="display:block;padding:8px;border-bottom:1px solid #f0f0f0;cursor:pointer;">
      <input type="checkbox" value="${doc.id}" style="margin-right:8px;" />
      <span>${doc.name}</span>
      <span style="color:#999;font-size:12px;margin-left:8px;">${doc.time}</span>
    </label>
  `).join('');
  content += '</div>';
  
  const footer = `
    <button class="soc-btn soc-btn-primary" onclick="confirmHistorySelect()">确定</button>
    <button class="soc-btn" onclick="this.closest('.modal-overlay').remove()">取消</button>
  `;
  
  createModal('选择历史文档', content, footer);
};

// 其他辅助函数
window.previewDoc = function(docId) {
  showToast('预览功能开发中', 'info');
};

window.removeDoc = function(docId) {
  const docList = JSON.parse(localStorage.getItem('selectedDocs') || '[]');
  const index = docList.findIndex(doc => doc.id === docId);
  if (index > -1) {
    docList.splice(index, 1);
    localStorage.setItem('selectedDocs', JSON.stringify(docList));
    updateSelectedDocs();
    showToast('文档已移除');
  }
};

window.validateDocs = function() {
  const docList = JSON.parse(localStorage.getItem('selectedDocs') || '[]');
  const params = JSON.parse(localStorage.getItem('currentParams') || '{}');
  
  let issues = [];
  if (!params.products || params.products.length === 0) {
    issues.push('未选择产品');
  }
  if (docList.length === 0) {
    issues.push('未上传任何文档');
  }
  
  if (issues.length > 0) {
    showToast('校验失败：' + issues.join('、'), 'warning');
  } else {
    showToast('校验通过，准备项完整！', 'success');
  }
};

window.saveAsTemplate = function() {
  const name = prompt('请输入模板名称：');
  if (!name) return;
  
  const docList = JSON.parse(localStorage.getItem('selectedDocs') || '[]');
  const templates = JSON.parse(localStorage.getItem('docTemplates') || '[]');
  
  templates.push({
    id: Date.now(),
    name: name,
    docs: docList,
    createTime: new Date().toISOString()
  });
  
  localStorage.setItem('docTemplates', JSON.stringify(templates));
  showToast('模板保存成功');
};

// 初始化树形选择器和搜索下拉框
function initTreeSelect(container) {
  const treeInputs = container.querySelectorAll('.tree-select-input');
  
  treeInputs.forEach(input => {
    input.addEventListener('click', function(e) {
      e.stopPropagation();
      const dropdown = this.nextElementSibling;
      dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
    });
  });
  
  // 复选框变化时更新显示
  const checkboxes = container.querySelectorAll('.tree-select-dropdown input[type="checkbox"]');
  checkboxes.forEach(checkbox => {
    checkbox.addEventListener('change', function() {
      updateTreeSelectDisplay(this.closest('.tree-select-container'));
    });
  });
  
  // 点击其他地方关闭下拉框
  document.addEventListener('click', function(e) {
    if (!e.target.closest('.tree-select-container')) {
      container.querySelectorAll('.tree-select-dropdown').forEach(dropdown => {
        dropdown.style.display = 'none';
      });
    }
  });
}

function updateTreeSelectDisplay(container) {
  const input = container.querySelector('.tree-select-input');
  const checkboxes = container.querySelectorAll('input[type="checkbox"]:checked');
  const hiddenInput = container.querySelector('input[type="hidden"]');
  
  const selectedValues = Array.from(checkboxes).map(cb => cb.value);
  const selectedLabels = Array.from(checkboxes).map(cb => cb.parentElement.textContent.trim());
  
  // 更新隐藏输入框的值
  if (hiddenInput) {
    hiddenInput.value = selectedValues.join(',');
  }
  
  // 更新显示
  if (selectedLabels.length === 0) {
    input.innerHTML = '<span class="tree-select-placeholder" style="color:#999;">请选择产品</span>';
  } else {
    input.innerHTML = selectedLabels.map(label => 
      `<span style="display:inline-block;padding:2px 8px;background:#e6f7ff;color:#1765d5;border-radius:4px;font-size:12px;margin:2px;">${label}</span>`
    ).join('');
  }
}

function initSearchableSelects(container) {
  const searchableContainers = container.querySelectorAll('.searchable-select-container');
  
  searchableContainers.forEach(container => {
    const input = container.querySelector('input[type="text"]');
    const dropdown = container.querySelector('.searchable-dropdown');
    
    if (!input || !dropdown) return;
    
    // 聚焦时显示下拉框
    input.addEventListener('focus', function() {
      dropdown.style.display = 'block';
    });
    
    // 失焦时延迟隐藏下拉框（允许点击选项）
    input.addEventListener('blur', function() {
      setTimeout(() => {
        dropdown.style.display = 'none';
      }, 200);
    });
    
    // 选项点击事件
    dropdown.querySelectorAll('.dropdown-option').forEach(option => {
      option.addEventListener('click', function() {
        input.value = this.textContent;
        dropdown.style.display = 'none';
      });
    });
    
    // 搜索过滤
    input.addEventListener('input', function() {
      const filter = this.value.toLowerCase();
      dropdown.querySelectorAll('.dropdown-option').forEach(option => {
        const text = option.textContent.toLowerCase();
        option.style.display = text.includes(filter) ? 'block' : 'none';
      });
    });
  });
}

// 导出文档类型颜色函数
window.getDocTypeColor = function(type) {
  const colorMap = {
    '项目文档': '#e6f7ff',
    '历史SOC文档': '#f6ffed',
    '文档库': '#fff7e6'
  };
  return colorMap[type] || '#f5f5f5';
};

window.getDocTypeBorderColor = function(type) {
  const colorMap = {
    '项目文档': '#91d5ff',
    '历史SOC文档': '#b7eb8f',
    '文档库': '#ffd591'
  };
  return colorMap[type] || '#d9d9d9';
};