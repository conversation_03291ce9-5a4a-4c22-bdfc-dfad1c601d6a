<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zte.mcrm.channel.dao.OpportunityDao" >
	<!-- 屏蔽mybatis的2级缓存  <cache />  -->

	<!-- 记录和实体映射配置 -->
	<resultMap id="BaseMap" type="com.zte.mcrm.channel.model.entity.Opportunity" >
		<id column="id" property="rowId" jdbcType="VARCHAR" />
		<result column="last_modified_time" property="lastUpd" jdbcType="TIMESTAMP" />
		<result column="last_modified_by" property="lastUpdBy" jdbcType="VARCHAR" />
		<result column="create_time" property="created" jdbcType="TIMESTAMP" />
		<result column="create_by" property="createdBy" jdbcType="VARCHAR" />
		<result column="opty_code" property="optyCd" jdbcType="VARCHAR" />
		<result column="org_tree" property="buId" jdbcType="VARCHAR" />
		<result column="old_bu_id" property="oldBuId" jdbcType="VARCHAR" />
		<result column="org_name_path" property="orgNamePath" jdbcType="VARCHAR" />
		<result column="opty_status" property="statusCd" jdbcType="VARCHAR" />
		<result column="report_status" property="reportStatus" jdbcType="VARCHAR"/>
		<result column="current_status" property="currentStatus" jdbcType="VARCHAR"/>
		<result column="PR_DEPT_OU_ID" property="prDeptOuId" jdbcType="VARCHAR" />
		<result column="delivery_interface" property="businessTypeCd" jdbcType="VARCHAR" />
		<result column="X_MKT_ID" property="xMktId" jdbcType="VARCHAR" />
		<result column="belonge_group" property="parOptyId" jdbcType="VARCHAR" />
		<result column="data_source" property="dataSource" jdbcType="VARCHAR" />
		<result column="NAME" property="name" jdbcType="VARCHAR" />
		<result column="SPACE_ID" property="spaceId" jdbcType="VARCHAR" />
		<result column="enabled_flag" property="enabledFlag" jdbcType="CHAR" />
		<result column="success_date" property="successDate" jdbcType="TIMESTAMP"/>
		<result column="expiry_date" property="expiryDate" jdbcType="TIMESTAMP"/>
		<result column="submit_date" property="submitDate" jdbcType="TIMESTAMP"/>
		<result column="is_new_business" property="isNewBusiness" jdbcType="VARCHAR" />
	</resultMap>

	<resultMap id="ExtMap" type="com.zte.mcrm.channel.model.vo.OpportunityVO" >
		<id column="id" property="rowId" jdbcType="VARCHAR" />
		<result column="create_time" property="created" jdbcType="TIMESTAMP" />
		<result column="create_by" property="createdBy" jdbcType="VARCHAR" />
		<result column="opty_code" property="optyCd" jdbcType="VARCHAR" />
		<result column="opty_status" property="statusCd" jdbcType="VARCHAR" />
		<result column="data_source" property="dataSource" jdbcType="VARCHAR" />
		<result column="opty_name" property="attrib46" jdbcType="VARCHAR" />
		<result column="X_TOTAL_AMOUNT" property="totalAmount" jdbcType="DECIMAL" />
		<result column="PROD_LV2_NAME" property="prodLv2Name" jdbcType="VARCHAR" />
		<result column="org_tree" property="deptNo" jdbcType="VARCHAR" />
		<result column="x_last_acc_name" property="finalCustomerName" jdbcType="VARCHAR" />
		<result column="customer" property="finalCustomerCode" jdbcType="VARCHAR" />
		<result column="active_count" property="activeCount" jdbcType="INTEGER"/>
		<result column="final_customer_child_trade" property="finalCustomerTradeChildCode" jdbcType="VARCHAR"/>
		<result column="project_phases_code" property="projectPhasesCode" jdbcType="VARCHAR"/>
		<result column="bidding_type" property="tendTypeCode" jdbcType="VARCHAR"/>
		<result column="for_bid_time" property="estimatedBiddingTime" jdbcType="DATE"/>
		<result column="bidding_deadline" property="biddingDeadline" jdbcType="TIMESTAMP" />
		<result column="CUSTOMER_NAME" property="channelBusiness" jdbcType="VARCHAR"/>
		<result column="business_manager_id" property="businessManagerId" jdbcType="VARCHAR"/>
		<result column="business_manager_name" property="businessManagerName" jdbcType="VARCHAR"/>
		<result column="win_rate" property="winRate" jdbcType="VARCHAR" />
		<result column="bid_provider_name" property="bidProviderName" jdbcType="VARCHAR" />
		<result column="report_status" property="statusReport" jdbcType="VARCHAR"/>
		<result column="current_status" property="currentStatus" jdbcType="VARCHAR"/>
	</resultMap>

	<resultMap id="PrmExtMap" type="com.zte.mcrm.channel.model.vo.PrmOpportunityVO" >
		<id column="id" property="rowId" jdbcType="VARCHAR" />
		<result column="opty_code" property="optyCd" jdbcType="VARCHAR" />
		<result column="opty_name" property="opportunityName" jdbcType="VARCHAR" />
		<result column="org_tree" property="deptNo" jdbcType="VARCHAR"/>
		<result column="final_customer_child_trade" property="finalCustomerTradeChildCode" jdbcType="VARCHAR"/>
		<result column="final_customer_parent_trade" property="finalCustomerTradeCode" jdbcType="VARCHAR"/>
		<result column="productIds" property="productIds" jdbcType="VARCHAR" />
		<result column="productNames" property="productNames" jdbcType="VARCHAR" />
		<result column="X_TOTAL_AMOUNT" property="totalAmount" jdbcType="DECIMAL" />
		<result column="project_phases_code" property="projectPhasesCode" jdbcType="VARCHAR"/>
		<result column="bidding_type" property="tendTypeCode" jdbcType="VARCHAR"/>
		<result column="for_bid_time" property="estimatedBiddingTime" jdbcType="DATE"/>
		<result column="create_time" property="created" jdbcType="TIMESTAMP" />
		<result column="data_source" property="dataSource" jdbcType="VARCHAR" />
		<result column="business_manager_id" property="businessManagerId" jdbcType="VARCHAR"/>
		<result column="business_manager_name" property="businessManagerName" jdbcType="VARCHAR"/>
		<result column="CUSTOMER_NAME" property="channelBusiness" jdbcType="VARCHAR"/>
		<result column="work_flow_instance_id" property="flowInstanceId" jdbcType="VARCHAR"/>
		<result column="create_by" property="createdBy" jdbcType="VARCHAR" />
		<result column="opty_status" property="statusCd" jdbcType="VARCHAR" />
		<result column="X_OPTY_PHASE" property="oldProjectPhasesCode" jdbcType="VARCHAR"/>
		<result column="TEND_TYPE" property="oldTendTypeCode" jdbcType="VARCHAR"/>
		<result column="x_last_acc_name" property="finalCustomerName" jdbcType="VARCHAR" />
		<result column="bidding_deadline" property="biddingDeadline" jdbcType="TIMESTAMP" />
		<result column="win_rate" property="winRate" jdbcType="VARCHAR" />
		<result column="bid_provider_name" property="bidProviderName" jdbcType="VARCHAR" />
		<result column="active_count" property="activeCount" jdbcType="INTEGER"/>
		<result column="from_active_flag" property="fromActiveFlag" jdbcType="VARCHAR"/>
		<result column="from_active_opty" property="fromActiveOpty" jdbcType="VARCHAR"/>
		<result column="customer" property="finalCustomerId" jdbcType="VARCHAR"/>
		<result column="final_user" property="finalCustomerCode" jdbcType="VARCHAR"/>
		<result column="last_acc_status" property="lastAccStatus" jdbcType="TINYINT"/>
		<result column="crm_customer_code" property="crmCustomerCode" jdbcType="VARCHAR"/>
		<result column="report_status" property="reportStatus" jdbcType="VARCHAR"/>
		<result column="current_status" property="currentStatus" jdbcType="VARCHAR"/>
		<result column="expiry_date" property="expiryDate" jdbcType="DATE"/>
		<result column="final_acnt_sanctioned_code" property="finalCustomerRestrictionFlag" jdbcType="VARCHAR"/>
		<result column="acnt_sanctioned_code" property="agencyRestrictionFlag" jdbcType="VARCHAR"/>
	</resultMap>

	<resultMap id="PrmPendingMap" type="com.zte.mcrm.channel.model.vo.PrmOpportunityPendingVO" >
		<id column="id" property="rowId" jdbcType="VARCHAR" />
		<result column="opty_code" property="optyCd" jdbcType="VARCHAR" />
		<result column="opty_name" property="opportunityName" jdbcType="VARCHAR" />
		<result column="org_tree" property="deptNo" jdbcType="VARCHAR"/>
		<result column="final_customer_child_trade" property="finalCustomerTradeChildCode" jdbcType="VARCHAR"/>
		<result column="final_customer_parent_trade" property="finalCustomerTradeCode" jdbcType="VARCHAR"/>
		<result column="project_phases_code" property="projectPhasesCode" jdbcType="VARCHAR"/>
		<result column="create_time" property="created" jdbcType="TIMESTAMP" />
		<result column="data_source" property="dataSource" jdbcType="VARCHAR" />
		<result column="CUSTOMER_NAME" property="channelBusiness" jdbcType="VARCHAR"/>
		<result column="x_last_acc_name" property="lastAccName" jdbcType="VARCHAR"/>
		<result column="final_customer_contact_name" property="finalCustomerContactName" jdbcType="VARCHAR"/>
		<result column="create_by" property="createdBy" jdbcType="VARCHAR" />
		<result column="opty_status" property="statusCd" jdbcType="VARCHAR" />
		<result column="work_flow_instance_id" property="flowInstanceId" jdbcType="VARCHAR"/>
	</resultMap>

	<resultMap id="OldOpportunityInfoMap" type="com.zte.mcrm.channel.model.entity.OldOpportunityInfoEntity">
		<result column="id" property="rowId" jdbcType="VARCHAR"/>
		<result column="create_time" property="created" jdbcType="TIMESTAMP" />
		<result column="create_by" property="createdBy" jdbcType="VARCHAR" />
		<result column="last_modified_time" property="lastUpd" jdbcType="TIMESTAMP" />
		<result column="last_modified_by" property="lastUpdBy" jdbcType="VARCHAR" />
		<result column="opty_code" property="optyCd" jdbcType="VARCHAR"/>
		<result column="opty_status" property="statusCd" jdbcType="VARCHAR"/>
		<result column="PR_DEPT_OU_ID" property="prDeptOuId" jdbcType="VARCHAR"/>
		<result column="delivery_interface" property="businessTypeCd" jdbcType="VARCHAR"/>
		<result column="org_tree" property="deptNo" jdbcType="VARCHAR"/>
		<result column="data_source" property="dataSource" jdbcType="VARCHAR"/>
		<result column="for_bid_time" property="date1" jdbcType="DATE"/>
		<result column="SECOND_DEALER_ID" property="secondDealerId" jdbcType="VARCHAR"/>
		<result column="CRM_CUSTOMER_CODE" property="crmCustomerCode" jdbcType="VARCHAR"/>
		<result column="CUSTOMER_NAME" property="customerName" jdbcType="VARCHAR"/>
		<result column="customer" property="lastAccId" jdbcType="VARCHAR"/>
		<result column="final_customer_child_trade" property="finalCustomerChildTrade" jdbcType="VARCHAR"/>
		<result column="final_customer_parent_trade" property="finalCustomerParentTrade" jdbcType="VARCHAR"/>
		<result column="final_customer_contact_name" property="finalCustomerContactName" jdbcType="VARCHAR"/>
		<result column="final_customer_contact_phone" property="finalCustomerContactPhone" jdbcType="VARCHAR"/>
		<result column="PROJECT_PHASES_NAME" property="projectPhasesCode" jdbcType="VARCHAR"/>
		<result column="PROJECT_PHASES_NAME" property="projectPhasesName" jdbcType="VARCHAR"/>
		<result column="WIN_RATE" property="winRate" jdbcType="VARCHAR"/>
		<result column="TENDER_TYPE_NAME" property="tenderTypeCode" jdbcType="VARCHAR"/>
		<result column="TENDER_TYPE_NAME" property="tenderTypeName" jdbcType="VARCHAR"/>
		<result column="bid_provider_name" property="bidProviderName" jdbcType="VARCHAR"/>
		<result column="BIDDING_DEADLINE" property="biddingDeadline" jdbcType="TIMESTAMP"/>
		<result column="business_manager_name" property="businessManagerName" jdbcType="VARCHAR"/>
		<result column="business_manager_id" property="businessManagerId" jdbcType="VARCHAR"/>
		<result column="PROJECT_DESC" property="projectDesc" jdbcType="VARCHAR"/>
		<result column="opty_name" property="attrib46" jdbcType="VARCHAR"/>
		<result column="NATIONAL_AREA_ID" property="nationalAreaId" jdbcType="VARCHAR"/>
		<result column="NATIONAL_AREA_NAME" property="nationalAreaName" jdbcType="VARCHAR"/>
		<result column="X_TOTAL_AMOUNT" property="totalAmount" jdbcType="DECIMAL"/>
		<result column="SALES_TYPE" property="salesType" jdbcType="VARCHAR"/>
		<result column="final_usage" property="finalUsage" jdbcType="VARCHAR"/>
		<result column="end_user_type" property="endUserType" jdbcType="VARCHAR"/>
		<result column="enduse_of_enduser" property="enduseOfEnduser" jdbcType="VARCHAR"/>
		<result column="specific_customer_desc" property="specificCustomerDesc" jdbcType="VARCHAR"/>
		<result column="specific_usage_desc" property="specificUsageDesc" jdbcType="VARCHAR"/>
	</resultMap>

	<sql id="base_column">
		t.id ,
        t.last_modified_time ,
        t.last_modified_by ,
        t.create_time ,
        t.create_by ,
        t.opty_code ,
        t.org_tree ,
        t.old_bu_id ,
        t.org_name_path ,
        t.opty_status ,
		t.report_status ,
		t.current_status ,
        t.PR_DEPT_OU_ID ,
        JSON_VALUE(t.delivery_interface, '$[0].value') AS delivery_interface,
        t.X_MKT_ID ,
        t.belonge_group ,
        t.data_source ,
        t.NAME ,
        t.SPACE_ID ,
		CASE WHEN is_deleted = 1 THEN 'N' ELSE 'Y' END AS enabled_flag,
		t.submit_date,
		t.success_date,
		t.expiry_date,
		JSON_VALUE(t.is_new_business, '$[0].value') AS is_new_business
	</sql>

	<sql id="base_where">
		<if test="rowId != null and rowId != ''"> and t.id = #{rowId}</if>
		<if test="lastUpd != null"> and t.last_modified_time = #{lastUpd}</if>
		<if test="lastUpdBy != null and lastUpdBy != ''"> and t.last_modified_by = #{lastUpdBy}</if>
		<if test="created != null"> and t.create_time = #{created}</if>
		<if test="createdBy != null and createdBy != ''"> and t.create_by = #{createdBy}</if>
		<if test="optyCd != null and optyCd != ''"> and t.opty_code = #{optyCd}</if>
		<if test="buId != null and buId != ''"> and t.org_tree = #{buId}</if>
		<if test="oldBuId != null and oldBuId != ''"> and t.old_bu_id = #{oldBuId}</if>
		<if test="orgNamePath != null and orgNamePath != ''"> and t.org_name_path = #{orgNamePath}</if>
		<if test="statusCd != null and statusCd != ''"> and t.opty_status = #{statusCd}</if>
		<if test="prDeptOuId != null and prDeptOuId != ''"> and t.PR_DEPT_OU_ID = #{prDeptOuId}</if>
		<if test="businessTypeCd != null and businessTypeCd != ''"> and JSON_VALUE(t.delivery_interface, '$[0].value') = #{businessTypeCd}</if>
		<if test="xMktId != null and xMktId != ''"> and t.X_MKT_ID = #{xMktId}</if>
		<if test="parOptyId != null and parOptyId != ''"> and t.belonge_group = #{parOptyId}</if>
		<if test="dataSource != null and dataSource != ''"> and t.data_source = #{dataSource}</if>
		<if test="name != null and name != ''"> and t.NAME = #{name}</if>
		<if test="spaceId != null and spaceId != ''"> and t.SPACE_ID = #{spaceId}</if>
		<if test="reportStatus != null and reportStatus != ''"> and report_status = #{reportStatus}</if>
		<if test="currentStatus != null and currentStatus != ''"> and current_status=#{currentStatus}</if>
	</sql>

	<!-- 获取一条记录 -->
	<select id="get"  resultMap="BaseMap">
		SELECT <include refid="base_column"/>
		FROM s_opty t
		WHERE
		t.id=#{rowId, jdbcType=VARCHAR}
		and is_deleted = 0
	</select>

	<select id="getMailOpportunityInfoEntity" resultType="com.zte.mcrm.channel.model.entity.MailOpportunityInfoEntity">
		select
			s.id as rowId,
			s.opty_code as optyCd,
			s.opty_name as attrib46,
			x.x_last_acc_name as lastAccName,
			x.final_acnt_sanctioned_code as finalCustomerRestrictionFlag,
			x.customer_name as customerName,
			x.acnt_sanctioned_code as agencyRestrictionFlag,
			s.data_source as dataSource,
			JSON_VALUE(tm.employee, '$[0].empUIID') as businessManagerId
		from s_opty s
		left join s_opty_x x on s.id = x.id
		left join s_opty_team tm on s.id = tm.p_id AND tm.is_deleted = 0
		where s.is_deleted = 0
		  and  s.id=#{rowId, jdbcType=VARCHAR}
	</select>

	<select id="getByOptyCd"  resultMap="BaseMap">
		SELECT <include refid="base_column"/>
		FROM s_opty t
		WHERE
		t.opty_code=#{optyCd, jdbcType=VARCHAR}
		and t.is_deleted = 0
		and t.data_source in ('iChannel','PRM')
	</select>

	<select id="getOldOpportunityByRowId" resultMap="OldOpportunityInfoMap">
		select s.id,
		       s.last_modified_time,
		       s.last_modified_by,
		       s.create_by,
		       s.create_time,
			   s.opty_code,
			   s.opty_status,
			   s.PR_DEPT_OU_ID,
			   JSON_VALUE(s.delivery_interface, '$[0].value') AS delivery_interface,
			   s.org_tree org_tree,
			   s.data_source,
			   ifnull(x.for_bid_time,x.date_2) as for_bid_time,
			   x.SECOND_DEALER_ID,
			   p.CRM_CUSTOMER_CODE,
			   p.CUSTOMER_NAME,
			   x.customer,
			   x.CHILD_TRADE as final_customer_child_trade,
			   x.PARENT_TRADE as final_customer_parent_trade,
			   p.LINKMAN_NAME as final_customer_contact_name,
			   p.LINKMAN_TEL as final_customer_contact_phone,
			   p.PROJECT_PHASES_NAME,
			   p.WIN_RATE,
			   p.TENDER_TYPE_NAME,
			   x.bid_provider_name,
			   p.BIDDING_DEADLINE,
			   p.BUSSINESS_MANAGER_NAME as business_manager_name,
			   p.PROJECT_DESC,
			   s.opty_name,
			   x.NATIONAL_AREA_ID,
			   x.NATIONAL_AREA_NAME,
			   x.X_TOTAL_AMOUNT,
			   x.SALES_TYPE,
			   x.final_usage,
			   x.end_user_type,
			   x.enduse_of_enduser,
			   x.specific_customer_desc,
			   x.specific_usage_desc
		from s_opty s
				 left join cx_prm_opp p on p.OPP_ID = s.id
				 left join s_opty_x x on x.id = s.id
		where s.id=#{rowId, jdbcType=VARCHAR}
		  and s.is_deleted = 0
	</select>

	<!-- 获取一条记录 -->
	<select id="getAll"  resultMap="BaseMap">
		SELECT <include refid="base_column"/>
		FROM s_opty t
		WHERE
		t.id=#{rowId, jdbcType=VARCHAR}
		and t.is_deleted = 0
	</select>

	<!-- 获取符合条件的记录列表 -->
	<select id="getList" parameterType="java.util.Map" resultMap="BaseMap">
		SELECT <include refid="base_column"/>
		FROM s_opty t
		WHERE is_deleted = 0
		<include refid="base_where"/>
	</select>

	<select id="getOpportunityIdRestricted" resultType="string">
		select x.id
		from s_opty_x x
	 	left join s_opty s on s.id = x.id
		where (final_acnt_sanctioned_code in ('pending', 'yes', 'embargo') or  acnt_sanctioned_code in ('pending', 'yes', 'embargo'))
		  and s.opty_status = 'reportedApprovaling'
		  and s.is_deleted = 0
		  and s.data_source = 'iChannel'
	</select>

	<!-- 翻页函数:获取符合条件的记录数 -->
	<select id="getCount" parameterType="java.util.Map" resultType="java.lang.Long">
		SELECT count(distinct o.id)
		from s_opty o
		left join s_opty_x x on o.id = x.id
		where o.is_deleted = 0
		<if test="crmCustomerCode != null and crmCustomerCode != ''">
			and (x.crm_customer_code = #{crmCustomerCode, jdbcType=VARCHAR} or x.source_crm_customer_code = #{crmCustomerCode, jdbcType=VARCHAR})
		</if>
		<if test="keyword != null and keyword != ''">
			and concat(ifnull(o.opty_code,''),'@',ifnull(o.opty_name,''),'@',ifnull(x.x_last_acc_name,'')) like concat('%',#{keyword, jdbcType=VARCHAR},'%')
		</if>
		<if test="minDate != null and  minDate != ''">
			<![CDATA[ and  date_format(o.create_time,'%Y-%m-%d') >= date_format(#{minDate,jdbcType=TIMESTAMP},'%Y-%m-%d')]]>
		</if>
		<if test="maxDate != null and  maxDate != ''">
			<![CDATA[ and  date_format(o.create_time,'%Y-%m-%d') <= date_format(#{maxDate,jdbcType=TIMESTAMP},'%Y-%m-%d')]]>
		</if>
		<if test="statusCds != null and statusCds.size > 0">
			and ((o.opty_status in
			<foreach item="item" index="index" collection="statusCds" open="(" separator="," close=")">
				#{item}
			</foreach> and o.opty_code  like 'XS%')
			<if test="oldStatusCd != null and oldStatusCd.size() > 0">
				or ( o.opty_status in (
				<foreach collection="oldStatusCd" item="item" index="index" separator="," >
					#{item,jdbcType=VARCHAR}
				</foreach>)
				and o.opty_code  like 'O%')
			</if>)
		</if>
		<if test="sourceType != null and sourceType == ''">
			and (
			o.data_source = 'iChannel' and o.opty_code  like 'XS%'
			or (o.data_source = 'PRM' and o.opty_status in ('Renewing','invalidation','ticketWin','Closed','cancel','Proj App Submit','Transferred'))
			or (o.opty_code  like 'O%' and o.opty_status in ('Renewing','Closed','Proj App Submit','Transferred'))
			)
		</if>
		<if test="sourceType != null and sourceType == 'iChannel'">
			and (o.data_source = 'iChannel'  and o.opty_code like 'XS%'
			or (o.opty_code like 'O%' and o.opty_status in ('Renewing', 'Closed', 'Proj App Submit', 'Transferred'))	)
		</if>
		<if test="sourceType != null and sourceType == 'PRM'">
			and o.data_source = 'PRM' and o.opty_status in ('Renewing', 'invalidation', 'ticketWin', 'Closed', 'cancel', 'Proj App Submit', 'Transferred')
		</if>
		<if test="reportStatusType == 'unfilled'">
			and (o.report_status = 'unfilled' or o.report_status is null or o.report_status = '')
		</if>
		<if test="reportStatusType == 'filled'">
			and o.report_status = 'filled'
		</if>
		<if test="rowId != null and rowId != ''">
			and o.id = #{rowId,jdbcType=VARCHAR}
		</if>
		and o.data_source in ('PRM','iChannel')
	</select>

	<!-- 翻页函数:获取一页的记录集 -->
	<select id="getPage" parameterType="java.util.Map" resultMap="ExtMap">
		WITH tp AS (
		SELECT p_id,
		json_value(success_rate, '$[0].value') success_rate,
		row_number() OVER (PARTITION BY p_id ORDER BY last_modified_time DESC ) AS rn
		FROM s_opty_product
		WHERE is_deleted = 0
		AND json_length(success_rate) != 0
		)
		SELECT
			o.id,
			o.opty_code,
			o.opty_status,
			o.data_source,
			o.create_time,
			o.create_by,
			o.opty_name,
			x.X_TOTAL_AMOUNT,
			o.org_tree,
			x.x_last_acc_name,
			x.customer,
			o.report_status,
			o.current_status,
			x.active_count,
		<if test="isExport">
			x.final_customer_child_trade,
			x.project_phases_code,
			JSON_VALUE(o.bidding_type, '$[0].value') AS bidding_type,
			o.for_bid_time,
			x.bidding_deadline,
			x.CUSTOMER_NAME,
			JSON_VALUE(tm.employee, '$[0].empUIID') business_manager_id,
			JSON_UNQUOTE(JSON_VALUE(tm.employee, '$[0].empName')) business_manager_name,
			tp.success_rate win_rate,
			x.bid_provider_name,
		</if>
			(select group_concat(prod_lv2_name) from s_opty_product where p_id = o.id and is_deleted = 0 and business_type != 'transferProject'  group by p_id)  as PROD_LV2_NAME
		from s_opty o
		left join s_opty_x x on o.id = x.id
		left join s_opty_team tm on o.id = tm.p_id AND tm.is_deleted = 0 AND tm.employee_type = 1
		LEFT join tp on o.id = tp.p_id AND tp.rn = 1
		where o.is_deleted = 0
		<if test="crmCustomerCode != null and crmCustomerCode != ''">
			and (crm_customer_code = #{crmCustomerCode, jdbcType=VARCHAR} or source_crm_customer_code = #{crmCustomerCode, jdbcType=VARCHAR})
		</if>
		<if test="keyword != null and keyword != ''">
			and concat(ifnull(o.opty_code,''),'@',ifnull(o.opty_name,''),'@',ifnull(x.x_last_acc_name,'')) like concat('%',#{keyword, jdbcType=VARCHAR},'%')
		</if>
		<if test="minDate != null and  minDate != ''">
			<![CDATA[ and  date_format(o.create_time,'%Y-%m-%d') >= date_format(#{minDate,jdbcType=TIMESTAMP},'%Y-%m-%d')]]>
		</if>
		<if test="maxDate != null and  maxDate != ''">
			<![CDATA[ and  date_format(o.create_time,'%Y-%m-%d') <= date_format(#{maxDate,jdbcType=TIMESTAMP},'%Y-%m-%d')]]>
		</if>
		<if test="statusCds != null and statusCds.size > 0">
			and ((o.opty_status in
			<foreach item="item" index="index" collection="statusCds" open="(" separator="," close=")">
				#{item}
			</foreach> and o.opty_code  like 'XS%')
			<if test="oldStatusCd != null and oldStatusCd.size() > 0">
				or ( o.opty_status in (
				<foreach collection="oldStatusCd" item="item" index="index" separator="," >
					#{item,jdbcType=VARCHAR}
				</foreach>)
				and o.opty_code  like 'O%')
			</if>)
		</if>
		<if test="sourceType != null and sourceType == ''">
			and (
			o.data_source = 'iChannel' and o.opty_code  like 'XS%'
			or (o.data_source = 'PRM' and o.opty_status in ('Renewing','invalidation','ticketWin','Closed','cancel','Proj App Submit','Transferred'))
			or (o.opty_code  like 'O%' and o.opty_status in ('Renewing','invalidation','ticketWin','Closed','cancel','Proj App Submit','Transferred'))
			)
		</if>
		<if test="sourceType != null and sourceType == 'iChannel'">
			and (o.data_source = 'iChannel'  and o.opty_code like 'XS%'
			or (o.opty_code like 'O%' and o.opty_status in ('Renewing','invalidation','ticketWin','Closed','cancel','Proj App Submit','Transferred'))	)
		</if>
		<if test="sourceType != null and sourceType == 'PRM'">
			and o.data_source = 'PRM' and o.opty_status in ('Renewing', 'invalidation', 'ticketWin', 'Closed', 'cancel', 'Proj App Submit', 'Transferred')
		</if>
		<if test="reportStatusType == 'unfilled'">
			and (o.report_status = 'unfilled' or o.report_status is null or o.report_status = '')
		</if>
		<if test="reportStatusType == 'filled'">
			and o.report_status = 'filled'
		</if>
		<!-- 这里添加的rowId是为了页面鉴权时用的	-->
		<if test="rowId != null and rowId != ''">
			and o.id = #{rowId,jdbcType=VARCHAR}
		</if>
		and o.data_source in ('PRM','iChannel')
		order by o.create_time desc
		<if test="startRow != null and rowSize != null"> limit #{startRow}, #{rowSize} </if>
	</select>

	<!-- 翻页函数:获取一页的记录集 -->
	<select id="getPrmOpportunityPage" parameterType="java.util.Map" resultMap="PrmExtMap">
		WITH tp AS (
		SELECT p_id,
		json_value(success_rate, '$[0].value') success_rate,
		row_number() OVER (PARTITION BY p_id ORDER BY last_modified_time DESC ) AS rn
		FROM s_opty_product
		WHERE is_deleted = 0
		AND json_length(success_rate) != 0
		)
		SELECT
		o.id ,
		o.opty_code ,
		o.opty_status ,
		o.data_source ,
		o.create_time ,
		o.create_by ,
		o.opty_name ,
		x.X_TOTAL_AMOUNT ,
		o.org_tree ,
		x.project_phases_code,
		x.X_OPTY_PHASE,
		JSON_VALUE(o.bidding_type, '$[0].value') AS bidding_type,
		x.TEND_TYPE,
		x.final_customer_child_trade,
		x.final_customer_parent_trade,
		o.for_bid_time,
		JSON_VALUE(tm.employee, '$[0].empUIID') business_manager_id,
		JSON_UNQUOTE(JSON_VALUE(tm.employee, '$[0].empName')) business_manager_name,
		x.customer_name,
		x.customer,
		x.final_user,
		x.last_acc_status,
		r.work_flow_instance_id,
		x.crm_customer_code,
		x.x_last_acc_name,
		o.report_status,
		o.current_status,
		x.active_count,x.final_acnt_sanctioned_code,x.acnt_sanctioned_code,
		<if test="isExport">
			x.bidding_deadline,
			tp.success_rate win_rate,
			x.bid_provider_name,
			x.from_active_flag,
			x.from_active_opty,
			o.expiry_date,
		</if>
		(select group_concat(product_operation_team) from s_opty_product  where p_id = o.id and is_deleted = 0 and business_type != 'transferProject' group by p_id)  as productIds,
		(select group_concat(prod_lv2_name) from s_opty_product where p_id = o.id and is_deleted = 0 and business_type != 'transferProject' group by p_id)  as productNames
		from s_opty o
		left join s_opty_x x on o.id = x.id
		left join com_approval_record r on r.business_id = o.id and r.enabled_flag = 'Y'
		left join s_opty_team tm on o.id = tm.p_id AND tm.is_deleted = 0 AND tm.employee_type = 1
		left join tp on o.id = tp.p_id AND tp.rn = 1
		where o.is_deleted = 0
		<if test="!isAdmin">
			and (o.create_by = #{empNo, jdbcType=VARCHAR} or JSON_VALUE(tm.employee, '$[0].empUIID') = #{empNo, jdbcType=VARCHAR}
			<if test="null != constrainedOrgList and constrainedOrgList.size() > 0 and null != constrainedIndustryList and constrainedIndustryList.size() > 0">
				or ( o.org_tree in (
				<foreach collection="constrainedOrgList" item="item" index="index" separator="," >
					#{item,jdbcType=VARCHAR}
				</foreach>)
				and  x.final_customer_child_trade in (
				<foreach collection="constrainedIndustryList" item="item" index="index" separator="," >
					#{item,jdbcType=VARCHAR}
				</foreach>)
				)
			</if>
			<if test="null != myRelatedIds and myRelatedIds.size() > 0">
				or r.work_flow_instance_id in
				<foreach collection="myRelatedIds" item="item" index="index" open="(" close=")" separator=",">
					#{item,jdbcType=VARCHAR}
				</foreach>
			</if>
			)
		</if>
		<if test="isAdmin">
			and ( o.opty_status not in ('Draft') or o.create_by = #{empNo, jdbcType=VARCHAR} or JSON_VALUE(tm.employee, '$[0].empUIID') = #{empNo, jdbcType=VARCHAR} )
		</if>
		<if test="keyWordForQuery != null and keyWordForQuery != ''">
			and concat(ifnull(o.opty_code,''),'@',ifnull(o.opty_name,''),'@',ifnull(x.CUSTOMER_NAME, '')) like concat('%',#{keyWordForQuery, jdbcType=VARCHAR},'%')
		</if>
		<if test="minCreateDate != null and  minCreateDate != ''">
			and date_format(o.create_time,'%Y-%m-%d') >= date_format(#{minCreateDate,jdbcType=TIMESTAMP},'%Y-%m-%d')
		</if>
		<if test="maxCreateDate != null and  maxCreateDate != ''">
			and date_format(o.create_time,'%Y-%m-%d') &lt;= date_format(#{maxCreateDate,jdbcType=TIMESTAMP},'%Y-%m-%d')
		</if>
		<if test="minTimeForBidIssuance != null and  minTimeForBidIssuance != ''">
			and date_format(o.for_bid_time,'%Y-%m-%d') >= date_format(#{minTimeForBidIssuance,jdbcType=DATE},'%Y-%m-%d')
		</if>
		<if test="maxTimeForBidIssuance != null and  maxTimeForBidIssuance != ''">
			and date_format(o.for_bid_time,'%Y-%m-%d') &lt;= date_format(#{maxTimeForBidIssuance,jdbcType=DATE},'%Y-%m-%d')
		</if>
		<if test="null != statusCd and statusCd.size() > 0">
			and ((o.opty_status in (
			<foreach collection="statusCd" item="item" index="index" separator="," >
				#{item,jdbcType=VARCHAR}
			</foreach>) and o.opty_code  like 'XS%')
			<if test="oldStatusCd != null and oldStatusCd.size() > 0">
				or ( o.opty_status in (
				<foreach collection="oldStatusCd" item="item" index="index" separator="," >
					#{item,jdbcType=VARCHAR}
				</foreach>)
				and o.opty_code  like 'O%')
			</if>)
		</if>
		<if test="reportStatusType == 'unfilled'">
			and (o.report_status = 'unfilled' or o.report_status is null or o.report_status = '')
		</if>
		<if test="reportStatusType == 'filled'">
			and o.report_status = 'filled'
		</if>
		<if test="null != dataSource and dataSource.size() > 0">
			and o.data_source in (
			<foreach collection="dataSource" item="item" index="index" separator="," >
				#{item,jdbcType=VARCHAR}
			</foreach>)
		</if>
		<if test="projectPhasesCode != null and projectPhasesCode != ''">
			and x.project_phases_code = #{projectPhasesCode}
		</if>
		<if test="tenderTypeCode != null and tenderTypeCode != ''">
			and JSON_VALUE(o.bidding_type, '$[0].value') = #{tenderTypeCode}
		</if>
		<if test="deptNo != null and deptNo != ''">
			and o.org_tree = #{deptNo}
		</if>
		<if test="finalCustomerTrade != null and finalCustomerTrade != ''">
			and x.final_customer_parent_trade = #{finalCustomerTrade}
		</if>
		<if test="finalCustomerChildTrade != null and finalCustomerChildTrade != ''">
			and x.final_customer_child_trade = #{finalCustomerChildTrade}
		</if>
		and o.data_source in ('PRM','iChannel')
		and o.tenant_id = #{headerTenantId}
		order by o.create_time desc
	</select>

	<!-- 翻页函数:获取一页的记录集 -->
	<select id="getPrmPendingInfos" parameterType="java.util.List" resultMap="PrmPendingMap">
		SELECT
		o.id ,
		o.opty_code ,
		o.opty_status ,
		o.data_source ,
		o.create_time ,
		o.create_by ,
		o.opty_name ,
		o.org_tree,
		x.project_phases_code,
		x.final_customer_contact_name,
		x.final_customer_child_trade,
		x.final_customer_parent_trade,
		x.CUSTOMER_NAME,
		x.x_last_acc_name
		from s_opty o
		left join s_opty_x x on o.id = x.id
		where o.is_deleted = 0
		and o.id in
		<foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
			#{item,jdbcType=VARCHAR}
		</foreach>
	</select>

	<select id="getFlowInstanceIdByBusinessId" parameterType="string" resultType="string">
		select t.work_flow_instance_id from com_approval_record t
		where t.business_id = #{businessId,jdbcType=VARCHAR}
		  and t.enabled_flag = 'Y'
	</select>

	<select id="getBusinessManagerIdByRowId" parameterType="string" resultType="string">
		select
			JSON_VALUE(tm.employee, '$[0].empUIID') business_manager_id
		from s_opty s
		join s_opty_team tm on s.id = tm.p_id
		where s.id = #{rowId,jdbcType=VARCHAR}
		  and s.is_deleted = 0
		  and tm.is_deleted = 0
		  AND tm.employee_type = 1
	</select>

	<select id="getMonthReportOpportunity" resultMap="ExtMap">
		SELECT o.id,
		o.opty_code,
		o.opty_status,
		o.data_source,
		o.create_time,
		o.create_by,
		o.opty_name,
		x.X_TOTAL_AMOUNT,
		o.org_tree,
		x.x_last_acc_name,
		x.customer,
		o.report_status,
		o.current_status,
		x.active_count,
		x.crm_customer_code
		from s_opty o
		left join s_opty_x x on o.id = x.id
		left join s_month_report s
		on s.opty_id = o.id and s.business_type = 'monthReport' and
		s.report_month = #{month,jdbcType=VARCHAR}
		where o.is_deleted = 0
		and o.data_source = 'iChannel'
		and o.opty_status = 'Renewing'
		and s.month_report_detail is null
		and (o.current_status is null or o.current_status in ('','underway'))
		and o.opty_code like 'XS%'
		and x.crm_customer_code = #{crmCustomerCode,jdbcType=VARCHAR}
		<if test="!manageAuthFlag">
			and o.create_by = #{empNo, jdbcType=VARCHAR}
		</if>
		<if test="keyword != null and keyword != ''">
			and concat(ifnull(o.opty_code,''),'@',ifnull(o.opty_name,''),'@',ifnull(x.x_last_acc_name,'')) like concat('%',#{keyword, jdbcType=VARCHAR},'%')
		</if>
		order by o.create_time desc
	</select>

	<select id="getPrmRenewingHighWinRateOpptyIds" resultType="java.lang.String">
		WITH tp AS (
		SELECT p_id,
		json_value(success_rate, '$[0].value') success_rate,
		row_number() OVER (PARTITION BY p_id ORDER BY last_modified_time DESC ) AS rn
		FROM s_opty_product
		WHERE is_deleted = 0
		AND json_length(success_rate) != 0
		)
		select s.id
		from s_opty s
		join tp on s.id = tp.p_id
		where s.is_deleted = 0
		and s.opty_status = 'Renewing'
		and tp.success_rate in ('signable', 'effective')
		AND tp.rn = 1
		and s.data_source = 'PRM'
		<if test="null != rowIds and rowIds.size() > 0">
			and s.id in (
			<foreach collection="rowIds" item="item" index="index" separator=",">
				#{item,jdbcType=VARCHAR}
			</foreach>)
		</if>
		<if test="createdTimeStart != null and createdTimeStart != ''">
			<![CDATA[and s.create_time >= #{createdTimeStart}]]>
		</if>
		<if test="createdTimeEnd != null and createdTimeEnd != ''">
			<![CDATA[ and s.create_time <= #{createdTimeEnd}]]>
		</if>
	</select>

	<update id="updateStatusByRowId">
		UPDATE s_opty
		SET opty_status = #{status}
		WHERE id = #{rowId}
		and is_deleted = 0
	</update>

	<update id="softDeleteDraft" >
		UPDATE s_opty
		SET is_deleted = 1
		WHERE
			id = #{rowId, jdbcType=VARCHAR}
		  and opty_status = 'Draft'
		  and is_deleted = 0
	</update>

	<update id="invalidApprovalRecord" parameterType="com.zte.mcrm.channel.model.entity.ComApprovalRecord" >
		UPDATE com_approval_record
		<set>
			<if test="lastUpdatedBy != null and lastUpdatedBy != ''">last_updated_by = #{lastUpdatedBy, jdbcType=CHAR} ,</if>
			enabled_flag = 'N',
			last_updated_date = NOW()
		</set>
		WHERE
		work_flow_instance_id =#{workFlowInstanceId, jdbcType=VARCHAR}
		and business_id = #{businessId, jdbcType=VARCHAR}
		and enabled_flag = 'Y'
	</update>

	<select id="getPrmOpportunityByIds" resultMap="PrmExtMap">
		SELECT
		o.id ,
		o.opty_code ,
		o.opty_status ,
		o.data_source ,
		o.create_time ,
		o.create_by ,
		o.opty_name ,
		o.org_tree ,
		o.for_bid_time,
		JSON_VALUE(tm.employee, '$[0].empUIID') business_manager_id,
		JSON_UNQUOTE(JSON_VALUE(tm.employee, '$[0].empName')) business_manager_name,
		r.work_flow_instance_id,
		o.report_status,
		o.current_status,
		x.x_total_amount,
		x.active_count,
		x.final_acnt_sanctioned_code,
		x.acnt_sanctioned_code
		from s_opty o
		left join s_opty_x x on x.id = o.id
		left join com_approval_record r on r.business_id = o.id and r.enabled_flag = 'Y'
		left join s_opty_team tm on o.id = tm.p_id AND tm.is_deleted = 0 AND JSON_VALUE(tm.role, '$[0].value') = 50
		where o.is_deleted = 0
		and o.data_source in ('PRM','iChannel')
		and o.id in
		<foreach collection="ids" item="id" open="(" separator="," close=")" >
			#{id}
		</foreach>
	</select>

	<select id="getPrmOpportunityByFlowIds" resultType="string">
		SELECT o.id
		from s_opty o, com_approval_record r
		where r.business_id = o.id and r.enabled_flag = 'Y'
		and o.is_deleted = 0
		and o.data_source in ('PRM','iChannel')
		<choose>
			<when test="ids == null or ids.size() == 0">
				and 1=2
			</when>
			<otherwise>
				and r.work_flow_instance_id in
				<foreach collection="ids" item="id" open="(" separator="," close=")" >
					#{id}
				</foreach>
			</otherwise>
		</choose>
	</select>
</mapper>
