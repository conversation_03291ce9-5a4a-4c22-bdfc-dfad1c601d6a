package com.zte.mcrm.channel.service.prm;

import com.zte.mcrm.channel.model.entity.ComApprovalRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface IComApprovalRecordService {


    /**
     * 查询列表
     * @param paramMap 查询条件
     * @return 实体集合
     */
    List<ComApprovalRecord> getList(Map<String, Object> paramMap);

    /**
     * 根据业务id查询
     * @param businessId 主键
     * @return 实体
     */
    ComApprovalRecord getByBusinessId(@Param("businessId")String  businessId);

    /**
     * 根据主键查询
     * @param rowId 主键
     * @return 实体
     */
    ComApprovalRecord get(@Param("rowId") Long rowId);

    /**
     * 软删除，enabled_flag字段更新为N
     * @param rowId 主键
     * @return 删除总数
     */
    int softDelete(@Param("rowId") Long rowId);

    /**
     * 删除
     * @param rowId 主键
     * @return 删除总数
     */
    int delete(@Param("rowId") Long rowId);


    /**
     * 根据流程id和业务Id失效记录
     * @return
     */
    int softDeleteByBusinessIdAndFlowId(@Param("businessId")String businessId,@Param("flowId")String workFlowInstanceId);

    /**
     * 动态新增
     * @param comApprovalRecord 新增实体
     * @return 新增总数
     */
    int insert(ComApprovalRecord comApprovalRecord);

    /**
     * 动态新增-2
     * @param comApprovalRecord 新增实体
     * @return 新增总数
     */
    int insertAny(ComApprovalRecord comApprovalRecord);

    /**
     * 更新
     * @param comApprovalRecordEntity 更新条件
     * @return 更新影响总数
     */
    int update(ComApprovalRecord comApprovalRecordEntity);


    /**
     * 批量新增
     * @param comApprovalRecords 新增实体集合
     * @return 新增总数
     */
    int insertByBatch(List<ComApprovalRecord> comApprovalRecords);


    /**
     * 统计
     * @param paramMap 查询条件
     * @return 统计总数
     */
    long getCount(Map<String, Object> paramMap);
    /**
     * 获取流程实例Id
     * @param businessId 业务Id
     * @return 流程实例Id
     */
    String queryFlowInstance(String businessId);

    /**
     * 分页查询
     * @param paramMap 查询条件
     * @return 实体集合
     */
    List<ComApprovalRecord> getPage(Map<String, Object> paramMap);

}
