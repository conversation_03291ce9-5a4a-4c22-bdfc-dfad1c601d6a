package com.zte.mcrm.adapter.model.vo;

import com.zte.mcrm.adapter.model.dto.RoleDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/16
 */
@Data
public class ChannelAccountInfo {

    @ApiModelProperty(value = "渠道商编号")
    private String customerId;

    @ApiModelProperty(value = "渠道商名称")
    private String channelName;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "用户个人姓名")
    private String personName;

    @ApiModelProperty(value = "手机号码")
    private String phone;

    @ApiModelProperty(value = "邮箱地址")
    private String email;

    @ApiModelProperty(value = "角色")
    private List<RoleDTO> roleList;

}
