package com.zte.leadinfo.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@TableName(value = "cx_doc_item")
@Data
public class CxDocItemDO implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(value = "ROW_ID")
    private String rowId;

    /**
     *
     */
    @TableField(value = "FILE_NAME")
    private String fileName;

    /**
     *
     */
    @TableField(value = "FILE_SIZE")
    private String fileSize;

    /**
     * 0加密，1不加密
     */
    @TableField(value = "ENCRYPT")
    private String encrypt;

    /**
     *
     */
    @TableField(value = "BILL_CODE")
    private String billCode;

    /**
     *
     */
    @TableField(value = "DOC_ID")
    private String docId;

    /**
     *
     */
    @TableField(value = "COMMENTS")
    private String comments;

    /**
     * 是否有效
     */
    @TableField(value = "ACTIVE_FLAG")
    private String activeFlag;

    /**
     *
     */
    @TableField(value = "FILE_TYPE")
    private String fileType;

    /**
     *
     */
    @TableField(value = "CREATED")
    private Date created;

    /**
     *
     */
    @TableField(value = "CREATED_BY")
    private String createdBy;

    /**
     *
     */
    @TableField(value = "LAST_UPD")
    private Date lastUpd;

    /**
     *
     */
    @TableField(value = "LAST_UPD_BY")
    private String lastUpdBy;
}