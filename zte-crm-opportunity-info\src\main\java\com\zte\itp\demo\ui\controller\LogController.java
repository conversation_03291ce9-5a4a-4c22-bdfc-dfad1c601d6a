/**
 * 
 */
package com.zte.itp.demo.ui.controller;

import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.util.string.LogHelper;
import com.zte.springbootframe.util.string.LogModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 *
 */
@RestController  
@RequestMapping("/log")  
public class LogController {  
    private final Logger logger = LoggerFactory.getLogger(this.getClass());  
      
    @Autowired
    MessageSource messageSource;

    @RequestMapping(value = "/writelog", method = {RequestMethod.POST, RequestMethod.GET})
    public Object writeLog() throws Exception
    {  
        LogModel lm=new LogModel();
        try
        {
            lm.setBusinessType("writeLog");
            lm.setLevel("info");
            lm.setStatus(messageSource.getMessage("log.status.normal",null,LocaleContextHolder.getLocale()));
            lm.setExtend1(messageSource.getMessage("log.behavior.analysis.test.log",null,LocaleContextHolder.getLocale())+"This is an info message");
	        logger.info(LogHelper.formatterLog(lm));  
        }
        catch(Exception ex)
        {
            lm.setBusinessType("writeLog");
            lm.setLevel("error");
            lm.setStatus(messageSource.getMessage("abnormal.operation",null,LocaleContextHolder.getLocale()));
            lm.setExtend1(ex.toString());
            logger.error(LogHelper.formatterLog(lm));
            throw new BusiException();
        }
        return "OK";  
    } 
}