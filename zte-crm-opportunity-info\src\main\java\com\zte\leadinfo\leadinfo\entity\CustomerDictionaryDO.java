package com.zte.leadinfo.leadinfo.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.mcrm.common.consts.CommonConst;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import java.util.stream.Collectors;

@TableName("customer_dictionary")
@Data
public class CustomerDictionaryDO implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 入参：可能是ID，也可能是code---作为主键
     */
    @TableId(value = "input_param")
    private String inputParam;

    /**
     * 转换属性
     */
    @TableField(value = "customer_id")
    private String customerId;


    /**
     * 状态，异常情况下为0，不为0则为正常
     */
    @TableField(value = "status")
    private String status;


    /**
     * 异常描述
     */
    @TableField(value = "error_message")
    private String errorMessage;

    @TableField(value = "update_time")
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;


    public boolean filterStatus() {
        return StringUtils.equalsIgnoreCase(CommonConst.CODE_0, status);
    }

    public void buildSaveParam(Map<String, String> idMap, String key, String errorMsg) {
        this.setInputParam(key);
        this.setUpdateTime(new Date());
        if (idMap.containsKey(key)) {
            this.setCustomerId(idMap.get(key));
            this.setStatus(CommonConst.CODE_1);
        } else if (idMap.containsValue(key)) {
            this.setCustomerId(idMap.entrySet()
                    .stream()
                    .filter(entry -> entry.getValue().equals(key))
                    .map(Map.Entry::getKey).collect(Collectors.toList()).get(0));
            this.setStatus(CommonConst.CODE_1);
        } else {
            this.setStatus(CommonConst.CODE_0);
            this.setErrorMessage(errorMsg);
        }
    }
}
