package com.zte.mcrm.channel.constant;

/**
 * <AUTHOR>
 * @date 2021/10/11
 */
public enum ApprovalOpinionEnum {
    /**
     * 通过
     */
    Y("Y","通过"),
    /**
     * 通过
     */
    N("N","不通过");

    private String descEn;
    private String descCn;

    public String getDescEn() {
        return descEn;
    }

    public String getDescCn() {
        return descCn;
    }

    ApprovalOpinionEnum(String descEn, String descCn) {
        this.descEn = descEn;
        this.descCn = descCn;
    }

    /**根据英文获取中文
     * @param descEn
     * @return
     */
    public static String getDescCnByEn(String descEn){
        for (ApprovalOpinionEnum e : ApprovalOpinionEnum.values()) {
            if (e.getDescEn().equalsIgnoreCase(descEn)) {
                return e.getDescCn();
            }
        }
        return  "";
    }

}
