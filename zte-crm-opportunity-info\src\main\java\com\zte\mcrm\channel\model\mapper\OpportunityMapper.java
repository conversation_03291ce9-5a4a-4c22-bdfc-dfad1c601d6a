package com.zte.mcrm.channel.model.mapper;

import com.zte.mcrm.channel.model.entity.Opportunity;
import com.zte.mcrm.channel.model.vo.OpportunityAddVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2021/10/19
 */
@Mapper
public interface OpportunityMapper {
    OpportunityMapper INSTANCE = Mappers.getMapper(OpportunityMapper.class);

    /**
     * PrmOpportunityAddVO 转 Opportunity
     * @param opportunityAddVO
     * @return
     */
    Opportunity transOpportunityAddVOToOpportunity(OpportunityAddVO opportunityAddVO);

    /**
     * Opportunity 转 PrmOpportunityAddVO
     * @param opportunity
     * @return
     */
    OpportunityAddVO transOpportunityToOpportunityAddVO(Opportunity opportunity);
}
