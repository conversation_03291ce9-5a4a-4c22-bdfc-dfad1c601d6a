package com.zte.mcrm.common.util;

import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.common.consts.CommonConst;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2020/4/14 11:08
 */
public class FormDataHelpUtil {

    /**
     * 获取当前页
     *
     * @param formData
     * @return
     */
    public static int getPageNum(FormData formData) {
        return formData.getPage() > 0 ? Long.valueOf(formData.getPage()).intValue()
                : CommonConst.DEFAULT_PAGE_NUM;
    }

    /**
     * 获取页面记录数
     *
     * @param formData
     * @return
     */
    public static int getPageSize(FormData formData) {
        return formData.getPage() > 0 ? Long.valueOf(formData.getRows()).intValue()
                : CommonConst.DEFAULT_PAGE_SIZE;
    }

    /**
     * 获取分页查询map参数
     *
     * <AUTHOR>
     * @date 2021年4月12日 下午3:59:35
     * @param form
     * @return
     */
    public static Map<String, Object> getPageQueryMap(FormData form) {
        Map<String, Object> map = EntityTransformUtils.toMapParams(form);
        int pageSize = getPageSize(form);
        long startRow = (long)(getPageNum(form) - 1) * (long)pageSize;
        map.put("startRow", startRow);
        map.put("rowSize", pageSize);
        return map;
    }

    /**
     * 获取分页查询结果
     *
     * <AUTHOR>
     * @date 2021年4月12日 下午3:59:57
     * @param form
     * @param total
     * @param result
     * @return
     */
    public static <T> PageRows<T> getPageRowsResult(FormData<T> form, long total,
                                                    List<T> result) {

        PageRows<T> page = new PageRows<>();
        page.setCurrent(form.getPage());
        page.setTotal(total);
        page.setRows(result);
        return page;
    }

    /**
     * 判断对象中属性值是否全为空
     * @param object
     * @return
     */
    public static boolean checkObjAllFieldsIsNull(Object object) {
        if (null == object) {
            return true;
        }
        try {
            for (Field f : object.getClass().getDeclaredFields()) {
                f.setAccessible(true);
                if (f.get(object) != null && StringUtils.isNotBlank(f.get(object).toString())) {
                    return false;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }
}
