package com.zte.mcrm.channel.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.channel.constant.SourceOfOpportunityEnum;
import com.zte.mcrm.channel.dao.OpportunityDao;
import com.zte.mcrm.channel.model.dto.ImportPdmDTO;
import com.zte.mcrm.channel.model.entity.Opportunity;
import com.zte.mcrm.channel.model.entity.OpportunityProduct;
import com.zte.mcrm.channel.service.channel.IOpportunityProductService;
import com.zte.opty.sync.util.CommonMapUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class ImportPdmListener implements ReadListener<ImportPdmDTO> {

    private final OpportunityDao opportunityDao;

    private final IOpportunityProductService iOpportunityProductService;

    /**
     * 如果使用了spring,请使用这个构造方法。每次创建Listener的时候需要把spring管理的类传进来
     *
     * @param opportunityDao
     */
    public ImportPdmListener(OpportunityDao opportunityDao, IOpportunityProductService iOpportunityProductService) {
        this.opportunityDao = opportunityDao;
        this.iOpportunityProductService = iOpportunityProductService;
    }

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param importPdmDTO one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(ImportPdmDTO importPdmDTO, AnalysisContext context) {
        try {
            log.info("解析到一条数据:{}", JSON.toJSONString(importPdmDTO));
            // 获取商机主键
            Opportunity opportunity = opportunityDao.getByOptyCd(importPdmDTO.getOptyCd());
            if (BeanUtil.isEmpty(opportunity)) {
                log.info("获取商机主键失败:{},{}", importPdmDTO.getOptyCd(), JSON.toJSONString(importPdmDTO));
                return;
            }
            opportunity.setBusinessTypeCd(CommonMapUtil.DELIVERY_INTERFACE_MAP.inverse().getOrDefault(opportunity.getBusinessTypeCd(), opportunity.getBusinessTypeCd()));
            // 获取政企产品信息主键
            Map<String, Object> params = Maps.newHashMap();
            params.put(OpportunityConstant.OPPTY_ID, opportunity.getRowId());
            params.put(OpportunityConstant.BUSINESS_TYPE, OpportunityConstant.NEW_OPPORTUNITY);
            params.put(OpportunityConstant.PROD_LV2_NAME, importPdmDTO.getProdLvName());
            List<OpportunityProduct> govOpportunityProductList = iOpportunityProductService.getList(params);
            if (CollUtil.isEmpty(govOpportunityProductList)) {
                log.info("获取政企产品信息主键失败:{},{}", JSON.toJSONString(params), JSON.toJSONString(importPdmDTO));
                return;
            }
            // 逻辑删除公司主产品
            params.clear();
            params.put(OpportunityConstant.OPPTY_ID, opportunity.getRowId());
            params.put(OpportunityConstant.BUSINESS_TYPE, OpportunityConstant.PDM_PROD);
            String parProdId = govOpportunityProductList.get(0).getRowId();
            params.put(OpportunityConstant.PAR_PROD_ID, parProdId);
            List<OpportunityProduct> pdmOpportunityProductList = iOpportunityProductService.getList(params);
            Optional.ofNullable(pdmOpportunityProductList).map(pdmList -> pdmList.stream().map(OpportunityProduct::getRowId).collect(Collectors.toList()))
                    .filter(CollUtil::isNotEmpty).ifPresent(iOpportunityProductService::batchSoftDelete);
            // 写入公司主产品
            OpportunityProduct opportunityProduct = new OpportunityProduct();
            BeanUtils.copyProperties(importPdmDTO, opportunityProduct);
            opportunityProduct.setOpptyId(opportunity.getRowId());
            opportunityProduct.setDataSource(SourceOfOpportunityEnum.ZTE_SELF_BUILT.getValue());
            opportunityProduct.setBusinessType(OpportunityConstant.PDM_PROD);
            opportunityProduct.setParProdId(parProdId);
            iOpportunityProductService.insert(opportunityProduct);
        } catch (Exception e) {
            log.error("公司主产品导入失败！{}", importPdmDTO);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // doAfterAllAnalysed
    }
}