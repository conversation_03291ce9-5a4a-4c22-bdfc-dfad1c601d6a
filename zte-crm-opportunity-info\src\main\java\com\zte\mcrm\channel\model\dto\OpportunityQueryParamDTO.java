package com.zte.mcrm.channel.model.dto;

import com.zte.mcrm.channel.model.vo.OpportunityQueryParamVO;
import com.zte.mcrm.common.consts.CommonConst;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 描述：
 * 创建时间：2021/9/22
 *
 * @author：王丹凤6396000572
 */
@Data
public class OpportunityQueryParamDTO {

    /**
     * 中兴业务经理工号，不为空时不返回赢单状态的商机
     */
    @ApiModelProperty(value="中兴业务经理工号" ,example="中兴业务经理工号")
    private String shortNo;
    /**
     * 渠道商客户编码
     */
    @ApiModelProperty(value="渠道商客户编码" ,example="渠道商客户编码")
    private String crmCustomerCode;
    /**
     * 渠道商名称（非必填）,模糊匹配
     */
    private String channelName;
    /**
     * 商机编号（非必填）,模糊匹配
     */
    private String optyCd;
    /**
     * 商机名称（非必填）,模糊匹配
     */
    private String optyName;

    /**
     * 政企中国营销事业部组织id
     */
    private String governmentAndEnterpriseChinaOrgId;

    /**
     * 当前页
     */
    @ApiModelProperty(value="当前页" ,required=true ,example="1")
    private int currentPage = 1;
    /**
     * 页大小
     */
    @ApiModelProperty(value="页大小" ,required=true ,example="10")
    private int pageSize = 10;

    @ApiModelProperty(value = "商机状态")
    private List<String> statusCd;

    @ApiModelProperty(value = "项目所属部门")
    private String deptNo;

    @ApiModelProperty(value = "最终用户行业")
    private String finalCustomerChildTrade;

    @ApiModelProperty(value = "报备开始时间")
    private String createdTimeStart;

    @ApiModelProperty(value = "报备结束时间")
    private String createdTimeEnd;

    @ApiModelProperty(value = "商机编号")
    private List<String> optyCdList;

    @ApiModelProperty(value = "商机来源")
    private String dataSource;

    public OpportunityQueryParamVO toOpportunityQueryParamVO()
    {
        OpportunityQueryParamVO vo = new OpportunityQueryParamVO();
        vo.setShortNo(this.getShortNo());
        vo.setChannelName(this.getChannelName());
        vo.setOptyCd(this.getOptyCd());
        vo.setOptyName(this.getOptyName());
        vo.setCrmCustomerCode(this.getCrmCustomerCode());
        int page = this.getCurrentPage()>0?this.getCurrentPage():CommonConst.DEFAULT_PAGE_NUM;
        int pageSize = this.getPageSize()>0?this.getPageSize():CommonConst.DEFAULT_PAGE_SIZE;
        int startIndex = (page-1)*pageSize;
        vo.setPageSize(pageSize);
        vo.setStartIndex(startIndex);
        vo.setStatusCd(this.getStatusCd());
        vo.setDeptNo(this.getDeptNo());
        vo.setFinalCustomerChildTrade(this.getFinalCustomerChildTrade());
        vo.setCreatedTimeStart(this.getCreatedTimeStart());
        vo.setCreatedTimeEnd(this.getCreatedTimeEnd());
        vo.setOptyCdList(this.getOptyCdList());
        vo.setDataSource(this.getDataSource());
        vo.setCurrentPage(page);
        return vo;
    }


}
