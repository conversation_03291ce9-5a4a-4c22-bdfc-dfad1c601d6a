package com.zte.aiagent.infrastruction.access.repository;

import com.zte.aiagent.common.util.DateTimeUtils;
import com.zte.aiagent.domain.aggregate.BidDocument;
import com.zte.aiagent.domain.repository.BidDocumentRepository;
import com.zte.aiagent.domain.valueobject.DocumentInfo;
import com.zte.aiagent.domain.enums.ParseStatusEnum;
import com.zte.aiagent.domain.shared.valueobject.AuditInfo;
import com.zte.aiagent.domain.shared.valueobject.Tenant;
import com.zte.aiagent.domain.valueobject.ExportParsedFileKey;
import com.zte.itp.msa.core.model.FormData;
import com.zte.aiagent.infrastruction.access.converter.BidDocumentConverter;
import com.zte.aiagent.infrastruction.access.mapper.BidDocumentMapper;
import com.zte.aiagent.infrastruction.access.po.BidDocumentPO;
import com.zte.aiagent.ui.dto.request.BidDocumentPageQueryDTO;
import com.zte.aiagent.ui.dto.vo.BidDocumentVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * 招标文档仓储实现
 * 负责文档聚合的持久化和重建
 */
@Repository
@Slf4j
public class BidDocumentRepositoryImpl implements BidDocumentRepository {

    @Resource
    private BidDocumentMapper bidDocumentMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(BidDocument bidDocument) {
        BidDocumentPO po = convertToDataObject(bidDocument);

        if (exists(bidDocument.getDocumentId())) {
            bidDocumentMapper.updateByPrimaryKey(po);
            log.info("更新文档记录成功 documentId={}", bidDocument.getDocumentId());
        } else {
            bidDocumentMapper.insert(po);
            log.info("插入文档记录成功 documentId={}", bidDocument.getDocumentId());
        }
    }

    @Override
    public Optional<BidDocument> findById(String documentId) {
        BidDocumentPO po = bidDocumentMapper.selectByPrimaryKey(documentId);
        if (po == null) {
            return Optional.empty();
        }
        return Optional.of(convertToDomainObject(po));
    }

    @Override
    public Optional<BidDocument> findByIdAndTenant(String documentId, Tenant tenantId) {
        BidDocumentPO po = bidDocumentMapper.selectByPrimaryKey(documentId);
        if (po == null || !tenantId.getValue().equals(po.getTenantId())) {
            return Optional.empty();
        }
        return Optional.of(convertToDomainObject(po));
    }

    @Override
    public boolean exists(String documentId) {
        BidDocumentPO po = bidDocumentMapper.selectByPrimaryKey(documentId);
        return po != null;
    }

    /**
     * 将领域对象转换为持久化对象
     */
    private BidDocumentPO convertToDataObject(BidDocument bidDocument) {
        BidDocumentPO po = new BidDocumentPO();
        po.setRowId(bidDocument.getDocumentId());
        po.setFileName(bidDocument.getDocumentInfo().getFileName());
        po.setFileSize(bidDocument.getDocumentInfo().getFileSize());
        po.setFileType(bidDocument.getDocumentInfo().getFileType());
        po.setFileKey(bidDocument.getDocumentInfo().getFileKey());
        po.setParseTemplateCode(bidDocument.getParseTemplateCode());
        po.setParseTemplateId(bidDocument.getParseTemplateId());

        // 枚举转换为字符串存储
        po.setParseStatus(bidDocument.getParseStatus().getCode());

        // 时间转换
        po.setParseStartTime(DateTimeUtils.toDate(bidDocument.getParseStartTime()));
        po.setParseEndTime(DateTimeUtils.toDate(bidDocument.getParseEndTime()));

        // 审计信息
        po.setCreatedBy(bidDocument.getAuditInfo().getCreatedBy());
        po.setCreatedDate(DateTimeUtils.toDate(bidDocument.getAuditInfo().getCreatedDate()));
        po.setLastUpdatedBy(bidDocument.getAuditInfo().getLastUpdatedBy());
        po.setLastUpdatedDate(DateTimeUtils.toDate(bidDocument.getAuditInfo().getLastUpdatedDate()));

        po.setEnabledFlag("Y");
        po.setTenantId(bidDocument.getTenantId().getValue());

        return po;
    }

    /**
     * 将持久化对象转换为领域对象
     * 使用包级别构造函数重建聚合根，避免反射
     */
    private BidDocument convertToDomainObject(BidDocumentPO po) {
        // 创建文档信息值对象
        DocumentInfo documentInfo = DocumentInfo.of(
            po.getFileName(),
            po.getFileSize(),
            po.getFileType(),
            po.getFileKey()
        );

        // 创建审计信息值对象
        AuditInfo auditInfo = AuditInfo.create(po.getCreatedBy());
        if (po.getLastUpdatedBy() != null && !po.getCreatedBy().equals(po.getLastUpdatedBy())) {
            auditInfo = auditInfo.updateBy(po.getLastUpdatedBy());
        }

        ExportParsedFileKey exportParsedFileKey = ExportParsedFileKey.of(
                po.getExportParsedExcelFileKey(),
                po.getExportParsedWordFileKey(),
                po.getYellowDocumentFileKey()
        );

        // 使用包级别构造函数直接重建聚合根
        return new BidDocument(
            po.getRowId(),
            documentInfo,
            po.getParseTemplateCode(),
            po.getParseTemplateId(),
            ParseStatusEnum.fromCode(po.getParseStatus()),
            DateTimeUtils.toLocalDateTime(po.getParseStartTime()),
            DateTimeUtils.toLocalDateTime(po.getParseEndTime()),
            auditInfo,
            Tenant.of(po.getTenantId()),
            exportParsedFileKey
        );
    }

    @Override
    public List<BidDocumentVO> selectPageList(FormData<BidDocumentPageQueryDTO> request) {
        List<BidDocumentPO> documentPOList = bidDocumentMapper.selectPageList(request);
        return BidDocumentConverter.INSTANCE.toVOList(documentPOList);
    }

    @Override
    public Long selectPageCount(FormData<BidDocumentPageQueryDTO> request) {
        return bidDocumentMapper.selectPageCount(request);
    }
}
