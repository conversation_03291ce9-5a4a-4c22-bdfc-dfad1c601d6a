package com.zte.mcrm.adapter.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ProductTreeData implements Serializable {
    /**
     * 产品编号
     */
    @ApiModelProperty("产品编号")
    private String itemNo;
    /**
     * 层级
     */
    @ApiModelProperty("产品层级")
    private Integer itemLevel;
    /**
     * 层级描述
     */
    @ApiModelProperty("产品层级描述")
    private String itemLevelDesc;
    /**
     * 中文名称
     */
    @ApiModelProperty("产品中文名称")
    private String cnName;
    /**
     * 英文名称
     */
    @ApiModelProperty("产品英文名称")
    private String enName;
    /**
     * 英文缩写
     */
    @ApiModelProperty("英文缩写")
    private String enNameAb;
    /**
     * 是否渠道
     */
    @ApiModelProperty("是否渠道")
    private String isChannel;
    /**
     * 状态：10:启用/20:禁用/30:删除
     */
    @ApiModelProperty("状态中文描述")
    private String status;
    /**
     * 状态：10:ABLE /20: DISABLED /30: DELETE
     */
    @ApiModelProperty("状态英文描述")
    private String statusEn;
    /**
     * 状态：10/20/30
     */
    @ApiModelProperty("状态值")
    private Integer statusValue;
    /**
     * 产品全路径-中文名称
     */
    @ApiModelProperty("产品全路径-中文名称")
    private String pathCnName;
    /**
     * 产品全路径-英文名称
     */
    @ApiModelProperty("产品全路径-英文名称")
    private String pathEnName;
    /**
     * 产品子树
     */
    @ApiModelProperty("产品子树")
    private List<ProductTreeData> children;
}
