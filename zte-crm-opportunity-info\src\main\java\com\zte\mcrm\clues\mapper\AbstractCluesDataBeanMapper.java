package com.zte.mcrm.clues.mapper;

import org.springframework.beans.BeanUtils;

import com.zte.mcrm.clues.access.vo.BusinessClues;
import com.zte.mcrm.clues.model.CluesSaveDTO;

/**
 * 本类提供线索前后台对象转化方法
 * <AUTHOR>
 *
 */
public abstract class AbstractCluesDataBeanMapper {
	
	/**
	 * 线索DTO转为VO
	 * @param from
	 * @return
	 */
	public static BusinessClues dtoToVo (CluesSaveDTO from){
		if(from==null){ 
			return null;
		}
		BusinessClues to = new BusinessClues();
		BeanUtils.copyProperties(from, to);
		return to;
		
	}

}
