
package com.zte.mcrm.adapter.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@Setter
@Getter
@ToString
public class CompanyOrgInfoDTO {

    @ApiModelProperty(value="实收注册资金")
    private String actualCapital;
    @ApiModelProperty(value="实收注册资本币种 人民币 美元 欧元 等")
    private String actualCapitalCurrency;
    @ApiModelProperty(value="股票名")
    private String bondName;
    @ApiModelProperty(value="股票号")
    private String bondNum;
    @ApiModelProperty(value="股票类型")
    private String bondType;
    @ApiModelProperty(value="城市")
    private String city;
    @ApiModelProperty(value="城市Code")
    private String cityCode;
    @ApiModelProperty(value="国家/地区")
    private String country;
    @ApiModelProperty(value="国家/地区Code")
    private String countryCode;
    @ApiModelProperty(value="统一社会信用代码")
    private String creditCode;
    @ApiModelProperty(value="区")
    private String district;
    @ApiModelProperty(value="区Code")
    private String districtCode;
    @ApiModelProperty(value="企业邮箱")
    private String email;
    @ApiModelProperty(value="经营结束时间")
    private Date endTime;
    @ApiModelProperty(value="成立日期（注册日期）")
    private Date establishTime;
    @ApiModelProperty(value="证件号")
    private String idNumber;
    @ApiModelProperty(value="证件类型")
    private String idType;
    @ApiModelProperty(value="国民经济行业分类门类")
    private String industryCategory;
    @ApiModelProperty(value="行业门类code")
    private String industryCategoryCode;
    @ApiModelProperty(value="参保人数")
    private String insuredCount;
    @ApiModelProperty(value="是否核验")
    private String isChecked;
    @ApiModelProperty(value="N-国内 Y-国际")
    private String isInternational;
    @ApiModelProperty(value="是否小微企业")
    private String isMicroEnt;
    @ApiModelProperty(value="N-没监控 Y-被监控")
    private String isMonitored;
    @ApiModelProperty(value="法人")
    private String legalPersonName;
    @ApiModelProperty(value="法人类型，1：人，2：公司")
    private Long legalPersonType;
    @ApiModelProperty(value="组织名称")
    private String name;
    @ApiModelProperty(value="组织英文名称")
    private String nameEn;
    @ApiModelProperty(value="曾用名列表")
    private List<String> orgHistoryNames;
    @ApiModelProperty(value="组织机构代码")
    private String orgNumber;
    @ApiModelProperty(value="企业类型")
    private String orgType;
    @ApiModelProperty(value="企业统一代码")
    private String orgUnifiedCode;
    @ApiModelProperty(value="其他不常用字段")
    private String otherExtendFields;
    @ApiModelProperty(value="企业联系方式")
    private String phoneNumber;
    @ApiModelProperty(value="省/州")
    private String province;
    @ApiModelProperty(value="省/州Code")
    private String provinceCode;
    @ApiModelProperty(value="注册资本")
    private String regCapital;
    @ApiModelProperty(value="注册资本币种")
    private String regCapitalCurrency;
    @ApiModelProperty(value="登记机关")
    private String regInstitute;
    @ApiModelProperty(value="注册地址")
    private String regLocation;
    @ApiModelProperty(value="企业状态（经营状态）")
    private String regStatus;
    @ApiModelProperty(value="企业状态（经营状态）")
    private String scanDate;
    @ApiModelProperty(value="评分")
    private String score;
    @ApiModelProperty(value="人员规模")
    private String staffNumRange;
    @ApiModelProperty(value="在职类型快码")
    private String staffType;
    @ApiModelProperty(value="经营开始时间")
    private Date startTime;
    @ApiModelProperty(value="股票曾用名")
    private String usedBondName;
    @ApiModelProperty(value="网址")
    private String website;

}
