package com.zte.mcrm.common.upload.controller;

import com.zte.itp.msa.core.config.ICommonConfig;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.clouddisk.service.DocCloudService;
import com.zte.mcrm.common.exception.BusinessRuntimeException;
import com.zte.mcrm.common.upload.constants.UploadRetCode;
import com.zte.mcrm.common.upload.service.UploadFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.http.entity.mime.content.ByteArrayBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

@Api(tags = "附件管理")
@RestController
@RequestMapping("/fileManager/public/")
public class PublicFileManageController {
    public static final int QUERY_SIZE = 20;
    public static final String ONCE_TIME = "1";
    private static final Logger LOGGER = LoggerFactory.getLogger(PublicFileManageController.class);

    @Autowired
    private UploadFileService uploadFileService;
    @Autowired
    private DocCloudService docCloudService;
    @Autowired
    private ICommonConfig commonConfig;

    @ApiOperation("下载公共图片-支持批量下载")
    @GetMapping("img/{dmeKey}")
    public void downloadImg(@PathVariable("dmeKey") String dmeKey, HttpServletResponse response) throws Exception {
        if(!uploadFileService.isPublicImg(dmeKey)){
            throw new BusinessRuntimeException(RetCode.BUSINESSERROR_CODE, UploadRetCode.NO_PERMISSION_DOWNLOAD);
        }
        // 系统编码
        String sysCode = commonConfig.getTokenMap().get(SysGlobalConst.SYS_CODE);
        ByteArrayBody file = docCloudService.download(dmeKey, sysCode);
        if (file == null) {
            return;
        }
        response.setHeader("Content-type", "application/octet-stream");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + file.getFilename());
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        ServletOutputStream outputStream = response.getOutputStream();
        file.writeTo(outputStream);
        outputStream.flush();
        outputStream.close();
    }

    @ApiOperation("下载公共附件-支持批量下载")
    @GetMapping("file/{dmeKey}")
    public void downloadFile(@PathVariable("dmeKey") String dmeKey,  HttpServletResponse response) throws Exception {
        if(!uploadFileService.isPublicFile(dmeKey)){
            throw new BusinessRuntimeException(RetCode.BUSINESSERROR_CODE, UploadRetCode.NO_PERMISSION_DOWNLOAD);
        }
        // 系统编码
        String sysCode = commonConfig.getTokenMap().get(SysGlobalConst.SYS_CODE);
        ByteArrayBody file = docCloudService.download(dmeKey, sysCode);
        if (file == null) {
            return;
        }
        response.setHeader("Content-type", "application/octet-stream");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + file.getFilename());
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        ServletOutputStream outputStream = response.getOutputStream();
        file.writeTo(outputStream);
        outputStream.flush();
        outputStream.close();
    }

    @ApiOperation("公共文档预览-返回预览路径")
    @GetMapping("/getPreviewFile")
    public ServiceData<String> getPreviewFile(@RequestParam String docKey, @RequestParam String fileName){
        if(!uploadFileService.isPublicFile(docKey)){
            throw new BusinessRuntimeException(RetCode.BUSINESSERROR_CODE, UploadRetCode.NO_PERMISSION_PREVIEW);
        }
        ServiceData<String> result = new ServiceData<>();

        result.setBo(docCloudService.previewFile(docKey,fileName));
        return result;
    }

}
