package com.zte.mcrm.common.errorcode.enums;


/**
 * 注册（register ）	10	R
 * 认证（certificate）	20	C
 * 业绩（achievement）	30	A
 * 激励（encourage）	40	E
 * 门户（portal）	50	P
 * 基础综合（base）	60	B
 *
 * @Author: <EMAIL>
 * @Date: 2022/01/06
 * @Description:
 */
public enum ModuleEnum {
    /**
     * 业绩
     */

    A("30", "achievement", "业绩"),
    /**
     * 基础综合
     */
    B("60", "base", "基础综合"),
    /**
     * 认证
     */
    C("20", "certificate", "认证"),
    /**
     * 激励
     */
    E("30", "encourage", "激励"),
    /**
     * 门户
     */
    P("30", "portal", "门户"),
    /**
     * 认证
     */
    R("10", "register", "注册"),
    /**
     * 政企评审
     */
    G("", "reviewAndDecision", "评审决策"),
    /**
     * 商机
     */
    O("", "opportunity", "商机");

    private String oldCode;
    private String en;
    private String zh;

    ModuleEnum(String oldCode, String en, String zh) {
        this.oldCode = oldCode;
        this.en = en;
        this.zh = zh;
    }

    public String getOldCode() {
        return oldCode;
    }

    public String getEn() {
        return en;
    }

    public String getZh() {
        return zh;
    }
}