package com.zte.mcrm.clues.business.service;

import com.zte.itp.msa.core.locale.LocaleMessageSourceBean;
import com.zte.mcrm.clues.access.dao.CluesSaveDao;
import com.zte.mcrm.clues.access.vo.BusinessClues;
import com.zte.mcrm.clues.common.CluesSysConst;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.model.PersonAndOrgInfoVO;
import com.zte.mcrm.common.util.OrganizationUtil;
import com.zte.mcrm.common.util.RowIdUtil;
import com.zte.mcrm.lov.business.service.LovService;
import com.zte.mcrm.number.business.service.NumberService;
import com.zte.mcrm.opportunity.business.service.PCBusinessOpporunityService;
import com.zte.mcrm.opportunity.common.OppSysConst;
import com.zte.springbootframe.common.exception.BusiException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/****
 *
 * <AUTHOR> @date 2021/2/5
 **/
@Service
public class CluesSaveServiceImpl implements CluesSaveService{
	@Autowired
	private NumberService numberService;
	@Autowired
	private CluesSaveDao cluesSaveDao;
	@Autowired
	private LovService lovService;
	@Autowired
	private PCBusinessOpporunityService pcBusinessOpporunityService;
	@Autowired
	private LocaleMessageSourceBean localeMessageSourceBean;

	@Override
	@Transactional(rollbackFor = BusiException.class)
	public BusinessClues save(BusinessClues vo) throws BusiException{
		try {
			String empId = vo.getxEmpNo();
			vo.setEmpId(empId);
            //创建人
			vo.setCreatedBy(empId);
		} catch (Exception e) {
			throw new BusiException("", localeMessageSourceBean.getMessage(CommonConst.USER_NOT_FOUND) + vo.getxEmpNo());
		}
		vo.setId(RowIdUtil.generateRowId());
		if(vo.getClueNum()==null||vo.getClueNum().isEmpty()){
			//如果为空，生成编码，不为空的话，前端需要传生成的编码过来
			vo.setClueNum(numberService.generateClueCode());
		}
		String postType = vo.getPostType();
		if(CommonConst.SUBMIT.equals(postType)){
			//如果是提交，判断归属客户经理是否为空，不为空：设置状态为待客户经理更新；为空：状态为待分配
			if(vo.getOwnerMgr()==null || vo.getOwnerMgr().isEmpty()){
				vo.setStatusCode("Assigning");
			}else{
				vo.setStatusCode("Renewing");
			}
		}else{
			//如果是保存，状态为草稿
			vo.setStatusCode("Draft");
		}
		if("".equals(vo.getOwnerMgr())) {
			vo.setOwnerMgr(null);
		}
		vo.setDatasource(OppSysConst.DATASOURCE_PC);
		// 部门ORG编码
        this.setOrgCodeClue(vo);
        //主表插入数据
		cluesSaveDao.saveInfoInMainTable(vo);
        //扩展表插入数据
		cluesSaveDao.saveInfoInExTable(vo);
		
		try {
            //将值列表类型为Code的转化为ID
			vo = setLovMessageOfVo(vo);
		} catch (Exception e) { }
		return vo;
	}

    @Override
    public BusinessClues setOrgCodeClue(BusinessClues vo) throws BusiException {
	    if(StringUtils.isBlank(vo.getDeptId())){
	        return vo;
        }
        PersonAndOrgInfoVO personAndOrgInfoVO = OrganizationUtil.getOrgByOrgNo(vo.getDeptId());
	    if(Objects.isNull(personAndOrgInfoVO)){
            return vo;
        }
        vo.setOrgCode(personAndOrgInfoVO.getHrOrgID());
        vo.setOrgCodePath(personAndOrgInfoVO.getOrgIDPath());
        return vo;
    }

    public BusinessClues setLovMessageOfVo(BusinessClues vo) throws Exception{
        // 客户类型id
        vo.setAcctTypeId(lovService.codeToId(CluesSysConst.ZTE_OPPTY_TYPE, vo.getAcctTypeCode(),null));
        String acctTypeId = vo.getAcctTypeId();
        // 状态
        vo.setStatus(lovService.codeToId(CluesSysConst.ZTE_LEAD_STATUS, vo.getStatusCode(),null));
        // 业务范围Id
        vo.setBusinessTypeId(lovService.codeToId(CluesSysConst.ZTE_OPPTY_BUS_TYPE, vo.getBusinessTypeCode(),null));
        // 销售模式Id
        vo.setSaleModelId(lovService.codeToId(CluesSysConst.ZTE_OPTY_SALES, vo.getSaleModelCode(),acctTypeId));
        // 线索来源id
        vo.setClueSourceId(lovService.codeToId(CluesSysConst.ZTE_OPPORTUNITY_SOURCE, pcBusinessOpporunityService.optySouceCodeToNewCode(CluesSysConst.ZTE_OPPORTUNITY_SOURCE, vo.getClueSourceCode()),null));
        // 行业id
        vo.setParentTradeId(lovService.codeToId(CluesSysConst.ZTE_PARENT_TRADE, vo.getParentTradeCode(),null));
        // 子行业id
        vo.setChildTradeId(lovService.codeToId(CluesSysConst.ZTE_CHILD_TRADE, vo.getChildTradeCode(),null));
        // 关闭原因Id
        vo.setReasonId(lovService.codeToId(CluesSysConst.ZTE_LEAD_CLOSED_REASON, vo.getReasonCode(),null));
		return vo;
	}
	

	@Override
	@Transactional(rollbackFor = BusiException.class)
	public void update(BusinessClues businessClues) throws BusiException {
		try {
			String empId =businessClues.getxEmpNo();
			businessClues.setEmpId(empId);
		} catch (Exception e) {
			throw new BusiException("", localeMessageSourceBean.getMessage(CommonConst.USER_NOT_FOUND) + businessClues.getxEmpNo());
		} 
		String clueNum = businessClues.getClueNum();
		businessClues.setClueNum(clueNum);
		if(CluesSysConst.POST_TYPE_SUBMIT.equals(businessClues.getPostType())){
			// 线索状态（待分配，草稿，被退回）
			if(StringUtils.isBlank(businessClues.getStatusCode()) 
					|| CluesSysConst.STATUS_CODE_ASSIGNING.equals(businessClues.getStatusCode())
					|| CluesSysConst.STATUS_CODE_DRAFT.equals(businessClues.getStatusCode())
					|| CluesSysConst.STATUS_CODE_REFUSED.equals(businessClues.getStatusCode())){
				//如果是提交，判断归属客户经理是否为空，不为空：设置状态为待客户经理更新；为空：状态为待分配
				if(businessClues.getOwnerMgr()==null || businessClues.getOwnerMgr().isEmpty()){
					businessClues.setStatusCode(CluesSysConst.STATUS_CODE_ASSIGNING);
				}else{
					businessClues.setStatusCode("Renewing");
				}
			}
		}
        // 部门ORG编码
        this.setOrgCodeClue(businessClues);
        //主表更新数据
		cluesSaveDao.updateInfoInMainTable(businessClues);
        //扩展表更新数据
		cluesSaveDao.updateInfoInExTable(businessClues);
        //更新金额，金额做单独处理
		cluesSaveDao.updateAmountInExTable(businessClues);
	}

}
