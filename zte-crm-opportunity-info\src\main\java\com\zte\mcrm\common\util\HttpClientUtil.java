package com.zte.mcrm.common.util;


import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.mcrm.adapter.clouddisk.service.CloudDiskBaseService;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.*;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.ByteArrayBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

/**
 * HttpClient工具类，含get,post,put,delete方法
 *
 * <AUTHOR>
 */
public class HttpClientUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(HttpClientUtil.class);
    private static final int INT_200 = 200;
    private static final int INT_201 = 201;
    private static final String STR1 = "?";
    private static final String STR2 = "&";

    private static volatile CloseableHttpClient closeableHttpClient;

    public static CloseableHttpClient createHttpClient() {
        if (closeableHttpClient == null) {
            synchronized (HttpClientUtil.class) {
                if (closeableHttpClient == null) {
                    closeableHttpClient = (CloseableHttpClient) SpringContextUtil.getBean("closeableHttpClient");
                }
            }
        }
        return closeableHttpClient;
    }

    /**
     * get方式请求，获取file文件
     *
     * @param url
     * @param paramsMap
     * @param headerParamsMap 传入的header参数
     * @return
     * <AUTHOR>
     */
    public static ByteArrayBody httpGetFile(String url, Map<String, String> paramsMap, Map<String, String> headerParamsMap,
                                 RequestConfig requestConfig) {
        // 数据必填项校验
        if (StringUtils.isBlank(url)) {
            throw new RuntimeException("url can't be empty");
        }
        String baseUrl;
        if (paramsMap != null) {
            baseUrl = getUrlFromParam(url, paramsMap);
        } else {
            baseUrl = url;
        }

        CloseableHttpClient httpClient = createHttpClient();
        HttpGet httpGet = new HttpGet(baseUrl);
        setHeader(httpGet, headerParamsMap, requestConfig);
        CloseableHttpResponse res = null;
        try {
            if (httpClient != null) {
                res = httpClient.execute(httpGet);
                HttpEntity httpEntity = res.getEntity();
                if (res.getStatusLine().getStatusCode() != INT_200) {
                    throw new RuntimeException("获取文件失败");
                }
                if(null != httpEntity){
                    //获取返回的输入流
                    InputStream inputStream = httpEntity.getContent();
                    byte[]bytes = IOUtils.toByteArray(inputStream);
                    String fileName = CloudDiskBaseService.getDownloadFileName(res);
                    return new ByteArrayBody(bytes, fileName);
                }
            }
        } catch (IOException e) {
            String errorMsg = url + ":httpGetFile connect failed";
            throwsRouteException(errorMsg, e, "GET_CONNECT_FAILED");
        } finally {
            if (res != null) {
                try {
                    res.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }


    public static String httpPostWithFile(String url, MultipartFile file, Map<String, String> headerParamsMap) {
        // 数据必填项校验
        if (StringUtils.isBlank(url)) {
            throw new RuntimeException("url can't be empty");
        }
        String result = null;
        CloseableHttpClient httpClient = createHttpClient();
        HttpPost httpPost = new HttpPost(url);
        setFileHeader(httpPost, headerParamsMap, null);
        CloseableHttpResponse res = null;
        try {
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            //builder.setCharset(StandardCharsets.UTF_8);
            builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
            //FileBody fileBody = new FileBody(file, ContentType.MULTIPART_FORM_DATA);
            //builder.addPart("file", fileBody);
            builder.addBinaryBody(
                    "file",
                    file.getInputStream(),
                    ContentType.create(file.getContentType()),
                    file.getOriginalFilename()
            );
            httpPost.setEntity(builder.build());
            if (httpClient != null) {
                res = httpClient.execute(httpPost);
                result = EntityUtils.toString(res.getEntity());
                if (res.getStatusLine().getStatusCode() != INT_200 && res.getStatusLine().getStatusCode() != INT_201) {
                    throw new RuntimeException(result);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error(e.getMessage());
        } finally {
            if (res != null) {
                try {
                    res.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return result;
    }



    private static String getUrlFromParam(String url, Map<String, String> paramsMap) {
        String baseUrl;
        List params = new ArrayList();
        for (Map.Entry<String, String> entry : paramsMap.entrySet()) {
            String key = entry.getKey();
            Object objValue = entry.getValue();
            String value = objValue == null ? "" : objValue.toString();
            params.add(new BasicNameValuePair(key, value));
        }
        if (params.size() > 0) {
            if (url.indexOf(STR1) > -1) {
                baseUrl = url + STR2 + URLEncodedUtils.format(params, "UTF-8");
            } else {
                baseUrl = url + STR1 + URLEncodedUtils.format(params, "UTF-8");
            }
        } else {
            baseUrl = url;
        }
        return baseUrl;
    }


    private static void throwsRouteException(String errorMsg, Exception e, String errorCode) {
        String msg = errorMsg + ".errorCode:" + errorCode + ",errorMsg:" + e.getMessage();
        LOGGER.error(msg, e);
        throw new RuntimeException(msg);
    }

    private static void setHeader(HttpRequestBase httpRequestBase, Map<String, String> headerParamsMap,
                                  RequestConfig requestConfig) {
        httpRequestBase.addHeader("Content-type", "application/json; charset=utf-8");
        httpRequestBase.setHeader("Accept", "application/json");
        if (requestConfig != null) {
            httpRequestBase.setConfig(requestConfig);
        }
        // 传入的header参数
        if (null != headerParamsMap) {
            for (Map.Entry<String, String> entry : headerParamsMap.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                httpRequestBase.setHeader(key, value);
            }
        }
    }


    /**
     * 发送文件的时候，千万不要设置Content-type，不然会报错，让request自己设置
     * @param httpRequestBase
     * @param headerParamsMap
     * @param requestConfig
     */
    private static void setFileHeader(HttpRequestBase httpRequestBase, Map<String, String> headerParamsMap,
                                      RequestConfig requestConfig){
        setCommonHeader(httpRequestBase,headerParamsMap,requestConfig);
    }
    private static void setCommonHeader(HttpRequestBase httpRequestBase, Map<String, String> headerParamsMap,
                                        RequestConfig requestConfig){
        if (requestConfig != null) {
            httpRequestBase.setConfig(requestConfig);
        }
        // 传入的header参数
        if (null != headerParamsMap) {
            for (Map.Entry<String, String> entry : headerParamsMap.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                httpRequestBase.setHeader(key, value);
            }
        }
    }
}
