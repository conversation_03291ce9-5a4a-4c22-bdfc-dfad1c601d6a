package com.zte.mcrm.adapter.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/* Started by AICoder, pid:88ebecc7a5b24522a50fbe711c22276a */
@ApiModel(description = "客户受限制主体信息模型")
@Getter
@Setter
public class ComplianceInfo {

    @ApiModelProperty(value = "受限制主体状态。embargo-禁运国，pending-待确认，yes-受限制主体，no-非受限制主体，空串或null-表示空的暂无结果", example = "no")
    private String sanctionedParty;

}
/* Ended by AICoder, pid:88ebecc7a5b24522a50fbe711c22276a */
