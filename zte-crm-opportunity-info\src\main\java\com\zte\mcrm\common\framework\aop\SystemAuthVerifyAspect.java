package com.zte.mcrm.common.framework.aop;

import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.common.annotation.SystemAuthVerify;
import com.zte.mcrm.common.framework.config.SystemAuthVerifyProperty;
import com.zte.mcrm.common.model.SysGlobalConstVo;
import com.zte.mcrm.common.util.ApprovalHeaderUtil;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.springbootframe.util.local.LocalMessageUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.joda.time.DateTimeUtils;
import org.joda.time.Duration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Aspect
@Order(2)
public class SystemAuthVerifyAspect {

    private static final Logger logger = LoggerFactory.getLogger(SystemAuthVerifyAspect.class);

    @Autowired
    private SystemAuthVerifyProperty systemAuthVerifyProperty;

    @Around(value = "execution(* com.zte.mcrm.channel.controller.*.*.*(..))")
    public Object processAuthority(ProceedingJoinPoint point) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) point.getSignature();
        Method method = methodSignature.getMethod();
        SystemAuthVerify systemAuthVerify = method.getAnnotation(SystemAuthVerify.class);
        Object[] args = point.getArgs();

        if (null != systemAuthVerify && systemAuthVerifyProperty.isEnable()) {
            ServiceData failResult = new ServiceData();
            failResult.setCode(new RetCode(RetCode.AUTHFAILED_CODE, RetCode.AUTHFAILED_MSGID));
            List<String> serviceNamesWhitelist = Arrays.asList(systemAuthVerify.serviceNamesWhitelist());
            SysGlobalConstVo sysGlobalConstVo = CommonUtils.getSysGlobalConstVo();
            if (StringUtils.isBlank(sysGlobalConstVo.getxOriginServiceName())) {
                // serviceName 缺失
                failResult.setBo(LocalMessageUtils.getMessage("serviceName.miss"));
                return failResult;
            }
            if (CollectionUtils.isNotEmpty(serviceNamesWhitelist) && serviceNamesWhitelist.contains(sysGlobalConstVo.getxOriginServiceName())){
                // 白名单服务不拦截
                return point.proceed(args);
            }
            boolean tokenMissing = StringUtils.isBlank(sysGlobalConstVo.getAuthNotice())
                    || StringUtils.isBlank(sysGlobalConstVo.getTimeStampStr())
                    || StringUtils.isBlank(sysGlobalConstVo.getAuthAccessKey())
                    || StringUtils.isBlank(sysGlobalConstVo.getAuthSignature());
            boolean tokenPass = false;
            if (!tokenMissing) {
                tokenPass = tokenVerify(sysGlobalConstVo);
            }
            if (tokenMissing || !tokenPass) {
                // 签名校验不通过
                failResult.setBo(LocalMessageUtils.getMessage("signature.verification.failed"));
                return failResult;
            }
        }
        return point.proceed(args);

    }

    private boolean tokenVerify(SysGlobalConstVo sysGlobalConstVo){
        Map<String, String> secretKeyMap = systemAuthVerifyProperty.getSystemSecretKeyMap();
        if (null == secretKeyMap){
            logger.error("未配置SecretKey,默认不拦截");
            return true;
        }
        long timestamp;
        try {
            timestamp = Long.parseLong(sysGlobalConstVo.getTimeStampStr());
            long currentTime = ApprovalHeaderUtil.getTimeStamp();
            // 2分钟内有效
            if (currentTime > timestamp + DateTimeUtils.getDurationMillis(Duration.standardMinutes(OpportunityConstant.MAX_TIMESTAMP_GAP))){
                logger.error("timeStamp超过有效期");
                return false;
            }
        }catch (NumberFormatException e){
            logger.error("输入的timeStamp非法", e);
            return false;
        }
        String secretKey = secretKeyMap.get(sysGlobalConstVo.getAuthAccessKey());
        if (StringUtils.isBlank(secretKey)) {
            return false;
        }
        String signature = ApprovalHeaderUtil.getSignature(sysGlobalConstVo.getAuthNotice(),
                timestamp, sysGlobalConstVo.getAuthAccessKey(), secretKey);
        if(StringUtils.isBlank(signature)){
            return false;
        }
        return signature.equals(sysGlobalConstVo.getAuthSignature());
    }
}
