package com.zte.mcrm.channel.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class PrmOpportunityPendingDTO {

    @ApiModelProperty(value = "关键字")
    private String keyWord;

    @ApiModelProperty(value = "报备时间_起始时间")
    private String minCreateDate;

    @ApiModelProperty(value = "报备时间_结束时间")
    private String maxCreateDate;

    @ApiModelProperty(value = "商机状态")
    private String statusCd;
}
