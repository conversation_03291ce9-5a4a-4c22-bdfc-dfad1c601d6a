package com.zte.mcrm.channel.service.channel;

import com.zte.mcrm.channel.model.dto.OpportunityInfoDTO;
import com.zte.mcrm.channel.model.entity.Opportunity;
import com.zte.mcrm.channel.model.entity.OpportunityDetail;
import com.zte.opty.model.bo.SOptyTeamBO;

import java.util.List;

public interface IOpportunityTeamService {

    /**
     * 更新或插入数据
     * @param insertOpportunity
     * @param opportunityDetail
     */
    List<SOptyTeamBO> insertOrUpdate(Opportunity insertOpportunity, OpportunityDetail opportunityDetail);
}
