package com.zte.aiagent.domain.event;

import com.zte.aiagent.common.util.IdUtils;
import com.zte.aiagent.domain.valueobject.DocumentInfo;
import com.zte.aiagent.domain.shared.event.DomainEvent;
import lombok.Getter;
import com.zte.aiagent.common.enums.EventTypeEnum;

import java.time.LocalDateTime;

/**
 * 文档创建事件
 * 当新文档被创建时发布
 */
@Getter
public class DocumentCreatedEvent implements DomainEvent {

    private final String eventId;
    private final String documentId;
    private final DocumentInfo documentInfo;
    private final String parseTemplateCode;
    private final Long tenantId;
    private final LocalDateTime occurredOn;

    public DocumentCreatedEvent(String documentId,
                               DocumentInfo documentInfo,
                               String parseTemplateCode,
                               Long tenantId) {
        this.eventId = IdUtils.generateNewId();
        this.documentId = documentId;
        this.documentInfo = documentInfo;
        this.parseTemplateCode = parseTemplateCode;
        this.tenantId = tenantId;
        this.occurredOn = LocalDateTime.now();
    }

    @Override
    public String getAggregateId() {
        return this.documentId;
    }

    @Override
    public String getEventType() {
        return EventTypeEnum.DOCUMENT_CREATED.getCode();
    }
}
