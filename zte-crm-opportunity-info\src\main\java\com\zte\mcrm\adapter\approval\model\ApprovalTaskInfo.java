package com.zte.mcrm.adapter.approval.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 审批任务信息
 * @author: 10243305
 * @date: 2021/7/23 下午3:58
 */
@Data
public class ApprovalTaskInfo {
    @ApiModelProperty(value = "流程实例id")
    private String flowInstanceId;
    @ApiModelProperty(value = "审批任务id")
    private String taskId;
    @ApiModelProperty(value = "审批人")
    private String approver;
    @ApiModelProperty(value = "创建时间")
    private String createDate;
}
