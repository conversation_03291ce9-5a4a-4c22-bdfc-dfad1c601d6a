package com.zte.mcrm.channel.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Setter
@Getter
@ToString
public class OpportunityQueryDTO {

    private String rowId;

    @ApiModelProperty(value = "渠道商客户编码")
    private String crmCustomerCode;

    @ApiModelProperty(value = "关键字")
    private String keyword;

    @ApiModelProperty(value = "起始时间")
    private String minDate;

    @ApiModelProperty(value = "结束时间")
    private String maxDate;

    @ApiModelProperty(value = "商机状态")
    private List<String> statusCds;

    @ApiModelProperty(value = "商机来源")
    private List<String> dataSources;

    @ApiModelProperty(value = "授权状态")
    private List<String> statusAuths;

    @ApiModelProperty(value = "月报状态")
    private List<String> statusReports;
}
