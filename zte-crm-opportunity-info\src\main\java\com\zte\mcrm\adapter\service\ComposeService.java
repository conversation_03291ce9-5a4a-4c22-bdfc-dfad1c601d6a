package com.zte.mcrm.adapter.service;

import com.zte.mcrm.adapter.model.dto.AccountDetailQueryDTO;
import com.zte.mcrm.adapter.model.dto.ChannelAccountDetailDTO;
import com.zte.mcrm.adapter.model.dto.ChannelCustomerRes;
import com.zte.mcrm.adapter.model.vo.AccountDetail;
import com.zte.mcrm.adapter.model.vo.ChannelAccountInfo;

/**
 * <AUTHOR>
 * @date 2021/9/16
 */
public interface ComposeService {
    /**
     * 查询渠道账号对应的渠道信息
     * @param accountId
     * @return ChannelAccountInfo
     */
    ChannelAccountDetailDTO getChannelAccountBasicInfo(String accountId) throws Exception;
    /**
     * 查询渠道账号对应的渠道信息
     * @param accountId
     * @return ChannelAccountInfo
     */
    ChannelAccountInfo getChannelAccountInfo(String accountId) throws Exception;

    ChannelCustomerRes getChannelInfo(String crmCustomerCode) throws Exception;

    AccountDetail getAccountDetailFromUcs(AccountDetailQueryDTO accountDetailQueryDTO);
    /**
     * 根据accountId查询账号详情
     * @param accountId
     * @return AccountDetail
     */
    AccountDetail getAccountDetail(String accountId) throws Exception;
}
