package com.zte.crm.eva.base.domain.universal;

import com.zte.crm.eva.base.common.constant.universal.FrontEndUniveralConsts;
import com.zte.crm.eva.base.common.constant.universal.UniversalSqlConstants;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-04-20
 */
@Data
public class CommonTableMap {
    /**
     * 表别名
     */
    private String tableNickName;
    /**
     * 真实库名+表名
     */
    private String tableRealName;
    /**
     * 表字段
     */
    private List<TableField> tableFields;

    public void checkAttributes() {
        if (StringUtils.isBlank(tableRealName) && CollectionUtils.isEmpty(tableFields)) {
            throw new IllegalStateException("tableRealName and tableFields don't exist!");
        }
        Optional<TableField> tableFieldAccordingToNickName = getTableFieldAccordingToNickName(FrontEndUniveralConsts.COMMON_ID_NAME);
        if (!tableFieldAccordingToNickName.isPresent()) {
            throw new IllegalStateException("field about id doesn't exist!");
        }
    }

    /**
     * 转换成数据库字段
     * @param map
     * @return
     */
    public Map<String, Object> getTransferParam(Map<String, Object> map) {
        Map<String, Object> transferMap = new HashMap<>(16);
        if (map == null || map.isEmpty()) {
            return transferMap;
        }
        for (TableField tableField : tableFields) {
            if (map.containsKey(tableField.getFieldNickName())) {
                transferMap.put(tableField.getFieldRealName(), map.get(tableField.getFieldNickName()));
            }
        }
        return transferMap;
    }

    /**
     * 根据nickname返回字段定义
     * @param nickName
     * @return
     */
    public Optional<TableField> getTableFieldAccordingToNickName(String nickName) {
        if (StringUtils.isBlank(nickName)) {
            return Optional.empty();
        }
        for (TableField tableField : tableFields) {
            if (Objects.equals(nickName, tableField.getFieldNickName())) {
                return Optional.of(tableField);
            }
        }
        return Optional.empty();
    }

    /**
     * 构建select查询子句
     * @return
     */
    public String buildSelectSql() {
        Map<String, String> nameMap = tableFields.stream().collect(Collectors.toMap(TableField::getFieldRealName, TableField::getFieldNickName, (key1, key2) -> key2));
        StringBuilder queryField = new StringBuilder();
        String queryFieldStr = StringUtils.EMPTY;
        if (nameMap != null && nameMap.size() > 0) {
            for (Map.Entry<String, String> entry : nameMap.entrySet()) {
                queryField.append(entry.getKey()).append(UniversalSqlConstants.AS).append(entry.getValue()).append(",");
            }
            queryFieldStr = queryField.substring(0, queryField.length() - 1);
        }
        return queryFieldStr;
    }
}