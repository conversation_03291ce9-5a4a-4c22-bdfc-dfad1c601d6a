package com.zte.mcrm.clues.business.service;

import com.zte.mcrm.clues.access.vo.BusinessClues;
import com.zte.mcrm.clues.access.vo.BusinessCluesInfoVO;
import com.zte.mcrm.clues.model.CluesToOptyDTO;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.common.model.ServiceDataCopy;
import com.zte.springbootframe.util.page.PageQuery;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;

import java.util.List;
import java.util.Map;

/****
 *
 * <AUTHOR> @date 2021/1/22
 **/
public interface PCBusinessCluesService {
	/**
	 * 带权限查线索
	 * 
	 * @param pageQuery
	 * @return
	 * @throws Exception
	 */
	List<BusinessClues> getCluesWithAuth(PageQuery<BusinessClues> pageQuery)throws Exception;
	/**
	 * 查询线索总数
	 * @param pageQuery
	 * @return
	 * @throws Exception
	 */
	int countClues(PageQuery<BusinessClues> pageQuery) throws Exception;

    /**
     * 带权限查询最近的线索
     * @param pageQuery
     * @param paramMap
     * @return
     * @throws Exception
     */
	List<BusinessClues> getRecentCluesWithAuth(PageQuery<BusinessClues> pageQuery,Map paramMap)throws Exception;

    /**
     * 查询线索详情
     * @param businessClues
     * @return
     * @throws BusiException
     */
	BusinessClues selectBaseInfo(BusinessClues businessClues) throws BusiException;
	
	/**
	 * 检查用户是否有权限查看线索，有的话返回true，无的话返回false
	 * @param empId
	 * @param clueNum
	 * @return
	 * @throws BusiException
	 */
	boolean checkHasAuthToReadClue(String empId,String clueNum) throws BusiException;

	/**
	 * 检查用户是否有权限认领线索，有的话返回true，无的话返回false
	 * @param businessClues
	 * @return
	 * @throws Exception
	 * @throws BusiException
	 */
	boolean checkClaimAuthWithClue(BusinessClues businessClues) throws BusiException;

	/**
	 * 检查用户是否有权限查看线索，有的话返回true，无的话返回false
	 * @param businessClues
	 * @return
	 * @throws Exception
	 * @throws BusiException
	 */
	boolean checkAssignedAuthWithClue(BusinessClues businessClues) throws BusiException;

    /**
     * 检查用户是否有权限查看线索，有的话返回true，无的话返回false
     * @param businessClues
     * @return
     * @throws BusiException
     * @throws RouteException
     */
	boolean checktransferAuthClue(BusinessClues businessClues) throws BusiException, RouteException;
	/**
	 * 检查用户是否有权限线索分配
	 * @param businessClues
	 * @return
	 * @throws Exception 
	 * @throws BusiException
	 */
	boolean checkAssignedAuthClue(BusinessClues businessClues) throws BusiException;
	
	/**
	 * 当前登录人是否为归属客户经理
	 * @param businessClues
	 * @return
	 * @throws Exception 
	 * @throws BusiException
	 */
	boolean isBelongCustomerMgr(BusinessClues businessClues) throws BusiException;
	
	/**
	 * 	线索状态是否为待客户经理更新
	 * @param businessClues
	 * @return
	 * @throws Exception 
	 * @throws BusiException
	 */
	boolean isLeadUpdate(BusinessClues businessClues) throws BusiException;

	/**
	 * 当前登录人是否为商机管理员
	 * @param businessClues
	 * @return
	 * @throws Exception 
	 * @throws BusiException
	 */
	boolean isOptyMgr(BusinessClues businessClues) throws BusiException;
	/**
	 * 线索分配-PC
	 * @param businessClues
	 * @return
	 * @throws BusiException
	 */
	void assignedSavePC(BusinessClues businessClues) throws BusiException;

    /**
     * 线索认领更新主表和扩展表
     * @param clue
     * @throws BusiException
     */
	void claim(BusinessClues clue) throws BusiException;

	/**
	 * 线索退回-PC
	 * @param clue
     * @throws BusiException
	 */
	void backClue(BusinessClues clue) throws BusiException;
	
	/**
	 * <p>线索初始化</p> 
	 * <p>Description: </p>  
	 * @param businessCluesInfoVO
	 * @return
	 * @throws BusiException  
	 * <AUTHOR> ZhaoShiGuang
	 * @date 2018年3月29日
	 */
	BusinessCluesInfoVO initBusinessCluesInfo(BusinessCluesInfoVO businessCluesInfoVO) throws BusiException;

    /**
     * 线索转商机
     * @param businessClues
     * @return
     * @throws Exception
     */
	String transferSave(BusinessClues businessClues) throws Exception;

    /**
     * PC线索转商机根据审批结果
     * @param businessClues
     * @return
     * @throws Exception
     */
	String transferFormApprove(BusinessClues businessClues) throws Exception;

	/**
	 * 线索关闭权限校验
	 * @param businessClues
	 * @return
	 * @throws BusiException
	 */
	boolean checkClosedAuthClue(BusinessClues businessClues) throws BusiException;
	/**
	 * 线索关闭
	 * @param businessClues
	 * @return
	 * @throws BusiException
	 */
	String closeSave(BusinessClues businessClues) throws BusiException;

	/**
	 * 线索还原权限校验
	 * @param businessClues
	 * @return
	 * @throws BusiException
	 */
	boolean checkRestoreAuthClue(BusinessClues businessClues) throws BusiException;
	/**
	 * 线索还原
	 * @param businessClues
	 * @return
	 * @throws BusiException
	 */
	String clueRestore(BusinessClues businessClues) throws BusiException;
	/**
	 * 线索删除权限校验
	 * @param businessClues
	 * @return
	 * @throws BusiException
	 * <AUTHOR>
	 * @createDate 2018-04-19
	 */
	boolean checkDeleteAuthClue(BusinessClues businessClues) throws BusiException;
	/**
	 * 删除线索
	 * @param businessClues
	 * @return
	 * @throws BusiException
	 * <AUTHOR>
	 * @createDate 2018-04-19
	 */
	boolean delteClue(BusinessClues businessClues) throws BusiException;
	
	/**
	 * 查询客户是否是禁运国黑名单里面
	 * @date 2019年7月29日 
	 * <AUTHOR>
	 * @param dto
	 * @return
	 */
	boolean queryForbiddon(CluesToOptyDTO dto);

	/***
	 * 查询我管理的线索计数
	 * @return
	 */
	int getMyClueCount();

	/****
	 * 查询我的所有线索 分页
	 * @methodName getBusinessCluesByPage
	 * @param pageQuery
	 * @return com.zte.springbootframe.common.model.ServiceDataCopy<java.util.List<com.zte.mcrm.clues.access.vo.BusinessClues>>
	 * <AUTHOR>
	 * @date 2021/2/2
     * @throws Exception
	**/
    ServiceDataCopy<List<BusinessClues>> getBusinessCluesByPage(PageQuery<BusinessClues> pageQuery) throws Exception;

    /****
     * 更新部门ORG编码
     * @methodName updateOrgCodePath
     * @param orgCode
     * @param clueNum
     * @return java.util.List<com.zte.mcrm.clues.access.vo.BusinessClues>
     * <AUTHOR>
     * @date 2021/2/3
     * @throws Exception
     **/
    List<BusinessClues> updateOrgCodePath(String orgCode, String clueNum) throws Exception;
}
