/**
 *  Copyright  2018 赵世光 All rights reserved.
 *  @author: 6011000197 
 *  @date: 2018年6月29日 下午3:55:06 
 */
package com.zte.mcrm.dynamic.business.service;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.zte.mcrm.common.model.DynamicMessage;
import com.zte.mcrm.common.util.RequestMessage;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.common.serviceregister.MicroServiceWithConfigUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.string.DateHelper;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;

/**  
 * <p>Title: DynamicInfoServiceimpl</p>  
 * <p>Description: </p>  
 * <AUTHOR> ZhaoShiGuang
 * @date 2018年6月29日  
 */
@Service
public class DynamicInfoServiceImpl implements DynamicInfoService {
	
    private final Logger logger = LoggerFactory.getLogger(this.getClass()); 
	
	
	/**
	 * 推送动态消息
	 * @param dynamicMessage
	 */
	public void pushDynamic(DynamicMessage dynamicMessage) {
		HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
		String lang =request.getHeader(SysGlobalConst.HTTP_HEADER_X_LANG_ID);
		String empShortNo = request.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
		Map<String,String> headerParamsMap = new HashMap<>(16);
		headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_TENANT_ID, "smartsales");
		headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_LANG_ID, lang);
		headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO, empShortNo);
		String url = "/dynamicInfo/save"; 
		try {
			
			try {
				logger.info("消息请求头:"+ JacksonJsonConverUtil.beanToJson(headerParamsMap));
				logger.info("消息体："+JacksonJsonConverUtil.beanToJson(dynamicMessage));
			} catch (RouteException e) {
				e.printStackTrace();
			}
			if(StringUtils.isNotBlank(dynamicMessage.getOperContent())) {
				MicroServiceWithConfigUtil.invokeServiceWithPostMethod("4", url, dynamicMessage, headerParamsMap);
			}
		} catch (BusiException e) {
			logger.error("（send dynamic message error）发送动态消息发生异常："+e.getMessage());
			e.printStackTrace();
		}
	}
	
	@Override
	public void sendMessage(String messageHeader,String message,String relationObjId,String relationObjCode) {
		String empNo = RequestMessage.getEmpNo();
		DynamicMessage dynamicMessage = new DynamicMessage();
		//信息类型为文本
		dynamicMessage.setFileType("01");
		//公开
		dynamicMessage.setInfoClass("01");
		//商机相关
		dynamicMessage.setInfoType("03");
		//设置不推送关联人员
		dynamicMessage.setInfoClassEmployee("N");
		//消息来源
		dynamicMessage.setInfoSourceFlag("pc");
		//消息头
		dynamicMessage.setInfoName(messageHeader);
		//消息体
		dynamicMessage.setOperContent(message);
		//关联对象ID
		dynamicMessage.setInfoId(relationObjId);
		//关联对象编码
		dynamicMessage.setInfoCode(relationObjCode);
		//操作人工号
		dynamicMessage.setOperNo(empNo);
		dynamicMessage.setOperNameNo(empNo);
		//设置为当前时间
		dynamicMessage.setOperTime(DateHelper.getCurrentTime());
		try {
			String params = JacksonJsonConverUtil.beanToJson(dynamicMessage);
			logger.info("动态消息json:"+params);
		} catch (RouteException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		//发送消息
		pushDynamic(dynamicMessage);
	}

}
