package com.zte.mcrm.channel.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.mcrm.channel.constant.ArbitrationTypeEnum;
import com.zte.mcrm.common.annotation.EmpName;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public class ApprovalDetail {


    @ApiModelProperty(value = "不通过原因，1：已有报备；2：不同意此报备；3：是受限制主体")
    private String failureReason;
    @ApiModelProperty(value = "审批意见")
    private String opinion;
    @ApiModelProperty(value = "审批结果")
    private String result;
    @EmpName
    @ApiModelProperty(value = "提交人")
    private String submitter;
    @ApiModelProperty(value = "TS审批单号")
    private String tsApprovalNo;
    @ApiModelProperty(value = "中兴业务经理")
    private String businessManagerId;
    @ApiModelProperty(value = "中兴业务经理")
    private String businessManagerName;
    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    @ApiModelProperty(value = "类似商机")
    private String similarOptyCd;

    @ApiModelProperty(value = "节点名称")
    public String nodeName;
    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getSimilarOptyCd() {
        return similarOptyCd;
    }

    public void setSimilarOptyCd(String similarOptyCd) {
        this.similarOptyCd = similarOptyCd;
    }

    public String getTsApprovalNo() {
        return tsApprovalNo;
    }

    public void setTsApprovalNo(String tsApprovalNo) {
        this.tsApprovalNo = tsApprovalNo;
    }

    public String getBusinessManagerId() {
        return businessManagerId;
    }

    public void setBusinessManagerId(String businessManagerId) {
        this.businessManagerId = businessManagerId;
    }

    public String getBusinessManagerName() {
        return businessManagerName;
    }

    public void setBusinessManagerName(String businessManagerName) {
        this.businessManagerName = businessManagerName;
    }

    public String getFailureReason() {
        return failureReason;
    }

    public void setFailureReason(Integer failureReason) {
        String failureReasonName = ArbitrationTypeEnum.getArbitrationTypeNameByCode(failureReason);
        this.failureReason= (failureReasonName==null? String.valueOf(failureReason) :failureReasonName);
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getSubmitter() {
        return submitter;
    }

    public void setSubmitter(String submitter) {
        this.submitter = submitter;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }
}
