<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.mcrm.clues.access.dao.BusinessCluesDao">

    <resultMap id="businessCluesMap" type="com.zte.mcrm.clues.access.vo.BusinessClues"></resultMap>

    <sql id="baseSql">
        T1.row_id as "id"
        ,T1.Lead_Num as "clueNum"
        ,T1.STATUS_CD as
        "statusCode"
        ,T2.LEAD_NAME as "clueName"
        ,T2.BUS_TYPE as
        "businessTypeCode"
        ,T1.BU_ID as "deptId"
        ,T2.OWNER_ID as "ownerMgr"
        ,T2.Currency_Id as "currency"
        ,T2.Lead_Amount as "investmentScaleOfAcct"
        ,T2.CONTRACT_PRICE as "predictSignAmt"
        ,T2.DATE_2 as "predictSignDate"
        ,T1.Accnt_Id as "acctId"
        ,T2.SALES_TYPE
        as "saleModelCode"
        ,T2.Lead_Source as "clueSourceCode"
        ,T2.NOTES_2 as "background"
        ,T1.CREATED_BY as "createdBy"
        ,t2.Lead_Type as "acctTypeCode"
        ,t2.CHILD_TRADE "childTradeCode"
        ,t2.PARENT_TRADE "parentTradeCode"
        ,t2.last_acc_id "lastAcctId"
        ,T4.NAME "lastAcctName"
        <!--,t3.attrib_48 "areaCode"-->
        ,t2.notes_4 "reasonCode"
        ,t1.MODIFICATION_NUM "version"
        ,t2.PROD_SYSTEM as "prodSystemId"
        ,t2.PROD_LINE_BIG as "bigProdcutLineId"
        ,t2.FUND_FLG foundFlgCode
        ,t2.ACCOUNT_ATTRIBUTE accountAttributeCode
        ,t2.POTENTIAL_MODEL potentialModelCode
    </sql>

    <select id="selectBaseInfo" parameterType="com.zte.mcrm.clues.access.vo.BusinessClues"
            resultType="com.zte.mcrm.clues.access.vo.BusinessClues">
        SELECT
        T1.row_id as "id"
        ,T1.Lead_Num as "clueNum"
        ,T1.STATUS_CD as
        "statusCode"
        ,T2.LEAD_NAME as "clueName"
        ,T2.BUS_TYPE as
        "businessTypeCode"
        ,T1.BU_ID as "deptId"
        ,T2.OWNER_ID as "ownerMgr"
        ,T2.Currency_Id as "currency"
        ,T2.Lead_Amount as "investmentScaleOfAcct"
        ,T2.CONTRACT_PRICE as "predictSignAmt"
        ,T2.DATE_2 as "predictSignDate"
        ,T1.Accnt_Id as "acctId"
        ,T2.SALES_TYPE
        as "saleModelCode"
        ,T2.Lead_Source as "clueSourceCode"
        ,T2.NOTES_2 as "background"
        ,T1.CREATED_BY as "createdBy"
        ,t2.Lead_Type as "acctTypeCode"
        ,t2.CHILD_TRADE "childTradeCode"
        ,t2.PARENT_TRADE "parentTradeCode"
        ,t2.last_acc_id "lastAcctId"
        ,T4.NAME "lastAcctName"
        <!--,t3.attrib_48 "areaCode"-->
        ,t2.notes_4 "reasonCode"
        ,t1.MODIFICATION_NUM "version"
        ,t2.PROD_SYSTEM as "prodSystemId"
        ,t2.PROD_LINE_BIG as "bigProdcutLineId"
        ,t2.FUND_FLG foundFlgCode
        ,t2.ACCOUNT_ATTRIBUTE accountAttributeCode
        ,t2.POTENTIAL_MODEL potentialModelCode
        FROM S_LEAD T1
        LEFT JOIN CX_LEAD_X T2 ON T1.ROW_ID = T2.ROW_ID
        LEFT JOIN s_org_ext_from_account T4 ON T2.LAST_ACC_ID = T4.ROW_ID
        WHERE T1.ENABLE_FLAG = 'Y'
        and T1.row_id = #{id}
        limit 1
    </select>
    <resultMap id="BusinessClues" type="com.zte.mcrm.clues.access.vo.BusinessClues"></resultMap>

    <select id="getCluesWithAuth" parameterType="com.zte.springbootframe.util.page.PageQuery"
            resultMap="BusinessClues">
        SELECT * FROM (
        (
        (
        SELECT
        t.row_id id,
        t.BU_ID deptId,
        t.STATUS_CD statusCode,
        t.lead_num clueNum,
        x.BUS_TYPE businessTypeCode,
        x.Currency_Id currency,
        x.CONTRACT_PRICE predictSignAmt,
        x.SALES_TYPE saleModelCode,
        x.lead_name clueName,
        x.owner_id ownerId,
        <!--st.last_name lastName-->
        FROM
        s_lead t,
        cx_lead_x x
        <!--LEFT JOIN s_contact st ON x.owner_id = st.row_id,-->
        left join s_org_ext_from_project org ON t.bu_id=org.name
        left join S_PARTY_RPT_REL p52 ON org.row_id=p52.sub_party_id
        <!--S_PARTY_PER p,-->
        <!--s_postn p3,-->
        <!--CX_POST_SECT p4-->
        WHERE t.ENABLE_FLAG = 'Y'
        AND t.row_id = x.row_id
        and p52.`party_id` IN
        <foreach collection="entity.auth.subOrgs" item="org"
                 open="(" close=")" separator=",">
            #{org}
        </foreach>
        <if test="null!=entity.clueMsg">
            and (upper(x.lead_name) like  upper(concat('%',#{entity.clueMsg},'%')) or upper(t.lead_num) like
            upper(concat('%',#{entity.clueMsg},'%'))
            <!--or upper(st.last_name) like upper('%${entity.clueMsg}%' )-->
            )
        </if>
        )
        UNION
        (
        SELECT
        t.row_id id,
        t.BU_ID deptId,
        t.STATUS_CD statusCode,
        t.lead_num clueNum,
        x.BUS_TYPE businessTypeCode,
        x.Currency_Id currency,
        x.CONTRACT_PRICE predictSignAmt,
        x.SALES_TYPE saleModelCode,
        x.lead_name clueName,
        x.owner_id ownerId,
        <!--st.last_name lastName-->
        FROM
        s_lead t,
        cx_lead_x x
        <!--LEFT JOIN s_contact st ON x.owner_id = st.row_id-->
        WHERE t.ENABLE_FLAG = 'Y'
        AND t.row_id = x.row_id
        AND (t.created_by = #{entity.empId} OR x.owner_id=#{entity.empId})
        <if test="null!=entity.clueMsg">
            and (upper(x.lead_name) like  upper(concat('%',#{entity.clueMsg},'%')) or upper(t.lead_num) like
            upper(concat('%',#{entity.clueMsg},'%'))
            <!--or upper(st.last_name) like upper('%${entity.clueMsg}%' )-->
            )
        </if>
        )
        ) )RE
        LIMIT #{start}, #{pageSize}
    </select>


    <select id="getAccountClues" parameterType="com.zte.springbootframe.util.page.PageQuery"
            resultMap="BusinessClues">
        select * from (
        (
        select t.row_id id
        , t.BU_ID deptId
        , t.STATUS_CD statusCode
        , t.lead_num clueNum
        , x.BUS_TYPE businessTypeCode
        , x.Currency_Id currency
        , x.CONTRACT_PRICE predictSignAmt
        , x.SALES_TYPE saleModelCode
        , x.lead_name clueName
        , x.owner_id ownerId
        <!--, st.last_name lastName-->
        from cx_lead_x x,s_lead t
        <!--left join s_contact st on x.owner_id = st.row_id-->
        left join s_org_ext_from_project org ON t.bu_id=org.name
        left join S_PARTY_RPT_REL p52 ON org.row_id=p52.sub_party_id
        <!--,S_PARTY_PER p-->
        <!--,s_postn p3-->
        <!--,CX_POST_SECT p4-->
        <choose>
            <when test="null!=entity.acctId">
                , S_ORG_EXT soe
            </when>
            <when test="null!=entity.acctNum">
                , S_ORG_EXT soe
            </when>
            <otherwise>
            </otherwise>
        </choose>
        where t.ENABLE_FLAG = 'Y' and t.row_id = x.row_id
        <if test="null!=entity.acctId">
            and t.accnt_id =soe.row_id
        </if>
        <if test="null!=entity.acctNum">
            and t.accnt_id =soe.row_id
        </if>
        <if test="entity.auth.subOrgs.size()>0 and entity.auth.subOrgs !=''">
            and p52.`party_id` IN
            <foreach collection="entity.auth.subOrgs" item="org"
                     open="(" close=")" separator=",">
                #{org}
            </foreach>
        </if>

        <if test="null!=entity.clueMsg">
            and (upper(x.lead_name) like upper(concat('%',#{entity.clueMsg},'%')) or upper(t.lead_num) like
            upper(concat('%',#{entity.clueMsg},'%'))
            <!--or upper(st.last_name) like upper('%${entity.clueMsg}%' )-->
            )
        </if>

        <if test="null!=entity.acctId">
            and (t.accnt_id = #{entity.acctId})
        </if>

        <if test="null!=entity.acctNum">
            and (soe.ou_num= #{entity.acctNum})
        </if>
        )
        union
        (
        select t.row_id id
        , t.BU_ID deptId
        , t.STATUS_CD statusCode
        , t.lead_num clueNum
        , x.BUS_TYPE businessTypeCode
        , x.Currency_Id currency
        , x.CONTRACT_PRICE predictSignAmt
        , x.SALES_TYPE saleModelCode
        , x.lead_name clueName
        , x.owner_id ownerId
        <!--, st.last_name lastName-->
        from s_lead t, cx_lead_x x
        <!--left join s_contact st on x.owner_id = st.row_id-->
        <choose>
            <when test="null!=entity.acctId">
                , S_ORG_EXT soe
            </when>
            <when test="null!=entity.acctNum">
                , S_ORG_EXT soe
            </when>
            <otherwise>
            </otherwise>
        </choose>
        where t.ENABLE_FLAG = 'Y' and t.row_id = x.row_id
        <if test="null!=entity.acctId">
            and t.accnt_id =soe.row_id
        </if>
        <if test="null!=entity.acctNum">
            and t.accnt_id =soe.row_id
        </if>
        <if test="null!=entity.clueMsg">
            and (upper(x.lead_name) like  upper(concat('%',#{entity.clueMsg},'%'))
            or upper(t.lead_num) like  upper(concat('%',#{entity.clueMsg},'%'))
            <!--or upper(st.last_name) like upper('%${entity.clueMsg}%' )-->
            )
        </if>

        <if test="null!=entity.acctId">
            and (t.accnt_id = #{entity.acctId})
        </if>

        <if test="null!=entity.acctNum">
            and (soe.ou_num= #{entity.acctNum})
        </if>
        and (t.created_by =#{entity.empId} or x.owner_id=#{entity.empId})
        )
        )re
        limit #{start}, #{pageSize}
    </select>


    <select id="accountCluesCount" parameterType="com.zte.springbootframe.util.page.PageQuery"
            resultType="int">
        select count(*)
        from (
        (
        SELECT
        t.row_id id
        FROM
        s_lead t
        left join s_org_ext_from_project org ON t.bu_id=org.name
        left join S_PARTY_RPT_REL p52 ON org.row_id=p52.sub_party_id,
        cx_lead_x x

        <choose>
            <when test="null!=entity.acctId">
                , s_org_ext_from_project soe
            </when>
            <when test="null!=entity.acctNum">
                , s_org_ext_from_project soe
            </when>
            <otherwise>
            </otherwise>
        </choose>
        where t.ENABLE_FLAG = 'Y' and t.row_id = x.row_id
        <if test="null!=entity.acctId and entity.acctId!=''">
            and t.accnt_id =soe.row_id
        </if>
        <if test="null!=entity.acctNum and entity.acctNum!=''">
            and t.accnt_id =soe.row_id
        </if>
        <if test="entity.auth.subOrgs.size()>0 and entity.auth.subOrgs!=''">
            and p52.`party_id` IN
            <foreach collection="entity.auth.subOrgs" item="org"
                     open="(" close=")" separator=",">
                #{org}
            </foreach>
        </if>
        <if test="null!=entity.clueMsg">
            and (upper(x.lead_name) like upper(concat('%',#{entity.clueMsg},'%')) or upper(t.lead_num) like
            upper(concat('%',#{entity.clueMsg},'%'))
            <!--or upper(st.last_name) like upper('%${entity.clueMsg}%' )-->
            )
        </if>

        <if test="null!=entity.acctId">
            and ( t.accnt_id =#{entity.acctId} )
        </if>

        <if test="null!=entity.acctNum">
            and (soe.ou_num= #{entity.acctNum})
        </if>
        )
        union
        (
        select t.row_id id
        from s_lead t,
        cx_lead_x x
        <!--left join s_contact st on x.owner_id = st.row_id-->
        <choose>
            <when test="null!=entity.acctId">
                , s_org_ext_from_project soe
            </when>
            <when test="null!=entity.acctNum">
                , s_org_ext_from_project soe
            </when>
            <otherwise>
            </otherwise>
        </choose>
        where t.ENABLE_FLAG = 'Y' and t.row_id = x.row_id
        <if test="null!=entity.acctId and entity.acctId!=''">
            and t.accnt_id =soe.row_id
        </if>
        <if test="null!=entity.acctNum and entity.acctNum!=''">
            and t.accnt_id =soe.row_id
        </if>
        <if test="null!=entity.clueMsg">
            and (upper(x.lead_name) like  upper(concat('%',#{entity.clueMsg},'%')) or upper(t.lead_num) like
            upper(concat('%',#{entity.clueMsg},'%'))
            <!--or upper(st.last_name) like upper('%${entity.clueMsg}%' )-->
            )
        </if>

        <if test="null!=entity.acctId">
            and ( t.accnt_id =#{entity.acctId} )
        </if>

        <if test="null!=entity.acctNum">
            and (soe.ou_num= #{entity.acctNum})
        </if>
        and (t.created_by =#{entity.empId} or x.owner_id=#{entity.empId})
        )
        ) re
    </select>


    <select id="countClues" parameterType="com.zte.springbootframe.util.page.PageQuery"
            resultType="int">
        select count(*)
        from (
        (
        SELECT
        t.row_id id
        FROM
        s_lead t,
        cx_lead_x x
        left join s_org_ext_from_project org ON t.bu_id=org.name
        left join S_PARTY_RPT_REL p52 ON org.row_id=p52.sub_party_id
        where 1=1
        and p52.`party_id` IN
        <foreach collection="entity.auth.subOrgs" item="org"
                 open="(" close=")" separator=",">
            #{org}
        </foreach>
        <if test="null!=entity.clueMsg">
            and (upper(x.lead_name) like  upper(concat('%',#{entity.clueMsg},'%')) or upper(t.lead_num) like
            upper(concat('%',#{entity.clueMsg},'%'))
            <!--or upper(st.last_name) like upper('%${entity.clueMsg}%' )-->
            )
        </if>
        )
        union
        (
        SELECT
        t.row_id id
        FROM
        s_lead t,
        cx_lead_x x
        <!--left join s_contact st on x.OWNER_ID = st.row_id-->
        where t.ENABLE_FLAG = 'Y'
        AND t.row_id = x.row_id
        AND (t.created_by = #{entity.empId} OR x.owner_id=#{entity.empId})
        <if test="null!=entity.clueMsg">
            and (upper(x.lead_name) like  upper(concat('%',#{entity.clueMsg},'%')) or upper(t.lead_num) like
            upper(concat('%',#{entity.clueMsg},'%'))
            <!--or upper(st.last_name) like upper('%${entity.clueMsg}%' )-->
            )
        </if>
        )
        ) re
    </select>

    <select id="selectClueVsersion" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT MODIFICATION_NUM FROM S_LEAD WHERE ROW_ID = #{id}
    </select>
    <select id="checkAuthWithEmpIdAndClueId" resultType="int">
        select count(*)
        from s_lead t, cx_lead_x x
        where t.ENABLE_FLAG = 'Y' and t.row_id = x.row_id
        and t.row_id = #{clueId}
        and (exists (select 1
        from S_PARTY_RPT_REL p52,
        S_PARTY_PER p,
        s_postn p3,
        CX_POST_SECT p4
        where p52.sub_party_id = T.bu_id
        and p52.party_id = p3.bu_id
        and p.party_id = p3.row_id
        and p3.POSTN_TYPE_CD = p4.POSITION_TYPE
        and p.person_id = #{empId}
        and p4.LEAD_SECT = 'suborg')
        or t.created_by =#{empId}
        or x.owner_id=#{empId})
    </select>

    <!-- 线索状态是否为"待客户经理更新"-->
    <select id="isLeadUpdate" parameterType="com.zte.mcrm.clues.access.vo.BusinessClues" resultType="int">
        select count(*)
        from s_lead t
        where t.ENABLE_FLAG = 'Y' and t.status_cd = 'Renewing' and
        t.row_id = #{id}
    </select>

    <select id="advanceQuery" parameterType="com.zte.springbootframe.util.page.PageQuery"
            resultType="com.zte.mcrm.clues.access.vo.BusinessClues">
        SELECT
        tb.*
        FROM
        (
        SELECT
        sl.row_id AS "id",
        sl.Lead_Num AS "clueNum",
        sl.STATUS_CD AS "statusCode",
        x.LEAD_NAME AS "clueName",
        x.BUS_TYPE AS "businessTypeCode",
        sl.BU_ID AS "deptId",
        x.OWNER_ID AS "ownerMgr",
        x.Currency_Id AS "currency",
        x.Lead_Amount AS "investmentScaleOfAcct",
        x.CONTRACT_PRICE AS "predictSignAmt",
        x.DATE_2 AS "predictSignDate",
        sl.Accnt_Id AS "acctId",
        x.SALES_TYPE AS "saleModelCode",
        x.Lead_Source AS "clueSourceCode",
        x.NOTES_2 AS "background",
        sl.CREATED_BY AS "createdBy",
        x.Lead_Type AS "acctTypeCode",
        st.LAST_NAME AS "backPerson",
        x.PROD_SYSTEM AS "prodSystemId",
        x.PROD_LINE_BIG AS "bigProdcutLineId",
        x.REFUSE_REASON AS "backReasonCode",
        x.MUL_DIVISION_FLG AS "mulDivisionFlg",
        x.MARKET_TYPE AS "marketTypeCode",
        x.tech_mgr_id techMgrId,
        x.FUND_FLG foundFlgCode,
        x.ACCOUNT_ATTRIBUTE accountAttributeCode,
        x.POTENTIAL_MODEL potentialModelCode
        FROM
        s_lead sl
        INNER JOIN (
        SELECT
        t.row_id
        FROM
        s_lead t
        INNER JOIN (
        SELECT
        p52.sub_party_id
        FROM
        (
        SELECT
        p3.bu_id
        FROM
        CX_POST_SECT p4
        INNER JOIN s_postn p3 ON p3.POSTN_TYPE_CD = p4.POSITION_TYPE
        INNER JOIN S_PARTY_PER p ON p.party_id = p3.row_id
        WHERE
        1 = 1
        AND p4.LEAD_SECT = 'suborg'
        AND p.person_id = #{entity.empId}
        ) post
        INNER JOIN S_PARTY_RPT_REL p52 ON p52.party_id = post.bu_id
        ) AS orgTable ON orgTable.sub_party_id = t.BU_ID
        UNION
        SELECT
        t.row_id
        FROM
        s_lead t
        WHERE
        t.ENABLE_FLAG = 'Y'
        AND t.created_by = #{entity.empId}
        UNION
        SELECT
        clx.ROW_ID
        FROM
        cx_lead_x clx
        WHERE
        clx.owner_id = #{entity.empId}
        ) AS filterResult ON filterResult.row_id = sl.row_id
        LEFT JOIN cx_lead_x x ON filterResult.row_id = x.row_id
        LEFT JOIN s_contact st ON st.row_id = x.owner_id
        LEFT JOIN s_user su ON su.row_id = st.row_Id
        WHERE
        sl.ENABLE_FLAG = 'Y'
        ORDER BY
        sl.LAST_UPD DESC
        ) tb
        <if test="null!=entity.deptId and ''!=entity.deptId">
            ,s_party_rpt_rel t1
            ,s_party_per t2
            ,s_postn t3
        </if>
        where 1 = 1
        <if test="null!=entity.statusCode and ''!=entity.statusCode">
            and tb.statusCode = #{entity.statusCode}
        </if>
        <if test="null!=entity.bigProdcutLineId and ''!=entity.bigProdcutLineId">
            and tb.bigProdcutLineId = #{entity.bigProdcutLineId}
        </if>
        <if test="null!=entity.deptId and ''!=entity.deptId">
            and tb.deptId = t1.SUB_PARTY_ID
            and t1.PARTY_ID = #{entity.deptId}
            and t2.PARTY_ID = t3.ROW_ID
            and t2.PERSON_ID = #{entity.empId}
        </if>
        <if test="null!=entity.predictSignDateFrom and ''!=entity.predictSignDateFrom and null!=entity.predictSignDateTo and ''!=entity.predictSignDateTo">
            and tb.predictSignDate BETWEEN predictSignDateFrom AND predictSignDateTo
        </if>
        LIMIT #{start}, #{pageSize}
    </select>

    <select id="advanceQueryCount" parameterType="com.zte.springbootframe.util.page.PageQuery" resultType="int">
        SELECT
        count(sl.row_id)
        FROM
        s_lead sl
        INNER JOIN (
        SELECT
        t.row_id
        FROM
        s_lead t
        INNER JOIN (
        SELECT
        p52.sub_party_id
        FROM
        (
        SELECT
        p3.bu_id
        FROM
        CX_POST_SECT p4
        INNER JOIN s_postn p3 ON p3.POSTN_TYPE_CD = p4.POSITION_TYPE
        INNER JOIN S_PARTY_PER p ON p.party_id = p3.row_id
        WHERE
        1 = 1
        AND p4.LEAD_SECT = 'suborg'
        AND p.person_id = #{entity.empId}
        ) post
        INNER JOIN S_PARTY_RPT_REL p52 ON p52.party_id = post.bu_id
        ) AS orgTable ON orgTable.sub_party_id = t.BU_ID
        UNION
        SELECT
        t.row_id
        FROM
        s_lead t
        WHERE
        t.ENABLE_FLAG = 'Y'
        AND t.created_by = #{entity.empId}
        UNION
        SELECT
        clx.ROW_ID
        FROM
        cx_lead_x clx
        WHERE
        clx.owner_id = #{entity.empId}
        ) AS filterResult ON filterResult.row_id = sl.row_id
        LEFT JOIN cx_lead_x x ON filterResult.row_id = x.row_id
        <if test="null!=entity.deptId and ''!=entity.deptId">
            ,s_party_rpt_rel t1
            ,s_party_per t2
            ,s_postn t3
        </if>
        where 1 = 1
        and sl.ENABLE_FLAG = 'Y'
        <if test="null!=entity.statusCode and ''!=entity.statusCode">
            and sl.STATUS_CD = #{entity.statusCode}
        </if>
        <if test="null!=entity.bigProdcutLineId and ''!=entity.bigProdcutLineId">
            and x.bigProdcutLineId = #{entity.bigProdcutLineId}
        </if>
        <if test="null!=entity.deptId and ''!=entity.deptId">
            and sl.BU_ID = t1.SUB_PARTY_ID
            and t1.PARTY_ID = #{entity.deptId}
            and t2.PARTY_ID = t3.ROW_ID
            and t2.PERSON_ID = #{entity.empId}
        </if>
        <if test="null!=entity.predictSignDateFrom and ''!=entity.predictSignDateFrom and null!=entity.predictSignDateTo and ''!=entity.predictSignDateTo">
            and x.predictSignDate BETWEEN predictSignDateFrom AND predictSignDateTo
        </if>
    </select>
</mapper>
