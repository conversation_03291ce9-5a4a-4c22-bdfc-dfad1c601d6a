package com.zte.mcrm.adapter.model.dto;

/* Started by AICoder, pid:6e2532778004411d8a9e1c3902b272d7 */
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@ApiModel(description = "客户审批信息模型")
@Getter
@Setter
public class CustomerApproveResult {

    @ApiModelProperty(value = "客户ID", example = "1-139SNG")
    private String accountId;

    @ApiModelProperty(value = "客户编码", example = "CN000000004185")
    private String accountCode;

    @ApiModelProperty(value = "客户名称", example = "中国铁塔股份有限公司深圳市分公司")
    private String accountName;

    @ApiModelProperty(value = "审批类型", example = "Account validation Approve")
    private String approveType;

    @ApiModelProperty(value = "审批状态", example = "Approving")
    private String approveStatus;

}
/* Ended by AICoder, pid:6e2532778004411d8a9e1c3902b272d7 */
