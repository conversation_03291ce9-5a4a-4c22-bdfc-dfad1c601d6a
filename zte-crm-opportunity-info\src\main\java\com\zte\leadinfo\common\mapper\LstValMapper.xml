<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.leadinfo.common.mapper.LstValMapper">

    <resultMap id="lovMap"  type="com.zte.mcrm.lov.access.vo.ListOfValue"></resultMap>

    <select id="getLovTypeValue" resultMap="lovMap">
        SELECT T.val val
             ,T.active_flg active
             ,T.type lovType
             ,T.NAME lovCode
             ,T.LANG_ID lang
             ,T.row_id Id
             ,T.Par_Row_Id parId
             ,T.ORDER_BY
             ,t1.name parCode
        from S_LST_OF_VAL T left join S_LST_OF_VAL t1 on t.Par_Row_Id = t1.row_id
        WHERE 1 = 1
        <if test="null!=lovType">
        AND T.type = #{lovType}
        </if>
    </select>
</mapper>