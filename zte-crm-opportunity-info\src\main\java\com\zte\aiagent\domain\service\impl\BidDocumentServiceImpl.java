package com.zte.aiagent.domain.service.impl;

import com.zte.aiagent.app.command.CreateDocumentCommand;
import com.zte.aiagent.common.exception.BidDocumentErrorCode;
import com.zte.aiagent.domain.aggregate.BidDocument;
import com.zte.aiagent.domain.enums.ParseStatusEnum;
import com.zte.aiagent.domain.repository.BidDocumentRepository;
import com.zte.aiagent.domain.repository.BidParseRecordRepository;
import com.zte.aiagent.domain.service.BidDocumentService;
import com.zte.aiagent.domain.shared.event.DomainEvent;
import com.zte.aiagent.domain.shared.valueobject.Tenant;
import com.zte.aiagent.domain.valueobject.DocumentInfo;
import com.zte.aiagent.infrastruction.adapter.DocCloudAdapter;
import com.zte.aiagent.ui.dto.request.BidDocumentPageQueryDTO;
import com.zte.aiagent.ui.dto.vo.BidDocumentVO;
import com.zte.aiagent.ui.dto.vo.BidParseRecordVO;
import com.zte.mcrm.common.consts.CommonConstant;
import com.zte.mcrm.common.framework.exception.ErrorCodeException;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.mcrm.common.util.FormDataHelpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import com.zte.crm.eva.base.common.utils.PageRowsUtil;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
@Slf4j
public class BidDocumentServiceImpl implements BidDocumentService {

    @Resource
    private BidDocumentRepository bidDocumentRepository;

    @Resource
    private DocCloudAdapter docCloudAdapter;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private BidParseRecordRepository bidParseRecordRepository;

    @Override
    public String uploadDocument(CreateDocumentCommand command) {
        // 1. 命令验证
        command.validate();

        try {
            // 2. 上传文件到文档云
            String fileKey = uploadToDocCloud(command);

            // 3. 创建文档信息值对象
            DocumentInfo documentInfo = DocumentInfo.of(
                    command.getFileName(),
                    command.getFileSize(),
                    command.getFileType(),
                    fileKey
            );

            // 4. 创建文档聚合
            BidDocument bidDocument = createBidDocument(documentInfo, command);

            // 5. 保存聚合
            saveBidDocument(bidDocument);

            // 6. 发布领域事件
            publishDomainEvents(bidDocument);

            log.info("文档创建成功 documentId={}", bidDocument.getDocumentId());
            return bidDocument.getDocumentId();

        } catch (ErrorCodeException e) {
            // 重新抛出业务异常
            log.error("文档上传失败 operator={}, fileName={}, errorCode={}",
                    command.getOperator(), command.getFileName(), e.getErrorCode(), e);
            throw e;
        } catch (Exception e) {
            // 系统异常转换为业务异常
            log.error("文档上传系统异常 operator={}, fileName={}",
                    command.getOperator(), command.getFileName(), e);
            throw new ErrorCodeException(BidDocumentErrorCode.BD3153);
        }
    }

    /**
     * 上传文件到文档云
     */
    private String uploadToDocCloud(CreateDocumentCommand command) {
        try {
            String fileKey = docCloudAdapter.uploadFile(command.getFile(), command.getFileName(), command.getOperator());
            log.info("文件上传成功 fileKey={}, operator={}", fileKey, command.getOperator());
            return fileKey;
        } catch (Exception e) {
            log.error("文档云上传失败 operator={}, fileName={}",
                    command.getOperator(), command.getFileName(), e);
            throw new ErrorCodeException(BidDocumentErrorCode.BD4103, e.getMessage());
        }
    }

    /**
     * 创建文档聚合
     */
    private BidDocument createBidDocument(DocumentInfo documentInfo, CreateDocumentCommand command) {
        try {
            return BidDocument.create(
                    documentInfo,
                    command.getParseTemplateCode(),
                    command.getParseTemplateId(),
                    Tenant.of(command.getTenantId()),
                    command.getOperator()
            );
        } catch (Exception e) {
            log.error("创建文档聚合失败 operator={}, fileName={}",
                    command.getOperator(), command.getFileName(), e);
            throw new ErrorCodeException(BidDocumentErrorCode.BD3153, e);
        }
    }

    /**
     * 保存文档聚合
     */
    private void saveBidDocument(BidDocument bidDocument) {
        try {
            bidDocumentRepository.save(bidDocument);
        } catch (Exception e) {
            log.error("保存文档失败 documentId={}", bidDocument.getDocumentId(), e);
            throw new ErrorCodeException(BidDocumentErrorCode.BD3123, e);
        }
    }

    /**
     * 发布领域事件
     */
    private void publishDomainEvents(BidDocument bidDocument) {
        try {
            List<DomainEvent> events = bidDocument.getDomainEventsAndClear();
            events.forEach(eventPublisher::publishEvent);
            log.info("领域事件发布成功 documentId={}, eventCount={}",
                    bidDocument.getDocumentId(), events.size());
        } catch (Exception e) {
            log.warn("领域事件发布失败 documentId={}", bidDocument.getDocumentId(), e);
            // 事件发布失败不影响主流程，只记录警告日志
        }
    }
    
    @Override
    public String exportParsedFile(String documentId, String format) {
        BidDocument bidDocument = bidDocumentRepository.findById(documentId).orElse(null);
        String fileName = Optional.ofNullable(bidDocument).map(BidDocument::getDocumentInfo)
                .map(DocumentInfo::getFileName).orElse(null);
        String fileKey = Optional.ofNullable(bidDocument).map(BidDocument::getExportParsedFileKey)
                .map(o -> o.findKeyByFormat(format)).orElse(null);
        if (fileKey == null) {
            log.error("未获取到文件key");
            throw new ErrorCodeException(BidDocumentErrorCode.BD3103);
        }
        return docCloudAdapter.getDownloadUrl(fileKey, fileName, CommonUtils.getEmpNo());
    }

    @Override
    public List<BidParseRecordVO> queryParseRecords(String documentId) throws Exception {
        return bidParseRecordRepository.selectByDocumentId(documentId);
    }

    @Override
    public PageRows<BidDocumentVO> queryPageWithProgress(FormData<BidDocumentPageQueryDTO> request) throws Exception {
        if (request == null || request.getBo() == null) {
            throw new IllegalArgumentException("Request parameter cannot be null");
        }
        request.setRows(FormDataHelpUtil.getPageSize(request));
        request.setPage(FormDataHelpUtil.getPageNum(request));

        // 查询分页数据
        Long totalCount = bidDocumentRepository.selectPageCount(request);
        if (totalCount <= CommonConstant.ZERO) {
            return PageRowsUtil.buildEmptyPage(request.getPage());
        }

        List<BidDocumentVO> documentList = bidDocumentRepository.selectPageList(request);

        // 为每个文档填充解析记录和计算进度
        for (BidDocumentVO document : documentList) {
            List<BidParseRecordVO> parseRecords = bidParseRecordRepository.selectByDocumentId(document.getRowId());
            document.setBidParseRecordList(parseRecords);

            // 计算解析进度
            int progress = calculateParseProgress(parseRecords);
            document.setParseProgress(progress);
        }
        // 构建响应对象
        return PageRowsUtil.buildPageRow(request.getPage(), totalCount, documentList);

    }

    /**
     * 计算解析进度
     * @param parseRecords 解析记录列表
     * @return 解析进度百分比(0-100整数)
     */
    private int calculateParseProgress(List<BidParseRecordVO> parseRecords) {
        if (CollectionUtils.isEmpty(parseRecords)) {
            return CommonConstant.ZERO;
        }

        int totalCount = parseRecords.size();
        int completedCount = CommonConstant.ZERO;

        for (BidParseRecordVO record : parseRecords) {
            String status = record.getParseStatus();
            if (ParseStatusEnum.SUCCESS.getCode().equals(status) || ParseStatusEnum.FAILED.getCode().equals(status)) {
                completedCount++;
            }
        }

        // 计算百分比，四舍五入
        double percentage = (double) completedCount / totalCount * CommonConstant.HUNDRED;
        return (int) Math.round(percentage);
    }
}
