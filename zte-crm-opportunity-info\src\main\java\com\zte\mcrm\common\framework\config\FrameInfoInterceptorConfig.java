package com.zte.mcrm.common.framework.config;


import com.zte.mcrm.common.framework.interceptors.CommonInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


/**
 * 系统拦截器注册器
 * <AUTHOR>
 * @date 2021/01/16
 */
@Configuration
public class FrameInfoInterceptorConfig implements WebMvcConfigurer {

    /**
     * 拦截器注册
     * <AUTHOR>
     * @date 2021/01/16
     * @see WebMvcConfigurer
     * #addInterceptors(org.springframework.web.servlet.config.annotation.InterceptorRegistry)
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        
        // 追加自定义拦截器，获取工号
        registry.addInterceptor(new CommonInterceptor());
        
        // 检查请求头信息
//        registry.addInterceptor(new CheckRequestHeaderInterceptor())
//            .excludePathPatterns("*.js,*.gif,*.jpg,*.png,*.css,*.ico,*.woff,*.woff2,*.ttf")
//            .excludePathPatterns("/swagger**")
//            .excludePathPatterns("/swagger-resources/**")
//            .excludePathPatterns("/webjars/**").excludePathPatterns("/constraint/**");
    }
}
