package com.zte.mcrm.common.upload.service;

import com.zte.mcrm.common.upload.model.entity.ComUploadFile;
import com.zte.mcrm.common.upload.service.base.UploadFileBaseService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 *  服务接口类
 * <AUTHOR>
 * @date 2021/06/05  
 */
public interface UploadFileService extends UploadFileBaseService {

    /**
     * 上传并保存记录
     * @param uploadFile
     * @param file
     * @exception
     */
    void uploadFile(ComUploadFile uploadFile, MultipartFile file, HttpServletRequest request) throws Exception;
    /**
     * 查询某个业务上传附件数量
     * @param uploadType
     * @param billOid
     * @return
     */
    Long countFileNum(String uploadType, Long billOid);


    /**
     * 根据业务类型和单据编号查询附件
     * @param uploadFile
     * @return
     */
    List<ComUploadFile> findUploadFiles(ComUploadFile uploadFile);

    /**
     * 複印文件
     * @param uploadType
     * @param oid
     * @param newBillOid
     * @return
     */
    Boolean copy(String uploadType, String oid, String newBillOid);

    /**
     * 刪除附件
     * @param dmeKey
     * @return
     */
    Boolean deleteFileByDmeKey(String dmeKey);


    /**
     * 是否是公共附件
     * @param dmeKey
     * @return
     * <AUTHOR>
     * @date 2021/9/1
     */
    Boolean isPublicFile(String dmeKey);

    /**
     * 是否是公共图片
     * @param dmeKey
     * @return
     * <AUTHOR>
     * @date 2021/9/1
     */
    Boolean isPublicImg(String dmeKey);
}