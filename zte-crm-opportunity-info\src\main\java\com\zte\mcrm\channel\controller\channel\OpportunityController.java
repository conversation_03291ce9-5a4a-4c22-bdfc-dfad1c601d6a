package com.zte.mcrm.channel.controller.channel;

import com.alibaba.fastjson.JSON;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import com.zte.mcrm.channel.model.dto.ChannelPartnerChangeDTO;
import com.zte.mcrm.channel.model.dto.OpportunityQueryDTO;
import com.zte.mcrm.channel.model.entity.IChannelOpptyCustomerCreateRecordQuery;
import com.zte.mcrm.channel.model.entity.OpportunityInfo;
import com.zte.mcrm.channel.model.vo.OpportunityVO;
import com.zte.mcrm.channel.model.vo.OpptyCustomerCreateRecordVO;
import com.zte.mcrm.channel.service.channel.IOpportunityInfoService;
import com.zte.mcrm.channel.service.channel.IOpportunityService;
import com.zte.mcrm.common.util.ApprovalHeaderUtil;
import com.zte.sec.uac.constant.HttpHeaderConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Map;

/**
 *  spring mvc控制类
 * <AUTHOR>
 * @date 2021/09/14
 */
@Api(tags = "商机信息相关API")
@RestController
@RequestMapping("/channel/sopty")
public class OpportunityController {

	/** 日志对象 */
	private static final Logger log = LoggerFactory.getLogger(OpportunityController.class);

	/** 服务对象，SPRING自动装配 */
	@Autowired
	IOpportunityService opportunityServiceImpl ;
	@Autowired
	private IOpportunityInfoService opportunityInfoService;

	@ApiOperation("分页查询商机列表-ichannel")
	@PostMapping(value="/getpage")
	public ServiceData<PageRows<OpportunityVO>>  getPage(@RequestBody FormData<OpportunityQueryDTO>  form) throws Exception {
		PageRows<OpportunityVO> page = opportunityServiceImpl.getOpportunityVoPageRows(form,Boolean.FALSE);
		//返回统一的服务端数据
        return ServiceResultUtil.success(page);
	}

	@ApiOperation("查询当月未填写月报的商机列表-ichannel")
	@PostMapping(value = "/getMonthReportOpportunity")
	public ServiceData<PageRows<OpportunityVO>> getMonthReportOpportunity(
			@RequestBody FormData<OpportunityQueryDTO> form,
			@RequestHeader(HttpHeaderConstant.HTTP_HEADER_X_EMP_NO) String empNo) {
		return ServiceResultUtil.success(opportunityServiceImpl.getMonthReportOpportunity(form, empNo));
	}

	@ApiOperation("查询渠道商关联商机总数")
	@GetMapping(value="/getCustomerTotalOpportunity")
	public ServiceData<Map<String,Object>>  getTotalOpportunity(@RequestParam String crmCustomerCode) throws Exception {

		Map<String,Object> resultMap = opportunityServiceImpl.getTotalOpportunity(crmCustomerCode);
		//返回统一的服务端数据
		return ServiceResultUtil.success(resultMap);
	}

	@ApiOperation("查询关联商机总数")
	@PostMapping(value="/getTotalOpportunity")
	public ServiceData<Map<String,Object>>  getTotalOpportunity(@RequestBody OpportunityQueryDTO opportunityQuery) throws Exception {

		Map<String,Object> resultMap = opportunityServiceImpl.getTotalOpportunity(opportunityQuery.getCrmCustomerCode());
		//返回统一的服务端数据
		return ServiceResultUtil.success(resultMap);
	}

	@ApiOperation("查询商机详情 -Get方式")
	@GetMapping(value="/opportunityinfo/{rowId}")
	public ServiceData<OpportunityInfo> getOpportunityInfo(@PathVariable("rowId") String rowId) throws Exception {
		//业务操作可以不捕获异常,由统一的异常处理方法处理
		OpportunityInfo sOpty = opportunityServiceImpl.getIChannelOpportunity(rowId);

		//返回统一的服务端数据
		return ServiceResultUtil.success(sOpty);
	}


	@ApiOperation("删除商机草稿 -delete方式")
	@GetMapping(value="/deleteOpportunityInfo/{rowId}")
	public ServiceData<Boolean> deleteOpportunityInfo(@PathVariable("rowId") String rowId) {
		//业务操作可以不捕获异常,由统一的异常处理方法处理
		 opportunityServiceImpl.softDeleteDraft(rowId);

		//返回统一的服务端数据
		return ServiceResultUtil.success(true);
	}

	@ApiOperation("撤销商机 -Get方式")
	@GetMapping(value="/cancel")
	public ServiceData<String> cancelOpportunity(@RequestParam("rowId") String rowId,@RequestParam("systemSource") String systemSource) {

		String result = opportunityServiceImpl.cancelOpportunity(rowId,systemSource);
		return ServiceResultUtil.success(result);
	}
	@ApiOperation("导出商机")
	@PostMapping(value = "/exportOpportunityList")
	public void exportOpportunityList(HttpServletResponse response, HttpServletRequest request,
									  @ApiParam(value = "导出请求对象", required = true) @RequestBody FormData<OpportunityQueryDTO>  form) throws Exception {
		HttpSession session=request.getSession(true);
		session.setMaxInactiveInterval(360000);
		opportunityServiceImpl.exportOpportunityList(form,response);
	}

	@ApiOperation("激活商机 -Get方式")
	@GetMapping(value="/activate")
	public ServiceData<String> activateOpportunity(@RequestParam("rowId") String rowId,@RequestParam("systemSource") String systemSource) {

		String result = opportunityServiceImpl.activateOpportunity(rowId,systemSource);
		return ServiceResultUtil.success(result);
	}
	@ApiOperation("催办商机 -Get方式")
	@GetMapping(value = "/urge")
	public ServiceData <String> urgeOpportunity(@RequestParam("rowId") String rowId){
			String result =opportunityServiceImpl.urgeOpportunity(rowId);
			return ServiceResultUtil.success(result);
	}

	@ApiOperation(value = "获取签名", hidden = true)
	@GetMapping(value="/getSign")
	public ServiceData<Map<String,String>> getSign(@RequestParam("accessKey") String accessKey, @RequestParam("secretKey") String secretKey) throws Exception {
		//业务操作可以不捕获异常,由统一的异常处理方法处理
		Map<String,String> sign = ApprovalHeaderUtil.getHeaderParamsMap(accessKey, secretKey);
		//返回统一的服务端数据
		return ServiceResultUtil.success(sign);
	}

	@ApiOperation("刷新合规状态")
	@GetMapping(value = "/refreshComplianceStatus")
	public 	ServiceData refreshComplianceStatus(@RequestParam("rowId") String rowId) throws Exception {
		return ServiceResultUtil.success(opportunityServiceImpl.refreshComplianceStatus(rowId));
	}

	@ApiOperation("客户进度查询接口-对外提供")
	@PostMapping("/createCustomer/query")
	public ServiceData<PageRows<OpptyCustomerCreateRecordVO>> queryCustomerRecord(@RequestBody FormData<IChannelOpptyCustomerCreateRecordQuery> formData) throws Exception {
		return ServiceResultUtil.success(opportunityServiceImpl.queryCustomerRecordWithIChannel(formData));
	}

	@ApiOperation("项目立项更新渠道商名称接口-运维")
	@PostMapping("/modifyCustomer")
	public ServiceData<Boolean> queryCustomerRecord(@RequestBody ChannelPartnerChangeDTO record) throws Exception {
		String jsonStr = JSON.toJSONString(record);
		return ServiceResultUtil.success(opportunityInfoService.modifyChannelPartner(jsonStr));
	}
}
