package com.zte.mcrm.adapter.service.impl;

import com.alibaba.fastjson.TypeReference;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.service.IChannelValidateService;
import com.zte.mcrm.channel.model.dto.CrmCustomerDertimineParamDTO;
import com.zte.mcrm.channel.model.vo.CrmCustomerDetermineResultVO;
import com.zte.mcrm.common.consts.HttpMethodEnum;
import com.zte.mcrm.common.util.MsaUtils;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ChannelValidateServiceImpl implements IChannelValidateService {

    private static  final Logger LOGGER = LoggerFactory.getLogger(ChannelValidateServiceImpl.class);

    @Value("${internalGov.serviceName}")
    private String internalGovServiceName;
    @Value("${internalGov.channelVerifyUrl}")
    private String internalGovVerifyUrl;
    @Value("${projectAuthorization.serviceName}")
    private String projectAuthServiceName;
    @Value("${projectAuthorization.channelVerifyUrl}")
    private String projectAuthVerifyUrl;
    @Value("${orderForm.serviceName}")
    private String orderFormServiceName;
    @Value("${orderForm.channelVerifyUrl}")
    private String orderFormVerifyUrl;
    @Value("${cpqd.serviceName}")
    private String cpqdServiceName;
    @Value("${cpqd.channelVerifyUrl}")
    private String cpqdVerifyUrl;

    /**
     * 根据渠道商编码及年份获取是否存在有效商机（政企方案&签约）
     *
     * @param crmCustomerDertimineParamDTO
     * @return
     */
    @Override
    public List<CrmCustomerDetermineResultVO> determineIntransitsAndValidByGov(CrmCustomerDertimineParamDTO crmCustomerDertimineParamDTO) throws RouteException {
        List<CrmCustomerDetermineResultVO> result = determineIntransitsAndValid(crmCustomerDertimineParamDTO, internalGovServiceName, internalGovVerifyUrl);
        LOGGER.info("政企方案&签约output:{}", result);
        return result;
    }

    /**
     * 根据渠道商编码及年份获取是否存在有效商机（订单）
     *
     * @param crmCustomerDertimineParamDTO
     * @return
     */
    @Override
    public List<CrmCustomerDetermineResultVO> determineIntransitsAndValidByOrderForm(CrmCustomerDertimineParamDTO crmCustomerDertimineParamDTO) throws RouteException {
        List<CrmCustomerDetermineResultVO> result = determineIntransitsAndValid(crmCustomerDertimineParamDTO, orderFormServiceName, orderFormVerifyUrl);
        LOGGER.info("订单output:{}", result);
        return result;
    }

    /**
     * 根据渠道商编码及年份获取是否存在有效商机（项目授权）
     *
     * @param crmCustomerDertimineParamDTO
     * @return
     */
    @Override
    public List<CrmCustomerDetermineResultVO> determineIntransitsAndValidByAuth(CrmCustomerDertimineParamDTO crmCustomerDertimineParamDTO) throws RouteException {
        List<CrmCustomerDetermineResultVO> result = determineIntransitsAndValid(crmCustomerDertimineParamDTO, projectAuthServiceName, projectAuthVerifyUrl);
        LOGGER.info("项目授权output:{}", result);
        return result;
    }

    /**
     * 根据渠道商编码及年份获取系统内是否存在有效及在途单据
     *
     * @param crmCustomerDertimineParamDTO
     * @return
     */
    @Override
    public List<CrmCustomerDetermineResultVO> determineIntransitsAndValid(CrmCustomerDertimineParamDTO crmCustomerDertimineParamDTO, String serviceName, String url) throws RouteException {
        Map<String, String> paramString = new HashMap<>(3);
        paramString.put("serviceName", serviceName);
        paramString.put("version", "v1");
        paramString.put("url", url);
        return MsaUtils.invokeServiceAndReturnBO(
                paramString,
                HttpMethodEnum.POST,
                crmCustomerDertimineParamDTO,
                new TypeReference<ServiceData<List<CrmCustomerDetermineResultVO>>>() {
                });
    }

    /**
     * 根据渠道商编码及年份获取是否存在有效商机（配置报价）
     *
     * @param crmCustomerDertimineParamDTO
     * @return
     */
    @Override
    public List<CrmCustomerDetermineResultVO> determineIntransitsAndValidByCpq(CrmCustomerDertimineParamDTO crmCustomerDertimineParamDTO) throws RouteException {
        List<CrmCustomerDetermineResultVO> result = determineIntransitsAndValid(crmCustomerDertimineParamDTO, cpqdServiceName, cpqdVerifyUrl);
        LOGGER.info("配置报价output:{}", result);
        return result;
    }
}
