package com.zte.aiagent.infrastruction.access.po;

import java.util.Date;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * 系统参数配置值表PO类
 * 对应数据库表：sys_conf_value
 * 用于存储系统参数配置的具体值，与sys_conf_type形成关联关系
 */
@Data
public class SysConfValuePO {
    /**
     * 主键ID
     */
    private String rowId;

    /**
     * 编码类型主键ID，关联sys_conf_type.row_id
     */
    private String codeTypeId;

    /**
     * 值代码
     */
    private String code;

    /**
     * 编码中文值
     */
    private String valueCn;

    /**
     * 编码英文值
     */
    private String valueEn;

    /**
     * 排序值
     */
    private Integer orderValue;

    /**
     * 父值代码
     */
    private String parentValueCode;

    /**
     * 父值id
     */
    private String parentId;

    /**
     * 数据层级
     */
    private Integer dataLevel;

    /**
     * 备注
     */
    private String memo;

    /**
     * 最后更新时间
     */
    private Date lastUpdatedDate;

    /**
     * 最后更新人
     */
    private String lastUpdatedBy;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 有效标记(Y/N)，用于逻辑删除
     */
    private String enabledFlag;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 状态(Y:生效,N:失效)
     */
    private String status;

    /**
     * 扩展字段(JSON格式)，存储额外的参数信息
     */
    private String expand;
}

