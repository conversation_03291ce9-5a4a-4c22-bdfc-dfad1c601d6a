package com.zte.leadinfo.leadinfo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.leadinfo.leadinfo.entity.CustomerInfoDO;
import com.zte.leadinfo.leadinfo.entity.SOptyDO;
import com.zte.opty.sync.domain.dto.OptySyncQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> suntugui
 * @date 2024/6/4 10:31
 */
@Mapper
public interface SOptyMapper extends BaseMapper<SOptyDO> {

    List<String> selectIdsByConditions(OptySyncQueryDTO params);

    List<String> loadUndoOptyIds(@Param("batchSize") int batchSize);

    List<CustomerInfoDO> selectAll();
}
