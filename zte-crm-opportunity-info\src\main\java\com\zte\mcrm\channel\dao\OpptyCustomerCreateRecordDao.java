package com.zte.mcrm.channel.dao;

import com.zte.mcrm.channel.model.entity.OpptyCustomerCreateRecordQuery;
import com.zte.mcrm.channel.model.entity.OpptyCustomerCreateRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 * 渠道商新建最终客户记录 数据访问接口类 
 * <AUTHOR>
 * @date 2023/05/10 
 */
@Mapper
public interface OpptyCustomerCreateRecordDao{
    /**
     * 根据主键查询
     * <AUTHOR>
     * @param rowId 主键
     * @date 2023/05/10 
     * @return 实体
     */
	OpptyCustomerCreateRecord get(@Param("rowId")String rowId);
	
    /**
     * 查询列表
     * <AUTHOR>
     * @param map 查询条件
     * @date 2023/05/10 
     * @return 实体集合
     */
	List<OpptyCustomerCreateRecord> getList(Map<String, Object> map);
	
	/**
     * 软删除，enabled_flag字段更新为N
     * <AUTHOR>
     * @param rowId 主键
     * @date 2023/05/10 
     * @return 删除总数
     */	
	int softDelete(@Param("rowId")String rowId);
	
    /**
     * 删除
     * <AUTHOR>
     * @param rowId 主键
     * @date 2023/05/10 
     * @return 删除总数
     */	
	int delete(@Param("rowId")String rowId);

    /**
     * 动态新增
     * <AUTHOR>
     * @param entity 新增实体
     * @date 2023/05/10 
     * @return 新增总数
     */	
	int insert(OpptyCustomerCreateRecord entity);

    /**
     * 批量新增
     * <AUTHOR>
     * @param list 新增实体集合
     * @date 2023/05/10 
     * @return 新增总数
     */	
	int insertByBatch(List<OpptyCustomerCreateRecord> list);


	/**
	 *批量更新接口
	 * */
	int updateByBatch(List<OpptyCustomerCreateRecord> list);

    /**
     * 更新
     * <AUTHOR>
     * @param entity 更新条件
     * @date 2023/05/10 
     * @return 更新影响总数
     */		
	int update(OpptyCustomerCreateRecord entity);

    /**
     * 统计
     * <AUTHOR>
     * @param map 查询条件
     * @date 2023/05/10 
     * @return 统计总数
     */	
	long getCount(Map<String, Object> map);

    /**
     * 分页查询
     * <AUTHOR>
     * @param map 查询条件
     * @date 2023/05/10 
     * @return 实体集合
     */	
	List<OpptyCustomerCreateRecord> getPage(Map<String, Object> map);

	/**
	 * 按条件分页查询
	 *
	 * @param param 参数集合
	 * @return 实体集合
	 * <AUTHOR>
	 * @date 2023/05/10
	 */
    List<OpptyCustomerCreateRecord> queryCustomerRecordByCondition(@Param("item") OpptyCustomerCreateRecordQuery param);


	/**
	 * 获取 未生效的客户
	 * */
	List<OpptyCustomerCreateRecord> getNotEffectedCustomers(Map<String, Object> map);
}