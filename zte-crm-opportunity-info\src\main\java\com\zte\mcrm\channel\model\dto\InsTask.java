package com.zte.mcrm.channel.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-10-08
 * @Description 存储的是流程实例审批和待审批信息, ins_flow对应ins_task是1对多的关系
 */
@Data
public class InsTask {
    /**
     * '审批任务id';
     */
    @ExcelProperty(value = "task_id", index = 0)
    private String taskId;
    /**
     * '流程实例id';
     */
    @ExcelProperty(value = "ins_flow_id", index = 1)
    private String insFlowId;
    /**
     * '引擎根流程实例ID（预留）';
     */
    @ExcelProperty(value = "root_process_instance_id", index = 2)
    private String rootProcessInstanceId;
    /**
     * '引擎流程实例ID（预留）';
     */
    @ExcelProperty(value = "process_instance_id", index = 3)
    private String processInstanceId;
    /**
     * '引擎任务id（预留）';
     */
    @ExcelProperty(value = "engine_task_id", index = 4)
    private String engineTaskId;
    /**
     * '引擎执行id（预留）';
     */
    @ExcelProperty(value = "engine_execution_id", index = 5)
    private String engineExecutionId;
    /**
     * '引擎节点定义键（预留）';
     */
    @ExcelProperty(value = "engine_task_definition_key", index = 6)
    private String engineTaskDefinitionKey;
    /**
     * '任务状态码：
     * ACTIVE 激活状态，即正在审批中；
     * UNACTIVE 未激活状态;
     * REASSIGN 已转交；
     * COMPLETED 已提交审批；
     * REVOKE 已撤回；
     * IGNORE 已失效；
     * ROLLBACK 已回退';
     */
    @ExcelProperty(value = "status_code", index = 7)
    private String statusCode;
    /**
     * '审批人（目前是工号）';
     */
    @ExcelProperty(value = "approver", index = 8)
    private String approver;
    /**
     * '意见结果，Y同意、N拒绝';
     */
    @ExcelProperty(value = "opinion_result", index = 9)
    private String opinionResult;
    /**
     * '意见内容';
     */
    @ExcelProperty(value = "opinion_content", index = 10)
    private String opinionContent;
    /**
     * 审批附件信息
     */
    @ExcelProperty(value = "ext_opinion", index = 11)
    private String extOpinion;
    /**
     * '审批附件';
     */
    @ExcelProperty(value = "attached_files", index = 12)
    private String attachedFiles;
    /**
     * '原任务ID';
     */
    @ExcelProperty(value = "from_task_id", index = 13)
    private String fromTaskId;
    /**
     * '10001' ; '租户';
     */
    @ExcelProperty(value = "tenant_id", index = 14)
    private String tenantId = "10001";
    /**
     * '应用编码';
     */
    @ExcelProperty(value = "app_code", index = 15)
    private String appCode = OpportunityConstant.NEW_OPPORTUNITY_APP_CODE;
    /**
     * '创建人';
     */
    @ExcelProperty(value = "created_by", index = 16)
    private String createdBy;
    /**
     * 创建时间';
     */
    @ExcelProperty(value = "create_date", index = 17)
    private Date createDate;
    /**
     * '更新人';
     */
    @ExcelProperty(value = "last_update_by", index = 18)
    private String lastUpdateBy;
    /**
     * '更新时间';
     */
    @ExcelProperty(value = "last_update_date", index = 19)
    private Date lastUpdateDate;
    /**
     * 'Y有效，N无效'
     */
    @ExcelProperty(value = "enable_flag", index = 20)
    private String enableFlag;
}
