package com.zte.leadinfo.leadinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.zte.leadinfo.leadinfo.entity.CxOptyEmpDO;
import com.zte.leadinfo.leadinfo.mapper.CxOptyEmpMapper;
import com.zte.leadinfo.leadinfo.service.CxOptyEmpService;
import com.zte.mcrm.opportunity.common.OppSysConst;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务接口实现
 *
 * <AUTHOR>
 * @description 由 Mybatisplus Code Generator 创建
 * @since 2024-06-04 10:51:25
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class CxOptyEmpServiceImpl extends ServiceImpl<CxOptyEmpMapper, CxOptyEmpDO> implements CxOptyEmpService {

    @Override
    public List<CxOptyEmpDO> listByOpptyIds(List<String> optyRowIds) {
        if (CollectionUtils.isEmpty(optyRowIds)) {
            return Lists.newArrayList();
        }

        optyRowIds = optyRowIds.stream().filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

        LambdaQueryWrapper<CxOptyEmpDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CxOptyEmpDO::getOpptyId, optyRowIds)
                .eq(CxOptyEmpDO::getActiveFlag, OppSysConst.FLAG_Y);
        return this.list(queryWrapper);
    }
}