package com.zte.mcrm.channel.service.channel;

import com.google.common.collect.Lists;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.security.AESOperator;
import com.zte.mcrm.adapter.model.dto.AccountInfo;
import com.zte.mcrm.adapter.model.dto.OrganizationNode;
import com.zte.mcrm.adapter.model.vo.IndustryTreeDataVO;
import com.zte.mcrm.adapter.service.CustomerInfoService;
import com.zte.mcrm.adapter.service.IChannelValidateService;
import com.zte.mcrm.adapter.service.PrmService;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.channel.dao.OpportunityProductDao;
import com.zte.mcrm.channel.dao.OpportunityQueryDao;
import com.zte.mcrm.channel.model.dto.CrmCustomerDertimineParamDTO;
import com.zte.mcrm.channel.model.dto.OpportunityStatusDTO;
import com.zte.mcrm.channel.model.entity.ExternalOpportunityDetail;
import com.zte.mcrm.channel.model.entity.OpportunityProduct;
import com.zte.mcrm.channel.model.vo.CrmCustomerDetermineResultVO;
import com.zte.mcrm.channel.model.vo.OpportunityDataVO;
import com.zte.mcrm.channel.model.vo.OpportunityQueryParamVO;
import com.zte.mcrm.channel.util.CommonRemoteUtils;
import com.zte.mcrm.common.access.vo.ComDictionaryMaintainVO;
import com.zte.mcrm.common.business.service.IComDictionaryMaintainService;
import com.zte.mcrm.common.consts.ComDictionaryMaintainConsts;
import com.zte.mcrm.common.model.PersonAndOrgInfoVO;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.mcrm.common.util.CompletableFutureWrapper;
import com.zte.mcrm.common.util.DictUtils;
import com.zte.mcrm.logger.bussiness.service.LoggerService;
import com.zte.mcrm.opportunity.utils.PersonAndOrgInfoUtil;
import com.zte.opty.common.enums.OptyStatusEnum;
import com.zte.opty.model.vo.IHolOrgVO;
import com.zte.opty.sync.util.CommonMapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 描述：
 * 创建时间：2021/9/22
 *
 * @author：王丹凤6396000572
 */
@Service
public class OpportunityQueryServiceImpl implements IOpportunityQueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OpportunityQueryServiceImpl.class);

    @Autowired
    private OpportunityQueryDao opportunityQueryDao;

    @Autowired
    private OpportunityProductDao opportunityProductDao;

    @Autowired
    CustomerInfoService customerInfoService;

    @Autowired
    private PrmService prmService;

    @Autowired
    private IComDictionaryMaintainService comDictionaryMaintainService;

    @Autowired
    private IChannelValidateService channelValidateService;

    @Autowired
    IOpportunityService opportunityService;

    @Autowired
    private LoggerService loggerService;
    @Autowired
    private IOpportunityQueryService opportunityQueryService;

    @Value("${encryption.secretKeySixteen}")
    private String secretKeySixteen;

    @Value("${encryption.iv}")
    private String iv;

    @Autowired
    CommonRemoteUtils commonRemoteUtils;

    /**
     * 查询渠道商关联单据有效性接口单次最大支持查询个数
     */
    private static final Integer MAX_CUSTOMER_SIZE = 100;

    @Override
    public PageRows<OpportunityDataVO> getOpportunityList(OpportunityQueryParamVO paramVO) throws Exception {
        PageRows<OpportunityDataVO> pageRows = new PageRows<>();
        int count = opportunityQueryDao.queryOpportunityListCount(paramVO);
        pageRows.setTotal(count);
        if (count == 0) {
            pageRows.setCurrent(0L);
            pageRows.setRows(new ArrayList<>());
            return pageRows;
        }
        List<OpportunityDataVO> list = opportunityQueryDao.queryOpportunityList(paramVO);
        List<String> industryIds = new ArrayList<>();
        List<String> deptIds = new ArrayList<>();
        List<String> oldOppCustomerIds = list.stream()
                .filter(opportunityDataVO -> !StringUtils.startsWith(opportunityDataVO.getOptyCd(), OpportunityConstant.OPPORTUNITY_PREFIX))
                .map(OpportunityDataVO::getFinalCustomerId)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<AccountInfo> oldAccountInfos = getAccountInformationBatch(oldOppCustomerIds);
        Map<String, AccountInfo> oldAccountInfosMap = oldAccountInfos.stream().collect(Collectors.toMap(AccountInfo::getId, Function.identity()));
        for (OpportunityDataVO dataVO : list) {
            if (StringUtils.isNotBlank(dataVO.getFinalCustomerParentTrade()) && !industryIds.contains(dataVO.getFinalCustomerParentTrade())) {
                industryIds.add(dataVO.getFinalCustomerParentTrade());
            }
            if (StringUtils.isNotBlank(dataVO.getDeptNo()) && !deptIds.contains(dataVO.getDeptNo())) {
                deptIds.add(dataVO.getDeptNo());
            }
            //最终用户子行业
            if (StringUtils.isNotBlank(dataVO.getFinalCustomerChildTrade()) && !industryIds.contains(dataVO.getFinalCustomerChildTrade())) {
                industryIds.add(dataVO.getFinalCustomerChildTrade());
            }

            compatibleOldOpportunities(dataVO, oldAccountInfosMap);
        }
        //填充行业和部门名称
        setIndustryAndDeptNames(list, industryIds, deptIds);
        pageRows.setCurrent(paramVO.getCurrentPage());
        pageRows.setRows(list);
        return pageRows;
    }

    private String getEmpIdByEmpShortNo(String empNo) {
        String result = null;
        try {
            List<PersonAndOrgInfoVO> personInfos = PersonAndOrgInfoUtil.getEmpInfo(Collections.singletonList(empNo));
            if (CollectionUtils.isNotEmpty(personInfos)) {
                PersonAndOrgInfoVO personAndOrgInfoVO = personInfos.get(0);
                result = personAndOrgInfoVO.getComputerNum();
            }
        } catch (Exception e) {
            LOGGER.error("获取员工信息失败,empNo:{}", empNo);
            LOGGER.error("获取员工信息失败", e);
        }
        return result;
    }

    private void compatibleOldOpportunities(OpportunityDataVO dataVO, Map<String, AccountInfo> oldAccountInfosMap) {

        if (StringUtils.isBlank(dataVO.getProdName())) {
            Map<String, String> productMap = getOldOpportunityProd(dataVO.getRowId());
            dataVO.setProdName(productMap.get(OpportunityConstant.PROD_NAME));
        }

        if (StringUtils.isNotBlank(dataVO.getOptyCd())
                && !StringUtils.startsWith(dataVO.getOptyCd(), OpportunityConstant.OPPORTUNITY_PREFIX)){
            dataVO.setExpectSignMoney(oldOpportunitySignMoneyConvertor(dataVO.getExpectSignMoney()));
            // 老商机closed状态对应报备失效
            if(OptyStatusEnum.TICKET_LOSS.getCode().equals(dataVO.getStatusCd())){
                dataVO.setStatusCd(OptyStatusEnum.OPTY_SUSPEND.getCode());
                dataVO.setStatusCdName(OptyStatusEnum.getStatusName(OptyStatusEnum.OPTY_SUSPEND.getDescription(),
                                                                    CommonUtils.getxLangId()));
            }
        }

        setOldFinalCustomerNameAndCode(dataVO, oldAccountInfosMap);
    }

    private String oldOpportunitySignMoneyConvertor(String expectSignMoneyStr) {
        String resulrStr = null;
        try {
            BigDecimal expectSignMoney = new BigDecimal(expectSignMoneyStr);
            resulrStr = expectSignMoney.multiply(BigDecimal.valueOf(10000L)).toString();
        } catch (Exception e) {
            LOGGER.error("老商机预计签单金额转换失败", e);
        }
        return resulrStr;
    }

    private List<AccountInfo> getAccountInformationBatch(List<String> accountIdList) {
        List<AccountInfo> resultList = new ArrayList<>();
        try {
            List<AccountInfo> accountInfoList = customerInfoService.getCustomerInformationBatch(accountIdList);
            if (null != accountInfoList) {
                resultList = accountInfoList;
            }
        } catch (Exception e) {
            LOGGER.error("外部接口:getOpportunityList,获取老商机客户信息失败", e);
        }
        return resultList;
    }

    private void setOldFinalCustomerNameAndCode(OpportunityDataVO vo, Map<String, AccountInfo> oldAccountInfosMap) {
        if (!StringUtils.startsWith(vo.getOptyCd(), OpportunityConstant.OPPORTUNITY_PREFIX)) {
            AccountInfo accountInfo = oldAccountInfosMap.getOrDefault(vo.getFinalCustomerId(), new AccountInfo());
            vo.setFinalCustomerName(accountInfo.getAccountName());
            vo.setFinalCustomerId(StringUtils.isNotBlank(accountInfo.getAccountNum()) ? accountInfo.getAccountNum() : vo.getFinalCustomerId());
        }
    }

    private Map<String, String> getOldOpportunityProd(String rowId) {
        Map<String, String> resultMap = new HashMap<>(2);
        String prodName = null;
        String prodId = null;
        List<OpportunityProduct> opportunityProducts = opportunityProductDao.getOldOpportunityProductsByRowId(rowId);
        if (CollectionUtils.isNotEmpty(opportunityProducts)) {
            prodName = opportunityProducts.stream()
                    .filter(opportunityProduct -> null != opportunityProduct.getProductAmount())
                    .filter(opportunityProduct -> opportunityProduct.getProductAmount().compareTo(BigDecimal.ZERO) > 0)
                    .map(OpportunityProduct::getProdLv2Name)
                    .collect(Collectors.joining(","));
            prodId = opportunityProducts.stream()
                    .filter(opportunityProduct -> null != opportunityProduct.getProductAmount())
                    .filter(opportunityProduct -> opportunityProduct.getProductAmount().compareTo(BigDecimal.ZERO) > 0)
                    .map(OpportunityProduct::getProdLv2Id)
                    .collect(Collectors.joining(","));
        }
        resultMap.put(OpportunityConstant.PROD_NAME, prodName);
        resultMap.put(OpportunityConstant.PROD_ID, prodId);
        return resultMap;
    }

    /**
     * 商机详情查询（对外）
     *
     * @param optyCd 商机编号
     * @return ExternalOpportunityDetail
     * <AUTHOR>
     * @date 2021/9/24
     */
    @Override
    public ExternalOpportunityDetail getExternalOpportunityDetail(String optyCd) throws Exception {
        ExternalOpportunityDetail detail = opportunityQueryDao.getExternalOpportunityDetail(optyCd);
        if (detail != null) {
            try {
                detail.setTendType(CommonMapUtil.BIDDING_TYPE_MAP.inverse().getOrDefault(detail.getTendType(), detail.getTendType()));
                //查询最终用户行业名称
                List<String> industryIds = new ArrayList<>();
                industryIds.add(detail.getFinalCustomerParentTrade());
                industryIds.add(detail.getFinalCustomerChildTrade());
                List<IndustryTreeDataVO> industryList = prmService.getIndustryNames(industryIds, "Y");
                Map<String, String> industryMap = new HashMap<>();
                if (industryList != null && industryList.size() > 0) {
                    industryMap = industryList.stream().collect(Collectors.toMap(IndustryTreeDataVO::getLookupCode, IndustryTreeDataVO::getMeaning));
                }
                detail.setFinalCustomerParentTradeName(industryMap.containsKey(detail.getFinalCustomerParentTrade()) ? industryMap.get(detail.getFinalCustomerParentTrade()) : detail.getFinalCustomerParentTradeName());
                detail.setFinalCustomerChildTradeName(industryMap.containsKey(detail.getFinalCustomerChildTrade()) ? industryMap.get(detail.getFinalCustomerChildTrade()) : detail.getFinalCustomerChildTradeName());
                //查询所属部门名称
                List<String> deptIds = new ArrayList<>();
                deptIds.add(detail.getDeptNo());
                List<IHolOrgVO> iHolOrgVOS = commonRemoteUtils.queryOrg(deptIds);
                Map<String, String> organizationMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(iHolOrgVOS)) {
                    organizationMap = iHolOrgVOS.stream().collect(Collectors.toMap(IHolOrgVO::getCode, IHolOrgVO::getName));
                }
                detail.setDeptName(organizationMap.containsKey(detail.getDeptNo()) ? organizationMap.get(detail.getDeptNo()) : detail.getDeptName());

                compatibleWithOldOpportunity(detail);
            } catch (Exception e) {
                LOGGER.error("getExternalOpportunityDetail外部接口字段转换异常,optyCd:{}", optyCd, e);
            }
            //填充商机状态，招标类型，赢率，当前阶段名称
            List<String> types = Lists.newArrayList(ComDictionaryMaintainConsts.CURRENT_PHASES_TYPE,
                    ComDictionaryMaintainConsts.WIN_RATE_TYPE,
                    ComDictionaryMaintainConsts.TYPE_FOR_TENDER_TYPE,
                    ComDictionaryMaintainConsts.OPPORTUNITY_STATUS);
            Map<String, List<ComDictionaryMaintainVO>> dcitMap = comDictionaryMaintainService.queryByTypeList(types);
            String projectPhasesName = DictUtils.getName(detail.getProjectPhases(), dcitMap.get(ComDictionaryMaintainConsts.CURRENT_PHASES_TYPE));
            String tenderTypeName = DictUtils.getName(detail.getTendType(), dcitMap.get(ComDictionaryMaintainConsts.TYPE_FOR_TENDER_TYPE));
            detail.setStatusCdName(OptyStatusEnum.getStatusName(detail.getStatusCd(), CommonUtils.getxLangId()));
            detail.setProjectPhasesName(projectPhasesName);
            detail.setWinRateName(CommonMapUtil.SUCCESS_TATE_NAME_MAP.getOrDefault(detail.getWinRate(), detail.getWinRate()));
            detail.setTenderTypeName(tenderTypeName);

        }
        return detail;
    }

    @Override
    public Boolean updateOpportunityStatus(OpportunityStatusDTO opportunityStatusDTO) {
        // 更新主表的 状态
        OptyStatusEnum status = OptyStatusEnum.getOptyStatusEnumByCode(opportunityStatusDTO.getStatusCd());
        if (status != OptyStatusEnum.TICKET_WIN){
            return Boolean.FALSE;
        }
        List<OpportunityStatusDTO> details = opportunityQueryDao.queryCurrentOpportunityByOptyCd(opportunityStatusDTO.getOptyCd());
        if (CollectionUtils.isEmpty(details)) {
            return Boolean.FALSE;
        }

        opportunityService.updateStatus(details.get(0).getRowId(), status);
        return Boolean.TRUE;
    }

    private void compatibleWithOldOpportunity(ExternalOpportunityDetail detail) {
        try {
            if (StringUtils.isBlank(detail.getProdName())) {
                Map<String, String> productMap = getOldOpportunityProd(detail.getRowId());
                detail.setProdName(productMap.get(OpportunityConstant.PROD_NAME));
                detail.setProdId(productMap.get(OpportunityConstant.PROD_ID));
            }

            if (StringUtils.isNotBlank(detail.getOptyCd()) && !StringUtils.startsWith(detail.getOptyCd(), OpportunityConstant.OPPORTUNITY_PREFIX)) {
                // 老商机预计签单金额单位为万元，需转换成元
                if (null !=  detail.getExpectSignMoney()) {
                    detail.setExpectSignMoney(detail.getExpectSignMoney().multiply(BigDecimal.valueOf(10000L)));
                }
                // 老商机closed状态对应报备失效
                if(OptyStatusEnum.TICKET_LOSS.getCode().equals(detail.getStatusCd())){
                    detail.setStatusCd(OptyStatusEnum.OPTY_SUSPEND.getCode());
                }
                // 老商机客户id转编码
                AccountInfo accountInfo = customerInfoService.getAccountByCodeOrId(detail.getFinalCustomerId());
                detail.setFinalCustomerName(accountInfo.getAccountName());
                detail.setFinalCustomerId(StringUtils.isNotBlank(accountInfo.getCustNo()) ? accountInfo.getCustNo() : detail.getFinalCustomerId());
            }
        } catch (Exception e) {
            LOGGER.error("老商机字段兼容错误,rowId:{}", detail.getRowId(), e);
        }
    }

    private void setIndustryAndDeptNames(List<OpportunityDataVO> list, List<String> industryIds, List<String> deptIds) {

        try {
            //查询行业名称
            List<IndustryTreeDataVO> industryList = prmService.getIndustryNames(industryIds, "Y");
            Map<String, String> industryMap = new HashMap<>();
            if (industryList != null && industryList.size() > 0) {
                industryMap = industryList.stream().collect(Collectors.toMap(IndustryTreeDataVO::getLookupCode, IndustryTreeDataVO::getMeaning, (e1, e2) -> e1));
            }
            //查询部门名称
            List<IHolOrgVO> iHolOrgVOS = commonRemoteUtils.queryOrg(deptIds);
            Map<String, String> organizationMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(iHolOrgVOS)) {
                organizationMap = iHolOrgVOS.stream().collect(Collectors.toMap(IHolOrgVO::getCode, IHolOrgVO::getName, (e1, e2) -> e1));
            }
            for (OpportunityDataVO data : list) {
                data.setDeptName(organizationMap.containsKey(data.getDeptNo()) ? organizationMap.get(data.getDeptNo()) : data.getDeptName());
                data.setFinalCustomerParentTradeName(industryMap.containsKey(data.getFinalCustomerParentTrade()) ? industryMap.get(data.getFinalCustomerParentTrade()) : data.getFinalCustomerParentTradeName());
                data.setFinalCustomerChildTradeName(industryMap.containsKey(data.getFinalCustomerChildTrade()) ? industryMap.get(data.getFinalCustomerChildTrade()) : data.getFinalCustomerChildTradeName());
            }
        } catch (Exception e) {
            LOGGER.error("查询行业和部门名称异常", e);
        }
    }

    /**
     * [项目与交易统一查询接口]根据渠道商编码及年份获取是否存在有效商机，存在则返回1
     * <p>
     * 使用多线程调用政企、订单、项目授权、配置报价、商机获取结果，然后汇总将相同渠道商编码的内容相加，判断结果得出汇总值。
     *
     * @param dertimineParam 入参，渠道商客户编码及年份
     * @return 出参，渠道商客户编码及对应结果的list
     */
    @Override
    public List<CrmCustomerDetermineResultVO> determineIntransitsAndValidDocuments(CrmCustomerDertimineParamDTO dertimineParam) {
        List<String> crmCustomerCodes = dertimineParam.getCrmCustomerCodes().stream()
                .filter(StringUtils::isNotBlank)
                .distinct().collect(Collectors.toList());
        LOGGER.info("determineIntransitsAndValidDocuments input:{}", dertimineParam);
        if (CollectionUtils.isEmpty(crmCustomerCodes)) {
            return Collections.emptyList();
        }
        if (crmCustomerCodes.size() > MAX_CUSTOMER_SIZE) {
            throw new BusiException(RetCode.SERVERERROR_CODE, "crmCustomerCodes.oversize");
        }
        dertimineParam.setCrmCustomerCodes(crmCustomerCodes);

        List<CompletableFuture<List<CrmCustomerDetermineResultVO>>> channelVerifyFutures = new ArrayList<>();

        channelVerifyFutures.add(govChannelVerify(dertimineParam));
        channelVerifyFutures.add(authChannelVerifyFuture(dertimineParam));
        channelVerifyFutures.add(orderFormChannelVerifyFuture(dertimineParam));
        channelVerifyFutures.add(opportunityChannelVerify(dertimineParam));
        channelVerifyFutures.add(cpqChannelVerify(dertimineParam));

        List<CrmCustomerDetermineResultVO> results = new ArrayList<>();
        Map<String, List<CrmCustomerDetermineResultVO>> resultMap = channelVerifyFutures.stream()
                .map(CompletableFuture::join)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(CrmCustomerDetermineResultVO::getCrmCustomerCode));
        for (String crmCustomerCode : crmCustomerCodes) {
            List<CrmCustomerDetermineResultVO> crmCustomerDetermineResults = resultMap.getOrDefault(crmCustomerCode, Collections.emptyList());
            Integer sumExistedDocument = crmCustomerDetermineResults.stream().map(CrmCustomerDetermineResultVO::getExistedDocument).filter(Objects::nonNull).reduce(Integer::sum).orElse(0);
            Integer sumInTransitDocument = crmCustomerDetermineResults.stream().map(CrmCustomerDetermineResultVO::getInTransitDocument).filter(Objects::nonNull).reduce(Integer::sum).orElse(0);
            CrmCustomerDetermineResultVO resultVO = new CrmCustomerDetermineResultVO();
            resultVO.setExistedDocument(sumExistedDocument >= 1 ? 1 : 0);
            resultVO.setInTransitDocument(sumInTransitDocument >= 1 ? 1 : 0);
            resultVO.setCrmCustomerCode(crmCustomerCode);
            results.add(resultVO);
        }

        return results;
    }

    private CompletableFuture<List<CrmCustomerDetermineResultVO>> opportunityChannelVerify(CrmCustomerDertimineParamDTO dertimineParam) {
        return CompletableFutureWrapper.supplyAsync(() -> {
            try {
                return opportunityQueryService.determineIntransitsAndValidOptys(dertimineParam);
            } catch (Exception e) {
                LOGGER.error("商机查询渠道商单据有效性失败", e);
                throw new BusiException(RetCode.SERVERERROR_CODE, "opportunity.verify.error");
            }
        });
    }

    private CompletableFuture<List<CrmCustomerDetermineResultVO>> cpqChannelVerify(CrmCustomerDertimineParamDTO dertimineParam) {
        return CompletableFutureWrapper.supplyAsync(() -> {
            try {
                return channelValidateService.determineIntransitsAndValidByCpq(dertimineParam);
            } catch (Exception e) {
                LOGGER.error("配置报价查询渠道商单据有效性失败", e);
                throw new BusiException(RetCode.SERVERERROR_CODE, "cpqd.verify.error");
            }
        });
    }

    private CompletableFuture<List<CrmCustomerDetermineResultVO>> govChannelVerify(CrmCustomerDertimineParamDTO dertimineParam) {
        return CompletableFutureWrapper.supplyAsync(() -> {
            try {
                return channelValidateService.determineIntransitsAndValidByGov(dertimineParam);
            } catch (Exception e) {
                LOGGER.error("政企查询渠道商单据有效性失败", e);
                throw new BusiException(RetCode.SERVERERROR_CODE, "gov.verify.error");
            }
        });
    }

    private CompletableFuture<List<CrmCustomerDetermineResultVO>> authChannelVerifyFuture(CrmCustomerDertimineParamDTO dertimineParam) {
        return CompletableFutureWrapper.supplyAsync(() -> {
            try {
                return channelValidateService.determineIntransitsAndValidByAuth(dertimineParam);
            } catch (Exception e) {
                LOGGER.error("项目授权查询渠道商单据有效性失败", e);
                throw new BusiException(RetCode.SERVERERROR_CODE, "auth.verify.error");
            }
        });
    }


    private CompletableFuture<List<CrmCustomerDetermineResultVO>> orderFormChannelVerifyFuture(CrmCustomerDertimineParamDTO dertimineParam) {
        return CompletableFutureWrapper.supplyAsync(() -> {
            try {
                return channelValidateService.determineIntransitsAndValidByOrderForm(dertimineParam);
            } catch (Exception e) {
                LOGGER.error("订单查询渠道商单据有效性失败", e);
                throw new BusiException(RetCode.SERVERERROR_CODE, "order.verify.error");
            }
        });
    }


    @Override
    public List<CrmCustomerDetermineResultVO> determineIntransitsAndValidOptys(CrmCustomerDertimineParamDTO crmCustomerDertimineParamDTO) {
        List<String> crmCustomerCodes = crmCustomerDertimineParamDTO.getCrmCustomerCodes().stream()
                .filter(StringUtils::isNotBlank)
                .distinct().collect(Collectors.toList());
        LOGGER.info("determineIntransitsAndValidOptys input:{}", crmCustomerDertimineParamDTO);
        if (CollectionUtils.isEmpty(crmCustomerCodes)) {
            return Collections.emptyList();
        }
        List<CrmCustomerDetermineResultVO> crmCustomerDetermineQueryResults = opportunityQueryDao.determineIntransitsAndValidOptys(crmCustomerCodes,
                crmCustomerDertimineParamDTO.getYears());
        Map<String, CrmCustomerDetermineResultVO> customerDetermineResultMap = crmCustomerDetermineQueryResults.stream()
                .collect(Collectors.toMap(CrmCustomerDetermineResultVO::getCrmCustomerCode, Function.identity()));
        List<CrmCustomerDetermineResultVO> crmCustomerDetermineResults = crmCustomerCodes
                .stream().map(crmCode -> customerDetermineResultMap.getOrDefault(crmCode, new CrmCustomerDetermineResultVO(crmCode)))
                .collect(Collectors.toList());
        LOGGER.info("determineIntransitsAndValidOptys output:{}", crmCustomerDetermineResults);
        return crmCustomerDetermineResults;
    }
}
