package com.zte.mcrm.channel.service.support;

import com.zte.mcrm.adapter.emdm.service.EmdmService;
import com.zte.mcrm.adapter.model.dto.AccountInfo;
import com.zte.mcrm.adapter.model.dto.CompanyInfoDTO;
import com.zte.mcrm.adapter.model.dto.CustomerApproveResult;
import com.zte.mcrm.adapter.model.vo.IndustryMappingBetweenLineAndSs;
import com.zte.mcrm.adapter.service.CustomerInfoService;
import com.zte.mcrm.adapter.service.ICpcServiceApi;
import com.zte.mcrm.adapter.service.PrmService;
import com.zte.mcrm.channel.constant.*;
import com.zte.mcrm.channel.model.dto.CustomerInfoDTO;
import com.zte.mcrm.channel.model.entity.OpportunityDetail;
import com.zte.mcrm.channel.model.entity.OpptyCustomerCreateRecord;
import com.zte.mcrm.channel.model.mapper.OpptyCustomerCreateRecordMapper;
import com.zte.mcrm.channel.service.OpptyCustomerCreateRecordService;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.framework.exception.ErrorCode;
import com.zte.mcrm.common.framework.exception.ErrorCodeException;
import com.zte.mcrm.common.upload.model.entity.ComUploadFile;
import com.zte.mcrm.common.upload.model.entity.SimpleUploadFileInfo;
import com.zte.mcrm.common.upload.model.entity.UploadFileMapper;
import com.zte.mcrm.common.upload.service.UploadFileService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.zte.mcrm.channel.constant.OpportunityConstant.APPROVING;

@Service
public class CustomerCreateSupport {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomerCreateSupport.class);


    @Autowired
    private EmdmService emdmService;
    @Autowired
    private PrmService prmService;

    @Autowired
    private UploadFileService uploadFileService;

    @Autowired
    private ICpcServiceApi iCpcServiceApi;

    @Autowired
    private OpptyCustomerCreateRecordService opptyCustomerCreateRecordService;

    @Autowired
    private CustomerInfoService customerInfoService;

    /**
     * 查找商机行业映射的SS行业
     * @param customerInfoDTO
     * @return
     */
    public IndustryMappingBetweenLineAndSs getIndustryMappingBetweenLineAndSs(CustomerInfoDTO customerInfoDTO) {
        Optional<IndustryMappingBetweenLineAndSs> customerSystemIndustryMapping = prmService.getCustomerSystemIndustryMapping(customerInfoDTO.getFinalCustomerParentTrade(), customerInfoDTO.getFinalCustomerChildTrade());
        if (!customerSystemIndustryMapping.isPresent()) {
            throw new ErrorCodeException(ErrorCode.UniversalErrorEnum.OU3183, customerInfoDTO.getFinalCustomerParentTrade(), customerInfoDTO.getFinalCustomerChildTrade());
        }
        IndustryMappingBetweenLineAndSs industryMappingBetweenLineAndSs = customerSystemIndustryMapping.get();
        return industryMappingBetweenLineAndSs;
    }

    /**
     * 查询公司信息
     * @param customerInfoDTO
     * @return
     */
    public CompanyInfoDTO queryCompanyInfo(CustomerInfoDTO customerInfoDTO) {
        try {
            CompanyInfoDTO companyInfo = iCpcServiceApi.searchCompanyInfo(customerInfoDTO.getLastAccName());
            if (companyInfo == null) {
                throw new IllegalStateException("company info is empty");
            }
            return companyInfo;
        } catch (Exception e) {
            LOGGER.error("querying company:{} info failed, error:{}!", customerInfoDTO.getLastAccName(), e.getMessage(), e);
            throw new ErrorCodeException(ErrorCode.UniversalErrorEnum.OU3173, customerInfoDTO.getLastAccName());
        }
    }

    /**
     * 获取城市编码
     * @param cityName
     * @return
     */
    public  String getCityCode(String cityName) {
        try {
            String cityCode = emdmService.getCityCode(cityName);
            if (StringUtils.isBlank(cityCode)) {
                throw new IllegalStateException("city Code of " + cityName + "doesn't exist");
            }
            return cityCode;
        } catch (Exception e) {
            LOGGER.error("querying city code of {} failed, error:{}", cityName, e.getMessage(), e);
            throw new ErrorCodeException(ErrorCode.UniversalErrorEnum.OU3163, cityName);
        }
    }

    /**
     * 保存最终用户附件
     * @param recordId
     * @param attachments
     */
    public void saveOrgAttachments(String recordId, List<SimpleUploadFileInfo> attachments) {
        for (SimpleUploadFileInfo item : attachments) {
            ComUploadFile comUploadFile = UploadFileMapper.INSTANCE.simpleInfoToUploadFile(item);
            if (Objects.equals(Boolean.TRUE,item.needInsertRecord())) {
                uploadFileService.insert(comUploadFile);
            } else {
                comUploadFile.setBillId(recordId);
                uploadFileService.update(comUploadFile);
            }
        }
    }

    public OpportunityErrorEnum getAccountCreateStatusResult(AccountInfo accountInfo) {
        String frozenFlag = accountInfo.getFrozenFlag();
        if (FrozenFlagEnum.WAKE_UP_APPROVING_CUSTOMER.isMe(frozenFlag)) {
            return OpportunityErrorEnum.SG2031;
        }
        if (FrozenFlagEnum.SILENT_CUSTOMER.isMe(frozenFlag)) {
            return OpportunityErrorEnum.SG2051;
        }
        String activeStatusCode = accountInfo.getActiveStatusCode();
        // 合并客户
        boolean mergeCustomer = AcctMergeFlagEnum.mergeCustomer(accountInfo.getAcctMergeFlag());
        // 生效
        if (AccountStatusEnum.ACCT_ACTIVE_STATUS.isMe(activeStatusCode)) {
            if (!mergeCustomer) {
                return OpportunityErrorEnum.SG3011;
            }
            if (mergeCustomer) {
                return OpportunityErrorEnum.SG2011;
            }
        }
        // 审批中
        if (AccountStatusEnum.APPROVING.isMe(activeStatusCode)) {
            return OpportunityErrorEnum.SG2031;
        }
        // 失效
        if (AccountStatusEnum.ACCT_INACTIVE_STATUS.isMe(activeStatusCode)) {
            CustomerApproveResult customerApproveResult = customerInfoService.queryCustomerCreateProcessStatus(accountInfo.getAccountNum());
            if (CustomerApprovalStatusEnum.hasEffectApproval(customerApproveResult.getApproveStatus())) {
                return OpportunityErrorEnum.SG2031;
            }
            return OpportunityErrorEnum.SG2051;
        }
        return OpportunityErrorEnum.SG2041;
    }

    /**
     * 保存创建客户记录
     * @param customerInfoDTO
     * @param customerStatus
     * @return
     */
    public OpptyCustomerCreateRecord saveCustomerCreateRecord(CustomerInfoDTO customerInfoDTO, String customerStatus) {
        OpptyCustomerCreateRecord opptyCustomerCreateRecord = OpptyCustomerCreateRecordMapper.INSTANCE.transFrom(customerInfoDTO);
        opptyCustomerCreateRecord.setStatus(customerStatus);
        opptyCustomerCreateRecordService.insert(opptyCustomerCreateRecord);
        return opptyCustomerCreateRecord;
    }

    /**
     * 记录record表
     * @param opportunityDetail
     */
    public void saveCustomerCreateRecord(OpportunityDetail opportunityDetail) {
        // 重复判断
        Map<String, Object> params = new HashMap<>();
        params.put("optyId", opportunityDetail.getRowId());
        params.put("xLastAccName", opportunityDetail.getLastAccName());
        params.put("enabledFlag", CommonConst.Y);
        long count = opptyCustomerCreateRecordService.getCount(params);
        // 写入记录
        if (count == 0) {
            OpptyCustomerCreateRecord opptyCustomerCreateRecord = OpptyCustomerCreateRecord.builder()
                    .optyId(opportunityDetail.getRowId())
                    .customerName(opportunityDetail.getCustomerName())
                    .crmCustomerCode(opportunityDetail.getCrmCustomerCode())
                    .xLastAccId(opportunityDetail.getLastAccId())
                    .xLastAccName(opportunityDetail.getLastAccName())
                    .deptNo(opportunityDetail.getDeptNo())
                    .status(APPROVING)
                    .finalCustomerParentTrade(opportunityDetail.getFinalCustomerParentTrade())
                    .finalCustomerChildTrade(opportunityDetail.getFinalCustomerChildTrade())
                    .businessManagerId(opportunityDetail.getBusinessManagerId())
                    .businessManagerName(opportunityDetail.getBusinessManagerName())
                    .build();
            opptyCustomerCreateRecordService.insert(opptyCustomerCreateRecord);
        }
    }
}
