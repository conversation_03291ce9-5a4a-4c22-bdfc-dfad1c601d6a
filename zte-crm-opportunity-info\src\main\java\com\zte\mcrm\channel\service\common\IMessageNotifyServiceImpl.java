package com.zte.mcrm.channel.service.common;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.channel.constant.OpportunityNotifyMsgEnum;
import com.zte.mcrm.channel.model.dto.OpportunityMsgNotifyDTO;
import com.zte.mcrm.channel.model.entity.OpportunityMailEntity;
import com.zte.mcrm.channel.service.channel.IOpportunityService;
import com.zte.mcrm.channel.util.UrlEncodeUtils;
import com.zte.mcrm.common.business.service.MessageService;
import com.zte.mcrm.common.model.ComMsgForwardDTO;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.mcrm.common.util.EntityTransformUtils;
import com.zte.mcrm.logger.bussiness.service.LoggerService;
import com.zte.springbootframe.config.ConfigByEnvironment;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

import static com.zte.mcrm.channel.constant.OpportunityConstant.MSG_MAIL_TYPE;
import static com.zte.mcrm.channel.constant.OpportunityConstant.MSG_NOTIFY_TYPE;

@Service
@Slf4j
public class IMessageNotifyServiceImpl implements IMessageNotifyService {

    @Resource
    private MessageService messageService;

    @Resource
    private LoggerService loggerService;

    @Resource
    @Lazy
    private IOpportunityService opportunityService;

    @Resource
    private ConfigByEnvironment configByEnvironment;

    @Resource
    Validator validator;

    @Value("${opportunity.internalui.url}")
    private String internaluiUrl;
    @Value("${opportunity.ichannelui.url}")
    private String ichanneluiUrl;
    @Value("${prmui.url}")
    private String prmUrl;
    @Value("${ipartnerui.url}")
    private String ipartnerUrl;

    @Override
    public void sendMessage(OpportunityMsgNotifyDTO opportunityMsgNotifyDTO) {
        Set<ConstraintViolation<OpportunityMsgNotifyDTO>> result = validator.validate(opportunityMsgNotifyDTO);
        if (CollectionUtils.isNotEmpty(result)) {
            log.error("通知消息发送参数校验不通过,result:{}", result);
            throw new BusiException(RetCode.VALIDATIONERROR_CODE, "param.validation.fail");
        }
        // 收件人和抄送人同时为空,则返回
        if (CollectionUtils.isEmpty(opportunityMsgNotifyDTO.getMailToList()) && CollectionUtils.isEmpty(opportunityMsgNotifyDTO.getMailCcList())) {
            log.error("收件人和抄送人不能同时为空,result:{}", result);
            return;
        }
        try {
            OpportunityMailEntity opportunityMailEntity = opportunityService.getOpptyMailEntity(opportunityMsgNotifyDTO.getRowId());
            log.info("商机通知发送,opportunityMailEntity转换后:{}", opportunityMailEntity);
            if (StringUtils.isNotBlank(opportunityMsgNotifyDTO.getMsgId().getMailUrl())) {
                String linkUrl = getLinkUrl(opportunityMsgNotifyDTO.getMsgId(), opportunityMailEntity);
                opportunityMailEntity.setLinkUrlCn(linkUrl);
                opportunityMailEntity.setLinkUrlEn(linkUrl);
            }

            Map<String, String> argsStrMap = getArgsStrMap(opportunityMailEntity);

            ComMsgForwardDTO.ComMsgForwardDTOBuilder comMsgForwardDTOBuilder = ComMsgForwardDTO.builder()
                    .msgId(opportunityMsgNotifyDTO.getMsgId().name())
                    .to(opportunityMsgNotifyDTO.getMailToList())
                    .cc(opportunityMsgNotifyDTO.getMailCcList())
                    .type(opportunityMsgNotifyDTO.getType())
                    .args(argsStrMap);

            messageService.sendMessageAsync(comMsgForwardDTOBuilder.build());
        } catch (Exception e) {
            loggerService.saveLogger(opportunityMsgNotifyDTO.toString(), OpportunityConstant.SEND_EMAIL_LOG_TIP);
            log.error("发送商机通知异常,opportunityMsgNotifyDTO:{}", opportunityMsgNotifyDTO, e);
        }
    }

    @Override
    public Boolean sendMessageThroughMailAndNotice(OpportunityMsgNotifyDTO opportunityMsgNotifyDTO) {
        opportunityMsgNotifyDTO.setType(MSG_MAIL_TYPE);
        sendMessage(opportunityMsgNotifyDTO);
        CollUtil.addAll(opportunityMsgNotifyDTO.getMailToList(), opportunityMsgNotifyDTO.getMailCcList());
        opportunityMsgNotifyDTO.setType(MSG_NOTIFY_TYPE);
        sendMessage(opportunityMsgNotifyDTO);
        return Boolean.TRUE;
    }

    private Map<String, String> getArgsStrMap(OpportunityMailEntity opportunityMailEntity) {
        Map<String, Object> argsMap = EntityTransformUtils.objectToMapParamsExcludeFields(opportunityMailEntity, new HashSet<>(Arrays.asList("modelCode", "modelType", "mailToList", "mailCcList")));
        Map<String, String> argsStrMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : argsMap.entrySet()) {
            Object value = entry.getValue();
            argsStrMap.put(entry.getKey(), value.toString());
        }
        return argsStrMap;
    }

    private String getLinkUrl(OpportunityNotifyMsgEnum notifyMsgEnum, OpportunityMailEntity opportunityMailEntity) throws UnsupportedEncodingException {
        String linkUrl = configByEnvironment.get(notifyMsgEnum.getMailUrl()).orElse(null);
        if (StringUtils.isBlank(linkUrl)) {
            return linkUrl;
        }
        linkUrl = CommonUtils.getStringReplaceEl(linkUrl, (JSONObject) JSON.toJSON(opportunityMailEntity));
        if (linkUrl.startsWith(ichanneluiUrl)) {
            linkUrl = ipartnerUrl + UrlEncodeUtils.unescapeAndBtoa(URLEncoder.encode(linkUrl, "UTF-8"));
        } else if (linkUrl.startsWith(internaluiUrl)) {
            linkUrl = prmUrl + URLEncoder.encode(linkUrl, "UTF-8");
        }
        return linkUrl;
    }
}
