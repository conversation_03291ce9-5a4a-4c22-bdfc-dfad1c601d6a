<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.mcrm.common.access.dao.LockDao">
<insert id="addLock" parameterType="com.zte.mcrm.common.access.vo.LockVO">
		insert into cx_method_lock
		(
			row_id,
			created_by,
			created,
			last_upd_by,
			last_upd,
			method_name
		)
		values
		(
			#{id},
			#{empId},
			sysdate(),
			#{empId},
			sysdate(),
			#{methodName}
		)
	</insert>
	
	<delete id="deleteLock">
		delete from cx_method_lock where method_name = #{methodName}
	</delete>


	<select id="getApprovedBy" resultType="java.lang.String">
		SELECT APPROVE_USER_ID FROM cx_appr_opp_ln WHERE APPR_OPR_HEAD_ID=#{approveId}
	</select>

	<update id="invalidOftenSearchByBizType">
		update COMMONMANAGER.COMMON_OFTENSEARCH
		set ENABEL_FLAG = 'N'
		where BIZ_TYPE = #{bizType}
	</update>
</mapper>