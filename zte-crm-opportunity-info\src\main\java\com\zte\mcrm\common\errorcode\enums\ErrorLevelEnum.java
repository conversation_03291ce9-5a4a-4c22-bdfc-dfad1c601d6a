package com.zte.mcrm.common.errorcode.enums;

/**
 * 错误级别枚举类
 * <AUTHOR>
 * @date 2023/5/26
 */
public enum ErrorLevelEnum {
    /**
     * 提示
     */
    INFO("1"),
    /**
     * 警告
     */
    WARN("2"),
    /**
     * 错误
     */
    ERROR("3"),
    /**
     * 致命
     */
    FATAL("4");

    private String code;

    ErrorLevelEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
