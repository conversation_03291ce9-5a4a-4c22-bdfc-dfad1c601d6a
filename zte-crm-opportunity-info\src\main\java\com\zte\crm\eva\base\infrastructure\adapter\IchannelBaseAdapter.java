package com.zte.crm.eva.base.infrastructure.adapter;

import com.zte.crm.eva.base.domain.pseudocode.model.PseudocodeRule;
import com.zte.crm.eva.base.domain.universal.CommonTableMap;
import com.zte.crm.eva.base.infrastructure.client.model.PrmQuickCodeValue;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.model.vo.BulkCodeValuesByOneTypeVo;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface IchannelBaseAdapter {

    /**
     * 根据动态json文件返回数据格式
     * @param fileName
     * @return
     */
    CommonTableMap getTableMap(String fileName) throws Exception;

    /**
     * 根据模块和规则名获取伪代码脚本
     * @param module
     * @param ruleName
     * @return
     */
    List<PseudocodeRule> getPseudocodeRule(String module, String ruleName);

    /**
     * 获取系统参数配置
     * @param codeType
     * @return
     */
    List<PrmQuickCodeValue> getCodeValuesByCodeType(String codeType);

    /**
     * 批量获取有效的参数配置
     * @param codeTypeList
     * @return
     */
    List<BulkCodeValuesByOneTypeVo> getCodeValuesByCodeTypeList(List<String> codeTypeList);

    /**
     * 发送邮件
     * @param msgId
     * @param argMap
     * @param toPersonList
     */
    void sendEmail(String msgId, Map<String, String> argMap, List<String> toPersonList);

    /**
     * 发送消息
     * @param msgId
     * @param argMap
     * @param toPersonList
     * @return
     */
    boolean sendNotice(String msgId, Map<String, String> argMap, List<String> toPersonList);


    /**
     *
     * @param formData
     * @return
     */
    PageRows<Map<String, Object>> getFoundationUpdate(@RequestBody FormData<Map<String, Object>> formData);

    /**
     * 修改组织任务
     * @param queryParam 参数
     * @return Boolean
     */
    public Boolean updateFoundation(Map<String, Object> queryParam);


    /**
     * 更新记录
     * @param queryParam
     * @return
     */
    public Boolean recordFoundation(Map<String, Object> queryParam);
}
