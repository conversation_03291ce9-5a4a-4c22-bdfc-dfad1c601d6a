package com.zte.mcrm.adapter.approval.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/11/2
 */
@Data
public class ApprovalCallBackDTO implements Serializable {
    private static final long serialVersionUID = -3107085613539389111L;

    @ApiModelProperty(value = "审批流实例ID")
    private String flowInstanceId;
    @ApiModelProperty("业务id")
    private String businessId;
    @ApiModelProperty("最后一次审批意见")
    private String opinion;
    @ApiModelProperty("最后一次审批扩展意见")
    private String extOpinion;
    @ApiModelProperty(value = "最后一次审批时间")
    private Date approvalDate;
    @ApiModelProperty(value = "流程状态")
    private String status;
    @ApiModelProperty(value = "最后一次提交审批人工号")
    private String approver;
    @ApiModelProperty(value = "最后一次审批结果，同意为Y，拒绝为N")
    private String approvalResult;
    @ApiModelProperty(value = "最后审批节点id")
    private String lastEngineNodeId;
    @ApiModelProperty(value = "最后审批节点名称")
    private String approveNodeName;
    @ApiModelProperty(value = "最后审批节点编码")
    private String approveNodeCode;
    @ApiModelProperty(value = "最后审批节点类型")
    private String approveNodeType;
    @ApiModelProperty(value = "流程编码")
    private String flowCode;
}
