package com.zte.aiagent.domain.valueobject;

import com.zte.aiagent.domain.enums.FileFormatEnum;
import lombok.Value;

/**
 * 导出解析结果的文件key
 * <AUTHOR>
 * @date 2025-07-24
 */
@Value
public class ExportParsedFileKey {

    /**
     * 导出解析结果的excel文件key
     */
    String exportParsedExcelFileKey;

    /**
     * 导出解析结果的word文件key
     */
    String exportParsedWordFileKey;

    /**
     * 标黄文档导出fileKey
     */
    String yellowDocumentFileKey;

    public static ExportParsedFileKey of(String exportParsedExcelFileKey,
                                         String exportParsedWordFileKey,
                                         String yellowDocumentFileKey) {
        return new ExportParsedFileKey(exportParsedExcelFileKey, exportParsedWordFileKey, yellowDocumentFileKey);
    }

    public String findKeyByFormat(String format) {
        if (FileFormatEnum.EXCEL.getCode().equalsIgnoreCase(format)) {
            return exportParsedExcelFileKey;
        }
        if (FileFormatEnum.WORD.getCode().equalsIgnoreCase(format)) {
            return exportParsedWordFileKey;
        }
        return null;
    }
}
