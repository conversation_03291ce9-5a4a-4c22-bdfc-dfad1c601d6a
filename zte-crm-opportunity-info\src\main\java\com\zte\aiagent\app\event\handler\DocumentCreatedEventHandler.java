package com.zte.aiagent.app.event.handler;

import com.zte.aiagent.domain.event.DocumentCreatedEvent;
import com.zte.mcrm.common.framework.exception.ErrorCodeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 文档创建事件处理器
 * 处理文档创建后的业务逻辑
 */
@Component
@Slf4j
public class DocumentCreatedEventHandler {

    /**
     * 处理文档创建事件 - 启动解析流程
     * 使用 @TransactionalEventListener 确保在事务提交后执行
     */
    @EventListener
    public void handleDocumentCreatedForParsing(DocumentCreatedEvent event) {
        log.info("开始处理文档创建事件 - 启动解析: documentId={}", event.getDocumentId());

        try {
            // TODO: 异步启动文档解析流程
            // 1. 验证解析模板是否存在
            // 2. 检查解析引擎服务状态
            // 3. 提交解析任务到队列
            // 4. 更新文档状态为PARSING

            log.info("文档解析任务已提交: documentId={}", event.getDocumentId());

        } catch (ErrorCodeException e) {
            log.error("启动文档解析失败 documentId={}, errorCode={}",
                     event.getDocumentId(), e.getErrorCode(), e);
            // TODO: 更新文档状态为FAILED，记录错误信息
        } catch (Exception e) {
            log.error("启动文档解析系统异常 documentId={}", event.getDocumentId(), e);
            // TODO: 更新文档状态为FAILED，记录系统错误
        }
    }
}
