package com.zte.mcrm.common.business.service;

import com.zte.iss.misccomponents.sdk.client.LookupTypeClient;
import com.zte.iss.misccomponents.sdk.model.dto.BasLookupTypesValuesDTO;
import com.zte.iss.misccomponents.sdk.model.dto.BasLookupValuesDTO;
import com.zte.iss.misccomponents.sdk.model.param.LookupTypeBatchParam;
import com.zte.itp.msa.client.HeaderParamThreadLocalUtils;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.mcrm.channel.util.DateUtils;
import com.zte.mcrm.common.business.IFastCodeService;
import com.zte.mcrm.common.consts.CommonConstant;
import com.zte.opty.common.constants.HeadersConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName: FastCodeServiceImpl
 * @Description: 快码服务实现类
 * @author: 10288408
 * @date: 2023/8/28
 */
@Service
@Slf4j
public class FastCodeServiceImpl implements IFastCodeService {
    private static final Integer NUM_0 = 0;
    private static final String DEFAULT_STRING = "";

    @Override
    public List<BasLookupValuesDTO> getLookupInfo(String lookupType, String accessSystem, String enableFlag, String sortOrder) {
        //设置为股份租户
        Map<String, String> headerParam = HeaderParamThreadLocalUtils.getHeaderParam();
        headerParam.put(SysGlobalConst.HTTP_HEADER_X_TENANT_ID, HeadersConstants.DEFAULT_X_TENANT_ID);
        HeaderParamThreadLocalUtils.setHeaderParam(headerParam);

        List<BasLookupValuesDTO> basLookupValuesDTOS;
        LookupTypeBatchParam lookupTypeBatchParam = new LookupTypeBatchParam();
        lookupTypeBatchParam.setAccessSystem(accessSystem);
        List<String> lookupTypeList = new ArrayList<>();
        lookupTypeList.add(lookupType);
        lookupTypeBatchParam.setLookupType(lookupTypeList);
        List<BasLookupTypesValuesDTO> lookupInfoBatch = LookupTypeClient.getLookupInfoBatch(lookupTypeBatchParam);
        if (CollectionUtils.isEmpty(lookupInfoBatch)) {
            return Collections.EMPTY_LIST;
        }
        BasLookupTypesValuesDTO basLookupTypesValuesDTO = lookupInfoBatch.get(NUM_0);
        List<BasLookupValuesDTO> basLookupValueList = basLookupTypesValuesDTO.getBasLookupValueList();
        //有效性过滤
        if (StringUtils.isEmpty(enableFlag)) {
            basLookupValuesDTOS = basLookupValueList;
        }else if (StringUtils.equals(CommonConstant.COMMON_FLAG_Y, enableFlag)) {
            basLookupValuesDTOS = basLookupValueList.stream()
                    .filter(value -> isLookUpValueValid(value)).collect(Collectors.toList());
        } else {
            basLookupValuesDTOS = basLookupValueList.stream()
                    .filter(value -> !isLookUpValueValid(value)).collect(Collectors.toList());
        }
        //排序
        if (StringUtils.isEmpty(sortOrder)) {
            return basLookupValuesDTOS;
        } else if (StringUtils.equals(sortOrder, CommonConstant.SORT_ORDER_ASC)) {
            return basLookupValuesDTOS.stream()
                    .sorted(Comparator.comparing(BasLookupValuesDTO::getSortId)).collect(Collectors.toList());
        } else {
            return basLookupValuesDTOS.stream()
                    .sorted(Comparator.comparing(BasLookupValuesDTO::getSortId).reversed()).collect(Collectors.toList());
        }
    }

    @Override
    public String getLookUpValue(String lookupType, String accessSystem, String lookupCode) {
        List<BasLookupValuesDTO> lookupInfo = this.getLookupInfo(lookupType, accessSystem, null, null);
        Optional<BasLookupValuesDTO> optional = lookupInfo.stream().filter(e -> StringUtils.equals(lookupCode, e.getLookupCode())).findFirst();
        if (optional.isPresent()) {
            BasLookupValuesDTO basLookupValuesDTO = optional.get();
            return basLookupValuesDTO.getMeaning();
        }
        return DEFAULT_STRING;
    }

    @Override
    public List<BasLookupValuesDTO> getByLookUpCode(String lookupType, String accessSystem, String lookupCode) {
        List<BasLookupValuesDTO> lookupInfo = this.getLookupInfo(lookupType, accessSystem, null, null);
        return lookupInfo.stream().filter(e -> StringUtils.equals(lookupCode, e.getLookupCode())).collect(Collectors.toList());
    }

    /**
     * 根据结束时间判断快码是否有效
     * @param basLookupValuesDTO
     * @return
     */
    private boolean isLookUpValueValid(BasLookupValuesDTO basLookupValuesDTO) {
        String endDateActive = basLookupValuesDTO.getEndDateActive();
        if (StringUtils.isEmpty(endDateActive)) {
            return true;
        }
        Date endDate = DateUtils.parseDate(endDateActive, DateUtils.DATE_FORMAT_DAY);
        Date now = new Date();
        if (endDate != null && endDate.compareTo(now) < NUM_0) {
            return false;
        } else {
            return true;
        }
    }
}
