package com.zte.mcrm.common.business;

import com.zte.iss.misccomponents.sdk.model.dto.BasLookupValuesDTO;

import java.util.List;

/**
 * @ClassName: IFastCodeService
 * @Description: 快码接口
 * @author: 10288408
 * @date: 2023/8/28
 */
public interface IFastCodeService {
    /**
     * 根据系统和快码类型获取快码值
     * @param lookupType
     * @param accessSystem
     * @param enableFlag
     * @param enableFlag
     * @return sortOrder
     */
    List<BasLookupValuesDTO> getLookupInfo(String lookupType, String accessSystem, String enableFlag, String sortOrder);

    /**
     * 获取快码值
     * @param lookupType
     * @param accessSystem
     * @param lookupCode
     * @return
     */
    String getLookUpValue(String lookupType, String accessSystem, String lookupCode);

    List<BasLookupValuesDTO> getByLookUpCode(String lookupType, String accessSystem, String lookupCode);
}
