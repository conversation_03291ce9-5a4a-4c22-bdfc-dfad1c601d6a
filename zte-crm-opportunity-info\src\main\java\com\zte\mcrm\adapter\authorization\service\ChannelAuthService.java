package com.zte.mcrm.adapter.authorization.service;

import com.zte.itp.authorityclient.entity.output.ServiceData;
import com.zte.mcrm.adapter.authorization.dto.RoleInfoDTO;
import com.zte.mcrm.adapter.authorization.model.RoleInfo;

import java.util.List;

import java.util.Set;

/**
 * @Author: 10245190 DengZiqiao
 * @Date: 2021年9月28日 18:55:18
 * @Version: V1.0
 */
public interface ChannelAuthService {

    /**
     * 查询当前登录人的所有角色信息
     *
     * @return
     */
    ServiceData getRoleList();

    /**
     * 根据角色Id、模块Id、行业Id、组织Id从权限平台查找角色信息
     *
     * @param entity：角色Id、模块Id、行业Id、组织Id集合
     * @param isPrm：是否在PRM页面展示
     *             true： 返回不加密的工号
     *             false：返回加密的工号
     * @return List<RoleInfo>
     * <AUTHOR>
     * @date 2021/10/30
     */
    List<RoleInfo> getUserByRoleAndData(RoleInfoDTO entity, Boolean isPrm);

    /**
     * 查询当前登录人的角色编码集合
     */
    Set<String> getRoleCodes();

    /**
     * 根据模块Id和角色编码获取角色Id
     */
    ServiceData getRoleByRoleCodeAndModuleId(String moduleId, String roleCode, String itpValue);
}
