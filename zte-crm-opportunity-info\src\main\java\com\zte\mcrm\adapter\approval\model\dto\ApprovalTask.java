package com.zte.mcrm.adapter.approval.model.dto;

import com.zte.mcrm.adapter.approval.model.AttachedFile;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName ApprovalTask
 * @Description 审批节点
 * <AUTHOR>
 * @Date 2021/5/18 16:19
 */
@Data
public class ApprovalTask implements Serializable {

    private static final long serialVersionUID = -5433266888823274830L;

    @ApiModelProperty(value = "审批任务ID")
    private String taskId;
    @ApiModelProperty(value = "审批节点ID")
    private String nodeId;
    @ApiModelProperty(value = "审批节点类型")
    private String nodeType;
    @ApiModelProperty(value = "审批节点名称")
    private String nodeName;
    @ApiModelProperty(value = "审批人")
    private String approver;
    @ApiModelProperty(value = "审批单创建时间")
    private Date createdDate;
    @ApiModelProperty(value = "审批完成时间")
    private Date approvalDate;
    @ApiModelProperty(value = "审批结果")
    private String result;
    @ApiModelProperty(value = "审批意见描述")
    private String opinion;
    @ApiModelProperty(value = "审批扩展意见描述")
    private String extOpinion;
    @ApiModelProperty(value = "附件")
    private List<AttachedFile> attachedFiles;

    public boolean judgeIsApproving(){
        return StringUtils.isBlank(opinion)
                && StringUtils.isBlank(result)
                && Objects.isNull(approvalDate);
    }
}
