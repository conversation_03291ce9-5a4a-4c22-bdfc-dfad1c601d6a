<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zte.mcrm.channel.dao.OpportunityDetailDao" >
    <!-- 屏蔽mybatis的2级缓存  <cache />  -->

    <!-- 记录和实体映射配置 -->
    <resultMap id="BaseMap" type="com.zte.mcrm.channel.model.entity.OpportunityDetail" >
        <id column="id" property="rowId" jdbcType="VARCHAR" />
		<result column="CREATED" property="created" jdbcType="TIMESTAMP" />
		<result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR" />
		<result column="LAST_UPD" property="lastUpd" jdbcType="TIMESTAMP" />
		<result column="LAST_UPD_BY" property="lastUpdBy" jdbcType="VARCHAR" />
		<result column="NATIONAL_AREA_ID" property="nationalAreaId" jdbcType="VARCHAR" />
        <result column="NATIONAL_AREA_PROVINCE_ID" property="nationalAreaProvinceId" jdbcType="VARCHAR" />
        <result column="NATIONAL_AREA_NAME" property="nationalAreaName" jdbcType="VARCHAR" />
        <result column="NATIONAL_AREA_CITY_ID" property="nationalAreaCityId" jdbcType="VARCHAR" />
        <result column="X_TOTAL_AMOUNT" property="totalAmount" jdbcType="DECIMAL" />
        <result column="CURRENCY_ID" property="currencyId" jdbcType="VARCHAR" />
        <result column="CURRENCY_CODE" property="currencyCode" jdbcType="VARCHAR" />
        <result column="OPTY_TYPE" property="optyType" jdbcType="VARCHAR" />
        <result column="SALES_TYPE" property="salesType" jdbcType="VARCHAR" />
        <result column="FINAL_USAGE" property="finalUsage" jdbcType="VARCHAR" />
        <result column="end_user_type" property="endUserType" jdbcType="VARCHAR" />
        <result column="enduse_of_enduser" property="enduseOfEnduser" jdbcType="VARCHAR" />
        <result column="specific_customer_desc" property="specificCustomerDesc" jdbcType="VARCHAR" />
        <result column="specific_usage_desc" property="specificUsageDesc" jdbcType="VARCHAR" />
        <result column="customer" property="lastAccId" jdbcType="VARCHAR" />
        <result column="final_user" property="lastAccCode" jdbcType="VARCHAR" />
        <result column="X_AREA" property="area" jdbcType="VARCHAR" />
        <result column="CHILD_TRADE" property="childTrade" jdbcType="VARCHAR" />
        <result column="PARENT_TRADE" property="parentTrade" jdbcType="VARCHAR" />
        <result column="X_PROJECT_LABEL" property="projectLabel" jdbcType="VARCHAR" />
        <result column="SER_ACCEPT" property="serAccept" jdbcType="VARCHAR" />
        <result column="PROD_ABILITY" property="prodAbility" jdbcType="VARCHAR" />
        <result column="PROD_ABILITY_2" property="prodAbility2" jdbcType="VARCHAR" />
        <result column="PROD_ABILITY_3" property="prodAbility3" jdbcType="VARCHAR" />
        <result column="PROD_ABILITY_4" property="prodAbility4" jdbcType="VARCHAR" />
        <result column="ACCNT_RELATION" property="accntRelation" jdbcType="VARCHAR" />
        <result column="ACCNT_RELATION_2" property="accntRelation2" jdbcType="VARCHAR" />
        <result column="ACCNT_RELATION_3" property="accntRelation3" jdbcType="VARCHAR" />
        <result column="ACCNT_RELATION_4" property="accntRelation4" jdbcType="VARCHAR" />
        <result column="HARD_1" property="hard1" jdbcType="VARCHAR" />
        <result column="HARD_2" property="hard2" jdbcType="VARCHAR" />
        <result column="HARD_3" property="hard3" jdbcType="VARCHAR" />
        <result column="HARD_4" property="hard4" jdbcType="VARCHAR" />
        <result column="RATE" property="rate" jdbcType="DECIMAL" />
        <result column="RATE_2" property="rate2" jdbcType="DECIMAL" />
        <result column="RATE_3" property="rate3" jdbcType="DECIMAL" />
        <result column="RATE_4" property="rate4" jdbcType="DECIMAL" />
        <result column="ATTRIB_19" property="attrib19" jdbcType="DECIMAL" />
        <result column="ATTRIB_20" property="attrib20" jdbcType="DECIMAL" />
        <result column="ATTRIB_21" property="attrib21" jdbcType="DECIMAL" />
        <result column="ATTRIB_22" property="attrib22" jdbcType="DECIMAL" />
        <result column="MUL_DIVISION_FLG" property="mulDivisionFlg" jdbcType="CHAR" />
        <result column="OPPTY_LEVEL_MANUAL" property="opptyLevelManual" jdbcType="VARCHAR" />
        <result column="OPPTY_LEVEL_SYSTEM" property="opptyLevelSystem" jdbcType="VARCHAR" />
        <result column="acnt_investment_scale" property="custInvestAmount" jdbcType="DECIMAL" />
        <result column="DATE_2" property="date2" jdbcType="DATE" />
        <result column="opty_brief" property="notes2" jdbcType="VARCHAR" />
        <result column="IS_FROM_PRM" property="isFromPrm" jdbcType="CHAR" />
        <result column="OPPTY_SOURCE" property="opptySource" jdbcType="VARCHAR" />
        <result column="is_financing" property="fundFlg" jdbcType="VARCHAR" />
        <result column="NET_TYPE" property="netType" jdbcType="VARCHAR" />
        <result column="X_MTO_UNION" property="mtoUnion" jdbcType="VARCHAR" />
        <result column="SUCC_PROB" property="succProb" jdbcType="DECIMAL" />
        <result column="SUCC_PROB_2" property="succProb2" jdbcType="DECIMAL" />
        <result column="SUCC_PROB_3" property="succProb3" jdbcType="DECIMAL" />
        <result column="SUCC_PROB_4" property="succProb4" jdbcType="DECIMAL" />
        <result column="X_OPTY_PHASE" property="optyPhase" jdbcType="VARCHAR" />
        <result column="OPPTY_RANGE" property="opptyRange" jdbcType="DECIMAL" />
        <result column="OPPTY_RECOM" property="opptyRecom" jdbcType="VARCHAR" />
        <result column="LEAD_ID" property="leadId" jdbcType="VARCHAR" />
        <result column="TEND_TYPE" property="tendType" jdbcType="VARCHAR" />
        <result column="X_OPTY_ID" property="optyId" jdbcType="VARCHAR" />
        <result column="DATA_SOURCE" property="dataSource" jdbcType="VARCHAR" />
        <result column="par_row_id" property="parRowId" jdbcType="VARCHAR" />
        <result column="PROJECT_TYPE" property="projectType" jdbcType="VARCHAR" />
        <result column="BU_ID_2" property="buId2" jdbcType="VARCHAR" />
        <result column="OPPTY_LEVEL" property="opptyLevel" jdbcType="VARCHAR" />
        <result column="AMT" property="amt" jdbcType="DECIMAL" />
        <result column="AMT_2" property="amt2" jdbcType="DECIMAL" />
        <result column="AMT_3" property="amt3" jdbcType="DECIMAL" />
        <result column="AMT_4" property="amt4" jdbcType="DECIMAL" />
        <result column="ACCOUNT_ATTRIBUTE" property="accountAttribute" jdbcType="VARCHAR" />
        <result column="POTENTIAL_MODEL" property="potentialModel" jdbcType="VARCHAR" />
        <result column="SECOND_DEALER_ID" property="secondDealerId" jdbcType="VARCHAR" />
        <result column="x_last_acc_name" property="lastAccName" jdbcType="VARCHAR" />
        <result column="last_acc_status" property="lastAccStatus" jdbcType="TINYINT" />
        <result column="final_customer_address" property="finalCustomerAddress" jdbcType="VARCHAR" />
        <result column="final_customer_parent_trade" property="finalCustomerParentTrade" jdbcType="VARCHAR" />
        <result column="final_customer_child_trade" property="finalCustomerChildTrade" jdbcType="VARCHAR" />
        <result column="final_customer_contact_name" property="finalCustomerContactName" jdbcType="VARCHAR" />
        <result column="final_customer_contact_phone" property="finalCustomerContactPhone" jdbcType="VARCHAR" />
        <result column="final_customer_contact_email" property="finalCustomerContactEmail" jdbcType="VARCHAR" />
        <result column="project_phases_code" property="projectPhasesCode" jdbcType="VARCHAR" />
        <result column="bid_provider_name" property="bidProviderName" jdbcType="VARCHAR" />
        <result column="bidding_deadline" property="biddingDeadline" jdbcType="TIMESTAMP" />
        <result column="agency_name" property="agencyName" jdbcType="VARCHAR" />
        <result column="agency_phone" property="agencyPhone" jdbcType="VARCHAR" />
        <result column="agency_email" property="agencyEmail" jdbcType="VARCHAR" />
        <result column="business_manager_id" property="businessManagerId" jdbcType="VARCHAR" />
        <result column="business_manager_name" property="businessManagerName" jdbcType="VARCHAR" />
        <result column="director_of_psc" property="directorOfPsc" jdbcType="VARCHAR"/>
        <result column="project_desc" property="projectDesc" jdbcType="VARCHAR" />
        <result column="self_use_flag" property="selfUseFlag" jdbcType="VARCHAR" />
        <result column="crm_customer_code" property="crmCustomerCode" jdbcType="VARCHAR"/>
        <result column="customer_name" property="customerName" jdbcType="VARCHAR" />
        <result column="source_customer_name" property="sourceCustomerName" jdbcType="VARCHAR" />
        <result column="agency_level_name" property="agencyLevelName" jdbcType="VARCHAR" />
        <result column="agency_level_code" property="agencyLevelCode" jdbcType="VARCHAR" />
        <result column="reserved_field1" property="reservedField1" jdbcType="VARCHAR" />
        <result column="reserved_field2" property="reservedField2" jdbcType="VARCHAR" />
        <result column="reserved_field3" property="reservedField3" jdbcType="VARCHAR" />
        <result column="reserved_field4" property="reservedField4" jdbcType="VARCHAR" />
        <result column="reserved_field5" property="reservedField5" jdbcType="VARCHAR" />
        <result column="final_acnt_sanctioned_code" property="finalCustomerRestrictionFlag" jdbcType="VARCHAR"/>
        <result column="acnt_sanctioned_code" property="agencyRestrictionFlag" jdbcType="VARCHAR"/>
        <result column="ts_approval_number" property="tsApprovalNumber" jdbcType="VARCHAR"/>
        <result column="active_count" property="activeCount" jdbcType="INTEGER"/>
        <result column="from_active_flag" property="fromActiveFlag" jdbcType="VARCHAR"/>
        <result column="from_active_opty" property="fromActiveOpty" jdbcType="VARCHAR"/>
        <result column="enabled_flag" property="enabledFlag" jdbcType="CHAR" />

        <result column="ATTRIB_46" property="attrib46" jdbcType="VARCHAR" />
        <result column="dept_no" property="deptNo" jdbcType="VARCHAR" />
        <result column="tender_type_code" property="tenderTypeCode" jdbcType="VARCHAR" />
        <result column="GROUP_FLG" property="groupFlg" jdbcType="VARCHAR" />
        <result column="DATE_1" property="date1" jdbcType="DATE" />
        <result column="DATE_2" property="date2" jdbcType="DATE" />
        <result column="NOTES_4" property="notes4" jdbcType="VARCHAR" />

        <result column="win_rate" property="winRate" jdbcType="VARCHAR" />
        <result column="MARKET_TYPE" property="marketType" jdbcType="VARCHAR" />
        <result column="opty_attribute" property="optyAttribute" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="base_column_detail">
        t.id,
        t.CREATED ,
        t.CREATED_BY ,
        t.LAST_UPD ,
        t.LAST_UPD_BY ,
        t.NATIONAL_AREA_ID ,
        t.NATIONAL_AREA_PROVINCE_ID ,
        t.NATIONAL_AREA_NAME ,
        t.NATIONAL_AREA_CITY_ID ,
        t.X_TOTAL_AMOUNT ,
        t.CURRENCY_ID ,
        t.CURRENCY_CODE ,
        t.OPTY_TYPE ,
        t.SALES_TYPE ,
        t.FINAL_USAGE ,
        t.end_user_type ,
        t.enduse_of_enduser ,
        t.specific_customer_desc ,
        t.specific_usage_desc ,
        t.customer ,
        t.final_user ,
        t.X_AREA ,
        t.CHILD_TRADE ,
        t.PARENT_TRADE ,
        t.X_PROJECT_LABEL ,
        t.SER_ACCEPT ,
        t.PROD_ABILITY ,
        t.PROD_ABILITY_2 ,
        t.PROD_ABILITY_3 ,
        t.PROD_ABILITY_4 ,
        t.ACCNT_RELATION ,
        t.ACCNT_RELATION_2 ,
        t.ACCNT_RELATION_3 ,
        t.ACCNT_RELATION_4 ,
        t.HARD_1 ,
        t.HARD_2 ,
        t.HARD_3 ,
        t.HARD_4 ,
        t.RATE ,
        t.RATE_2 ,
        t.RATE_3 ,
        t.RATE_4 ,
        t.ATTRIB_19 ,
        t.ATTRIB_20 ,
        t.ATTRIB_21 ,
        t.ATTRIB_22 ,
        t.MUL_DIVISION_FLG ,
        t.OPPTY_LEVEL_MANUAL ,
        t.OPPTY_LEVEL_SYSTEM ,
        t.acnt_investment_scale ,
        t.opty_brief ,
        t.IS_FROM_PRM ,
        JSON_VALUE(t.opty_source, '$[0].value') OPPTY_SOURCE ,
        JSON_VALUE(t.is_financing, '$[0].value') is_financing,
        t.NET_TYPE ,
        t.X_MTO_UNION ,
        t.SUCC_PROB ,
        t.SUCC_PROB_2 ,
        t.SUCC_PROB_3 ,
        t.SUCC_PROB_4 ,
        t.X_OPTY_PHASE ,
        t.OPPTY_RANGE ,
        t.OPPTY_RECOM ,
        t.LEAD_ID ,
        t.TEND_TYPE ,
        t.X_OPTY_ID ,
        t.par_row_id ,
        t.PROJECT_TYPE ,
        t.BU_ID_2 ,
        t.OPPTY_LEVEL ,
        t.AMT ,
        t.AMT_2 ,
        t.AMT_3 ,
        t.AMT_4 ,
        t.ACCOUNT_ATTRIBUTE ,
        t.POTENTIAL_MODEL ,
        t.SECOND_DEALER_ID ,
        t.x_last_acc_name ,
        t.last_acc_status ,
        t.final_customer_address ,
        t.final_customer_parent_trade ,
        t.final_customer_child_trade ,
        t.final_customer_contact_name ,
        t.final_customer_contact_phone ,
        t.final_customer_contact_email ,
        t.project_phases_code ,
        t.bid_provider_name ,
        t.bidding_deadline ,
        t.agency_name ,
        t.agency_phone ,
        t.agency_email ,
        t.project_desc ,
        t.self_use_flag ,
        t.crm_customer_code ,
        t.customer_name ,
        t.source_crm_customer_code ,
        t.source_customer_name ,
        t.agency_level_name ,
        t.agency_level_code ,
        t.reserved_field1 ,
        t.reserved_field2 ,
        t.reserved_field3 ,
        t.reserved_field4 ,
        t.reserved_field5 ,
        t.final_acnt_sanctioned_code ,
        t.acnt_sanctioned_code ,
        t.ts_approval_number ,
        t.active_count ,
        t.from_active_flag ,
        t.from_active_opty ,
        t.enabled_flag,
        t.opty_attribute,

        MAX(CASE WHEN tm.employee_type = 1 THEN JSON_VALUE(tm.employee, '$[0].empUIID')  END) AS business_manager_id,
        MAX(CASE WHEN tm.employee_type = 1 THEN JSON_UNQUOTE(JSON_VALUE(tm.employee, '$[0].empName'))  END) AS business_manager_name,
        MAX(CASE WHEN tm.employee_type = 2 THEN JSON_VALUE(tm.employee, '$[0].empUIID')  END) AS director_of_psc,

        s.org_tree  dept_no,
        JSON_VALUE(s.bidding_type, '$[0].value') tender_type_code,
        s.opty_name ATTRIB_46,
        JSON_VALUE(s.is_opty_group, '$[0].value') GROUP_FLG,
        s.for_bid_time DATE_1,
        JSON_VALUE(s.close_reason, '$[0].value') NOTES_4,
        s.DATA_SOURCE ,
        JSON_VALUE(op.success_rate, '$[0].value') win_rate,
        op.for_sign_date DATE_2,
        op.market_type,
        s.org_tree deptNo
    </sql>

    <sql id="base_column">
        t.id,
        t.CREATED ,
        t.CREATED_BY ,
        t.LAST_UPD ,
        t.LAST_UPD_BY ,
        t.NATIONAL_AREA_ID ,
        t.NATIONAL_AREA_PROVINCE_ID ,
        t.NATIONAL_AREA_NAME ,
        t.NATIONAL_AREA_CITY_ID ,
        t.X_TOTAL_AMOUNT ,
        t.CURRENCY_ID ,
        t.CURRENCY_CODE ,
        t.OPTY_TYPE ,
        t.SALES_TYPE ,
        t.FINAL_USAGE ,
        t.end_user_type ,
        t.enduse_of_enduser ,
        t.specific_customer_desc ,
        t.specific_usage_desc ,
        t.customer ,
        t.final_user ,
        t.X_AREA ,
        t.CHILD_TRADE ,
        t.PARENT_TRADE ,
        t.X_PROJECT_LABEL ,
        t.SER_ACCEPT ,
        t.PROD_ABILITY ,
        t.PROD_ABILITY_2 ,
        t.PROD_ABILITY_3 ,
        t.PROD_ABILITY_4 ,
        t.ACCNT_RELATION ,
        t.ACCNT_RELATION_2 ,
        t.ACCNT_RELATION_3 ,
        t.ACCNT_RELATION_4 ,
        t.HARD_1 ,
        t.HARD_2 ,
        t.HARD_3 ,
        t.HARD_4 ,
        t.RATE ,
        t.RATE_2 ,
        t.RATE_3 ,
        t.RATE_4 ,
        t.ATTRIB_19 ,
        t.ATTRIB_20 ,
        t.ATTRIB_21 ,
        t.ATTRIB_22 ,
        t.MUL_DIVISION_FLG ,
        t.OPPTY_LEVEL_MANUAL ,
        t.OPPTY_LEVEL_SYSTEM ,
        t.acnt_investment_scale ,
        t.DATE_2 ,
        t.opty_brief ,
        t.IS_FROM_PRM ,
        JSON_VALUE(t.opty_source, '$[0].value') OPPTY_SOURCE ,
        JSON_VALUE(t.is_financing, '$[0].value') is_financing,
        t.NET_TYPE ,
        t.X_MTO_UNION ,
        t.SUCC_PROB ,
        t.SUCC_PROB_2 ,
        t.SUCC_PROB_3 ,
        t.SUCC_PROB_4 ,
        t.X_OPTY_PHASE ,
        t.OPPTY_RANGE ,
        t.OPPTY_RECOM ,
        t.LEAD_ID ,
        t.TEND_TYPE ,
        t.X_OPTY_ID ,

        t.par_row_id ,
        t.PROJECT_TYPE ,
        t.BU_ID_2 ,
        t.OPPTY_LEVEL ,
        t.AMT ,
        t.AMT_2 ,
        t.AMT_3 ,
        t.AMT_4 ,
        t.ACCOUNT_ATTRIBUTE ,
        t.POTENTIAL_MODEL ,
        t.SECOND_DEALER_ID ,
        t.x_last_acc_name ,
        t.last_acc_status ,
        t.final_customer_address ,
        t.final_customer_parent_trade ,
        t.final_customer_child_trade ,
        t.final_customer_contact_name ,
        t.final_customer_contact_phone ,
        t.final_customer_contact_email ,
        t.project_phases_code ,
        t.bid_provider_name ,
        t.bidding_deadline ,
        t.agency_name ,
        t.agency_phone ,
        t.agency_email ,
        t.project_desc ,
        t.self_use_flag ,
        t.crm_customer_code ,
        t.customer_name ,
        t.source_crm_customer_code ,
        t.source_customer_name ,
        t.agency_level_name ,
        t.agency_level_code ,
        t.reserved_field1 ,
        t.reserved_field2 ,
        t.reserved_field3 ,
        t.reserved_field4 ,
        t.reserved_field5 ,
        t.final_acnt_sanctioned_code ,
        t.acnt_sanctioned_code ,
        t.ts_approval_number ,
        t.active_count ,
        t.from_active_flag ,
        t.from_active_opty ,
        t.enabled_flag,
        t.opty_attribute,

        MAX(CASE WHEN tm.employee_type = 1 THEN JSON_VALUE(tm.employee, '$[0].empUIID')  END) AS business_manager_id,
        MAX(CASE WHEN tm.employee_type = 1 THEN JSON_UNQUOTE(JSON_VALUE(tm.employee, '$[0].empName'))  END) AS business_manager_name,
        MAX(CASE WHEN tm.employee_type = 2 THEN JSON_VALUE(tm.employee, '$[0].empUIID')  END) AS director_of_psc,

        s.org_tree  dept_no,
        JSON_VALUE(s.bidding_type, '$[0].value') tender_type_code,
        s.opty_name ATTRIB_46,
        JSON_VALUE(s.is_opty_group, '$[0].value') GROUP_FLG,
        s.for_bid_time DATE_1,
        JSON_VALUE(s.close_reason, '$[0].value') NOTES_4,
        s.DATA_SOURCE ,
        JSON_VALUE(op.success_rate, '$[0].value') win_rate,
        op.market_type
    </sql>


    <sql id="base_where">
        <if test="rowId != null and rowId != ''"> and t.id = #{rowId}</if>
        <if test="created != null"> and t.CREATED = #{created}</if>
        <if test="createdBy != null and createdBy != ''"> and t.CREATED_BY = #{createdBy}</if>
        <if test="lastUpd != null"> and t.LAST_UPD = #{lastUpd}</if>
        <if test="lastUpdBy != null and lastUpdBy != ''"> and t.LAST_UPD_BY = #{lastUpdBy}</if>
        <if test="nationalAreaId != null and nationalAreaId != ''"> and t.NATIONAL_AREA_ID = #{nationalAreaId}</if>
        <if test="nationalAreaProvinceId != null and nationalAreaProvinceId != ''"> and t.NATIONAL_AREA_PROVINCE_ID = #{nationalAreaProvinceId}</if>
        <if test="nationalAreaName != null and nationalAreaName != ''"> and t.NATIONAL_AREA_NAME = #{nationalAreaName}</if>
        <if test="nationalAreaCityId != null and nationalAreaCityId != ''"> and t.NATIONAL_AREA_CITY_ID = #{nationalAreaCityId}</if>
        <if test="totalAmount != null"> and t.X_TOTAL_AMOUNT = #{totalAmount}</if>
        <if test="currencyId != null and currencyId != ''"> and t.CURRENCY_ID = #{currencyId}</if>
        <if test="currencyCode != null and currencyCode != ''"> and t.CURRENCY_CODE = #{currencyCode}</if>
        <if test="optyType != null and optyType != ''"> and t.OPTY_TYPE = #{optyType}</if>
        <if test="salesType != null and salesType != ''"> and t.SALES_TYPE = #{salesType}</if>
        <if test="finalUsage != null and finalUsage != ''"> and t.FINAL_USAGE = #{finalUsage}</if>
        <if test="endUserType != null and endUserType != ''"> and t.end_user_type = #{endUserType}</if>
        <if test="enduseOfEnduser != null and enduseOfEnduser != ''"> and t.enduse_of_enduser = #{enduseOfEnduser}</if>
        <if test="specificCustomerDesc != null and specificCustomerDesc != ''"> and t.specific_customer_desc = #{specificCustomerDesc}</if>
        <if test="specificUsageDesc != null and specificUsageDesc != ''"> and t.specific_usage_desc = #{specificUsageDesc}</if>
        <if test="lastAccId != null and lastAccId != ''"> and t.customer = #{lastAccId}</if>
        <if test="area != null and area != ''"> and t.X_AREA = #{area}</if>
        <if test="childTrade != null and childTrade != ''"> and t.CHILD_TRADE = #{childTrade}</if>
        <if test="parentTrade != null and parentTrade != ''"> and t.PARENT_TRADE = #{parentTrade}</if>
        <if test="projectLabel != null and projectLabel != ''"> and t.X_PROJECT_LABEL = #{projectLabel}</if>
        <if test="serAccept != null and serAccept != ''"> and t.SER_ACCEPT = #{serAccept}</if>
        <if test="prodAbility != null and prodAbility != ''"> and t.PROD_ABILITY = #{prodAbility}</if>
        <if test="prodAbility2 != null and prodAbility2 != ''"> and t.PROD_ABILITY_2 = #{prodAbility2}</if>
        <if test="prodAbility3 != null and prodAbility3 != ''"> and t.PROD_ABILITY_3 = #{prodAbility3}</if>
        <if test="prodAbility4 != null and prodAbility4 != ''"> and t.PROD_ABILITY_4 = #{prodAbility4}</if>
        <if test="accntRelation != null and accntRelation != ''"> and t.ACCNT_RELATION = #{accntRelation}</if>
        <if test="accntRelation2 != null and accntRelation2 != ''"> and t.ACCNT_RELATION_2 = #{accntRelation2}</if>
        <if test="accntRelation3 != null and accntRelation3 != ''"> and t.ACCNT_RELATION_3 = #{accntRelation3}</if>
        <if test="accntRelation4 != null and accntRelation4 != ''"> and t.ACCNT_RELATION_4 = #{accntRelation4}</if>
        <if test="hard1 != null and hard1 != ''"> and t.HARD_1 = #{hard1}</if>
        <if test="hard2 != null and hard2 != ''"> and t.HARD_2 = #{hard2}</if>
        <if test="hard3 != null and hard3 != ''"> and t.HARD_3 = #{hard3}</if>
        <if test="hard4 != null and hard4 != ''"> and t.HARD_4 = #{hard4}</if>
        <if test="rate != null"> and t.RATE = #{rate}</if>
        <if test="rate2 != null"> and t.RATE_2 = #{rate2}</if>
        <if test="rate3 != null"> and t.RATE_3 = #{rate3}</if>
        <if test="rate4 != null"> and t.RATE_4 = #{rate4}</if>
        <if test="attrib19 != null"> and t.ATTRIB_19 = #{attrib19}</if>
        <if test="attrib20 != null"> and t.ATTRIB_20 = #{attrib20}</if>
        <if test="attrib21 != null"> and t.ATTRIB_21 = #{attrib21}</if>
        <if test="attrib22 != null"> and t.ATTRIB_22 = #{attrib22}</if>
        <if test="mulDivisionFlg != null and mulDivisionFlg != ''"> and t.MUL_DIVISION_FLG = #{mulDivisionFlg}</if>
        <if test="opptyLevelManual != null and opptyLevelManual != ''"> and t.OPPTY_LEVEL_MANUAL = #{opptyLevelManual}</if>
        <if test="opptyLevelSystem != null and opptyLevelSystem != ''"> and t.OPPTY_LEVEL_SYSTEM = #{opptyLevelSystem}</if>
        <if test="custInvestAmount != null"> and t.acnt_investment_scale = #{custInvestAmount}</if>
        <if test="date2 != null"> and t.DATE_2 = #{date2}</if>
        <if test="notes2 != null and notes2 != ''"> and t.opty_brief = #{notes2}</if>
        <if test="isFromPrm != null and isFromPrm != ''"> and t.IS_FROM_PRM = #{isFromPrm}</if>
        <if test="opptySource != null and opptySource != ''"> and JSON_VALUE(t.opty_source, '$[0].value') = #{opptySource}</if>
        <if test="fundFlg != null and fundFlg != ''"> and JSON_VALUE(t.is_financing, '$[0].value') = #{fundFlg}</if>
        <if test="netType != null and netType != ''"> and t.NET_TYPE = #{netType}</if>
        <if test="mtoUnion != null and mtoUnion != ''"> and t.X_MTO_UNION = #{mtoUnion}</if>
        <if test="succProb != null"> and t.SUCC_PROB = #{succProb}</if>
        <if test="succProb2 != null"> and t.SUCC_PROB_2 = #{succProb2}</if>
        <if test="succProb3 != null"> and t.SUCC_PROB_3 = #{succProb3}</if>
        <if test="succProb4 != null"> and t.SUCC_PROB_4 = #{succProb4}</if>
        <if test="optyPhase != null and optyPhase != ''"> and t.X_OPTY_PHASE = #{optyPhase}</if>
        <if test="opptyRange != null"> and t.OPPTY_RANGE = #{opptyRange}</if>
        <if test="opptyRecom != null and opptyRecom != ''"> and t.OPPTY_RECOM = #{opptyRecom}</if>
        <if test="leadId != null and leadId != ''"> and t.LEAD_ID = #{leadId}</if>
        <if test="tendType != null and tendType != ''"> and t.TEND_TYPE = #{tendType}</if>
        <if test="optyId != null and optyId != ''"> and t.X_OPTY_ID = #{optyId}</if>
        <if test="dataSource != null and dataSource != ''"> and s.DATA_SOURCE = #{dataSource}</if>
        <if test="parRowId != null and parRowId != ''"> and t.par_row_id = #{parRowId}</if>
        <if test="projectType != null and projectType != ''"> and t.PROJECT_TYPE = #{projectType}</if>
        <if test="buId2 != null and buId2 != ''"> and t.BU_ID_2 = #{buId2}</if>
        <if test="opptyLevel != null and opptyLevel != ''"> and t.OPPTY_LEVEL = #{opptyLevel}</if>
        <if test="amt != null"> and t.AMT = #{amt}</if>
        <if test="amt2 != null"> and t.AMT_2 = #{amt2}</if>
        <if test="amt3 != null"> and t.AMT_3 = #{amt3}</if>
        <if test="amt4 != null"> and t.AMT_4 = #{amt4}</if>
        <if test="accountAttribute != null and accountAttribute != ''"> and t.ACCOUNT_ATTRIBUTE = #{accountAttribute}</if>
        <if test="potentialModel != null and potentialModel != ''"> and t.POTENTIAL_MODEL = #{potentialModel}</if>
        <if test="secondDealerId != null and secondDealerId != ''"> and t.SECOND_DEALER_ID = #{secondDealerId}</if>
        <if test="lastAccName != null and lastAccName != ''"> and t.x_last_acc_name = #{lastAccName}</if>
        <if test="lastAccStatus != null and lastAccStatus != ''"> and t.last_acc_status = #{lastAccStatus}</if>
        <if test="finalCustomerAddress != null and finalCustomerAddress != ''"> and t.final_customer_address = #{finalCustomerAddress}</if>
        <if test="finalCustomerParentTrade != null and finalCustomerParentTrade != ''"> and t.final_customer_parent_trade = #{finalCustomerParentTrade}</if>
        <if test="finalCustomerChildTrade != null and finalCustomerChildTrade != ''"> and t.final_customer_child_trade = #{finalCustomerChildTrade}</if>
        <if test="finalCustomerContactName != null and finalCustomerContactName != ''"> and t.final_customer_contact_name = #{finalCustomerContactName}</if>
        <if test="finalCustomerContactPhone != null and finalCustomerContactPhone != ''"> and t.final_customer_contact_phone = #{finalCustomerContactPhone}</if>
        <if test="finalCustomerContactEmail != null and finalCustomerContactEmail != ''"> and t.final_customer_contact_email = #{finalCustomerContactEmail}</if>
        <if test="projectPhasesCode != null and projectPhasesCode != ''"> and t.project_phases_code = #{projectPhasesCode}</if>
        <if test="bidProviderName != null and bidProviderName != ''"> and t.bid_provider_name = #{bidProviderName}</if>
        <if test="biddingDeadline != null"> and t.bidding_deadline = #{biddingDeadline}</if>
        <if test="agencyName != null and agencyName != ''"> and t.agency_name = #{agencyName}</if>
        <if test="agencyPhone != null and agencyPhone != ''"> and t.agency_phone = #{agencyPhone}</if>
        <if test="agencyEmail != null and agencyEmail != ''"> and t.agency_email = #{agencyEmail}</if>


        <if test="projectDesc != null and projectDesc != ''"> and t.project_desc = #{projectDesc}</if>
        <if test="selfUseFlag != null and selfUseFlag != ''"> and t.self_use_flag = #{selfUseFlag}</if>
        <if test="crmCustomerCode"> and t.crm_customer_code = #{crmCustomerCode}</if>
        <if test="customerName != null and customerName != ''"> and t.customer_name = #{customerName}</if>
        <if test="agencyLevelName != null and agencyLevelName != ''"> and t.agency_level_name = #{agencyLevelName}</if>
        <if test="agencyLevelCode != null and agencyLevelCode != ''"> and t.agency_level_code = #{agencyLevelCode}</if>
        <if test="reservedField1 != null and reservedField1 != ''"> and t.reserved_field1 = #{reservedField1}</if>
        <if test="reservedField2 != null and reservedField2 != ''"> and t.reserved_field2 = #{reservedField2}</if>
        <if test="reservedField3 != null and reservedField3 != ''"> and t.reserved_field3 = #{reservedField3}</if>
        <if test="reservedField4 != null and reservedField4 != ''"> and t.reserved_field4 = #{reservedField4}</if>
        <if test="reservedField5 != null and reservedField5 != ''"> and t.reserved_field5 = #{reservedField5}</if>
        <if test="finalCustomerRestrictionFlag != null and finalCustomerRestrictionFlag != ''"> and t.final_acnt_sanctioned_code = #{finalCustomerRestrictionFlag}</if>
        <if test="agencyRestrictionFlag != null and agencyRestrictionFlag != ''"> and t.acnt_sanctioned_code = #{agencyRestrictionFlag}</if>
        <if test="tsApprovalNumber != null and tsApprovalNumber != ''"> and t.ts_approval_number = #{tsApprovalNumber}</if>
        <if test="activeCount != null and activeCount != ''"> and t.active_count = #{activeCount}</if>
        <if test="fromActiveFlag != null and fromActiveFlag != ''"> and t.from_active_flag = #{fromActiveFlag}</if>
        <if test="fromActiveOpty != null and fromActiveOpty != ''"> and t.from_active_opty = #{fromActiveOpty}</if>
        <if test="enabledFlag != null and enabledFlag != ''"> and t.enabled_flag = #{enabledFlag}</if>

        <if test="businessManagerId != null and businessManagerId != ''"> and t.business_manager_id = #{businessManagerId}</if>
        <if test="businessManagerName != null and businessManagerName != ''"> and t.business_manager_name = #{businessManagerName}</if>
        <if test="directorOfPsc != null and directorOfPsc != ''">and director_of_psc=#{directorOfPsc, jdbcType=VARCHAR} </if>

        <if test="deptNo != null and deptNo != ''"> and s.org_tree = #{deptNo}</if>
        <if test="tenderTypeCode != null and tenderTypeCode != ''"> and JSON_VALUE(s.bidding_type, '$[0].value') = #{tenderTypeCode}</if>
        <if test="attrib46 != null and attrib46 != ''"> and s.opty_name = #{attrib46}</if>
        <if test="groupFlg != null and groupFlg != ''"> and JSON_VALUE(s.is_opty_group, '$[0].value') = #{groupFlg}</if>
        <if test="date1 != null"> and s.for_bid_time = #{date1}</if>
        <if test="notes4 != null and notes4 != ''"> and JSON_VALUE(s.close_reason, '$[0].value') = #{notes4}</if>


        <if test="winRate != null and winRate != ''"> and op.success_rate = #{marketType}</if>
        <if test="marketType != null and marketType != ''"> and op.MARKET_TYPE = #{marketType}</if>
    </sql>

    <!-- 获取一条记录 -->
    <select id="get"  resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM s_opty_x t left join s_opty s on t.id = s.id
        left join s_opty_product op on s.id = op.p_id and op.business_type = 'newOpportunity' and op.is_deleted  = 0
        left join s_opty_team tm on s.id = tm.p_id AND tm.is_deleted = 0
        WHERE
        t.id=#{rowId, jdbcType=VARCHAR} and s.data_source in ('iChannel','PRM')
    </select>

    <!-- 获取符合条件的记录列表 -->
    <select id="getList" parameterType="java.util.Map" resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM s_opty_x t left join s_opty s on t.id = s.id
        left join s_opty_product op on s.id = op.p_id and op.business_type = 'newOpportunity' and op.is_deleted  = 0
        left join s_opty_team tm on s.id = tm.p_id AND tm.is_deleted = 0
        WHERE 1=1
        <include refid="base_where"/>
        and s.data_source in ('iChannel','PRM')
    </select>

    <select id="getNoCrmCustomerCodeList" resultMap="BaseMap">
        select <include refid="base_column"/>
        from s_opty_x t
        left join s_opty s on t.id = s.id
        left join s_opty_product op on s.id = op.p_id and op.business_type = 'newOpportunity' and op.is_deleted  = 0
        left join s_opty_team tm on s.id = tm.p_id AND tm.is_deleted = 0
        where (t.crm_customer_code is null or t.crm_customer_code = '')
        <choose>
            <when test="type == 'old'">
                and locate('ORG0100668', s.org_name_path)
                and t.SECOND_DEALER_ID is not null and t.SECOND_DEALER_ID != ''
            </when>
            <otherwise>
                and t.customer_name is not null and t.customer_name != ''
            </otherwise>
        </choose>
        and s.is_deleted = 0
        and s.data_source in ('iChannel','PRM')
<!--        and t.enabled_flag = 'Y'-->
    </select>

    <select id="getNoLastAccNameList" resultMap="BaseMap">
        select <include refid="base_column"/>
        from s_opty_x t
        left join s_opty s on t.id = s.id
        left join s_opty_product op on s.id = op.p_id and op.business_type = 'newOpportunity' and op.is_deleted  = 0
        left join s_opty_team tm on s.id = tm.p_id AND tm.is_deleted = 0
        where (t.x_last_acc_name is null or t.x_last_acc_name = '')
        and locate('ORG0100668', s.org_name_path)
        and t.customer is not null and t.customer != ''
        and s.is_deleted = 0 and s.data_source in ('iChannel','PRM')
<!--        and t.enabled_flag = 'Y'-->
    </select>

    <!-- 软删除一条记录 -->
    <update id="softDelete" >
        UPDATE s_opty_x
        SET enabled_flag = 'N'
        WHERE
        id = #{rowId, jdbcType=VARCHAR}
    </update>

    <!-- 删除一条记录 -->
    <delete id="delete" >
        DELETE FROM s_opty_x
        WHERE
        id = #{rowId, jdbcType=VARCHAR}
    </delete>

    <delete id="deleteByOptyIds" >
        DELETE FROM s_opty_x
        WHERE id in (
        <foreach collection ="optyIds" item="item" index= "index" separator =",">
            #{item, jdbcType=VARCHAR}
        </foreach>)
    </delete>

    <update id="softDeleteByOptyIds" >
        update s_opty_x set enabled_flag = 'N'
        WHERE id in (
        <foreach collection ="optyIds" item="item" index= "index" separator =",">
            #{item, jdbcType=VARCHAR}
        </foreach>)
    </update>

    <!--添加一条记录 -->
    <insert id="insert" parameterType="com.zte.mcrm.channel.model.entity.OpportunityDetail" >
        INSERT INTO s_opty_x
        <trim prefix="(" suffix=")" suffixOverrides=",">
		    <if test="rowId != null">id ,</if>
		    <if test="created != null">CREATED ,</if>
		    <if test="createdBy != null">CREATED_BY ,</if>
		    <if test="lastUpd != null">LAST_UPD ,</if>
		    <if test="lastUpdBy != null">LAST_UPD_BY ,</if>
		    <if test="nationalAreaId != null">NATIONAL_AREA_ID ,</if>
		    <if test="nationalAreaProvinceId != null">NATIONAL_AREA_PROVINCE_ID ,</if>
		    <if test="nationalAreaName != null">NATIONAL_AREA_NAME ,</if>
		    <if test="nationalAreaCityId != null">NATIONAL_AREA_CITY_ID ,</if>
		    <if test="totalAmount != null">X_TOTAL_AMOUNT ,</if>
		    <if test="currencyId != null">CURRENCY_ID ,</if>
		    <if test="currencyCode != null">CURRENCY_CODE ,</if>
		    <if test="optyType != null">OPTY_TYPE ,</if>
		    <if test="salesType != null">SALES_TYPE ,</if>
		    <if test="finalUsage != null">FINAL_USAGE ,</if>
		    <if test="endUserType != null">end_user_type ,</if>
		    <if test="enduseOfEnduser != null">enduse_of_enduser ,</if>
		    <if test="specificCustomerDesc != null">specific_customer_desc ,</if>
		    <if test="specificUsageDesc != null">specific_usage_desc ,</if>
		    <if test="lastAccId != null">customer ,</if>
		    <if test="area != null">X_AREA ,</if>
		    <if test="childTrade != null">CHILD_TRADE ,</if>
		    <if test="parentTrade != null">PARENT_TRADE ,</if>
		    <if test="projectLabel != null">X_PROJECT_LABEL ,</if>
		    <if test="serAccept != null">SER_ACCEPT ,</if>
		    <if test="prodAbility != null">PROD_ABILITY ,</if>
		    <if test="prodAbility2 != null">PROD_ABILITY_2 ,</if>
		    <if test="prodAbility3 != null">PROD_ABILITY_3 ,</if>
		    <if test="prodAbility4 != null">PROD_ABILITY_4 ,</if>
		    <if test="accntRelation != null">ACCNT_RELATION ,</if>
		    <if test="accntRelation2 != null">ACCNT_RELATION_2 ,</if>
		    <if test="accntRelation3 != null">ACCNT_RELATION_3 ,</if>
		    <if test="accntRelation4 != null">ACCNT_RELATION_4 ,</if>
		    <if test="hard1 != null">HARD_1 ,</if>
		    <if test="hard2 != null">HARD_2 ,</if>
		    <if test="hard3 != null">HARD_3 ,</if>
		    <if test="hard4 != null">HARD_4 ,</if>
		    <if test="rate != null">RATE ,</if>
		    <if test="rate2 != null">RATE_2 ,</if>
		    <if test="rate3 != null">RATE_3 ,</if>
		    <if test="rate4 != null">RATE_4 ,</if>
		    <if test="attrib19 != null">ATTRIB_19 ,</if>
		    <if test="attrib20 != null">ATTRIB_20 ,</if>
		    <if test="attrib21 != null">ATTRIB_21 ,</if>
		    <if test="attrib22 != null">ATTRIB_22 ,</if>
		    <if test="mulDivisionFlg != null">MUL_DIVISION_FLG ,</if>
		    <if test="opptyLevelManual != null">OPPTY_LEVEL_MANUAL ,</if>
		    <if test="opptyLevelSystem != null">OPPTY_LEVEL_SYSTEM ,</if>
		    <if test="custInvestAmount != null">acnt_investment_scale ,</if>
		    <if test="date2 != null">DATE_2 ,</if>
		    <if test="notes2 != null">opty_brief ,</if>
		    <if test="isFromPrm != null">IS_FROM_PRM ,</if>
		    <if test="opptySource != null">opty_source ,</if>
		    <if test="fundFlg != null">FUND_FLG ,</if>
		    <if test="netType != null">NET_TYPE ,</if>
		    <if test="mtoUnion != null">X_MTO_UNION ,</if>
		    <if test="succProb != null">SUCC_PROB ,</if>
		    <if test="succProb2 != null">SUCC_PROB_2 ,</if>
		    <if test="succProb3 != null">SUCC_PROB_3 ,</if>
		    <if test="succProb4 != null">SUCC_PROB_4 ,</if>
		    <if test="optyPhase != null">X_OPTY_PHASE ,</if>
		    <if test="opptyRange != null">OPPTY_RANGE ,</if>
		    <if test="opptyRecom != null">OPPTY_RECOM ,</if>
		    <if test="leadId != null">LEAD_ID ,</if>
		    <if test="tendType != null">TEND_TYPE ,</if>
		    <if test="optyId != null">X_OPTY_ID ,</if>
<!--		    <if test="dataSource != null">DATA_SOURCE ,</if>-->
		    <if test="parRowId != null">par_row_id ,</if>
		    <if test="projectType != null">PROJECT_TYPE ,</if>
		    <if test="buId2 != null">BU_ID_2 ,</if>
		    <if test="opptyLevel != null">OPPTY_LEVEL ,</if>
		    <if test="amt != null">AMT ,</if>
		    <if test="amt2 != null">AMT_2 ,</if>
		    <if test="amt3 != null">AMT_3 ,</if>
		    <if test="amt4 != null">AMT_4 ,</if>
		    <if test="accountAttribute != null">ACCOUNT_ATTRIBUTE ,</if>
		    <if test="potentialModel != null">POTENTIAL_MODEL ,</if>
		    <if test="secondDealerId != null">SECOND_DEALER_ID ,</if>
		    <if test="lastAccName != null">x_last_acc_name ,</if>
            <if test="lastAccStatus != null">last_acc_status ,</if>
		    <if test="finalCustomerAddress != null">final_customer_address ,</if>
		    <if test="finalCustomerParentTrade != null">final_customer_parent_trade ,</if>
		    <if test="finalCustomerChildTrade != null">final_customer_child_trade ,</if>
		    <if test="finalCustomerContactName != null">final_customer_contact_name ,</if>
		    <if test="finalCustomerContactPhone != null">final_customer_contact_phone ,</if>
		    <if test="finalCustomerContactEmail != null">final_customer_contact_email ,</if>
		    <if test="projectPhasesCode != null">project_phases_code ,</if>
		    <if test="bidProviderName != null">bid_provider_name ,</if>
		    <if test="biddingDeadline != null">bidding_deadline ,</if>
		    <if test="agencyName != null">agency_name ,</if>
		    <if test="agencyPhone != null">agency_phone ,</if>
		    <if test="agencyEmail != null">agency_email ,</if>
            <if test="projectDesc != null">project_desc ,</if>
		    <if test="selfUseFlag != null">self_use_flag ,</if>
            <if test="crmCustomerCode != null">crm_customer_code ,</if>
		    <if test="customerName != null">customer_name ,</if>
		    <if test="agencyLevelName != null">agency_level_name ,</if>
		    <if test="agencyLevelCode != null">agency_level_code ,</if>
		    <if test="reservedField1 != null">reserved_field1 ,</if>
		    <if test="reservedField2 != null">reserved_field2 ,</if>
		    <if test="reservedField3 != null">reserved_field3 ,</if>
		    <if test="reservedField4 != null">reserved_field4 ,</if>
		    <if test="reservedField5 != null">reserved_field5 ,</if>
		    <if test="enabledFlag != null">enabled_flag ,</if>
            <if test="tenantId != null">tenant_id ,</if>
            <if test="finalCustomerRestrictionFlag != null">final_acnt_sanctioned_code ,</if>
            <if test="agencyRestrictionFlag != null">acnt_sanctioned_code ,</if>
            <if test="tsApprovalNumber != null">ts_approval_number ,</if>
            <if test="activeCount != null">active_count ,</if>
            <if test="fromActiveFlag != null">from_active_flag ,</if>
            <if test="fromActiveOpty != null">from_active_opty ,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
    	    <if test="rowId != null">#{rowId, jdbcType=VARCHAR} ,</if>
    	    <if test="created != null">#{created, jdbcType=TIMESTAMP} ,</if>
    	    <if test="createdBy != null">#{createdBy, jdbcType=VARCHAR} ,</if>
    	    <if test="lastUpd != null">#{lastUpd, jdbcType=TIMESTAMP} ,</if>
    	    <if test="lastUpdBy != null">#{lastUpdBy, jdbcType=VARCHAR} ,</if>
    	    <if test="nationalAreaId != null">#{nationalAreaId, jdbcType=VARCHAR} ,</if>
    	    <if test="nationalAreaProvinceId != null">#{nationalAreaProvinceId, jdbcType=VARCHAR} ,</if>
    	    <if test="nationalAreaName != null">#{nationalAreaName, jdbcType=VARCHAR} ,</if>
    	    <if test="nationalAreaCityId != null">#{nationalAreaCityId, jdbcType=VARCHAR} ,</if>
    	    <if test="totalAmount != null">#{totalAmount, jdbcType=DECIMAL} ,</if>
    	    <if test="currencyId != null">#{currencyId, jdbcType=VARCHAR} ,</if>
    	    <if test="currencyCode != null">#{currencyCode, jdbcType=VARCHAR} ,</if>
    	    <if test="optyType != null">#{optyType, jdbcType=VARCHAR} ,</if>
    	    <if test="salesType != null">#{salesType, jdbcType=VARCHAR} ,</if>
    	    <if test="finalUsage != null">#{finalUsage, jdbcType=VARCHAR} ,</if>
    	    <if test="endUserType != null">#{endUserType, jdbcType=VARCHAR} ,</if>
    	    <if test="enduseOfEnduser != null">#{enduseOfEnduser, jdbcType=VARCHAR} ,</if>
    	    <if test="specificCustomerDesc != null">#{specificCustomerDesc, jdbcType=VARCHAR} ,</if>
    	    <if test="specificUsageDesc != null">#{specificUsageDesc, jdbcType=VARCHAR} ,</if>
    	    <if test="lastAccId != null">#{lastAccId, jdbcType=VARCHAR} ,</if>
    	    <if test="area != null">#{area, jdbcType=VARCHAR} ,</if>
    	    <if test="childTrade != null">#{childTrade, jdbcType=VARCHAR} ,</if>
    	    <if test="parentTrade != null">#{parentTrade, jdbcType=VARCHAR} ,</if>
    	    <if test="projectLabel != null">#{projectLabel, jdbcType=VARCHAR} ,</if>
    	    <if test="serAccept != null">#{serAccept, jdbcType=VARCHAR} ,</if>
    	    <if test="prodAbility != null">#{prodAbility, jdbcType=VARCHAR} ,</if>
    	    <if test="prodAbility2 != null">#{prodAbility2, jdbcType=VARCHAR} ,</if>
    	    <if test="prodAbility3 != null">#{prodAbility3, jdbcType=VARCHAR} ,</if>
    	    <if test="prodAbility4 != null">#{prodAbility4, jdbcType=VARCHAR} ,</if>
    	    <if test="accntRelation != null">#{accntRelation, jdbcType=VARCHAR} ,</if>
    	    <if test="accntRelation2 != null">#{accntRelation2, jdbcType=VARCHAR} ,</if>
    	    <if test="accntRelation3 != null">#{accntRelation3, jdbcType=VARCHAR} ,</if>
    	    <if test="accntRelation4 != null">#{accntRelation4, jdbcType=VARCHAR} ,</if>
    	    <if test="hard1 != null">#{hard1, jdbcType=VARCHAR} ,</if>
    	    <if test="hard2 != null">#{hard2, jdbcType=VARCHAR} ,</if>
    	    <if test="hard3 != null">#{hard3, jdbcType=VARCHAR} ,</if>
    	    <if test="hard4 != null">#{hard4, jdbcType=VARCHAR} ,</if>
    	    <if test="rate != null">#{rate, jdbcType=DECIMAL} ,</if>
    	    <if test="rate2 != null">#{rate2, jdbcType=DECIMAL} ,</if>
    	    <if test="rate3 != null">#{rate3, jdbcType=DECIMAL} ,</if>
    	    <if test="rate4 != null">#{rate4, jdbcType=DECIMAL} ,</if>
    	    <if test="attrib19 != null">#{attrib19, jdbcType=DECIMAL} ,</if>
    	    <if test="attrib20 != null">#{attrib20, jdbcType=DECIMAL} ,</if>
    	    <if test="attrib21 != null">#{attrib21, jdbcType=DECIMAL} ,</if>
    	    <if test="attrib22 != null">#{attrib22, jdbcType=DECIMAL} ,</if>
    	    <if test="mulDivisionFlg != null">#{mulDivisionFlg, jdbcType=CHAR} ,</if>
    	    <if test="opptyLevelManual != null">#{opptyLevelManual, jdbcType=VARCHAR} ,</if>
    	    <if test="opptyLevelSystem != null">#{opptyLevelSystem, jdbcType=VARCHAR} ,</if>
    	    <if test="custInvestAmount != null">#{custInvestAmount, jdbcType=DECIMAL} ,</if>
    	    <if test="date2 != null">#{date2, jdbcType=DATE} ,</if>
    	    <if test="notes2 != null">#{notes2, jdbcType=VARCHAR} ,</if>
    	    <if test="isFromPrm != null">#{isFromPrm, jdbcType=CHAR} ,</if>
    	    <if test="opptySource != null">#{opptySource, jdbcType=VARCHAR} ,</if>
    	    <if test="fundFlg != null">#{fundFlg, jdbcType=VARCHAR} ,</if>
    	    <if test="netType != null">#{netType, jdbcType=VARCHAR} ,</if>
    	    <if test="mtoUnion != null">#{mtoUnion, jdbcType=VARCHAR} ,</if>
    	    <if test="succProb != null">#{succProb, jdbcType=DECIMAL} ,</if>
    	    <if test="succProb2 != null">#{succProb2, jdbcType=DECIMAL} ,</if>
    	    <if test="succProb3 != null">#{succProb3, jdbcType=DECIMAL} ,</if>
    	    <if test="succProb4 != null">#{succProb4, jdbcType=DECIMAL} ,</if>
    	    <if test="optyPhase != null">#{optyPhase, jdbcType=VARCHAR} ,</if>
    	    <if test="opptyRange != null">#{opptyRange, jdbcType=DECIMAL} ,</if>
    	    <if test="opptyRecom != null">#{opptyRecom, jdbcType=VARCHAR} ,</if>
    	    <if test="leadId != null">#{leadId, jdbcType=VARCHAR} ,</if>
    	    <if test="tendType != null">#{tendType, jdbcType=VARCHAR} ,</if>
    	    <if test="optyId != null">#{optyId, jdbcType=VARCHAR} ,</if>
<!--    	    <if test="dataSource != null">#{dataSource, jdbcType=VARCHAR} ,</if>-->
    	    <if test="parRowId != null">#{parRowId, jdbcType=VARCHAR} ,</if>
    	    <if test="projectType != null">#{projectType, jdbcType=VARCHAR} ,</if>
    	    <if test="buId2 != null">#{buId2, jdbcType=VARCHAR} ,</if>
    	    <if test="opptyLevel != null">#{opptyLevel, jdbcType=VARCHAR} ,</if>
    	    <if test="amt != null">#{amt, jdbcType=DECIMAL} ,</if>
    	    <if test="amt2 != null">#{amt2, jdbcType=DECIMAL} ,</if>
    	    <if test="amt3 != null">#{amt3, jdbcType=DECIMAL} ,</if>
    	    <if test="amt4 != null">#{amt4, jdbcType=DECIMAL} ,</if>
    	    <if test="accountAttribute != null">#{accountAttribute, jdbcType=VARCHAR} ,</if>
    	    <if test="potentialModel != null">#{potentialModel, jdbcType=VARCHAR} ,</if>
    	    <if test="secondDealerId != null">#{secondDealerId, jdbcType=VARCHAR} ,</if>
    	    <if test="lastAccName != null">#{lastAccName, jdbcType=VARCHAR} ,</if>
            <if test="lastAccStatus != null">#{lastAccStatus, jdbcType=TINYINT} ,</if>
    	    <if test="finalCustomerAddress != null">#{finalCustomerAddress, jdbcType=VARCHAR} ,</if>
    	    <if test="finalCustomerParentTrade != null">#{finalCustomerParentTrade, jdbcType=VARCHAR} ,</if>
    	    <if test="finalCustomerChildTrade != null">#{finalCustomerChildTrade, jdbcType=VARCHAR} ,</if>
    	    <if test="finalCustomerContactName != null">#{finalCustomerContactName, jdbcType=VARCHAR} ,</if>
    	    <if test="finalCustomerContactPhone != null">#{finalCustomerContactPhone, jdbcType=VARCHAR} ,</if>
    	    <if test="finalCustomerContactEmail != null">#{finalCustomerContactEmail, jdbcType=VARCHAR} ,</if>
    	    <if test="projectPhasesCode != null">#{projectPhasesCode, jdbcType=VARCHAR} ,</if>
    	    <if test="bidProviderName != null">#{bidProviderName, jdbcType=VARCHAR} ,</if>
    	    <if test="biddingDeadline != null">#{biddingDeadline, jdbcType=TIMESTAMP} ,</if>
    	    <if test="agencyName != null">#{agencyName, jdbcType=VARCHAR} ,</if>
    	    <if test="agencyPhone != null">#{agencyPhone, jdbcType=VARCHAR} ,</if>
    	    <if test="agencyEmail != null">#{agencyEmail, jdbcType=VARCHAR} ,</if>
            <if test="projectDesc != null">#{projectDesc, jdbcType=VARCHAR} ,</if>
    	    <if test="selfUseFlag != null">#{selfUseFlag, jdbcType=VARCHAR} ,</if>
            <if test="crmCustomerCode != null">#{crmCustomerCode, jdbcType=VARCHAR} ,</if>
    	    <if test="customerName != null">#{customerName, jdbcType=VARCHAR} ,</if>
    	    <if test="agencyLevelName != null">#{agencyLevelName, jdbcType=VARCHAR} ,</if>
    	    <if test="agencyLevelCode != null">#{agencyLevelCode, jdbcType=VARCHAR} ,</if>
    	    <if test="reservedField1 != null">#{reservedField1, jdbcType=VARCHAR} ,</if>
    	    <if test="reservedField2 != null">#{reservedField2, jdbcType=VARCHAR} ,</if>
    	    <if test="reservedField3 != null">#{reservedField3, jdbcType=VARCHAR} ,</if>
    	    <if test="reservedField4 != null">#{reservedField4, jdbcType=VARCHAR} ,</if>
    	    <if test="reservedField5 != null">#{reservedField5, jdbcType=VARCHAR} ,</if>
    	    <if test="enabledFlag != null">#{enabledFlag, jdbcType=CHAR} ,</if>
            <if test="tenantId != null">#{tenantId, jdbcType=BIGINT} ,</if>
            <if test="finalCustomerRestrictionFlag != null">#{finalCustomerRestrictionFlag,jdbcType=VARCHAR} ,</if>
            <if test="agencyRestrictionFlag != null">#{agencyRestrictionFlag,jdbcType=VARCHAR} ,</if>
            <if test="tsApprovalNumber != null">#{tsApprovalNumber,jdbcType=VARCHAR} ,</if>
            <if test="activeCount != null">#{activeCount,jdbcType=INTEGER} ,</if>
            <if test="fromActiveFlag != null">#{fromActiveFlag,jdbcType=VARCHAR} ,</if>
            <if test="fromActiveOpty != null">#{fromActiveOpty,jdbcType=VARCHAR} ,</if>
        </trim>
    </insert>



    <!-- 更新一条记录 -->
    <update id="update" parameterType="com.zte.mcrm.channel.model.entity.OpportunityDetail" >
        UPDATE s_opty_x
        <set>
		    <if test="created != null">CREATED=#{created, jdbcType=TIMESTAMP} ,</if>
		    <if test="createdBy != null and createdBy != ''">CREATED_BY=#{createdBy, jdbcType=VARCHAR} ,</if>
		    <if test="lastUpd != null">LAST_UPD=#{lastUpd, jdbcType=TIMESTAMP} ,</if>
		    <if test="lastUpdBy != null and lastUpdBy != ''">LAST_UPD_BY=#{lastUpdBy, jdbcType=VARCHAR} ,</if>
		    <if test="nationalAreaId != null and nationalAreaId != ''">NATIONAL_AREA_ID=#{nationalAreaId, jdbcType=VARCHAR} ,</if>
		    <if test="nationalAreaProvinceId != null and nationalAreaProvinceId != ''">NATIONAL_AREA_PROVINCE_ID=#{nationalAreaProvinceId, jdbcType=VARCHAR} ,</if>
		    <if test="nationalAreaName != null and nationalAreaName != ''">NATIONAL_AREA_NAME=#{nationalAreaName, jdbcType=VARCHAR} ,</if>
		    <if test="nationalAreaCityId != null and nationalAreaCityId != ''">NATIONAL_AREA_CITY_ID=#{nationalAreaCityId, jdbcType=VARCHAR} ,</if>
		    <if test="totalAmount != null">X_TOTAL_AMOUNT=#{totalAmount, jdbcType=DECIMAL} ,</if>
		    <if test="currencyId != null and currencyId != ''">CURRENCY_ID=#{currencyId, jdbcType=VARCHAR} ,</if>
		    <if test="currencyCode != null and currencyCode != ''">CURRENCY_CODE=#{currencyCode, jdbcType=VARCHAR} ,</if>
		    <if test="optyType != null and optyType != ''">OPTY_TYPE=#{optyType, jdbcType=VARCHAR} ,</if>
		    <if test="salesType != null and salesType != ''">SALES_TYPE=#{salesType, jdbcType=VARCHAR} ,</if>
		    <if test="finalUsage != null and finalUsage != ''">FINAL_USAGE=#{finalUsage, jdbcType=VARCHAR} ,</if>
		    <if test="endUserType != null and endUserType != ''">end_user_type=#{endUserType, jdbcType=VARCHAR} ,</if>
		    <if test="enduseOfEnduser != null and enduseOfEnduser != ''">enduse_of_enduser=#{enduseOfEnduser, jdbcType=VARCHAR} ,</if>
		    <if test="specificCustomerDesc != null and specificCustomerDesc != ''">specific_customer_desc=#{specificCustomerDesc, jdbcType=VARCHAR} ,</if>
		    <if test="specificUsageDesc != null and specificUsageDesc != ''">specific_usage_desc=#{specificUsageDesc, jdbcType=VARCHAR} ,</if>
		    <if test="lastAccId != null and lastAccId != ''">customer=#{lastAccId, jdbcType=VARCHAR} ,</if>
		    <if test="area != null and area != ''">X_AREA=#{area, jdbcType=VARCHAR} ,</if>
		    <if test="childTrade != null and childTrade != ''">CHILD_TRADE=#{childTrade, jdbcType=VARCHAR} ,</if>
		    <if test="parentTrade != null and parentTrade != ''">PARENT_TRADE=#{parentTrade, jdbcType=VARCHAR} ,</if>
		    <if test="projectLabel != null and projectLabel != ''">X_PROJECT_LABEL=#{projectLabel, jdbcType=VARCHAR} ,</if>
		    <if test="serAccept != null and serAccept != ''">SER_ACCEPT=#{serAccept, jdbcType=VARCHAR} ,</if>
		    <if test="prodAbility != null and prodAbility != ''">PROD_ABILITY=#{prodAbility, jdbcType=VARCHAR} ,</if>
		    <if test="prodAbility2 != null and prodAbility2 != ''">PROD_ABILITY_2=#{prodAbility2, jdbcType=VARCHAR} ,</if>
		    <if test="prodAbility3 != null and prodAbility3 != ''">PROD_ABILITY_3=#{prodAbility3, jdbcType=VARCHAR} ,</if>
		    <if test="prodAbility4 != null and prodAbility4 != ''">PROD_ABILITY_4=#{prodAbility4, jdbcType=VARCHAR} ,</if>
		    <if test="accntRelation != null and accntRelation != ''">ACCNT_RELATION=#{accntRelation, jdbcType=VARCHAR} ,</if>
		    <if test="accntRelation2 != null and accntRelation2 != ''">ACCNT_RELATION_2=#{accntRelation2, jdbcType=VARCHAR} ,</if>
		    <if test="accntRelation3 != null and accntRelation3 != ''">ACCNT_RELATION_3=#{accntRelation3, jdbcType=VARCHAR} ,</if>
		    <if test="accntRelation4 != null and accntRelation4 != ''">ACCNT_RELATION_4=#{accntRelation4, jdbcType=VARCHAR} ,</if>
		    <if test="hard1 != null and hard1 != ''">HARD_1=#{hard1, jdbcType=VARCHAR} ,</if>
		    <if test="hard2 != null and hard2 != ''">HARD_2=#{hard2, jdbcType=VARCHAR} ,</if>
		    <if test="hard3 != null and hard3 != ''">HARD_3=#{hard3, jdbcType=VARCHAR} ,</if>
		    <if test="hard4 != null and hard4 != ''">HARD_4=#{hard4, jdbcType=VARCHAR} ,</if>
		    <if test="rate != null">RATE=#{rate, jdbcType=DECIMAL} ,</if>
		    <if test="rate2 != null">RATE_2=#{rate2, jdbcType=DECIMAL} ,</if>
		    <if test="rate3 != null">RATE_3=#{rate3, jdbcType=DECIMAL} ,</if>
		    <if test="rate4 != null">RATE_4=#{rate4, jdbcType=DECIMAL} ,</if>
		    <if test="attrib19 != null">ATTRIB_19=#{attrib19, jdbcType=DECIMAL} ,</if>
		    <if test="attrib20 != null">ATTRIB_20=#{attrib20, jdbcType=DECIMAL} ,</if>
		    <if test="attrib21 != null">ATTRIB_21=#{attrib21, jdbcType=DECIMAL} ,</if>
		    <if test="attrib22 != null">ATTRIB_22=#{attrib22, jdbcType=DECIMAL} ,</if>
		    <if test="mulDivisionFlg != null and mulDivisionFlg != ''">MUL_DIVISION_FLG=#{mulDivisionFlg, jdbcType=CHAR} ,</if>
		    <if test="opptyLevelManual != null and opptyLevelManual != ''">OPPTY_LEVEL_MANUAL=#{opptyLevelManual, jdbcType=VARCHAR} ,</if>
		    <if test="opptyLevelSystem != null and opptyLevelSystem != ''">OPPTY_LEVEL_SYSTEM=#{opptyLevelSystem, jdbcType=VARCHAR} ,</if>
		    <if test="custInvestAmount != null">acnt_investment_scale=#{custInvestAmount, jdbcType=DECIMAL} ,</if>
		    <if test="date2 != null">DATE_2=#{date2, jdbcType=DATE} ,</if>
		    <if test="notes2 != null and notes2 != ''">opty_brief=#{notes2, jdbcType=VARCHAR} ,</if>
		    <if test="isFromPrm != null and isFromPrm != ''">IS_FROM_PRM=#{isFromPrm, jdbcType=CHAR} ,</if>
		    <if test="opptySource != null and opptySource != ''">opty_source=#{opptySource, jdbcType=VARCHAR} ,</if>
		    <if test="fundFlg != null and fundFlg != ''">FUND_FLG=#{fundFlg, jdbcType=VARCHAR} ,</if>
		    <if test="netType != null and netType != ''">NET_TYPE=#{netType, jdbcType=VARCHAR} ,</if>
		    <if test="mtoUnion != null and mtoUnion != ''">X_MTO_UNION=#{mtoUnion, jdbcType=VARCHAR} ,</if>
		    <if test="succProb != null">SUCC_PROB=#{succProb, jdbcType=DECIMAL} ,</if>
		    <if test="succProb2 != null">SUCC_PROB_2=#{succProb2, jdbcType=DECIMAL} ,</if>
		    <if test="succProb3 != null">SUCC_PROB_3=#{succProb3, jdbcType=DECIMAL} ,</if>
		    <if test="succProb4 != null">SUCC_PROB_4=#{succProb4, jdbcType=DECIMAL} ,</if>
		    <if test="optyPhase != null and optyPhase != ''">X_OPTY_PHASE=#{optyPhase, jdbcType=VARCHAR} ,</if>
		    <if test="opptyRange != null">OPPTY_RANGE=#{opptyRange, jdbcType=DECIMAL} ,</if>
		    <if test="opptyRecom != null and opptyRecom != ''">OPPTY_RECOM=#{opptyRecom, jdbcType=VARCHAR} ,</if>
		    <if test="leadId != null and leadId != ''">LEAD_ID=#{leadId, jdbcType=VARCHAR} ,</if>
		    <if test="tendType != null and tendType != ''">TEND_TYPE=#{tendType, jdbcType=VARCHAR} ,</if>
		    <if test="optyId != null and optyId != ''">X_OPTY_ID=#{optyId, jdbcType=VARCHAR} ,</if>
<!--		    <if test="dataSource != null and dataSource != ''">DATA_SOURCE=#{dataSource, jdbcType=VARCHAR} ,</if>-->
		    <if test="parRowId != null and parRowId != ''">par_row_id=#{parRowId, jdbcType=VARCHAR} ,</if>
		    <if test="projectType != null and projectType != ''">PROJECT_TYPE=#{projectType, jdbcType=VARCHAR} ,</if>
		    <if test="buId2 != null and buId2 != ''">BU_ID_2=#{buId2, jdbcType=VARCHAR} ,</if>
		    <if test="opptyLevel != null and opptyLevel != ''">OPPTY_LEVEL=#{opptyLevel, jdbcType=VARCHAR} ,</if>
		    <if test="amt != null">AMT=#{amt, jdbcType=DECIMAL} ,</if>
		    <if test="amt2 != null">AMT_2=#{amt2, jdbcType=DECIMAL} ,</if>
		    <if test="amt3 != null">AMT_3=#{amt3, jdbcType=DECIMAL} ,</if>
		    <if test="amt4 != null">AMT_4=#{amt4, jdbcType=DECIMAL} ,</if>
		    <if test="accountAttribute != null and accountAttribute != ''">ACCOUNT_ATTRIBUTE=#{accountAttribute, jdbcType=VARCHAR} ,</if>
		    <if test="potentialModel != null and potentialModel != ''">POTENTIAL_MODEL=#{potentialModel, jdbcType=VARCHAR} ,</if>
		    <if test="secondDealerId != null and secondDealerId != ''">SECOND_DEALER_ID=#{secondDealerId, jdbcType=VARCHAR} ,</if>
		    <if test="lastAccName != null and lastAccName != ''">x_last_acc_name=#{lastAccName, jdbcType=VARCHAR} ,</if>
            <if test="lastAccStatus != null">last_acc_status=#{lastAccStatus, jdbcType=TINYINT} ,</if>
		    <if test="finalCustomerAddress != null and finalCustomerAddress != ''">final_customer_address=#{finalCustomerAddress, jdbcType=VARCHAR} ,</if>
		    <if test="finalCustomerParentTrade != null and finalCustomerParentTrade != ''">final_customer_parent_trade=#{finalCustomerParentTrade, jdbcType=VARCHAR} ,</if>
		    <if test="finalCustomerChildTrade != null and finalCustomerChildTrade != ''">final_customer_child_trade=#{finalCustomerChildTrade, jdbcType=VARCHAR} ,</if>
		    <if test="finalCustomerContactName != null and finalCustomerContactName != ''">final_customer_contact_name=#{finalCustomerContactName, jdbcType=VARCHAR} ,</if>
		    <if test="finalCustomerContactPhone != null and finalCustomerContactPhone != ''">final_customer_contact_phone=#{finalCustomerContactPhone, jdbcType=VARCHAR} ,</if>
		    <if test="finalCustomerContactEmail != null and finalCustomerContactEmail != ''">final_customer_contact_email=#{finalCustomerContactEmail, jdbcType=VARCHAR} ,</if>
		    <if test="projectPhasesCode != null and projectPhasesCode != ''">project_phases_code=#{projectPhasesCode, jdbcType=VARCHAR} ,</if>
		    <if test="bidProviderName != null and bidProviderName != ''">bid_provider_name=#{bidProviderName, jdbcType=VARCHAR} ,</if>
		    <if test="biddingDeadline != null">bidding_deadline=#{biddingDeadline, jdbcType=TIMESTAMP} ,</if>
		    <if test="agencyName != null and agencyName != ''">agency_name=#{agencyName, jdbcType=VARCHAR} ,</if>
		    <if test="agencyPhone != null and agencyPhone != ''">agency_phone=#{agencyPhone, jdbcType=VARCHAR} ,</if>
		    <if test="agencyEmail != null and agencyEmail != ''">agency_email=#{agencyEmail, jdbcType=VARCHAR} ,</if>
            <if test="projectDesc != null and projectDesc != ''">project_desc=#{projectDesc, jdbcType=VARCHAR} ,</if>
		    <if test="selfUseFlag != null and selfUseFlag != ''">self_use_flag=#{selfUseFlag, jdbcType=VARCHAR} ,</if>
            <if test="crmCustomerCode != null and crmCustomerCode != ''">crm_customer_code=#{crmCustomerCode, jdbcType=VARCHAR} ,</if>
            <if test="customerName != null and customerName != ''">customer_name=#{customerName, jdbcType=VARCHAR} ,</if>
		    <if test="agencyLevelName != null and agencyLevelName != ''">agency_level_name=#{agencyLevelName, jdbcType=VARCHAR} ,</if>
		    <if test="agencyLevelCode != null and agencyLevelCode != ''">agency_level_code=#{agencyLevelCode, jdbcType=VARCHAR} ,</if>
		    <if test="reservedField1 != null and reservedField1 != ''">reserved_field1=#{reservedField1, jdbcType=VARCHAR} ,</if>
		    <if test="reservedField2 != null and reservedField2 != ''">reserved_field2=#{reservedField2, jdbcType=VARCHAR} ,</if>
		    <if test="reservedField3 != null and reservedField3 != ''">reserved_field3=#{reservedField3, jdbcType=VARCHAR} ,</if>
		    <if test="reservedField4 != null and reservedField4 != ''">reserved_field4=#{reservedField4, jdbcType=VARCHAR} ,</if>
		    <if test="reservedField5 != null and reservedField5 != ''">reserved_field5=#{reservedField5, jdbcType=VARCHAR} ,</if>
		    <if test="finalCustomerRestrictionFlag != null and finalCustomerRestrictionFlag != ''">final_acnt_sanctioned_code=#{finalCustomerRestrictionFlag, jdbcType=VARCHAR} ,</if>
            <if test="agencyRestrictionFlag != null and agencyRestrictionFlag != ''">acnt_sanctioned_code=#{agencyRestrictionFlag, jdbcType=VARCHAR} ,</if>
            <if test="tsApprovalNumber != null and tsApprovalNumber != ''">ts_approval_number=#{tsApprovalNumber, jdbcType=VARCHAR} ,</if>
            <if test="activeCount != null">active_count=#{activeCount, jdbcType=INTEGER} ,</if>
            <if test="fromActiveFlag != null and fromActiveFlag != ''">from_active_flag=#{fromActiveFlag, jdbcType=VARCHAR} ,</if>
            <if test="fromActiveOpty != null and fromActiveOpty != ''">from_active_opty=#{fromActiveOpty, jdbcType=VARCHAR} ,</if>
            <if test="crmCustomerCode != null and crmCustomerCode != ''">crm_customer_code=#{crmCustomerCode, jdbcType=VARCHAR} ,</if>
            <if test="sourceCustomerName != null and sourceCustomerName != ''">source_customer_name=#{sourceCustomerName, jdbcType=VARCHAR} ,</if>
            <if test="sourceCrmCustomerCode != null and sourceCrmCustomerCode != ''">source_crm_customer_code=#{sourceCrmCustomerCode, jdbcType=VARCHAR} ,</if>
	    </set>
        WHERE
        id=#{rowId, jdbcType=VARCHAR}
    </update>

    <!-- 更新一条记录 -->
    <update id="updateAll" parameterType="com.zte.mcrm.channel.model.entity.OpportunityDetail" >
        UPDATE s_opty_x
        <set>
            id 	=	#{rowId, jdbcType=BIGINT} ,
            <if test="created != null">CREATED=#{created, jdbcType=TIMESTAMP} ,</if>
            <if test="createdBy != null">CREATED_BY=#{createdBy, jdbcType=VARCHAR} ,</if>
            <if test="lastUpd != null">LAST_UPD=#{lastUpd, jdbcType=TIMESTAMP} ,</if>
            <if test="lastUpdBy != null">LAST_UPD_BY=#{lastUpdBy, jdbcType=VARCHAR} ,</if>
            NATIONAL_AREA_ID 	=	#{nationalAreaId, jdbcType=VARCHAR} ,
            NATIONAL_AREA_PROVINCE_ID 	=	#{nationalAreaProvinceId, jdbcType=VARCHAR} ,
            NATIONAL_AREA_NAME 	=	#{nationalAreaName, jdbcType=VARCHAR} ,
            NATIONAL_AREA_CITY_ID 	=	#{nationalAreaCityId, jdbcType=VARCHAR} ,
            X_TOTAL_AMOUNT 	=	#{totalAmount, jdbcType=DECIMAL} ,
            CURRENCY_ID 	=	#{currencyId, jdbcType=VARCHAR} ,
            CURRENCY_CODE 	=	#{currencyCode, jdbcType=VARCHAR} ,
            OPTY_TYPE 	=	#{optyType, jdbcType=VARCHAR} ,
            SALES_TYPE 	=	#{salesType, jdbcType=VARCHAR} ,
            FINAL_USAGE 	=	#{finalUsage, jdbcType=VARCHAR} ,
            end_user_type 	=	#{endUserType, jdbcType=VARCHAR} ,
            enduse_of_enduser 	=	#{enduseOfEnduser, jdbcType=VARCHAR} ,
            specific_customer_desc 	=	#{specificCustomerDesc, jdbcType=VARCHAR} ,
            specific_usage_desc 	=	#{specificUsageDesc, jdbcType=VARCHAR} ,
            customer 	=	#{lastAccId, jdbcType=VARCHAR} ,
            X_AREA 	=	#{area, jdbcType=VARCHAR} ,
            CHILD_TRADE 	=	#{childTrade, jdbcType=VARCHAR} ,
            PARENT_TRADE 	=	#{parentTrade, jdbcType=VARCHAR} ,
            X_PROJECT_LABEL 	=	#{projectLabel, jdbcType=VARCHAR} ,
            SER_ACCEPT 	=	#{serAccept, jdbcType=VARCHAR} ,
            PROD_ABILITY 	=	#{prodAbility, jdbcType=VARCHAR} ,
            PROD_ABILITY_2 	=	#{prodAbility2, jdbcType=VARCHAR} ,
            PROD_ABILITY_3 	=	#{prodAbility3, jdbcType=VARCHAR} ,
            PROD_ABILITY_4 	=	#{prodAbility4, jdbcType=VARCHAR} ,
            ACCNT_RELATION 	=	#{accntRelation, jdbcType=VARCHAR} ,
            ACCNT_RELATION_2 	=	#{accntRelation2, jdbcType=VARCHAR} ,
            ACCNT_RELATION_3 	=	#{accntRelation3, jdbcType=VARCHAR} ,
            ACCNT_RELATION_4 	=	#{accntRelation4, jdbcType=VARCHAR} ,
            HARD_1 	=	#{hard1, jdbcType=VARCHAR} ,
            HARD_2 	=	#{hard2, jdbcType=VARCHAR} ,
            HARD_3 	=	#{hard3, jdbcType=VARCHAR} ,
            HARD_4 	=	#{hard4, jdbcType=VARCHAR} ,
            RATE 	=	#{rate, jdbcType=DECIMAL} ,
            RATE_2 	=	#{rate2, jdbcType=DECIMAL} ,
            RATE_3 	=	#{rate3, jdbcType=DECIMAL} ,
            RATE_4 	=	#{rate4, jdbcType=DECIMAL} ,
            ATTRIB_19 	=	#{attrib19, jdbcType=DECIMAL} ,
            ATTRIB_20 	=	#{attrib20, jdbcType=DECIMAL} ,
            ATTRIB_21 	=	#{attrib21, jdbcType=DECIMAL} ,
            ATTRIB_22 	=	#{attrib22, jdbcType=DECIMAL} ,
            <if test="mulDivisionFlg != null">MUL_DIVISION_FLG=#{mulDivisionFlg, jdbcType=CHAR} ,</if>
            OPPTY_LEVEL_MANUAL 	=	#{opptyLevelManual, jdbcType=VARCHAR} ,
            OPPTY_LEVEL_SYSTEM 	=	#{opptyLevelSystem, jdbcType=VARCHAR} ,
            acnt_investment_scale 	=	#{custInvestAmount, jdbcType=DECIMAL} ,
            DATE_2 	=	#{date2, jdbcType=DATE} ,
            opty_brief 	=	#{notes2, jdbcType=VARCHAR} ,
            <if test="isFromPrm != null">IS_FROM_PRM=#{isFromPrm, jdbcType=CHAR} ,</if>
            opty_source 	=	#{opptySource, jdbcType=VARCHAR} ,
            FUND_FLG 	=	#{fundFlg, jdbcType=VARCHAR} ,
            NET_TYPE 	=	#{netType, jdbcType=VARCHAR} ,
            X_MTO_UNION 	=	#{mtoUnion, jdbcType=VARCHAR} ,
            SUCC_PROB 	=	#{succProb, jdbcType=DECIMAL} ,
            SUCC_PROB_2 	=	#{succProb2, jdbcType=DECIMAL} ,
            SUCC_PROB_3 	=	#{succProb3, jdbcType=DECIMAL} ,
            SUCC_PROB_4 	=	#{succProb4, jdbcType=DECIMAL} ,
            X_OPTY_PHASE 	=	#{optyPhase, jdbcType=VARCHAR} ,
            OPPTY_RANGE 	=	#{opptyRange, jdbcType=DECIMAL} ,
            OPPTY_RECOM 	=	#{opptyRecom, jdbcType=VARCHAR} ,
            LEAD_ID 	=	#{leadId, jdbcType=VARCHAR} ,
            TEND_TYPE 	=	#{tendType, jdbcType=VARCHAR} ,
            X_OPTY_ID 	=	#{optyId, jdbcType=VARCHAR} ,
<!--            <if test="dataSource != null">DATA_SOURCE=#{dataSource, jdbcType=VARCHAR} ,</if>-->
            <if test="parRowId != null">par_row_id=#{parRowId, jdbcType=VARCHAR} ,</if>
            <if test="projectType != null">PROJECT_TYPE=#{projectType, jdbcType=VARCHAR} ,</if>
            BU_ID_2 	=	#{buId2, jdbcType=VARCHAR} ,
            OPPTY_LEVEL 	=	#{opptyLevel, jdbcType=VARCHAR} ,
            AMT 	=	#{amt, jdbcType=DECIMAL} ,
            AMT_2 	=	#{amt2, jdbcType=DECIMAL} ,
            AMT_3 	=	#{amt3, jdbcType=DECIMAL} ,
            AMT_4 	=	#{amt4, jdbcType=DECIMAL} ,
            ACCOUNT_ATTRIBUTE 	=	#{accountAttribute, jdbcType=VARCHAR} ,
            POTENTIAL_MODEL 	=	#{potentialModel, jdbcType=VARCHAR} ,
            SECOND_DEALER_ID 	=	#{secondDealerId, jdbcType=VARCHAR} ,
            x_last_acc_name 	=	#{lastAccName, jdbcType=VARCHAR} ,
            last_acc_status     =   #{lastAccStatus, jdbcType=TINYINT} ,
            final_customer_address 	=	#{finalCustomerAddress, jdbcType=VARCHAR} ,
            final_customer_parent_trade 	=	#{finalCustomerParentTrade, jdbcType=VARCHAR} ,
            final_customer_child_trade 	=	#{finalCustomerChildTrade, jdbcType=VARCHAR} ,
            final_customer_contact_name 	=	#{finalCustomerContactName, jdbcType=VARCHAR} ,
            final_customer_contact_phone 	=	#{finalCustomerContactPhone, jdbcType=VARCHAR} ,
            final_customer_contact_email 	=	#{finalCustomerContactEmail, jdbcType=VARCHAR} ,
            project_phases_code 	=	#{projectPhasesCode, jdbcType=VARCHAR} ,
            bid_provider_name 	=	#{bidProviderName, jdbcType=VARCHAR} ,
            bidding_deadline 	=	#{biddingDeadline, jdbcType=TIMESTAMP} ,
            agency_name 	=	#{agencyName, jdbcType=VARCHAR} ,
            agency_phone 	=	#{agencyPhone, jdbcType=VARCHAR} ,
            agency_email 	=	#{agencyEmail, jdbcType=VARCHAR} ,
            project_desc 	=	#{projectDesc, jdbcType=VARCHAR} ,
            self_use_flag 	=	#{selfUseFlag, jdbcType=VARCHAR} ,
            crm_customer_code=#{crmCustomerCode, jdbcType=VARCHAR} ,
            customer_name 	=	#{customerName, jdbcType=VARCHAR} ,
            agency_level_name 	=	#{agencyLevelName, jdbcType=VARCHAR} ,
            agency_level_code 	=	#{agencyLevelCode, jdbcType=VARCHAR} ,
            reserved_field1 	=	#{reservedField1, jdbcType=VARCHAR} ,
            reserved_field2 	=	#{reservedField2, jdbcType=VARCHAR} ,
            reserved_field3 	=	#{reservedField3, jdbcType=VARCHAR} ,
            reserved_field4 	=	#{reservedField4, jdbcType=VARCHAR} ,
            reserved_field5 	=	#{reservedField5, jdbcType=VARCHAR} ,
            final_acnt_sanctioned_code=#{finalCustomerRestrictionFlag, jdbcType=VARCHAR} ,
            acnt_sanctioned_code=#{agencyRestrictionFlag, jdbcType=VARCHAR} ,
            ts_approval_number=#{tsApprovalNumber, jdbcType=VARCHAR} ,
            active_count=#{activeCount, jdbcType=VARCHAR} ,
            <if test="fromActiveFlag != null">from_active_flag=#{fromActiveFlag, jdbcType=VARCHAR} ,</if>
            from_active_opty=#{fromActiveOpty, jdbcType=VARCHAR} ,
            <if test="enabledFlag != null">enabled_flag=#{enabledFlag, jdbcType=CHAR} ,</if>
        </set>
        WHERE
        id=#{rowId, jdbcType=VARCHAR}
    </update>

    <update id="markReportDataMigration">
        update s_opty_x set reserved_field1 = 'Y' where id in (
        <foreach collection ="optyIds" item="item" index= "index" separator =",">
            #{item, jdbcType=VARCHAR}
        </foreach>)
    </update>

    <!-- 翻页函数:获取符合条件的记录数 -->
    <select id="getCount" parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT count(*) FROM s_opty_x t
        left join s_opty s on t.id = s.id
        left join s_opty_product op on s.id = op.p_id and op.business_type = 'newOpportunity' and op.is_deleted  = 0
        left join s_opty_team tm on s.id = tm.p_id AND tm.is_deleted = 0
        WHERE 1=1 and s.data_source in ('iChannel','PRM')
        <include refid="base_where"/>

    </select>

    <!-- 翻页函数:获取一页的记录集 -->
    <select id="getPage" parameterType="java.util.Map" resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM s_opty_x t
        left join s_opty s on t.id = s.id
        left join s_opty_product op on s.id = op.p_id and op.business_type = 'newOpportunity' and op.is_deleted  = 0
        left join s_opty_team tm on s.id = tm.p_id AND tm.is_deleted = 0
        WHERE 1=1 and s.data_source in ('iChannel','PRM')
        <include refid="base_where"/>
        <if test="orderField != null and orderField == 'rowId'"> order by t.id
            <if test="order != null and order == 'desc'"> desc </if>
        </if>
        <if test="startRow != null and rowSize != null"> limit #{startRow},#{rowSize} </if>
    </select>

    <select id="getLastAccId" parameterType="string" resultType="string">
        SELECT customer
        FROM s_opty_x t
        WHERE
        id=#{rowId, jdbcType=VARCHAR}
    </select>

    <select id="getOpportunityDetailByLastAccStatus" resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM s_opty_x t
        LEFT JOIN s_opty s ON s.id = t.id AND s.is_deleted = 0
        left join s_opty_product op on s.id = op.p_id and op.business_type = 'newOpportunity' and op.is_deleted  = 0
        left join s_opty_team tm on s.id = tm.p_id AND tm.is_deleted = 0
        WHERE 1=1
        AND t.last_acc_status=#{lastAccStatus, jdbcType=TINYINT}
        and s.opty_status = 'reportedApprovaling'
        and s.data_source in ('iChannel','PRM')
    </select>
    <select id="getOpportunityMailEntityByRowId"
            resultType="com.zte.mcrm.channel.model.entity.OpportunityMailEntity">
        select s.id rowId,
        s.opty_status statusCd,
        com.chinese_name statusCdName,
        s.data_source dataSource,
        s.opty_code optyCd,
        s.opty_name optyName,
        s.org_tree deptNo,
        x.x_last_acc_name lastAccName,
        x.customer_name customerName,
        x.final_customer_parent_trade finalCustomerParentTrade,
        x.final_customer_child_trade finalCustomerChildTrade,
        x.X_TOTAL_AMOUNT totalAmount,
        s.create_time submitDate,
        group_concat(prod.PROD_LV2_NAME) as products
        from s_opty_x x
        left join s_opty s on s.id = x.id
        left join s_opty_product prod on prod.p_id = x.id and prod.is_deleted  = 0 and prod.business_type != 'transferProject'
        left join com_dictionary_maintain com on com.code = s.opty_status and com.type = 'opportunityStatus' and com.enabled_flag = 'Y'
        where s.id = #{rowId, jdbcType=VARCHAR} and s.is_deleted = 0 and s.data_source in ('iChannel','PRM')
<!--        and x.enabled_flag = 'Y'-->
        group by x.id
    </select>

    <update id="updateOpportunityApproveInfo" parameterType="com.zte.mcrm.channel.model.entity.OpportunityDetail" >
        UPDATE s_opty_x
        <set>
            <if test="lastUpd != null">LAST_UPD=#{lastUpd, jdbcType=TIMESTAMP} ,</if>
<!--            <if test="businessManagerId != null and businessManagerId != ''">business_manager_id=#{businessManagerId, jdbcType=VARCHAR} ,</if>-->
<!--            <if test="businessManagerName != null and businessManagerName != ''">business_manager_name=#{businessManagerName, jdbcType=VARCHAR} ,</if>-->
            <if test="tsApprovalNumber != null and tsApprovalNumber != ''">ts_approval_number=#{tsApprovalNumber, jdbcType=VARCHAR} ,</if>
        </set>
        WHERE id=#{rowId, jdbcType=VARCHAR}
    </update>
</mapper>
