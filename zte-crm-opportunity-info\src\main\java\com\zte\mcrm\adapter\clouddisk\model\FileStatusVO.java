package com.zte.mcrm.adapter.clouddisk.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FileStatusVO {
	private Boolean exists;
	private String chunks;
	public Boolean getExists() {
		return exists;
	}
	public void setExists(Boolean exists) {
		this.exists = exists;
	}
	public String getChunks() {
		return chunks;
	}
	public void setChunks(String chunks) {
		this.chunks = chunks;
	}
}
