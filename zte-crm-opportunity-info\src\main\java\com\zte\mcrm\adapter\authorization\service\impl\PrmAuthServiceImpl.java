package com.zte.mcrm.adapter.authorization.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zte.crm.eva.base.infrastructure.client.IchannelBaseFeign;
import com.zte.itp.authorityclient.client.AuthorityClient;
import com.zte.itp.authorityclient.entity.input.CommonModuleIdEntity;
import com.zte.itp.authorityclient.entity.input.CommonRoleEntity;
import com.zte.itp.authorityclient.entity.output.ServiceData;
import com.zte.itp.authorityclient.entity.output.UserVO;
import com.zte.itp.msa.core.model.IDNResponseData;
import com.zte.itp.msa.util.web.HttpHeaderUtil;
import com.zte.mcrm.adapter.authorization.dto.RoleInfoDTO;
import com.zte.mcrm.adapter.authorization.model.RoleInfo;
import com.zte.mcrm.adapter.authorization.service.PrmAuthService;
import com.zte.mcrm.adapter.authorization.service.UppAuthorityService;
import com.zte.mcrm.adapter.authorization.util.UppAuthorityUtil;
import com.zte.mcrm.adapter.constant.UcsConstant;
import com.zte.mcrm.adapter.constant.UcsSpringProperties;
import com.zte.mcrm.adapter.model.dto.AccountDetailQueryDTO;
import com.zte.mcrm.adapter.model.dto.UcsUserInfoDTO;
import com.zte.mcrm.adapter.model.vo.AccountDetail;
import com.zte.mcrm.adapter.service.ComposeService;
import com.zte.mcrm.adapter.service.IBmtUcsService;
import com.zte.mcrm.adapter.util.PolyphonePinyinUtils;
import com.zte.mcrm.common.business.service.SensitiveEncryptor;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/10/7
 */
@Service
public class PrmAuthServiceImpl implements PrmAuthService {

    private final Logger LOGGER = LoggerFactory.getLogger(PrmAuthServiceImpl.class);

    @Autowired
    private UcsSpringProperties ucsSpringProperties;
    @Autowired
    private UppAuthorityService uppAuthorityService;
    @Autowired
    private ComposeService composeService;
    @Autowired
    private IBmtUcsService iBmtUcsService;

    @Value("${encryption.secretKeySixteen}")
    private String secretKeySixteen;

    @Value("${encryption.iv}")
    private String iv;
    @Autowired
    IchannelBaseFeign ichannelBaseFeign;

    /**
     * 根据角色Id、模块Id、行业Id、组织Id查询姓名和工号
     */
    @Override
    public List<RoleInfo> getUserByRoleAndData(RoleInfoDTO entity) {
        return getUserByRoleAndData(entity, false);
    }

    /**
     * 根据角色编号集合查询用户集合-无约束
     * @param roleCodes
     * @return
     */
    public Map<String,List<UserVO>> getPersonsUnconstrained(List<String> roleCodes){
        String empNo = CommonUtils.getEmpNo();
        String token = CommonUtils.getAuthValue();
        CommonModuleIdEntity entity = new CommonModuleIdEntity();
        entity.setEmpidui(empNo);
        entity.setToken(token);
        return uppAuthorityService.queryUsersByRoleCode(entity, roleCodes);
    }

    @Override
    public List<RoleInfo> getUserByRoleAndData(RoleInfoDTO entity, Boolean isPrm) {
        String empNo = CommonUtils.getEmpNo();
        String token = CommonUtils.getAuthValue();
        entity.setEmpidui(empNo);
        entity.setToken(token);
        ServiceData serviceData;
        // 外部用户
        if (UppAuthorityUtil.determineTheUserIsExternal(empNo)) {
            String ucsItpValue = getUcsItpValue(empNo, token);
            entity.setItpValue(ucsItpValue);
            serviceData = uppAuthorityService.getUserByRoleAndData(entity);
        }else{
            // 内部员工
            serviceData = uppAuthorityService.getUserByRoleAndData(entity);
        }
        if(Objects.isNull(serviceData) || Objects.isNull(serviceData.getBo())){
            return Collections.emptyList();
        }

        LinkedHashMap bo = (LinkedHashMap)serviceData.getBo();

        List<LinkedHashMap> users = (List<LinkedHashMap>)bo.get(entity.getRoleId());
        if(CollectionUtils.isEmpty(users)){
            LOGGER.info("根据角色Id:{}、模块Id:{}、行业Id:{}、组织Id:{}，在权限平台查询结果为空！",entity.getRoleId(),entity.getModuleId(),entity.getIndustryIds(),entity.getOrgIds());
            return Collections.emptyList();
        }
        return combinationUser(users, isPrm);
    }

    @Override
    public ServiceData getRoleByRoleCodeAndModuleId(String moduleId, String roleCode, String itpValue) {
        CommonRoleEntity entity = new CommonRoleEntity();
        //当前用户
        entity.setEmpidui(CommonUtils.getEmpNo());
        entity.setRoleNameEN(roleCode);
        //token
        entity.setToken(CommonUtils.getAuthValue());
        if(StringUtils.isNotBlank(itpValue)) {
            entity.setItpValue(itpValue);
        }

        return AuthorityClient.queryRoleByNameEn(entity);
    }

    /**
     * 设置Ucs的ItpValue
     * <AUTHOR>
     * @date 2020/11/27
     * @param empNo 工号
     * @param token token
     * @return String
     */
    private String getUcsItpValue(String empNo, String token) {
        String uuid = UUID.randomUUID().toString();
        StringBuilder sb = new StringBuilder();
        sb.append(ucsSpringProperties.getSecretKey()).append(ucsSpringProperties.getAppId())
                .append(ucsSpringProperties.getAccessKey()).append(uuid);

        String authValue = DigestUtils.sha256Hex(sb.toString());
        JSONObject json = new JSONObject();
        // type是统一权限规定的关键字段，UCS用于区分是否是外部用户
        json.put("type","UCS");
        json.put("tenantId",ucsSpringProperties.getTenantId());
        json.put("appId",ucsSpringProperties.getAppId());
        json.put("authValue",authValue);
        json.put("accessKey",ucsSpringProperties.getAccessKey());
        json.put("uuid",uuid);
        json.put("token",token);
        json.put("accountId",empNo);
        return json.toString();
    }

    /**
     * 组合用户信息
     *
     * @param users 用户信息集合
     * @param isPrm 判断是否需要加密
     * @return List<RoleInfo>
     * <AUTHOR>
     * @date 2021/10/30
     */
    public List<RoleInfo> combinationUser(List<LinkedHashMap> users, Boolean isPrm) {
        List<RoleInfo> list = Lists.newArrayList();
        for (LinkedHashMap user : users) {
            RoleInfo info = new RoleInfo();
            String userName = (String) user.get("userName");
            info.setUserName(userName);
            String userNamePinYin = PolyphonePinyinUtils.changeToGetShortPinYin(userName);
            info.setUserNamePinYin(userNamePinYin);
            String userId = (String) user.get("userId");
            info.setEmpNo(userId);
            info.setEmail(getEmail(userId));
            list.add(info);
        }
        return  list.stream().sorted(Comparator.comparing(RoleInfo::getUserNamePinYin)).collect(Collectors.toList());
    }

    private String getEmail(String userId) {
        com.zte.itp.msa.core.model.ServiceData<List<UcsUserInfoDTO>> userServiceData = ichannelBaseFeign.queryAll(Lists.newArrayList(userId));
        List<UcsUserInfoDTO> userInfoByAccountIdList = userServiceData.getBo();
        if(CollectionUtils.isEmpty(userInfoByAccountIdList)) {
            LOGGER.info("未查询到账号为:{}的邮箱信息",userId);
            return null;
        }
        return userInfoByAccountIdList.get(0).getEmail();
    }

}
