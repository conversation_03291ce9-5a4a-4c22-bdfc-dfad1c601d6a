package com.zte.aiagent.infrastruction.access.repository;

import com.zte.aiagent.domain.repository.SysConfValueRepository;
import com.zte.aiagent.infrastruction.access.mapper.SysConfValueMapper;
import com.zte.aiagent.infrastruction.access.po.SysConfValuePO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 系统参数配置值数据访问仓储实现类
 * 负责系统参数配置值相关的数据访问操作
 *
 * <AUTHOR>
 */
@Service
public class SysConfValueRepositoryImpl implements SysConfValueRepository {

    @Autowired
    private SysConfValueMapper sysConfValueMapper;

    @Override
    public int insert(SysConfValuePO sysConfValue) {
        return sysConfValueMapper.insert(sysConfValue);
    }

    @Override
    public int batchInsert(List<SysConfValuePO> items) {
        return sysConfValueMapper.batchInsert(items);
    }

    @Override
    public SysConfValuePO selectByPrimaryKey(String rowId) {
        return sysConfValueMapper.selectByPrimaryKey(rowId);
    }

    @Override
    public List<SysConfValuePO> selectByCodeTypeId(String codeTypeId, Long tenantId) {
        return sysConfValueMapper.selectByCodeTypeId(codeTypeId, tenantId);
    }

    @Override
    public SysConfValuePO selectByTypeIdCodeAndTenant(String codeTypeId, String code, Long tenantId) {
        return sysConfValueMapper.selectByTypeIdCodeAndTenant(codeTypeId, code, tenantId);
    }

    @Override
    public List<SysConfValuePO> selectByParentId(String parentId, Long tenantId) {
        return sysConfValueMapper.selectByParentId(parentId, tenantId);
    }

    @Override
    public int updateByPrimaryKey(SysConfValuePO sysConfValue) {
        return sysConfValueMapper.updateByPrimaryKey(sysConfValue);
    }

    @Override
    public int logicDelete(String rowId, String lastUpdatedBy, Date lastUpdatedDate) {
        return sysConfValueMapper.logicDelete(rowId, lastUpdatedBy, lastUpdatedDate);
    }

    @Override
    public int batchLogicDelete(List<String> rowIds, String lastUpdatedBy, Date lastUpdatedDate) {
        return sysConfValueMapper.batchLogicDelete(rowIds, lastUpdatedBy, lastUpdatedDate);
    }
}
