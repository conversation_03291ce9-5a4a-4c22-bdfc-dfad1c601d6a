package com.zte.mcrm.channel.constant;

/**
 * 审批中心节点状态
 *
 * <AUTHOR>
 * @date 2021/10/11
 */
public enum ApprovalNodeStatusEnum {
    /**待审批状态 */
    ACTIVE("ACTIVE"),
    /**沟通答复场景中，处于等待答复才能审批状态 */
    ACTIVE_WAIT_ANSWER("ACTIVE_WAIT_ANSWER"),
    /**已审批 */
    COMPLETED("COMPLETED"),
    /**该任务由于其它原因被忽略 */
    IGNORE("IGNORE"),
    /**转交 */
    REASSIGN("REASSIGN"),
    /**撤销 */
    REVOKE("REVOKE"),
    /**回滚 */
    ROLLBACK("ROLL<PERSON>CK"),
    /**主持人尚不可审批状态 */
    UNACTIVE("UNACTIVE");

    private String descEn;

    public String getDescEn() {
        return descEn;
    }

    ApprovalNodeStatusEnum(String descEn) {
        this.descEn = descEn;
    }
}
