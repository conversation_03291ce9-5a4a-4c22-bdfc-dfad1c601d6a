package com.zte.mcrm.adapter.approval.service;

import com.zte.mcrm.common.util.ApprovalHeaderUtil;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @description: 审批中心头信息配置
 * @author: 10243305
 * @date: 2021/6/24 下午2:17
 */
@Component("ApprovalHeaderBuilder")
@Data
public class ApprovalHeaderBuilder {
    @Value("${approval.auth.accessKey}")
    private String accessKey;

    @Value("${approval.auth.secretKey}")
    private String secretKey;

    @Value("${approval.prefixpath}")
    private String prefixpath;

    public Map<String, String> getHeaderParams() {
        return ApprovalHeaderUtil.getHeaderParamsMap(accessKey, secretKey);
    }

    public Map<String, String> getHeaderParams(Map<String, String> headerParams) {
        return ApprovalHeaderUtil.getHeaderParamsMap(accessKey, secretKey, headerParams);
    }
}

