package com.zte.mcrm.channel.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zte.mcrm.common.annotation.EmpName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@ToString
public class OldOpportunityInfoEntity {
    @ApiModelProperty(value = "")
    private String rowId;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "")
    private Date lastUpd;
    @ApiModelProperty(value = "")
    private String lastUpdBy;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "")
    private Date created;
    @ApiModelProperty(value = "")
    private String createdBy;
    @ApiModelProperty(value = "商机编码")
    private String optyCd;
    @ApiModelProperty(value = "组织全路径")
    private String orgNamePath;
    @ApiModelProperty(value = "商机状态")
    private String statusCd;

    @ApiModelProperty(value = "商机来源")
    private String dataSource;
    @JsonFormat(pattern ="yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "预计发标/议标时间")
    private Date date1;
    @ApiModelProperty(value = "二级经销商")
    private String secondDealerId;
    @ApiModelProperty(value = "最终用户Id")
    private String lastAccId;
    @ApiModelProperty(value = "最终用户名称")
    private String lastAccName;
    @ApiModelProperty(value = "最终用户行业编码")
    private String finalCustomerParentTrade;
    @ApiModelProperty(value = "最终用户子行业编码")
    private String finalCustomerChildTrade;
    @ApiModelProperty(value = "最终用户联系人姓名")
    private String finalCustomerContactName;
    @ApiModelProperty(value = "最终用户联系人电话")
    private String finalCustomerContactPhone;

    @ApiModelProperty(value = "投资方所在地（商机所属部门）")
    private String deptNo;
    @ApiModelProperty(value = "项目当前阶段编码")
    private String projectPhasesCode;
    @ApiModelProperty(value = "项目当前阶段名称")
    private String projectPhasesName;
    @ApiModelProperty(value = "赢率")
    private String winRate;
    @ApiModelProperty(value = "招标类型编码")
    private String tenderTypeCode;
    @ApiModelProperty(value = "招标类型名称")
    private String tenderTypeName;
    @ApiModelProperty(value = "招标方全称")
    private String bidProviderName;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "竞标截止日期")
    private Date biddingDeadline;
    @ApiModelProperty(value = "中兴业务经理id")
    private String businessManagerId;
    @EmpName
    @ApiModelProperty(value = "中兴业务经理名字")
    private String businessManagerName;
    @ApiModelProperty(value = "商机概况")
    private String projectDesc;
    @ApiModelProperty(value = "渠道商客户编码")
    private String crmCustomerCode;
    @ApiModelProperty(value = "渠道商名称")
    private String customerName;

    @ApiModelProperty(value = "商机名称")
    private String attrib46;
    @ApiModelProperty(value = "商机执行地")
    private String nationalAreaId;
    @ApiModelProperty(value = "商机执行地名称")
    private String nationalAreaName;
    @ApiModelProperty(value = "预计签单金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "销售模式")
    private String salesType;
    @ApiModelProperty(value = "最终用途")
    private String finalUsage;
    @ApiModelProperty(value = "最终用户类型")
    private String endUserType;
    @ApiModelProperty(value = "最终用户的最终用途")
    private String enduseOfEnduser;
    @ApiModelProperty(value = "具体客户描述")
    private String specificCustomerDesc;
    @ApiModelProperty(value = "具体用途描述")
    private String specificUsageDesc;
    @ApiModelProperty(value = "客户Id")
    private String prDeptOuId;
    @ApiModelProperty(value = "业务范围")
    private String businessTypeCd;

}
