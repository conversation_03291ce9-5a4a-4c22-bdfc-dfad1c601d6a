package com.zte.mcrm.adapter.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 描述：最终客户VO
 * 创建时间：2021/9/17
 *
 * @author：王丹凤6396000572
 */
@Data
public class EndCustomerVO {


    @ApiModelProperty(value = "最终客户编码")
    private String endCustomerId;

    @ApiModelProperty(value = "最终客户名称")
    private String endCustomerName;

    @ApiModelProperty("客户受限制主体")
    private String restrictedParty;

    @ApiModelProperty("客户受限制主体code")
    private String restrictedPartyCode;

}
