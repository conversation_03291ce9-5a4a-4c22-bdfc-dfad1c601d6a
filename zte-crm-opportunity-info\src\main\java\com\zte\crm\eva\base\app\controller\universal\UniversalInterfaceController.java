package com.zte.crm.eva.base.app.controller.universal;

import com.zte.crm.eva.base.service.universal.UniversalService;
import com.zte.crm.eva.base.service.universal.UniversalUpdateService;
import com.zte.iss.gcsc.log.annotation.ZteLog;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import com.zte.mcrm.common.errorcode.util.ExceptionLogOperateUtil;
import com.zte.mcrm.common.framework.exception.ErrorCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import java.util.Map;


/**
 * @ClassName: UniversalInterface
 * @Description: 通用界面后端
 * <AUTHOR>
 **/
@Api(tags = "通用界面后端接口 API")
@RestController
@RequestMapping("/common/module/interface/")
public class UniversalInterfaceController {
    private static final Logger log = LoggerFactory.getLogger(UniversalInterfaceController.class);

    @Autowired
    private UniversalService universalService;

    @Resource
    private UniversalUpdateService universalUpdateService;

    @ApiOperation("通用界面新增 -post方式")
    @PostMapping(value = "insert/{fileName}")
    @ZteLog(rdcId = "DT_BO-3900750")
    public ServiceData<Boolean> insert(@RequestBody Map<String, Object> map, @PathVariable("fileName") String fileName) {
        try {
            return ServiceResultUtil.success(universalService.insertUniversal(map, fileName));
        } catch (Exception e) {
            log.error("UniversalInterfaceController-insert param:{} fileName:{}, error:{}", map, fileName, e.getMessage(), e);
            throw new BusiException(RetCode.BUSINESSERROR_CODE, "common insert error");
        }
    }

    /**
     * 插入数据初始化特殊字段
     * @param
     * @param
     */
    @ApiOperation("获取符合条件的分页记录列表，包括当页数据/记录总数,按指定属性排序 -Post方式")
    @PostMapping(value = "/getpage/{fileName}")
    public ServiceData<PageRows<Map<String, Object>>> getPage(@RequestBody FormData<Map<String, Object>> form, @PathVariable("fileName") String fileName) {
        try {
            PageRows<Map<String, Object>> pageModel = universalService.selectCommonByPage(form, fileName);
            //返回统一的服务端数据
            return ServiceResultUtil.success(pageModel);
        } catch (Exception e) {
            log.error("UniversalInterfaceController-getPage, param:{}, fileName:{}, error:{}",
                    form, fileName,e.getMessage(), e);
            throw new BusiException(RetCode.BUSINESSERROR_CODE, "common query error");
        }
    }


    @ApiOperation("通用界面修改 -post方式")
    @PostMapping(value = "/update/{fileName}")
    @ZteLog(rdcId = "DT_BO-3900750")
    public ServiceData<Boolean> update(@RequestBody Map<String, Object> map, @PathVariable("fileName") String fileName) {
        try {
            return ServiceResultUtil.success(universalService.updateUniversal(map, fileName));
        } catch (Exception e) {
            log.error("UniversalInterfaceController-update, param:{}, fileName:{}, error:{}", map, fileName,
                    e.getMessage(), e);
            throw new BusiException(RetCode.BUSINESSERROR_CODE, "common update error");
        }
    }


    @ApiOperation("通用界面批量删除（软删除） -post方式")
    @PostMapping(value = "/batch/delete/{fileName}")
    @ZteLog(rdcId = "DT_BO-3900750")
    public ServiceData<Boolean> batchDelete(@RequestBody Map<String, Object> map, @PathVariable("fileName") String fileName) {
        try {
            return ServiceResultUtil.success(universalService.deleteUniversal(map, fileName));
        } catch (Exception e) {
            log.error("UniversalInterfaceController-update, param:{}, fileName:{}, error:{}", map, fileName,
                    e.getMessage(), e);
            throw new BusiException(RetCode.BUSINESSERROR_CODE, "common delete error");
        }
    }

    @ApiOperation("通用界面更新处理")
    @GetMapping(value = "/universal/update/{executeModule}")
    @ZteLog(rdcId = "DT_BO-3900750")
    public ServiceData<Map<String, Object>> universalUpdate(@PathVariable("executeModule") String executeModule) {
        try {
            return ServiceResultUtil.success(universalUpdateService.universalUpdate(executeModule));
        } catch (Exception e) {
            log.error("组织调整定时任务", e);
            ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            ExceptionLogOperateUtil.saveExceptionLog(servletRequestAttributes.getRequest(), e, ErrorCode.UniversalErrorEnum.OU3103.code());
        }
        return ServiceResultUtil.businessError(null);
    }

}