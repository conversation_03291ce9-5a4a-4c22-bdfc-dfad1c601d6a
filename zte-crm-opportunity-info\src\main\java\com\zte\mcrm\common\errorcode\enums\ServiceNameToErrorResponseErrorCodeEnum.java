package com.zte.mcrm.common.errorcode.enums;

import com.zte.mcrm.common.errorcode.util.ErrorCodeMessageUtil;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.PropertyKey;

import java.util.Arrays;
import java.util.Optional;

/**
 * 微服务名对应的请求有响应，但是响应结果不正常的错误码
 *
 * @Author: <EMAIL>
 * @Date: 2022/04/24
 * @Description:
 */
public enum ServiceNameToErrorResponseErrorCodeEnum {
    /**
     * 远程调用zte-bmt-ucs-api服务响应结果异常
     */
    UCS_SERVICE("zte-bmt-ucs-api", "OY4113"),

    /**
     * 远程调用zte-iss-cpc-partnerservice服务响应结果异常
     */
    CPC_SERVICE("zte-iss-cpc-partnerservice", "OY4213"),

    /**
     * 远程调用zte-iss-approval-manage服务响应结果异常
     */
    APPROVAL_SERVICE("zte-iss-approval-manage", "OY4313"),

    /**
     * 远程调用其它第三方服务响应结果异常
     */
    OTHER_SERVICE("otherThirdServiceName", "OY4913"),
    /**
     * 远程调用内部服务响应结果异常
     */
    INTERNAL_SERVICE("", "OY3103");


    public static String getByServiceName(String callerServiceName) {
        if(StringUtils.isBlank(callerServiceName)){
            return OTHER_SERVICE.errorCode;
        }
        if (ServiceNameEnum.getInternalServiceNames().contains(callerServiceName)) {
            return INTERNAL_SERVICE.errorCode;
        }
        ServiceNameToErrorResponseErrorCodeEnum[] values = ServiceNameToErrorResponseErrorCodeEnum.values();
        Optional<ServiceNameToErrorResponseErrorCodeEnum> value = Arrays.stream(values).filter(x -> x.serviceName.equals(callerServiceName)).findFirst();
        return value.orElse(OTHER_SERVICE).errorCode;
    }

    private String serviceName;
    private String errorCode;

    ServiceNameToErrorResponseErrorCodeEnum(String serviceName, @PropertyKey(resourceBundle = ErrorCodeMessageUtil.ERRORCODEMESSAGES_BUNDLE) String errorCode) {
        this.serviceName = serviceName;
        this.errorCode = errorCode;
    }
}
