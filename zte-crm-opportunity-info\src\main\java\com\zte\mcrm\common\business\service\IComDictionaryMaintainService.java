package com.zte.mcrm.common.business.service;

import com.zte.mcrm.common.access.vo.ComDictionaryMaintainVO;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;


/**
 * @Author: 10245190 DengZiqiao
 * @Date: 2021/9/9 11:37
 * @Version: V1.0
 */
public interface IComDictionaryMaintainService {

    /**
     * 根据“类型集合”查询测算数据字典
     *
     * @param typeList 类型集合
     * @return key为入参中的type，value为该type从数据字典表查询的List结果
     */
    Map<String, List<ComDictionaryMaintainVO>> queryByTypeList(List<String> typeList);

    /**
     * 插入字典表数据
     * @param comDictionaryMaintainVO
     * @return
     */
    int insert(ComDictionaryMaintainVO comDictionaryMaintainVO);

    /**
     * 更新字典表数据
     * @param comDictionaryMaintainVO
     * @return
     */
    int update(ComDictionaryMaintainVO comDictionaryMaintainVO);

    /**
     * 根据type查询测算数据字典
     *
     * @param type 类型集合
     * @return key为入参中的type，value为该type从数据字典表查询的List结果
     */
    public  List<ComDictionaryMaintainVO> queryByType( String type) ;

    /**
     * 根据Code获取商机来源的值
     * @param code
     * @return
     */
    String getDataSourceName(String code);

    /**
     * 根据Code获取商机状态的值
     * @param code
     * @return
     */
    String getStatusCdName(String code);

    /**
     * 根据Code获取当前项目现状的值
     * @param code
     * @return
     */
    String getCurrentPhaseTypeName(String code);

    /**
     * 根据Code获取当前招标类型的值
     * @param code
     * @return
     */
    String getTenderTypeName(String code);

    /**
     * 根据code获取授权状态的值
     * @param code
     * @return
     */
    String getStatusAuthName(String code);

    /**
     * 根据code获取赢率的值
     * @param code
     * @return
     */
    String getWinRateTypeName(String code);

    /**
     * 根据code获取月报状态的值
     * @param code
     * @return
     */
    String getMonthlyReportStatusName(String code);

    }
