package com.zte.leadinfo.leadinfo;

import com.zte.leadinfo.leadinfo.entity.CustomerDictionaryDO;
import com.zte.mcrm.lov.access.vo.ListOfValue;
import com.zte.opty.sync.domain.dto.OptySyncQueryDTO;
import com.zte.springbootframe.common.exception.BusiException;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> suntugui
 * @date 2024/6/4 10:15
 */
public interface LeadInfoRepository {

    List<LeadInfoDTO> listByIds(Collection<String> optyRowIds);

    boolean saveSyncResult(List<String> rowIds, String result, String message);

    /**
     * 查询自定义快码
     * @param lovType
     * @return
     */
    List<ListOfValue> getLovTypeValue(String lovType);

    /**
     * 动态sql，根据入参条件查询id
     * @param params
     * @return
     */
    List<String> selectIdsByConditions(OptySyncQueryDTO params);

    /**
     * 查询客户字典表
     * @param params
     * @return
     */
    Map<String, CustomerDictionaryDO> selectCustomerId(Set<String> params);

    /**
     * 保存字典数据
     * @return
     */
    Boolean saveBatchCustomer() throws BusiException, InterruptedException;

    /**
     * 查询redis缓存
     * @param key
     * @return
     */
    String queryRedis(String key);

    /**
     * 根据ID保存客户数据
     * @param idList
     * @return
     */
    Boolean saveCustomerByIdList(List<String> idList);
}
