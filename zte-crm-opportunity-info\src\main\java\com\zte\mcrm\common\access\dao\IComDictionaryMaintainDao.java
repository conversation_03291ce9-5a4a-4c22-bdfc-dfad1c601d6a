package com.zte.mcrm.common.access.dao;

import com.zte.mcrm.common.access.vo.ComDictionaryMaintainVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: 10245190 DengZiqiao
 * @Date: 2021/9/9 11:37
 * @Version: V1.0
 */
@Mapper
@Repository
public interface IComDictionaryMaintainDao {

    /**
     * 根据“类型集合”查询测算数据字典
     *
     * @param list 类型集合
     * @return 查询结果
     */
    List<ComDictionaryMaintainVO> queryByTypeList(@Param("list") List<String> list);

    /**
     * 更新
     * @param comDictionaryMaintainVO
     * @return
     */
    int updateDictById(ComDictionaryMaintainVO comDictionaryMaintainVO);

    /**
     * 插入
     * @param comDictionaryMaintainVO
     * @return
     */
    int insert(ComDictionaryMaintainVO comDictionaryMaintainVO);

}
