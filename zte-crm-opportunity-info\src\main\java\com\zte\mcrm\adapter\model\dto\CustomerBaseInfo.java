package com.zte.mcrm.adapter.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/* Started by AICoder, pid:88ebecc7a5b24522a50fbe711c22276a */
@Getter
@Setter
@ApiModel(description = "客户基本信息模型")
public class CustomerBaseInfo {

    @ApiModelProperty(value = "客户ID，建议外部用的话，应该用客户编码", example = "1-10564Z")
    private String customerId;

    @ApiModelProperty(value = "客户编码", example = "CN000000003192")
    private String customerCode;

    @ApiModelProperty(value = "客户名称", example = "科华恒盛股份有限公司")
    private String customerName;

    @ApiModelProperty(value = "客户英文名称", example = "")
    private String customerEnglishName;

    @ApiModelProperty(value = "客户状态", example = "effect")
    private String customerStatus;

    @ApiModelProperty(value = "客户冻结标识。F-冻结，FA-冻结审批中，FU-解冻审批中，空或null表示没有冻结", example = "F")
    private String customerFrozenFlag;

    @ApiModelProperty(value = "客户合并标识。M-主客户标识，H-被合并子客户标识，HU-解除合并中，HA-合并中，空或null表示没有合并", example = "F")
    private String customerMergeFlag;

    @ApiModelProperty(value = "客户主管部门组织编号", example = "ORG2227732")
    private String buId;

    @ApiModelProperty(value = "这个字段主要是以前SS同步给MDM数据，MDM返回的关联ID。MDM又同步客户数据给其他系统，其他系统保存的是integrationId，导致外部一些数据不同源，目前不再给MDM同步数据，该字段SS维护兼容外部，【一般来说该字段不要用】", example = "551498")
    private String integrationId;

    @ApiModelProperty(value = "别名", example = "上饶中科")
    private String aliasName;

    @ApiModelProperty(value = "统一社会信用代码", example = "")
    private String creditCode;

    @ApiModelProperty(value = "所在国家/地区编码", example = "0001")
    private String countryCode;

    @ApiModelProperty(value = "所在城市编码", example = "000100160090")
    private String cityCode;

    @ApiModelProperty(value = "是否国际。Y-国家，N-国内", example = "N")
    private String international;

    @ApiModelProperty(value = "受限制主体状态。embargo-禁运国，pending-待确认，yes-受限制主体，no-非受限制主体，空串或null-表示空的暂无结果", example = "no")
    private String sanctionedParty;

}
/* Ended by AICoder, pid:88ebecc7a5b24522a50fbe711c22276a */
