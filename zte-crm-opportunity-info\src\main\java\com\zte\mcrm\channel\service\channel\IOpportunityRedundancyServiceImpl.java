package com.zte.mcrm.channel.service.channel;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.channel.model.entity.Opportunity;
import com.zte.mcrm.channel.model.entity.OpportunityDetail;
import com.zte.opty.dao.SOptyRedundancyDao;
import com.zte.opty.feign.PlmProjectCategoryClient;
import com.zte.opty.model.bo.SOptyProductBO;
import com.zte.opty.model.bo.SOptyRedundancyBO;
import com.zte.opty.model.bo.SOptyTeamBO;
import com.zte.opty.model.dto.OptyPlmProjectQueryDTO;
import com.zte.opty.model.vo.OptyPlmProjectVO;
import com.zte.opty.sync.util.LcapConverterUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/* Started by AICoder, pid:w21a30ec9ag1928143b5081ef08c3863a7244691 */
import static org.springframework.util.CollectionUtils.isEmpty;


@Service
public class IOpportunityRedundancyServiceImpl implements IOpportunityRedundancyService {

    @Autowired
    private SOptyRedundancyDao sOptyRedundancyDao;

    @Autowired
    private PlmProjectCategoryClient plmProdClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insert(List<SOptyTeamBO> teamList, List<SOptyProductBO> productBOList, Opportunity insertOpportunity) {
        Optional<SOptyProductBO> mainProduct = findMainProductId(productBOList);
        SOptyRedundancyBO sOptyRedundancyBO = LcapConverterUtil.builtRedundancy(teamList, mainProduct, insertOpportunity.getRowId(), getProductionInfo(mainProduct));
        saveOrUpdate(sOptyRedundancyBO, insertOpportunity.getRowId());
    }

    private Optional<SOptyProductBO> findMainProductId(List<SOptyProductBO> productBOList) {
        if (isEmpty(productBOList)) {
            return Optional.empty();
        }
        return productBOList.stream()
                .filter(e -> e.isMainProduct(e))
                .findAny();
    }

    private OptyPlmProjectVO getProductionInfo(Optional<SOptyProductBO> optyProductBO) {
        if (!optyProductBO.isPresent()) {
            return new OptyPlmProjectVO();
        }
        SOptyProductBO product = optyProductBO.get();
        String code = product.getProductLine();
        ServiceData<PageRows<OptyPlmProjectVO>> serviceData = plmProdClient.prodTreeQuery(new OptyPlmProjectQueryDTO(){{setCodeList(Lists.newArrayList(code));}});

        if (serviceData.getBo() == null || isEmpty(serviceData.getBo().getRows())) {
            throw new BusiException(RetCode.BUSINESSERROR_CODE, "save redundancy fail: prod Tree Query for code: " + code);
        }
        return serviceData.getBo().getRows().get(0);
    }

    public void saveOrUpdate(SOptyRedundancyBO sOptyRedundancyBO, String rowId) {
        SOptyRedundancyBO existingRecord = sOptyRedundancyDao.selectById(rowId);
        if (existingRecord != null) {
            sOptyRedundancyDao.update(sOptyRedundancyBO, Wrappers.lambdaUpdate(SOptyRedundancyBO.class).eq(SOptyRedundancyBO::getId, rowId));
        } else {
            sOptyRedundancyDao.insert(sOptyRedundancyBO);
        }
    }
}

/* Ended by AICoder, pid:w21a30ec9ag1928143b5081ef08c3863a7244691 */
