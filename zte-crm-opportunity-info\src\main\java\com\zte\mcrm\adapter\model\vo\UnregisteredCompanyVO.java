package com.zte.mcrm.adapter.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 未注册时公司的信息对象
 * @Author: <EMAIL>
 * @Date: 2021/07/23
 * @Description:
 */
@Data
public class UnregisteredCompanyVO {
    /**
     * 渠道商名称
     */
    @ApiModelProperty(value = "渠道商名称")
    private String customerName;

    /**
     * 国家/地区
     */
    @ApiModelProperty(value = "国家/地区")
    private String countryRegion;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String socialCreditCode;

    /**
     * 注册地址
     */
    @ApiModelProperty(value = "注册地址")
    private String registerAddress;

    /**
     * 注册资本（币种）
     */
    @ApiModelProperty(value = "注册资本（币种）")
    private String registerCapital;

    /**
     * 法人代表
     */
    @ApiModelProperty(value = "法人代表")
    private String legalRepresent;

    /**
     * 通讯地址
     */
    @ApiModelProperty(value = "通讯地址")
    private String communicationAddress;

    /**
     * 办公电话
     */
    @ApiModelProperty(value = "办公电话")
    private String officeTelephone;

    @ApiModelProperty(value = "公司代码")
    private String customerCode;

    @ApiModelProperty(value = "省份")
    private String provinceName;

    @ApiModelProperty(value = "城市")
    private String cityName;

    @ApiModelProperty(value = "参保人数")
    private Long insuranceNum;

    @ApiModelProperty(value = "注册资本实缴金额")
    private String trueRegisterCapital;

    @ApiModelProperty(value = "公司成立日期")
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date buildDate;

    @ApiModelProperty(value = "曾用名,多个曾用名用逗号分割")
    private String oldName;
}
