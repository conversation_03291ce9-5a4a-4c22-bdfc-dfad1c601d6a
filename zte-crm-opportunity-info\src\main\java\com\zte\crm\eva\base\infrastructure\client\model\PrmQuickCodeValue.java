package com.zte.crm.eva.base.infrastructure.client.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 快速编码值 实体类

 */

@Setter
@Getter
@ToString
public class PrmQuickCodeValue {
    /**
     * 值代码
     */
    private String valueCode;

    /**
     * 父值代码
     */
    private String parentValueCode;

    /**
     * 层级
     */
    private Integer dataLevel;

    /**
     * 编码中文值
     */
    private String codeValueZh;

    /**
     * 编码英文值
     */
    private String codeValueEn;

    /**
     * 代码值
     */
    private String codeValue;

    /**
     * 排序值
     */
    private Integer orderValue;

    /**
     * 备注
     */
    private String memo;

    /**
     * 扩展字段
     */
    private String expand;

    /**
     * 子节点
     */
    private List<PrmQuickCodeValue> children;
    
}