package com.zte.mcrm.channel.controller.channel;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import com.zte.mcrm.channel.model.entity.OpportunityMonthReport;
import com.zte.mcrm.channel.model.vo.OpportunityMonthReportVO;
import com.zte.mcrm.channel.service.channel.IOpportunityMonthReportService;
import com.zte.mcrm.common.util.ValidationGroups;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 *  商机 月报相关 Api
 * <AUTHOR>
 * @date 2021/10/20
 */
@Api(tags = "商机月报相关 API")
@RestController
@RequestMapping("/report")
public class OpportunityMonthReportController {

    @Autowired
    IOpportunityMonthReportService opportunityMonthReportService;

    @ApiOperation("根据当前商机和归属期 获取 状态和原因 -Get方式")
    @GetMapping(value="/query/statusandreason")
    public ServiceData<OpportunityMonthReport> queryOpportunityStatusAndReason
            (@RequestParam String opportunityId, @RequestParam String reportMonth) {
        OpportunityMonthReport result = opportunityMonthReportService.queryOpportunityStatusAndReason(opportunityId, reportMonth);
        return ServiceResultUtil.success(result);
    }

    @ApiOperation("根据当前商机和归属期 更新 状态和原因 -Post方式")
    @PostMapping(value="/update/statusandreason")
    public ServiceData<Integer> updateOpportunityStatusAndReason(@Validated @RequestBody OpportunityMonthReport updateBody, BindingResult bindingResult) throws Exception{
        // 数据校验
        if (bindingResult != null && bindingResult.hasErrors()) {
            throw new com.zte.springbootframe.common.exception.ValidationException(bindingResult);
        }
        int result = opportunityMonthReportService.updateOpportunityStatusAndReason(updateBody);
        return ServiceResultUtil.success(result);
    }


    @ApiOperation("提交月报")
    @PostMapping(value = "/submitMonthReport")
    public ServiceData<OpportunityMonthReport> submitMonthReport(@RequestHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO)String empNo,
                                                                 @Validated(ValidationGroups.Submit.class) @RequestBody OpportunityMonthReportVO opportunityMonthReportVO,
                                                                 BindingResult bindingResult) throws Exception {
        // 数据校验
        if (bindingResult != null && bindingResult.hasErrors()) {
            throw new com.zte.springbootframe.common.exception.ValidationException(bindingResult);
        }
        return ServiceResultUtil.success(opportunityMonthReportService.updateOpportunityMonthReport(opportunityMonthReportVO));
    }

    @ApiOperation("查询月报详情")
    @GetMapping(value = "/getMonthReportDetail")
    public ServiceData<OpportunityMonthReportVO> getMonthReportDetail(@RequestParam @ApiParam(value = "商机id", required = true) String optyId,
                                                                      @RequestParam @ApiParam(value = "月报归属期", required = true) String reportMonth){
        return ServiceResultUtil.success(opportunityMonthReportService.getMonthReportDetail(optyId, reportMonth));
    }

    @ApiOperation("查询当前商机已填写月报的归属期列表")
    @GetMapping(value = "/getReportMonthList")
    public ServiceData<List<String>> getReportMonthList(@RequestParam @ApiParam(value = "商机id", required = true) String optyId){
        return ServiceResultUtil.success(opportunityMonthReportService.getReportMonthList(optyId));
    }
}
