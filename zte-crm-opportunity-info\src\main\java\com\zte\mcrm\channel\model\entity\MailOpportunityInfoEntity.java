package com.zte.mcrm.channel.model.entity;


import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class MailOpportunityInfoEntity {

    private String rowId;

    @ApiModelProperty("商机编码")
    private String optyCd;

    @ApiModelProperty("商机名称")
    private String attrib46;

    @ApiModelProperty("最终用户名称")
    private String lastAccName;

    @ApiModelProperty("最终客户受限制主体")
    private String finalCustomerRestrictionFlag;

    @ApiModelProperty("渠道商名称")
    private String customerName;

    @ApiModelProperty("渠道商是否受限制主体")
    private String agencyRestrictionFlag;

    @ApiModelProperty("中兴业务经理")
    private String businessManagerId;

    @ApiModelProperty("来源")
    private String dataSource;
}
