package com.zte.mcrm.adapter.constant;

public enum ReasonCategoryEnum {
    /**
     * chineseDescription 为枚举的意义
     */
    THIRD_PARTY_SHARE_HOLDING("thirdPartyShareHolding", "三商入股"),

    ASSOCIATED_COMPANIES_BLOCK_LIST("associatedCompaniesBlocklist", "黑名单关联公司"),

    FAKE_BILL("fakeBill", "假单"),

    COMPLIANCE_REASONS("complianceReasons ","合规原因"),

    OVERDUE_NOT_PAID_FINES("overdueNotPaidFines","超期未缴纳罚款"),

    ILLEGAL_INTERNET_SALES("illegalInternetSales","互联网违规销售"),

    NON_ZTE_DISTRIBUTOR_VIOLATIONS("nonZTEDistributorViolations", "非我司经销商违规"),

    BRIBERY("bribery", "贿赂"),

    OTHERS("others","其他"),

    INTEGRITY_PROBLEMS("integrityProblems", "诚信问题"),

    SUNSHINE_PROBLEMS("sunshineProblems", "阳光问题"),

    VICIOUS_EVENTS("viciousEvents", "恶性事件"),

    COUNTERFEITING("counterfeiting", "围标/串标以及造假等问题"),

    LEGAL_DISPUTES("legalDisputes", "法律纠纷"),

    MAJOR_EVENT("majorEvent", "重大事件"),

    CRITICAL_EVENT("criticalEvent", "严重事件"),

    SUBCONTRACTING_PROBLEM("subcontractingProblem", "转包分包问题"),

    THIRD_PARTY_ASSOCIATION_PROBLEMS("thirdPartyAssociationProblems", "三商关联问题"),

    BIDDING_REJECTION("biddingRejection", "拒标问题"),

    COOPERATION_PROBLEM("cooperationProblem", "配合问题"),

    QUALITY_PROBLEM("qualityProblem", "质量问题");

    private String fastCodeValue;

    private String chineseDescription;

    ReasonCategoryEnum(String fastCodeValue, String chineseDescription) {
        this.fastCodeValue = fastCodeValue;
        this.chineseDescription = chineseDescription;
    }

    public String getFastCodeValue() {
        return fastCodeValue;
    }

    public String getChineseDescription() {
        return chineseDescription;
    }

    public static String getChineseDescriptionByKey(String fastCodeValue) {
        ReasonCategoryEnum[] reasonCategoryEnums = ReasonCategoryEnum.values();
        for (ReasonCategoryEnum reasonCategory : reasonCategoryEnums) {
            if (reasonCategory.fastCodeValue.equals(fastCodeValue)) {
                return reasonCategory.getChineseDescription();
            }
        }
        return ReasonCategoryEnum.OTHERS.getChineseDescription();
    }
}
