package com.zte.mcrm.common.util;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.util.web.HttpHeaderUtil;
import com.zte.mcrm.adapter.constant.CompanyEnum;
import com.zte.mcrm.adapter.constant.HeaderNameConst;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.model.SysGlobalConstVo;
import org.apache.commons.lang3.StringUtils;

import javax.script.ScriptException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.apache.curator.shaded.com.google.common.base.Strings.nullToEmpty;


/**
 * 通用方法
 * <AUTHOR>
 * @date 2021/01/16
 */
public class CommonUtils {
    private CommonUtils() {
        throw new IllegalStateException("CommonUtils class");
    }

    private static TransmittableThreadLocal<SysGlobalConstVo> sysGlobalInfo = new TransmittableThreadLocal<>();


    private static final String PATTERN_STRING = "\\{(.*?)}";


    /**
     * 设置请求头信息
     * @param vo
     * <AUTHOR>
     * @date 2019/05/30
     */
    public static void setSysGlobalConstVo(SysGlobalConstVo vo) {
        sysGlobalInfo.set(vo);
    }

    /**
     * 获取当前请求头
     * <AUTHOR>
     * @date 2021/01/16
     * @return
     */
    public static SysGlobalConstVo getSysGlobalConstVo() {
        return sysGlobalInfo.get();
    }

    /**
     * 获取员工号
     * <AUTHOR>
     * @date 2021/01/16
     * @return String
     */
    public static String getEmpNo() {
        if (null == sysGlobalInfo.get()) {
            return nullToEmpty(HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO));
        }
        return sysGlobalInfo.get().getxEmpNo();
    }


    /**
     * 存放多语言ID的HTTP头
     * <AUTHOR>
     * @date 2021/01/16
     * @return
     */
    public static String getxLangId() {
        if (null == sysGlobalInfo.get()) {
            return HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_LANG_ID);
        }
        String language = sysGlobalInfo.get().getxLangId();
        return StringUtils.isEmpty(language) ? CommonConst.ZH_CN : language;
    }

    /**
     * 移除
     * <AUTHOR>
     * @date 2021/01/16
     */
    public static void remove() {
        sysGlobalInfo.remove();
    }

    /**
     * 查询工号
     * <AUTHOR>
     * @date 2021/01/16
     * @param empNo 员工号
     * @return java.lang.String
     */
    public static String getAvailableEmpNo(String empNo){
        if(StringUtils.isNotBlank(empNo) && !CommonConst.UNDEFINED.equals(empNo)){
            return empNo;
        } else {
            return CommonUtils.getEmpNo();
        }
    }

    /**
     * 接受网关信息查询工号
     * <AUTHOR>
     * @date 2021/01/16
     * @param empNo  员工号
     * @return java.lang.String
     */
    public static String geteWayGetEmpNo(String empNo){
        return null == getAvailableEmpNo(empNo) ? StringUtils.EMPTY :  getAvailableEmpNo(empNo);
    }

    /**
     * 查询工号 ,若为空返回空字符串
     * <AUTHOR>
     * @date 2021/01/16
     * @return String
     */
    public static String getEmpNoByEmptyString(){
        return StringUtils.isNotEmpty(CommonUtils.getEmpNo()) ? CommonUtils.getEmpNo() : StringUtils.EMPTY ;
    }

    /**
     * 查询员工号，如果为空则返回system
     * <AUTHOR>
     * @date 2021/01/16
     * @return java.lang.String
     */
    public static String getEmpNoBySystem(){
        return StringUtils.isNotEmpty(CommonUtils.getEmpNo()) ? CommonUtils.getEmpNo() : CommonConst.SYSTEM ;
    }
    
    /**
     * 份校验token
     * <AUTHOR>
     * @date 2019/11/20
     * @return String
     */
    public static String getAuthValue() {
        if (null == sysGlobalInfo.get()) {
            return HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE);
        }
        return sysGlobalInfo.get().getxAuthValue();
    }

    /**
     * 校验tenantId
     * @return String
     */
    public static String getTenantId() {
        if (null == sysGlobalInfo.get()) {
            return HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_TENANT_ID);
        }
        return sysGlobalInfo.get().getxTenantId();
    }

    public static String getSubTenantId() {
        if (null == sysGlobalInfo.get()) {
            return HttpHeaderUtil.getHeader(HeaderNameConst.X_TENANT_ID_SUB);
        }
        return sysGlobalInfo.get().getxSubTenantId();
    }

    public static String getCompanyId() {
        if (null == sysGlobalInfo.get()) {
            return HttpHeaderUtil.getHeader(HeaderNameConst.X_COMPANY_ID) == null ? CompanyEnum.ZTE.getCompanyId() : HttpHeaderUtil.getHeader(HeaderNameConst.X_COMPANY_ID);
        }
        return sysGlobalInfo.get().getCompanyId();
    }

    /**
     * 校验tenantId
     * @return String
     */
    public static String getTenantId(String defaultTenantId) {
        if (null == sysGlobalInfo.get()) {
            return HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_TENANT_ID) == null ? defaultTenantId : HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_TENANT_ID);
        }
        return sysGlobalInfo.get().getxTenantId() == null ? defaultTenantId : sysGlobalInfo.get().getxTenantId();
    }

    /****
     * 替换字符串 EL表达式内变量
     * @date 2019年8月15日
     * <AUTHOR>
     * @param str 被替换字符串
     * @param jsonObject 参数
     * @return
     * @throws ScriptException
     */
    public static String getStringReplaceEl(String str, JSONObject jsonObject) {
        if(StringUtils.isBlank(str)){
            return StringUtils.EMPTY;
        }
        Pattern p = Pattern.compile(PATTERN_STRING);
        Matcher m = p.matcher(str);
        while(m.find()){
            String key = m.group(1);
            if(null != jsonObject && jsonObject.containsKey(key)
                    && null != jsonObject.get(key) ){
                str = str.replace("{" +m.group(1)+ "}", jsonObject.get(key).toString());
            }
        }
        return str;
    }
}
