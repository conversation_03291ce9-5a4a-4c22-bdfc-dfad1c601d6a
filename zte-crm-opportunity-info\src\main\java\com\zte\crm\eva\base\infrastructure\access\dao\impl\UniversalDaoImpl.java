package com.zte.crm.eva.base.infrastructure.access.dao.impl;

import com.google.common.collect.Lists;
import com.zte.crm.eva.base.common.constant.universal.FrontEndUniveralConsts;
import com.zte.crm.eva.base.domain.universal.AggregationParams;
import com.zte.crm.eva.base.domain.universal.CommonTableMap;
import com.zte.crm.eva.base.domain.universal.TableField;
import com.zte.crm.eva.base.domain.universal.WhereBO;
import com.zte.crm.eva.base.infrastructure.access.dao.UniversalDao;
import com.zte.crm.eva.base.infrastructure.access.mapper.UniversalMapper;
import com.zte.mcrm.common.business.KeyIdBusiness;
import com.zte.mcrm.common.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023-04-13
 */
@Repository
public class UniversalDaoImpl implements UniversalDao {

    @Autowired
    private UniversalMapper universalMapper;

    @Override
    public void saveRecord(CommonTableMap tableMap, Map<String, Object> rawMap) {
        String empNo = CommonUtils.getEmpNo();
        Date curDate = new Date();
        Long idValue = KeyIdBusiness.getID();
        String tenantId = CommonUtils.getTenantId();
        rawMap.put(FrontEndUniveralConsts.COMMON_ID_NAME, idValue);
        rawMap.put(FrontEndUniveralConsts.COMMON_CREATED_DATE, curDate);
        rawMap.put(FrontEndUniveralConsts.COMMON_CREATED_BY, empNo);
        rawMap.put(FrontEndUniveralConsts.COMMON_TENANT_ID_NAME, tenantId);
        rawMap.put(FrontEndUniveralConsts.COMMON_UPDATE_DATE, curDate);
        rawMap.put(FrontEndUniveralConsts.COMMON_UPDATE_BY, empNo);
        universalMapper.insertCommon(tableMap.getTransferParam(rawMap), tableMap.getTableRealName());
    }

    @Override
    public void updateRecord(CommonTableMap tableMap, Map<String, Object> rawMap) {
        String idValue = String.valueOf(rawMap.get(FrontEndUniveralConsts.COMMON_ID_NAME));
        Optional<TableField> idOptional = tableMap.getTableFieldAccordingToNickName(FrontEndUniveralConsts.COMMON_ID_NAME);
        TableField idTableField = idOptional.get();
        String idName = idTableField.getFieldRealName();
        String empNo = CommonUtils.getEmpNo();
        Date curDate = new Date();
        rawMap.put(FrontEndUniveralConsts.COMMON_UPDATE_DATE, curDate);
        rawMap.put(FrontEndUniveralConsts.COMMON_UPDATE_BY, empNo);
        Map<String, Object> transferParam = tableMap.getTransferParam(rawMap);
        universalMapper.updateCommon(transferParam, tableMap.getTableRealName(), idName, idValue);
    }

    @Override
    public void deleteRecord(CommonTableMap tableMap, Map<String, Object> rawMap) {
        Object idValue = rawMap.get(FrontEndUniveralConsts.COMMON_ID_NAME);
        Optional<TableField> idOptional = tableMap.getTableFieldAccordingToNickName(FrontEndUniveralConsts.COMMON_ID_NAME);
        TableField idTableField = idOptional.get();
        String idName = idTableField.getFieldRealName();
        String empNo = CommonUtils.getEmpNo();
        Date curDate = new Date();
        rawMap.put(FrontEndUniveralConsts.COMMON_UPDATE_DATE, curDate);
        rawMap.put(FrontEndUniveralConsts.COMMON_UPDATE_BY, empNo);
        rawMap.put(FrontEndUniveralConsts.ENABLED_FLAG_NAME, rawMap.get(FrontEndUniveralConsts.ENABLED_FLAG_VALUE));
        Map<String, Object> transferParam = tableMap.getTransferParam(rawMap);
        universalMapper.deleteCommons(transferParam, tableMap.getTableRealName(), idName, Lists.newArrayList(idValue));
    }

    @Override
    public Long countRecord(String tableRealName, WhereBO whereDTO) {
        return universalMapper.selectCommonCount(tableRealName, whereDTO);
    }

    @Override
    public List<Map<String, Object>> findRecordByPage(String fields, String tableRealName, WhereBO whereDTO){
        return universalMapper.selectCommonByPage(fields, tableRealName, whereDTO);
    }

    @Override
    public List<Map<String, Object>> selectAggregationByParams(AggregationParams aggregationParams) {
        return universalMapper.selectAggregationByParams(aggregationParams);
    }

    @Override
    public int selectEqCommonCount(String tableName, Map<String, Object> conditionMap) {
        return universalMapper.selectEqCommonCount(tableName,conditionMap);
    }

    @Override
    public int updateSingleCommon(String tableName, Map<String, Object> fieldMap, Map<String, Object> conditionMap, String updateTimeFlag, Map<String, Object> updateTimeMap) {
        return universalMapper.updateSingleCommon(tableName,fieldMap,conditionMap,updateTimeFlag,updateTimeMap);
    }
}
