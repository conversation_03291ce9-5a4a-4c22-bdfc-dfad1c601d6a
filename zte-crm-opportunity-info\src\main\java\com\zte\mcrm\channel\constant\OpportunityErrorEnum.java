package com.zte.mcrm.channel.constant;

import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.util.CommonUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public enum OpportunityErrorEnum {
    /**
     * 最终用户\"%s\"为重复客户，已替换为有效的最终用户\"%s\"，请确认无误后点击提交。
     */
    SG2011("最终用户%s为重复客户，已替换为有效的最终用户%s，请确认无误后点击提交。", "Final user %s is a duplicate customer, it has been replaced with an effective final user %s. Please confirm and click submit if everything is correct."),
    /**
     * 您报备的商机中最终用户\"%s\"处于审批中，审批通过后系统将邮件通知您来报备商机。
     */
    SG2031("您报备的商机中最终用户%s处于审批中，审批通过后系统将邮件通知您来报备商机。", "The end user %s in the opportunity you reported is under review. Once the review is completed, the system will send an email notification to inform you to report the opportunity."),
    /**
     * 抱歉，您报备的商机中的最终用户\"%s\"异常，请与中兴业务经理联系。
     */
    SG2041("抱歉，您报备的商机中的最终用户%s异常，请与中兴业务经理联系。", "Sorry, there is an exception with the end user %s in the opportunity you reported. Please contact ZTE business manager for assistance."),
    /**
     * 当前商机中的最终用户\"%s\"不是ZTE的客户，请先申请创建该最终用户且通过中兴审批后，系统将邮件通知您提交商机报备。
     */
    SG2051("当前商机中的最终用户%s不是ZTE的客户，请先申请创建该最终用户且通过中兴审批后，系统将邮件通知您提交商机报备。", "Sorry, the end user %s in your current opportunity is not a ZTE customer. Please apply to create this end user and wait for ZTE approval. Once approved, the system will send an email notification to inform you to submit the opportunity report."),
    /**
     * 您创建的最终用户"%s"处于审批中，审批通过后系统将邮件通知您来报备商机。
     */
    SG2061("您创建的最终用户%s处于审批中，审批通过后系统将邮件通知您提交商机报备。", "Sorry, the end user %s you created is under review. Once the review is completed, the system will send an email notification to inform you to submit the opportunity report."),
    /**
     * "最终用户已生效，您现在可以继续进行商机报备。
     */
    SG3011("最终用户已生效，您现在可以继续进行商机报备。", "The end user has been activated, you can now continue to report the opportunity.");



    String msg;

    String enMsg;

    OpportunityErrorEnum(String msg, String enMsg) {
        this.msg = msg;
        this.enMsg = enMsg;
    }

    /**
     * 获取枚举对象
     *
     * @return 枚举对象
     */
    public OpportunityErrorEnum get() {
        return this;
    }

    /**
     * @return 返回枚举值的name(错误码)
     */
    public String code() {
        return this.name();
    }

    /**
     * @return 返回枚举值的msg
     */
    public String msg() {
        if (Objects.equals(CommonConst.ZH_CN, CommonUtils.getxLangId())) {
            return this.msg;
        }
        return this.enMsg;
    }

}