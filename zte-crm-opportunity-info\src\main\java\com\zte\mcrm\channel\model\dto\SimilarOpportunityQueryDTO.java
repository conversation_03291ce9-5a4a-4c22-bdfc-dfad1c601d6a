package com.zte.mcrm.channel.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2021/9/30
 */
@Setter
@Getter
@ToString
public class SimilarOpportunityQueryDTO {

    @ApiModelProperty(value = "当前商机主键")
    private String rowId;

    @ApiModelProperty(value = "渠道商客户编码")
    private String crmCustomerCode;

    @ApiModelProperty(value = "商机名称")
    private String opportunityName;

    @ApiModelProperty(value = "渠道商名称")
    private String channelVendor;

    // 以下字段默认查询商机用到
    @ApiModelProperty(value = "当前商机招标类型")
    private String currentOptyTenderType;

    @ApiModelProperty(value = "当前商机最终用户名称")
    private String currentOptyLastName;

    @ApiModelProperty(value = "当前商机客户编码")
    private String currentCrmCode;

    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "当前商机预计发标日期")
    private Date currentOptyTime;

    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "当前商机竞标截止日期")
    private Date currentDeadline;

    // 移动端用到
    @ApiModelProperty(value = "当前商机产品列表")
    private List<String> currentProductList;

    private Long startRow;

    private Long rowSize;
}
