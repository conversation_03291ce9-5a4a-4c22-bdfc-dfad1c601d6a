package com.zte.mcrm.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.TypeReference;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.client.util.TestFallbackUtils;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.servicecenter.config.MsbProperties;
import com.zte.itp.msa.servicecenter.util.MsbSpringContextUtil;
import com.zte.itp.msa.util.web.HttpHeaderUtil;
import com.zte.mcrm.adapter.constant.HeaderNameConst;
import com.zte.mcrm.common.consts.HttpMethodEnum;
import com.zte.mcrm.common.exception.BusinessRuntimeException;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 微服务调用工具类
 */
public class MsaUtils {

    /** 服务版本号 */
    public static final String VERSION = "v1";

    /** Opersupportmanage服务 */
    public static final String SERVICE_NAME_BASE = "zte-crm-ichannel-base";

    /** 服务调用成功返回码 */
    public static final String SUCCESS_CODE = "0000";

    private static final Logger logger = LoggerFactory.getLogger(MsaUtils.class);

    private static MsbProperties msbProperties = (MsbProperties) MsbSpringContextUtil.getBean("msbProperties");

    private MsaUtils() {
        // 禁止初始化
    }

    public static <T> T invokeBaseService(String url, HttpMethodEnum methodType,
                                                  Object params, TypeReference<ServiceData<T>> resultType) throws RouteException {
        return invokeBaseService(SERVICE_NAME_BASE, url, methodType, params, resultType);
    }

    public static <T> T invokeBaseService(String version, String url, HttpMethodEnum methodType,
                                                  Object params, TypeReference<ServiceData<T>> resultType) throws RouteException {
        Map<String, String> paramString = new HashMap<>(3);
        paramString.put("serviceName", SERVICE_NAME_BASE);
        paramString.put("version", VERSION);
        paramString.put("url", url);
        return invokeServiceAndReturnBO(paramString, methodType, params, resultType);
    }

    /**
     * 微服务调用
     *
     * @param serviceName 服务名
     * @param version 版本号
     * @param url 操作URL(Class的RequestMapping + Method的RequestMapping)
     * @param methodType 服务请求类型
     * @param params 请求参数:可序列化为JSON格式的对象，或者基本类型
     * @param resultType 返回结果的数据类型
     * @return
     * @throws RouteException
     */
    public static <T> T invokeService(Map<String, String> paramString,
            HttpMethodEnum methodType, Object params, TypeReference<ServiceData<T>> resultType) throws RouteException {
        String bodyParams = JSON.toJSONString(params);
        String serviceName = paramString.get("serviceName");
        String url = paramString.get("url");
        String version = paramString.get("version");
        if (logger.isInfoEnabled()) {
            logger.info("MSB call [serviceName:{}, version:{}, url:{}, method:{}, bodyParams:{}, headerParams:{} ",
                    serviceName, version, url, methodType.getKey(), bodyParams, JSON.toJSONString(headerParams()));
        }
        String response = MicroServiceRestUtil.invokeService(serviceName, VERSION, methodType.getKey(), url, bodyParams, headerParams());

        logger.info("MSB call result:{} ", response);

        // 先用String类型取ServiceData.bo, 判断BO是否可以正常序列化。
        ServiceData<String> rawData = JSON.parseObject(response, new TypeReference<ServiceData<String>>() {});

        if (StringUtils.isEmpty(rawData.getBo())) {
            logger.warn("MSB call result.BO is empty!");
            return null;
        }

        try {
            return JSON.parseObject(response, resultType).getBo();
        } catch (JSONException e) {
            logger.error("MSB call result parse failed! Type:{}, BO:{}", resultType.getType().getTypeName(), rawData.getBo());
            throw new BusinessRuntimeException(rawData.getCode().getCode(), rawData.getCode().getMsgId());
        }
    }

    /**
     * 微服务调用<br/>
     * 服务返回数据类型为ServiceData, 且本方法返回ServiceData.bo
     *
     * @param serviceName 服务名
     * @param version 版本号
     * @param url 操作URL(Class的RequestMapping + Method的RequestMapping)
     * @param methodType 服务请求类型
     * @param params 请求参数:可序列化为JSON格式的对象，或者基本类型
     * @param resultType 返回结果的数据类型
     * @return
     * @throws RouteException 返回结果非成功状态（“0000”）时抛此异常
     */
    public static <T> T invokeServiceAndReturnBO(Map<String, String> paramString,
            HttpMethodEnum methodType, Object params, TypeReference<ServiceData<T>> resultType) throws RouteException {
        String bodyParams = JSON.toJSONString(params);
        String url = paramString.get("url");
        String serviceName = paramString.get("serviceName");
        String version = paramString.get("version");
        if (logger.isInfoEnabled()) {
            logger.info("MSB call [serviceName:{}, version:{}, url:{}, method:{}, bodyParams:{}, headerParams:{} ",
                    serviceName, version, url, methodType.getKey(), bodyParams, JSON.toJSONString(headerParams()));
        }
        String response = MicroServiceRestUtil.invokeService(serviceName, VERSION, methodType.getKey(), url, bodyParams, headerParams());

        logger.info("MSB call result:{} ", response);

        // 先用String类型取ServiceData.bo, 判断ServiceData.RetCode是否是成功的。
        ServiceData<String> rawData = JSON.parseObject(response, new TypeReference<ServiceData<String>>() {});

        RetCode retCode = rawData.getCode();

        if (!SUCCESS_CODE.equals(retCode.getCode())) {
            BusinessRuntimeException e= new BusinessRuntimeException(rawData.getCode().getCode(), rawData.getCode().getMsgId(), rawData.getBo());
            e.setExMsg(rawData.getCode().getMsg());
            throw e;
        }
        // 再用泛型类型解析BO数据
        ServiceData<T> serviceData = JSON.parseObject(response, resultType);

        return serviceData.getBo();
    }

    /**
     * 获取Header参数
     *
     * @return
     * <AUTHOR> ********
     */
    private static Map<String, String> headerParams() {
        Map<String, String> result = new HashMap<>();
        result.put("X-Emp-No", CommonUtils.getEmpNo());
        result.put("X-Auth-Value", CommonUtils.getAuthValue());
        result.put("X-Lang-Id", CommonUtils.getxLangId());
        String tenantId = HeaderNameConst.DEFAULT_X_TENANT_ID;
        result.put("X-Tenant-Id", tenantId);
        result.put("X-Account-Id", CommonUtils.getEmpNo());
        return result;
    }


    /**
     * url调用方式调用接口
     * 服务返回数据类型为ServiceData, 且本方法返回ServiceData.bo
     *
     * @param version 版本号
     * @param url 操作URL(Class的RequestMapping + Method的RequestMapping)
     * @param methodType 服务请求类型
     * @param params 请求参数:可序列化为JSON格式的对象，或者基本类型
     * @param resultType 返回结果的数据类型
     * @return
     * @throws RouteException
     */
    public static <T> T invokeServiceUtil(String version, String url,
                                      HttpMethodEnum methodType, Object params, TypeReference<ServiceData<T>> resultType) throws RouteException {
        String result = "";
        Map<String, String> headerParams = headerParams();
        Objects.requireNonNull(version, "version  can't be empty");
        Objects.requireNonNull(methodType, "sendType  can't be empty");
        headerParams.put(SysGlobalConst.HTTP_HEADER_X_ORIGIN_SERVICE_NAME, msbProperties.getServiceName());
        headerParams.put(SysGlobalConst.HTTP_HEADER_X_ORIGIN_SERVICE_VERSION, msbProperties.getVersion());
        headerParams.put(SysGlobalConst.HTTP_HEADER_X_TARGET_SERVICE_VERSION, VERSION);
        try {
            result = getResult(url, methodType, params, headerParams);
        } catch (Exception e) {
            e.printStackTrace();
            if (TestFallbackUtils.isProdEnv()) {
                throw new RuntimeException(e);
            }
        }

        // 先用String类型取ServiceData.bo, 判断ServiceData.RetCode是否是成功的。
        ServiceData<String> rawData = JSON.parseObject(result, new TypeReference<ServiceData<String>>() {});

        RetCode retCode = rawData.getCode();

        if (!SUCCESS_CODE.equals(retCode.getCode())) {
            throw new BusinessRuntimeException(rawData.getCode().getCode(), rawData.getCode().getMsgId());
        }

        ServiceData<T> serviceData = JSON.parseObject(result, resultType);

        return serviceData.getBo();
    }

    @SuppressWarnings("unchecked")
    public static String getResult(String url, HttpMethodEnum methodType, Object params, Map<String, String> headerParams) {
        if (HttpMethodEnum.POST.name().equals(org.apache.commons.lang3.StringUtils.upperCase(methodType.getKey()))) {
            return HttpClientUtil.httpPostWithJSON(url, JSON.toJSONString(params), headerParams);
        } else if (HttpMethodEnum.GET.name().equals(org.apache.commons.lang3.StringUtils.upperCase(methodType.getKey()))) {
            return HttpClientUtil.httpGet(url, (Map<String, String>) params, headerParams);
        }
        return "";
    }

}
