package com.zte.mcrm.common.business.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.mcrm.common.access.dao.LockDao;
import com.zte.mcrm.common.business.IOftenSearchService;
import com.zte.mcrm.common.util.RequestMessage;
import com.zte.mcrm.oftensearch.access.vo.OftensearchVO;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.common.model.HttpResultData;
import com.zte.springbootframe.common.serviceregister.MicroServiceWithConfigUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/****
 * 查看历史记录
 * @ClassName OftenSearchServiceImpl
 * <AUTHOR>
 * @Date 2021/3/12
 * @Version V1.0
 */
@Service
public class OftenSearchServiceImpl implements IOftenSearchService {

    @Autowired
    LockDao lockDao;

    @Override
    public List<OftensearchVO> getOftenSearchList(String empNo, String bizType, Integer pageNo, Integer pageSize) throws BusiException, RouteException {
        if(null == pageNo || null == pageSize){
            return Collections.EMPTY_LIST;
        }
        Map<String, String> paramMap = new HashMap<>(4);
        paramMap.put("empNo", empNo);
        paramMap.put("bizType", bizType);
        paramMap.put("pageNo", pageNo.toString());
        paramMap.put("pageSize", pageSize.toString());
        Map<String, String> headerParamsMap = RequestMessage.getHeader("smartsales");
        // 调用接口
        HttpResultData searchHttpResult = MicroServiceWithConfigUtil.invokeServiceWithGetMethod("4",
                "/oftensearchservice/oftensearch", paramMap, headerParamsMap);
        if (null == searchHttpResult || null == searchHttpResult.getBo()) {
            return Collections.EMPTY_LIST;
        }
        String oftenSearchString = JacksonJsonConverUtil.beanToJson(searchHttpResult.getBo());
        return JacksonJsonConverUtil.jsonToListBean(oftenSearchString, new TypeReference<List<OftensearchVO>>() {});
    }

    @Override
    public int invalidOftenSearchByBizType(String bizType) {
        return lockDao.invalidOftenSearchByBizType(bizType);
    }
}
