package com.zte.mcrm.channel.controller.prm;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import com.zte.mcrm.adapter.approval.model.dto.ApprovalStartParamsDTO;
import com.zte.mcrm.channel.model.dto.OptyCdRecordDto;
import com.zte.mcrm.channel.model.entity.ComApprovalRecord;
import com.zte.mcrm.channel.model.entity.Opportunity;
import com.zte.mcrm.channel.model.entity.OpportunityDetail;
import com.zte.mcrm.channel.model.entity.OpportunityProduct;
import com.zte.mcrm.channel.service.channel.*;
import com.zte.mcrm.channel.service.prm.IComApprovalRecordService;
import com.zte.mcrm.common.annotation.SystemAuthVerify;
import com.zte.opty.model.bo.SOptyProductBO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "商机更新相关API")
@RestController
@RequestMapping("/prm/updateOpportunity")
public class PrmOpportunityUpdateController {

    @Autowired
    IOpportunityService opportunityService;
    @Autowired
    IOpportunityDetailService opportunityDetailService;
    @Autowired
    IOpportunityProductService opportunityProductService;
    @Autowired
    IComApprovalRecordService comApprovalRecordServicel;
    @Autowired
    IOpportunityInfoService opportunityInfoService;
    @Autowired
    IOptyCdRecordService optyCdRecordService;

    @PostMapping("/updateSOpty")
    @SystemAuthVerify(serviceNamesWhitelist = {"zte-crm"})
    public ServiceData<Opportunity> updateSOpty(@RequestBody Opportunity opportunity){
        return ServiceResultUtil.success(opportunityService.updateOnTheQuiet(opportunity));
    }

    @PostMapping("/updateSOptyX")
    @SystemAuthVerify(serviceNamesWhitelist = {"zte-crm"})
    public ServiceData<OpportunityDetail> updateSOptyX(@RequestBody OpportunityDetail opportunityDetail){
        return ServiceResultUtil.success(opportunityDetailService.update(opportunityDetail, true));
    }

    @PostMapping("/updateCxProd")
    @SystemAuthVerify(serviceNamesWhitelist = {"zte-crm"})
    public ServiceData<OpportunityProduct> updateCxProd(@RequestBody OpportunityProduct opportunityProduct){
        return ServiceResultUtil.success(opportunityProductService.update(opportunityProduct));
    }

    @PostMapping("/insertOrUpdateCxProd")
    @SystemAuthVerify(serviceNamesWhitelist = {"zte-crm"})
    public ServiceData<List<SOptyProductBO>> insertOrUpdateCxProd(@RequestBody List<OpportunityProduct> opportunityProduct,
                                                                  @RequestParam String opptyId,
                                                                  @RequestParam String businessType){
        return ServiceResultUtil.success(opportunityProductService.insertOrUpdateProductList(opportunityProduct,
                                                                                             opptyId, businessType,
                                                                                             new OpportunityDetail()));
    }

    @PostMapping("/updateApprovalRecord")
    @SystemAuthVerify(serviceNamesWhitelist = {"zte-crm"})
    public ServiceData<Integer> updateApprovalRecord(@RequestBody ComApprovalRecord entity){
        return ServiceResultUtil.success(comApprovalRecordServicel.update(entity));
    }

    @PostMapping("/insertApprovalRecord")
    @SystemAuthVerify(serviceNamesWhitelist = {"zte-crm"})
    public ServiceData<Integer> insertApprovalRecord(@RequestBody List<ComApprovalRecord> entity){
        return ServiceResultUtil.success(comApprovalRecordServicel.insertByBatch(entity));
    }

    @PostMapping("/updateOptyCdRecord")
    @SystemAuthVerify(serviceNamesWhitelist = {"zte-crm"})
    public ServiceData<Integer> updateOptyCdRecord(@RequestBody OptyCdRecordDto optyCdRecordDto){
        return ServiceResultUtil.success(optyCdRecordService.update(optyCdRecordDto));
    }

    @PostMapping("/insertOptyCdRecord")
    @SystemAuthVerify(serviceNamesWhitelist = {"zte-crm"})
    public ServiceData<Integer> insertOptyCdRecord(@RequestBody List<OptyCdRecordDto> optyCdRecords){
        return ServiceResultUtil.success(optyCdRecordService.insertBatch(optyCdRecords));
    }

    @GetMapping("/startProcess")
    @SystemAuthVerify(serviceNamesWhitelist = {"zte-crm"})
    public ServiceData<String> startProcess(@RequestParam String rowId) throws Exception {
        opportunityInfoService.startFlowTask(rowId);
        return ServiceResultUtil.success("success");
    }

    @GetMapping("/getStartProcessParams")
    @SystemAuthVerify(serviceNamesWhitelist = {"zte-crm"})
    public ServiceData<ApprovalStartParamsDTO> getStartProcessParams(@RequestParam String rowId) throws Exception {
        return ServiceResultUtil.success(opportunityInfoService.getStartProcessParams(rowId));
    }

    @ApiOperation(value = "根据审批中心流程状态更新商机状态",
            notes = "1. 针对报备审批中的商机，流程已走到仲裁，但状态未修改为仲裁中的，如果doUpdate为true，会直接更新为仲裁中；\n" +
                    "2. 针对报备审批中的商机但实际已经审批完成的商机，会检测出来，但不会更新状态，需要根据实际情况分析原因后再更新状态。\n" +
                    "rowId不传值时会检测所有状态为报备审批中的商机")
    @GetMapping("/refreshStatusByApproveCenter")
    @SystemAuthVerify(serviceNamesWhitelist = {"zte-crm"})
    public ServiceData<List<String>> refreshStatusByApproveCenter(String rowId, Boolean doUpdate) throws Exception {
        return ServiceResultUtil.success(opportunityInfoService.refreshStatusByApproveCenter(rowId, doUpdate));
    }

}
