package com.zte.mcrm.channel.model.mapper;

import com.zte.mcrm.channel.model.dto.CustomerInfoDTO;
import com.zte.mcrm.channel.model.entity.OpptyCustomerCreateRecord;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface OpptyCustomerCreateRecordMapper {

    OpptyCustomerCreateRecordMapper INSTANCE = Mappers.getMapper(OpptyCustomerCreateRecordMapper.class);

    /**
     * 最终客户查询数据入库对象转换
     * @param customerInfoDTO
     * @return
     */
    @Mapping(source = "lastAccName", target = "xLastAccName")
    @Mapping(source = "lastAccId", target = "xLastAccId")
    OpptyCustomerCreateRecord transFrom(CustomerInfoDTO customerInfoDTO);

}
