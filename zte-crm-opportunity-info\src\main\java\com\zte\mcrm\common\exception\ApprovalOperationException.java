package com.zte.mcrm.common.exception;

import com.zte.itp.msa.core.model.RetCode;

/**
 * @description: 审批中心审批操作异常
 * @author: 10243305
 * @date: 2021/6/26 上午11:09
 */
public class ApprovalOperationException extends RuntimeException {
    /**
     * 业务异常编码
     */
    private String exCode;
    /**
     * 业务异常消息
     */
    private String exMsg;

    public ApprovalOperationException() {}

    public ApprovalOperationException(String exCode, String exMsg) {
        super(exMsg);
        this.exCode = exCode;
        this.exMsg = exMsg;
    }

    public ApprovalOperationException(String exMsg) {
        this(RetCode.BUSINESSERROR_CODE, exMsg);
    }

    public ApprovalOperationException(Throwable cause) {
        super(cause);
    }

    public ApprovalOperationException(Throwable cause, String exCode, String exMsg) {
        super(exMsg, cause);
        this.exCode = exCode;
        this.exMsg = exMsg;
    }

    public ApprovalOperationException(Throwable cause, String exMsg) {
        this(cause, RetCode.BUSINESSERROR_CODE, exMsg);
    }

    public String getExCode() {
        return exCode;
    }

    public void setExCode(String exCode) {
        this.exCode = exCode;
    }

    public String getExMsg() {
        return exMsg;
    }

    public void setExMsg(String exMsg) {
        this.exMsg = exMsg;
    }
}
