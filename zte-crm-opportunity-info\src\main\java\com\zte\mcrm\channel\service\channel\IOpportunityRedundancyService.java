package com.zte.mcrm.channel.service.channel;

import com.zte.mcrm.channel.model.entity.Opportunity;
import com.zte.opty.model.bo.SOptyProductBO;
import com.zte.opty.model.bo.SOptyRedundancyBO;
import com.zte.opty.model.bo.SOptyTeamBO;

import java.util.List;

public interface IOpportunityRedundancyService {

    /**
     * 更新生成冗余表
     * @param teamList
     * @param insertOpportunity
     */
    void insert(List<SOptyTeamBO> teamList, List<SOptyProductBO> productBOList, Opportunity insertOpportunity);


    /**
     * 新增或者更新方法
     * @param sOptyRedundancyBO
     * @param rowId
     */
    void saveOrUpdate(SOptyRedundancyBO sOptyRedundancyBO, String rowId);
}
