package com.zte.mcrm.common.errorcode.service;

import com.zte.crm.eva.base.infrastructure.client.IchannelBaseFeign;
import com.zte.mcrm.common.errorcode.model.ComExceptionLog;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: <EMAIL>
 * @Date: 2022/04/29
 * @Description:
 */
@Component
@Slf4j
public class ComExceptionLogService {
    @Autowired
    private IchannelBaseFeign ichannelBaseFeign;

    public void asyncAdd(ComExceptionLog comExceptionLog) throws RouteException {

        try {
            ichannelBaseFeign.asyncAddErrorRecord(comExceptionLog);
        } catch (Exception e) {
            log.error("add error record failure, id: {}, errorCode:{}",comExceptionLog.getId(), comExceptionLog.getErrorCode(), e);
        }
    }
}
