package com.zte.mcrm.common.business.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zte.mcrm.common.access.dao.LockDao;
import com.zte.mcrm.common.access.vo.LockVO;

import java.util.List;

/****
 *
 * <AUTHOR> @date 2021/2/10
 **/

@Service
public class LockServiceImpl implements LockService {

	@Autowired
	private LockDao lockDao;
	
	@Override
	public void addLock(LockVO lock) {
		lockDao.addLock(lock);
	}

	@Override
	public void deleteLock(String methodName) {
		lockDao.deleteLock(methodName);
	}

	@Override
	public List<String> getApprovedBy(String approveId) {
		List<String> approvedBy = lockDao.getApprovedBy(approveId);
		return approvedBy;
	}
}
