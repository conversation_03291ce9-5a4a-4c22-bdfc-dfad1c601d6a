package com.zte.leadinfo.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zte.leadinfo.common.entity.CxApprOpHead;
import com.zte.leadinfo.common.entity.CxApprOppLn;
import com.zte.leadinfo.common.service.CxApprOppLnService;
import com.zte.leadinfo.common.mapper.CxApprOppLnMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cx_appr_opp_ln】的数据库操作Service实现
* @createDate 2024-07-12 23:29:27
*/
@Service
public class CxApprOppLnServiceImpl extends ServiceImpl<CxApprOppLnMapper, CxApprOppLn>
    implements CxApprOppLnService{

    @Override
    public List<CxApprOppLn> listByOpptyIds(List<String> optyIds, List<String> approveObjects) {
        return getBaseMapper().listByOpptyIds(optyIds, approveObjects);
    }
}




