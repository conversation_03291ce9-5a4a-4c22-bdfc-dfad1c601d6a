package com.zte.mcrm.adapter.approval.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: 10243305
 * @date: 2021/7/14 上午11:17
 */
@Data
public class ApprovalActiveTaskParamsDTO implements Serializable {
    @ApiModelProperty(value = "应用编码")
    private String appCode;
    @ApiModelProperty(value = "业务实例id集合")
    private List<String> businessIds;
    @ApiModelProperty(value = "流程编码集合")
    private List<String> flowCodes;
    @ApiModelProperty(value = "流程实例id集合")
    private List<String> insFlowIds;
    @ApiModelProperty(value = "页号")
    private Integer pageNo;
    @ApiModelProperty(value = "页内记录数")
    private Integer pageSize;
}
