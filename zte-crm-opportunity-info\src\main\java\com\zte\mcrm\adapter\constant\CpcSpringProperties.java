package com.zte.mcrm.adapter.constant;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/9/16
 */
@Component
@ConfigurationProperties(prefix = "cpc")
@PropertySource(value = "classpath:${spring.application.name}.properties", ignoreResourceNotFound = true)
@Data
public class CpcSpringProperties {
    private String partnerCategory;
    private String partnerSmallCategory;
    /**
     * 调用方access key
     */
    private String accessKey;
    /**
     * secretKey
     */
    private String secretKey;
    @Value("${cpc.thirdParty.obscureQueryByName}")
    private String thirdPartyObscureQueryByNameUrl;
    @Value("${cpc.queryOrgInfo.byKeyword}")
    private String queryOrgInfoByKeyWordUrl;
    @Value("${cpc.blocklist.query}")
    private String blockListQueryUrl;
    @Value("${cpc.partner.obscureQuery}")
    private String partnerObscureQueryUrl;
}
