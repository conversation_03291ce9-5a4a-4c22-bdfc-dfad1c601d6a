package com.zte.mcrm.clues.business.service;

import com.zte.mcrm.clues.access.vo.BusinessClues;
import com.zte.springbootframe.common.exception.BusiException;

/****
 *
 * <AUTHOR> @date 2021/2/10
 **/

public interface CluesSaveService {
	/**
	 * 保存线索，返回线索Id
	 * @param vo
	 * @return
	 * @throws BusiException
	 */
	BusinessClues save(BusinessClues vo) throws BusiException;

	/**
	 * 更新线索，返回线索Id
	 * @param businessClues
	 * @return
	 * @throws BusiException
	 */
	void update(BusinessClues businessClues) throws BusiException;

	/****
	 * 部门ORG编码
	 * @methodName setOrgCodeClue
	 * @param vo
	 * @return void
	 * <AUTHOR>
	 * @date 2021/2/5
     * @throws BusiException
	**/
    BusinessClues setOrgCodeClue(BusinessClues vo) throws BusiException;
}
