package com.zte.mcrm.channel.model.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 变更渠道商信息
 *
 * <AUTHOR>
 * @date 2024/8/6
 */
@Getter
@Setter
@ToString
public class ChannelPartnerChangeDTO {
    /**
     * 营销项目编号
     */
    private String projNum;
    /**
     * 商机编号
     */
    private String opportunityCode;
    /**
     * 旧渠道商编码
     */
    private String oldDealerCode;
    /**
     * 旧渠道商名称
     */
    private String oldDealerName;
    /**
     * 新渠道商编码
     */
    private String newDealerCode;
    /**
     * 新渠道商名称
     */
    private String newDealerName;
    /**
     * 变更原因
     */
    private String changeReason;
    /**
     * 变更操作人
     */
    private String operatorId;
    /**
     * 变更完成时间
     */
    private Date operatorTime;
}
