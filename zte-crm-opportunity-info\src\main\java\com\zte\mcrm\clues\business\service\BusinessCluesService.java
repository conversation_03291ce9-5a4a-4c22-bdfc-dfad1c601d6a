package com.zte.mcrm.clues.business.service;

import com.zte.mcrm.clues.access.vo.BusinessClues;
import com.zte.mcrm.clues.access.vo.BusinessCluesInfoVO;
import com.zte.mcrm.opportunity.access.vo.ListOfValueOpty;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.common.exception.SiebelErrorAuthDeniedException;
import com.zte.springbootframe.util.page.PageQuery;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;

import java.util.List;
import java.util.Map;


/****
 *
 * <AUTHOR> @date 2021/2/10
 **/
public interface BusinessCluesService {
    /**
     * 查询线索详情
     * @param businessClues
     * @return
     * @throws BusiException
     */
	BusinessClues selectBaseInfo(BusinessClues businessClues) throws BusiException;
	/**
	 * 检查用户是否有权限查看线索，有的话返回true，无的话返回false
	 * @param empId
	 * @param clueId
	 * @return
	 * @throws BusiException
	 */
	boolean checkHasAuthToReadClue(String empId,String clueId) throws BusiException;
	/**
	 * 带权限查线索
	 * 
	 * @param pageQuery
	 * @return
	 * @throws Exception
	 */
	List<BusinessClues> getCluesWithAuth(PageQuery<BusinessClues> pageQuery)throws Exception;
	/**
	 * 客户关联线索
	 * 
	 * @param pageQuery
	 * @return
	 * @throws Exception
	 */
	List<BusinessClues> getAccountClues(PageQuery<BusinessClues> pageQuery)throws Exception;
	/**
	 * 查询客户关联线索总数
	 * @param pageQuery
	 * @return
	 * @throws Exception
	 */
	int accountCluesCount(PageQuery<BusinessClues> pageQuery) throws Exception;
	/**
	 * 查询线索总数
	 * @param pageQuery
	 * @return
	 * @throws Exception
	 */
	int countClues(PageQuery<BusinessClues> pageQuery) throws Exception;
	/**
	 * 初始化线索及相关信息
	 * @param businessCluesInfoVO
	 * @return
	 * @throws BusiException
	 */
	BusinessCluesInfoVO initBusinessCluesInfo(BusinessCluesInfoVO businessCluesInfoVO) throws BusiException;
	/**
	 * 新建线索
	 * @param businessClues
	 * @return
	 * @throws BusiException
	 */
	void saveClues(BusinessClues businessClues) throws BusiException;
	/**
	 * 线索关闭
	 * @param businessClues
	 * @return
	 * @throws BusiException
	 */
	void closeSave(BusinessClues businessClues)throws BusiException;

    /**
     * 移动端线索转商机(MySQL方法)
     * @param businessClues
     * @return
     * @throws Exception
     */
	String mobileClueToOpty(BusinessClues businessClues) throws Exception;
	/**
	 * 线索分配
	 * @param businessClues
	 * @return
	 * @throws BusiException
	 */
	void assignedSave(BusinessClues businessClues) throws BusiException;
	
	/**
	 * 检查用户是否有权限查看线索，有的话返回true，无的话返回false
	 * @param businessClues
	 * @return
	 * @throws Exception 
	 * @throws BusiException
	 */
	boolean checktransferAuthClue(BusinessClues businessClues) throws BusiException;
	/**
	 * 检查用户是否有权限线索分配
	 * @param businessClues
	 * @return
	 * @throws Exception 
	 * @throws BusiException
	 */
	boolean checkAssignedAuthClue(BusinessClues businessClues) throws BusiException;

    /**
     * 获取值列表
     * @param headerParamsMap
     * @param getParams
     * @return
     * @throws BusiException
     * @throws RouteException
     */
	List<ListOfValueOpty> getHttpData(Map<String, String> headerParamsMap, Map<String, Object> getParams) throws BusiException, RouteException ;
	

	/**
	 * 线索认领状态检查
	 * @param statusCode
	 * @return
	 * @throws BusiException
	 */
	boolean claimClueStatusDocimasia(String statusCode) throws BusiException;
	
	
	/**
	 * 检查是否有线索查询权限，是的话返回true,否的话返回false
	 * @param empId
	 * @param clueId
	 * @return
	 * @throws SiebelErrorAuthDeniedException 
	 */
	boolean checkAuthWithEmpIdAndClueId(String empId,String clueId) throws SiebelErrorAuthDeniedException;
	/**
	 * 线索关闭
	 * @param businessClues
	 * @return
	 * @throws BusiException
	 */
	boolean checkClosedAuthClue(BusinessClues businessClues) throws BusiException;
	/**
	 * 当前登录人是否为归属客户经理
	 * @param businessClues
	 * @return
	 */
	 boolean isBelongCustomerMgr(BusinessClues businessClues);
	
	/**
	 * 线索状态是否为待客户经理更新
	 * @param clue
	 * @return
	 */
	boolean isLeadUpdate(BusinessClues clue);

    /**
     * 线索回退
     * @param clue
     * @throws BusiException
     */
	void backLead(BusinessClues clue) throws BusiException;

    /**
     * 线索认领
     * @param clue
     * @throws BusiException
     * @throws SiebelErrorAuthDeniedException
     */
	void claimClue(BusinessClues clue) throws BusiException,SiebelErrorAuthDeniedException;

	/**
	 * 高级查询
	 * @param pageQuery
	 * @return
	 */
	List<BusinessClues> advanceQuery(PageQuery pageQuery);
}
