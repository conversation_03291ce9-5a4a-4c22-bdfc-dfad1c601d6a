package com.zte.mcrm.adapter.clouddisk.model;

public class EncryptProfileVO {

    private String key;
    private String useId;
    private boolean encrypEnable;
    private int encrypValue=1;
    private boolean  fixedRead=false;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getUseId() {
        return useId;
    }

    public void setUseId(String useId) {
        this.useId = useId;
    }

    public boolean isEncrypEnable() {
        return encrypEnable;
    }

    public boolean getEncrypEnable() {
        return encrypEnable;
    }

    public void setEncrypEnable(boolean encrypEnable) {
        this.encrypEnable = encrypEnable;
    }

    public int getEncrypValue() {
        return encrypValue;
    }

    public void setEncrypValue(int encrypValue) {
        this.encrypValue = encrypValue;
    }

    public boolean isFixedRead() {
        return fixedRead;
    }

    public void setFixedRead(boolean fixedRead) {
        this.fixedRead = fixedRead;
    }

}
