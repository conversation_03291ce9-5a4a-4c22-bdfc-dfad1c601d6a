package com.zte.mcrm.adapter.controller;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.approval.model.ApprovingQueryParam;
import com.zte.mcrm.adapter.clouddisk.util.ServiceDataUtil;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.channel.model.vo.PrmOpportunityVO;
import com.zte.mcrm.channel.service.prm.IPrmOpportunityService;
import com.zte.mcrm.common.util.CommonUtils;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api(tags = "低代码商机融合列表查询")
@RestController
@RequestMapping("/lowCode")
public class OpportunityLowCodeController {

    @Autowired
    IPrmOpportunityService prmOpportunityService;

    @PostMapping("/getOpportunityList")
    public ServiceData<List<PrmOpportunityVO>> getOpportunityList(@RequestBody List<String> ids) throws Exception {
        return ServiceDataUtil.success(prmOpportunityService.getOpportunityLowCodeList(ids));
    }

    @PostMapping("/getMyPendingAndPendedIds")
    public ServiceData<List<String>> getMyPendingAndPendedIds() throws Exception {
        return ServiceDataUtil.success(prmOpportunityService.getMyPendingAndPendedIds());
    }

}
