package com.zte.mcrm.channel.service.channel;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.util.web.HttpHeaderUtil;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.channel.model.entity.OpportunityDetail;
import com.zte.mcrm.channel.model.entity.OpportunityProduct;
import com.zte.mcrm.common.business.service.IKeyIdService;
import com.zte.mcrm.common.util.FormDataHelpUtil;
import com.zte.opty.dao.SOptyProductDao;
import com.zte.opty.model.bo.OptionBO;
import com.zte.opty.model.bo.SOptyProductBO;
import com.zte.opty.model.bo.SOptyRedundancyBO;
import com.zte.opty.sync.domain.constants.CommonConstant;
import com.zte.opty.sync.domain.enums.MarketTypeEnum;
import com.zte.opty.sync.util.CommonMapUtil;
import com.zte.opty.sync.util.LcapConverterUtil;
import com.zte.opty.sync.util.RadioFieldConverterUtils;
import com.zte.springbootframe.common.exception.BusiException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;


/**
 *  服务类 
 * <AUTHOR>
 * @date 2021/09/14
 */
@Service
public class IOpportunityProductServiceImpl implements IOpportunityProductService{
    @Autowired
    private IKeyIdService iKeyIdService;

	@Autowired
	private IOpportunityDetailService detailService;

	@Autowired
	SOptyProductDao sOptyProductDao;

	private final Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
	private com.zte.mcrm.channel.dao.OpportunityProductDao opportunityProductDao ;

	/**
	 * 根据主键获取实体对象
	 * @param rowId 主键ID
	 * @return
	 * <AUTHOR>
     * @date 2021/09/14
     */
	@Override
	public OpportunityProduct get(String rowId) {
		return opportunityProductDao.get(rowId);
	}

	/**
	 * 软删除指定记录
	 * @param rowId 主键ID
	 * @return 删除记录个数
	 * <AUTHOR>
     * @date 2021/09/14
     */
//	@Override
//	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
//	public int softDelete(String rowId){
//		List<OptionBO> optionBOS = RadioFieldConverterUtils.converterRadioField(CommonConstant.TABLE_FIELD_ENABLE_FLAG, CommonConstant.N);
//		return opportunityProductDao.softDelete(rowId, JSONObject.toJSONString(optionBOS));
//	}


	/**
	 * 批量软删除指定记录
	 * @param rowIds 主键ID
	 * @return 删除记录个数
	 * <AUTHOR>
	 * @date 2021/09/14
	 */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public int batchSoftDelete(List<String> rowIds){
		rowIds.forEach(x->{
			opportunityProductDao.softDelete(x,JSONObject.toJSONString(RadioFieldConverterUtils
					.converterRadioField(CommonConstant.TABLE_FIELD_ENABLE_FLAG, CommonConstant.N)));
		});
		return 0;
	}

	/**
	 * 删除指定记录
	 * @param rowId 主键ID
	 * @return 删除记录个数
	 * <AUTHOR>
     * @date 2021/09/14
     */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public int delete(String rowId){
		return opportunityProductDao.delete(rowId);
	}

	/**
	 * 新增指定记录
	 * @param entity 实体对象
	 * @return 新增的记录对象,注意是提交数据库之前的实体对象
	 * <AUTHOR>
     * @date 2021/09/14
     */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public OpportunityProduct insert(OpportunityProduct entity){
		String emp = HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
		Date now = new Date();
		entity.setCreatedBy(emp);
		entity.setLastUpdBy(emp);
        entity.setCreated(now);
		entity.setLastUpd(now);
        if (null==entity.getRowId()) {
            entity.setRowId(iKeyIdService.getKeyId());
        }

		String enableFlag = JSONObject.toJSONString(RadioFieldConverterUtils
				.converterRadioField(CommonConstant.TABLE_FIELD_ENABLE_FLAG,
						Objects.equals(CommonConstant.N,entity.getActiveFlg())?CommonConstant.N:CommonConstant.Y));
		entity.setEnableFlagExt(Objects.equals(CommonConstant.N,entity.getActiveFlg())?CommonConstant.ARRAY_N:CommonConstant.ARRAY_Y);
		entity.setActiveFlg(enableFlag);

		try {
			// 查询对应商机拓展数据
			/* Started by AICoder, pid:bfd52t4126580e21406a09db905d38378ec9d02b */
			String pid = Optional.ofNullable(entity.getOpptyId())
					.orElseThrow(() -> new BusiException(RetCode.BUSINESSERROR_CODE,"The associated ID for the business opportunity details is empty, " +
							"and the product information ID for the business opportunity is blank:"+ entity.getOpptyId()));
			/* Ended by AICoder, pid:bfd52t4126580e21406a09db905d38378ec9d02b */
			// 预计签单时间
			OpportunityDetail detail = null;
			/* Started by AICoder, pid:ae814v7aeav13a014a5d09e73043dc48ef24c023 */
			detail= Optional.ofNullable(detailService.get(pid))
					.orElseThrow(() -> new BusiException(RetCode.BUSINESSERROR_CODE, "Business opportunity details are empty, " +
							"business opportunity details are associated with ID:" + pid));
			/* Ended by AICoder, pid:ae814v7aeav13a014a5d09e73043dc48ef24c023 */

			List<OptionBO> rateOption = RadioFieldConverterUtils.converterRadioField(CommonConstant.TABLE_FIELD_SUCCESS_RATE,
					detail.getWinRate());
			entity.setSuccessRate(JSONObject.toJSONString(rateOption));
			/* Started by AICoder, pid:50b3e73844kf6a81461e0a1ba04a860ba68128a8 */
			entity.setSuccessRate(JSONObject.toJSONString(Arrays.asList(detail.getWinRate())));
			/* Ended by AICoder, pid:50b3e73844kf6a81461e0a1ba04a860ba68128a8 */
			// 市场类型
			List<OptionBO> marketTypeOption = RadioFieldConverterUtils.converterRadioField(CommonConstant.TABLE_FIELD_MARKET_TYPE,
					MarketTypeEnum.getByTargetCode(detail.getMarketType()));
			entity.setMarketType(JSONObject.toJSONString(marketTypeOption));

			// 是否主产品
			List<OptionBO> mainProdOption = RadioFieldConverterUtils.converterRadioField(CommonConstant.TABLE_FIELD_MAIN_PROD, entity.getZteMainProduct());
			entity.setMainProd(JSONObject.toJSONString(mainProdOption));
		} catch (BusiException e) {
			throw new RuntimeException(e);
		}
        opportunityProductDao.insert(entity);
		return entity;
	}


	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public List<SOptyProductBO> insertOrUpdateProductList(List<OpportunityProduct> opportunityProductList, String opptyId, String businessType,
														  OpportunityDetail opportunityDetail){
		// 业务类型 = 政企产品，需删除掉主产品信息
		List<OptionBO> enableFlag = RadioFieldConverterUtils
				.converterRadioField(CommonConstant.TABLE_FIELD_ENABLE_FLAG, CommonConstant.N);
		if(OpportunityConstant.NEW_OPPORTUNITY.equals(businessType)) {
			sOptyProductDao.batchLogicDeleteByOptyId(opportunityDetail.getRowId(), JSONArray.toJSONString(enableFlag), OpportunityConstant.PDM_PROD);
		}
		if(CollectionUtils.isEmpty(opportunityProductList)){
			logger.info("商机产品信息为空，商机id:{}", opportunityDetail.getRowId());
			sOptyProductDao.batchLogicDeleteByOptyId(opportunityDetail.getRowId(), JSONArray.toJSONString(enableFlag), businessType);
			return Collections.emptyList();
		}
		String emp = HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
		Date now = new Date();
		sOptyProductDao.batchLogicDeleteByOptyId(opportunityDetail.getRowId(), JSONArray.toJSONString(enableFlag), businessType);
		List<OpportunityProduct> pdmProds = new ArrayList<>();
		opportunityProductList.forEach(opportunityProduct -> {
			String parProdId = iKeyIdService.getKeyId();
			opportunityProduct.setOpptyId(opptyId);
			opportunityProduct.setCreatedBy(emp);
			opportunityProduct.setLastUpdBy(emp);
			opportunityProduct.setCreated(now);
			opportunityProduct.setLastUpd(now);
			opportunityProduct.setRowId(parProdId);
			opportunityProduct.setBusinessType(businessType);
			if(StringUtils.isEmpty(opportunityProduct.getActiveFlg())){
				opportunityProduct.setActiveFlg("Y");
			}
			// 保存公司主产品
			OpportunityProduct pdmProd = opportunityProduct.getPdmProd();
			if (Objects.nonNull(pdmProd) && OpportunityConstant.NEW_OPPORTUNITY.equals(businessType)) {
				pdmProd.setCreatedBy(emp);
				pdmProd.setLastUpdBy(emp);
				pdmProd.setCreated(now);
				pdmProd.setLastUpd(now);
				pdmProd.setRowId(iKeyIdService.getKeyId());
				pdmProd.setOpptyId(opptyId);
				pdmProd.setParProdId(parProdId);
				pdmProd.setBusinessType(OpportunityConstant.PDM_PROD);
				pdmProd.setZteMainProduct(opportunityProduct.getZteMainProduct());
				pdmProd.setActiveFlg(StringUtils.isEmpty(opportunityProduct.getActiveFlg()) ? "Y" : opportunityProduct.getActiveFlg());
				pdmProds.add(pdmProd);
			}
		});
		opportunityProductList.addAll(pdmProds);
		// 转换成新对象
		List<SOptyProductBO> productBOList = LcapConverterUtil.buildSoptyProduct(opportunityProductList, opportunityDetail);
		sOptyProductDao.insertBatch(productBOList);
		return productBOList;
	}

	/**
	 * 修改指定记录
	 * @param entity 实体对象
	 * @return 修改的记录对象,注意是提交数据库之前的实体对象
	 * <AUTHOR>
     * @date 2021/09/14
     */
	@Override
	@Transactional(propagation = Propagation.SUPPORTS,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
	public OpportunityProduct update(OpportunityProduct entity){
		String emp = HttpHeaderUtil.getHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO);
		entity.setLastUpdBy(emp);
		entity.setLastUpd(new Date());
		OpportunityDetail detail = detailService.get(entity.getOpptyId());
		List<SOptyProductBO> productBOList = LcapConverterUtil.buildSoptyProduct(Collections.singletonList(entity), detail);
		sOptyProductDao.update(productBOList.get(0), Wrappers.lambdaUpdate(SOptyProductBO.class).eq(SOptyProductBO::getId, entity.getRowId()));
		return entity;
	}

	/**
	 * 获取符合条件的实体列表,按指定属性排序
	 * @param map 参数集合
	 * @return 实体集合
	 * <AUTHOR>
     * @date 2021/09/14
     */
	@Override
	public List<OpportunityProduct> getList(Map<String, Object> map){
		return opportunityProductDao.getList(map);
	}

	/**
	 * 统计
	 * @param map 参数集合
	 * @return 统计总数
	 * <AUTHOR>
     * @date 2021/09/14
     */
	@Override
	public long getCount(Map<String, Object> map){
        return opportunityProductDao.getCount(map);
	}

	/**
	 * 获取符合条件的记录列表,先按指定属性排序,在分页
	 * @param map 参数集合
	 * @return 实体集合
	 * <AUTHOR>
     * @date 2021/09/14
     */
	@Override
	public List<OpportunityProduct> getPage(Map<String, Object> map){
		return opportunityProductDao.getPage(map);
	}

	/**
	 * 获取符合条件的记录列表,先按指定属性排序,在分页
	 * @param form 参数集合
	 * @return 实体集合
	 * <AUTHOR>
     * @date 2021/09/14
     */
	@Override
	public PageRows<OpportunityProduct> getPageRows(FormData<OpportunityProduct> form){
		Map<String, Object> map = FormDataHelpUtil.getPageQueryMap(form);
		long total = this.getCount(map);
		List<OpportunityProduct> result = this.getPage(map);
		return FormDataHelpUtil.getPageRowsResult(form, total, result);
	}
}
