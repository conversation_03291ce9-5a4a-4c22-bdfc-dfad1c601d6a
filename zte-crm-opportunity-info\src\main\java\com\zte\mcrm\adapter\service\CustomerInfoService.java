package com.zte.mcrm.adapter.service;

import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.mcrm.adapter.model.dto.*;
import com.zte.mcrm.adapter.model.vo.*;
import com.zte.mcrm.common.framework.exception.ErrorCodeException;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;

import java.util.List;
import java.util.Map;

/**
 * 描述：客户信息查询
 * 创建时间：2021/9/15
 *
 * @author：王丹凤**********
 */
public interface CustomerInfoService {

    /**
     * 根据客户名称、客户编码模糊分页查询客户，查询结果只有状态为“生效”的客户
     * @param form
     * @return
     */
    public PageRows<AccountsVO> getCustomerInfoByNameOrCode(FormData<ChannelCompanySearchDTO> form);

    public PageRows<AccountsVO> getCustomerInfoByNameOrCodeV2(FormData<ChannelCompanySearchDTO> form);

    /**
     * 查询最终客户
     * @param form
     * @return
     */
    public List<EndCustomerVO> getEndCustomerList(FormData<ChannelCompanySearchDTO> form);

    /**
     * 根据客户名称查询客户信息
     *
     * @param   accountName 客户名称
     * @return  List<AccountInfo>
     */
    List<AccountInfo> getCustomerInformationByName(String accountName) throws BusiException;

    /**
     * 根据组织编码、客户名称查询客户信息 --支持不同法人合规信息
     * @param deptNo
     * @param accountName
     * @return
     * @throws BusiException
     */
    List<AccountInfo> getCustomerInformationByNameV2(String deptNo,String accountName) throws BusiException;

    /**
     * 根据客户名称返回对应的客户信息
     * */
    Map<String, AccountInfo> getAccountInfoByBatch(List<String> accountNameList);

    /**
     * 客户批量查询
     *  精确匹配，不分页
     *	输入参数：客户编号（批次，大概一次传10个客户编码，最大不超过20个）
     *	输出参数：客户编号、客户名称、客户类型、运营商类型、是否高端客户等。
     * @param accountCodeList
     * @return
     * @throws BusiException
     */
    List<AccountInfo> getCustomerInformationBatch(List<String> accountCodeList) throws BusiException;

    /**
     * 客户批量查询 -支持根据法人编号查询客户信息
     * @param accountCodeList
     * @return
     * @throws BusiException
     */
    List<AccountInfo> getCustomerInformationBatchV2(String organizationId,List<String> accountCodeList) throws BusiException;
    /**
     * 客户详情查询
     * @param lastAccId
     * @return
     */
    AccountInfo getCustomerDetails(String lastAccId) throws BusiException;

    AccountInfo getCustomerDetailsV2(String organizationId,String lastAccId) throws BusiException;

    AccountInfo getAccountByCodeOrId(String customerCode);

    String getAccountIdByCode(String customerCode);

    /**
     * 创建客户草稿
     * @param paramObj
     * @return
     */
    String createCustomer(CreateCustomerParam paramObj) throws Exception;

    /**
     * 分页模糊查询渠道商信息接口
     *
     * @param formData @return
     */
    PageRows<PartnerInfoVO> partnerQueryByName(FormData<PartnerObscureQueryDTO> formData) throws Exception;

    /**
     * 查询渠道商认证等级信息接口
     * @param crmCustomerCode
     * @return
     */
    PartnerLevelVO partnerLevelByCrmCustomerCode(String crmCustomerCode);


    /**
     * 查询客户主体分类
     * @param custClassifyExternalQueryDTO
     * @return
     * @throws BusiException
     * @throws RouteException
     */
    List<CustClassifyExternalVO> getCustClassify(CustClassifyExternalQueryDTO custClassifyExternalQueryDTO) throws BusiException, RouteException;


    /**
     *
     * @param custClassifyExternalQueryDTO
     * @return
     * @throws BusiException
     * @throws RouteException
     */
    List<CustClassifyExternalVO> getCustClassifyAndValidate(CustClassifyExternalQueryDTO custClassifyExternalQueryDTO) throws BusiException, RouteException;

    /**
     * 创建客户
     * @param customerSysCreateParam
     * @throws BusiException
     */
    Boolean importCustomer(CustomerSysCreateParam customerSysCreateParam) throws BusiException;

    /**
     * 创建客户
     * @param customerCreateInfoDTO
     * @return
     * @throws ErrorCodeException
     */
    CustomerCreateResult createFinalCustomer(CustomerCreateInfoDTO customerCreateInfoDTO) throws ErrorCodeException;

    CustomerApproveResult queryCustomerCreateProcessStatus(String customerCode);

    /**
     * 根据客户名称批量查询客户信息
     * @param accountNameList
     * @return
     */
    List<AccountInfo> getAccountInfoBatchByName(List<String> accountNameList);

    /**
     * 获取
     * @param customerNames
     * @return
     */
    Map<String, AccountInfo> getAccountInfoByPriorityMap(List<String> customerNames);

}
