package com.zte.mcrm.channel.constant;

/* Started by AICoder, pid:9c49c5fd070d46b29a31c188392d4c07 */
public enum CustomerApprovalStatusEnum {
    /**
     * 草稿
     */
    DRAFT("Draft"),
    /**
     * 废止
     */
    ABOLISH("Abolish"),
    /**
     * 同意
     */
    AGREE("Agree"),
    /**
     * 拒绝
     */
    DISAGREE("Disagree"),
    /**
     * 审批中
     */
    APPROVING("Approving"),
    /**
     * 生效
     */
    VALID("Valid");

    private String status;

    CustomerApprovalStatusEnum(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public static boolean hasEffectApproval(String status) {
        return APPROVING.isMe(status) || AGREE.isMe(status);
    }

    public boolean isMe(String status) {
        return this.status.equalsIgnoreCase(status);
    }
}
/* Ended by AICoder, pid:9c49c5fd070d46b29a31c188392d4c07 */
