package com.zte.mcrm.channel.service.prm;

import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.model.dto.AccountInfo;
import com.zte.mcrm.channel.model.dto.*;
import com.zte.mcrm.channel.model.entity.OpportunityInfo;
import com.zte.mcrm.channel.model.entity.PrmOpptyCustomerCreateRecordQuery;
import com.zte.mcrm.channel.model.vo.OpportunityPartInfoVO;
import com.zte.mcrm.channel.model.vo.OpptyCustomerCreateRecordVO;
import com.zte.mcrm.channel.model.vo.PrmOpportunityPendingVO;
import com.zte.mcrm.channel.model.vo.PrmOpportunityVO;
import com.zte.mcrm.opportunity.access.vo.ConversionProjectOrdinary;
import com.zte.opty.model.dto.IHolOrgQueryDTO;
import com.zte.opty.model.dto.OptyPlmProjectQueryDTO;
import com.zte.opty.model.vo.IHolOrgVO;
import com.zte.opty.model.vo.OptyPlmProjectVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 */
public interface IPrmOpportunityService {



    /**
     * prm侧商机列表分页查询
     * @param formData
     * @param isExport
     * @return
     * @throws Exception
     */
    PageRows<PrmOpportunityVO> getPrmOpportunityPage(FormData<PrmOpportunityQueryDTO> formData, boolean isExport) throws Exception;

    List<String> getMyPendingAndPendedIds() throws ParseException;

    List<AccountInfo> getCustomerInformationBatch(List<String> accountCodeList);

    /**
     * 商机报备查询待我审批
     * @param formData 查询map
     * @return
     * @throws Exception
     */
    PageRows<PrmOpportunityPendingVO> getOpportunityPendingPage(FormData<PrmOpportunityPendingDTO> formData) throws Exception;

    OpportunityInfo getOpportunity(String rowId) throws Exception;

    /**
     * 商机转立项
      * @param entity
     */
    ConversionProjectOrdinary transferProject(OpportunityTransferredProjectDTO entity) throws Exception;

    /**
     * 商机快速转立项
     * @param entity
     */
    ConversionProjectOrdinary fastTransferProject(OpportunityTransferredProjectDTO entity) throws Exception;

    /**
     * 商机转立项跳转验证
     * @param rowId
     */
    ConversionProjectOrdinary transferProjectValidate(String rowId) throws Exception;

    /**
     * 商机导出
     * @param form
     * @param response
     */
    void exportOpportunityList(FormData<PrmOpportunityQueryDTO> form, HttpServletResponse response) throws Exception;
     /* 商机勘误接口(更新用户行业,商机所属行业）
     * @param vo
     * * @return
     * @return
     */
    public boolean errataOppty(OpportunityPartInfoVO vo) throws Exception;

    /**
     * 校验是否有查看详情权限
     * @param empNo
     * @param rowId
     * @return
     */
    Boolean checkDetailReviewPermission(String empNo, String rowId) throws ExecutionException, InterruptedException;

    /**
     * 查询渠道商创建最终用户列表
     *
     * @param  formData 查询条件
     * @return PageRows<OpptyCustomerCreateRecordVO>
     * @throws Exception
     * <AUTHOR>
     * @date 2023/5/10
     */
    PageRows<OpptyCustomerCreateRecordVO> queryCustomerRecord(FormData<PrmOpptyCustomerCreateRecordQuery> formData) throws Exception;

    /**
     * 导入公司主产品
     * @param file
     * @throws IOException
     */
    void importPdm(MultipartFile file) throws IOException;

    List<PrmOpportunityVO> getOpportunityLowCodeList(List<String> codes) throws Exception;

    /**
     * 查询组织数据
     * @param queryDTO
     * @return
     */
    ServiceData<PageRows<IHolOrgVO>> queryOrg(IHolOrgQueryDTO queryDTO);

    /**
     * 查询产品数据
     * @param deptPath
     * @return
     */
    ServiceData<PageRows<OptyPlmProjectVO>> queryProduct(OptyPlmProjectQueryDTO deptPath);

    /**
     * 查询中标商机信息
     * @param queryDTO 查询条件
     * @return List<WinOpportunityVO>
     * @throws Exception
     * <AUTHOR>
     * @date 2024/01/01
     */
    List<com.zte.mcrm.channel.model.vo.WinOpportunityVO> queryWinOpportunities(com.zte.mcrm.channel.model.dto.WinOpportunityQueryDTO queryDTO) throws Exception;
}
