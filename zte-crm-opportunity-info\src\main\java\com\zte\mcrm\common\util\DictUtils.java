package com.zte.mcrm.common.util;

import com.zte.mcrm.common.access.vo.ComDictionaryMaintainVO;
import com.zte.mcrm.common.consts.CommonConst;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class DictUtils {

    public static String getName(String code, List<ComDictionaryMaintainVO> dicts) {
        if (dicts == null) {
            return code;
        }
        String langId = CommonUtils.getxLangId();
        for (ComDictionaryMaintainVO vo : dicts) {
            if (StringUtils.equals(vo.getCode(), code)) {
                return CommonConst.ZH_CN.equalsIgnoreCase(langId)? vo.getChineseName() : vo.getEnglishName();
            }
        }
        return code;
    }

}
