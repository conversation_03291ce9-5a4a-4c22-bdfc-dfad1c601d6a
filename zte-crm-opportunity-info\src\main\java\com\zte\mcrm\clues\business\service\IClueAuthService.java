package com.zte.mcrm.clues.business.service;

import com.zte.mcrm.opportunity.access.vo.Auth;

/****
 * 统一权限
 * @ClassName IClueAuthService
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/7/14
 * @Version V1.0
 **/
public interface IClueAuthService {

    /****
     * 我的所有线索列表查询 权限
     * @methodName getBusinessClueListAuth
     * @param
     * @return com.zte.mcrm.opportunity.access.vo.Auth
     * <AUTHOR>
     * @date 2021/2/4
     * @throws Exception
    **/
    Auth getBusinessClueListAuth() throws Exception;

    /**
     * CluesAuthUtil.getAuthOrgs()
     * @return
     */
    Auth getAuthOrgs();
}
