package com.zte.mcrm.channel.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 最终用户查询结果
 *
 * <AUTHOR>
 * @date 2023/5/9
 */
@Data
public class OpptyCustomerCreateRecordVO {
    @ApiModelProperty(value = "主键id")
    private String rowId;
    @ApiModelProperty(value = "渠道商名称")
    private String customerName;
    @ApiModelProperty(value = "最终客户名称")
    private String lastAccName;
    @ApiModelProperty(value = "所属省份/办事处编码")
    private String deptNo;
    @ApiModelProperty(value = "所属省份/办事处名称")
    private String deptName;
    @ApiModelProperty(value = "最终用户行业编码")
    private String finalCustomerParentTrade;
    @ApiModelProperty(value = "最终用户子行业编码")
    private String finalCustomerChildTrade;
    @ApiModelProperty(value = "最终用户行业(父行业-子行业形式)")
    private String finalCustomerTradeName;
    @ApiModelProperty(value = "中兴业务经理：姓名工号")
    private String businessManager;
    @ApiModelProperty(value = "申请人")
    private String applicant;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "申请时间")
    private Date createdDate;
    @ApiModelProperty(value = "客户实时状态：与SS保持一致的状态，含：待提交、审批中、生效、驳回")
    private String status;
}
