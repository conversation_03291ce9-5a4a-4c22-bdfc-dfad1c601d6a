package com.zte.mcrm.adapter.authorization.util;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/9/30
 */
public class UppAuthorityUtil {

    /**
     * 判断工号是外部工号
     * <AUTHOR>
     * @date 2020/11/27
     * @param empNo 工号
     * @return boolean
     */
    public static boolean determineTheUserIsExternal(String empNo){
        // 内部用户工号最多14位
        int num = 14;
        return StringUtils.isNotBlank(empNo) && empNo.length() > num;
    }

}
