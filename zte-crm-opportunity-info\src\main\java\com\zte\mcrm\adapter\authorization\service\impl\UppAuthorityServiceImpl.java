package com.zte.mcrm.adapter.authorization.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.zte.itp.authorityclient.client.AuthorityClient;
import com.zte.itp.authorityclient.entity.input.*;
import com.zte.itp.authorityclient.entity.output.ConstraintVo;
import com.zte.itp.authorityclient.entity.output.RoleVO;
import com.zte.itp.authorityclient.entity.output.ServiceData;
import com.zte.itp.authorityclient.entity.output.UserVO;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.adapter.authorization.dto.RoleInfoDTO;
import com.zte.mcrm.adapter.authorization.model.ConstraintData;
import com.zte.mcrm.adapter.authorization.service.UppAuthorityService;
import com.zte.mcrm.common.util.CommonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * UPP AuthorityClient 封装
 * <AUTHOR>
 * @date 2021/9/29
 */
@Service
public class UppAuthorityServiceImpl implements UppAuthorityService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public ServiceData getUserByRoleAndData(RoleInfoDTO dto) {
        RoleDataEntity entity = new RoleDataEntity();
        entity.setEmpidui(dto.getEmpidui());
        entity.setModuleId(dto.getModuleId());
        entity.setTenantId(dto.getTenantId());
        entity.setSecretKey(dto.getSecretKey());
        entity.setProductId(dto.getProductId());
        entity.setToken(dto.getToken());
        if(StringUtils.isNotBlank(dto.getItpValue())){
            entity.setItpValue(dto.getItpValue());
        }
        RoleDataVo roleData = new RoleDataVo();
        List<String> datas = new ArrayList<>();
        List<String> orgIds = dto.getOrgIds();
        if(CollectionUtils.isNotEmpty(orgIds)){
            datas.addAll(orgIds);
        }
        List<String> industryIds = dto.getIndustryIds();
        if(CollectionUtils.isNotEmpty(industryIds)){
            datas.addAll(industryIds);
        }
        List<DataVo> dataVos = new ArrayList<>();
        for (String constraintValue:datas){
            DataVo data = new DataVo();
            // 0 - 只查询当前指定约束值；1 - 查询当前指定约束值+向上递归约束值；2 - 查询当前指定约束值最近的约束值
            data.setIsInclude(1);
            data.setType("ORG");
            data.setData(constraintValue);
            dataVos.add(data);
        }

        roleData.setDatas(dataVos);
        roleData.setRoleId(dto.getRoleId());
        entity.setRoleDataVoList(Collections.singletonList(roleData));

        logger.info("getUserByRoleAndData:{}",entity);
        return AuthorityClient.getRoleDataUser(entity);
    }

    /**
     * 根据角色编号集合查询用户集合
     * @param entity
     * @param roleCodes
     * @return
     */
    public Map<String, List<UserVO>> queryUsersByRoleCode(CommonModuleIdEntity entity, List<String> roleCodes){
        CommonRoleEntity commonRoleEntity = transModuleIdEntityToRoleEntity(entity);
        logger.info("queryUsersByRoleCode,input:{},roleCodes:{}", entity, roleCodes);
        ServiceData sd = AuthorityClient.queryUsersByRoleCode(commonRoleEntity, roleCodes, null);
        logger.info("queryUsersByRoleCode,input:{},roleCodes:{},result:{}", entity, roleCodes, JSON.toJSONString(sd));
        Object bo = this.getBoFromServiceData(sd);
        if (null == bo){
            return null;
        }
        return JSONObject.parseObject(JSON.toJSONString(bo), new TypeReference<Map<String, List<UserVO>>>(){});
    }


    /**
     * 根据用户工号 模块id， 获取用户拥有角色
     * @param entity 入参（包括工号、模块id、产品id等）
     * @return 角色列表
     * @throws BusiException 业务异常
     */
    @Override
    public List<RoleVO> getRoleListModule(CommonModuleIdEntity entity) throws BusiException {
        List<RoleVO> roleList = new ArrayList<>();

        CommonRoleEntity commonRoleEntity = transModuleIdEntityToRoleEntity(entity);

        logger.info("getRoleListModule: commRoleRntity{}", JSON.toJSONString(commonRoleEntity));
        ServiceData roleSd = AuthorityClient.getRoleList(commonRoleEntity);
        Object bo = this.getBoFromServiceData(roleSd);
        if(null == bo){
            return roleList;
        }
        logger.info("getRoleListModule: return{}", JSON.toJSONString(roleSd));
        List<RoleVO> retRole = JSON.parseArray(JSON.toJSONString(roleSd.getBo()), RoleVO.class);
        roleList.addAll(retRole);

        return roleList;
    }

    private CommonRoleEntity transModuleIdEntityToRoleEntity(CommonModuleIdEntity entity){
        CommonRoleEntity commonRoleEntity = new CommonRoleEntity();
        //用户工号（必填）
        commonRoleEntity.setEmpidui(entity.getEmpidui());
        //token值（必填）
        commonRoleEntity.setToken(entity.getToken());
        // 模块id
        commonRoleEntity.setModuleId(entity.getModuleId());
        //租户id
        commonRoleEntity.setTenantId(entity.getTenantId());
        //密钥
        commonRoleEntity.setSecretKey(entity.getSecretKey());
        //产品id
        commonRoleEntity.setProductId(entity.getProductId());
        return commonRoleEntity;
    }

    private Object getBoFromServiceData(ServiceData serviceData) {
        if(null == serviceData){
            return null;
        }
        if(null == serviceData.getCode()){
            return null;
        }
        if(!RetCode.SUCCESS_CODE.equals(serviceData.getCode().getCode())){
            String errorMsg = JSON.toJSONString(serviceData);
            logger.error("serviceData返回Code为不成功:{}", errorMsg);
            String errMsg = errorMsg.length() > 2000 ? errorMsg.substring(0, 2000) : errorMsg;
            //返回值 不是 0000
            throw new BusiException(RetCode.BUSINESSERROR_CODE, errMsg);
        }
        return serviceData.getBo();
    }



    /**
     * 根据用户工号 场景编码， 获取用户拥有角色
     * @param entity 入参（包括工号、模块id、产品id等）
     * @return 返回角色和角色信息的键值对map
     * @throws BusiException 业务异常
     */
    @Override
    public Map<String,RoleVO> getRoleMap(CommonModuleIdEntity entity) throws BusiException {

        List<RoleVO> roleList = getRoleListModule(entity);
        //实例化返回对象，键：角色编码   值：角色信息
        Map<String,RoleVO> roleMap = new HashMap<>(10);
        if(CollectionUtils.isEmpty(roleList)){
            return roleMap;
        }
        return roleList.stream().collect(Collectors.toMap(RoleVO::getRoleCode, Function.identity()));
    }

    /**
     * 根据工号, 角色ID列表及约束编码查询对应约束下的权限数据
     * @param roleIdList 需要查询约束权限的角色id列表
     * @param constraintCode 需要查询的约束编码
     * @param entity 其他参数，如模块id、工号、token等，其中模块id、产品id、密钥等如果不传则自动从配置文件里面取。
     * @return java.util.List<java.lang.String> 返回权限数据列表（已去重）
     */
    @Override
    public List<String> getAuthInfoByUserAndRoleId(List<String> roleIdList, String constraintCode,  CommonModuleIdEntity entity)  {
        logger.info("getAuthInfoByUserAndRoleCode roleIdList:{}, constraintCode:{}", roleIdList, constraintCode);
        Set<String> resultCodeSet = new HashSet<>();
        ConstraintListVO constraintListVO = new ConstraintListVO();
        constraintListVO.setEmpidui(entity.getEmpidui());
        constraintListVO.setToken(entity.getToken());
        ConstraintRoleListAndUserVO constraintVO = new ConstraintRoleListAndUserVO();
        //获取约束id
        String constraintId = this.getConstraintId(constraintCode, entity);
        constraintVO.setConstraintId(constraintId);
        // 用户工号
        constraintVO.setUserId(entity.getEmpidui());
        //设置角色ID列表
        constraintVO.setRoleIdList(roleIdList);
        //封装请求格式 step1
        List<ConstraintRoleListAndUserVO> reqList = new ArrayList<>();
        reqList.add(constraintVO);
        //封装请求格式 step2
        constraintListVO.setConstraintList(reqList);
        constraintListVO.setModuleId(entity.getModuleId());
        //获取权限信息
        ServiceData serviceData = AuthorityClient.queryUserRoleConstraintDataAuthWithParse(constraintListVO);
        logger.info("getAuthInfoByUserAndRoleId constraintListVO=【{}】, sd:{}", JSON.toJSONString(constraintListVO), JSON.toJSONString(serviceData));
        //将获取的到的权限信息取出放到set格式的返回值中
        if (null != serviceData.getCode() && RetCode.SUCCESS_CODE.equals(serviceData.getCode().getCode()) && null != serviceData.getBo()) {
            List<ConstraintData> constraintValueList = JSON.parseArray(JSON.toJSONString(serviceData.getBo()), ConstraintData.class);
            logger.info("constraintValueList:{}", constraintValueList);
            if (CollectionUtils.isNotEmpty(constraintValueList)) {
                for (ConstraintData returnConstraintValue : constraintValueList) {
                    String constraintValue = returnConstraintValue.getConstraintValue();
                    if (StringUtils.isNotEmpty(constraintValue)) {
                        Set<String> resultSet = Arrays.stream(constraintValue.split(",")).collect(Collectors.toSet());
                        resultCodeSet.addAll(resultSet);
                    }
                }
            }
        }
        return new ArrayList<>(resultCodeSet);
    }

    /**
     * 根据约束编码获取约束id
     * @param constraintCode 约束编码，如“ORG”等
     * @param commEntity  其他参数，如模块id、工号、token等，其中模块id、产品id、密钥等如果不传则自动从配置文件里面取。
     * @return String 约束id
     */
    @Override
    public String getConstraintId(String constraintCode, CommonModuleIdEntity commEntity) {
        String constraintId = "";
        ConstraintEntity constraintEntity = new ConstraintEntity();
        constraintEntity.setCode(constraintCode);
        // 封装约束id到请求格式中
        constraintEntity.setToken(commEntity.getToken());
        constraintEntity.setEmpidui(commEntity.getEmpidui());
        // 设置moduleId
        constraintEntity.setModuleId(commEntity.getModuleId());
        logger.info("getConstraintId.ConstraintEntity:{}", JSON.toJSONString(constraintEntity));
        ServiceData authResult = AuthorityClient.getConstraintByCodeOnProduct(constraintEntity);

        if (null != authResult && null != authResult.getCode() && RetCode.SUCCESS_CODE.equals(authResult.getCode().getCode())) {
            ConstraintVo constraintVo = (ConstraintVo) authResult.getBo();
            if (null != constraintVo) {
                Long id = constraintVo.getId();
                if (null != id) {
                    constraintId = String.valueOf(id);
                }
            }
        }
        return constraintId;
    }

    @Override
    public Boolean hasPermissionInRoleCodes(List<String> roleCodes) {
        CommonModuleIdEntity commonModuleIdEntity = new CommonModuleIdEntity();
        commonModuleIdEntity.setEmpidui(CommonUtils.getEmpNo());
        commonModuleIdEntity.setToken(CommonUtils.getAuthValue());
        Map<String, RoleVO> roleMap = this.getRoleMap(commonModuleIdEntity);

        for (String roleCode : roleCodes) {
            if (Objects.nonNull(roleMap.get(roleCode))) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }
}
