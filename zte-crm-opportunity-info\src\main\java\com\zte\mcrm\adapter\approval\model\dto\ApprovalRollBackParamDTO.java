package com.zte.mcrm.adapter.approval.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description: 流程回退参数
 * @author: 10243305
 * @date: 2021/7/8 下午3:06
 */
@Data
public class ApprovalRollBackParamDTO implements Serializable {

    @ApiModelProperty("应用编码")
    private String appCode;

    @ApiModelProperty("流程id")
    private String flowCode;

    @ApiModelProperty("业务id")
    private String businessId;

    @ApiModelProperty("流程实例id")
    private String flowInstanceId;

    @ApiModelProperty("撤销原因描述")
    private String opinion;

    @ApiModelProperty("撤销扩展原因描述")
    private String extOpinion;

    @ApiModelProperty("目标节点")
    private String targetNodeKey;
}
