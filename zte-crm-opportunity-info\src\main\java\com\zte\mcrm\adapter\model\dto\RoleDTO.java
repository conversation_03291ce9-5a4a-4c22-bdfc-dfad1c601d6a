package com.zte.mcrm.adapter.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2021/9/24
 */
@Setter
@Getter
@ToString
@ApiModel(description="角色")
public class RoleDTO {

    @ApiModelProperty(value = "角色ID")
    private Long id;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "产品ID")
    private String productId;

    @ApiModelProperty(value = "模块ID")
    private String moduleId;

    @ApiModelProperty(value = "角色编码")
    private String roleCode;

    @ApiModelProperty(value = "来源")
    private String source;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "角色状态")
    private String roleStatus;

    @ApiModelProperty(value = "范围类型")
    private String scopeType;

    @ApiModelProperty(value = "角色标签")
    private String roleTag;

    @ApiModelProperty(value = "父节点ID")
    private String parentId;

    @ApiModelProperty(value = "扩展属性1")
    private String expansionAttrOne;

    @ApiModelProperty(value = "扩展属性2")
    private String expansionAttrTwo;

    @ApiModelProperty(value = "扩展属性3")
    private String expansionAttrThree;

    @ApiModelProperty(value = "扩展属性4")
    private String expansionAttrFour;

    @ApiModelProperty(value = "扩展属性5")
    private String expansionAttrFive;

    @ApiModelProperty(value = "角色名称中文")
    private String roleNameCN;

    @ApiModelProperty(value = "角色名称英文")
    private String roleNameEN;

    @ApiModelProperty(value = "租户名称中文")
    private String tenantNameCN;

    @ApiModelProperty(value = "产品名称中文")
    private String productNameCN;

    @ApiModelProperty(value = "模块名称种文")
    private String moduleNameCN;

    @ApiModelProperty(value = "租户名称英文")
    private String tenantNameEN;

    @ApiModelProperty(value = "产品名称英文")
    private String productNameEN;

    @ApiModelProperty(value = "模块名称英文")
    private String moduleNameEN;

}
