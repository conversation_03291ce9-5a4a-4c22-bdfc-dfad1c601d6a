package com.zte.mcrm.channel.model.mapper;

import com.zte.mcrm.channel.model.entity.ApprovalStartParamsBO;
import com.zte.mcrm.channel.model.entity.OpportunityDetail;
import org.apache.logging.log4j.util.Strings;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/10/9
 */
@Mapper
public interface ApprovalStartParamsMapper {
    String DATE_FORMAT = "yyyy-MM-dd hh:mm:ss";

    ApprovalStartParamsMapper INSTANCE = Mappers.getMapper(ApprovalStartParamsMapper.class);

    @Mapping(source = "created", target = "created", qualifiedByName = "dateToString")
    @Mapping(source = "totalAmount", target = "totalAmount", qualifiedByName = "bigDecimalToString")
    ApprovalStartParamsBO transOpportunityDetailToApprovalStartParamsBO(OpportunityDetail opportunityDetail);

    @Named("dateToString")
    default String dateToString(Date date) {
        if(Objects.nonNull(date)) {
            LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            return localDateTime.format(DateTimeFormatter.ofPattern(DATE_FORMAT));
        }
        return Strings.EMPTY;
    }

    @Named("bigDecimalToString")
    default String bigDecimalToString(BigDecimal decimal) {
        if (decimal != null) {
            return decimal.toString();
        }
        return Strings.EMPTY;
    }


}
