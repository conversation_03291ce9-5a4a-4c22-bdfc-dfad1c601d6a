package com.zte.mcrm.adapter.mail.domain;

/**
 * 邮件优先级
 */
public enum MailPriority {
    /**
     * 紧急，重要，及时
     */
    HIGHEST("Highest", 10),
    /**
     * 紧急，非重要
     */
    ABOVE_NORMAL("AboveNormal", 20),
    /**
     * 一般
     */
    NORMAL("Normal", 30),
    /**
     *  非紧急, 重要
     */
    BELOW_NORMAL("BelowNormal", 40),
    /**
     * 非紧急，非重要
     */
    LOWEST("Lowest", 50);

    private String desc;
    private Integer grade;

    MailPriority(String desc, Integer grade) {
        this.desc = desc;
        this.grade = grade;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getGrade() {
        return grade;
    }
}
