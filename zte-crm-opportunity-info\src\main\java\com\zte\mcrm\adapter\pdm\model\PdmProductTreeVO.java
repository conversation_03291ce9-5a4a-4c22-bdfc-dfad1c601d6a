
package com.zte.mcrm.adapter.pdm.model;

import java.util.List;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class PdmProductTreeVO {
    @ApiModelProperty("中文名称")
    private String cnName;
    @ApiModelProperty("组织ID")
    private Integer devOrgId;
    @ApiModelProperty("英文名称")
    private String enName;
    @ApiModelProperty("是否有效Y/N")
    private String enabledFlag;
    @ApiModelProperty("ID")
    private String id;
    @ApiModelProperty("线索ID")
    private String inheritId;
    @ApiModelProperty("关联ID")
    private String innerClass;
    @ApiModelProperty("层级")
    private String itemLevel;
    @ApiModelProperty("产品名称")
    private String name;
    @ApiModelProperty("产品编号")
    private String no;
    @ApiModelProperty("父元素id")
    private String pid;
    @ApiModelProperty("产品类别")
    private String prodPurpose;
    @ApiModelProperty("子元素")
    private List<PdmProductTreeVO> children;
}
