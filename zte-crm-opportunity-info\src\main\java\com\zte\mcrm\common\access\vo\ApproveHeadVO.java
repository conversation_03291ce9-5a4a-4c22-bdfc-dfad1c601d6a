package com.zte.mcrm.common.access.vo;

import lombok.Data;

/**
 * 审批流头
 * <AUTHOR>
 *
 */
@Data
public class ApproveHeadVO {

	private String id;
	/** 当前审批节点*/
	private String currentNode;
	/** 描述*/
	private String description;
	/** 对象名称*/
	private String objectName;
	/** 对象Id*/
	private String objectId;
	/** 对象Code*/
	private String objectCode;
	/** 提交人底层部门*/
	private String department;
	/** 提交人底层部门Id*/
	private String departmentId;
	/** 提交人*/
	private String submiter;
	/** 提交人*/
	private String submiterId;
	/**  审批人ID*/
	private String approveEmpId;
	/** 审批树Id*/
	private String treeId;
	/** 审批类型编码*/
	private String approveTypeCode;
	/**  提交人工号*/
	private String submiterNo;
	/**客户审批意见*/
	public String approveAdvice;
	
	private String approveId;
	/**主管部门 add by 石浪生*/
	public String mainDeptName;

	/**
	 * 主管部门二层单位 add by 10259700
	 */
	private String secondFloorUnit;

	/**
	 * 资信当前综合评级
	 */
	private String totalRating;

	/**
	 * 资信信用额度
	 */
	private String creditLimit;

	/**
	 * 资信可用信用额度
	 */
	private String availableCredit;

	/**
	 * 资信申请日期
	 */
	private String applicationDate;

	/**
	 * 资信失效日期
	 */
	private String closeDate;

	/**
	 * 资信风险信用提示
	 */
	private String accntRistTip;

	/**
	 * 客户范围
	 */
	private String acctRangeCode;

	/**资信评级邮件的评级状态 add by 石浪生*/
	public String status;
	public String statusName;
	
	public String forCreditLinkId;
	
	public String urlLink;
	
	public String nextNodeId;
	
	public String lastNodeId;
	/***反贿赂合规审批，评审结果*/
	private String approveResult;
	/***反贿赂合规审批，评审结果--英文*/
	private String approveResultEng;
	/***------客户联系人信息--------**/
	/**客户名称**/
	private String accntName;
	/**联系人名称**/
	private String name;
	/**性别**/
	private String sex;
	/**办公邮箱**/
	private String officeMail;
	/**岗位**/
	private String job;
	/**联系人级别**/
	private String accntLevel;
	/**客户经理A角**/
	private String accntManagerA;
	/**客户经理B角**/
	private String accntManagerB;

}
