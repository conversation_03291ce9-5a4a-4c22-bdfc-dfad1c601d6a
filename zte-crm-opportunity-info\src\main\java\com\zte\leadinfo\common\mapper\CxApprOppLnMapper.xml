<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.leadinfo.common.mapper.CxApprOppLnMapper">

    <resultMap id="BaseResultMap" type="com.zte.leadinfo.common.entity.CxApprOppLn">
            <id property="rowId" column="ROW_ID" jdbcType="VARCHAR"/>
            <result property="created" column="CREATED" jdbcType="TIMESTAMP"/>
            <result property="createdBy" column="CREATED_BY" jdbcType="VARCHAR"/>
            <result property="lastUpd" column="LAST_UPD" jdbcType="TIMESTAMP"/>
            <result property="lastUpdBy" column="LAST_UPD_BY" jdbcType="VARCHAR"/>
            <result property="activeFlg" column="ACTIVE_FLG" jdbcType="VARCHAR"/>
            <result property="approveNodeId" column="APPROVE_NODE_ID" jdbcType="VARCHAR"/>
            <result property="approveUserId" column="APPROVE_USER_ID" jdbcType="VARCHAR"/>
            <result property="operationType" column="OPERATION_TYPE" jdbcType="VARCHAR"/>
            <result property="approveAdvice" column="APPROVE_ADVICE" jdbcType="VARCHAR"/>
            <result property="approveTransfer" column="APPROVE_TRANSFER" jdbcType="VARCHAR"/>
            <result property="apprOprHeadId" column="APPR_OPR_HEAD_ID" jdbcType="VARCHAR"/>
            <result property="approveDate" column="APPROVE_DATE" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ROW_ID,CREATED,CREATED_BY,
        LAST_UPD,LAST_UPD_BY,ACTIVE_FLG,
        APPROVE_NODE_ID,APPROVE_USER_ID,OPERATION_TYPE,
        APPROVE_ADVICE,APPROVE_TRANSFER,APPR_OPR_HEAD_ID,
        APPROVE_DATE
    </sql>
    <select id="listByOpptyIds" resultType="com.zte.leadinfo.common.entity.CxApprOppLn">
        select <include refid="Base_Column_List" />
        from commonmanager.cx_appr_opp_ln
        where APPR_OPR_HEAD_ID in (
            select ROW_ID from commonmanager.cx_appr_op_head
            where OBJECT_ID in <foreach collection="optyIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and APPROVE_OBJECT in <foreach collection="approveObjects" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        )
    </select>
</mapper>
