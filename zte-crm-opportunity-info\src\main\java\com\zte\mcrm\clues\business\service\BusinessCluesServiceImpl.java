
package com.zte.mcrm.clues.business.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.zte.itp.msa.core.locale.LocaleMessageSourceBean;
import com.zte.mcrm.account.access.vo.Account;
import com.zte.mcrm.account.business.service.AccountService;
import com.zte.mcrm.authority.business.IAuthorityClientService;
import com.zte.mcrm.clues.access.dao.BusinessCluesDao;
import com.zte.mcrm.clues.access.dao.CluesSaveDao;
import com.zte.mcrm.clues.access.vo.BusinessClues;
import com.zte.mcrm.clues.access.vo.BusinessCluesInfoVO;
import com.zte.mcrm.clues.common.CluesSysConst;
import com.zte.mcrm.clues.util.CluesAuthUtil;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.model.PersonAndOrgInfoVO;
import com.zte.mcrm.common.util.EmployeeUtil;
import com.zte.mcrm.common.util.RequestMessage;
import com.zte.mcrm.common.util.RowIdUtil;
import com.zte.mcrm.lov.access.dao.LovDao;
import com.zte.mcrm.lov.business.service.LovService;
import com.zte.mcrm.number.business.service.NumberService;
import com.zte.mcrm.opportunity.access.dao.PCOpportunityDao;
import com.zte.mcrm.opportunity.access.vo.Auth;
import com.zte.mcrm.opportunity.access.vo.ListOfValueOpty;
import com.zte.mcrm.opportunity.access.vo.RoleInfomation;
import com.zte.mcrm.opportunity.business.service.PCBusinessOpporunityService;
import com.zte.mcrm.opportunity.common.OppSysConst;
import com.zte.mcrm.opportunity.utils.OppAuthUtils;
import com.zte.mcrm.opportunity.utils.PersonAndOrgInfoUtil;
import com.zte.mcrm.org.business.service.OrganizationService;
import com.zte.mcrm.predict.access.vo.Product;
import com.zte.mcrm.product.business.service.ProductService;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.common.exception.SiebelErrorAuthDeniedException;
import com.zte.springbootframe.common.model.HttpResultData;
import com.zte.springbootframe.common.serviceregister.MicroServiceWithConfigUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.page.PageQuery;
import com.zte.springbootframe.util.string.StringHelper;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/****
 *
 * <AUTHOR> @date 2021/1/22
 **/
@Service
public class BusinessCluesServiceImpl implements BusinessCluesService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private BusinessCluesDao businessCluesDao;
    @Autowired
    private LovService lovService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private CluesSaveDao cluesSaveDao;
    @Autowired
    private NumberService numberService;
    @Autowired
    private LovDao lovDao;
    @Autowired
    private BusinessCluesDao pcbusinessCluesDao;
    @Autowired
    private PCBusinessOpporunityService pcBusinessOpporunityService;
    @Autowired
    private ProductService productService;
    @Autowired
    private PCBusinessCluesService pcBusinessCluesService;
    @Autowired
    private LocaleMessageSourceBean localeMessageSourceBean;
    @Autowired
    private PCOpportunityDao pcOpportunityDao;
    @Autowired
    MessageSource messageSource;
    @Autowired
    CluesSaveService cluesSaveService;
    @Autowired
    private AccountService accountService;
    @Autowired
    IAuthorityClientService authorityClientService;
    @Autowired
    IClueAuthService clueAuthService;

    /**
     * 查询线索详情
     *
     * @param businessClues
     * @return
     * @throws Exception
     */
    @Override
    public BusinessClues selectBaseInfo(BusinessClues businessClues) throws BusiException {
        BusinessClues result = new BusinessClues();
        try {
            result = businessCluesDao.selectBaseInfo(businessClues);
            if (null == result) {
                return null;
            }
            //当前人是否可以线索转立项
            boolean transferAuth = this.checktransferAuthClue(businessClues);
            //当前人是否可以线索分配
            boolean assignedAuth = this.checkAssignedAuthClue(businessClues);
            //当前登录人是否可以回退线索
            boolean backLeadAuth = isBelongCustomerMgr(businessClues) && isLeadUpdate(businessClues);

            setValue2BusinessClues(result);

            //获取关闭原因值列表
            result.setReasonList(lovService.getLovsAllByHttp(CluesSysConst.ZTE_LEAD_CLOSED_REASON, "Y"));
            List<String> idList = getValue(result, transferAuth, assignedAuth, backLeadAuth);
            setProduct(result, idList);
            // 组织查询
            List<String> orgs = new ArrayList<>();
            orgs.add(result.getDeptId());
            List<PersonAndOrgInfoVO> hrOrgs = PersonAndOrgInfoUtil.getOrgInfo(orgs);
            if(CollectionUtils.isNotEmpty(hrOrgs) && null != hrOrgs.get(0)) {
                result.setFullDepetName(hrOrgs.get(0).getHrOrgNamePath());
                result.setDeptName(hrOrgs.get(0).getHrOrgName());
            }
        } catch (Exception e) {
            logger.error("线索详情查询出现异常：" + e.getMessage(), e);
        }
        return result;
    }

    private Map<String, String> getHeaderMap(BusinessClues businessClues) {
        Map<String, String> headerParamsMap = new HashMap<>(3);
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_TENANT_ID, businessClues.getxTenantId());
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_LANG_ID, businessClues.getxLangId());
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO, businessClues.getxEmpNo());
        return headerParamsMap;
    }

    private void setValue2BusinessClues(BusinessClues result) throws Exception {
        // 转换相关值信息,状态
        result.setStatus(lovService.getlovVal(CluesSysConst.ZTE_LEAD_STATUS, result.getStatusCode()));
     // 业务范围
        result.setBusinessType(lovService.getlovVal(CluesSysConst.ZTE_OPPTY_BUS_TYPE, result.getBusinessTypeCode()));
     // 业务范围Id
        result.setBusinessTypeId(lovService.getlovId(CluesSysConst.ZTE_OPPTY_BUS_TYPE, result.getBusinessTypeCode()));
     // 销售模式
        result.setSaleMode(lovService.getlovValByCode(CluesSysConst.ZTE_OPTY_SALES, result.getSaleModelCode(), lovService.getlovId(CluesSysConst.ZTE_OPPTY_TYPE, result.getAcctTypeCode())));
     // 销售模式Id
        result.setSaleModelId(lovService.getlovIdByCode(CluesSysConst.ZTE_OPTY_SALES, result.getSaleModelCode(), lovService.getlovId(CluesSysConst.ZTE_OPPTY_TYPE, result.getAcctTypeCode())));

        //移动端修改线索，把历史值列表做转换
        result.setClueSource(lovService.getlovVal(CluesSysConst.ZTE_OPPORTUNITY_SOURCE, pcBusinessOpporunityService.optySouceCodeToNewCode(CluesSysConst.ZTE_OPPORTUNITY_SOURCE, result.getClueSourceCode())));
        // 线索来源
        result.setClueSourceId(lovService.getlovId(CluesSysConst.ZTE_OPPORTUNITY_SOURCE, pcBusinessOpporunityService.optySouceCodeToNewCode(CluesSysConst.ZTE_OPPORTUNITY_SOURCE, result.getClueSourceCode())));
        // 线索来源id
        result.setClueSourceCode(pcBusinessOpporunityService.optySouceCodeToNewCode(CluesSysConst.ZTE_OPPORTUNITY_SOURCE, result.getClueSourceCode()));
     // 客户类型
        result.setAcctType(lovService.getlovVal(CluesSysConst.ZTE_OPPTY_TYPE, result.getAcctTypeCode()));
     // 客户类型id
        result.setAcctTypeId(lovService.getlovId(CluesSysConst.ZTE_OPPTY_TYPE, result.getAcctTypeCode()));
     // 国内国际
        result.setArea(lovService.getlovVal(CluesSysConst.ZTE_ACC_MARKET_SCOPE, result.getAreaCode()));
     // 行业
        result.setParentTrade(lovService.getlovVal(CluesSysConst.ZTE_PARENT_TRADE, result.getParentTradeCode()));
     // 行业id
        result.setParentTradeId(lovService.getlovId(CluesSysConst.ZTE_PARENT_TRADE, result.getParentTradeCode()));
     // 子行业
        result.setChildTrade(lovService.getlovVal(CluesSysConst.ZTE_CHILD_TRADE, result.getChildTradeCode()));
     // 子行业id
        result.setChildTradeId(lovService.getlovId(CluesSysConst.ZTE_CHILD_TRADE, result.getChildTradeCode()));
     // 关闭原因
        result.setReason(lovService.getlovVal(CluesSysConst.ZTE_LEAD_CLOSED_REASON, result.getReasonCode()));
     // 关闭原因Id
        result.setReasonId(lovService.getlovId(CluesSysConst.ZTE_LEAD_CLOSED_REASON, result.getReasonCode()));

        //是否融资
        result.setFoundFlg(lovService.getlovVal(CluesSysConst.ZTE_BOOLEAN_STATUS, result.getFoundFlgCode()));
        //客户属性
        result.setAccountAttribute(lovService.getlovVal(CluesSysConst.ZTE_ACCOUNT_ATTRIBUTE, result.getAccountAttributeCode()));
        //潜在融资模式
        result.setPotentialModel(lovService.getlovVal(CluesSysConst.ZTE_POTENTIAL_FOUND_MODEL, result.getPotentialModelCode()));
    }

    private List<String> getValue(BusinessClues result, boolean transferAuth, boolean assignedAuth, boolean backLeadAuth) throws BusiException {
        makeSimple(result, transferAuth);
        if (assignedAuth) {
            // 设置是否分配可线索
            result.setIsAssigned("Y");
        }
        if (backLeadAuth) {
            result.setIsBack("Y");
            result.setBackReason(lovService.getLovsAllByHttp(CluesSysConst.ZTE_LEAD_REFUSED_REASON, "Y"));
        }
        //获取产品体系分类和大产品线名称
        List<String> idList = new ArrayList<>();
        if (StringUtils.isNotBlank(result.getProdSystemId())) {
            idList.add(result.getProdSystemId());
        }
        if (StringUtils.isNotBlank(result.getBigProdcutLineId())) {
            idList.add(result.getBigProdcutLineId());
        }
        return idList;
    }

    private void makeSimple(BusinessClues result, boolean transferAuth) throws BusiException {
    	// 获取客户名称及客户类型
        if (null != result.getAcctId()) {
            setAccountNameAndType(result);
        }
        // 设置最终客户
        if (null != result.getLastAcctId()) {
            setLastAccountName(result);
        }
        // 服务调用获取代表处名字
        if (null != result.getDeptId()) {
            setDeptName(result);
        }
        // 获取归属客户经理
        if (null != result.getOwnerMgr() && !result.getOwnerMgr().isEmpty()) {
            PersonAndOrgInfoVO employee = EmployeeUtil.getEmployeeByEmpNo(result.getOwnerMgr());
            if(null != employee) {
                // 姓名
                result.setOwnerName(employee.getEmpName());
                // 工号
                result.setOwnerNo(employee.getEmpNO());	
            }
        }
        if (transferAuth) {
            // 设置是否可转商机
        	result.setIsTransfer("Y");	
        }
    }

    private void setDeptName(BusinessClues result) {
        Account org = accountService.getAccount(result.getDeptId());
        if (null != org) {
        	// 设置代表处
            result.setDeptName(org.getOrgName());
        }
    }

    private void setLastAccountName(BusinessClues result) {
        Account lastAcct = accountService.getAccount(result.getLastAcctId());
        if (null != lastAcct) {
            result.setLastAcctName(lastAcct.getAccountName());
        }
    }

    private void setAccountNameAndType(BusinessClues result) {
        Account account = accountService.getAccount(result.getAcctId());
        if (null != account) {
            // 设置客户名称
            result.setAcctName(account.getAccountName());
            result.setAreaCode(account.getCustRangeCode());
        }
    }

    private void setProduct(BusinessClues result, List<String> idList) throws Exception {
        //批量查询产品，塞回体系内部分类、大产品线
        if (!idList.isEmpty()) {
            List<Product> productList = productService.selectProductsByIds(idList);
            for (Product product : productList) {
                if (product.getId().equals(result.getProdSystemId())) {
                    result.setProdSystem(product.getName());
                }
                if (product.getId().equals(result.getBigProdcutLineId())) {
                    result.setBigProductLine(product.getName());
                }
            }
        }
    }

    private Boolean checkAddFlagByPosition(List<RoleInfomation> businessOppPositionList) {
        if (businessOppPositionList != null && !businessOppPositionList.isEmpty()) {
            for (RoleInfomation businessOppPositionVo : businessOppPositionList) {
                if (checkPositionAdd(businessOppPositionVo.getRoleNameEN())) {
                    return true;
                }
            }
        }
        return false;
    }

    private Boolean checkAssignedFlagByPosition(List<RoleInfomation> roleInfomations) {
        if (roleInfomations != null && !roleInfomations.isEmpty()) {
            for (RoleInfomation businessOppPositionVo : roleInfomations) {
                if (checkPositionAssigned(businessOppPositionVo.getRoleNameEN())) {
                    return true;
                }
            }
        }
        return false;
    }

    private Boolean checkCloseFlagByPosition(List<RoleInfomation> roleInfomations) {
        if (roleInfomations != null && !roleInfomations.isEmpty()) {
            for (RoleInfomation businessOppPositionVo : roleInfomations) {
                if (checkCloseAssigned(businessOppPositionVo.getRoleNameEN())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 商机管理员
     * @param positionCode
     * @return
     */
    private Boolean checkPositionAdd(String positionCode) {
        return OppSysConst.OPPTY_ADMIN.equalsIgnoreCase(positionCode);
    }

    /**
     * 商机分配：商机总监、国代/办事处经理可分配
     * @param positionCode
     * @return
     */
    private Boolean checkPositionAssigned(String positionCode) {
        return OppSysConst.OPPORTUNITY_DIRECTOR.equalsIgnoreCase(positionCode) || OppSysConst.POSITION_TYPE_06.equalsIgnoreCase(positionCode)
                || OppSysConst.SERVICE_OPP_MANAGER.equalsIgnoreCase(positionCode);
    }


    /**
     * 线索关闭：权限
     * @param positionCode
     * @return
     */
    private Boolean checkCloseAssigned(String positionCode) {
    	return OppSysConst.OPPORTUNITY_DIRECTOR.equalsIgnoreCase(positionCode) || OppSysConst.POSITION_TYPE_06.equalsIgnoreCase(positionCode)
                || OppSysConst.SERVICE_OPP_MANAGER.equalsIgnoreCase(positionCode)
                || OppSysConst.OPPTY_ADMIN.equalsIgnoreCase(positionCode)
                || OppSysConst.OPERATOR_VIEWER.equalsIgnoreCase(positionCode)
                || OppSysConst.GOVERMENT_VIEWER.equalsIgnoreCase(positionCode)
                || OppSysConst.OPERATOR_SERVICE_VIEWER.equalsIgnoreCase(positionCode)
                || OppSysConst.GOVERMENT_SERVICE_VIEWER.equalsIgnoreCase(positionCode)
                || OppSysConst.SERVICE_VIEWER.equalsIgnoreCase(positionCode)
                || OppSysConst.OFFICE_MANAGER.equalsIgnoreCase(positionCode)
                || OppSysConst.VICE_OFFICE_MANAGER.equalsIgnoreCase(positionCode)
                || OppSysConst.OPPORTUNITY_QUERY.equalsIgnoreCase(positionCode)
                || OppSysConst.AREA_TECH_MANAGER.equalsIgnoreCase(positionCode)
                || OppSysConst.CTO.equalsIgnoreCase(positionCode);
    }
    @Override
    public List<BusinessClues> getCluesWithAuth(PageQuery<BusinessClues> pageQuery) throws BusiException {
        List<BusinessClues> businessClues = new ArrayList<>();
        try {

            Auth authOrgs = clueAuthService.getAuthOrgs();
            pageQuery.getEntity().setAuth(authOrgs);

            businessClues = businessCluesDao.getCluesWithAuth(pageQuery);
            // 销售模式
            Map<String, String> saleModeType = lovService.getlovValList(CluesSysConst.ZTE_ORDER_SALES);
            // 业务范围
            Map<String, String> busType = lovService.getlovValList(CluesSysConst.ZTE_OPPTY_BUS_TYPE);
            // 状态
            Map<String, String> clueStatus = lovService.getlovValList(CluesSysConst.ZTE_LEAD_STATUS);
            Map<String, String> headerParamsMap = getHeaderMap(pageQuery.getEntity());
            for (BusinessClues busClues : businessClues) {
                busClues.setSaleMode(getKeyVal(saleModeType, busClues.getSaleModelCode()));
                busClues.setBusinessType(getKeyVal(busType, busClues.getBusinessTypeCode()));
                busClues.setStatus(getKeyVal(clueStatus, busClues.getStatusCode()));
                // 办事处/代表处
                setDeptName(busClues);
            }
        } catch (Exception e) {
            logger.error("线索详情查询出现异常：" + e.getMessage(), e);
        }
        return businessClues;
    }

    @Override
    public List<BusinessClues> getAccountClues(PageQuery<BusinessClues> pageQuery) throws Exception {
        List<BusinessClues> businessClues = new ArrayList<>();
        try {
            Auth authOrgs = clueAuthService.getAuthOrgs();
            pageQuery.getEntity().setAuth(authOrgs);
            businessClues = businessCluesDao.getAccountClues(pageQuery);
            // 销售模式
            Map<String, String> saleModeType = lovService.getlovValList(CluesSysConst.ZTE_ORDER_SALES);
            // 业务范围
            Map<String, String> busType = lovService.getlovValList(CluesSysConst.ZTE_OPPTY_BUS_TYPE);
            // 状态
            Map<String, String> clueStatus = lovService.getlovValList(CluesSysConst.ZTE_LEAD_STATUS);
            Map<String, String> headerParamsMap = getHeaderMap(pageQuery.getEntity());

            for (BusinessClues busClues : businessClues) {
                busClues.setSaleMode(getKeyVal(saleModeType, busClues.getSaleModelCode()));
                busClues.setBusinessType(getKeyVal(busType, busClues.getBusinessTypeCode()));
                busClues.setStatus(getKeyVal(clueStatus, busClues.getStatusCode()));
                // 办事处/代表处
                setDeptName(busClues);
            }
        } catch (Exception e) {
            logger.error("客户关联线索查询出现异常：" + e.getMessage(), e);
        }
        return businessClues;
    }

    public String getKeyVal(Map<String, String> map, String code) {
        for (Map.Entry<String, String> m : map.entrySet()) {
            if (m.getKey().equals(code)) {
                return m.getValue();
            }
        }
        return null;

    }

    @Override
    public int accountCluesCount(PageQuery<BusinessClues> pageQuery) throws Exception {
        Auth authOrgs = clueAuthService.getAuthOrgs();
        pageQuery.getEntity().setAuth(authOrgs);
        return businessCluesDao.accountCluesCount(pageQuery);
    }

    @Override
    public int countClues(PageQuery<BusinessClues> pageQuery) throws Exception {
        return businessCluesDao.countClues(pageQuery);
    }

    @Override
    public boolean checktransferAuthClue(BusinessClues businessClues) throws BusiException {
        BusinessClues result = businessCluesDao.selectBaseInfo(businessClues);
        // 获取归属客户经理
        if (StringUtils.isNotBlank(result.getOwnerMgr())) {
            PersonAndOrgInfoVO employee = EmployeeUtil.getEmployeeByEmpNo(result.getOwnerMgr());
            if (employee != null) {
            	// 姓名
                result.setOwnerName(employee.getEmpName());
             // 工号
                result.setOwnerNo(employee.getEmpNO());
            }
        }
        boolean statusFlag = result.getStatusCode().equalsIgnoreCase(OppSysConst.RENEWING) || result.getStatusCode().equalsIgnoreCase(OppSysConst.OPPTY);
        try {
            List<RoleInfomation> roleInfomations = authorityClientService.queryUserRole(businessClues.getEmpId());
            boolean addFlag = checkAddFlagByPosition(roleInfomations);

            if ((addFlag || businessClues.getEmpId().equals(result.getOwnerMgr()) && statusFlag)) {
                // 设置是否可转商机
                return true;
            }
        } catch (Exception e) {
            logger.error("获取操作人出现异常：" + e.getMessage(), e);
        }
        // 当前操作人的职位是否满足

        return false;
    }

    @Override
    public boolean checkHasAuthToReadClue(String empId, String clueId) throws BusiException {
        BusinessClues businessClues = new BusinessClues();
        businessClues.setId(clueId);
        businessClues = businessCluesDao.selectBaseInfo(businessClues);
        if (null == businessClues) {
            throw new BusiException("", localeMessageSourceBean.getMessage(CommonConst.DATA_NOT_FOUND));
        }
        String ownerId = Strings.nullToEmpty(businessClues.getOwnerMgr());
        //是否为归属客户经理、创建人
        if (ownerId.equals(empId) || empId.equals(businessClues.getCreatedBy())) {
            return true;
        }
        //是否有suborg查看权限
        else if (null != businessClues.getDeptId()) {
            Auth authOrgs = clueAuthService.getAuthOrgs();
            businessClues.setAuth(authOrgs);
            return organizationService.checkLowerOrganizationByDeptIdAndUserId(businessClues);
        }
        return false;
    }

    /**
     * 初始化线索及相关信息
     *
     * @param businessCluesInfoVO
     * @return
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = BusiException.class)
    public BusinessCluesInfoVO initBusinessCluesInfo(BusinessCluesInfoVO businessCluesInfoVO) throws BusiException {
        // 1，初始化值列表相关信息
        Map<String, String> headerParamsMap = new HashMap<>(3);
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_TENANT_ID, businessCluesInfoVO.getxTenantId());
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_LANG_ID, RequestMessage.getUserLang());
        headerParamsMap.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO, businessCluesInfoVO.getxEmpNo());
        try {
            Map<String, Object> getParams = new HashMap<>(16);
            getParams.put(CluesSysConst.PARAM_LOVTYPE, CluesSysConst.ZTE_OPPTY_BUS_TYPE);
            // 获取业务范围值列表
            businessCluesInfoVO.setBusinessTypeList(getHttpData(headerParamsMap, getParams));
            getParams.clear();
            // 获取线索来源值列表
            getParams.put(CluesSysConst.PARAM_LOVTYPE, CluesSysConst.ZTE_OPPORTUNITY_SOURCE);
            businessCluesInfoVO.setClueSourceList(getHttpData(headerParamsMap, getParams));
            getParams.clear();
            // 获取销售模式值列表
            getParams.put(CluesSysConst.PARAM_LOVTYPE, CluesSysConst.ZTE_OPTY_SALES);
            businessCluesInfoVO.setSaleModeList(getHttpData(headerParamsMap, getParams));
            getParams.clear();
            // 获取客户类型值列表
            getParams.put(CluesSysConst.PARAM_LOVTYPE, CluesSysConst.ZTE_OPPTY_TYPE);
            businessCluesInfoVO.setCustTypeList(getHttpData(headerParamsMap, getParams));
            getParams.clear();
            // 获取子行业值列表
            getParams.put(CluesSysConst.PARAM_LOVTYPE, CluesSysConst.ZTE_CHILD_TRADE);
            businessCluesInfoVO.setChildTradeList(getHttpData(headerParamsMap, getParams));
            getParams.clear();
            // 获取行业值列表
            getParams.put(CluesSysConst.PARAM_LOVTYPE, CluesSysConst.ZTE_PARENT_TRADE);
            businessCluesInfoVO.setParentTradeList(getHttpData(headerParamsMap, getParams));
            getParams.clear();

            getParams.put(CluesSysConst.PARAM_LOVTYPE, CluesSysConst.ZTE_MARKET_TYPE);
            businessCluesInfoVO.setMakeTypeList(getHttpData(headerParamsMap, getParams));
            getParams.clear();

            //是否融资
            getParams.put(CluesSysConst.PARAM_LOVTYPE, CluesSysConst.ZTE_BOOLEAN_STATUS);
            businessCluesInfoVO.setFoundFlagList(getHttpData(headerParamsMap, getParams));
            getParams.clear();
            //客户属性
            getParams.put(CluesSysConst.PARAM_LOVTYPE, CluesSysConst.ZTE_ACCOUNT_ATTRIBUTE);
            businessCluesInfoVO.setAccountAttributeList(getHttpData(headerParamsMap, getParams));
            getParams.clear();
            //潜在融资模式
            getParams.put(CluesSysConst.PARAM_LOVTYPE, CluesSysConst.ZTE_POTENTIAL_FOUND_MODEL);
            businessCluesInfoVO.setPotentialFoundModelList(getHttpData(headerParamsMap, getParams));
            getParams.clear();

            // 2，如果线索ID不为空，初始化线索详情信息
            if (StringUtils.isNotBlank(businessCluesInfoVO.getId())) {
                BusinessClues businessClues = new BusinessClues();
                businessClues.setId(businessCluesInfoVO.getId());
                businessClues.setEmpId(businessCluesInfoVO.getxEmpNo());
                businessClues.setxEmpNo(businessCluesInfoVO.getxEmpNo());
                businessClues.setxLangId(businessCluesInfoVO.getxLangId());
                businessClues.setxTenantId(businessCluesInfoVO.getxTenantId());
                businessCluesInfoVO.setBusinessClues(this.selectBaseInfo(businessClues));
            } else {
                String clueNum = numberService.generateClueCode();
                businessCluesInfoVO.setClueNum(clueNum);
            }
        } catch (Exception e) {
            logger.error("初始化线索出现异常：" + e.getMessage(), e);
        }
        return businessCluesInfoVO;
    }

    /**
     * 调服务获取值列表
     *
     * @param headerParamsMap
     * @param getParams
     * @return
     * @throws BusiException
     * @throws RouteException
     */
    @Override
    public List<ListOfValueOpty> getHttpData(Map<String, String> headerParamsMap, Map<String, Object> getParams) {
        List<ListOfValueOpty> valueTypeList = new ArrayList<>();
        List<ListOfValueOpty> valueTypeListTmp = null;
        try {
            HttpResultData httpResultLovs = MicroServiceWithConfigUtil.invokeServiceWithGetMethod("4",
                    CluesSysConst.URL_LOVALL, getParams, headerParamsMap);
            if (httpResultLovs != null) {

                String resultBo = JacksonJsonConverUtil.beanToJson(httpResultLovs.getBo());
                valueTypeListTmp = JacksonJsonConverUtil.jsonToListBean(resultBo,
                        new TypeReference<List<ListOfValueOpty>>() {
                        });
            }
            String lang = CluesSysConst.CHS;
            if (headerParamsMap.get(SysGlobalConst.HTTP_HEADER_X_LANG_ID).equals(CluesSysConst.EN_US)) {
                lang = CluesSysConst.ENU;
            }
            if (StringHelper.isNotEmpty(valueTypeListTmp)) {
                getListOfValues(valueTypeList, valueTypeListTmp, lang);
            }
        } catch (Exception e) {
            logger.error("初始化线索转换值列表出现异常：" + e.getMessage(), e);
        }
        return valueTypeList;
    }

    private void getListOfValues(List<ListOfValueOpty> valueTypeList, List<ListOfValueOpty> valueTypeListTmp, String lang) {
        for (ListOfValueOpty listOfValue : valueTypeListTmp) {
            if (OppSysConst.FLAG_Y.equalsIgnoreCase(listOfValue.getActive()) && lang.equalsIgnoreCase(listOfValue.getLang())) {
                if (OppSysConst.OPERATOR_CODE.equals(listOfValue.getLovCode())) {
                    listOfValue.setLovCode("Operator");
                } else if (OppSysConst.GOVERNMENT_CODE.equals(listOfValue.getLovCode())) {
                    listOfValue.setLovCode("Government");
                }
                valueTypeList.add(listOfValue);
            }
        }
    }

    /**
     * 线索保存时，前端返回的是值列表Id，获取所有值列表字段的Id
     *
     * @param entity
     * @return
     */
    public List<String> getLovIds(BusinessClues entity) {
        if (null == entity) {
            return null;
        }
        List<String> lovIds = new ArrayList<>();
        String sourceId = entity.getClueSourceId();
        String businessTypeId = entity.getBusinessTypeId();
        String accntTypeId = entity.getAcctTypeId();
        String saleModeId = entity.getSaleModelId();
        String parentTradeId = entity.getParentTradeId();
        String childTradeId = entity.getChildTradeId();
        String closeReasonId = entity.getReasonId();
        String backReasonId = entity.getBackReasonId();
        //线索来源
        setCluesLovId(lovIds, sourceId);
        //业务范围
        setCluesLovId(lovIds, businessTypeId);
        //客户类型
        setCluesLovId(lovIds, accntTypeId);
        //销售模式
        setCluesLovId(lovIds, saleModeId);
        //行业
        setCluesLovId(lovIds, parentTradeId);
        //子行业
        setCluesLovId(lovIds, childTradeId);
        //关闭原因
        setCluesLovId(lovIds, closeReasonId);
        //退回原因
        setCluesLovId(lovIds, backReasonId);
        return lovIds;
    }

	private void setCluesLovId(List<String> lovIds, String sourceId) {
		if (StringUtils.isNotBlank(sourceId) && !lovIds.contains(sourceId)) {
            lovIds.add(sourceId);
        }
	}

    /**
     * 线索保存时，前端返回的是值列表Id，先获取所有值列表字段的Id，批量查询值列表，再把对应的code塞回去
     *
     * @param entity
     * @param lovs
     * @return
     */
    public BusinessClues getLovCode(BusinessClues entity, List<com.zte.mcrm.lov.access.vo.ListOfValue> lovs) {
        if(CollectionUtils.isEmpty(lovs)) {
            return entity;
        }
        for (com.zte.mcrm.lov.access.vo.ListOfValue lov : lovs) {
            if(null == lov) {
                continue;
            }
            String lovId = lov.getId();
            //业务范围
            if (lovId.equals(entity.getBusinessTypeId())) {
                entity.setBusinessTypeCode(lov.getLovCode());
            }
            //客户类型
            else if (lovId.equals(entity.getAcctTypeId())) {
                entity.setAcctTypeCode(lov.getLovCode());
            }
            //销售模式
            else if (lovId.equals(entity.getSaleModelId())) {
                entity.setSaleModelCode(lov.getLovCode());
            }
            //行业
            else if (lovId.equals(entity.getParentTradeId())) {
                entity.setParentTradeId(lov.getLovCode());
            }
            //子行业
            else if (lovId.equals(entity.getChildTradeId())) {
                entity.setChildTradeId(lov.getLovCode());
            }
            //关闭原因
            else if (lovId.equals(entity.getReasonId())) {
                entity.setReasonId(lov.getLovCode());
            }
            //退回原因
            else if (lovId.equals(entity.getBackReasonId())) {
                entity.setBackReasonId(lov.getLovCode());
            }
        }
        return entity;
    }

    /**
     * 新建线索
     *
     * @param businessClues
     * @return
     * @throws BusiException
     */
    @Override
    @Transactional(rollbackFor = BusiException.class)
    public void saveClues(BusinessClues businessClues) throws BusiException {
        //区分提交、保存，确定线索状态
        if (null != businessClues.getPostType() && CluesSysConst.POST_TYPE_SUBMIT.equals(businessClues.getPostType())) {
            //归属客户经理为空
            if (StringUtils.isBlank(businessClues.getOwnerNo())) {
                businessClues.setStatusCode(CluesSysConst.STATUS_CODE_ASSIGNING);
            } else {
                businessClues.setStatusCode(CluesSysConst.STATUS_CODE_RENEWING);
            }
        } else {
            businessClues.setStatusCode(CluesSysConst.STATUS_CODE_DRAFT);
        }
        //获取归属客户经理id
        if (!StringUtils.isBlank(businessClues.getOwnerNo())) {
            businessClues.setOwnerMgr(businessClues.getOwnerNo());
        }
        //值列表Id转换成code
        List<String> lovIds = getLovIds(businessClues);
        List<com.zte.mcrm.lov.access.vo.ListOfValue> lovs = lovDao.selectLovByIds(lovIds);
        businessClues = getLovCode(businessClues, lovs);
        //数据源
        businessClues.setDatasource(CluesSysConst.MOBILE);
        // 部门ORG编码
        cluesSaveService.setOrgCodeClue(businessClues);
        //新建
        if (StringUtils.isBlank(businessClues.getId())) {
            businessClues.setId(RowIdUtil.generateRowId());
            businessClues.setClueNum(numberService.generateClueCode());
            //主表插入数据
            cluesSaveDao.saveInfoInMainTable(businessClues);
            //扩展表插入数据
            cluesSaveDao.saveInfoInExTable(businessClues);
        }
        //更新
        else {
            cluesSaveDao.updateInfoInMainTable(businessClues);
            cluesSaveDao.updateInfoInExTable(businessClues);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String mobileClueToOpty(BusinessClues businessClues) throws Exception {
        //值列表Id转换成code
        List<String> lovIds = getLovIds(businessClues);
        List<com.zte.mcrm.lov.access.vo.ListOfValue> lovs = lovDao.selectLovByIds(lovIds);
        businessClues = getLovCode(businessClues, lovs);
        //线索转商机
        return pcBusinessCluesService.transferSave(businessClues);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignedSave(BusinessClues businessClues) throws BusiException {
        //获取销售模式
        BusinessClues entity = pcbusinessCluesDao.selectBaseInfo(businessClues);
        if (null != entity) {
            businessClues.setSaleModelCode(entity.getSaleModelCode());
            businessClues.setPotentialModelCode(entity.getPotentialModelCode());
            businessClues.setAccountAttributeCode(entity.getAccountAttributeCode());
            businessClues.setParentTradeCode(entity.getParentTradeCode());
            businessClues.setChildTradeCode(entity.getChildTradeCode());
            businessClues.setLastAcctId(entity.getLastAcctId());
        }
        // 部门ORG编码
        cluesSaveService.setOrgCodeClue(businessClues);
        //更新主表
        cluesSaveDao.updateInfoInMainTable(businessClues);
        //更新子表
        cluesSaveDao.updateInfoInExTable(businessClues);
    }

    @Override
    public boolean checkAssignedAuthClue(BusinessClues businessClues) throws BusiException {
        BusinessClues result = businessCluesDao.selectBaseInfo(businessClues);
        // 获取当前操作人的职位信息
        try {
            List<RoleInfomation> roleInfomations = authorityClientService.queryUserRole(businessClues.getEmpId());
            boolean addFlag = checkAssignedFlagByPosition(roleInfomations);
            boolean statusFlag = result.getStatusCode().equalsIgnoreCase(OppSysConst.ASSIGNING) || result.getStatusCode().equalsIgnoreCase(OppSysConst.REFUSED);
            // 无归属客户经理且权限满足分配
            if (addFlag && null == result.getOwnerMgr() && statusFlag) {
                // 设置是否可转商机
                return true;
            }
        } catch (Exception e) {
            logger.error("获取操作人出现异常：" + e.getMessage(), e);
        }
        return false;
    }

    @Override
    public boolean checkClosedAuthClue(BusinessClues businessClues) throws BusiException {
        BusinessClues result = businessCluesDao.selectBaseInfo(businessClues);
        try {
        	//当前登录人所有权限
			List<RoleInfomation> roleInfomations = authorityClientService.queryUserRole(businessClues.getEmpId());
			//是否关闭线索的权限
			boolean closeFlag = checkCloseFlagByPosition(roleInfomations);
			if(!closeFlag) {
				return false;
			}
	        if (result.getStatusCode().equalsIgnoreCase(OppSysConst.ASSIGNING) || result.getStatusCode().equalsIgnoreCase(OppSysConst.REFUSED)
	                || result.getStatusCode().equalsIgnoreCase(OppSysConst.OPPTY) || result.getStatusCode().equalsIgnoreCase(OppSysConst.RENEWING)) {
	            // 设置是否可关闭
	            return true;
	        }
		} catch (Exception e) {
			e.printStackTrace();
		}
        // 职位满足关闭线索权限

        return false;
    }

    /**
     * 线索认领 **********  20171030
     */
    @Override
    public void claimClue(BusinessClues clue) throws BusiException, SiebelErrorAuthDeniedException {
    	//检查权限
    	checkAuthWithEmpIdAndClueId(clue.getEmpId(), clue.getId());
    	//获取线索
        BusinessClues oldClue = businessCluesDao.selectBaseInfo(clue);
      //检查线索是否已有客户归属经理
        checkHasOwnerMgr(oldClue);
      //线索状态检查
        claimClueStatusDocimasia(oldClue.getStatusCode());
        // 部门ORG编码
        cluesSaveService.setOrgCodeClue(clue);
        cluesSaveDao.updateInfoInMainTable(clue);
        //获取主表id
        BusinessClues entity = pcbusinessCluesDao.selectBaseInfo(clue);
        if (null != entity) {
            clue.setId(entity.getId());
            clue.setSaleModelCode(entity.getSaleModelCode());
            clue.setPotentialModelCode(entity.getPotentialModelCode());
            clue.setAccountAttributeCode(entity.getAccountAttributeCode());
            clue.setParentTradeCode(entity.getParentTradeCode());
            clue.setChildTradeCode(entity.getChildTradeCode());
            clue.setLastAcctId(entity.getLastAcctId());
            cluesSaveDao.updateInfoInExTable(clue);
        }
    }

    @Override
    public List<BusinessClues> advanceQuery(PageQuery pageQuery) {
        int count = businessCluesDao.advanceQueryCount(pageQuery);
        pageQuery.setCount(count);
        List<BusinessClues> clues = new ArrayList<>();
        if (count > 0) {
            clues = businessCluesDao.advanceQuery(pageQuery);
        }
        return clues;
    }


    /**
     * 检查是否有线索查询权限，是的话返回true，否的话抛出SiebelErrorAuthDeniedException **********  ********
     */
    @Override
    public boolean checkAuthWithEmpIdAndClueId(String empId, String clueId) throws SiebelErrorAuthDeniedException {
        int count = businessCluesDao.checkAuthWithEmpIdAndClueId(empId, clueId);
        if (count > 0) {
            return true;
        } else {
            throw new SiebelErrorAuthDeniedException("", "");
        }
    }

    /**
     * 线索认领状态检查  **********  ********
     */
    @Override
    public boolean claimClueStatusDocimasia(String statusCode) throws BusiException {
        try {
            if (null == statusCode) {
                return false;
            }
            String message = lovService.getlovVal(CluesSysConst.ZTE_LEAD_STATUS, statusCode) + messageSource.getMessage("can.not.change.under.zhe.stadus",null,LocaleContextHolder.getLocale());
            //当线索状态为待分配，被退回的情况下才可以认领，其他状态不可以认领
            if (OppSysConst.ASSIGNING.equals(statusCode) || OppSysConst.REFUSED.equals(statusCode)) {
                return true;
            } else {
                throw new Exception(message);
            }
        } catch (Exception e) {
            throw new BusiException("", e.getMessage());
        }
    }

    /**
     * 检查是否存在客户归属经理,是的话不可以认领线索，否的可以认领线索 **********  20171030
     *
     * @param businessClue
     * @throws BusiException
     */
    public void checkHasOwnerMgr(BusinessClues businessClue) throws BusiException {
        if (null != businessClue.getOwnerMgr()) {
            throw new BusiException("", localeMessageSourceBean.getMessage(CommonConst.BELONG_MANAGER_EXIST));
        }
    }


    @Override
    @Transactional(rollbackFor = BusiException.class)
    public void closeSave(BusinessClues businessClues) throws BusiException {
        //获取退回原因
        List<String> ids = new ArrayList<>();
        ids.add(businessClues.getReasonId());
        List<com.zte.mcrm.lov.access.vo.ListOfValue> lovs = lovDao.selectLovByIds(ids);
        if (null != lovs && lovs.size() > 0) {
            businessClues.setReasonCode(lovs.get(0).getLovCode());
        } else {
            throw new BusiException("", localeMessageSourceBean.getMessage(CommonConst.CLOSE_REASON_NOT_EXIST));
        }
        //获取销售模式
        BusinessClues entity = pcbusinessCluesDao.selectBaseInfo(businessClues);
        if (null != entity) {
            businessClues.setSaleModelCode(entity.getSaleModelCode());
            businessClues.setPotentialModelCode(entity.getPotentialModelCode());
            businessClues.setAccountAttributeCode(entity.getAccountAttributeCode());
            businessClues.setParentTradeCode(entity.getParentTradeCode());
            businessClues.setChildTradeCode(entity.getChildTradeCode());
            businessClues.setLastAcctId(entity.getLastAcctId());
        }
        // 部门ORG编码
        cluesSaveService.setOrgCodeClue(businessClues);
        cluesSaveDao.updateInfoInMainTable(businessClues);
        cluesSaveDao.updateInfoInExTable(businessClues);
    }

    @Override
    public boolean isBelongCustomerMgr(BusinessClues businessClues) {
        //当前登录人是否为归属客户经理
        BusinessClues result = businessCluesDao.selectBaseInfo(businessClues);
        if (null != result) {
            if (StringUtils.isBlank(businessClues.getEmpId()) || !businessClues.getEmpId().equals(result.getOwnerMgr())) {
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean isLeadUpdate(BusinessClues clue) {
        //线索状态是否为待客户经理更新
        if (businessCluesDao.isLeadUpdate(clue) > 0) {
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = BusiException.class)
    public void backLead(BusinessClues clue) throws BusiException {
        //获取退回原因code
        List<String> ids = new ArrayList<>();
        ids.add(clue.getBackReasonId());
        List<com.zte.mcrm.lov.access.vo.ListOfValue> lovs = lovDao.selectLovByIds(ids);
        if (lovs != null && lovs.size() > 0) {
            clue.setBackReasonCode(lovs.get(0).getLovCode());
        } else {
            throw new BusiException("", localeMessageSourceBean.getMessage(CommonConst.BACK_REASON_NOT_EXIST));
        }
        //获取线索销售模式
        BusinessClues entity = pcbusinessCluesDao.selectBaseInfo(clue);
        if (null != entity) {
            clue.setSaleModelCode(entity.getSaleModelCode());
            clue.setPotentialModelCode(entity.getPotentialModelCode());
            clue.setAccountAttributeCode(entity.getAccountAttributeCode());
            clue.setChildTradeCode(entity.getChildTradeCode());
            clue.setParentTradeCode(entity.getParentTradeCode());
            clue.setLastAcctId(entity.getLastAcctId());
        }
        // 部门ORG编码
        cluesSaveService.setOrgCodeClue(clue);
        cluesSaveDao.updateInfoInMainTable(clue);
        cluesSaveDao.updateInfoInExTable(clue);
    }
}
