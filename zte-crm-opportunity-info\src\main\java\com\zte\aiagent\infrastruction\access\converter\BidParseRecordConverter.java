package com.zte.aiagent.infrastruction.access.converter;

import com.zte.aiagent.infrastruction.access.po.BidParseRecordPO;
import com.zte.aiagent.ui.dto.vo.BidParseRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 解析项记录PO与VO转换器
 * 使用MapStruct进行字段一一映射转换
 *
 * <AUTHOR>
 */
@Mapper
public interface BidParseRecordConverter {

    BidParseRecordConverter INSTANCE = Mappers.getMapper(BidParseRecordConverter.class);

    /**
     * PO转VO
     *
     * @param po BidParseRecordPO
     * @return BidParseRecordVO
     */
    @Mappings({
            @Mapping(source = "rowId", target = "rowId"),
            @Mapping(source = "documentId", target = "documentId"),
            @Mapping(source = "parseType", target = "parseType"),
            @Mapping(source = "parseStatus", target = "parseStatus"),
            @Mapping(source = "parseStartTime", target = "parseStartTime"),
            @Mapping(source = "parseEndTime", target = "parseEndTime"),
            @Mapping(source = "parseDuration", target = "parseDuration"),
            @Mapping(source = "errorMessage", target = "errorMessage"),
            @Mapping(source = "extractedCount", target = "extractedCount"),
            @Mapping(source = "createdBy", target = "createdBy"),
            @Mapping(source = "createdDate", target = "createdDate"),
            @Mapping(source = "lastUpdatedBy", target = "lastUpdatedBy"),
            @Mapping(source = "lastUpdatedDate", target = "lastUpdatedDate"),
            @Mapping(source = "enabledFlag", target = "enabledFlag"),
            @Mapping(source = "tenantId", target = "tenantId"),
            @Mapping(source = "parseTypeValueCn", target = "parseTypeValueCn"),
            @Mapping(source = "parseTypeValueEn", target = "parseTypeValueEn")
    })
    BidParseRecordVO toVO(BidParseRecordPO po);

    /**
     * PO列表转VO列表
     *
     * @param poList BidParseRecordPO列表
     * @return BidParseRecordVO列表
     */
    List<BidParseRecordVO> toVOList(List<BidParseRecordPO> poList);

    /**
     * VO转PO
     *
     * @param vo BidParseRecordVO
     * @return BidParseRecordPO
     */
    @Mappings({
            @Mapping(source = "rowId", target = "rowId"),
            @Mapping(source = "documentId", target = "documentId"),
            @Mapping(source = "parseType", target = "parseType"),
            @Mapping(source = "parseStatus", target = "parseStatus"),
            @Mapping(source = "parseStartTime", target = "parseStartTime"),
            @Mapping(source = "parseEndTime", target = "parseEndTime"),
            @Mapping(source = "parseDuration", target = "parseDuration"),
            @Mapping(source = "errorMessage", target = "errorMessage"),
            @Mapping(source = "extractedCount", target = "extractedCount"),
            @Mapping(source = "createdBy", target = "createdBy"),
            @Mapping(source = "createdDate", target = "createdDate"),
            @Mapping(source = "lastUpdatedBy", target = "lastUpdatedBy"),
            @Mapping(source = "lastUpdatedDate", target = "lastUpdatedDate"),
            @Mapping(source = "enabledFlag", target = "enabledFlag"),
            @Mapping(source = "tenantId", target = "tenantId"),
            @Mapping(source = "parseTypeValueCn", target = "parseTypeValueCn"),
            @Mapping(source = "parseTypeValueEn", target = "parseTypeValueEn")
    })
    BidParseRecordPO toPO(BidParseRecordVO vo);

    /**
     * VO列表转PO列表
     *
     * @param voList BidParseRecordVO列表
     * @return BidParseRecordPO列表
     */
    List<BidParseRecordPO> toPOList(List<BidParseRecordVO> voList);
}
