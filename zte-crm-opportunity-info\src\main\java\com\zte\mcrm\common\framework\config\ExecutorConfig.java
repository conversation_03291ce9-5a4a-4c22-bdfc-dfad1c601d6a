package com.zte.mcrm.common.framework.config;


import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置类
 * <AUTHOR>
 */
@Configuration
public class ExecutorConfig {

    @Bean("sendRestrictedEntityMainThread")
    public ThreadPoolTaskExecutor sendRestrictedEntityMainThread() {
        return getUserInfoThreadPool(1, 3, "send-restricted-entity-mail-main-thread");
    }

    @Bean("commonThreadPool")
    public ThreadPoolTaskExecutor commonThreadPool() {
        return getUserInfoThreadPool(50, 50, "common-thread");
    }

    @Bean("uppThreadPool")
    public ThreadPoolTaskExecutor uppThreadPool() {
        return getUserInfoThreadPool(10, 10, "upp-thread");
    }

    @Bean("submitThreadPool")
    public ThreadPoolTaskExecutor submitThreadPool() {
        return getUserInfoThreadPool(50, 50, "submit-thread");
    }
    /**
     * 获取线程池实例
     *
     * @param corePoolSize
     * @param maxPoolSize
     * @param threadNamePrefix
     * @return ThreadPoolTaskExecutor
     */
    private ThreadPoolTaskExecutor getUserInfoThreadPool(int corePoolSize, int maxPoolSize, String threadNamePrefix) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(Integer.MAX_VALUE);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setKeepAliveSeconds(60);// 线程空闲时间
        executor.setThreadNamePrefix(threadNamePrefix);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }
}
