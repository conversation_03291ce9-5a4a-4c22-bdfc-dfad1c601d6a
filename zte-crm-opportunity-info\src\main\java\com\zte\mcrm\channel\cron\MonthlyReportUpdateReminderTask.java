package com.zte.mcrm.channel.cron;

import com.google.common.collect.Maps;
import com.zte.itp.timedjobs.annotation.ZTEJobWorker;
import com.zte.itp.timedjobs.api.ZteJobContext;
import com.zte.itp.timedjobs.api.ZteJobInterface;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.channel.constant.OpportunityCurrentStatus;
import com.zte.mcrm.channel.model.entity.OpportunityKeyInfoEntity;
import com.zte.mcrm.channel.service.channel.IOpportunityMonthReportService;
import com.zte.mcrm.channel.service.channel.IOpportunityService;
import com.zte.mcrm.channel.util.ExceptionMsgUtils;
import com.zte.mcrm.common.access.vo.ComDictionaryMaintainVO;
import com.zte.mcrm.common.business.service.IComDictionaryMaintainService;
import com.zte.mcrm.common.business.service.MessageService;
import com.zte.mcrm.common.model.ComMsgForwardDTO;
import com.zte.mcrm.logger.bussiness.service.LoggerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@Slf4j
@ZTEJobWorker(value = "MonthlyReportUpdateReminderTask")
public class MonthlyReportUpdateReminderTask implements ZteJobInterface {


    @Autowired
    IOpportunityMonthReportService monthReportService;

    @Autowired
    IOpportunityService opportunityService;

    @Autowired
    IComDictionaryMaintainService comDictionaryMaintainService;

    @Autowired
    private LoggerService loggerService;

    @Autowired
    private MessageService messageService;

    @Value("${base.comMsgForward.msgIdForMonthReport:XS002}")
    private String msgId;

//    ThreadFactory nameFactory = new ThreadFactoryBuilder().setNameFormat("failure-mail-send-%d").build();
//    ThreadPoolExecutor monthReportReminderExecutorPool = new ThreadPoolExecutor(4, 10, 10,
//            TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(500), nameFactory,
//            new ThreadPoolExecutor.DiscardOldestPolicy());


    @Override
    public void execute(ZteJobContext zteJobContext) {
        int shardingItem = zteJobContext.getShardingItem();
        if (shardingItem != OpportunityConstant.FIRST_SHARDING) {
            return;
        }

        try {
            log.info("---------------------MonthlyReportUpdateReminderTask开始执行---------------" + "当前时间:{}", new Date());
            monthReportReminder();
            log.info("---------------------MonthlyReportUpdateReminderTask执行完成---------------" + "当前时间:{}", new Date());
        } catch (Exception e) {
            log.error("MonthlyReportUpdateReminderTask执行出错, Exception:", e);
        }

    }

    /**
     * 发送更新月报提醒
     */
    public void monthReportReminder() {

        if (!judgeNeedReminder()){
            log.info("MonthlyReportUpdateReminderTask未到指定执行时间");
            return;
        }
        // 当前月的月报归属期
        SimpleDateFormat parser = new SimpleDateFormat(OpportunityConstant.DATE_FORMAT_YYYYMM);
        String reportMonth = parser.format(new Date());

        List<OpportunityKeyInfoEntity> opportunityKeyInfoEntities = monthReportService.getMonthReportReminders(reportMonth);
        for (OpportunityKeyInfoEntity opportunityKeyInfoEntity : opportunityKeyInfoEntities) {
            try {
                    opportunityService.sendMail(opportunityKeyInfoEntity.getRowId(),
                            Collections.singletonList(opportunityKeyInfoEntity.getCreatedBy()),
                            OpportunityConstant.OPPORTUNITY_EMAIL_FOR_MONTH_REPORT_REMINDER,
                            OpportunityConstant.TEMPLATE_TYPE_NOTIFICATION);
                    sendSystemMsg(opportunityKeyInfoEntity, reportMonth);
            }catch (Exception e){
                log.error("monthReportReminder error,rowId:{}", opportunityKeyInfoEntity.getRowId(), e);
                String logStr = "rowId: " + opportunityKeyInfoEntity.getRowId() + "Error,Exception: " + ExceptionMsgUtils.getStackTrace(e, 2000);
                loggerService.saveLogger(logStr, "MonthlyReportUpdateReminderTask");
            }
        }
    }

    /**
     * 发送月报提醒系统消息
     * @param opportunityKeyInfoEntity
     * @param reportMonth
     */
    private void sendSystemMsg(OpportunityKeyInfoEntity opportunityKeyInfoEntity, String reportMonth){
        ComMsgForwardDTO.ComMsgForwardDTOBuilder msgForwardDTOBuilder = ComMsgForwardDTO.builder()
                .msgId(msgId)
                .to(Collections.singletonList(opportunityKeyInfoEntity.getCreatedBy()));
        Map<String, String> args = Maps.newHashMap();
        args.put("optyName", opportunityKeyInfoEntity.getAttrib46());
        args.put("customerName", opportunityKeyInfoEntity.getCustomerName());
        args.put("optyCd", opportunityKeyInfoEntity.getOptyCd());
        args.put("reportMonth", reportMonth);
        msgForwardDTOBuilder.args(args);
        log.info("MonthlyReportUpdateReminderTask.sendMessageAsync,args:{}", msgForwardDTOBuilder.build());
        Boolean result = messageService.sendMessageAsync(msgForwardDTOBuilder.build());
        log.info("MonthlyReportUpdateReminderTask.sendMessageAsync,result:{}", result);
    }

    /**
     * 判断是否在发送提醒邮件的时间范围内
     * @return
     */
    private boolean judgeNeedReminder(){
        // 月报更新提醒时间，每月最后n天
        int dayToMonthlyReportReminder = getEndDayToMonthlyReportReminder();

        Date thisMonthLastDays = getThisMonthLastDays(dayToMonthlyReportReminder);
        if (thisMonthLastDays.after(new Date())) {
            log.info("MonthlyReportUpdateReminderTask未到指定执行时间，指定执行时间:{}", thisMonthLastDays);
            return false;
        }
        return true;
    }

    private int getEndDayToMonthlyReportReminder(){
        // 月报更新提醒时间，默认为月底前7天
        int dayToMonthlyReportReminder = 7;
        // 从数据库获取月报更新提醒时间
        List<ComDictionaryMaintainVO> comDictionaryMaintains = comDictionaryMaintainService.queryByType("dateToMonthlyReportReminder");
        if (CollectionUtils.isNotEmpty(comDictionaryMaintains)) {
            String dateToMonthlyReportReminder = comDictionaryMaintains.get(0).getCode();
            if (StringUtils.isNotBlank(dateToMonthlyReportReminder)){
                dayToMonthlyReportReminder = Integer.parseInt(dateToMonthlyReportReminder);
            }
        }
        return dayToMonthlyReportReminder;
    }

    /**
     * 获取当前月最后第{daysToMonthlyEnd}天
     * @param daysToMonthlyEnd
     * @return
     */
    private Date getThisMonthLastDays(int daysToMonthlyEnd){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.DAY_OF_MONTH, -daysToMonthlyEnd);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取当前月第一天
     * @return
     */
    private Date getFirstDayOfCurrentMonth(){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }
}
