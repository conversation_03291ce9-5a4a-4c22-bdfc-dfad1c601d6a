<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zte.mcrm.exceptionlog.access.dao.SysInterfaceLogDao">
	<!-- 保存接口日志 -->
	<insert id="saveSysInterfaceLog" parameterType="com.zte.mcrm.exceptionlog.access.vo.SysInterfaceLogVO" >
		INSERT INTO sys_interface_log
		(
		    id,
            key_field,
            call_locale,
            service_name,
            api_name,
            api_param,
            api_result,
            remark,
            leg_id,
            created_by,
            created_date,
            last_updated_by,
            last_updated_date,
            enabled_flag
		)
		VALUES
		(
		    #{id,jdbcType=VARCHAR},
            #{keyField,jdbcType=VARCHAR},
            #{callLocale,jdbcType=VARCHAR},
            #{serviceName,jdbcType=VARCHAR},
            #{apiName,jdbcType=VARCHAR},
            #{apiParam,jdbcType=VARCHAR},
            #{apiResult,jdbcType=VARCHAR},
            #{remark,jdbcType=VARCHAR},
            #{legId,jdbcType=VARCHAR},
            #{createdBy,jdbcType=VARCHAR},
            NOW(),
            #{lastUpdatedBy,jdbcType=VARCHAR},
            NOW(),
            'Y'
		)
	</insert>
	
</mapper>