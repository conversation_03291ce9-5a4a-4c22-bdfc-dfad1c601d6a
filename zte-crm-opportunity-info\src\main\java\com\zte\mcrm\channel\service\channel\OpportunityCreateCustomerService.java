package com.zte.mcrm.channel.service.channel;

import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.channel.model.dto.CustomerInfoDTO;
import com.zte.mcrm.channel.model.entity.OpportunityDetail;

/**
 * 创建客户
 */
public interface OpportunityCreateCustomerService {

    /**
     * 创建客户
     * @param customerInfoDTO
     * @return
     */
    RetCode createCustomer(CustomerInfoDTO customerInfoDTO);

    /**
     * 获取客户状态码
     * @param opportunityDetail
     * @return
     */
    RetCode getCustomerStatusCodeAndSaveCustomerCreateRecord(OpportunityDetail opportunityDetail);
}
