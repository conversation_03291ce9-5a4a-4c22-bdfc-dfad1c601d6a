package com.zte.mcrm.adapter.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 注册前模糊查询服务
 * @Author: <EMAIL>
 * @Date: 2021/07/21
 * @Description:
 */
@ApiModel(description = "公司信息搜索条件对象")
@Setter
@Getter
@ToString
public class CompanyInfoSearchDTO {
    /**
     *企业名称
     */
    @ApiModelProperty(value="企业名称" ,required=true ,example="四个关键字")
    private String name;
    /**
     *国家
     */
    @ApiModelProperty(value = "国家",required = false ,allowableValues = "CN,EN")
    private String country;


    @ApiModelProperty(value = "关键字-企业名称", required = false)
    private String keyword;

    @ApiModelProperty(value = "国家编码", required = false, example = "0001")
    private String countryCode;
}
