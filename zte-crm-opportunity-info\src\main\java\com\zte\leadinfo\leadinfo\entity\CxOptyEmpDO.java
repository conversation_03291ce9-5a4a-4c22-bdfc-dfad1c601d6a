package com.zte.leadinfo.leadinfo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@TableName(value = "cx_opty_emp")
@Data
public class CxOptyEmpDO implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "ROW_ID")
    private String rowId;

    /**
     *
     */
    @TableField(value = "ACTIVE_FLAG")
    private String activeFlag;

    /**
     *
     */
    @TableField(value = "OPPTY_ID")
    private String opptyId;

    /**
     *
     */
    @TableField(value = "EMP_ID")
    private String empId;

    /**
     * 角色
     */
    @TableField(value = "OPTY_ROLE_TYPE")
    private String optyRoleType;

    /**
     * 工号
     */
    @TableField(value = "LOGIN")
    private String login;

    /**
     * 计划开始时间
     */
    @TableField(value = "START_TIME")
    private Date startTime;

    /**
     * 计划结束时间
     */
    @TableField(value = "END_TIME")
    private Date endTime;

    /**
     * 实际结束时间
     */
    @TableField(value = "ACTUAL_END_TIME")
    private Date actualEndTime;

    /**
     * 核心信息是否开放
     */
    @TableField(value = "SECRET_INFO_FLAG")
    private String secretInfoFlag;

    /**
     *
     */
    @TableField(value = "DATA_SOURCE")
    private String dataSource;

    /**
     *
     */
    @TableField(value = "FILING_INFO_FLAG")
    private String filingInfoFlag;

    /**
     *
     */
    @TableField(value = "CREATED")
    private Date created;

    /**
     *
     */
    @TableField(value = "CREATED_BY")
    private String createdBy;

    /**
     *
     */
    @TableField(value = "LAST_UPD")
    private Date lastUpd;

    /**
     *
     */
    @TableField(value = "LAST_UPD_BY")
    private String lastUpdBy;
}