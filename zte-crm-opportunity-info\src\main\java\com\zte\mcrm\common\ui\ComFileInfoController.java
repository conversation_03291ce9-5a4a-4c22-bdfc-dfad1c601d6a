package com.zte.mcrm.common.ui;

import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.common.business.service.IComFileInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: 10245190 DengZiqiao
 * @Date: 2021年9月14日 16:01:58
 * @Version: V1.0
 */
@Api("附件上传下载API")
@RestController
@RequestMapping("/file")
public class ComFileInfoController {

    @Autowired
    private IComFileInfoService comFileInfoService;

    @ApiOperation("获取附件上传下载地址")
    @GetMapping(value = "/getUploadUrl")
    public ServiceData<String> getUploadUrl(
            @RequestHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO) String xEmpNo,
            @RequestHeader(SysGlobalConst.HTTP_HEADER_X_LANG_ID) String xLangId,
            @RequestParam("businessId") @ApiParam(value = "业务ID", required = true) String businessId,
            @RequestParam("businessType") @ApiParam(value = "业务类型", required = true) String businessType,
            @RequestParam("operationFlag") @ApiParam(value = "操作标识（1：可以上传下载，0：只读）", required = true) String operationFlag,
            @RequestParam("cipherGrade") @ApiParam(value = "是否加密（-99：不加密，2：可编辑，1：只读）", required = true) String cipherGrade) throws Exception {
        ServiceData<String> ret = new ServiceData<>();
        Map<String, String> params = new HashMap<>(6);
        params.put("xEmpNo", xEmpNo);
        params.put("xLangId", xLangId);
        params.put("businessId", businessId);
        params.put("businessType", businessType);
        params.put("operationFlag", operationFlag);
        params.put("cipherGrade", cipherGrade);
        String fileUploadUrl = comFileInfoService.getUploadUrl(params);
        ret.setBo(fileUploadUrl);
        return ret;
    }

}

