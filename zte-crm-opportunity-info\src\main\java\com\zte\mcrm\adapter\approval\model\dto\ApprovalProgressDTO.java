package com.zte.mcrm.adapter.approval.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName ApprovalProgressDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/5/18 16:18
 */
@Data
public class ApprovalProgressDTO implements Serializable {

    private static final long serialVersionUID = -3107085613539389111L;
    @ApiModelProperty(value = "审批流实例ID")
    private String flowInstanceId;
    @ApiModelProperty(value = "审批流程实例的状态")
    private String status;
    @ApiModelProperty(value = "审批任务记录列表")
    private List<ApprovalTask> approvalTaskList;
}
