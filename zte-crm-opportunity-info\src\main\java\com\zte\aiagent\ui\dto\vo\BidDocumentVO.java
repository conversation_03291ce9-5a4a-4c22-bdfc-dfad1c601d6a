package com.zte.aiagent.ui.dto.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 招标文件视图对象
 * 对应PO: BidDocumentPO
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class BidDocumentVO {
    /**
     * 主键ID
     */
    private String rowId;

    /**
     * 招标文件名称
     */
    private String fileName;

    /**
     * 文件大小(字节)
     */
    private Long fileSize;

    /**
     * 文件类型(pdf/doc/docx等)
     */
    private String fileType;

    /**
     * 文档云fileKey
     */
    private String fileKey;

    /**
     * 解析模板编码
     */
    private String parseTemplateCode;

    /**
     * 解析模板参数配置id
     */
    private String parseTemplateId;

    /**
     * 解析状态: PENDING-待解析, PARSING-解析中, SUCCESS-解析成功, FAILED-解析失败
     */
    private String parseStatus;

    /**
     * 解析开始时间
     */
    private Date parseStartTime;

    /**
     * 解析结束时间
     */
    private Date parseEndTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDate;

    /**
     * 最后更新人
     */
    private String lastUpdatedBy;

    /**
     * 最后更新时间
     */
    private Date lastUpdatedDate;

    /**
     * 有效标记(Y/N)
     */
    private String enabledFlag;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 导出解析结果的excel文件key
     */
    private String exportParsedExcelFileKey;

    /**
     * 导出解析结果的word文件key
     */
    private String exportParsedWordFileKey;

    /**
     * 标黄文档导出fileKey
     */
    private String yellowDocumentFileKey;
    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 解析进度(百分比，0-100的整数)
     */
    private Integer parseProgress;

    /**
     * 解析记录列表
     */
    private List<BidParseRecordVO> bidParseRecordList;
} 