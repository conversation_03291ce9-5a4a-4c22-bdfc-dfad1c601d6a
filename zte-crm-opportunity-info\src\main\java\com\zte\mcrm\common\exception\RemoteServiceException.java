package com.zte.mcrm.common.exception;

/**
 * 微服务点对点调用异常类，UnChecked Exception
 * @Author: 朱正梁10266679
 * @Date: 2021/6/1 9:59
 */
public class RemoteServiceException extends RuntimeException {

    private static final long serialVersionUID = 5305234786878792418L;

    private final String errorCode;
    private final String errorMsgId;
    private final String errorMsg;
    private final Object body;

    public RemoteServiceException(String errorCode, String errorMsgId, String errorMsg) {
        this(errorCode, errorMsgId, errorMsg, null, null);
    }

    public RemoteServiceException(String errorCode, String errorMsgId, String errorMsg, Object body) {
        this(errorCode, errorMsgId, errorMsg, body, null);
    }

    public RemoteServiceException(String errorCode, String errorMsgId, String errorMsg, Throwable cause) {
        this(errorCode, errorMsgId, errorMsg, null, cause);
    }

    public RemoteServiceException(String errorCode, String errorMsgId, String errorMsg, Object body, Throwable cause) {
        super(cause);
        this.body = body;
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
        this.errorMsgId = errorMsgId;
    }

    public Object getBody() {
        return body;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public String getErrorMsgId() {
        return errorMsgId;
    }
}
