<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.leadinfo.common.mapper.CxDocItemMapper">

    <resultMap id="BaseResultMap" type="com.zte.leadinfo.common.entity.CxDocItemDO">
        <id property="rowId" column="ROW_ID" jdbcType="VARCHAR"/>
        <result property="created" column="CREATED" jdbcType="TIMESTAMP"/>
        <result property="createdBy" column="CREATED_BY" jdbcType="VARCHAR"/>
        <result property="lastUpd" column="LAST_UPD" jdbcType="TIMESTAMP"/>
        <result property="lastUpdBy" column="LAST_UPD_BY" jdbcType="VARCHAR"/>
        <result property="fileName" column="FILE_NAME" jdbcType="VARCHAR"/>
        <result property="fileSize" column="FILE_SIZE" jdbcType="VARCHAR"/>
        <result property="encrypt" column="ENCRYPT" jdbcType="VARCHAR"/>
        <result property="billCode" column="BILL_CODE" jdbcType="VARCHAR"/>
        <result property="docId" column="DOC_ID" jdbcType="VARCHAR"/>
        <result property="comments" column="COMMENTS" jdbcType="VARCHAR"/>
        <result property="activeFlag" column="ACTIVE_FLAG" jdbcType="VARCHAR"/>
        <result property="fileType" column="FILE_TYPE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ROW_ID,CREATED,CREATED_BY,
        LAST_UPD,LAST_UPD_BY,FILE_NAME,
        FILE_SIZE,ENCRYPT,BILL_CODE,
        DOC_ID,COMMENTS,ACTIVE_FLAG,
        FILE_TYPE
    </sql>

</mapper>