package com.zte.mcrm.channel.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;


@Setter
@Getter
@ToString
public class OpportunityKeyInfoEntity {
    @ApiModelProperty(value = "商机id")
    String rowId;
    @ApiModelProperty(value = "商机编号")
    private String optyCd;
    @ApiModelProperty(value = "商机状态")
    private String statusCd;
    @ApiModelProperty(value = "商机当前状态")
    private String currentStatus;
    @ApiModelProperty(value = "商机名称")
    private String attrib46;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "创建人")
    private String createdBy;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpd;
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "报备成功时间")
    private Date successDate;
    @ApiModelProperty(value = "最后更新人")
    private String lastUpdBy;
    @ApiModelProperty(value = "中兴业务经理id")
    private String businessManagerId;
    @ApiModelProperty(value = "中兴业务经理名字")
    private String businessManagerName;
    @ApiModelProperty(value = "渠道商客户编码")
    private String crmCustomerCode;
    @ApiModelProperty(value = "渠道商名称")
    private String customerName;
    @ApiModelProperty(value = "最终客户名称")
    private String lastAccName;
    @ApiModelProperty(value = "最终客户编码")
    private String lastAccId;
    @ApiModelProperty(value = "流程实例编码")
    private String flowInstanceId;

    private String status;
}
