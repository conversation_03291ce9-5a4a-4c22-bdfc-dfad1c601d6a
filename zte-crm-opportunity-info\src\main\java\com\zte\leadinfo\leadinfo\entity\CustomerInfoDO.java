package com.zte.leadinfo.leadinfo.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Data
public class CustomerInfoDO {

    @ApiModelProperty("主键ID，防止全null的情况")
    private String id;

    @ApiModelProperty("最终用户信息，存在编码和ID")
    private String xLastAccId;

    @ApiModelProperty("渠道商客户编码")
    private String crmCustomerCode;

    @ApiModelProperty("客户ID或编码")
    private String prDeptOuId;

    public List<String> buildAll() {
        List<String> list = new ArrayList<>();
        list.add(Optional.ofNullable(xLastAccId).orElse(StringUtils.EMPTY));
        list.add(Optional.ofNullable(crmCustomerCode).orElse(StringUtils.EMPTY));
        list.add(Optional.ofNullable(prDeptOuId).orElse(StringUtils.EMPTY));
        return list;
    }
}
