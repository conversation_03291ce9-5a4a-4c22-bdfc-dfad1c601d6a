package com.zte.mcrm.channel.constant;


public enum ArbitrationTypeEnum {
    /**
     * 通过
     */
    FAILURE_REASON_SUCCESS(0, "通过"),

    /**
     * 已有报备
     */
    FAILURE_REASON_ALREADY(1, "已有报备"),

    /**
     * 不同意此报备
     */
    FAILURE_REASON_REFUSE(2, "不同意此报备"),

    /**
     * 受限制主体
     */
    FAILURE_REASON_RESTRICTED_PARTY(3, "受限制主体");

    private final Integer code;
    private final String name;

    ArbitrationTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static Integer getArbitrationTypeCodeByName(String valName){
        for (ArbitrationTypeEnum value : ArbitrationTypeEnum.values()) {
            if (value.name.equals(valName)){
                return value.getCode();
            }
        }
        return null;
    }

    public static String getArbitrationTypeNameByCode(Integer valCode){
        for (ArbitrationTypeEnum value : ArbitrationTypeEnum.values()) {
            if (value.code.equals(valCode)){
                return value.getName();
            }
        }
        return null;
    }
}
