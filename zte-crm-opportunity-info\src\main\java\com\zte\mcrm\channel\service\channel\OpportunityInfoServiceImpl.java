package com.zte.mcrm.channel.service.channel;

import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.crm.eva.base.infrastructure.adapter.IchannelBaseAdapter;
import com.zte.itp.authorityclient.entity.input.CommonModuleIdEntity;
import com.zte.itp.authorityclient.entity.output.RoleVO;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.account.access.vo.Account;
import com.zte.mcrm.adapter.approval.model.FlowParameter;
import com.zte.mcrm.adapter.approval.model.dto.*;
import com.zte.mcrm.adapter.approval.service.ApprovalFlowService;
import com.zte.mcrm.adapter.authorization.constant.UppAuthPropertiesConstant;
import com.zte.mcrm.adapter.authorization.dto.RoleInfoDTO;
import com.zte.mcrm.adapter.authorization.model.RoleInfo;
import com.zte.mcrm.adapter.authorization.service.ChannelAuthService;
import com.zte.mcrm.adapter.authorization.service.RoleService;
import com.zte.mcrm.adapter.authorization.service.UppAuthorityService;
import com.zte.mcrm.adapter.constant.HeaderNameConst;
import com.zte.mcrm.adapter.emdm.service.EmdmService;
import com.zte.mcrm.adapter.mail.service.SendMailService;
import com.zte.mcrm.adapter.model.dto.AccountInfo;
import com.zte.mcrm.adapter.model.dto.BlackListDTO;
import com.zte.mcrm.adapter.model.dto.CompanyInfoDTO;
import com.zte.mcrm.adapter.model.dto.UcsUserInfoDTO;
import com.zte.mcrm.adapter.model.vo.*;
import com.zte.mcrm.adapter.service.*;
import com.zte.mcrm.channel.constant.*;
import com.zte.mcrm.channel.dao.*;
import com.zte.mcrm.channel.model.dto.*;
import com.zte.mcrm.channel.model.entity.*;
import com.zte.mcrm.channel.model.mapper.ApprovalStartParamsMapper;
import com.zte.mcrm.channel.model.vo.CreateCustomerParamVO;
import com.zte.mcrm.channel.model.vo.OpportunityDetailVO;
import com.zte.mcrm.channel.model.vo.OpportunityProductVO;
import com.zte.mcrm.channel.model.vo.RestrictedPartyVO;
import com.zte.mcrm.channel.service.OpptyCustomerCreateRecordService;
import com.zte.mcrm.channel.service.common.IMessageNotifyService;
import com.zte.mcrm.channel.service.common.TeamConverter;
import com.zte.mcrm.channel.service.prm.IPrmOpportunityApprovalService;
import com.zte.mcrm.channel.util.*;
import com.zte.mcrm.clues.common.CluesSysConst;
import com.zte.mcrm.common.access.vo.ComDictionaryMaintainVO;
import com.zte.mcrm.common.business.service.IComDictionaryMaintainService;
import com.zte.mcrm.common.business.service.IKeyIdService;
import com.zte.mcrm.common.business.service.MessageService;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.consts.CommonConstant;
import com.zte.mcrm.common.model.ComMsgForwardDTO;
import com.zte.mcrm.common.model.PersonAndOrgInfoVO;
import com.zte.mcrm.common.model.SysGlobalConstVo;
import com.zte.mcrm.common.util.AccountUtil;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.mcrm.common.util.CompletableFutureWrapper;
import com.zte.mcrm.common.util.DictUtils;
import com.zte.mcrm.logger.bussiness.service.LoggerService;
import com.zte.mcrm.opportunity.utils.PersonAndOrgInfoUtil;
import com.zte.opty.common.enums.OptyStatusEnum;
import com.zte.opty.dao.SOptyDao;
import com.zte.opty.dao.SOptyTeamDao;
import com.zte.opty.dao.SOptyXDao;
import com.zte.opty.feign.CustomerClient;
import com.zte.opty.model.bo.SOptyBO;
import com.zte.opty.model.bo.SOptyProductBO;
import com.zte.opty.model.bo.SOptyTeamBO;
import com.zte.opty.model.bo.SOptyXBO;
import com.zte.opty.service.excel.dto.CustomerQueryInfoDTO;
import com.zte.opty.sync.util.CommonMapUtil;
import com.zte.opty.sync.util.LcapConverterUtil;
import com.zte.springbootframe.common.model.RetCodeCopy;
import com.zte.springbootframe.util.local.LocalMessageUtils;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.validation.ValidationException;
import javax.validation.Validator;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 商机信息相关服务
 *
 * <AUTHOR>
 * @date 2021-09-16
 */
@Service
public class OpportunityInfoServiceImpl implements IOpportunityInfoService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    IKeyIdService keyIdService;

    @Autowired
    IOpportunityService opportunityService;

    @Autowired
    OpportunityInfoServiceImpl opportunityInfoService;

    @Autowired
    IOptyCdRecordService optyCdRecordService;

    @Autowired
    IOpportunityDetailService opportunityDetailService;

    @Autowired
    IOpportunityProductService opportunityProductService;

    @Autowired
    MessageSource messageSource;

    @Autowired
    Validator globalValidator;

    @Autowired
    ChannelAuthService channelAuthService;
    @Autowired
    IPrmOpportunityApprovalService prmOpportunityApprovalService;
    @Autowired
    RoleService roleService;
    @Autowired
    OpportunityDetailDao opportunityDetailDao;
    @Autowired
    OpportunityDao opportunityDao;
    @Autowired
    CustomerInfoService customerInfoService;
    @Autowired
    ApprovalFlowService approvalFlowService;
    @Autowired
    private ApprovalInfoService approvalInfoService;
    @Autowired
    PrmService prmService;
    @Autowired
    ComposeService composeService;
    @Autowired
    UppAuthorityService uppAuthorityService;
    @Autowired
    IComDictionaryMaintainService comDictionaryMaintainService;
    @Autowired
    ComApprovalRecordDao comApprovalRecordDao;
    @Autowired
    private IBmtUcsService iBmtUcsService;
    @Autowired
    private ICpcServiceApi iCpcServiceApi;
    @Autowired
    private EmdmService emdmService;
    @Autowired
    private LoggerService loggerService;
    @Autowired
    private OpptyCustomerCreateRecordService opptyCustomerCreateRecordService;
    @Autowired
    OpportunityProductDao opportunityProductDao;
    @Autowired
    CustomerClient customerClient;

    /**
     * 客户系统有效标识
     */
    private static final String ACTIVE_STATUS = "生效";
    /**
     * 草稿状态：0-需要创建草稿，1-不需要创建草稿
     */
    private static final Integer DRAFT_STATUS_0 = 0;
    private static final Integer DRAFT_STATUS_1 = 1;
    /**
     * 业务类型
     */
    private static final String BUSINESS_TYPE = "newOpportunity";

    private static final String APPROVING_PERSON_EMPTY = "approvingPersonEmpty";
    /**
     * ichannel产品ID
     **/
    @Value("${ichannel.upp.auth.productId}")
    private Long ichannelProductId;
    /**
     * ichannel模块ID
     **/
    @Value("${ichannel.upp.auth.moduleId}")
    private String ichannelModuleId;
    /**
     * ichannel租户ID
     **/
    @Value("${ichannel.upp.auth.tenantId}")
    private Long ichannelTenantId;
    /**
     * ichannel产品密钥key
     **/
    @Value("${ichannel.upp.auth.productSecretKey}")
    private String ichannelProductSecretKey;

    @Value("${opportunity.mail.from}")
    private String mailFrom;

    @Autowired
    private SendMailService sendMailService;

    @Value("${encryption.secretKeySixteen}")
    private String secretKeySixteen;

    @Value("${mail.ssHttpHead}")
    private String ssHttpHead;

//    @Autowired
//    IComApprovalRecordService comApprovalRecordService;

    @Value("${encryption.iv}")
    private String iv;

    @Value("${opportunity.internalui.url}")
    private String internaluiUrl;

    @Value("${opportunity.ichannelui.url}")
    private String ichanneluiUrl;

    @Value("${ipartnerui.url}")
    private String ipartnerUrl;

    @Value("${prmui.url}")
    private String prmUrl;

    @Value("${base.comMsgForward.msgIdForApprovalResults:XS001}")
    private String msgId;
    @Autowired
    private OpportunityQueryDao opportunityQueryDao;
    @Autowired
    private MessageService messageService;
    @Autowired
    private IchannelBaseAdapter ichannelBaseAdapter;
    @Autowired
    private IMessageNotifyService iMessageNotifyService;

    @Autowired
    private OpportunityCreateCustomerService opportunityCreateCustomerService;

    @Autowired
    private SOptyDao sOptyDao;

    @Autowired
    private SOptyXDao sOptyXDao;

    @Autowired
    private SOptyTeamDao sOptyTeamDao;

    @Autowired
    private TeamConverter teamConverter;

    @Autowired
    IOpportunityTeamService opportunityTeamService;

    @Autowired
    IOpportunityRedundancyService opportunityRedundancyService;

    @Autowired
    CommonRemoteUtils commonRemoteUtils;
    @Resource(name="submitThreadPool")
    ThreadPoolTaskExecutor taskExecutor;

    /**
     * 提交商机报备
     *
     * @param opportunityInfo 商机报备信息
     * @return
     */
    @Override
    public ServiceData<OpportunityInfoDTO> submitOpportunity(OpportunityInfoDTO opportunityInfo) throws Exception {
        CommonModuleIdEntity commonModuleIdEntity = new CommonModuleIdEntity();
        commonModuleIdEntity.setEmpidui(CommonUtils.getEmpNo());
        commonModuleIdEntity.setToken(CommonUtils.getAuthValue());
        SysGlobalConstVo sysGlobalConstVo = CommonUtils.getSysGlobalConstVo();
        // 权限校验
        CompletableFuture<Void> permissionCheckFuture = CompletableFuture.runAsync(() ->
        {
            CommonUtils.setSysGlobalConstVo(sysGlobalConstVo);
            permissionCheckAsynchronous(commonModuleIdEntity, SourceOfOpportunityEnum.CHANNEL_FILING.getValue());
        }, taskExecutor);
        // 校验提交信息
        validateSubmitOpportunityInfo(opportunityInfo);
        permissionCheckFuture.get();
        return submitOpportunityHandler(opportunityInfo);
    }

    /**
     * 商机提交执行
     * @param opportunityInfo
     * @return
     * @throws Exception
     */
    private ServiceData<OpportunityInfoDTO> submitOpportunityHandler(OpportunityInfoDTO opportunityInfo) throws Exception {
        ServiceData<OpportunityInfoDTO> sd = new ServiceData<>();
        // 最终用户不存
        if (StringUtils.isBlank(opportunityInfo.getOpportunityDetail().getLastAccId())) {
            RetCode customerStatusCode = opportunityCreateCustomerService.getCustomerStatusCodeAndSaveCustomerCreateRecord(opportunityInfo.getOpportunityDetail());
            sd.setCode(customerStatusCode);
        }
        // 存草稿
        OpportunityInfoDTO opportunityInfoVO = storageOpportunityInfo(opportunityInfo);
        // 记录record表
        if (RetCode.SUCCESS_CODE.equals(sd.getCode().getCode()) && submitOpportunityProcess(opportunityInfo)) {
            logger.info("商机提交成功,开始修改商机状态为“报备审批中”,商机编号:{},商机id:{}", opportunityInfo.getOpportunity().getOptyCd(), opportunityInfo.getOpportunity().getRowId());
            opportunityService.updateStatus(opportunityInfo.getOpportunity().getRowId(), OptyStatusEnum.APPROVING);
        }
        sd.setBo(opportunityInfoVO);
        return sd;
    }

    /**
     * prm提交商机报备
     *
     * @param opportunityInfo 商机报备信息
     * @return
     */
    @Override
    public OpportunityInfoDTO submitPrmOpportunity(OpportunityInfoDTO opportunityInfo) throws Exception {
        logger.info("submitPrmOpportunity:{}", opportunityInfo);
        OpportunityInfoDTO opportunityInfoVO;
        // 校验用户权限
        //0. 权限校验
        CommonModuleIdEntity commonModuleIdEntity = new CommonModuleIdEntity();
        commonModuleIdEntity.setEmpidui(CommonUtils.getEmpNo());
        commonModuleIdEntity.setToken(CommonUtils.getAuthValue());
        if (!permissionCheck(commonModuleIdEntity, SourceOfOpportunityEnum.ZTE_SELF_BUILT.getValue())) {
            logger.error("提交商机权限校验不通过,opportunityInfo:{}", opportunityInfo);
            throw new BusiException(RetCode.AUTHFAILED_CODE, LocalMessageUtils.getMessage("noPermission"));
        }
        // 校验提交信息
        validateSubmitOpportunityInfo(opportunityInfo);
        opportunityInfoVO = storageOpportunityInfo(opportunityInfo);
        logger.info("商机提交成功,开始修改商机状态为“报备成功”,商机编号:{},商机id:{}", opportunityInfo.getOpportunity().getOptyCd(), opportunityInfo.getOpportunity().getRowId());
        opportunityService.updateStatus(opportunityInfo.getOpportunity().getRowId(), OptyStatusEnum.OPTY_RENEWING);
        opportunityService.updateSubmitDate(opportunityInfo.getOpportunity().getRowId(), new Date());
        return opportunityInfoVO;
    }

    @Override
    public boolean submitOpportunityProcess(OpportunityInfoDTO opportunityInfo) throws Exception {

        ApprovalStartParamsDTO startParamsDTO = setStartParams(opportunityInfo);


        return startFlowTask(startParamsDTO);

    }

    private void permissionCheckAsynchronous(CommonModuleIdEntity commonModuleIdEntity, String dataSource){
        boolean checkResult = permissionCheck(commonModuleIdEntity, dataSource);
        if (!checkResult){
            throw new BusiException(RetCode.AUTHFAILED_CODE, "noPermission");
        }
    }

    @Override
    public boolean startFlowTask(String rowId) throws Exception {
        ApprovalStartParamsDTO startParamsDTO = getStartProcessParams(rowId);
        return startFlowTask(startParamsDTO);
    }

    @Override
    public boolean startFlowTask(ApprovalStartParamsDTO startParamsDTO){
        try {
            String flowInstanceId = approvalFlowService.startFlow(startParamsDTO);
            logger.info("startFlowTask invoke success,rowId:{},flowInstanceId:{},startParamsDTO:{}", startParamsDTO.getBusinessId(), flowInstanceId, startParamsDTO);
            //将流程实例ID存入审批记录表
            saveFlowInstanceId(startParamsDTO.getBusinessId(), flowInstanceId);
            opportunityService.updateSubmitDate(startParamsDTO.getBusinessId(), new Date());
            return true;
        } catch (Exception e){
            logger.info("startFlowTask invoke failed,rowId:{}, Exception:{}", startParamsDTO.getBusinessId(),
                    ExceptionMsgUtils.getStackTrace(e, 2000));
            logger.error("startFlowTask invoke failed", e);
            String logStr = "startFlowTask invoke failed,rowId:[" + startParamsDTO.getBusinessId() + "]error:" + ExceptionMsgUtils.getStackTrace(e, 500);
            loggerService.saveLogger(logStr,"opportunity_start_process");
            throw new BusiException(RetCode.AUTHFAILED_CODE, "process.start.fail");
        }
    }



    /**
     * 获取启动流程参数
     * @param rowId
     * @return
     * @throws Exception
     */
    @Override
    public ApprovalStartParamsDTO getStartProcessParams (String rowId) throws Exception {
        OpportunityInfoDTO opportunityInfo = getOpportunityInfo(rowId);
        ApprovalStartParamsDTO startParamsDTO = setStartParams(opportunityInfo);
        return startParamsDTO;
    }

    /**
     * 根据商机id查询商机信息（商机主表、商机扩展表、产品信息）
     * @param rowId
     * @return
     */
    private OpportunityInfoDTO getOpportunityInfo(String rowId){
        Opportunity opportunity = opportunityService.get(rowId);
        OpportunityDetail opportunityDetail = opportunityDetailService.get(rowId);
        Map<String,Object> params = Maps.newHashMap();
        params.put("opptyId", rowId);
        params.put("businessType", "newOpportunity");
        List<OpportunityProduct> products = opportunityProductService.getList(params);
        OpportunityInfoDTO opportunityInfo = new OpportunityInfoDTO();
        opportunityInfo.setOpportunity(opportunity);
        opportunityInfo.setOpportunityDetail(opportunityDetail);
        opportunityInfo.setOpportunityProducts(products);
        return opportunityInfo;
    }
    /**
     * 最终用户ID为空创建客户草稿并给中兴业务经理发邮件
     * @param opportunityDetail
     * @throws Exception
     */
    @Override
    public void creatCustomer(OpportunityDetail opportunityDetail) throws Exception {
        //最终用户ID为空创建客户草稿并给中兴业务经理发邮件
        CreateCustomerParamVO createCustomerParamVO = new CreateCustomerParamVO();
        createCustomerParamVO.setRowId(opportunityDetail.getRowId());
        createCustomerParamVO.setBusinessManagerId(opportunityDetail.getBusinessManagerId());
        createCustomerParamVO.setBusinessManagerName(opportunityDetail.getBusinessManagerName());
        createCustomerParamVO.setLastAccName(opportunityDetail.getLastAccName());
        createCustomerParamVO.setDeptNo(opportunityDetail.getDeptNo());
        doCreateCustomerDraft(createCustomerParamVO);
    }

    /**
     * 权限检查
     */
    private boolean permissionCheck(CommonModuleIdEntity commonModuleIdEntity, String dataSource) {
        if (Objects.equals(dataSource, OpportunityConstant.PRM)) {
//            return prmPermissionCheck(commonModuleIdEntity);
            return true;
        } else if (Objects.equals(dataSource, OpportunityConstant.ICHANNEL)) {
            return iChannelPermissionCheck(commonModuleIdEntity);
        }
        return false;
    }

    /**
     * 检查PRM侧权限 商机创建人或管理员可以新建商机
     */
    private Boolean prmPermissionCheck(CommonModuleIdEntity commonModuleIdEntity) {
        Map<String, RoleVO> roleMap = uppAuthorityService.getRoleMap(commonModuleIdEntity);
        if (roleMap.containsKey(OpportunityRoleEnum.GEC_OPPORTUNITY_CREATOR.getCode())
                || roleMap.containsKey(OpportunityRoleEnum.GEC_OPPORTUNITY_ADMIN.getCode())) {
            return true;
        }
        return false;
    }

    /**
     * 检查iChannel侧权限
     */
    private Boolean iChannelPermissionCheck(CommonModuleIdEntity commonModuleIdEntity) {
        commonModuleIdEntity.setModuleId(ichannelModuleId);
        commonModuleIdEntity.setProductId(ichannelProductId);
        commonModuleIdEntity.setSecretKey(ichannelProductSecretKey);
        commonModuleIdEntity.setTenantId(ichannelTenantId);
        Map<String, RoleVO> roleMap = uppAuthorityService.getRoleMap(commonModuleIdEntity);
        if (roleMap.containsKey(OpportunityConstant.COMPANY_ACCOUNT)
                || roleMap.containsKey(OpportunityConstant.SERVICE_MANAGER)
                || roleMap.containsKey(OpportunityConstant.BUSINESS_MANAGER)) {
            return true;
        }
        return false;
    }


    @Override
    public Map<String, RoleVO> getChannelRoleMap(String empNo) {
        CommonModuleIdEntity commonModuleIdEntity = new CommonModuleIdEntity();
        commonModuleIdEntity.setModuleId(ichannelModuleId);
        commonModuleIdEntity.setProductId(ichannelProductId);
        commonModuleIdEntity.setSecretKey(ichannelProductSecretKey);
        commonModuleIdEntity.setTenantId(ichannelTenantId);
        commonModuleIdEntity.setEmpidui(empNo);
        commonModuleIdEntity.setToken(CommonUtils.getAuthValue());
        return uppAuthorityService.getRoleMap(commonModuleIdEntity);
    }

    @Override
    public String handleApprovalCallBack(ApprovalCallBackDTO callBackDTO) {
        logger.info("handleApprovalResults:{}", callBackDTO);
        //状态转换
        OptyStatusEnum status = converseStatus(callBackDTO);
        logger.info("businessId:{}, converseStatus:{}", callBackDTO.getBusinessId(), status);
        if (null != status) {
            try {
                doHandleApprovalResults(callBackDTO, status);
            } catch (Exception e) {
                logger.error("商机流程结束回调出错,{}, Exception:{}", JSON.toJSONString(callBackDTO),
                        ExceptionMsgUtils.getStackTrace(e, 2000));
                String logStr = "callBackError,rowId:" + callBackDTO.getBusinessId() + ";Exception:"
                        + ExceptionMsgUtils.getStackTrace(e, 1000);
                //写入日志表
                loggerService.synSaveLogger(logStr, "handleApprovalCallBack");
            }
        }
        return OpportunityConstant.SUCCESS_MSG;
    }

    private void doHandleApprovalResults(ApprovalCallBackDTO callBackDTO, OptyStatusEnum status) throws Exception {
        int saveResult = opportunityService.updateStatus(callBackDTO.getBusinessId(), status);
        if (saveResult > 0){
            sendMessage(callBackDTO);
            // 发送审批结束邮件
            sendProcessOverMail(callBackDTO, status);
        }
    }

    private void sendProcessOverMail(ApprovalCallBackDTO callBackDTO, OptyStatusEnum status) throws Exception {
        List<String> internalReceivers = getInternalReceivers(callBackDTO, status);
        List<String> externReceivers = getExternalReceivers(callBackDTO);
        if (status == OptyStatusEnum.OPTY_RENEWING) {
            opportunityService.sendMail(callBackDTO.getBusinessId(), internalReceivers,
                                        OpportunityConstant.OPPORTUNITY_REPORT_SUCCESS_MAIL_INTERNAL,
                                        OpportunityConstant.TEMPLATE_TYPE_NOTIFICATION);
            opportunityService.sendMail(callBackDTO.getBusinessId(), externReceivers,
                                        OpportunityConstant.OPPORTUNITY_REPORT_SUCCESS_MAIL_EXTERNAL,
                                        OpportunityConstant.TEMPLATE_TYPE_NOTIFICATION);
        } else {
            String opinion = callBackDTO.getOpinion();
            opportunityService.sendMailWithOpinion(new SendEmailPreDto(callBackDTO.getBusinessId(), internalReceivers,
                                                                       OpportunityConstant.OPPORTUNITY_REPORT_FAIL_MAIL_INTERNAL,
                                                                       OpportunityConstant.TEMPLATE_TYPE_NOTIFICATION,
                                                                       opinion));
            opportunityService.sendMailWithOpinion(new SendEmailPreDto(callBackDTO.getBusinessId(), externReceivers,
                                                                       OpportunityConstant.OPPORTUNITY_REPORT_FAIL_MAIL_EXTERNAL,
                                                                       OpportunityConstant.TEMPLATE_TYPE_NOTIFICATION,
                                                                       opinion));
        }
    }

    private List<String> getInternalReceivers(ApprovalCallBackDTO callBackDTO, OptyStatusEnum status) throws Exception {
        List<CompletableFuture<List<RoleInfo>>> notifyPersonFutures = new ArrayList<>();
        String rowId = callBackDTO.getBusinessId();
        OpportunityDetail opportunityDetail = opportunityDetailService.get(rowId);
        String deptNo = opportunityDetail.getDeptNo();
        OrgConditionVO orgInfo = getDeptInfoByDeptNo(deptNo);
        if (StringUtils.equals(orgInfo.getOrgLabel(), CommonConstant.ORG_LABEL_FLAG_DIRECTOR_OFFICE)) {
            // 总监办
            // 政企中国总监办总监
            notifyPersonFutures.add(getNotifyPersonFutures(opportunityDetail,
                                                           OpportunityRoleEnum.GEC_DIRECTORS_OFFICE.getCode()));
            // 政企中国总监办副总监
            notifyPersonFutures.add(getNotifyPersonFutures(opportunityDetail,
                                                           OpportunityRoleEnum.GEC_DEPUTY_DIRECTOR_OF_THE_DIRECTORS_OFFICE.getCode()));
        } else {
            // 办事处
            if (prmService.isNewBusinessIndustry(opportunityDetail.getFinalCustomerParentTrade())) {
                // 政企中国新业务科长
                notifyPersonFutures.add(getNotifyPersonFutures(opportunityDetail,
                                                               OpportunityRoleEnum.GEC_NEW_BUSINESS_SECTIONCHIEF.getCode()));
            } else {
                // 政企中国办事处销售副经理
                notifyPersonFutures.add(getNotifyPersonFutures(opportunityDetail,
                                                               OpportunityRoleEnum.GEC_REPRESENTATIVE_OFFICE_DEPUTY_MANAGER.getCode()));
            }
            // 政企中国办事处经理
            notifyPersonFutures.add(getNotifyPersonFutures(opportunityDetail,
                                                           OpportunityRoleEnum.GEC_REPRESENTATIVE_OFFICE_BUSINESS_MANAGER.getCode()));
        }
        Set<String> notifyPersons = new HashSet<>();
        notifyPersons.add(opportunityDetail.getBusinessManagerId());
        if (status != OptyStatusEnum.OPTY_RENEWING) {
            // 政企中国渠道业务部部长
            notifyPersonFutures.add(getNotifyPersonFutures(opportunityDetail,
                                                           OpportunityRoleEnum.GEC_DIRECTOR_OF_CHANNEL_BUSINESS_DEPARTMENT.getCode()));
            // 政企中国渠道管理团队部长
            notifyPersonFutures.add(getNotifyPersonFutures(opportunityDetail,
                                                           OpportunityRoleEnum.GECHEAD_OF_CHANNEL_MANAGEMENT_TEAM.getCode()));
        }
        List<RoleInfo> roleInfos = notifyPersonFutures.stream().map(CompletableFuture::join).flatMap(Collection::stream)
                                                      .collect(Collectors.toList());
        notifyPersons.addAll(
                roleInfos.stream().map(RoleInfo::getEmpNo).filter(StringUtils::isNotBlank).collect(Collectors.toSet()));
        return new ArrayList<>(notifyPersons);
    }

    private CompletableFuture<List<RoleInfo>> getNotifyPersonFutures(OpportunityDetail opportunityDetail,
                                                                     String roleCode) {
        return CompletableFuture.supplyAsync(() -> {
            AuthorizationRoleInfoDTO entity = new AuthorizationRoleInfoDTO();
            entity.setIndustryId(opportunityDetail.getFinalCustomerChildTrade());
            entity.setOrgId(opportunityDetail.getDeptNo());
            entity.setModuleCode(OpportunityConstant.OPPORTUNITY_MANAGEMENT);
            entity.setSysFlag(OpportunityConstant.PRM);
            entity.setRoleCode(roleCode);
            return prmOpportunityApprovalService.queryRoleInfoWithConstraint(entity);
        });
    }

    private List<String> getExternalReceivers(ApprovalCallBackDTO callBackDTO){
        List<String> receivers = new ArrayList<>();
        String rowId = callBackDTO.getBusinessId();
        Opportunity opportunity = opportunityService.get(rowId);
        receivers.add(opportunity.getCreatedBy());
        return receivers;
    }

    /**
     * 发送通知
     *
     * @param callBackDto ApprovalCallBackDTO
     * <AUTHOR>
     */
    @Override
    public void sendMessage(ApprovalCallBackDTO callBackDto) {
        try {
            OpportunityDetail opportunityDetail = opportunityDetailDao.get(callBackDto.getBusinessId());
            if (null == opportunityDetail) {
                return;
            }
            Opportunity opportunity = opportunityDao.get(callBackDto.getBusinessId());
            if (null == opportunity) {
                return;
            }
            opportunity.setBusinessTypeCd(CommonMapUtil.DELIVERY_INTERFACE_MAP.inverse().getOrDefault(opportunity.getBusinessTypeCd(), opportunity.getBusinessTypeCd()));
            String approvalResult = ApprovalOpinionEnum.getDescCnByEn(callBackDto.getApprovalResult());
            ComMsgForwardDTO.ComMsgForwardDTOBuilder msgForwardDTOBuilder = ComMsgForwardDTO.builder()
                    .sender(callBackDto.getApprover())
                    .msgId(msgId)
                    .to(Collections.singletonList(opportunityDetail.getCreatedBy()));
            Map<String, String> args = Maps.newHashMap();
            args.put("optyName", opportunityDetail.getAttrib46());
            args.put("optyCd", opportunity.getOptyCd());
            args.put("approvalResult", approvalResult);
            msgForwardDTOBuilder.args(args);
            messageService.sendMessageAsync(msgForwardDTOBuilder.build());
        } catch (Exception e) {
            logger.info("sendMessage Exception, rowId:{}", callBackDto.getBusinessId());
            logger.info("sendMessage Exception", e);
        }
    }


    private OptyStatusEnum converseStatus(ApprovalCallBackDTO callBackDTO) {
        if (Objects.isNull(callBackDTO)) {
            return null;
        }
        String status = callBackDTO.getStatus();
        String approvalResult = callBackDTO.getApprovalResult();
        if (Objects.equals(status, ApprovalStatusEnum.COMPLETE.getDescEn())
                && Objects.equals(approvalResult, ApprovalOpinionEnum.Y.getDescEn())) {
            return OptyStatusEnum.OPTY_RENEWING;
        }
        if (Objects.equals(status, ApprovalStatusEnum.COMPLETE.getDescEn())
                && Objects.equals(approvalResult, ApprovalOpinionEnum.N.getDescEn())) {
            return OptyStatusEnum.OPTY_APPROVAL_NOT_PASSED;
        }
        return null;
    }

    /**
     * 查询审批人
     */
    @Override
    public String queryApprover(String finalCustomerChildTrade, String deptNo) {
        AuthorizationRoleInfoDTO entity = new AuthorizationRoleInfoDTO();
        entity.setIndustryId(finalCustomerChildTrade);
        entity.setOrgId(deptNo);
        entity.setModuleCode(OpportunityConstant.OPPORTUNITY_MANAGEMENT);
        entity.setSysFlag(OpportunityConstant.PRM);
        return queryApprover(entity);
    }

    private String queryApprover(AuthorizationRoleInfoDTO entity) {
        //政企中国渠道业务部渠道总监
        entity.setRoleCode(OpportunityRoleEnum.GEC_CHANNEL_DIRECTOR_OF_CHANNEL_BUSINESS_DEPARTMENT.getCode());
        List<RoleInfo> roleInfos = prmOpportunityApprovalService.queryRoleInfoWithConstraint(entity);
        logger.info("[queryApprover]政企中国渠道业务部渠道总监:{},entity:{} ", roleInfos, entity);
        if (CollectionUtils.isEmpty(roleInfos)) {
            return null;
        }
        return roleInfos.stream().map(RoleInfo::getEmpNo)
                        .filter(StringUtils::isNotBlank)
                        .distinct()
                        .collect(Collectors.joining(","));
    }

    /**
     * 查询最终用户Id
     * @return
     */
    @Override
    public AccountInfo queryActivatedLastAccByName(String lastAccName) throws com.zte.springbootframe.common.exception.BusiException {
        List<AccountInfo> accountInfoList = customerInfoService.getCustomerInformationByName(lastAccName);
        if (CollectionUtils.isEmpty(accountInfoList)) {
            return null;
        }
        for (AccountInfo accountInfo : accountInfoList) {
            if (ACTIVE_STATUS.equals(accountInfo.getActiveStatus())) {
                return accountInfo;
            }
        }
        return null;
    }

    /**
     * 更新最终用户ID
     */
    private void updateLastAccInfo(String rowId, AccountInfo accountInfo) {
        OpportunityDetail entity = new OpportunityDetail();
        entity.setRowId(rowId);
        entity.setLastAccId(accountInfo.getAccountNum());
        entity.setFinalCustomerRestrictionFlag(accountInfo.getRestrictedPartyCode());
        entity.setLastUpd(new Date());
        entity.setLastUpdBy(CommonUtils.getEmpNo());

        //政企商机融入新逻辑
//        opportunityDetailDao.update(entity);
        SOptyXBO sOptyXBO = LcapConverterUtil.buildSoptyx(entity);
        sOptyXDao.updateById(sOptyXBO);

    }

    @Autowired
    private ICpcService iCpcService;

    /**
     * 设置启动流程所需参数
     */
    @Override
    public ApprovalStartParamsDTO setStartParams(OpportunityInfoDTO opportunityInfo) throws Exception {

        ApprovalStartParamsDTO startParams = new ApprovalStartParamsDTO();
        startParams.setFlowCode(OpportunityConstant.NEW_OPPORTUNITY_FLOW_CODE);
        startParams.setBusinessId(opportunityInfo.getOpportunity().getRowId());
        Map<String, Object> paramMap = getParamMap(opportunityInfo);
        startParams.setParams(paramMap);
        return startParams;
    }

    private Map<String, Object> getParamMap(OpportunityInfoDTO opportunityInfo) throws Exception {
        OpportunityDetail opportunityDetail = opportunityInfo.getOpportunityDetail();
        SysGlobalConstVo sysGlobalConstVo = CommonUtils.getSysGlobalConstVo();

        CompletableFuture<String> approvingPersonFuture = getApprovingPersonAsynchronous(opportunityDetail.getFinalCustomerChildTrade(),
                opportunityDetail.getDeptNo(), sysGlobalConstVo);
        //将OpportunityInfoVO转ApprovalStartParamsBO
        ApprovalStartParamsBO approvalStartParamsBO = ApprovalStartParamsMapper.INSTANCE.transOpportunityDetailToApprovalStartParamsBO(opportunityDetail);
        //提交时间
        approvalStartParamsBO.setCreated(getCurrentDate());
        //预计发标时间
        approvalStartParamsBO.setDate1(DateUtils.getDate(opportunityDetail.getDate1(), "yyyy-MM-dd"));
        approvalStartParamsBO.setStatusCd(OptyStatusEnum.APPROVING.getCode());
        approvalStartParamsBO.setDataSource(opportunityDetail.getDataSource());
        // 设置最终行业
        String parentTrade = opportunityDetail.getFinalCustomerParentTrade();
        String childTrade = opportunityDetail.getFinalCustomerChildTrade();
        CompletableFuture<List<IndustryTreeDataVO>> industriesFuture = getIndustryNamesAsynchronous(Lists.newArrayList(parentTrade, childTrade), sysGlobalConstVo);

        approvalStartParamsBO.setOptyCd(opportunityInfo.getOpportunity().getOptyCd());
        approvalStartParamsBO.setSsHttpHead(ssHttpHead);

        List<CompletableFuture<Void>> completableFutures = initializeSomeFieldsAsynchronously(opportunityInfo, approvalStartParamsBO);

        String approvingPerson = approvingPersonFuture.get();
        if (StringUtils.isBlank(approvingPerson)) {
            throw new BusiException(RetCode.BUSINESSERROR_CODE, APPROVING_PERSON_EMPTY);
        }
        approvalStartParamsBO.setApprovingPerson(approvingPerson);

        setProducts(opportunityInfo, approvalStartParamsBO);
        setLink(opportunityInfo, approvalStartParamsBO);
        setBusinessManager(opportunityInfo, approvalStartParamsBO);
        setKeyValue(opportunityInfo, approvalStartParamsBO);
        setDraftStatus(opportunityDetail.getLastAccId(), approvalStartParamsBO);
        setKeyWord(approvalStartParamsBO);
        setKeyWordForQuery(approvalStartParamsBO);
        setChannelRestrictedParty(opportunityInfo, approvalStartParamsBO);
        setConsumerRestrictedParty(opportunityInfo, approvalStartParamsBO);

        CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).join();

        List<IndustryTreeDataVO> industries = industriesFuture.get();
        String tradeName = getTradeName(industries, parentTrade) + "-" + getTradeName(industries, childTrade);
        approvalStartParamsBO.setFinalCustomerParentTradeName(tradeName);

        // 判断是否包含受限制主体
        boolean restricted = RestrictedPartyEnum.isRestrictedParty(opportunityDetail.getFinalCustomerRestrictionFlag())
                || RestrictedPartyEnum.isRestrictedParty(opportunityDetail.getAgencyRestrictionFlag());
        boolean hasSimilarOpportunity =StringUtils.isNotBlank(approvalStartParamsBO.getSimilarOpportunityInfo());
        approvalStartParamsBO.setFailureReason(getFailureReasonStr(restricted, hasSimilarOpportunity));
        if (OpportunityConstant.ARBITRATION_LEVEL_JUDGE_MONEY.compareTo(opportunityDetail.getTotalAmount()) >= 0){
            // 预计签单金额小于等于300万
            approvalStartParamsBO.setArbitrationLevel(OpportunityConstant.ARBITRATION_PROCESS_ONE);
        }else{
            // 预计签单金额大于300万
            approvalStartParamsBO.setArbitrationLevel(OpportunityConstant.ARBITRATION_PROCESS_TWO);
        }

        Map<String, Object> paramMap = MapUtil.objectToMap(approvalStartParamsBO);
        if (null == paramMap){
            throw new BusiException(RetCodeCopy.SERVICE_ERROR_NEED_TO_SHOW, "start.param.map.error");
        }
        // 根据受限制主体 判断ts单据 移动审批是否必填
//        if (restricted){
//            paramMap.put("tsApprovalNumber_requireType", "REQ");
//        }
        // 业务经理为空 移动审批必须选择业务经理
        if (StringUtils.isBlank(approvalStartParamsBO.getBusinessManager())) {
            paramMap.put("businessManagerName_requireType", "REQ");
        }
        return paramMap;
    }

    private String getFailureReasonStr(boolean restricted, boolean hasSimilarOpportunity){
        List<ComDictionaryMaintainVO> failureReasons = comDictionaryMaintainService.queryByType(OpportunityConstant.FAILURE_REASON);
        // 不存在受限制主体时不显示“为受限制主体”
        if (!restricted){
            failureReasons.removeIf(comDictionaryMaintainVO -> comDictionaryMaintainVO.getCode().equals(OpportunityConstant.IS_RESTRICTED));
        }
        // 无类似商机时不显示“已有报备”
        if (! hasSimilarOpportunity){
            failureReasons.removeIf(comDictionaryMaintainVO -> comDictionaryMaintainVO.getCode().equals(OpportunityConstant.HAS_REPORTED));
        }
        List<MoaOptionalEntity> moaOptionalEntities = new ArrayList<>();
        for (ComDictionaryMaintainVO failureReason : failureReasons) {
            MoaOptionalEntity moaOptional = new MoaOptionalEntity();
            moaOptional.setLabelTitle(failureReason.getChineseName());
            moaOptional.setLabelValue(failureReason.getCode());
            moaOptionalEntities.add(moaOptional);
        }
        return JSON.toJSONString(moaOptionalEntities);
    }

    private CompletableFuture<List<IndustryTreeDataVO>> getIndustryNamesAsynchronous(List<String> industryCodes, SysGlobalConstVo sysGlobalConstVo){
        return CompletableFuture.supplyAsync(()-> {
            try {
                CommonUtils.setSysGlobalConstVo(sysGlobalConstVo);
                return prmService.getIndustryNames(industryCodes, "Y");
            } catch (Exception e) {
                logger.error("getIndustryNames error", e);
                throw new BusiException(RetCode.BUSINESSERROR_CODE, "getIndustryNames.error");
            }
        });
    }

    private CompletableFuture<String> getApprovingPersonAsynchronous(String finalCustomerChildTrade, String deptNo, SysGlobalConstVo sysGlobalConstVo){
        return CompletableFuture.supplyAsync(() -> {
            CommonUtils.setSysGlobalConstVo(sysGlobalConstVo);
            return queryApprover(finalCustomerChildTrade, deptNo);
        });
    }

    private void setKeyValue(OpportunityInfoDTO opportunityInfo, ApprovalStartParamsBO approvalStartParamsBO){

        //填充商机状态，招标类型，赢率，当前阶段名称
        List<String> types = Lists.newArrayList(OpportunityConstant.CURRENT_PHASES_TYPE,
                OpportunityConstant.TYPE_FOR_TENDER_TYPE,
                OpportunityConstant.OPPORTUNITY_STATUS,
                OpportunityConstant.PRM_WIN_RATE_TYPE
                );
        Map<String, List<ComDictionaryMaintainVO>> dictMap = comDictionaryMaintainService.queryByTypeList(types);

        setStatusName(dictMap, approvalStartParamsBO);
        setWinRate(dictMap, opportunityInfo, approvalStartParamsBO);
        setProjectPhasesName(dictMap, opportunityInfo, approvalStartParamsBO);
        setTenderType(dictMap, opportunityInfo, approvalStartParamsBO);
    }

    private List<CompletableFuture<Void>> initializeSomeFieldsAsynchronously(OpportunityInfoDTO opportunityInfo, ApprovalStartParamsBO approvalStartParamsBO) throws Exception {
        List<CompletableFuture<Void>> completableFutures = new ArrayList<>();
        SysGlobalConstVo sysGlobalConstVo = CommonUtils.getSysGlobalConstVo();
        completableFutures.add(setDeptName(opportunityInfo, approvalStartParamsBO, sysGlobalConstVo));
        completableFutures.add(setSimilarOpportunityInfo(opportunityInfo, approvalStartParamsBO, sysGlobalConstVo));
        completableFutures.add(setBlackListInfo(opportunityInfo, approvalStartParamsBO, sysGlobalConstVo));
        return completableFutures;
    }

    private CompletableFuture<Void> setBlackListInfo(OpportunityInfoDTO opportunityInfo, ApprovalStartParamsBO approvalStartParamsBO, SysGlobalConstVo sysGlobalConstVo) throws Exception {
        return CompletableFuture.runAsync(() -> {
            try {
                CommonUtils.setSysGlobalConstVo(sysGlobalConstVo);
                // 黑名单信息
                List<BlackListDTO> blacks = iCpcService.getBlackListInfoByCrmCustomerCode(opportunityInfo.getOpportunityDetail().getCrmCustomerCode());
                approvalStartParamsBO.setBlackListInfo(getBlackInfo(blacks));
            } catch (Exception e) {
                logger.error("setBlackListInfo error, optyCd:{}", approvalStartParamsBO.getOptyCd(), e);
                throw new BusiException(RetCode.BUSINESSERROR_CODE, "setBlackListInfo.error");
            }
        });
    }

    private CompletableFuture<Void> setSimilarOpportunityInfo(OpportunityInfoDTO opportunityInfo, ApprovalStartParamsBO approvalStartParamsBO, SysGlobalConstVo sysGlobalConstVo) throws Exception {
        return CompletableFuture.runAsync(() -> {
            try {
                CommonUtils.setSysGlobalConstVo(sysGlobalConstVo);
                //类似商机 	CurrentOptyTime currentDeadline
                SimilarOpportunityQueryDTO similarQuery = new SimilarOpportunityQueryDTO();
                similarQuery.setCurrentCrmCode(opportunityInfo.getOpportunityDetail().getCrmCustomerCode());
                similarQuery.setCurrentOptyLastName(opportunityInfo.getOpportunityDetail().getLastAccName());
                similarQuery.setCurrentDeadline(opportunityInfo.getOpportunityDetail().getBiddingDeadline());
                similarQuery.setCurrentOptyTime(opportunityInfo.getOpportunityDetail().getDate1());
                similarQuery.setCurrentOptyTenderType(opportunityInfo.getOpportunityDetail().getTenderTypeCode());
                List<OpportunityProduct> opportunityProducts = opportunityInfo.getOpportunityProducts();
                if (opportunityProducts != null) {
                    List<String> prodNames = opportunityProducts.stream().map(e -> e.getProdLv2Name()).collect(Collectors.toList());
                    similarQuery.setCurrentProductList(prodNames);
                }
                similarQuery.setStartRow(1L);
                similarQuery.setRowSize(10L);
                List<SimilarOpportunity> similarOpportunities = prmOpportunityApprovalService.querySimilarOpportunityByRowId(similarQuery);
                String similarOpportunityStr = getSimilarOpportunityOptionalStr(similarOpportunities);
                approvalStartParamsBO.setSimilarOptyCd(similarOpportunityStr);
                approvalStartParamsBO.setSimilarOpportunityInfo(getSimilarOpportunityStr(similarOpportunities));
            } catch (Exception e) {
                logger.error("setSimilarOpportunityInfo error, optyCd:{}", approvalStartParamsBO.getOptyCd(), e);
                throw new BusiException(RetCode.BUSINESSERROR_CODE, "setSimilarOpportunityInfo.error");
            }
        });
    }

    private String getSimilarOpportunityOptionalStr(List<SimilarOpportunity> similarOpportunities){
        List<MoaOptionalEntity> moaOptionalEntities = new ArrayList<>();
        MoaOptionalEntity defaultOptional = new MoaOptionalEntity();
        defaultOptional.setLabelTitle(OpportunityConstant.NO_THING);
        defaultOptional.setLabelValue(OpportunityConstant.NO_THING_ENG);
        moaOptionalEntities.add(defaultOptional);
        for (SimilarOpportunity similarOpportunity : similarOpportunities) {
            MoaOptionalEntity moaOptional = new MoaOptionalEntity();
            moaOptional.setLabelValue(similarOpportunity.getOpportunityNo());
            moaOptionalEntities.add(moaOptional);
        }
        return JSON.toJSONString(moaOptionalEntities);
    }

    private void setConsumerRestrictedParty(OpportunityInfoDTO opportunityInfo, ApprovalStartParamsBO approvalStartParamsBO) {
        //最终用户受限制主体
        String lastAccId = opportunityInfo.getOpportunityDetail().getLastAccId();
        String finalCustomerRestrictionFlag = opportunityInfo.getOpportunityDetail().getFinalCustomerRestrictionFlag();
        if (StringUtils.isNotBlank(lastAccId) && StringUtils.isNotBlank(finalCustomerRestrictionFlag)) {
            String restrictedParty = RestrictedPartyEnum.getNameByCode(finalCustomerRestrictionFlag);
            approvalStartParamsBO.setConsumerRestrictedParty(restrictedParty);
        }
    }

    private void setChannelRestrictedParty(OpportunityInfoDTO opportunityInfo, ApprovalStartParamsBO approvalStartParamsBO) {
        //渠道商受限制主体
        String crmCustomerCode = opportunityInfo.getOpportunityDetail().getCrmCustomerCode();
        String agencyRestrictionFlag = opportunityInfo.getOpportunityDetail().getAgencyRestrictionFlag();
        if (StringUtils.isNotBlank(crmCustomerCode) && StringUtils.isNotBlank(agencyRestrictionFlag)) {
            String restrictedParty = RestrictedPartyEnum.getNameByCode(agencyRestrictionFlag);
            approvalStartParamsBO.setChannelRestrictedParty(restrictedParty);
        }
    }

    private void setTenderType(Map<String, List<ComDictionaryMaintainVO>> dictMap, OpportunityInfoDTO opportunityInfo, ApprovalStartParamsBO approvalStartParamsBO) {
        //招标类型编码
        String tenderTypeCode = opportunityInfo.getOpportunityDetail().getTenderTypeCode();
        approvalStartParamsBO.setTenderTypeCode(tenderTypeCode);
        String tenderTypeName = DictUtils.getName(tenderTypeCode, dictMap.get(OpportunityConstant.TYPE_FOR_TENDER_TYPE));
        approvalStartParamsBO.setTenderTypeName(tenderTypeName);
    }

    private void setProjectPhasesName(Map<String, List<ComDictionaryMaintainVO>> dictMap, OpportunityInfoDTO opportunityInfo, ApprovalStartParamsBO approvalStartParamsBO) {
        //商机当前阶段名称
        String projectPhasesName = DictUtils.getName(opportunityInfo.getOpportunityDetail().getProjectPhasesCode(),
                dictMap.get(OpportunityConstant.CURRENT_PHASES_TYPE));
        approvalStartParamsBO.setProjectPhasesName(projectPhasesName);
    }

    private void setWinRate(Map<String, List<ComDictionaryMaintainVO>> dictMap, OpportunityInfoDTO opportunityInfo, ApprovalStartParamsBO approvalStartParamsBO) {
        //填充赢率
        String winRate = DictUtils.getName(opportunityInfo.getOpportunityDetail().getWinRate(),
                dictMap.get(OpportunityConstant.PRM_WIN_RATE_TYPE));
        approvalStartParamsBO.setWinRate(winRate);
    }

    private void setStatusName(Map<String, List<ComDictionaryMaintainVO>> dictMap, ApprovalStartParamsBO approvalStartParamsBO) {
        String statusName = DictUtils.getName(approvalStartParamsBO.getStatusCd(), dictMap.get(OpportunityConstant.OPPORTUNITY_STATUS));
        approvalStartParamsBO.setStatusName(statusName);
    }

    private void setKeyWordForQuery(ApprovalStartParamsBO approvalStartParamsBO) {
        // 将商机编号、商机名称、渠道商使用“#”连接起来，方便查询商机列表时过滤
        List<String> tempList2 = Arrays.asList(approvalStartParamsBO.getOptyCd(),
                approvalStartParamsBO.getAttrib46(),
                approvalStartParamsBO.getCustomerName());
        String keyWordForQuery = tempList2.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(OpportunityConstant.KEY_WORD_SPLIT));
        approvalStartParamsBO.setKeyWordForQuery(keyWordForQuery);
    }

    private void setKeyWord(ApprovalStartParamsBO approvalStartParamsBO) {
        // 将商机编号、商机名称、渠道商、最终用户名称使用“#”连接起来，方便查询审批列表时过滤
        List<String> tempList = Arrays.asList(approvalStartParamsBO.getOptyCd(),
                approvalStartParamsBO.getAttrib46(),
                approvalStartParamsBO.getCustomerName(),
                approvalStartParamsBO.getLastAccName());
        String keyWord = tempList.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(OpportunityConstant.KEY_WORD_SPLIT));
        approvalStartParamsBO.setKeyWord(keyWord);
    }

    private void setDraftStatus(String lastAccId, ApprovalStartParamsBO approvalStartParamsBO) {
        if (StringUtils.isNotBlank(lastAccId)) {
            approvalStartParamsBO.setExisted(OpportunityConstant.YES);
            approvalStartParamsBO.setDraftStatus(DRAFT_STATUS_1);
        } else {
            approvalStartParamsBO.setExisted(OpportunityConstant.NO);
            approvalStartParamsBO.setDraftStatus(DRAFT_STATUS_0);
        }
    }

    private CompletableFuture<Void> setDeptName(OpportunityInfoDTO opportunityInfo, ApprovalStartParamsBO approvalStartParamsBO, SysGlobalConstVo sysGlobalConstVo) throws Exception {
        return CompletableFuture.runAsync(() -> {
            try {
                CommonUtils.setSysGlobalConstVo(sysGlobalConstVo);
                String deptNo = opportunityInfo.getOpportunityDetail().getDeptNo();
                OrgConditionVO orgInfo = getDeptInfoByDeptNo(deptNo);
                if (StringUtils.equals(orgInfo.getOrgLabel(),  CommonConstant.ORG_LABEL_FLAG_DIRECTOR_OFFICE)){
                   approvalStartParamsBO.setOrgLabel(CommonConstant.ORG_LABEL_FLAG_DIRECTOR_OFFICE);
                }else{
                    approvalStartParamsBO.setOrgLabel(CommonConstant.ORG_LABEL_FLAG_OFFICE);
                }
                approvalStartParamsBO.setDeptName(orgInfo.getOrganizationName());
            } catch (Exception e) {
                logger.error("setDeptName error", e);
                throw new BusiException(RetCode.BUSINESSERROR_CODE, "setDeptName.error");
            }
        });
    }

    private void setBusinessManager(OpportunityInfoDTO opportunityInfo, ApprovalStartParamsBO approvalStartParamsBO) {

        String businessManagerId = opportunityInfo.getOpportunityDetail().getBusinessManagerId();
        approvalStartParamsBO.setBusinessManagerId(businessManagerId);

        if (StringUtils.isNotBlank(opportunityInfo.getOpportunityDetail().getBusinessManagerName())
                && StringUtils.isNotBlank(businessManagerId)) {
            approvalStartParamsBO.setBusinessManager(opportunityInfo.getOpportunityDetail().getBusinessManagerName() + businessManagerId);
        }
    }

    private void setProducts(OpportunityInfoDTO opportunityInfo, ApprovalStartParamsBO approvalStartParamsBO) {

        String products = opportunityInfo.getOpportunityProducts().stream()
                .map(OpportunityProduct::getProdLv2Name)
                .collect(Collectors.joining(","));
        approvalStartParamsBO.setProducts(products);
    }

    private void setLink(OpportunityInfoDTO opportunityInfo, ApprovalStartParamsBO approvalStartParamsBO) throws UnsupportedEncodingException {
        //链接地址（审批页面）
        String approvalReportLink = URLEncoder.encode(internaluiUrl + OpportunityConstant.APPROVAL_REPORT_URL,"UTF-8");
        approvalStartParamsBO.setApprovalReportLink(prmUrl + approvalReportLink);
        String dataSource = opportunityInfo.getOpportunityDetail().getDataSource();
        String iChannelUrl =  UrlEncodeUtils.unescapeAndBtoa(URLEncoder.encode(ichanneluiUrl + MessageFormat.format(OpportunityConstant.ICHANNEL_APPROVAL_DETAIL_URL, opportunityInfo.getOpportunity().getRowId()),"UTF-8"));
        String internalUrl = URLEncoder.encode(internaluiUrl + MessageFormat.format(OpportunityConstant.PRM_APPROVAL_DETAIL_URL, opportunityInfo.getOpportunity().getRowId(), dataSource),"UTF-8");

        //链接地址（IChannel侧商机详情）
        approvalStartParamsBO.setIChannelApprovalDetailLink(ipartnerUrl + iChannelUrl);
        //链接地址（PRM侧商机详情）
        approvalStartParamsBO.setPrmApprovalDetailLink(prmUrl + internalUrl);
    }

    private String getCurrentDate() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return formatter.format(currentTime);
    }

    private String getTradeName(List<IndustryTreeDataVO> trees, String trade) {
        if (trees != null) {
            for (IndustryTreeDataVO treeData : trees) {
                if (StringUtils.equalsIgnoreCase(treeData.getLookupCode(), trade)) {
                    return treeData.getMeaning();
                }
            }
        }
        return "";
    }

    private String getBlackInfo(List<BlackListDTO> blacks) {
        if (CollectionUtils.isEmpty(blacks)) {
            return OpportunityConstant.NO;
        }

        BlackListDTO black = blacks.get(0);
        return new StringBuffer()
                .append(black.getListType())
                .append(" , ")
                .append(OpportunityConstant.INVESTIGATION_DATE_CN)
                .append(" : ")
                .append(DateUtils.getDate(black.getInvestigationDate()))
                .append(" , ")
                .append(OpportunityConstant.REASON_CN)
                .append(" : ")
                .append(black.getReasonCategory())
                .toString();

    }

    private String getSimilarOpportunityStr(List<SimilarOpportunity> similarOpportunities) throws Exception {
        if (CollectionUtils.isEmpty(similarOpportunities)) {
            return "";
        }
        List<String> deptNos = similarOpportunities.stream()
                .map(SimilarOpportunity::getOpportunityDepartment)
                .filter(StringUtils::isNotBlank)
                .distinct().collect(Collectors.toList());
        // 根据新组织(ORG打头)编码去HR查询组织信息
        List<PersonAndOrgInfoVO> orgList = PersonAndOrgInfoUtil.getOrgAndChildCompanyInfo(deptNos);
        Map<String, String> deptMap = orgList.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(PersonAndOrgInfoVO::getHrOrgID, PersonAndOrgInfoVO::getHrOrgName));
        int i = 0;
        StringBuffer sb = new StringBuffer();
        sb.append("<div>");
        for (SimilarOpportunity oppty : similarOpportunities) {
            i++;
            sb.append("<p>").append(OpportunityConstant.SIMILAR_OPPTY_CN + i).append("</p>");
            setNode(OpportunityConstant.OPPTY_CODE_CN, oppty.getOpportunityNo(), sb);
            setNode(OpportunityConstant.CUSTOMER_NAME_CN, oppty.getChannelVendor(), sb);
            setNode(OpportunityConstant.LAST_ACC_NAME_CN, oppty.getEndUserName(), sb);
            String deptNo = oppty.getOpportunityDepartment();
            setNode(OpportunityConstant.DEPT_NAME_CN, deptMap.get(deptNo), sb);
            setNode(OpportunityConstant.OPTTY_STATUS_CN, oppty.getOpportunityStatus(), sb);
            sb.append("<br>");
        }
        sb.append("</div>");
        return sb.toString();
    }

    private void setNode(String name, String value, StringBuffer sb) {
        value = value != null ? value : "";
        sb.append("<p>")
                .append(name)
                .append(" : ")
                .append(value)
                .append("</p>");
    }


    /**
     * 查询最终用户行业名称
     */
    private String getFinalCustomerParentTradeNameByCode(String finalCustomerParentTrade) throws Exception {
        List<String> industryIds = new ArrayList<>();
        industryIds.add(finalCustomerParentTrade);
        List<IndustryTreeDataVO> industryList = prmService.getIndustryNames(industryIds, "Y");
        if (CollectionUtils.isNotEmpty(industryList)) {
            return industryList.get(0).getMeaning();
        }
        return null;
    }

    /**
     * 查询组织名称
     */
    private OrgConditionVO getDeptInfoByDeptNo(String deptNo) throws Exception {
        OrgConditionVO result = new OrgConditionVO();
        List<String> deptIds = new ArrayList<>();
        deptIds.add(deptNo);
        List<OrgConditionVO> organizationList = prmService.getOrgInfosFromChannel(deptIds);
        if (CollectionUtils.isNotEmpty(organizationList)) {
            result = organizationList.get(0);
        }
        return result;
    }

    /**
     * 保存流程实例Id到流程记录表
     */
    private void saveFlowInstanceId(String rowId, String flowInstanceId) {
        ComApprovalRecord comApprovalRecord = new ComApprovalRecord();
        comApprovalRecord.setRowId(keyIdService.getKeyLongId());
        comApprovalRecord.setBusinessId(rowId);
        comApprovalRecord.setCreatedBy(CommonUtils.getEmpNo());
        comApprovalRecord.setLastUpdatedBy(CommonUtils.getEmpNo());
        comApprovalRecord.setWorkFlowInstanceId(flowInstanceId);
        comApprovalRecord.setBusinessType(BUSINESS_TYPE);
        comApprovalRecordDao.insert(comApprovalRecord);
    }

    /**
     * 暂存/提交商机信息
     *
     * @param opportunityInfo 商机信息
     * @return
     */
    @Override
    public OpportunityInfoDTO storageOpportunityInfo(OpportunityInfoDTO opportunityInfo) throws Exception {
        // 暂存字段相关校验
        validateStorageOpportunityInfo(opportunityInfo);
        SysGlobalConstVo sysGlobalConstVo = CommonUtils.getSysGlobalConstVo();
        Opportunity opportunity = opportunityInfo.getOpportunity();
        String rowId = opportunity.getRowId();
        OpportunityDetail opportunityDetail = opportunityInfo.getOpportunityDetail();
        if (StringUtils.isBlank(rowId)) {
            rowId = keyIdService.getKeyId();
            opportunity.setRowId(rowId);
            String optyCode = generateOpportunityCode();
            opportunity.setOptyCd(optyCode);
            opportunity.setExpiryDate(null);
            opportunity.setSuccessDate(null);
            opportunity.setSubmitDate(null);
        } else {
            Opportunity opportunityTemp = opportunityService.getAll(opportunity.getRowId());
            if (null == opportunityTemp) {
                // 生成商机编码
                String optyCode = generateOpportunityCode();
                opportunity.setOptyCd(optyCode);
            } else if (!OptyStatusEnum.DRAFT.getCode().equals(opportunityTemp.getStatusCd())) {
                logger.error("商机单据状态为:{}, 不允许修改/提交", opportunityTemp.getStatusCd());
                throw new BusiException(RetCode.BUSINESSERROR_CODE, LocalMessageUtils.getMessage("opportunity.status.notDraft"));
            } else if (!CommonConstant.EnableFlagEnum.Y.getKey().equals(opportunityTemp.getEnabledFlag())) {
                logger.error("商机单据已失效:{}", opportunityTemp);
                throw new BusiException(RetCode.BUSINESSERROR_CODE, LocalMessageUtils.getMessage("opportunity.status.invalided"));
            }
        }
        // 状态设置为草稿
        opportunity.setStatusCd(OptyStatusEnum.DRAFT.getCode());
        opportunityDetail.setRowId(rowId);
        String fromActiveFlag = opportunityDetail.getFromActiveFlag();
        // 设置是否来源激活报备默认值为N
        if (!CommonConst.Y.equals(fromActiveFlag) && !CommonConst.N.equals(fromActiveFlag)) {
            opportunityDetail.setFromActiveFlag(CommonConst.N);
        }
        // 设置激活次数默认为0
        if (null == opportunityDetail.getActiveCount()) {
            opportunityDetail.setActiveCount(0);
        }
        CompletableFuture<Void> agencyLevelFuture = CompletableFutureWrapper.runAsync(() -> {
            CommonUtils.setSysGlobalConstVo(sysGlobalConstVo);
            setAgencyLevel(opportunityDetail, opportunity.getDataSource());
        }, taskExecutor);
        CompletableFuture<Void> restrictionFlagFuture = setRestrictionFlag(opportunityDetail, sysGlobalConstVo);
        // 为了兼容原商机系统数据库非空约束字段而给一些字段设置默认值
        setDefaultValueForCompatible(opportunityInfo, rowId);
        CompletableFuture<Void> lastAccStatusFuture =  CompletableFutureWrapper.runAsync(() -> {
            CommonUtils.setSysGlobalConstVo(sysGlobalConstVo);
        }, taskExecutor);
        // 转换客户编码到ID
        CompletableFuture<Void> customerIdFuture = getCustomerId(opportunityDetail, sysGlobalConstVo);
        List<CompletableFuture<Void>> completableFutures = Arrays.asList(agencyLevelFuture, restrictionFlagFuture, lastAccStatusFuture, customerIdFuture);
        CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).join();

        logger.info("开始保存商机报备,id为{},商机编号为{}", rowId, opportunity.getOptyCd());

        opportunityInfoService.insertOrUpdateTheOpportunity(opportunityInfo);
        return opportunityInfo;
    }

    // 客户编码转换ID
    /* Started by AICoder, pid:e92651a8eaedc3b14e440a251032911c883914b6 */
    public CompletableFuture<Void> getCustomerId(OpportunityDetail opportunityDetail, SysGlobalConstVo sysGlobalConstVo) {
        List<String> customerCodes = opportunityDetail.buildCustomerCodeList();

        return CompletableFutureWrapper.runAsync(() -> {
            try {
                CommonUtils.setSysGlobalConstVo(sysGlobalConstVo);
                CustomerQueryInfoDTO customerInfoDTO = new CustomerQueryInfoDTO();
                customerInfoDTO.setCustomerCodeList(customerCodes);

                List<CustomerDetailInfoVO> customersByCode = AccountUtil.getCustomerByCode(customerCodes);
                Map<String, CustomerDetailInfoVO> customerMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(customersByCode)) {
                    customerMap.putAll(customersByCode.stream()
                            .collect(Collectors.toMap(CustomerDetailInfoVO::getCustomerCode, Function.identity(), (k1, k2) -> k1)));
                }
                opportunityDetail.setCustomerCodeIdMap(customerMap);
            } catch (Exception e) {
                logger.error("getCustomerId error, rowId: " + opportunityDetail.getRowId() + ", Exception: " + e.getMessage());
                throw new BusiException(RetCode.BUSINESSERROR_CODE, "query customer error");
            }
        }, taskExecutor);
    }
    /* Ended by AICoder, pid:e92651a8eaedc3b14e440a251032911c883914b6 */

    private void setLastAccStatus(OpportunityDetail opportunityDetail){
        try {
            //2. 从客户系统查询最终用户是否存在
            if (StringUtils.isBlank(opportunityDetail.getLastAccId())
                    && StringUtils.isNotBlank(opportunityDetail.getLastAccName())) {

                AccountInfo accountInfo = queryActivatedLastAccByName(opportunityDetail.getLastAccName());
                if (null != accountInfo) {
                    //最终用户ID存在,设置最终用户id
                    opportunityDetail.setLastAccId(accountInfo.getAccountNum());
                    opportunityDetail.setFinalCustomerRestrictionFlag(accountInfo.getRestrictedPartyCode());
                    //修改用户状态字段last_acc_status为“客户系统本身已存在生效客户”
                    opportunityDetail.setLastAccStatus(LastAccStatusEnum.INDIGENOUS_EFFECTIVE_CUSTOMER.getKey());
                } else {
                    //修改用户状态字段last_acc_status为“待创建客户草稿”
                    opportunityDetail.setLastAccStatus(LastAccStatusEnum.WAIT_CREATE_CUSTOMER_DRAFT.getKey());
                }
            }else if (StringUtils.isNotBlank(opportunityDetail.getLastAccId())){
                opportunityDetail.setLastAccStatus(LastAccStatusEnum.INDIGENOUS_EFFECTIVE_CUSTOMER.getKey());
            }else{
                opportunityDetail.setLastAccStatus(LastAccStatusEnum.NO_STATUS.getKey());
            }
        }catch (Exception e){
            logger.error("查询客户状态失败,客户名称:{},商机id:{}", opportunityDetail.getLastAccName(), opportunityDetail.getRowId());
            logger.error("查询客户状态失败", e);
            throw new BusiException(RetCode.BUSINESSERROR_CODE, LocalMessageUtils.getMessage("lastAccStatus.error"));
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW,isolation=Isolation.READ_UNCOMMITTED,rollbackFor = Exception.class)
    public void insertOrUpdateTheOpportunity(OpportunityInfoDTO opportunityInfo) throws Exception {
        Opportunity opportunity = opportunityInfo.getOpportunity();
        String rowId = opportunity.getRowId();
        OpportunityDetail opportunityDetail = opportunityInfo.getOpportunityDetail();
        List<OpportunityProduct> opportunityProductList = opportunityInfo.getOpportunityProducts();
        Opportunity insertOpportunity = opportunityService.insertOrUpdate(opportunity, opportunityDetail);
        opportunityDetailService.insertOrUpdate(opportunityDetail, true);
        List<SOptyProductBO> productBOList = opportunityProductService.insertOrUpdateProductList(opportunityProductList, rowId,
                OpportunityConstant.NEW_OPPORTUNITY, opportunityDetail);
        List<SOptyTeamBO> teamList = opportunityTeamService.insertOrUpdate(insertOpportunity, opportunityDetail);
        opportunityRedundancyService.insert(teamList, productBOList, insertOpportunity);
    }


    // 设置渠道商等级
    private void setAgencyLevel(OpportunityDetail opportunityDetail, String dataSource) {
        String crmCustomerCode = opportunityDetail.getCrmCustomerCode();
        boolean needGetAgencyLevel = StringUtils.isNotBlank(crmCustomerCode)
                && StringUtils.isBlank(opportunityDetail.getAgencyLevelName());
        if (needGetAgencyLevel) {
            PartnerLevelVO partnerLevelVO = customerInfoService.partnerLevelByCrmCustomerCode(crmCustomerCode);
            if (null != partnerLevelVO) {
                opportunityDetail.setAgencyLevelName(partnerLevelVO.getCertificationInfos());
            }
        }
    }

    // 设置受限制主体
    private CompletableFuture<Void> setRestrictionFlag(OpportunityDetail opportunityDetail, SysGlobalConstVo headerInfo) throws Exception {
        return CompletableFutureWrapper.runAsync(() -> {
            try {
                CommonUtils.setSysGlobalConstVo(headerInfo);
                RestrictedPartyVO restrictedPartyVO = prmOpportunityApprovalService.getRestrictedPartyV2(opportunityDetail.getLastAccId(), opportunityDetail.getCrmCustomerCode(),opportunityDetail.getDeptNo());
                if (StringUtils.isNotBlank(restrictedPartyVO.getCustomerName())) {
                    opportunityDetail.setCustomerName(restrictedPartyVO.getCustomerName());
                    opportunityDetail.setAgencyRestrictionFlag(restrictedPartyVO.getCustomerRestrictedPartyCode());
                }
                if (StringUtils.isNotBlank(restrictedPartyVO.getEndUserRestrictedPartyCode())) {
                    opportunityDetail.setFinalCustomerRestrictionFlag(restrictedPartyVO.getEndUserRestrictedPartyCode());
                }
            } catch (Exception e) {
                logger.error("setRestrictionFlag error, rowId:{}", opportunityDetail.getRowId(), e);
                throw new BusiException(RetCode.BUSINESSERROR_CODE, LocalMessageUtils.getMessage("restrictionFlag.set.error"));
            }
        }, taskExecutor);
    }

    /**
     * 依据角色、组织、子行业从权限平台找人(Channel侧)
     *
     * @param dto
     * @return
     */
    @Override
    public List<RoleInfo> queryRoleInfoWithConstraint(AuthorizationRoleInfoDTO dto) throws Exception {
        RoleInfoDTO entity = new RoleInfoDTO();
        //1. 将模块编码变成模块Id
        String moduleId = UppAuthPropertiesConstant.MODULE_MAP.get(dto.getModuleCode());
        //2. 将RoleCode变成RoleId
        String roleId = roleService.getRoleByRoleCodeAndModuleId(moduleId, dto.getRoleCode(), Boolean.FALSE);
        //3. 调用ChannelAuthService#getUserByRoleAndData
        entity.setRoleId(roleId);
        entity.setModuleId(moduleId);
        entity.setIndustryIds(Lists.newArrayList(dto.getIndustryId()));
        entity.setOrgIds(Lists.newArrayList(dto.getOrgId()));

        String sysFlag = dto.getSysFlag();
        boolean isPrm = StringUtils.equalsIgnoreCase(OpportunityConstant.PRM, sysFlag);
        return channelAuthService.getUserByRoleAndData(entity, isPrm);
    }

    /**
     * 刷新
     *
     * @param opportunityDetail
     * @return
     */
    @Override
    public String refresh(OpportunityDetail opportunityDetail) throws Exception {
        String rowId = opportunityDetail.getRowId();
        AccountInfo accountInfo = queryActivatedLastAccByName(opportunityDetail.getLastAccName());
        if (null != accountInfo) {
            //最终用户ID存在,更新数据库
            updateLastAccInfo(opportunityDetail.getRowId(), accountInfo);
            ComApprovalRecord comApprovalRecord = comApprovalRecordDao.getByBusinessId(rowId);
            if (comApprovalRecord!=null && StringUtils.isNotBlank(comApprovalRecord.getWorkFlowInstanceId())){
                return OpportunityConstant.ERROR_MSG;
            }
            //启动流程
            startFlowTask(rowId);
            //修改用户状态字段last_acc_status为“创建客户草稿后生效的客户”
            opportunityDetailService.updateLastAccStatus(rowId, LastAccStatusEnum.EFFECTIVE_CUSTOMER.getKey());
        }
        return OpportunityConstant.SUCCESS_MSG;
    }

    @Override
    public void doCreateCustomerDraft(CreateCustomerParamVO paramVO) throws Exception {
        //查询商机信息
        OpportunityDetail opportunityDetail = opportunityDetailDao.get(paramVO.getRowId());
        if (StringUtils.isBlank(paramVO.getDeptNo())){
            paramVO.setDeptNo(opportunityDetail.getDeptNo());
        }
        Assert.notNull(opportunityDetail, LocalMessageUtils.getMessage("services.are.abnormal"));
        logger.info("创建客户草稿,entity:{}", paramVO);
        //查询最终客户是否存在且生效
        List<AccountInfo> accountInfoList = customerInfoService.getCustomerInformationByNameV2(opportunityDetail.getDeptNo(),paramVO.getLastAccName());
        logger.info("查询最终客户是否存在且生效,paramVO:{},accountInfoList:{}", paramVO, accountInfoList);
        if (CollectionUtils.isEmpty(accountInfoList)) {
            //最终客户不存在，创建客户草稿
            createCustomer(paramVO);
            // 发送邮件通知中兴业务经理
            sendCreateCustomerDraftEmail(paramVO);
        }else{
            boolean accountWaitSubmit = accountInfoList.stream()
                    .anyMatch(accountInfo -> AccountStatusEnum.isCreatedCustomer(accountInfo.getActiveStatusCode()));
            if (accountWaitSubmit){
                logger.info("最终用户已存在但未提交，通知中兴业务经理,,paramVO:{}", paramVO);
                // 发送邮件通知中兴业务经理
                sendCreateCustomerDraftEmail(paramVO);
            }
        }

        //修改最终客户状态为"2-已创建客户草稿"
        opportunityDetailService.updateLastAccStatus(paramVO.getRowId(), LastAccStatusEnum.CUSTOMER_DRAFT_CREATED.getKey());
    }

    private void modifyApprovalCenterBusinessManagerId(String rowId, String newBusinessManagerId) {
        ApprovalResetStartParamsDTO params = new ApprovalResetStartParamsDTO();
        params.setFlowInstanceId(queryFlowInstanceId(rowId));
        Map<String, Parameter> parameterMap = Maps.newHashMap();
        Parameter parameter = new Parameter();
        parameter.setParameterType("String");
        parameter.setParameterValue(newBusinessManagerId);
        parameterMap.put("businessManagerId", parameter);
        params.setParameterMap(parameterMap);
        String message = approvalFlowService.resetFlowParams(params);
        logger.info("重设businessManagerId流程参数成功，变更后的值：{}", message);
    }

    private void createCustomer(CreateCustomerParamVO paramVO) throws Exception {
        CreateCustomerParam ccp = new CreateCustomerParam();
        ccp.setBusinessAccntTypeCode("10504");
        ccp.setCustRangeCode("N");
        ccp.setCountryId("0001");
        ccp.setFinanceTypeCode("FINS ACNT 02");
        ccp.setSubcompanyCode("N");
        ccp.setSubTypeCode("10133");
        ccp.setCountryCode("0086");
        ccp.setIsNoMktCode("N");
        ccp.setAccountName(paramVO.getLastAccName());
        ccp.setCreatedBy(paramVO.getBusinessManagerId());
        if (StringUtils.isNotBlank(paramVO.getDeptNo())) {
            List<PersonAndOrgInfoVO> orgInfoList = PersonAndOrgInfoUtil.getOrgInfo(Collections.singletonList(paramVO.getDeptNo()));
            if (CollectionUtils.isNotEmpty(orgInfoList)){
                PersonAndOrgInfoVO personAndOrgInfoVO = orgInfoList.get(0);
                CreateCustomerParam.OrganizationVo organizationVo = new CreateCustomerParam.OrganizationVo();
                organizationVo.setDeptName(personAndOrgInfoVO.getHrOrgName());
                organizationVo.setDeptNo(paramVO.getDeptNo());
                organizationVo.setId(paramVO.getDeptNo());
                organizationVo.setFullname(personAndOrgInfoVO.getHrOrgNamePath());
                organizationVo.setIsMainDept(CommonConst.Y);
                ccp.setOrgList(Collections.singletonList(organizationVo));
            }
            ccp.setBuId(paramVO.getDeptNo());
        }
        //调用法人基本信息查询接口，查询企业的办公电话和城市
        CompanyInfoDTO companyInfo = iCpcServiceApi.searchCompanyInfo(paramVO.getLastAccName());
        if (companyInfo != null) {
            if (StringUtils.isNotBlank(companyInfo.getPhoneNumber())) {
                ccp.setPhoneNum(companyInfo.getPhoneNumber());
            }
            String cityName = companyInfo.getCity();
            //调用mdm接口查询城市编码
            String cityCode = emdmService.getCityCode(cityName);
            if (StringUtils.isNotBlank(cityCode)) {
                ccp.setCityId(cityCode);
            }
        }
        logger.info("createCustomer,rowId:{},params:{}",paramVO.getRowId(), ccp);
        customerInfoService.createCustomer(ccp);
    }

    /**
     * 给中兴业务经理发送客户创建通知邮件
     *
     * @param vo
     */
    @Override
    public void sendCreateCustomerDraftEmail(CreateCustomerParamVO vo) {
        //调用用户中心接口查询中兴业务经理邮箱
        String receiver = "";
        List<String> empNoList = new ArrayList<>();
        empNoList.add(vo.getBusinessManagerId());
        List<UcsUserInfoDTO> ucsUserInfoList = iBmtUcsService.getUserInfoByAccountIdList(empNoList);
        if (CollectionUtils.isNotEmpty(ucsUserInfoList)) {
            receiver = ucsUserInfoList.stream().map(UcsUserInfoDTO::getEmail).collect(Collectors.joining(","));
        }else{
            logger.error("发送创建客户草稿邮件获取用户中心邮件为空，使用工号<EMAIL>.cn方式发送邮件");
            receiver = vo.getBusinessManagerId() + "@zte.com.cn";
        }
        //发送邮件
        opportunityService.sendMail(vo.getRowId(), receiver, OpportunityConstant.OPPORTUNITY_EMAIL_FOR_CREATE_CUSTOMER, OpportunityConstant.TEMPLATE_TYPE_NOTIFICATION);
    }

    /**
     * 修改中兴业务经理
     *
     * @param vo
     */
    private void updateBusinessManagerToDB(CreateCustomerParamVO vo) {
            OpportunityDetail entity = new OpportunityDetail();
            entity.setRowId(vo.getRowId());
            entity.setBusinessManagerId(vo.getBusinessManagerId());
            entity.setBusinessManagerName(vo.getBusinessManagerName());
            entity.setLastUpd(new Date());
            entity.setLastUpdBy(CommonUtils.getEmpNo());
            SOptyTeamBO sOptyTeamBO = teamConverter.teamUtil(null,entity,TeamConverter.ROLE_OWNER_CODE);
            teamConverter.saveOrUpdate(sOptyTeamBO);
    }



    /**
     * 根据业务Id从审批记录表中获取流程实例Id
     * 这里的业务Id对应商机主表和商机扩展表里的主键id
     *
     * @return 商机编号
     */
    private String queryFlowInstanceId(String businessId) {
        return comApprovalRecordDao.queryFlowInstance(businessId);
    }


    /**
     * 生成商机编号
     *
     * @return 商机编号
     */
    private String generateOpportunityCode() throws Exception {
        SimpleDateFormat parser = new SimpleDateFormat(OpportunityConstant.DATE_FORMAT_YYYYMMDD);
        Date date = new Date();
        String prefix = OpportunityConstant.OPPORTUNITY_PREFIX + parser.format(date);
        return optyCdRecordService.getNewOptyCd(prefix, 3);
    }

    /**
     * 暂存商机相关校验
     *
     * @param opportunityInfo 商机信息
     */
    public void validateStorageOpportunityInfo(OpportunityInfoDTO opportunityInfo) {
        Opportunity opportunity = opportunityInfo.getOpportunity();
        OpportunityDetail opportunityDetail = opportunityInfo.getOpportunityDetail();
        List<OpportunityProduct> opportunityProducts = opportunityInfo.getOpportunityProducts();
        // 校验入参是否为null
        if (null == opportunity || null == opportunityDetail) {
            logger.error("商机入参不能为null");
            throw new ValidationException(LocalMessageUtils.getMessage("opportunityNull"));
        }
        // 校验商机编号格式
        if (StringUtils.isNotBlank(opportunity.getOptyCd())
                && !Pattern.matches(OpportunityConstant.OPTY_CD_REGEX, opportunity.getOptyCd())) {
            logger.error("当前单据编号不符合规则,当前单据编号为{}", opportunity.getOptyCd());
            throw new ValidationException(LocalMessageUtils.getMessage("optyCodeIllegal"));
        }
        // 校验产品信息
        if (CollectionUtils.isNotEmpty(opportunityProducts) && opportunityProducts.size() > 1) {
            List<String> productNames = new ArrayList<>();
            validProduct(opportunityProducts, productNames, opportunity);
        }
        // 如果是否属于激活报备不为"Y",校验激活次数和来源激活报备单据id应为空
        boolean isFromActiveError = !CommonConst.Y.equals(opportunityInfo.getOpportunityDetail().getFromActiveFlag())
                && StringUtils.isNotBlank(opportunityInfo.getOpportunityDetail().getFromActiveOpty());
        if (isFromActiveError) {
            throw new BusiException(RetCode.VALIDATIONERROR_CODE, LocalMessageUtils.getMessage("isFromActiveOptyError"));
        }
        // 校验招标类型
        this.validateByDateSource(opportunity, opportunityDetail);

    }

    private void validProduct(List<OpportunityProduct> opportunityProducts, List<String> productNames, Opportunity opportunity) {
        opportunityProducts.forEach(opportunityProduct -> {
            if (StringUtils.isNotEmpty(opportunityProduct.getProdLv2Name()) &&
                    productNames.contains(opportunityProduct.getProdLv2Name()) && StringUtils.equals(CommonUtils.getSubTenantId(), HeaderNameConst.DEFAULT_X_TENANT_ID)) {
                logger.error("存在重复的产品，请检查，重复的产品名称:{}", opportunityProduct.getProdLv2Name());
                throw new ValidationException(LocalMessageUtils.getMessage("opportunityProduct.repeated"));
            }
            // 校验主产品信息
            this.validatePdmProd(opportunityProduct, opportunity.getDataSource());
            productNames.add(opportunityProduct.getProdLv2Name());
        });
        if (opportunityProducts.stream().filter(e -> StringUtils.equalsIgnoreCase(CommonConst.Y,
                e.getZteMainProduct())).count() > 1L) {
            logger.error("主产品不能大于1条");
            throw new ValidationException(LocalMessageUtils.getMessage("opportunityProduct.main.product.more"));
        }
    }

    private void validateByDateSource(Opportunity opportunity, OpportunityDetail opportunityDetail) {
        // 来源为渠道
        if (SourceOfOpportunityEnum.CHANNEL_FILING.getValue().equals(opportunity.getDataSource())) {
            // 渠道报备时校验渠道商客户编码
            if (StringUtils.isBlank(opportunityDetail.getCrmCustomerCode())) {
                throw new BusiException(RetCode.VALIDATIONERROR_CODE, LocalMessageUtils.getMessage("crmCustomerCode.null"));
            }

            // 招标类型为 = 非招标项目，字段转换
            if (BooleanUtil.and(!TenderTypeEnum.TENDER_PROJECT.getValue().equals(opportunityDetail.getTenderTypeCode()),
                    Objects.isNull(opportunityDetail.getDate2()))) {
                opportunityDetail.setDate2(opportunityDetail.getDate1());
            }
        }
        // 来源为prm，预计签约日期必填，招标类型 = 招标项目，预计发标日期，预计签约时间为必填
        if (SourceOfOpportunityEnum.ZTE_SELF_BUILT.getValue().equals(opportunity.getDataSource())) {
            if (Objects.isNull(opportunityDetail.getDate2())){
                throw new BusiException(RetCode.VALIDATIONERROR_CODE, LocalMessageUtils.getMessage("opportunityDetail.date2.null"));
            }
            if (TenderTypeEnum.TENDER_PROJECT.getValue().equals(opportunityDetail.getTenderTypeCode())
                    && Objects.isNull(opportunityDetail.getDate1())){
                throw new BusiException(RetCode.VALIDATIONERROR_CODE, LocalMessageUtils.getMessage("estimatedBiddingTime.null"));
            }
        }
    }

    /**
     * 校验公司主产品
     * @param opportunityProduct
     */
    public void validatePdmProd(OpportunityProduct opportunityProduct, String dataSource) {
        OpportunityProduct pdmProd = opportunityProduct.getPdmProd();
        if (Objects.isNull(pdmProd) || !SourceOfOpportunityEnum.ZTE_SELF_BUILT.getValue().equals(dataSource)) {
            return;
        }
        if(StringUtils.isBlank(pdmProd.getProdLv1Id()) && StringUtils.equals(CommonUtils.getSubTenantId(), HeaderNameConst.DEFAULT_X_TENANT_ID)) {
            logger.error("公司主产品：体系内部分类为空");
            throw new ValidationException(LocalMessageUtils.getMessage("pdmProd.prodLv1Id.blank"));
        }
        if(StringUtils.isBlank(pdmProd.getProdLv2Id())) {
            logger.error("公司主产品：大产品线为空");
            throw new ValidationException(LocalMessageUtils.getMessage("pdmProd.prodLv2Id.blank"));
        }
        if(StringUtils.isBlank(pdmProd.getProdLv21Id())) {
            logger.error("公司主产品：产品线为空");
            throw new ValidationException(LocalMessageUtils.getMessage("pdmProd.prodLv21Id.blank"));
        }
    }


    /**
     * 提交相关校验操作
     *
     * @param opportunityInfo 商机信息
     */
    private void validateSubmitOpportunityInfo(OpportunityInfoDTO opportunityInfo) throws Exception {
        if (CollectionUtils.isEmpty(opportunityInfo.getOpportunityProducts())) {
            throw new ValidationException(LocalMessageUtils.getMessage("opportunityProducts.at.least.one"));
        }
        OpportunityDetail opportunityDetail = opportunityInfo.getOpportunityDetail();
        Opportunity opportunity = opportunityInfo.getOpportunity();
        // 招标项目时校验竞标截止日期必填
        if (TenderTypeEnum.TENDER_PROJECT.getValue().equals(opportunityDetail.getTenderTypeCode())) {
            if (null == opportunityDetail.getBiddingDeadline()) {
                throw new ValidationException(LocalMessageUtils.getMessage("biddingDeadline.null"));
            }
        }
        //ichannel侧校验最终客户信息ID不存在时中兴业务经理必填
        if(SourceOfOpportunityEnum.CHANNEL_FILING.getValue().equals(opportunity.getDataSource())
                && StringUtils.isEmpty(opportunityDetail.getLastAccId())
                && StringUtils.isEmpty(opportunityDetail.getBusinessManagerId())){
                throw new ValidationException(LocalMessageUtils.getMessage("businessManagerName.null"));

        }
        // prm侧商机校验是否受限制主体
        if (SourceOfOpportunityEnum.ZTE_SELF_BUILT.getValue().equals(opportunity.getDataSource())) {
            Boolean isRestrictedParty = prmOpportunityApprovalService.checkRestrictedParty(opportunityDetail.getDeptNo(),opportunityDetail.getLastAccId(), opportunityDetail.getCrmCustomerCode());
            logger.error("提交时受限制主体校验不通过，渠道商:{} 或者最终用户:{} 是受限制主体", opportunityDetail.getCustomerName(),
                    opportunityDetail.getLastAccName());
            if (Boolean.TRUE.equals(isRestrictedParty)
                    && StringUtils.isBlank(opportunityDetail.getTsApprovalNumber())) {
                throw new BusiException(RetCode.VALIDATIONERROR_CODE, LocalMessageUtils.getMessage("isRestrictedParty"));
            }
        }
    }


    /**
     * 为了兼容原商机系统数据库非空约束字段而设置默认值
     *
     * @param opportunityInfo 商机报备信息
     * @param rowId           商机id
     */
    private void setDefaultValueForCompatible(OpportunityInfoDTO opportunityInfo, String rowId) {
        Opportunity opportunity = opportunityInfo.getOpportunity();
        OpportunityDetail opportunityDetail = opportunityInfo.getOpportunityDetail();
        List<OpportunityProduct> opportunityProducts = opportunityInfo.getOpportunityProducts();
        String dataSource = opportunity.getDataSource();
        opportunity.setBuId("");
        opportunity.setName("");
        opportunity.setDataSource(dataSource);
        opportunityDetail.setParRowId(rowId);
        opportunityDetail.setDataSource(dataSource);
        if (CollectionUtils.isNotEmpty(opportunityProducts)) {
            opportunityProducts.forEach(opportunityProduct -> {
                opportunityProduct.setDataSource(dataSource);
                if (Objects.nonNull(opportunityProduct.getPdmProd())) {
                    opportunityProduct.getPdmProd().setDataSource(dataSource);
                }
            });
        }
    }

    @Override
    public List<String> refreshStatusByApproveCenter(String rowId, boolean doUpdate) {
        List<String> refreshResults = new ArrayList<>();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("statusCd", OptyStatusEnum.APPROVING.getCode());
        paramMap.put("dataSource", SourceOfOpportunityEnum.CHANNEL_FILING.getValue());
        paramMap.put("rowId", rowId);
        List<Opportunity> opportunities = opportunityService.getList(paramMap);
        for (Opportunity opportunity : opportunities) {
            opportunity.setBusinessTypeCd(CommonMapUtil.DELIVERY_INTERFACE_MAP.inverse().getOrDefault(opportunity.getBusinessTypeCd(), opportunity.getBusinessTypeCd()));
            String refreshResult = "optyCd: " + opportunity.getOptyCd();
            String flowInstanceId = opportunityDao.getFlowInstanceIdByBusinessId(opportunity.getRowId());
            if (StringUtils.isBlank(flowInstanceId)){
                refreshResult = refreshResult +  ", status: empty flowInstanceId";
                refreshResults.add(refreshResult);
                continue;
            }
            try {
                FlowParameter flowParameter = new FlowParameter();
                flowParameter.setFlowInstanceId(flowInstanceId);
                List<ApprovalRecordsDTO> approvalRecords = approvalFlowService.getApprovalRecords(flowParameter);
                if (CollectionUtils.isNotEmpty(approvalRecords)) {
                    ApprovalRecordsDTO approvalRecord = approvalRecords.get(0);
                    refreshResult = refreshResult + doRefreshStatusByApproveCenter(opportunity, approvalRecord, doUpdate);
                } else {
                    refreshResult = refreshResult + ", status: empty approvalRecords";
                }

                refreshResults.add(refreshResult);
            }catch (Exception e){
                refreshResult = refreshResult +  ", Exception: " + ExceptionMsgUtils.getStackTrace(e, 500);
                refreshResults.add(refreshResult);
            }
        }
        return refreshResults;
    }


    private String doRefreshStatusByApproveCenter(Opportunity opportunity, ApprovalRecordsDTO approvalRecord, boolean doUpdate){
        String refreshResult = "";
        String nodeList = "";
        if (CollectionUtils.isNotEmpty(approvalRecord.getApprovalTaskRecordList())) {
            nodeList = approvalRecord.getApprovalTaskRecordList().stream().map(ApprovedTask::getNodeName).collect(Collectors.joining(","));
        }
        if (approvalRecord.getStatus().equalsIgnoreCase(ApprovalNodeStatusEnum.ACTIVE.getDescEn())){
            List<ApprovedTask> approvalTaskRecordList = approvalRecord.getApprovalTaskRecordList();

            boolean isArbitration = approvalTaskRecordList.stream().anyMatch(approvedTask ->
                    !approvedTask.getNodeName().equals(OpportunityApprovalNodeEnum.APPROVAL_NODE.getNodeName())
                            && !approvedTask.getNodeName().equals(OpportunityConstant.CUSTOMER_DRAFT_CREATED));
            if (isArbitration) {
                refreshResult = refreshResult +  ", status:process Arbitration, nodeList:" + nodeList;
                String status = OptyStatusEnum.OPTY_SUSPEND.getCode();
                if (doUpdate) {
                    opportunityService.updateStatusByRowId(opportunity.getRowId(), status);
                }
            }
        }else {
            refreshResult = refreshResult +  ", status:process " + approvalRecord.getStatus() + ", nodeList:" + nodeList;
        }
        return refreshResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean maintainMainProds(List<OpportunityProductVO> maintainMainProdsParams) {
        if (CollectionUtils.isEmpty(maintainMainProdsParams)) {
            return false;
        }

        String opptyId = maintainMainProdsParams.get(0).getOpptyId();
        OpportunityDetail opportunityDetail = opportunityDetailService.get(opptyId);

        // 权限校验：商机对应的政企中国中兴业务经理，或者政企中国商机管理员
        boolean isAdmin = StringUtils.equals(opportunityDetail.getBusinessManagerId(), CommonUtils.getEmpNo())
                || uppAuthorityService.hasPermissionInRoleCodes(Collections.singletonList(OpportunityRoleEnum.GEC_OPPORTUNITY_ADMIN.getCode()));
        if (!isAdmin) {
            return false;
        }

        // 保存数据（全删全插逻辑）
        opportunityProductDao.batchLogicDeleteByOptyId(opptyId,OpportunityConstant.PDM_PROD);

        String dataSource = opportunityDetail.getDataSource();
        // 组装插入数据
        List<OpportunityProduct> productList = maintainMainProdsParams.stream()
                .map(OpportunityProductVO::pdmProdToOpportunityProduct)
                .collect(Collectors.toList());
        this.buildProdsBaseInfo(productList, opptyId, dataSource);
        LcapConverterUtil.buildProduct(productList,opportunityDetail);
        opportunityProductDao.insertByBatch(productList);

        return true;
    }

    private void buildProdsBaseInfo(List<OpportunityProduct> products, String opptyId, String dataSource) {
        String emp = CommonUtils.getEmpNo();
        Date now = new Date();

        products.forEach(opportunityProduct -> {
            opportunityProduct.setOpptyId(opptyId);
            opportunityProduct.setCreatedBy(emp);
            opportunityProduct.setLastUpdBy(emp);
            opportunityProduct.setCreated(now);
            opportunityProduct.setLastUpd(now);
            opportunityProduct.setRowId(keyIdService.getKeyId());
            opportunityProduct.setActiveFlg(CluesSysConst.FLAG_Y);
            opportunityProduct.setDataSource(dataSource);
        });
    }


    /* Started by AICoder, pid:e02882fbeaf925514b55085c40970d23fa80d813 */
    @Override
    public Boolean modifyChannelPartner(String record) {
        logger.info("Opportunity modify channel partner, kafka msg: {}", record);

        Optional<ChannelPartnerChangeDTO> channelPartnerChangeDTOOpt = parseRecord(record);

        if (!channelPartnerChangeDTOOpt.isPresent()) {
            return false;
        }

        ChannelPartnerChangeDTO channelPartnerChangeDTO = channelPartnerChangeDTOOpt.get();
        // 变更后渠道商如果为空，不做处理
        if (StringUtils.isBlank(channelPartnerChangeDTO.getNewDealerCode())) {
            return false;
        }

        Optional<Opportunity> opportunityOpt = getOpportunityByCode(channelPartnerChangeDTO.getOpportunityCode());

        if (!opportunityOpt.isPresent()) {
            return false;
        }

        Opportunity opportunity = opportunityOpt.get();
        OpportunityDetail opportunityDetail = opportunityDetailService.get(opportunity.getRowId());

        updateOpportunityDetail(opportunityDetail, channelPartnerChangeDTO);

        opportunityDetailService.update(opportunityDetail, false);

        return true;
    }

    private Optional<ChannelPartnerChangeDTO> parseRecord(String record) {
        try {
            ChannelPartnerChangeDTO channelPartnerChangeDTO = JSONObject.parseObject(record, new TypeReference<ChannelPartnerChangeDTO>() {});
            return Optional.ofNullable(channelPartnerChangeDTO);
        } catch (Exception e) {
            logger.error("Failed to parse record: {}", record, e);
            return Optional.empty();
        }
    }

    private Optional<Opportunity> getOpportunityByCode(String opportunityCode) {
        return Optional.ofNullable(opportunityService.getByOptyCd(opportunityCode));
    }

    @SneakyThrows
    public void updateOpportunityDetail(OpportunityDetail opportunityDetail, ChannelPartnerChangeDTO channelPartnerChangeDTO) {
        if (StringUtils.isBlank(opportunityDetail.getSourceCustomerName())) {
            opportunityDetail.setSourceCustomerName(channelPartnerChangeDTO.getOldDealerName());
        }
        if (StringUtils.isBlank(opportunityDetail.getSourceCrmCustomerCode())) {
            opportunityDetail.setSourceCrmCustomerCode(channelPartnerChangeDTO.getOldDealerCode());
        }

        /* Started by AICoder, pid:t58e7y84dba33fa144d40b5aa0b1bc00cc4851df */
        // 立项成功渠道数据处理
        if (OpportunityConstant.PROJECT_APPROVAL_PASSED.equals(channelPartnerChangeDTO.getChangeReason())
                && StringUtils.isNotBlank(channelPartnerChangeDTO.getNewDealerCode())) {
            projectSuccessChannelHandle(opportunityDetail, channelPartnerChangeDTO);
        } else {
            opportunityDetail.setCustomerName(channelPartnerChangeDTO.getNewDealerName());
            opportunityDetail.setCrmCustomerCode(channelPartnerChangeDTO.getNewDealerCode());
            setCrmCustomer(opportunityDetail);
        }

        /* Ended by AICoder, pid:t58e7y84dba33fa144d40b5aa0b1bc00cc4851df */
        //获取渠道商是否受限制主体
        Optional.ofNullable(this.getAccountByCustomerCode(opportunityDetail.getDeptNo(),channelPartnerChangeDTO.getNewDealerCode())).ifPresent(accountInfo ->
                opportunityDetail.setAgencyRestrictionFlag(accountInfo.getRestrictedPartyCode())
        );
        // 渠道商级别
        PartnerLevelVO partnerLevelVO = Optional.ofNullable(customerInfoService.partnerLevelByCrmCustomerCode(channelPartnerChangeDTO.getNewDealerCode())).orElse(new PartnerLevelVO());
        opportunityDetail.setAgencyLevelName(partnerLevelVO.getCertificationInfos());
    }


    /* Started by AICoder, pid:jb10bs8d55u4ee71481a0b4bc0a3a91be0812619 */
    private AccountInfo getAccountByCustomerCode(String deptNo, String customerCode) {
        try {
           return customerInfoService.getCustomerDetailsV2(deptNo, customerCode);
        } catch (Exception e) {
            logger.error("获取客户ID失败, customerCode: {}, deptNo: {}", customerCode, deptNo, e);
            return new AccountInfo();
        }
    }
    /* Ended by AICoder, pid:jb10bs8d55u4ee71481a0b4bc0a3a91be0812619 */
    /**
     * 立项成功渠道数据处理
     * @param opportunityDetail
     * @param channelPartnerChangeDTO
     */
    /* Started by AICoder, pid:4a2c0wceb065b77148ae0813f059e226b9b7bddd */
    @SneakyThrows
    public void projectSuccessChannelHandle(OpportunityDetail opportunityDetail, ChannelPartnerChangeDTO channelPartnerChangeDTO) {
        String newDealerCode = channelPartnerChangeDTO.getNewDealerCode();
        String crmCustomerCode = opportunityDetail.getCrmCustomerCode();
        String customerName = opportunityDetail.getCustomerName();

        // 渠道商名称不相同或新经销商代码不为空则更新
        if (!newDealerCode.equalsIgnoreCase(crmCustomerCode)) {
                AccountInfo customerDetails = customerInfoService.getCustomerDetails(newDealerCode);
                if (customerDetails == null) {
                    return;
                }
                opportunityDetail.setCustomerName(customerDetails.getAccountName());
                opportunityDetail.setCrmCustomerCode(newDealerCode);
                setCrmCustomer(opportunityDetail);
                // 判断原先数据渠道商和新渠道商是否一致，不一致需更改
                if (StringUtils.isBlank(opportunityDetail.getSourceCustomerName())) {
                    opportunityDetail.setSourceCustomerName(customerName);
                }
                if (StringUtils.isBlank(opportunityDetail.getSourceCrmCustomerCode())) {
                    opportunityDetail.setSourceCrmCustomerCode(crmCustomerCode);
                }
        }
    }

    private static void setCrmCustomer(OpportunityDetail opportunityDetail) throws com.zte.springbootframe.common.exception.BusiException {
        String crmCustomer = opportunityDetail.getCrmCustomerCode();
        List<Account> accountList = AccountUtil.getAccountListByids(Arrays.asList(opportunityDetail.getCrmCustomerCode()));
        if(CollectionUtils.isNotEmpty(accountList)){
            crmCustomer = accountList.get(0).getId();
        }
        opportunityDetail.setCrmCustomer(crmCustomer);
    }

    /* Ended by AICoder, pid:4a2c0wceb065b77148ae0813f059e226b9b7bddd */


    /* Ended by AICoder, pid:e02882fbeaf925514b55085c40970d23fa80d813 */


    @Override
    public Boolean updateOptyAttribute(OpportunityDetailVO detailVO) {
        if (!verifyOperatorAuth(detailVO.getRowId())) {
            logger.warn("updateOptyAttribute verify fail, param:{}", JSON.toJSONString(detailVO));
            return false;
        }
        SOptyXBO sOptyXBO = new SOptyXBO();
        sOptyXBO.setId(detailVO.getRowId());
        sOptyXBO.setOptyAttribute(detailVO.getOptyAttribute());
        return sOptyXDao.update(sOptyXBO) == CommonConstant.ONE;
    }

    @Override
    public Boolean verifyOperatorAuth(String rowId) {
        // 当前的单据是商机培育和商机转立项审批
        SOptyBO sOptyBO = sOptyDao.selectById(rowId);
        if (sOptyBO == null) {
            return false;
        }
        if (!StringUtils.equals(OptyStatusEnum.OPTY_RENEWING.getCode(), sOptyBO.getOptyStatus())) {
            return false;
        }
        String empNo = CommonUtils.getEmpNo();
        // 判断当前人员是商机负责人
        SOptyTeamBO sOptyTeamBO = sOptyTeamDao.selectList(Wrappers.lambdaUpdate(SOptyTeamBO.class).eq(SOptyTeamBO::getPId, rowId)).stream()
                .filter(e -> StringUtils.equals(OpportunityConstant.EMPLOYEE_OWNER, e.getEmployeeType())).findAny().orElse(new SOptyTeamBO());
        if (sOptyTeamBO.getEmployeeExt().contains(empNo)) {
            return true;
        }
        // 判断当前人员是政企中国管理员
        return uppAuthorityService.hasPermissionInRoleCodes(Collections.singletonList(OpportunityRoleEnum.GEC_OPPORTUNITY_ADMIN.getCode()));
    }
}
