package com.zte.crm.eva.base.infrastructure.access.mapper;

import com.zte.crm.eva.base.domain.universal.AggregationParams;
import com.zte.crm.eva.base.domain.universal.WhereBO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-04-20
 **/
@Mapper
public interface UniversalMapper {
    /**
     * 分页或关键字查询
     * @param fields
     * @param tableRealName
     * @param whereBO
     * @return
     */
    List<Map<String, Object>> selectCommonByPage(@Param(value = "fields") String fields, @Param(value = "tableRealName") String tableRealName, @Param(value = "whereBO") WhereBO whereBO);

    /**
     * 查询总条数
     * @param tableRealName
     * @return
     */
    long selectCommonCount(@Param(value = "tableRealName") String tableRealName, @Param(value = "whereBO") WhereBO whereBO);

    /**
     * 插入
     * @param map
     * @param tableName
     */
    int insertCommon(@Param(value="map") Map<String,Object> map,@Param(value="tableName") String tableName);

    /**
     * 批量插入
     * @param list
     * @param tableName
     */
    int insertBatchCommon(@Param(value="list") List<Map<String, String>> list,@Param(value="tableName") String tableName);

    /**
     * 批量软删除
     * @param tableName
     * @param idName
     * @param map
     * @param ids
     */
    int deleteCommons(@Param("map")Map<String,Object> map,@Param("tableName") String  tableName, @Param("idName") String idName,@Param("ids") List<Object> ids);

    /**
     * 更新
     * @param map
     * @param tableName
     * @param idName
     * @param idValue
     */
    int updateCommon(@Param("map")Map<String,Object> map,@Param("tableName") String tableName,@Param("idName") String idName,@Param("idValue") String idValue);


    /**
     * 聚合函数汇总
     * @param aggregationParams
     * @return
     */
    List<Map<String, Object>> selectAggregationByParams(@Param(value = "aggregationParams") AggregationParams aggregationParams);

    /**
     * 查询总条数
     * @param tableName 表名
     * @param conditionMap 条件列表
     * @return int
     */
    int selectEqCommonCount(@Param("tableName") String tableName, @Param("conditionMap") Map<String, Object> conditionMap);

    /**
     * 通用单独更新
     * @param tableName 表名
     * @param fieldMap  字段列表
     * @param conditionMap 条件列表
     * @param updateTimeFlag 更新时间标记
     * @param updateTimeMap 更新时间列表
     * @return int
     */
    int updateSingleCommon(@Param("tableName") String tableName, @Param("fieldMap") Map<String, Object> fieldMap,
                           @Param("conditionMap") Map<String, Object> conditionMap, @Param("updateTimeFlag") String updateTimeFlag, @Param("updateTimeMap") Map<String, Object> updateTimeMap);

}


