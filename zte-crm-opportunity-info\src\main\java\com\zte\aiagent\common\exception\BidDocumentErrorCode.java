package com.zte.aiagent.common.exception;

import com.zte.mcrm.common.framework.exception.ErrorCode;

/**
 * <AUTHOR>
 * 招标文档错误码枚举
 * 错误码规则：BD + 来源 + 编号 + 级别
 * BD: Bid Document (招标文档)
 * 来源: 2-用户错误, 3-系统错误, 4-第三方错误
 * 编号: 10-99
 * 级别: 1-INFO, 2-WARN, 3-ERROR, 4-FATAL
 */
public enum BidDocumentErrorCode implements ErrorCode {

    // ========== 用户输入错误 (BD2xxx) ==========

    /**
     * 上传文件不能为空
     */
    BD2103,

    /**
     * 不支持的文件类型，仅支持 PDF、DOC、DOCX 格式
     */
    BD2113,

    /**
     * 文件大小超出限制，最大支持 {0}MB
     */
    BD2123,

    /**
     * 解析模板ID不能为空
     */
    BD2133,

    /**
     * 解析模板编码不能为空
     */
    BD2143,

    /**
     * 租户ID不能为空或无效
     */
    BD2153,

    /**
     * 操作人不能为空
     */
    BD2163,

    // ========== 系统业务错误 (BD3xxx) ==========

    /**
     * 文档不存在：{0}
     */
    BD3103,

    /**
     * 文档状态不允许当前操作：{0}
     */
    BD3113,

    /**
     * 文档保存失败
     */
    BD3123,

    /**
     * 文档解析失败：{0}
     */
    BD3133,

    /**
     * 文档状态转换失败：从 {0} 到 {1}
     */
    BD3143,

    /**
     * 文档创建失败
     */
    BD3153,

    /**
     * 解析模板不存在：{0}
     */
    BD3163,

    /**
     * 文档解析引擎异常
     */
    BD3173,

    // ========== 第三方服务错误 (BD4xxx) ==========

    /**
     * 文档云上传失败：{0}
     */
    BD4103,

    /**
     * 文档云下载失败：{0}
     */
    BD4113,

    /**
     * 解析引擎服务不可用
     */
    BD4123,

    /**
     * 解析引擎超时
     */
    BD4133,

    /**
     * 通知服务异常：{0}
     */
    BD4143;

    @Override
    public Enum get() {
        return this;
    }

    @Override
    public String code() {
        return this.name();
    }
}
