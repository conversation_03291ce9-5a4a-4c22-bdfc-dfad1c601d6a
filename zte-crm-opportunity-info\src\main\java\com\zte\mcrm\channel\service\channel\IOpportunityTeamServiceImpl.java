package com.zte.mcrm.channel.service.channel;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.channel.model.entity.Opportunity;
import com.zte.mcrm.channel.model.entity.OpportunityDetail;
import com.zte.mcrm.channel.service.common.TeamConverter;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.opty.dao.SOptyTeamDao;
import com.zte.opty.model.bo.SOptyTeamBO;
import com.zte.opty.model.vo.OptyEmployeeVO;
import com.zte.opty.sync.domain.converter.SOptyTeamConverter;
import com.zte.opty.sync.util.OptionExtFillUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
/* Started by AICoder, pid:ye082f6c8ftc3931450b0bd440da567e2749b68a */
@Service
public class IOpportunityTeamServiceImpl implements IOpportunityTeamService {

    @Autowired
    private SOptyTeamDao sOptyTeamDao;

    @Autowired
    private TeamConverter teamConverter;

    @Autowired
    private SOptyTeamConverter sOptyTeamConverter;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SOptyTeamBO> insertOrUpdate(Opportunity insertOpportunity, OpportunityDetail opportunityDetail) {
        /* Started by AICoder, pid:1c033vee9bjdf5e14e9e09f040ff9a110122726a */
        // 更新团队信息的辅助方法
        List<SOptyTeamBO> list = new ArrayList<>();
        List<String> codeList =  Stream.of(opportunityDetail.getBusinessManagerId(),opportunityDetail.getDirectorOfPsc()
                ,insertOpportunity.getCreatedBy()).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String,List<OptyEmployeeVO>> employeeVOS = sOptyTeamConverter.convertOptyEmployeeVo(codeList).stream()
                .collect(Collectors.groupingBy(OptyEmployeeVO::getEmpUIID));

        Map<String,List<SOptyTeamBO>> teamMap = sOptyTeamDao.selectList(Wrappers.lambdaUpdate(SOptyTeamBO.class).eq(SOptyTeamBO::getPId,
                        insertOpportunity.getRowId())).stream()
                .collect(Collectors.groupingBy(SOptyTeamBO::getEmployeeType));

        if(StringUtils.isNotBlank(opportunityDetail.getBusinessManagerId())) {
            SOptyTeamBO sOptyTeamBO = teamConverter.teamUtil(employeeVOS.get(opportunityDetail.getBusinessManagerId())
                    , insertOpportunity, TeamConverter.ROLE_OWNER_CODE, OpportunityConstant.EMPLOYEE_OWNER);
            saveTeam(sOptyTeamBO, insertOpportunity, teamMap);
            list.add(sOptyTeamBO);
        }
        if(StringUtils.isNotBlank(opportunityDetail.getDirectorOfPsc())) {
            SOptyTeamBO sOptyTeamBO = teamConverter.teamUtil(employeeVOS.get(opportunityDetail.getDirectorOfPsc())
                    , insertOpportunity, TeamConverter.ROLE_MEMBER_CODE, OpportunityConstant.EMPLOYEE_DESC);
            saveTeam(sOptyTeamBO, insertOpportunity, teamMap);
            list.add(sOptyTeamBO);
        }
        if (StringUtils.equalsIgnoreCase(OpportunityConstant.PRM, insertOpportunity.getDataSource())) {
            SOptyTeamBO sOptyTeamBO = teamConverter.teamUtil(employeeVOS.get(insertOpportunity.getCreatedBy())
                    , insertOpportunity, TeamConverter.ROLE_MEMBER_CODE, OpportunityConstant.EMPLOYEE_CREATE);
            saveTeam(sOptyTeamBO, insertOpportunity, teamMap);
            list.add(sOptyTeamBO);
        }
        return list;
        /* Ended by AICoder, pid:1c033vee9bjdf5e14e9e09f040ff9a110122726a */
    }

    private void saveTeam(SOptyTeamBO sOptyTeamBO, Opportunity insertOpportunity, Map<String, List<SOptyTeamBO>> teamMap) {
        if (CollectionUtils.isNotEmpty(teamMap.get(sOptyTeamBO.getEmployeeType()))) {
            OptionExtFillUtil.setOptionExtValue(sOptyTeamBO, SOptyTeamBO.class);
            sOptyTeamDao.update(sOptyTeamBO, Wrappers.lambdaUpdate(SOptyTeamBO.class).eq(SOptyTeamBO::getPId, insertOpportunity.getRowId())
                    .eq(SOptyTeamBO::getEmployeeType, sOptyTeamBO.getEmployeeType()));
        } else {
            if (CommonUtils.getSubTenantId() != null) {
                sOptyTeamBO.setTenantId(CommonUtils.getSubTenantId());
            }
            OptionExtFillUtil.setOptionExtValue(sOptyTeamBO, SOptyTeamBO.class);
            sOptyTeamDao.insert(sOptyTeamBO);
        }
    }
    /* Ended by AICoder, pid:ye082f6c8ftc3931450b0bd440da567e2749b68a */
}
