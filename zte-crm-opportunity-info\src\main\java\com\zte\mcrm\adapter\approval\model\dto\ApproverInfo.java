package com.zte.mcrm.adapter.approval.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: 10243305
 * @date: 2021/8/25 下午7:07
 */
@Data
public class ApproverInfo {
    private String approverId;
    private String createDate;
    private String lastUpdateDate;
    @ApiModelProperty(value = "审批意见描述")
    private String opinion;
    @ApiModelProperty(value = "审批扩展意见描述")
    private String extOpinion;
    @ApiModelProperty(value = "审批结果")
    private String result;
    private String status;
}
