package com.zte.mcrm.channel.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.Predicate;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 商机当前状态 枚举类
 * <AUTHOR>
 */
public enum OpportunityCurrentStatus {
    /** 进行中 */
    UNDERWAY("underway"),
    /** 中标 */
    WINNING("winning"),
    /** 丢标 */
    BID_LOSS("bidLoss"),
    /** 取消 */
    CANCEL("cancel");

    private final String value;

    @JsonCreator
    public static OpportunityCurrentStatus fromValue(final String value) {
        return (OpportunityCurrentStatus) CollectionUtils.find(Arrays.asList(OpportunityCurrentStatus.values()), new Predicate() {
            @Override
            public boolean evaluate(Object o) {
                OpportunityCurrentStatus status = (OpportunityCurrentStatus) o;
                return StringUtils.equals(status.value, value);
            }
        });
    }

    OpportunityCurrentStatus(String value) {
        this.value = value;
    }
    public String getValue() {
        return value;
    }
}
