package com.zte.mcrm.adapter.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/11/19
 */
@Data
public class CertificationInfo {
    @ApiModelProperty(value = "渠道商id customer_id")
    private Long customerId;

    @ApiModelProperty(value = "资质类型,快码:认证信息快码 Authentication Information Quick Code （qualificationCategory）")
    private String qualificationCategory;

    @ApiModelProperty(value = "配置认证条件表主键ID Configures the primary key ID of the authentication condition table.")
    private Long authenConfigConditionId;

    @ApiModelProperty(value = "认证分类,快码:认证信息快码 Authentication Information Quick Code （certificationClassification）")
    private String authenType;

    @ApiModelProperty(value = "认证等级,快码:认证信息快码 Authentication Information Quick Code, （certificationLevel）")
    private String authenLevel;

    @ApiModelProperty(value = "认证等级名称")
    private String authenLevelName;

    @ApiModelProperty(value = "认证产品 Certification Product")
    private String authenProduct;

    @ApiModelProperty(value = "认证产品名称 Certification Product")
    private String authenProductName;

    @ApiModelProperty(value = "组合类型,快码:认证信息快码 Authentication Information Quick Code （combinationType）")
    private String combinationType;

    @ApiModelProperty(value = "产品小类 Product Sub-Category")
    private String smallProduct;

    @ApiModelProperty(value = "认证状态,快码:认证信息快码 Authentication Information Quick Code （certificationStatus）")
    private String authenStatus;

    @ApiModelProperty(value = "申请类型 applyType")
    private String applyType;

    @ApiModelProperty(value = "缓冲类别 bufferType")
    private String bufferType;

    @ApiModelProperty(value = "创建时间  createdDate")
    private Date createdDate;

    @ApiModelProperty(value = "最后更新时间 lastUpdatedDate")
    private Date lastUpdatedDate;

    @ApiModelProperty(value = "认证开始时间 Certification start time")
    private Date authenBeginDate;

    @ApiModelProperty(value = "认证结束时间 Certification endtime")
    private Date authenEndDate;

}
