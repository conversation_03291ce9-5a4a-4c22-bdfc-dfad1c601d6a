package com.zte.mcrm.adapter.clouddisk.util;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

/**
 * Json序列化及反序列化工具类
 * <p>工具统一化：建议使用com.zte.itp.msa.util.json.JacksonJsonConverUtil</p>
 * <AUTHOR>
 *
 */
public class JsonUtils {
	private static final Logger log = LoggerFactory.getLogger(JsonUtils.class);

	private static final ObjectMapper OM;

	static{
		OM = new ObjectMapper();
		OM.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,false);
	}

	/**
	 * Json序列化
	 * @param value
	 * @return
	 */
	public static String toJSONString(Object value) {
		try {
			return OM.writeValueAsString(value);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return null;
	}
	/**
	 *  Json反序列化
	 * @param <T>
	 * @param content 
	 * @param valueTypeRef
	 * @return
	 */
	public static <T> T parseObject(String content, TypeReference<T> valueTypeRef) {
		try {
			if (StringUtils.isEmpty(content)){
				content = "";
			}
			return OM.readValue(content, valueTypeRef);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return null;
	}

	/**
	 * Json反序列化
	 * 
	 * <AUTHOR>
	 * @date 2019年8月7日
	 * @param <T>
	 * @param content
	 * @param valueType
	 * @return
	 */
	public static <T> T parseObject(String content, Class<T> valueType) {
		try {
			return OM.readValue(content, valueType);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return null;
	}

	public static boolean isJSON(String str) {
		try {
			Object obj = JSON.parse(str);
			return true;
		}catch (Exception e){
			return false;
		}
	}
	
}
