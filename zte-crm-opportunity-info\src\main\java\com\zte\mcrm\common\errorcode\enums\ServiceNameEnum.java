package com.zte.mcrm.common.errorcode.enums;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2022/04/28
 * @Description:
 */
public enum ServiceNameEnum {
    /**
     * BY3113=服务器开小差了，请稍候重试---远程调用base服务无响应异常
     */
    BASE_SERVICE("zte-crm-ichannel-base", 'I'),
    /**
     * BY3123=服务器开小差了，请稍候重试---远程调用certification服务无响应异常
     */
    CERTIFICATION_SERVICE("zte-crm-ichannel-certification", 'I'),
    /**
     * BY3133=服务器开小差了，请稍候重试---远程调用opersupportmanage服务无响应异常
     */
    OPERSUPPORTMANAGE_SERVICE("zte-crm-ichannel-opersupportmanage", 'I'),
    /**
     * BY3143=服务器开小差了，请稍候重试---远程调用rebate服务无响应异常
     */
    REBATE_SERVICE("zte-crm-ichannel-rebate", 'I'),
    /**
     * 远程调用zte-bmt-ucs-api服务无响应异常
     */
    UCS_SERVICE("zte-bmt-ucs-api", 'O'),
    /**
     * 远程调用zte-iss-cpc-partnerservice服务无响应异常
     */
    CPC_SERVICE("zte-iss-cpc-partnerservice", 'O'),
    /**
     * 远程调用zte-iss-approval-manage服务无响应异常
     */
    APPROVAL_SERVICE("zte-iss-approval-manage", 'O'),
    /**
     * 远程调用其它第三方服务无响应异常
     */
    OTHER_SERVICE("otherThirdServiceName", 'O');
    private String name;
    private char type;

    ServiceNameEnum(String name, char type) {
        this.name = name;
        this.type = type;
    }
    public static Set<String> getInternalServiceNames(){
        ServiceNameEnum[] values = ServiceNameEnum.values();
        return Arrays.stream(values).filter(x->'I'==x.type).map(x->x.name).collect(Collectors.toSet());
    }
}
