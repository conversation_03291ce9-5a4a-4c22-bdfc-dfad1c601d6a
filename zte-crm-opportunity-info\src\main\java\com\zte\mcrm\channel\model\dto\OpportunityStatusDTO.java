package com.zte.mcrm.channel.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @ClassName OpportunityStatusDTO
 * @Description
 * @Data 2022/5/7 10:40
 */
@Data
@Getter
@Setter
public class OpportunityStatusDTO {
    @ApiModelProperty(value = "商机编号")
    @NotBlank(message = "{optyCd.null}")
    private String optyCd;
    @ApiModelProperty(value = "商机状态code")
    @NotBlank(message = "{statusCd.null}")
    private String statusCd;
    @ApiModelProperty(value = "主键id")
    private String rowId;
}
