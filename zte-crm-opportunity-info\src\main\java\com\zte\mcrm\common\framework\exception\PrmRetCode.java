package com.zte.mcrm.common.framework.exception;

import com.zte.itp.msa.core.locale.LocaleMessageSourceBean;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.util.SpringContextUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;

/**
 * @Auther: 10244932
 * @Date: 2021/6/21 11:11
 * @Description:
 */
public class PrmRetCode extends RetCode {
    private final static Logger logger = LoggerFactory.getLogger(PrmRetCode.class);
    private final static String FORMAT_D = "%d";
    private final static String FORMAT_S = "%s";
    //服务异常
    public static final String SERVERERROR_CODE = "30001";
    public static final String SERVERERROR_MSGID = "RetCode.ServerError";
    //token校验认证失败异常
    public static final String AUTHFAILED_CODE = "30002";
    public static final String AUTHFAILED_MSGID = "RetCode.AuthFailed";
    //权限异常
    public static final String PERMISSIONDENIED_CODE = "30003";
    public static final String PERMISSIONDENIED_MSGID = "RetCode.PermissionDenied";
    //校验异常
    public static final String VALIDATIONERROR_CODE = "30004";
    public static final String VALIDATIONERROR_MSGID = "RetCode.ValidationError";
    //业务异常
    public static final String BUSINESSERROR_CODE = "30005";
    public static final String BUSINESSERROR_MSGID = "RetCode.BusinessError";
    //数据库异常
    public static final String SQL_EXCEPTION_CODE = "30006";
    public static final String SQL_EXCEPTION_MSGID = "RetCode.SqlException";
    // 第三方服务异常
    public static final String EXTERNAL_SERVERERROR_CODE = "40007";
    public static final String EXTERNAL_SERVERERROR_MSGID = "RetCode.ExternalServiceException";

    private String code;
    private String msgId;
    private String msg;
    private Object[] params;

    public PrmRetCode(String code, String msgId) {
        this.code = getFullCode(code);
        this.msgId = msgId;
        this.msg = getMsg(msgId);

    }

    public String getFullCode(String code) {
        try {
            Environment environment = SpringContextUtil.getEnvironment();
            String property = environment.getProperty("microservice.error.code.pre", "003");
            return property+code;
        } catch (Exception e) {
            logger.info(" Environment  exception: {}",e.getMessage());
        }
        return code;
    }

    public PrmRetCode(String code, String msgId, Object... params) {
        this.code = getFullCode(code);
        this.msgId = msgId;
        this.params = params;
        this.msg = getMsg(msgId, params);

    }

    private String getMsg(String msgId, Object... params) {
        String message=null;
        try {
            // LocaleMessageSourceBean 支持多参数 自定义异常信息
            LocaleMessageSourceBean lmb = (LocaleMessageSourceBean) SpringContextUtil.getBean("localeResolver");
            message = lmb.getMessage(msgId, params);
            try {
                 // 兼容以前的老版本，String.format自定义的异常信息
                if (params != null && params.length > 0&&(message.contains(FORMAT_S)||message.contains(FORMAT_D))) {
                    message = String.format(message, params);
                }
            } catch (Exception e) {
                logger.info(" message format exception {}",e.getMessage());
            }


        } catch (Exception e) {
            logger.info(" message  exception {}",e.getMessage());
           return msgId;
        }
        return message;
    }

    public Object[] getParams() {
        return params;
    }

    public void setParams(Object[] params) {
        this.params = params;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = getFullCode(code);
    }

    @Override
    public String getMsgId() {
        return msgId;
    }

    @Override
    public void setMsgId(String msgId) {
        this.msgId = msgId;
        this.msg = getMsg(msgId);
    }

    @Override
    public String getMsg() {
        return msg;
    }

    @Override
    public void setMsg(String msg) {
        this.msg = msg;
    }

}
