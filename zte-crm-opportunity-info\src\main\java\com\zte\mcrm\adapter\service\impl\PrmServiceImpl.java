package com.zte.mcrm.adapter.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.constant.HeaderNameConst;
import com.zte.mcrm.adapter.model.AuthConstraintDTO;
import com.zte.mcrm.adapter.model.dto.*;
import com.zte.mcrm.adapter.model.vo.*;
import com.zte.mcrm.adapter.service.PrmService;
import com.zte.mcrm.adapter.util.PolyphonePinyinUtils;
import com.zte.mcrm.channel.util.ExceptionMsgUtils;
import com.zte.mcrm.channel.util.ReUtils;
import com.zte.mcrm.common.consts.CommonConstant;
import com.zte.mcrm.common.consts.HttpMethodEnum;
import com.zte.mcrm.common.exception.BusinessRuntimeException;
import com.zte.mcrm.common.framework.exception.PrmRetCode;
import com.zte.mcrm.common.model.SysGlobalConstVo;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.mcrm.common.util.MsaUtils;
import com.zte.opty.common.constants.CommonConst;
import com.zte.springbootframe.util.local.LocalMessageUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/9/16
 */
@Service
public class PrmServiceImpl implements PrmService {
    /** 日志对象 */
    private static  final Logger LOGGER = LoggerFactory.getLogger(PrmServiceImpl.class);
    @Value("${prm.industryTree}")
    private String queryIndustryTreeUrl;
    @Value("${prm.channelCustomerList}")
    private String getChannelCustomerListUrl;
    @Value("${prm.industryNameUrl}")
    private String queryIndustryNameUrl;
    @Value("${prm.role}")
    private String queryRoleListUrl;
    @Value("${prm.channelAccountDetail}")
    private String channelAccountDetail;
    @Value("${prm.queryCertificationInfo}")
    private String queryCertificationInfo;
    @Value("${prm.effectiveAndHistoryCodeValuesUrl}")
    private String effectiveAndHistoryCodeValuesUrl;

    @Value("${government.enterprise.sales.division.china.org.id}")
    private String governmentDeptNo;

    @Value("${department.channelBusiness}")
    private String channelBusinessDeptNo;

    /** PRM base微服务名*/
    public static final String PRM_BASE_SERVICE = "zte-crm-ichannel-base";
    /** PRM certification微服务名*/
    public static final String PRM_CERTIFICATION_SERVICE = "zte-crm-ichannel-certification";
    /**
     * 政企接收人系统参数类型
     */
    public static final String GOV_INTERFACE_PERSON_CODE_TYPE = "govInterfacePerson";

    /** 行业树 */
    public static final String TREE_NAME = "行业树";

    /** 政企行业树类型 */
    public static final String GOV_INDUSTRY_TYPE = "10";

//    @Autowired
//    PrmService prmService;
    /**
     * 查询行业树
     *
     * @return IndustryTreeDataVO
     * <AUTHOR>
     * @date 2021/9/16
     */
    @Override
    public IndustryTreeDataVO listIndustry(String needInvalid) throws Exception {
        try {
            Map<String, String> paramString = new HashMap<>(3);
            paramString.put("serviceName", PRM_BASE_SERVICE);
            paramString.put("version", "v1");
            paramString.put("url", queryIndustryTreeUrl);
            Map<String, Object> parmas = Maps.newHashMap();
            parmas.put("industryType", GOV_INDUSTRY_TYPE);
            parmas.put("needInvalid", needInvalid);
            List<IndustryTreeDataVO> industryTreeDataVOS = MsaUtils.invokeServiceAndReturnBO(
                    paramString,
                    HttpMethodEnum.GET,
                    parmas,
                    new TypeReference<ServiceData<List<IndustryTreeDataVO>>>() {
            });
            IndustryTreeDataVO ret = new IndustryTreeDataVO();
            ret.setSons(industryTreeDataVOS);
            ret.setMeaning(TREE_NAME);
            return ret;
        } catch (BusinessRuntimeException e) {
            LOGGER.error("query industry tree error",e);
            throw new BusinessRuntimeException(PrmRetCode.EXTERNAL_SERVERERROR_CODE,e.getExMsg());
        }
    }

    @Override
    public PageRows<TreeNodeVO> getIndustryTree(IndustryTreeDTO industryTreeDTO) throws Exception {
        PageRows<TreeNodeVO> page = new PageRows<>();
        List<TreeNodeVO> rootNodeList = new ArrayList<>();
        IndustryTreeDataVO industryTree = listIndustry(CommonConst.N);
        List<IndustryTreeDataVO> sons = industryTree.getSons();
        if (CollectionUtils.isNotEmpty(sons)) {
            for (IndustryTreeDataVO son : sons) {
                TreeNodeVO treeNodeVO = new TreeNodeVO();
                treeNodeVO.setCode(son.getLookupCode());
                treeNodeVO.setName(son.getMeaning());
                treeNodeVO.setCodeFullPath(son.getLookupCode());
                treeNodeVO.setNameFullPath(son.getMeaning());
                if (addByConditionOne(industryTreeDTO, son, rootNodeList, treeNodeVO)) {
                    continue;
                }
                assignChildren(industryTreeDTO, son, treeNodeVO, rootNodeList);
            }
        }
        page.setRows(rootNodeList);
        page.setTotal(rootNodeList.size());
        return page;
    }

    private static boolean addByConditionOne(IndustryTreeDTO industryTreeDTO, IndustryTreeDataVO son,
                                     List<TreeNodeVO> rootNodeList, TreeNodeVO treeNodeVO) {
        if (StringUtils.isNotBlank(industryTreeDTO.getParentCode())) {
            if (!StringUtils.equals(industryTreeDTO.getParentCode(), son.getLookupCode())) {
                return true;
            }
        } else if (StringUtils.isNotBlank(industryTreeDTO.getName())) {
            if (son.getMeaning().contains(industryTreeDTO.getName())) {
                rootNodeList.add(treeNodeVO);
            }
        } else if (CollectionUtils.isNotEmpty(industryTreeDTO.getCodeList())) {
            if (industryTreeDTO.getCodeList().contains(son.getLookupCode())) {
                rootNodeList.add(treeNodeVO);
            }
        } else {
            rootNodeList.add(treeNodeVO);
        }
        return false;
    }

    private static void assignChildren(IndustryTreeDTO industryTreeDTO, IndustryTreeDataVO son, TreeNodeVO treeNodeVO,
                                  List<TreeNodeVO> rootNodeList) {
        if (CollectionUtils.isNotEmpty(son.getSons())) {
            List<IndustryTreeDataVO> sonSons = son.getSons();
            List<TreeNodeVO> tempNodeList = new ArrayList<>();
            treeNodeVO.setChildren(tempNodeList);
            for (IndustryTreeDataVO sonSon : sonSons) {
                TreeNodeVO treeNodeVO1 = new TreeNodeVO();
                treeNodeVO1.setCode(sonSon.getLookupCode());
                treeNodeVO1.setName(sonSon.getMeaning());
                treeNodeVO1.setCodeFullPath(son.getLookupCode() + "-" + sonSon.getLookupCode());
                treeNodeVO1.setNameFullPath(son.getMeaning() + "-" + sonSon.getMeaning());
                treeNodeVO1.setParentCode(son.getLookupCode());
                treeNodeVO1.setLeafFlag(Boolean.TRUE);
                addByConditionTwo(industryTreeDTO, treeNodeVO, rootNodeList, sonSon, treeNodeVO1);
            }
        }
    }

    private static void addByConditionTwo(IndustryTreeDTO industryTreeDTO, TreeNodeVO treeNodeVO,
                                          List<TreeNodeVO> rootNodeList, IndustryTreeDataVO sonSon, TreeNodeVO treeNodeVO1) {
        List<TreeNodeVO> tempNodeList = treeNodeVO.getChildren();
        if (StringUtils.isNotBlank(industryTreeDTO.getParentCode())) {
            rootNodeList.add(treeNodeVO1);
        } else if (StringUtils.isNotBlank(industryTreeDTO.getName())) {
            if (StringUtils.contains(sonSon.getMeaning(), industryTreeDTO.getName())) {
                tempNodeList.add(treeNodeVO1);
                if (!rootNodeList.contains(treeNodeVO)) {
                    rootNodeList.add(treeNodeVO);
                }
            }
        } else if (CollectionUtils.isNotEmpty(industryTreeDTO.getCodeList())) {
            if (industryTreeDTO.getCodeList().contains(treeNodeVO1.getCode())) {
                rootNodeList.add(treeNodeVO1);
            }
        }
    }

    /**
     * 查询所有子行业列表
     *
     * @return 子行业列表
     * @throws Exception
     * <AUTHOR>
     * @date 2021年9月24日 11:47:41
     */
    @Override
    public List<AuthConstraintDTO> getSubIndustryList(String needInvalid) throws Exception {
        // 返回结果
        List<AuthConstraintDTO> resultList = new LinkedList<>();
        // 查询行业树
        IndustryTreeDataVO industryTreeDataVO = this.listIndustry(needInvalid);
        // 行业
        List<IndustryTreeDataVO> industryList = Optional.ofNullable(industryTreeDataVO).map(v -> v.getSons()).orElse(Collections.EMPTY_LIST);
        for (IndustryTreeDataVO industryVO : industryList) {
            // 每个行业对应的子行业
            assignSubList(industryVO, resultList);
        }
        return resultList;
    }

    private static void assignSubList(IndustryTreeDataVO industryVO, List<AuthConstraintDTO> resultList) {
        List<IndustryTreeDataVO> subIndustryList = Optional.ofNullable(
                industryVO).map(v -> v.getSons()).orElse(Collections.EMPTY_LIST);
        for (IndustryTreeDataVO subIndustryVO : subIndustryList) {
            // 展示名称为：行业-子行业
            String showName = industryVO.getMeaning() + CommonConstant.MID_LINE + subIndustryVO.getMeaning();
            resultList.add(new AuthConstraintDTO(showName, subIndustryVO.getLookupCode()));
        }
    }

    /**
     * 查询渠道商客户列表
     * @param formData
     * @return
     *
     */
    @Override
    public List<ChannelCustomerRes> getChannelCustomerList(FormData<ChannelCustomerRes> formData) throws Exception {
        try {
            Map<String, String> paramString = new HashMap<>(3);
            paramString.put("serviceName", PRM_CERTIFICATION_SERVICE);
            paramString.put("version", "v1");
            paramString.put("url", getChannelCustomerListUrl);
            return MsaUtils.invokeServiceAndReturnBO(
                    paramString,
                    HttpMethodEnum.POST,
                    formData,
                    new TypeReference<ServiceData<List<ChannelCustomerRes>>>() {
                    });
        } catch (BusinessRuntimeException e) {
            LOGGER.error("getChannelCustomerList error:{}", ExceptionMsgUtils.getStackTrace(e, 2000));
            throw new BusinessRuntimeException(PrmRetCode.EXTERNAL_SERVERERROR_CODE, e.getExMsg());
        }
    }


    @Override
    public AccountDetail getAccountDetail(AccountDetailQueryDTO accountDetailQueryDTO) throws Exception {
        try {
            Map<String, String> paramString = new HashMap<>(3);
            paramString.put("serviceName", PRM_BASE_SERVICE);
            paramString.put("version", "v1");
            paramString.put("url", "/ucs/account/detail");
            return MsaUtils.invokeServiceAndReturnBO(
                    paramString,
                    HttpMethodEnum.POST,
                    accountDetailQueryDTO,
                    new TypeReference<ServiceData<AccountDetail>>() {
                    });
        } catch (BusinessRuntimeException e) {
            LOGGER.error("getChannelCustomerList error:{}", ExceptionMsgUtils.getStackTrace(e, 2000));
            throw new BusinessRuntimeException(PrmRetCode.EXTERNAL_SERVERERROR_CODE, e.getExMsg());
        }
    }

    /**
     * 查询渠道商帐号基本信息
     *
     * @param accountId
     * @return IndustryTreeDataVO
     * <AUTHOR>
     * @date 2021/9/16
     */
    @Override
    public ChannelAccountDetailDTO getChannelAccountBasicInfo(String accountId) throws Exception {
        Map<String,String> paramsMap = Maps.newHashMap();
        paramsMap.put("accountId",accountId);
        try {
            Map<String, String> paramString = new HashMap<>(3);
            paramString.put("serviceName", PRM_CERTIFICATION_SERVICE);
            paramString.put("version", "v1");
            paramString.put("url", channelAccountDetail);
            return MsaUtils.invokeServiceAndReturnBO(
                    paramString,
                    HttpMethodEnum.GET,
                    paramsMap,
                    new TypeReference<ServiceData<ChannelAccountDetailDTO>>() {
                    });
        } catch (BusinessRuntimeException e) {
            LOGGER.error("query  channelAccountDetail info error",e);
            throw new BusinessRuntimeException(PrmRetCode.EXTERNAL_SERVERERROR_CODE,e.getExMsg());
        }
    }

    @Override
    public List<OrganizationTreeData> getDescendants() throws Exception {
        List<OrganizationTreeData> organizationTreeData = null;

        try {
            organizationTreeData = MsaUtils.invokeBaseService(
                    CommonConstant.ORGANIZATION_TREE_QUERY_URL,
                    HttpMethodEnum.GET,
                    null,
                    new TypeReference<ServiceData<List<OrganizationTreeData>>>() {
                    });
        } catch (Exception e) {
            LOGGER.error("PrmServiceImpl#getDescendants Error, url:{}, Exceptions:{}",
                    CommonConstant.ORGANIZATION_TREE_QUERY_URL, ExceptionMsgUtils.getStackTrace(e, 2000));
        }


        return organizationTreeData;
    }

    @Override
    public OrganizationTreeData getDescendants(@RequestParam(value = "orgId",required = false)String orgId) throws Exception {
        OrganizationTreeData organizationTreeData = new OrganizationTreeData();
        Map<String,String> paramMap = Maps.newHashMap();
        paramMap.put("orgId", orgId);
        try {
            organizationTreeData = MsaUtils.invokeBaseService(
                    CommonConstant.ORGANIZATION_TREE_QUERY_URL,
                    HttpMethodEnum.GET,
                    paramMap,
                    new TypeReference<ServiceData<OrganizationTreeData>>() {
                    });
        } catch (Exception e) {
            LOGGER.error("PrmServiceImpl#getDescendants Error, url:{}, params:{}, Exceptions:{}",
                    CommonConstant.ORGANIZATION_TREE_QUERY_URL, paramMap, ExceptionMsgUtils.getStackTrace(e, 2000));
        }


        return organizationTreeData;
    }

    /**
     * base服务查询 组织树接口,获取组织信息，最大1000条
     * @param orgIds
     * @return
     * @throws Exception
     */
    public List<OrgConditionVO> getOrgInfosFromChannel(List<String> orgIds) throws Exception {
        List<OrgConditionVO> orgData = new ArrayList<>();
        OrgConditionParam orgConditionParam = new OrgConditionParam();
        orgConditionParam.setOrgIds(orgIds);
        orgConditionParam.setIsVisible(CommonConstant.COMMON_FLAG_Y);
        orgConditionParam.setOrgType(CommonConstant.ORGANIZATION_CHINA);
        orgConditionParam.setPageSize(CommonConstant.ONE_THOUSAND);

        OrgConditionVos descendants = getCondition(orgConditionParam);
        if(Objects.nonNull(descendants)) {
            orgData = descendants.getOrgDatas();
        }
        return orgData;
    }

    @Override
    public OrgConditionVos getCondition(@RequestBody @Valid OrgConditionParam orgConditionParam) throws Exception {
        OrgConditionVos orgConditionVos = new OrgConditionVos();
        try {
            orgConditionVos = MsaUtils.invokeBaseService(
                    CommonConstant.ORG_DATA_CONDITION_URL,
                    HttpMethodEnum.POST,
                    orgConditionParam,
                    new TypeReference<ServiceData<OrgConditionVos>>() {
                    });
        } catch (Exception e) {
            LOGGER.error("PrmServiceImpl#GetCondition Error, url:{}, params:{}, Exceptions:{}",
                    CommonConstant.ORG_DATA_CONDITION_URL, JSON.toJSONString(orgConditionParam), ExceptionMsgUtils.getStackTrace(e, 2000));
        }
        return orgConditionVos;
    }

    @Override
    public List<ProductTreeData> getProduction() throws Exception {
        List<ProductTreeData> productionTreeData = null;
        try {
            productionTreeData = MsaUtils.invokeBaseService(
                    CommonConstant.PRODUCT_TREE_QUERY_URL,
                    HttpMethodEnum.GET,
                    null,
                    new TypeReference<ServiceData<List<ProductTreeData>>>() {
                    });

        } catch (Exception e) {
            LOGGER.error("PrmServiceImpl#getProduction Error, url:{},Exceptions:{}",
                    CommonConstant.PRODUCT_TREE_QUERY_URL, ExceptionMsgUtils.getStackTrace(e, 2000));
        }

        return productionTreeData;
    }

    @Override
    public List<OrgConditionVO> getOrganizationNodeList(boolean filterChannelDepart) throws Exception {
        // 调用外部服务请求参数
        OrgConditionParam orgConditionParam = new OrgConditionParam();
        orgConditionParam.setIsVisible(CommonConstant.COMMON_FLAG_Y);
        orgConditionParam.setOrgType(CommonConstant.ORGANIZATION_CHINA);
        orgConditionParam.setPageSize(CommonConstant.ONE_THOUSAND);
        orgConditionParam.setIsPerformanceDept(CommonConstant.COMMON_FLAG_Y);

        //调用 base 服务的组织树接口, 返回的所有
        List<OrgConditionVO> organizationDatas = this.getCondition(orgConditionParam).getOrgDatas();
        List<OrgConditionVO> result = new ArrayList<>();

        if (CollectionUtils.isEmpty(organizationDatas)){
            return result;
        }

        // 需要数据 enabled_flag = "Y"， orgStatusted = 1 ； 如果 filterChannelDepart 为 True 则过滤渠道业务部
        for (OrgConditionVO orgConditionVO: organizationDatas) {

            // 如果 要求过滤 并且 当前data是渠道商业务部，则过滤
            boolean canAddFlag =
                    !(filterChannelDepart && channelBusinessDeptNo.equals(orgConditionVO.getOrganizationId()));

            // enabled_flag = "1", orgStatusted = 1
            boolean legalFlagAndStatusted =
                    (CommonConstant.ENABELD_FLAG_VALID.equals(orgConditionVO.getEnabledFlag())
                            && (CommonConstant.ORG_STATUS_VALID == orgConditionVO.getOrgStatusId()));

            // 不是 政企中国营销事业部
            boolean notGovernmentEnterprisePart = !governmentDeptNo.equals(orgConditionVO.getOrganizationId());

            if (canAddFlag && legalFlagAndStatusted && notGovernmentEnterprisePart) {
                String simplifiedPinyin = PolyphonePinyinUtils.changeToGetShortPinYin(orgConditionVO.getOrganizationName());
                orgConditionVO.setOrganizationNameSimplifiedPinyin(simplifiedPinyin);
                result.add(orgConditionVO);
            }
        }

        return result;
    }

    /**
     * 获取投资方所在地的组织列表，先按总监办、办事处标签顺序排序，总监办、办事处的再分别按首字母拼音顺序排序
     * @param filterChannelDepart 是否过滤渠道业务部
     * @return
     * @throws Exception
     */
    @Override
    public List<OrgConditionVO> getOrganizationNodeListWithSorted(boolean filterChannelDepart) throws Exception {
        List<OrgConditionVO> orgConditionVOS = getOrganizationNodeList(filterChannelDepart);
        return orgConditionVOS.stream()
                .sorted(Comparator.comparing(OrgConditionVO::getOrganizationNameSimplifiedPinyin))
                .sorted(Comparator.comparing(OrgConditionVO::getOrgLabel, (o1, o2) -> {
                    int result = 0;
                    if (StringUtils.equals(o1, CommonConstant.ORG_LABEL_FLAG_DIRECTOR_OFFICE)
                            && !StringUtils.equals(o2, CommonConstant.ORG_LABEL_FLAG_DIRECTOR_OFFICE)){
                        result = -1;
                    }else if (StringUtils.equals(o2, CommonConstant.ORG_LABEL_FLAG_DIRECTOR_OFFICE)
                            && !StringUtils.equals(o1, CommonConstant.ORG_LABEL_FLAG_DIRECTOR_OFFICE)) {
                        result = 1;
                    }
                    return result;
                }))
                .collect(Collectors.toList());
    }


    @Override
    public List<ProductTreeData> getProductList() throws Exception {
        List<ProductTreeData> productions = getProduction();

        if(CollectionUtils.isEmpty(productions)) {
            return Lists.newArrayList();
        }
        // 仅返回 itemLevel=1 的产品
        for (ProductTreeData product : productions) {
            product.setChildren(null);
        }

        return productions;
    }

    @Override
    public List<IndustryTreeDataVO> getIndustryNames(List<String> industryIds, String needInvalid) throws Exception {
        Map<String, String> paramString = new HashMap<>(3);
        paramString.put("serviceName", PRM_BASE_SERVICE);
        paramString.put("version", "v1");
        paramString.put("url", queryIndustryNameUrl + "?" + "needInvalid" + "=" + needInvalid);
        return MsaUtils.invokeServiceAndReturnBO(
                paramString,
                HttpMethodEnum.POST,
                industryIds,
                new TypeReference<ServiceData<List<IndustryTreeDataVO>>>() {
                });
    }

    @Override
    public Map<String, List<RoleDTO>> queryRoleList(@RequestParam(value = "roleId",required = false)String roleId,
                                             @RequestParam(value = "moduleId",required = true) String moduleId) throws Exception{
        Map<String,String> paramMap = Maps.newHashMap();
        paramMap.put("moduleId",moduleId);
        paramMap.put("empNoList",roleId);
        Map<String, String> paramString = new HashMap<>(3);
        paramString.put("serviceName", PRM_BASE_SERVICE);
        paramString.put("version", "v1");
        paramString.put("url", queryRoleListUrl);
        return MsaUtils.invokeServiceAndReturnBO(
                paramString,
                HttpMethodEnum.GET,
                paramMap,
                new TypeReference<ServiceData<Map<String, List<RoleDTO>>>>() {
                });
    }
    @Override
    public List<OrganizationNode> getOrganizationNames(List<String> industryIds) throws Exception {
        Map<String, String> paramString = new HashMap<>(3);
        paramString.put("serviceName", PRM_BASE_SERVICE);
        paramString.put("version", "v1");
        paramString.put("url", "/tree/org/bulk/query");
        return MsaUtils.invokeServiceAndReturnBO(
                paramString,
                HttpMethodEnum.POST,
                industryIds,
                new TypeReference<ServiceData<List<OrganizationNode>>>() {
                });
    }

    @Override
    public List<OrganizationNode> getOrganizationNamesWithNoException(List<String> orgCodeList){
        List<OrganizationNode> organizationNames = new ArrayList<>();
        try {
            // 从组织树获取组织信息列表
            organizationNames = this.getOrganizationNames(orgCodeList);
        }catch (Exception e){
            LOGGER.error("批量获取组织名称失败，Exception：{}", ExceptionMsgUtils.getStackTrace(e, 2000));
        }
        return organizationNames;
    }

    @Override
    public List<AuthConstraintDTO> getSubIndustryListWithNoException(String needInvalid){
        List<AuthConstraintDTO> subIndustryList = new ArrayList<>();
        try {
            // 从行业树获取行业列表
            subIndustryList = this.getSubIndustryList(needInvalid);
        }catch (Exception e){
            LOGGER.error("从行业树获取子行业列表失败，Exception：{}", ExceptionMsgUtils.getStackTrace(e, 2000));
        }
        return subIndustryList;
    }

    /**
     * 查询渠道认证信息服务
     *
     * @param   entity
     * @return  PageRows<CertificationQueryResult>
     * <AUTHOR>
     * @date    2021/11/19
     */
    @Override
    public PageRows<CertificationQueryResult> queryCertificationInfoCustomer(CertificationQueryInfo entity) {
        try {
            Map<String, String> paramString = new HashMap<>(3);
            paramString.put("serviceName", PRM_CERTIFICATION_SERVICE);
            paramString.put("version", "v1");
            paramString.put("url", queryCertificationInfo);
            return MsaUtils.invokeServiceAndReturnBO(
                    paramString,
                    HttpMethodEnum.POST,
                    entity,
                    new TypeReference<ServiceData<PageRows<CertificationQueryResult>>>() {
                    });
        } catch (Exception e) {
            LOGGER.error("查询渠道认证信息服务出错,entity:{},Exception:{}", entity, ExceptionMsgUtils.getStackTrace(e, 2000), e);
            throw new com.zte.itp.msa.core.exception.BusiException(RetCode.BUSINESSERROR_CODE, LocalMessageUtils.getMessage("queryCertificationInformError"));
        }
    }

    /**
     * 查询渠道系统参数配置
     * @param codeType
     * @return
     */
    @Override
    public List<PrmQuickCodeValueVO> getByCodeType(String codeType) {
        try {
            String url = "/prmquickcodevalue/" + codeType + "/codevalues";
            Map<String, String> paramString = new HashMap<>(3);
            paramString.put("serviceName", PRM_BASE_SERVICE);
            paramString.put("version", "v1");
            paramString.put("url", url);
            return MsaUtils.invokeServiceAndReturnBO(
                    paramString,
                    HttpMethodEnum.GET,
                    null,
                    new TypeReference<ServiceData<List<PrmQuickCodeValueVO>>>() {
                    });
        } catch (Exception e) {
            LOGGER.error("查询渠道系统参数出错,codeType:{},Exception:{}", codeType, e.getMessage(), e);
            throw new com.zte.itp.msa.core.exception.BusiException(RetCode.BUSINESSERROR_CODE, "query system config error");
        }
    }


    /**
     * 根据系统参数类型列表获取状态为有效和历史的配置值数据
     * @param codeType
     * @return
     */
    @Override
    public List<PrmQuickCodeValueVO> getEffectiveAndHistoryByCodeType(String codeType) {
        try {
            List<String> params = Lists.newArrayList(codeType);
            Map<String, String> paramString = new HashMap<>(3);
            paramString.put("serviceName", PRM_BASE_SERVICE);
            paramString.put("version", "v1");
            paramString.put("url", effectiveAndHistoryCodeValuesUrl);
            List<BulkCodeValuesByOneTypeVo> res = MsaUtils.invokeServiceAndReturnBO(
                    paramString,
                    HttpMethodEnum.POST,
                    params,
                    new TypeReference<ServiceData<List<BulkCodeValuesByOneTypeVo>>>() {
                    });
            if(CollectionUtils.isNotEmpty(res)){
                return res.get(0).getCodeValueVOList();
            }else{
                return Lists.newArrayList();
            }
        } catch (Exception e) {
            LOGGER.error("根据系统参数类型列表获取状态为有效和历史的配置值数据出错,codeType:{},Exception:{}", codeType, e.getMessage(), e);
            throw new com.zte.itp.msa.core.exception.BusiException(RetCode.BUSINESSERROR_CODE, "query system config error");
        }
    }

    /**
     * 获取商机行业映射到SS系统的行业
     * @param parentIndustry
     * @param subIndustry
     * @return
     */
    @Override
    public Optional<IndustryMappingBetweenLineAndSs> getCustomerSystemIndustryMapping(String parentIndustry, String subIndustry) {
        IndustryMappingBetweenLineAndSs industryMappingBetweenLineAndSs = new IndustryMappingBetweenLineAndSs(parentIndustry, subIndustry);
        List<PrmQuickCodeValueVO> lineToSSIndustry = getByCodeType("lineToSSIndustry");
        if (CollectionUtils.isEmpty(lineToSSIndustry)) {
            return Optional.empty();
        }
        String key = parentIndustry + ":" + subIndustry;
        Map<String, String> lineAndCustomerIndustryMap = lineToSSIndustry.stream()
                .collect(Collectors.toMap(PrmQuickCodeValueVO::getValueCode, PrmQuickCodeValueVO::getCodeValueZh));
        String customerIndustryCompose = lineAndCustomerIndustryMap.get(key);
        if (StringUtils.isBlank(customerIndustryCompose)) {
            return Optional.empty();
        }
        industryMappingBetweenLineAndSs.setCustomerIndustry(customerIndustryCompose);
        return Optional.of(industryMappingBetweenLineAndSs);
    }


    /**
     * 获取政企接口人
     * @return
     */
    @Override
    public List<String> getGovInterfacePerson() {
        List<String> govInterfacePersonList = new ArrayList<>();
        // 政企的接口人
        List<PrmQuickCodeValueVO> prmQuickCodeValues = getByCodeType(GOV_INTERFACE_PERSON_CODE_TYPE);
        if (CollUtil.isNotEmpty(prmQuickCodeValues)) {
            prmQuickCodeValues.forEach(prmQuickCodeValue -> {
                if (ReUtils.isInnerId(prmQuickCodeValue.getValueCode())) {
                    govInterfacePersonList.add(prmQuickCodeValue.getValueCode());
                }
            });
        }
        return govInterfacePersonList;
    }

    /**
     * 获取新业务的行业，该配置来自系统参数配置：newBusinessIndustry
     * @return
     */
    @Override
    public boolean isNewBusinessIndustry(String industryCode) {
        if (StringUtils.isBlank(industryCode)) {
            return false;
        }
        List<PrmQuickCodeValueVO> newBusinessIndustryCodeValueList = getByCodeType("newBusinessIndustry");
        if (CollectionUtils.isEmpty(newBusinessIndustryCodeValueList)) {
            return false;
        }
        return newBusinessIndustryCodeValueList.stream().map(PrmQuickCodeValueVO::getValueCode)
                .anyMatch(e -> Objects.equals(e, industryCode));
    }


}
