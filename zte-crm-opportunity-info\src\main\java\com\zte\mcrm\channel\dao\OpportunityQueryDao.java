package com.zte.mcrm.channel.dao;


import com.zte.mcrm.channel.model.dto.OpportunityStatusDTO;
import com.zte.mcrm.channel.model.dto.SimilarOpportunityQueryDTO;
import com.zte.mcrm.channel.model.entity.CurrentOptyInfo;
import com.zte.mcrm.channel.model.entity.ExternalOpportunityDetail;
import com.zte.mcrm.channel.model.entity.SimilarOpportunity;
import com.zte.mcrm.channel.model.vo.CrmCustomerDetermineResultVO;
import com.zte.mcrm.channel.model.vo.OpportunityDataVO;
import com.zte.mcrm.channel.model.vo.OpportunityQueryParamVO;
import com.zte.mcrm.channel.model.vo.WinOpportunityVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 描述：
 * 创建时间：2021/9/22
 *
 * @author：王丹凤6396000572
 */
@Mapper
public interface OpportunityQueryDao {
    /**
     * 分页查询
     * @param paramVO
     * @return
     */
    List<OpportunityDataVO> queryOpportunityList(OpportunityQueryParamVO paramVO);

    List<CrmCustomerDetermineResultVO> determineIntransitsAndValidOptys(@Param("crmCustomerCodes") List<String> crmCustomerCodes, @Param("years") int years);

    /**
     * 查询记录总数
     * @param paramVO
     * @return
     */
    int queryOpportunityListCount(OpportunityQueryParamVO paramVO);

    /**
     * 商机详情查询（对外）
     *
     * @param optyCd 商机编号
     * @return ExternalOpportunityDetail
     * <AUTHOR>
     * @date 2021/9/24
     */
    ExternalOpportunityDetail getExternalOpportunityDetail(String optyCd);

    /**
     * 根据 row-id 查询类似商机
     *
     * @param param 商机编号
     * @return List<SimilarOpportunity>
     * <AUTHOR>
     * @date 2021/10/09
     */
    List<SimilarOpportunity> querySimilarOpportunityByRowId(SimilarOpportunityQueryDTO param);

    /**
     * 根据LastAccName 查询类似商机
     *
     * @param lastAccName 最终行业
     * @return List<SimilarOpportunity>
     * <AUTHOR>
     * @date 2021/10/09
     */
    List<SimilarOpportunity> querySimilarOpportunityByLastAccName(@Param("lastAccName") String lastAccName);

    /**
     * 根据 商机名称、渠道商名称 查询类似商机
     *
     * @param param 商机编号
     * @return List<SimilarOpportunity>
     * <AUTHOR>
     * @date 2021/10/09
     */
    List<SimilarOpportunity> querySimilarOpportunityByName(SimilarOpportunityQueryDTO param);

    /**
     * 查询中标商机信息
     *
     * @param crmCustomerCode 渠道商客户编码
     * @return List<WinOpportunityVO>
     * <AUTHOR>
     * @date 2024/01/01
     */
    List<WinOpportunityVO> queryWinOpportunities(@Param("crmCustomerCode") String crmCustomerCode);

    /**
     * 商机编号对应的 类似商机，查出来数据数量 <= 1
     *
     * @param simiarOptyCd 商机编号
     * @return List<SimilarOpportunity>
     * <AUTHOR>
     * @date 2021/10/09
     */
    List<SimilarOpportunity> querySimilarOpportunityByOptyCd(String simiarOptyCd);

    /**
     * 根据 rowId 查询当前商机的信息
     *
     * @param rowId 当前商机主键
     * @return ExternalOpportunityDetail
     * <AUTHOR>
     * @date 2021/10/09
     */
    CurrentOptyInfo queryCurrentOpportunityByRowId(String rowId);

    /**
     *
     * @param optyCd
     * @return
     */
    List<OpportunityStatusDTO> queryCurrentOpportunityByOptyCd(String optyCd);
}
