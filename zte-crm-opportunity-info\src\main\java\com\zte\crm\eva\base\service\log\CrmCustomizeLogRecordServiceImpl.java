package com.zte.crm.eva.base.service.log;

import com.zte.crm.eva.base.infrastructure.access.mapper.ZteLogMapper;
import com.zte.iss.gcsc.log.model.ZteLogRecord;
import com.zte.iss.gcsc.log.service.CustomizeLogRecordService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-04-14
 */
@Service
@Primary
public class CrmCustomizeLogRecordServiceImpl implements CustomizeLogRecordService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CrmCustomizeLogRecordServiceImpl.class);

    @Value("#{'${zte.log.filter.rdcIds:}'.split(',')}")
    private List<String> rdcIds;

    @Autowired
    private ZteLogMapper zteLogMapper;

    @Override
    public ZteLogRecord filter(ZteLogRecord zteLogRecord) {
        String rdcId = zteLogRecord.getRdcId();
        if (StringUtils.isBlank(rdcId) || CollectionUtils.isEmpty(rdcIds) || !rdcIds.contains(rdcId)) {
            return null;
        }
        return zteLogRecord;
    }

    @Override
    public void save(ZteLogRecord zteLogRecord) {
        try {
            zteLogMapper.save(zteLogRecord);
        } catch (Exception e) {
            LOGGER.error("CrmCustomizeLogRecordService-save error, param:{}, error:{}", zteLogRecord, e.getMessage(), e);
        }
    }
}
