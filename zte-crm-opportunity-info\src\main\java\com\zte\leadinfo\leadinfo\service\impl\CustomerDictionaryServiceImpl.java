package com.zte.leadinfo.leadinfo.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zte.leadinfo.leadinfo.entity.CustomerDictionaryDO;
import com.zte.leadinfo.leadinfo.mapper.CustomerDictionaryMapper;
import com.zte.leadinfo.leadinfo.service.CustomerDictionaryService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class CustomerDictionaryServiceImpl extends ServiceImpl<CustomerDictionaryMapper, CustomerDictionaryDO> implements CustomerDictionaryService {

    @Override
    public List<CustomerDictionaryDO> selectByIds(List<String> accountList) {
        return this.baseMapper.selectBatchIds(accountList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchInsert(List<CustomerDictionaryDO> list) {
        return this.saveOrUpdateBatch(list);
    }
}
