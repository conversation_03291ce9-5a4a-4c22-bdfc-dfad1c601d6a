<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zte.mcrm.channel.dao.ComApprovalRecordDao" >
    <!-- 屏蔽mybatis的2级缓存  <cache />  -->
  
    <!-- 记录和实体映射配置 -->
    <resultMap id="BaseMap" type="com.zte.mcrm.channel.model.entity.ComApprovalRecord" >
        <id column="row_id" property="rowId" jdbcType="BIGINT" />
		<result column="business_id" property="businessId" jdbcType="VARCHAR" />	
		<result column="business_type" property="businessType" jdbcType="VARCHAR" />	
		<result column="work_flow_instance_id" property="workFlowInstanceId" jdbcType="VARCHAR" />	
		<result column="enabled_flag" property="enabledFlag" jdbcType="CHAR" />	
		<result column="created_by" property="createdBy" jdbcType="VARCHAR" />	
		<result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />	
		<result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR" />	
		<result column="last_updated_date" property="lastUpdatedDate" jdbcType="TIMESTAMP" />	
    </resultMap>

    <sql id="base_column">
        t.row_id ,
        t.business_id ,
        t.business_type ,
        t.work_flow_instance_id ,
        t.enabled_flag ,
        t.created_by ,
        t.created_date ,
        t.last_updated_by ,
        t.last_updated_date 
    </sql>

    <sql id="base_where">
        <if test="rowId != null"> and t.row_id = #{rowId}</if>
        <if test="businessId != null and businessId != ''"> and t.business_id = #{businessId}</if>
        <if test="businessType != null and businessType != ''"> and t.business_type = #{businessType}</if>
        <if test="workFlowInstanceId != null and workFlowInstanceId != ''"> and t.work_flow_instance_id = #{workFlowInstanceId}</if>
        <if test="enabledFlag != null and enabledFlag != ''"> and t.enabled_flag = #{enabledFlag}</if>
        <if test="createdBy != null and createdBy != ''"> and t.created_by = #{createdBy}</if>
        <if test="createdDate != null"> and t.created_date = #{createdDate}</if>
        <if test="lastUpdatedBy != null and lastUpdatedBy != ''"> and t.last_updated_by = #{lastUpdatedBy}</if>
        <if test="lastUpdatedDate != null"> and t.last_updated_date = #{lastUpdatedDate}</if>
    and t.tenant_id = #{headerTenantId}
    </sql>

    <select id="getFlowIdByOpportunityCode" resultMap="BaseMap">
        select business_id, work_flow_instance_id
        FROM com_approval_record t, s_opty o
        WHERE t.enabled_flag= 'Y'
        and t.business_id = o.id
        and o.opty_code in
        <foreach collection="codeList" item="code" open="(" separator="," close=")" >
            #{code}
        </foreach>
    </select>

    <!-- 获取一条记录 -->
    <select id="get"  resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM com_approval_record t
        WHERE
        t.row_id=#{rowId, jdbcType=BIGINT}
    </select>
    <select id="getByBusinessId"  resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM com_approval_record t
        WHERE enabled_flag= 'Y' and
        t.business_id=#{businessId, jdbcType=VARCHAR}
    </select>
 
    <!-- 获取符合条件的记录列表 -->
    <select id="getList" parameterType="java.util.Map" resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM com_approval_record t
        WHERE 1=1
        <include refid="base_where"/>
    </select>
  
    <!-- 软删除一条记录 -->
    <update id="softDelete" >
        UPDATE com_approval_record
        SET enabled_flag = 'N'
        WHERE
        row_id = #{rowId, jdbcType=BIGINT}
    </update>

    <!-- 软删除一条记录 -->
    <update id="softDeleteByBusinessIdAndFlowId" >
        UPDATE com_approval_record
        SET enabled_flag = 'N'
        WHERE
        business_id = #{businessId, jdbcType=VARCHAR}
        and work_flow_instance_id = #{flowId, jdbcType=VARCHAR}
    </update>
    
    <!-- 删除一条记录 -->
    <delete id="delete" >
        DELETE FROM com_approval_record
        WHERE
        row_id = #{rowId, jdbcType=BIGINT}
    </delete>

    <!--添加一条记录 -->
    <insert id="insert" parameterType="com.zte.mcrm.channel.model.entity.ComApprovalRecord" >
        INSERT INTO com_approval_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
		    <if test="rowId != null">row_id ,</if>
		    <if test="businessId != null">business_id ,</if>
		    <if test="businessType != null">business_type ,</if>
		    <if test="workFlowInstanceId != null">work_flow_instance_id ,</if>
		    <if test="enabledFlag != null">enabled_flag ,</if>
		    <if test="createdBy != null">created_by ,</if>
		    <if test="createdDate != null">created_date ,</if>
		    <if test="lastUpdatedBy != null">last_updated_by ,</if>
		    <if test="lastUpdatedDate != null">last_updated_date ,</if>
    		tenant_id
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
    	    <if test="rowId != null">#{rowId, jdbcType=BIGINT} ,</if>
    	    <if test="businessId != null">#{businessId, jdbcType=VARCHAR} ,</if>
    	    <if test="businessType != null">#{businessType, jdbcType=VARCHAR} ,</if>
    	    <if test="workFlowInstanceId != null">#{workFlowInstanceId, jdbcType=VARCHAR} ,</if>
    	    <if test="enabledFlag != null">#{enabledFlag, jdbcType=CHAR} ,</if>
    	    <if test="createdBy != null">#{createdBy, jdbcType=VARCHAR} ,</if>
    	    <if test="createdDate != null">#{createdDate, jdbcType=TIMESTAMP} ,</if>
    	    <if test="lastUpdatedBy != null">#{lastUpdatedBy, jdbcType=VARCHAR} ,</if>
    	    <if test="lastUpdatedDate != null">#{lastUpdatedDate, jdbcType=TIMESTAMP} ,</if>
			#{headerTenantId, jdbcType=BIGINT}
        </trim>
    </insert>

    <!--批量添加记录 -->
    <insert id="insertByBatch" parameterType="java.util.List" >
        INSERT INTO com_approval_record
        (
		    row_id ,
    	    business_id ,
    	    business_type ,
    	    work_flow_instance_id ,
    	    enabled_flag ,
    	    created_by ,
    	    created_date ,
    	    last_updated_by ,
    	    last_updated_date ,
    		tenant_id
        )
        VALUES
        <foreach collection ="list" item="item" index= "index" separator =",">
        (
    	    #{item.rowId, jdbcType=BIGINT} ,
    	    #{item.businessId, jdbcType=VARCHAR} ,
    	    #{item.businessType, jdbcType=VARCHAR} ,
    	    #{item.workFlowInstanceId, jdbcType=VARCHAR} ,
    	    #{item.enabledFlag, jdbcType=CHAR} ,
    	    #{item.createdBy, jdbcType=VARCHAR} ,
    	    #{item.createdDate, jdbcType=TIMESTAMP} ,
    	    #{item.lastUpdatedBy, jdbcType=VARCHAR} ,
    	    #{item.lastUpdatedDate, jdbcType=TIMESTAMP} ,
			#{headerTenantId, jdbcType=BIGINT}
        )
        </foreach>
    </insert>
  
    <!-- 更新一条记录 -->
    <update id="update" parameterType="com.zte.mcrm.channel.model.entity.ComApprovalRecord" >
        UPDATE com_approval_record
        <set>
		    <if test="businessId != null">business_id=#{businessId, jdbcType=VARCHAR} ,</if>
		    <if test="businessType != null">business_type=#{businessType, jdbcType=VARCHAR} ,</if>
		    <if test="workFlowInstanceId != null">work_flow_instance_id=#{workFlowInstanceId, jdbcType=VARCHAR} ,</if>
		    <if test="enabledFlag != null">enabled_flag=#{enabledFlag, jdbcType=CHAR} ,</if>
		    <if test="createdBy != null">created_by=#{createdBy, jdbcType=VARCHAR} ,</if>
		    <if test="createdDate != null">created_date=#{createdDate, jdbcType=TIMESTAMP} ,</if>
		    <if test="lastUpdatedBy != null">last_updated_by=#{lastUpdatedBy, jdbcType=VARCHAR} ,</if>
		    <if test="lastUpdatedDate != null">last_updated_date=#{lastUpdatedDate, jdbcType=TIMESTAMP} ,</if>
	    </set>
        WHERE
        row_id=#{rowId, jdbcType=BIGINT}
    </update>
  
    <!-- 翻页函数:获取符合条件的记录数 -->
    <select id="getCount" parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT count(*) FROM com_approval_record t
        WHERE 1=1
        <include refid="base_where"/>
    </select>
	
    <!-- 翻页函数:获取一页的记录集 -->
    <select id="getPage" parameterType="java.util.Map" resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM com_approval_record t
        WHERE 1=1
        <include refid="base_where"/>
        <if test="orderField != null and orderField == 'rowId'"> order by t.row_id
            <if test="order != null and order == 'desc'"> desc </if>
        </if>
        <if test="startRow != null and rowSize != null"> limit #{startRow},#{rowSize} </if>
    </select>

    <select id="queryFlowInstance" resultType="string" parameterType="string">
        SELECT work_flow_instance_id
        FROM com_approval_record t
        WHERE business_id=#{businessId}
        and enabled_flag = 'Y'
    </select>
</mapper>
