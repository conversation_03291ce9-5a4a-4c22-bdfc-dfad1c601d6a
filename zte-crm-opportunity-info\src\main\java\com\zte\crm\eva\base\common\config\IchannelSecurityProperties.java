package com.zte.crm.eva.base.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;


/**
 * 渠道加密配置
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "ichannel.encrypt.decrypt")
@PropertySource(value = "classpath:${spring.application.name}.properties", ignoreResourceNotFound = true)
@Getter
@Setter
public class IchannelSecurityProperties {

    private String key;
}
