package com.zte.mcrm.adapter.model.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
* 快速编码值 VO 实体类
* <AUTHOR> 10269210
* @date 2021/06/01
*/

@Setter
@Getter
@ToString
public class PrmQuickCodeValueVO {
    /**
     * 值代码 max=200
     */
    private String valueCode;
    /**
     * 父值代码
     */
    private String parentValueCode;
    /**
     * 数据层级
     */
    private Integer dataLevel;
    /**
     * 编码中文值 max=500
     */
    private String codeValueZh;
    /**
     * 编码英文值 max=500
     */
    private String codeValueEn;
    /**
     * 编码值-随语言环境变化
     */
    private String codeValue;
    /**
     * 排序值
     */
    private Integer orderValue;
    /**
     * 备注
     */
    private String memo;
    /**
     * 备注
     */
    private String expand;
    /**
     * 子结构
     */
    private List<PrmQuickCodeValueVO> children;
}