package com.zte.mcrm.common.upload.controller;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.clouddisk.service.DocCloudService;
import com.zte.mcrm.common.exception.BusinessRuntimeException;
import com.zte.mcrm.common.upload.constants.UploadConst;
import com.zte.mcrm.common.upload.constants.UploadRetCode;
import com.zte.mcrm.common.upload.model.entity.ComUploadFile;
import com.zte.mcrm.common.upload.model.entity.SimpleUploadFileInfo;
import com.zte.mcrm.common.upload.model.entity.UploadFileMapper;
import com.zte.mcrm.common.upload.service.UploadFileService;
import com.zte.mcrm.common.util.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.mime.content.ByteArrayBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Api(tags = "附件管理")
@RestController
@RequestMapping("/fileManager")
public class FileManageController {
    public static final int QUERY_SIZE = 20;
    public static final String ONCE_TIME = "1";
    private static final Logger LOGGER = LoggerFactory.getLogger(FileManageController.class);

    @Autowired
    private UploadFileService uploadFileService;
    @Autowired
    private DocCloudService docCloudService;

    @ApiOperation("上传附件")
    @PostMapping(value = "/uploadFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ServiceData<SimpleUploadFileInfo> uploadFiles(HttpServletRequest request,
                                                         @ApiParam @RequestParam(required=false) String uploadType,
                                                         @ApiParam @RequestParam(required=false) String billId,
                                                         @ApiParam @RequestPart MultipartFile file,
                                                         @ApiParam @RequestParam(required=false) String docDesc,
                                                         @ApiParam("附件密级-0为公开,1内部公开") @RequestParam(required=false) Integer secretLevel,
                                                         @ApiParam("文件类型-01为文本,02为图片") @RequestParam(required=false) String fileType) throws Exception{
        if(StringUtils.isNotEmpty(docDesc) && docDesc.length() > UploadConst.DOCDESC_LENGTH) {
            throw new BusinessRuntimeException(RetCode.BUSINESSERROR_CODE, UploadRetCode.DOCDESC_LENGTH_ERROR);
        }
        ServiceData<SimpleUploadFileInfo> result = new ServiceData<>();

        ComUploadFile uploadFile = new ComUploadFile();
        uploadFile.setUploadType(uploadType);
        uploadFile.setBillId(billId);
        uploadFile.setDocDesc(docDesc);
        uploadFile.setSecretLevel(secretLevel);
        uploadFile.setFileType(fileType);

        uploadFileService.uploadFile(uploadFile,file, request);
        SimpleUploadFileInfo simpleUploadFileInfo = UploadFileMapper.INSTANCE.uploadFileToSimpleInfo(uploadFile);
        result.setBo(simpleUploadFileInfo);
        return result;
    }

    @ApiOperation("下载附件-支持批量下载")
    @GetMapping("/downloadFile")
    public void downloadFile(@ApiParam @RequestParam String dmeKey, @ApiParam @RequestParam(required = false) String userId,
                             HttpServletResponse response) throws Exception {
        if (StringUtils.isBlank(userId)) {
            userId = CommonUtils.getEmpNo();
        }
        if (StringUtils.isBlank(userId)) {
            throw new BusinessRuntimeException("HTTP Header X-Emp-No and userId are empty");
        }
        ByteArrayBody file = docCloudService.download(dmeKey, userId);
        ServletOutputStream outputStream = response.getOutputStream();
        if (null != file) {
            response.setHeader("Content-type", "application/octet-stream");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + file.getFilename());
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            file.writeTo(outputStream);
        }
        outputStream.flush();
        outputStream.close();
    }

    @ApiOperation("根据dmeKey刪除附件")
    @DeleteMapping("/deleteFile/{dmeKey}")
    public ServiceData<Boolean> deleteFile(@ApiParam @PathVariable String dmeKey) {
        ServiceData<Boolean> result = new ServiceData<>();
        result.setBo(uploadFileService.deleteFileByDmeKey(dmeKey));
        return result;
    }
    @ApiOperation("复印文件")
    @GetMapping("/copy")
    public ServiceData<Boolean> copy(@ApiParam @RequestParam String uploadType, @ApiParam @RequestParam String oid, @ApiParam @RequestParam String newBillOid) {
        ServiceData<Boolean> result = new ServiceData<>();
        result.setBo(uploadFileService.copy(uploadType, oid, newBillOid));
        return result;
    }

    @ApiOperation("统计某个业务的附件数")
    @GetMapping("/countFileNum")
    public ServiceData<Long> countFileNum(@ApiParam @RequestParam String uploadType, @ApiParam @RequestParam Long billId) {
        ServiceData<Long> result = new ServiceData<>();
        result.setBo(uploadFileService.countFileNum(uploadType, billId));
        return result;
    }

    @ApiOperation("查询业务上传的附件")
    @GetMapping("/findSpecUploadFile")
    public ServiceData<List<ComUploadFile>> findSpecUploadFile(@RequestParam(required = false) String billId, @RequestParam(required = false) String uploadType) {
        ServiceData<List<ComUploadFile>> result = new ServiceData<>();
        ComUploadFile uploadFile = new ComUploadFile();
        uploadFile.setUploadType(uploadType);
        uploadFile.setBillId(billId);
        uploadFile.setEnabledFlag("Y");
        result.setBo(uploadFileService.findUploadFiles(uploadFile));
        return result;
    }

    @ApiOperation("更新文件顺序")
    @PutMapping("/updateUploadFileOrder")
    public ServiceData<Boolean> updateUploadFileOrder(@RequestBody List<ComUploadFile> list)
    {
        ServiceData<Boolean> ret = new ServiceData<>();
        uploadFileService.updateUploadFileOrder(list);
        ret.setBo(true);
        return ret;
    }

    @ApiOperation("更新文件密级")
    @PutMapping("/updateUploadFileSecretLevel")
    public ServiceData<Boolean> updateUploadFileSecretLevel
            (@RequestBody List<String> dmeKeyList, @RequestParam int secretLevel)
            throws Exception
    {
        ServiceData<Boolean> ret = new ServiceData<>();
//        boolean ans;
////        ans = uploadFileService.updateUploadFileSecretLevel(dmeKeyList, secretLevel);
//        ret.setBo(ans);
        return ret;
    }

    @ApiOperation("批量查询业务上传的附件")
    @GetMapping("/findSpecUploadFileInBatch")
    public ServiceData<List<ComUploadFile>> findSpecUploadFileInBatch(@RequestParam String billIds, @RequestParam(required = false) String uploadType) {
        ServiceData<List<ComUploadFile>> result = new ServiceData<>();
        List<String> billIdList = Splitter.on(',').trimResults().omitEmptyStrings().splitToList(billIds);
        if (CollectionUtils.isEmpty(billIdList)) {
            result.setBo(Lists.newArrayList());
            return result;
        }
        int size = billIdList.size();
        if (size > QUERY_SIZE) {
            LOGGER.error("findSpecUploadFile billIds exceed the size:20, billIds: {}, uploadTypeOid:{}", billIds, uploadType);
            throw new BusinessRuntimeException("exceed the size of query: 20");
        }
        List<String> uniqueBillOids = billIdList.stream().map(String::new).distinct().collect(Collectors.toList());
        result.setBo(uploadFileService.queryUploadFileInBatch(uploadType, uniqueBillOids));
        return result;
    }

    @ApiOperation("获取附件一次性下载链接")
    @GetMapping("/getDownloadLink")
    public com.zte.itp.msa.core.model.ServiceData<String> getDownloadLink(@ApiParam @RequestParam String dmeKey) throws Exception {
        com.zte.itp.msa.core.model.ServiceData<String> result = new com.zte.itp.msa.core.model.ServiceData<>();
        String userId = CommonUtils.getEmpNo();
        String downLink = docCloudService.getDownloadLink(dmeKey, userId, ONCE_TIME, true);
        result.setBo(downLink);
        return result;
    }
    @ApiOperation("批量获取附件一次性下载链接-返回Map<K,V> K-dmeKey;V-一次性下载链接")
    @PostMapping ("/getDownloadLinks")
    public ServiceData<Map<String,String>> getDownloadLinks(@ApiParam @RequestBody List<String> dmeKeys) throws Exception {
        ServiceData<Map<String,String>> result = new ServiceData<>();
        String userId = CommonUtils.getEmpNo();
        Map<String,String> downLinkMap = docCloudService.getDownloadLinks(dmeKeys, userId, ONCE_TIME);
        result.setBo(downLinkMap);
        return result;
    }

    @ApiOperation("文档预览")
    @GetMapping("/getPreviewFile")
    public ServiceData<String> getPreviewFile(@RequestParam String docKey,@RequestParam String fileName){
        ServiceData<String> result = new ServiceData<>();
        result.setBo(docCloudService.previewFile(docKey,fileName));
        return result;
    }

}
