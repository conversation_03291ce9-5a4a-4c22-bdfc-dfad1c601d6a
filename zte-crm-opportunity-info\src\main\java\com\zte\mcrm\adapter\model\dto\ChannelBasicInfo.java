package com.zte.mcrm.adapter.model.dto;

import lombok.Data;


/**
 * 渠道商基本信息
 * <AUTHOR>
 * @date 2021/9/16
 */
@Data
public class ChannelBasicInfo {


    /**
     * 渠道商编号
     */
    private String customerId;

    /**
     * 渠道商名称
     */
    private String channelName;

    /**
     * 渠道商认证等级
     */
    private String certificationLevel;

    /**
     * 渠道商信用等级
     */
    private String creditLevel;

    /**
     * 渠道商认证产品
     */
    private String certificationProduct;

    /**
     * 渠道商业务省份
     */
    private String province;

    /**
     * 受限主体类型
     */
    private String restrictedPartyType;

    /**
     * 认证类型
     */
    private String certificationType;

    /**
     * BEID
     */
    private String beid;

}
