package com.zte.mcrm.channel.service;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.zte.mcrm.channel.constant.ArbitrationTypeEnum;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.channel.model.dto.HistApproveDataDTO;
import com.zte.mcrm.common.consts.CommonConst;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class HistApproveDataExcelListener extends AnalysisEventListener<HistApproveDataDTO> {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final String PASS = "通过";

    private static final String REFUSE = "拒绝";

    private static final String TRANSFER = "转移";

    private static final String ARBITRATION = "仲裁";

    private static final String GO_BACK = "退回";

    private static final String ICENTER_OPINION = "iCenter APP:";

    private static final String UNIQUENESS_OF_REPORTING = "报备唯一性";

    private static final String DISAGREE_WITH_THIS_REPORTING_ITEM = "不同意此报备项目";

    private String opinionResultConvertor(String inputResult){
        String outputResult = null;
        if (StringUtils.isBlank(inputResult)){
            return null;
        }
        switch (inputResult){
            case PASS:
                outputResult = CommonConst.Y;
                break;
            case REFUSE:
                outputResult = CommonConst.N;
                break;
            case TRANSFER:
                // 转移的不同步
                break;
            case ARBITRATION:
                outputResult = CommonConst.N;
                break;
            case GO_BACK:
                outputResult = CommonConst.N;
                break;
            default:
                outputResult = inputResult;
                break;
        }
        return outputResult;
    }

    private String arbitrationTypeConvertor(String inputResult){
        String outputResult = null;
        if (StringUtils.isBlank(inputResult)){
            return null;
        }
        switch (inputResult){
            case UNIQUENESS_OF_REPORTING:
                outputResult = ArbitrationTypeEnum.FAILURE_REASON_ALREADY.getName();
                break;
            case DISAGREE_WITH_THIS_REPORTING_ITEM:
                outputResult = ArbitrationTypeEnum.FAILURE_REASON_REFUSE.getName();
                break;
            default:
                outputResult = inputResult;
                break;
        }
        return outputResult;
    }

    /**
     * 进行读的操作具体执行方法，一行一行的读取数据
     * 从第二行开始读取，不读取表头
     *
     * @param histApproveDataDTO
     * @param analysisContext
     */
    @Override
    public void invoke(HistApproveDataDTO histApproveDataDTO, AnalysisContext analysisContext) {
        logger.info("histApproveDataDTO:{}", histApproveDataDTO);
        String approver = histApproveDataDTO.getApprover();
        if (StringUtils.isNotBlank(approver)){
            approver = approver.replaceAll(OpportunityConstant.REG_NO, "");
        }
        histApproveDataDTO.setApprover(approver);
        histApproveDataDTO.setApproveResult(opinionResultConvertor(histApproveDataDTO.getApproveResult()));
        String opinion = histApproveDataDTO.getOpinion();
        if (StringUtils.isNotBlank(opinion)){
            opinion = opinion.replaceAll(ICENTER_OPINION, "");
        }
        histApproveDataDTO.setOpinion(opinion);
        histApproveDataDTO.setArbitrationType(arbitrationTypeConvertor(histApproveDataDTO.getArbitrationType()));
    }

    /**
     * 读取表头信息
     *
     * @param headMap
     * @param context
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        super.invokeHeadMap(headMap, context);
        System.out.println("表头信息：" + headMap);
    }

    /**
     * 读取完数据的操作
     *
     * @param analysisContext
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
    }
}
