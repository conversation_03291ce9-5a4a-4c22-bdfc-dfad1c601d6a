package com.zte.mcrm.adapter.controller;

import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import com.zte.mcrm.adapter.model.dto.AccountDetailQueryDTO;
import com.zte.mcrm.adapter.model.dto.ChannelAccountDetailDTO;
import com.zte.mcrm.adapter.model.dto.ChannelCustomerRes;
import com.zte.mcrm.adapter.model.vo.AccountDetail;
import com.zte.mcrm.adapter.model.vo.ChannelAccountInfo;
import com.zte.mcrm.adapter.service.impl.ComposeServiceImpl;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 外部依赖相关接口
 * <AUTHOR>
 * @date 2021/9/16
 */
@Api(tags = "外部依赖相关接口")
@RestController
@RequestMapping("/channel")
public class ComposeController {
    @Autowired
    private ComposeServiceImpl composeServiceImpl;

    @ApiOperation("查询渠道商身份基础信息-进入商机时使用")
    @GetMapping(value = "/account/detail")
    public ServiceData<ChannelAccountDetailDTO> getChannelAccountBasicInfo(@RequestParam(required = false) String accountId) throws Exception {
        accountId= StringUtils.isNotBlank(accountId)?accountId: CommonUtils.getEmpNo();
        ChannelAccountDetailDTO res = composeServiceImpl.getChannelAccountBasicInfo(accountId);
        return ServiceResultUtil.success(res);
    }
    @ApiOperation("查询渠道商身份信息-新增商机时使用")
    @GetMapping(value = "/identity/info/{crmCustomerCode}")
    public ServiceData<ChannelAccountInfo> getChannelAccountInfo(@PathVariable("crmCustomerCode") String crmCustomerCode) throws Exception {
        ChannelAccountInfo res = composeServiceImpl.getChannelAccountInfo(crmCustomerCode);
        return ServiceResultUtil.success(res);
    }

    @ApiOperation("查询用户账号信息")
    @GetMapping(value = "/getAccountDetail")
    public ServiceData<AccountDetail> getAccountDetail(@RequestHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO)String xEmpNo) throws Exception {
        if(StringUtils.isBlank(xEmpNo)){
            return null;
        }
        AccountDetail accountDetail = composeServiceImpl.getAccountDetail(xEmpNo);
        return ServiceResultUtil.success(accountDetail);
    }

    @ApiOperation("用户中心查询用户账号信息")
    @PostMapping(value = "/getAccountDetailFromUcs")
    public ServiceData<AccountDetail> getAccountDetailFromUcs(@RequestHeader(SysGlobalConst.HTTP_HEADER_X_EMP_NO)String xEmpNo,
                                                              @RequestBody AccountDetailQueryDTO accountDetailQueryDTO) throws Exception {
        if(StringUtils.isBlank(xEmpNo)){
            return null;
        }
        // 限制只能查询当前登录用户
        accountDetailQueryDTO.setAccountId(xEmpNo);
        AccountDetail accountDetail = composeServiceImpl.getAccountDetailFromUcs(accountDetailQueryDTO);
        return ServiceResultUtil.success(accountDetail);
    }

    @ApiOperation("查询渠道商信息")
    @GetMapping(value = "/channelInfo/{crmCustomerCode}")
    public ServiceData<ChannelCustomerRes> getChannelInfoList(@PathVariable("crmCustomerCode") String crmCustomerCode) throws Exception {
        ChannelCustomerRes res = composeServiceImpl.getChannelInfo(crmCustomerCode);
        return ServiceResultUtil.success(res);
    }
}
