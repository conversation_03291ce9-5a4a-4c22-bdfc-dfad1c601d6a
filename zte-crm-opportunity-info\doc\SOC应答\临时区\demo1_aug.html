<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <title>SOC应答系统 DEMO - 增强版</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <style>
    body {
      margin: 0;
      font-family: 'Segoe UI', 'PingFang SC', Arial, sans-serif;
      background: #ffffff;
    }

    .soc-navbar {
      height: 48px;
      background: #ffffff;
      color: #7c7c7c;
      display: flex;
      align-items: center;
      padding: 0 32px;
      font-size: 16px;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
      z-index: 10;
    }

    .soc-navbar-logo {
      font-weight: bold;
      font-size: 20px;
      margin-right: 32px;
      letter-spacing: 1px;
    }

    .soc-navbar-menu {
      flex: 1;
      display: flex;
      align-items: center;
    }

    .soc-navbar-menu>div {
      margin-right: 24px;
      cursor: pointer;
    }

    .soc-navbar-search {
      background: #fff;
      border-radius: 4px;
      padding: 2px 8px;
      margin-right: 24px;
      color: #333;
      display: flex;
      align-items: center;
    }

    .soc-navbar-search input {
      border: none;
      outline: none;
      background: transparent;
      font-size: 14px;
      width: 120px;
    }

    .soc-navbar-user {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .soc-navbar-avatar {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: #e6eaf2;
      display: inline-block;
    }

    .soc-layout {
      display: flex;
      height: calc(100vh - 48px);
    }

    .soc-sidebar,
    .soc-chatbar {
      background: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      z-index: 2;
    }

    .soc-sidebar {
      width: 220px;
      min-width: 180px;
      max-width: 260px;
      border-right: 1px solid #e8eaec;
      display: flex;
      flex-direction: column;
    }

    .soc-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-width: 0;
    }

    .soc-header {
      height: 48px;
      background: #fff;
      border-bottom: 1px solid #e8eaec;
      display: flex;
      align-items: center;
      padding: 0 24px;
      font-size: 18px;
      font-weight: 500;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    }

    .soc-main {
      flex: 1;
      overflow: auto;
      padding: 16px;
      background: #ffffff;
    }

    /* 表格容器样式 */
    #answerTableCard {
      background: none;
      border-radius: 0;
      box-shadow: none;
      margin: 0;
      overflow-x: auto;
      padding: 0;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      margin: 8px 0;
      position: relative;
    }

    table.soc-table {
      border-collapse: separate;
      border-spacing: 0;
      width: 100%;
      min-width: 1100px;
      font-size: 13px;
      background: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      border-radius: 6px;
      border: 1px solid #e8eaec;
      overflow: hidden;
    }

    table.soc-table th,
    table.soc-table td {
      border-bottom: 1px solid #e8eaec;
      border-right: 1px solid #e8eaec;
      padding: 8px 8px;
      text-align: left;
      vertical-align: middle;
      font-size: 13px;
    }

    table.soc-table th:last-child,
    table.soc-table td:last-child {
      border-right: none;
    }

    table.soc-table td {
      word-wrap: break-word;
      overflow-wrap: break-word;
    }

    table.soc-table th {
      background: #EEEEEE;
      font-weight: 500;
      color: #717171;
      border-top: 1px solid #d6d6d6;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    table.soc-table tr:last-child td {
      border-bottom: none;
    }

    table.soc-table tbody tr:hover {
      background-color: #f5f7fa;
    }

    table.soc-table tbody tr {
      transition: background-color 0.2s ease;
    }

    /* 合并单元格样式 */
    table.soc-table td[rowspan] {
      vertical-align: top;
      background-color: #fafafa;
    }

    /* 产品列样式 */
    table.soc-table td:nth-child(4) {
      font-weight: 500;
      color: #1765d5;
    }

    /* AnswerType标签样式 */
    table.soc-table td:nth-child(5) span {
      display: inline-block;
      font-size: 12px;
      border-radius: 4px;
      padding: 2px 8px;
    }

    /* 应答下拉框样式 */
    table.soc-table td:nth-child(6) select {
      width: 100%;
      min-width: 80px;
      max-width: 180px;
      text-align: left;
      padding: 4px 8px;
      box-sizing: border-box;
      background-position: right 6px center;
      appearance: none;
    }

    /* 应答说明文本框样式 */
    table.soc-table td:nth-child(7) textarea {
      min-height: 40px;
      resize: vertical;
      border: none;
      outline: none;
      background: transparent;
      width: 100%;
      font-family: inherit;
      font-size: inherit;
      padding: 4px;
      box-sizing: border-box;
    }

    /* 备注输入框样式 */
    table.soc-table td:nth-child(9) textarea {
      min-height: 40px;
      resize: vertical;
      border: none;
      outline: none;
      background: transparent;
      width: 100%;
      font-family: inherit;
      font-size: inherit;
      padding: 4px;
      box-sizing: border-box;
    }

    /* 表格内输入框通用样式 */
    table.soc-table input[type="text"],
    table.soc-table textarea {
      border: none;
      outline: none;
      background: transparent;
      width: 100%;
      font-family: inherit;
      font-size: inherit;
      padding: 4px;
      box-sizing: border-box;
    }

    /* 表格内输入框聚焦时的样式 */
    table.soc-table input[type="text"]:focus,
    table.soc-table textarea:focus {
      background: #f8f9fa;
      border-radius: 4px;
    }

    /* 表格内select下拉框样式 */
    table.soc-table select {
      border: none;
      outline: none;
      background: transparent;
      width: 100%;
      font-family: inherit;
      font-size: inherit;
      padding: 4px;
      box-sizing: border-box;
    }

    /* 表格内select下拉框聚焦时的样式 */
    table.soc-table select:focus {
      background: #f8f9fa;
      border-radius: 4px;
    }

    /* 表格内select下拉框悬停时的样式 */
    table.soc-table select:hover {
      background: #f8f9fa;
    }

    /* 表格头部检索框聚焦时的样式 */
    table.soc-table th input[type="text"]:focus,
    table.soc-table th select:focus {
      background: #f8f9fa;
      border-radius: 4px;
    }

    /* 表格头部检索框悬停时的样式 */
    table.soc-table th input[type="text"]:hover,
    table.soc-table th select:hover {
      background: #f8f9fa;
    }

    /* 表格内输入框悬停时的样式 */
    table.soc-table input[type="text"]:hover,
    table.soc-table textarea:hover {
      background: #f8f9fa;
    }

    .soc-table-img {
      max-width: 80px;
      max-height: 40px;
      border-radius: 4px;
      border: 1px solid #eee;
    }

    .soc-table-action {
      color: #1890ff;
      cursor: pointer;
    }

    .soc-chatbar {
      width: 300px;
      min-width: 180px;
      max-width: 340px;
      border-left: 1px solid #e8eaec;
      display: flex;
      flex-direction: column;
    }

    .soc-hide-btn {
      position: absolute;
      top: 12px;
      right: -12px;
      background: #fff;
      border: 1px solid #ececec;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      cursor: pointer;
      box-shadow: 0 2px 8px #eee;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .soc-sidebar,
    .soc-chatbar {
      position: relative;
    }

    .soc-sidebar .soc-header,
    .soc-chatbar .soc-header {
      font-size: 15px;
      font-weight: 500;
      border-bottom: 1px solid #f0f0f0;
    }

    .soc-sidebar>div,
    .soc-chatbar>div {
      padding: 12px 16px;
    }

    .soc-sidebar>div>div,
    .soc-chatbar>div>div {
      margin-bottom: 12px;
      color: #555;
    }

    @media (max-width: 1200px) {
      .soc-sidebar {
        width: 140px;
      }

      .soc-chatbar {
        width: 160px;
      }
    }

    @media (max-width: 900px) {

      .soc-sidebar,
      .soc-chatbar {
        display: none;
      }

      .soc-content {
        flex: 1 1 100%;
      }
    }

    .soc-btn {
      height: 32px;
      padding: 0 16px;
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      background: #fff;
      color: #666;
      font-size: 14px;
      cursor: pointer;
      transition: all .2s ease;
      margin-right: 0;
      outline: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
      display: inline-flex;
      align-items: center;
      gap: 4px;
      font-weight: 400;
      line-height: 1;
    }

    .soc-btn:hover {
      background: #fff;
      color: #1890ff;
      border-color: #1890ff;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.12);
    }

    .soc-btn-green {
      background: #52c41a;
      color: #fff;
      border: 1px solid #52c41a;
      box-shadow: 0 2px 4px rgba(82, 196, 26, 0.2);
    }

    .soc-btn-green:hover {
      background: #73d13d;
      border-color: #73d13d;
      color: #fff;
      box-shadow: 0 4px 8px rgba(82, 196, 26, 0.3);
    }

    .soc-btn-orange {
      background: #fa8c16;
      color: #fff;
      border: 1px solid #fa8c16;
      box-shadow: 0 2px 4px rgba(250, 140, 22, 0.2);
    }

    .soc-btn-orange:hover {
      background: #ffa940;
      border-color: #ffa940;
      color: #fff;
      box-shadow: 0 4px 8px rgba(250, 140, 22, 0.3);
    }

    .soc-btn-blue {
      background: #0077ff;
      color: #fff;
      border: 1px solid #1871D6;
    }

    .soc-btn-blue:hover {
      background: #40a9ff;
      border-color: #40a9ff;
      color: #fff;
    }

    .soc-btn-red {
      background: #f5222d;
      border: 1px solid #f5222d;
      color: #fff;
    }

    .soc-btn-red:hover {
      background: #ff4d4f;
      border-color: #ff4d4f;
      color: #fff;
    }

    .soc-btn-2 {
      background: #ffffff;
      border: 1px solid #1871D6;
      color: #0077ff;
    }

    .soc-btn-3 {
      background: #ffffff;
      border: 1px solid #D8D8D8;
      color: #0077ff;
    }

    .soc-btn-4 {
      background: #ffffff;
      border: 1px solid #D8D8D8;
      color: #9f9f9f;
    }

    /* 次要按钮样式 - 白色背景蓝色边框 */
    .soc-btn-secondary {
      background: #fff;
      color: #1890ff;
      border: 1px solid #1890ff;
      box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
    }

    .soc-btn-secondary:hover {
      background: #e6f7ff;
      color: #1890ff;
      border-color: #1890ff;
      box-shadow: 0 4px 8px rgba(24, 144, 255, 0.15);
    }

    /* 输入框通用样式 */
    input[type="text"],
    input[type="search"],
    select {
      border: 1px solid #d4d7db;
      border-radius: 4px;
      padding: 4px 8px;
      font-size: 13px;
      transition: border-color 0.2s ease, box-shadow 0.2s ease;
      background: #fff;
    }

    input[type="text"]:focus,
    input[type="search"]:focus,
    select:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      outline: none;
    }

    /* 错误提示样式 */
    .error-message {
      color: #ff4d4f;
      font-size: 12px;
      margin-top: 4px;
      display: none;
    }

    /* 成功提示样式 */
    .success-message {
      color: #52c41a;
      font-size: 12px;
      margin-top: 4px;
      display: none;
    }

    /* 加载状态样式 */
    .loading {
      opacity: 0.6;
      pointer-events: none;
    }

    .loading::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 20px;
      height: 20px;
      margin: -10px 0 0 -10px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #1890ff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* 弹窗遮罩层 */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0, 0, 0, 0.3);
      z-index: 1000;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .modal-content {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.15);
      max-width: 90vw;
      max-height: 90vh;
      overflow: auto;
    }

    /* 菜单项悬停效果 */
    .main-action-item:hover {
      background-color: #f5f5f5;
    }

    .main-action-item:focus {
      background-color: #e6f7ff;
      outline: none;
    }

    /* 表格工具栏样式 */
    .soc-toolbar {
      background: #fafafa;
      border: 1px solid #e8e8e8;
      border-radius: 6px 6px 0 0;
      padding: 12px 16px;
      margin-bottom: 0;
    }

    /* 状态提示样式 */
    .status-tips {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-top: none;
      padding: 8px 16px;
      font-size: 13px;
      border-radius: 0 0 6px 6px;
      margin-bottom: 8px;
    }

    /* 分页样式优化 */
    #tablePagination {
      background: #fafafa;
      border: 1px solid #e8e8e8;
      border-top: none;
      border-radius: 0 0 6px 6px;
      padding: 12px 16px;
      margin: 0;
    }

    #tablePagination button {
      background: #fff;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      padding: 4px 12px;
      cursor: pointer;
      font-size: 13px;
      transition: all 0.2s ease;
    }

    #tablePagination button:hover:not(:disabled) {
      background: #e6f7ff;
      border-color: #1890ff;
      color: #1890ff;
    }

    #tablePagination button:disabled {
      background: #f5f5f5;
      color: #ccc;
      cursor: not-allowed;
    }

    #tablePagination select {
      background: #fff;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      padding: 4px 8px;
      font-size: 13px;
    }

    /* 表格容器优化 */
    #answerTableCard {
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }

    /* 操作列样式 */
    .action-col {
      display: flex;
      gap: 6px;
      align-items: center;
    }

    .action-col .soc-btn {
      height: 24px;
      padding: 0 8px;
      font-size: 11px;
      line-height: 1;
    }

    /* 标签样式 */
    .status-tag {
      display: inline-block;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 11px;
      font-weight: 500;
      line-height: 1.2;
    }

    .status-tag.fc {
      background: #f6ffed;
      color: #52c41a;
      border: 1px solid #b7eb8f;
    }

    .status-tag.pc {
      background: #fff7e6;
      color: #faad14;
      border: 1px solid #ffd591;
    }

    .status-tag.nc {
      background: #fff2f0;
      color: #ff4d4f;
      border: 1px solid #ffccc7;
    }

    .status-tag.na {
      background: #f5f5f5;
      color: #666;
      border: 1px solid #d9d9d9;
    }

    .status-tag.unanswered {
      background: #f5f5f5;
      color: #999;
      border: 1px solid #d9d9d9;
    }

    /* 侧边栏标签页样式 */
    .soc-tab-btn {
      background: none;
      border: none;
      padding: 8px 12px;
      cursor: pointer;
      font-size: 13px;
      color: #666;
      border-bottom: 2px solid transparent;
      transition: all 0.2s ease;
    }

    .soc-tab-btn:hover {
      color: #1890ff;
      background: #f0f8ff;
    }

    .soc-tab-btn.active {
      color: #1890ff;
      border-bottom-color: #1890ff;
      background: #f0f8ff;
    }

    /* 快捷指令样式 */
    .quick-cmd-item {
      background: #e6f7ff;
      color: #1765d5;
      border: 1px solid #91d5ff;
      border-radius: 4px;
      padding: 4px 8px;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.2s ease;
      user-select: none;
    }

    .quick-cmd-item:hover {
      background: #bae7ff;
      border-color: #69c0ff;
    }

    /* 聊天消息样式 */
    .chat-message {
      margin-bottom: 12px;
      padding: 8px 12px;
      border-radius: 8px;
      font-size: 13px;
      line-height: 1.5;
    }

    .chat-message.user {
      background: #e6f7ff;
      color: #1765d5;
      margin-left: 20px;
      border-bottom-right-radius: 4px;
    }

    .chat-message.assistant {
      background: #f6ffed;
      color: #389e0d;
      margin-right: 20px;
      border-bottom-left-radius: 4px;
    }

    .chat-message.system {
      background: #fff7e6;
      color: #d46b08;
      text-align: center;
      font-size: 12px;
      margin: 8px 0;
    }

    /* 响应式优化 */
    @media (max-width: 1400px) {
      .soc-sidebar {
        width: 180px;
      }

      .soc-chatbar {
        width: 260px;
      }
    }

    @media (max-width: 1200px) {
      .soc-sidebar {
        width: 160px;
      }

      .soc-chatbar {
        width: 220px;
      }

      table.soc-table {
        font-size: 12px;
      }

      table.soc-table th,
      table.soc-table td {
        padding: 6px 6px;
      }
    }

    @media (max-width: 900px) {
      .soc-sidebar,
      .soc-chatbar {
        display: none;
      }

      .soc-content {
        flex: 1 1 100%;
      }

      .soc-toolbar {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
      }

      .soc-toolbar > div {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }
    }

    @media (max-width: 768px) {
      .soc-main {
        padding: 8px;
      }

      table.soc-table {
        font-size: 11px;
        min-width: 800px;
      }

      table.soc-table th,
      table.soc-table td {
        padding: 4px 4px;
      }

      .soc-btn {
        height: 28px;
        padding: 0 10px;
        font-size: 12px;
      }

      .action-col .soc-btn {
        height: 22px;
        padding: 0 6px;
        font-size: 10px;
      }

      .modal-content {
        width: 95vw !important;
        margin: 10px;
      }

      .soc-navbar {
        padding: 0 16px;
        font-size: 14px;
      }

      .soc-navbar-logo {
        font-size: 16px;
        margin-right: 16px;
      }

      .soc-navbar-search {
        display: none;
      }
    }

    @media (max-width: 480px) {
      .soc-navbar-menu {
        display: none;
      }

      table.soc-table {
        min-width: 600px;
      }

      .soc-toolbar > div {
        flex-direction: column;
      }

      #tablePagination {
        flex-direction: column;
        gap: 8px;
        text-align: center;
      }
    }

    /* 加载动画优化 */
    .loading-spinner {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #1890ff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 8px;
    }

    /* 工具提示样式 */
    .tooltip {
      position: relative;
      cursor: help;
    }

    .tooltip::after {
      content: attr(data-tooltip);
      position: absolute;
      bottom: 100%;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      white-space: nowrap;
      opacity: 0;
      pointer-events: none;
      transition: opacity 0.2s ease;
      z-index: 1000;
    }

    .tooltip:hover::after {
      opacity: 1;
    }

    /* 滚动条样式 */
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    ::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }

    /* 表格头部固定优化 */
    .table-container {
      position: relative;
      overflow: auto;
      max-height: calc(100vh - 300px);
    }

    .table-container table.soc-table thead th {
      position: sticky;
      top: 0;
      z-index: 10;
      background: #EEEEEE;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  </style>
</head>

<body>
  <!-- 导航栏 -->
  <nav class="soc-navbar">
    <div class="soc-navbar-logo">SOC应答系统</div>
    <div class="soc-navbar-menu">
      <div>应答管理</div>
      <div>文档库</div>
      <div>历史记录</div>
    </div>
    <div class="soc-navbar-search">
      <input type="text" placeholder="搜索..." />
    </div>
    <div class="soc-navbar-user">
      <span>管理员</span>
      <div class="soc-navbar-avatar"></div>
    </div>
  </nav>

  <div class="soc-layout">
    <!-- 任务区/侧边栏 -->
    <aside class="soc-sidebar" id="socSidebar">
      <div class="soc-header" style="display:flex;align-items:center;justify-content:space-between;">
        <span>任务区</span>
        <button id="taskManageBtn"
          style="background:#e6f7ff;color:#1765d5;border:1px solid #91d5ff;border-radius:4px;padding:2px 10px;cursor:pointer;font-size:13px;line-height:1;">📋
          任务管理</button>
      </div>
      <div style="padding:0;">
        <div id="taskTabBar" style="display:flex;">
          <button class="soc-tab-btn" data-tab="todo">待办任务</button>
          <button class="soc-tab-btn" data-tab="history">历史归档</button>
          <button class="soc-tab-btn" data-tab="template">模板管理</button>
        </div>
        <div id="taskTabContent" style="padding:12px 8px 0 8px;"></div>
      </div>
      <button class="soc-hide-btn" title="收起侧边栏" id="hideSidebarBtn">&lt;</button>
    </aside>

    <!-- 内容主区 -->
    <main class="soc-content" id="socContent">
      <div class="soc-header">SOC应答任务</div>
      <div class="soc-main">
        <!-- 表格卡片 -->
        <div id="answerTableCard">
          <!-- 工具栏 -->
          <div class="soc-toolbar">
            <!-- 操作按钮区 -->
            <div style="display:flex;gap:10px;flex-wrap:wrap;">
              <button id="importExcelBtn" class="soc-btn soc-btn-blue">📥 导入</button>
              <button id="addSingleEntryBtn" class="soc-btn soc-btn-2">➕ 新增单条</button>
              <input type="file" id="importExcelInput" accept=".xlsx,.xls" style="display:none;" />
              <button id="exportBtn" class="soc-btn soc-btn-2">📤 导出</button>
              <button id="batchReAnswerBtn" class="soc-btn soc-btn-2">🤖 开始应答</button>
              <div style="position:relative;">
                <button id="mainActionBtn" class="soc-btn soc-btn-3">更多 ▾</button>
                <div id="mainActionMenu"
                  style="display:none;position:absolute;left:0;top:36px;z-index:20;background:#fff;border:1px solid #ececec;box-shadow:0 2px 8px #eee;border-radius:6px;min-width:160px;overflow:hidden;">
                  <div id="exportSettingBtn" class="main-action-item" data-act="exportSetting" tabindex="0">⚙️ 导出设置</div>
                  <div id="openParamSettingBtn" class="main-action-item" data-act="openParamSetting" tabindex="0">⚙️ 参数设置</div>
                  <div id="openReferenceDocBtn" class="main-action-item" data-act="openReferenceDoc" tabindex="0">📑 参考文档</div>
                  <div id="priorityConfigBtn" class="main-action-item" data-act="priorityConfig" tabindex="0">🔧 优先级配置</div>
                  <div id="satisfactionCalcBtn" class="main-action-item" data-act="satisfactionCalc" tabindex="0">📊 满足度计算</div>
                  <div id="viewSimilarBtn" class="main-action-item" data-act="viewSimilar" tabindex="0">🔍 查看相似条目</div>
                  <div id="historyRecordBtn" class="main-action-item" data-act="historyRecord" tabindex="0">🕓 历史记录</div>
                  <div style="border-top:1px solid #f0f0f0;margin:4px 0;"></div>
                  <div id="clearAllBtn" class="main-action-item" data-act="clearAll" tabindex="0" style="color:#ff4d4f;">🗑️ 清空全部条目</div>
                </div>
              </div>
              <button id="batchDeleteBtn" class="soc-btn soc-btn-4">🗑️ 删除</button>
            </div>
          </div>

          <!-- 状态提示区 -->
          <div class="status-tips">
            <span id="similarTip" style="color:#faad14;display:none;">⚠️ 发现<span id="similarCount"></span>组相似条目，<a href="#" id="showSimilarBtn">查看</a></span>
            <span id="productFilterTip" style="color:#1765d5;display:none;">🔍 当前筛选产品：<span id="currentFilterProduct"></span></span>
            <span id="loadingTip" style="color:#1890ff;display:none;"><span class="loading-spinner"></span>正在加载数据...</span>
            <span id="errorTip" style="color:#ff4d4f;display:none;">❌ 数据加载失败</span>
          </div>

          <!-- 表格容器 -->
          <div class="table-container">
            <table class="soc-table" id="answerTable">
              <thead>
                <tr>
                  <th style="width:40px;"><input type="checkbox" id="selectAllRow" /></th>
                  <th style="width:80px;">
                    编号
                    <div style="margin-top:4px;">
                      <input id="searchNo" type="text" placeholder="筛选"
                        style="width:100%;height:24px;border:1px solid #d9d9d9;border-radius:3px;padding:2px 6px;font-size:12px;" />
                    </div>
                  </th>
                  <th style="min-width:200px;">
                    条目描述
                    <div style="margin-top:4px;">
                      <input id="searchDesc" type="text" placeholder="筛选"
                        style="width:100%;height:24px;border:1px solid #d9d9d9;border-radius:3px;padding:2px 6px;font-size:12px;" />
                    </div>
                  </th>
                  <th style="min-width:80px;">
                    产品
                    <div style="margin-top:4px;">
                      <select id="searchProduct"
                        style="width:100%;height:28px;border:1px solid #d9d9d9;border-radius:3px;padding:2px 6px;font-size:12px;">
                        <option value="">全部</option>
                        <option value="5GC">5GC</option>
                        <option value="VoLTE">VoLTE</option>
                        <option value="IMS">IMS</option>
                      </select>
                    </div>
                  </th>
                  <th style="min-width:60px;">
                    方式
                    <div style="margin-top:4px;">
                      <select id="searchAnswerType"
                        style="width:100%;height:28px;border:1px solid #d9d9d9;border-radius:3px;padding:2px 6px;font-size:12px;">
                        <option value="">全部</option>
                        <option value="AI">AI</option>
                        <option value="人工">人工</option>
                      </select>
                    </div>
                  </th>
                  <th style="min-width:80px;">
                    应答
                    <div style="margin-top:4px;">
                      <select id="searchAnswer"
                        style="width:100%;height:28px;border:1px solid #d9d9d9;border-radius:3px;padding:2px 6px;font-size:12px;">
                        <option value="">全部</option>
                        <option value="FC">FC</option>
                        <option value="PC">PC</option>
                        <option value="NC">NC</option>
                        <option value="N/A">N/A</option>
                        <option value="未应答">未应答</option>
                      </select>
                    </div>
                  </th>
                  <th style="min-width:200px;">
                    应答说明
                    <div style="margin-top:4px;">
                      <input type="text" id="searchExplain" placeholder="应答说明"
                        style="width:100%;height:24px;border:1px solid #d9d9d9;border-radius:3px;padding:2px 6px;font-size:12px;" />
                    </div>
                  </th>
                  <th style="min-width:120px;">索引</th>
                  <th style="min-width:100px;">
                    备注
                    <div style="margin-top:4px;">
                      <input type="text" id="searchRemark" placeholder="备注"
                        style="width:100%;height:24px;border:1px solid #d9d9d9;border-radius:3px;padding:2px 6px;font-size:12px;" />
                    </div>
                  </th>
                  <th style="min-width:120px;">操作</th>
                </tr>
              </thead>
              <tbody id="answerTableBody"></tbody>
            </table>
          </div>

          <!-- 分页条 -->
          <div id="tablePagination">
            <div style="display:flex;align-items:center;gap:8px;">
              <span>每页</span>
              <select id="pageSizeSelect">
                <option value="10">10</option>
                <option value="20" selected>20</option>
                <option value="50">50</option>
                <option value="100">100</option>
              </select>
              <span>条</span>
            </div>
            <div style="flex:1;text-align:center;">
              <span id="pageInfo"></span>
            </div>
            <div style="display:flex;align-items:center;gap:8px;">
              <button id="prevPageBtn">上一页</button>
              <button id="nextPageBtn">下一页</button>
            </div>
          </div>
        </div>
        
        <!-- 详情弹窗容器 -->
        <div id="answerDetailDialog" style="display:none;"></div>
      </div>
    </main>

    <!-- 对话区/右侧栏 -->
    <aside class="soc-chatbar" id="socChatbar">
      <div class="soc-header">投标Agent</div>
      <div style="padding:0;display:flex;flex-direction:column;height:calc(100% - 48px);position:relative;">
        <!-- 对话历史区 -->
        <div style="flex:1;display:flex;flex-direction:column;overflow:hidden;">
          <div style="padding:8px 12px 0 12px;flex:1;overflow:auto;" id="luiHistoryWrap">
            <div id="luiHistory" style="font-size:13px;line-height:1.7;display:flex;flex-direction:column;gap:8px;">
            </div>
          </div>
        </div>
        
        <!-- 快捷指令栏 -->
        <div style="padding:8px 12px 0 12px; min-height:60px;">
          <div id="quickCmdHeader"
            style="font-weight:500;margin-bottom:4px;cursor:pointer;display:flex;justify-content:space-between;align-items:center;user-select:none;background:#fff;position:relative;">
            <span>快捷指令</span>
            <span id="quickCmdToggle" style="transition: transform 0.2s; display:inline-block;">▼</span>
          </div>
          <div id="quickCmdBar" style="display:flex;flex-wrap:wrap;gap:6px 8px;transition: all 0.3s ease;"></div>
        </div>
        
        <!-- LUI输入区，固定底部 -->
        <form id="luiForm"
          style="display:flex;gap:4px;padding:10px 12px 10px 12px;background:#fff;box-shadow:0 -2px 8px #f0f0f0;position:absolute;left:0;right:0;bottom:0;">
          <input id="luiInput" type="text"
            placeholder="请输入自然语言描述，如'帮我设置项目参数：产品5GC和VoLTE，国家泰国，运营商AIS'或'上传招标文件.pdf，再分析5GC安全要求'"
            style="flex:1;padding:8px 12px;border-radius:8px;border:1px solid #d9d9d9;font-size:14px;outline:none;transition:border .2s;" />
          <button type="submit"
            style="background:#1765d5;color:#fff;border:none;border-radius:8px;padding:8px 16px;cursor:pointer;font-size:14px;">发送</button>
        </form>

        <!-- 智能提示模板区域 -->
        <div id="smartPromptArea"
          style="position:absolute;left:0;right:0;bottom:60px;background:#fff;border-top:1px solid #f0f0f0;padding:8px 12px;display:none;">
          <div style="font-size:12px;color: #666;margin-bottom:6px;">💡 智能推荐：</div>
          <div id="smartPrompts" style="display:flex;flex-wrap:wrap;gap:6px;"></div>
        </div>
      </div>
      <button class="soc-hide-btn" title="收起对话区" id="hideChatbarBtn">&gt;</button>
    </aside>
  </div>

  <!-- 大纲浮动面板 -->
  <div id="outline-float" style="position:fixed;top:80px;left:0;z-index:9999;transition:all .2s;">
    <div id="outline-panel"
      style="width:260px;background:#fff;border-radius:0 12px 12px 0;box-shadow:2px 0 12px #eee;padding:12px 8px 12px 0;overflow-y:auto;max-height:80vh;display:block;">
      <div style="display:flex;align-items:center;justify-content:space-between;margin-bottom:8px;padding-left:8px;">
        <span style="font-weight:600;font-size:16px;color:#333;">大纲</span>
        <button id="outline-collapse"
          style="background:none;border:none;font-size:18px;cursor:pointer;color:#666;padding:4px;">&laquo;</button>
      </div>
      <div id="outline-tree" style="padding-left:8px;"></div>
    </div>
    <button id="outline-expand"
      style="display:none;width:36px;height:36px;border-radius:50%;background:#1765d5;color:#fff;border:none;box-shadow:0 2px 8px #eee;cursor:pointer;font-size:18px;">&#9776;</button>
  </div>

  <script>
    // ====== 全局变量和配置 ======
    let tablePage = { page: 1, pageSize: 20 };
    let currentFilters = {};
    let isLoading = false;
    let modalStack = []; // 弹窗栈管理

    // ====== 工具函数 ======
    
    // 安全的localStorage操作
    function safeLocalStorage() {
      return {
        getItem: function(key) {
          try {
            return localStorage.getItem(key);
          } catch (e) {
            console.error('localStorage getItem error:', e);
            return null;
          }
        },
        setItem: function(key, value) {
          try {
            localStorage.setItem(key, value);
            return true;
          } catch (e) {
            console.error('localStorage setItem error:', e);
            return false;
          }
        },
        removeItem: function(key) {
          try {
            localStorage.removeItem(key);
            return true;
          } catch (e) {
            console.error('localStorage removeItem error:', e);
            return false;
          }
        }
      };
    }

    const storage = safeLocalStorage();

    // 防抖函数
    function debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }

    // 节流函数
    function throttle(func, limit) {
      let inThrottle;
      return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
          func.apply(context, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      }
    }

    // 显示错误消息
    function showError(message, duration = 3000) {
      const errorTip = document.getElementById('errorTip');
      if (errorTip) {
        errorTip.textContent = message;
        errorTip.style.display = 'inline';
        setTimeout(() => {
          errorTip.style.display = 'none';
        }, duration);
      }
    }

    // 显示成功消息
    function showSuccess(message, duration = 3000) {
      // 可以实现一个toast组件
      console.log('Success:', message);
    }

    // 显示加载状态
    function showLoading(show = true) {
      const loadingTip = document.getElementById('loadingTip');
      if (loadingTip) {
        loadingTip.style.display = show ? 'inline' : 'none';
      }
      isLoading = show;
    }

    // ====== 数据管理 ======
    
    // 获取应答列表数据
    function getAnswerList() {
      try {
        const data = storage.getItem('socAnswerList');
        return data ? JSON.parse(data) : getDefaultAnswerList();
      } catch (e) {
        console.error('获取应答列表失败:', e);
        return getDefaultAnswerList();
      }
    }

    // 保存应答列表数据
    function saveAnswerList(list) {
      try {
        const success = storage.setItem('socAnswerList', JSON.stringify(list));
        if (!success) {
          showError('保存数据失败，请检查存储空间');
        }
        return success;
      } catch (e) {
        console.error('保存应答列表失败:', e);
        showError('保存数据失败');
        return false;
      }
    }

    // 获取默认数据
    function getDefaultAnswerList() {
      return [
        {
          no: '1.1',
          desc: '系统应支持5G核心网的基本功能',
          products: ['5GC'],
          productData: {
            '5GC': {
              answer: 'FC',
              explain: '我们的5GC产品完全支持5G核心网的所有基本功能，包括用户面和控制面的处理。',
              supplement: '',
              remark: '核心功能',
              index: '5GC技术规范-1.1',
              source: '项目文档',
              answerType: 'AI'
            }
          }
        },
        {
          no: '1.2',
          desc: 'VoLTE语音通话质量要求',
          products: ['VoLTE'],
          productData: {
            'VoLTE': {
              answer: 'PC',
              explain: 'VoLTE语音通话质量在大部分场景下满足要求，但在高负载情况下可能需要优化。',
              supplement: '',
              remark: '需要优化',
              index: 'VoLTE测试报告-2.3',
              source: '测试文档',
              answerType: 'AI'
            }
          }
        }
      ];
    }

    // ====== 表格渲染 ======
    
    // 渲染表格
    function renderAnswerTable() {
      showLoading(true);
      
      try {
        const list = getAnswerList();
        const filteredList = applyFilters(list);
        const paginatedList = applyPagination(filteredList);
        
        const tbody = document.getElementById('answerTableBody');
        if (!tbody) {
          showError('表格容器未找到');
          return;
        }

        // 清空现有内容
        tbody.innerHTML = '';

        if (paginatedList.length === 0) {
          tbody.innerHTML = '<tr><td colspan="10" style="text-align:center;padding:40px;color:#999;">暂无数据</td></tr>';
          updatePaginationInfo(0, filteredList.length);
          showLoading(false);
          return;
        }

        // 渲染数据行
        paginatedList.forEach((row, index) => {
          const actualIndex = (tablePage.page - 1) * tablePage.pageSize + index;
          const tr = document.createElement('tr');
          tr.id = `outline-item-${actualIndex}`;
          
          tr.innerHTML = `
            <td><input type="checkbox" class="rowCheck" data-idx="${actualIndex}" /></td>
            <td>${row.no || actualIndex + 1}</td>
            <td title="${row.desc || ''}">${truncateText(row.desc || '', 50)}</td>
            <td>${(row.products || []).join(', ')}</td>
            <td>${getAnswerTypeDisplay(row)}</td>
            <td>${getAnswerDisplay(row)}</td>
            <td>${getExplainDisplay(row)}</td>
            <td>${getIndexDisplay(row)}</td>
            <td>${getRemarkDisplay(row)}</td>
            <td>
              <div class="action-col">
                <button class="soc-btn soc-btn-blue" onclick="showAnswerDetail(${actualIndex})">详情</button>
                <button class="soc-btn soc-btn-red" onclick="confirmDeleteItem(${actualIndex})">删除</button>
              </div>
            </td>
          `;
          
          tbody.appendChild(tr);
        });

        // 更新分页信息
        updatePaginationInfo(paginatedList.length, filteredList.length);
        
        // 绑定事件
        bindTableEvents();
        
        showLoading(false);
        
      } catch (e) {
        console.error('渲染表格失败:', e);
        showError('表格渲染失败');
        showLoading(false);
      }
    }

    // 应用筛选条件
    function applyFilters(list) {
      return list.filter(row => {
        // 编号筛选
        if (currentFilters.no && !String(row.no || '').includes(currentFilters.no)) {
          return false;
        }
        
        // 描述筛选
        if (currentFilters.desc && !(row.desc || '').toLowerCase().includes(currentFilters.desc.toLowerCase())) {
          return false;
        }
        
        // 产品筛选
        if (currentFilters.product && !(row.products || []).includes(currentFilters.product)) {
          return false;
        }
        
        // 应答类型筛选
        if (currentFilters.answerType) {
          const hasType = (row.products || []).some(product => {
            const productData = row.productData?.[product];
            return productData?.answerType === currentFilters.answerType;
          });
          if (!hasType) return false;
        }
        
        // 应答状态筛选
        if (currentFilters.answer) {
          if (currentFilters.answer === '未应答') {
            const hasUnanswered = (row.products || []).some(product => {
              const productData = row.productData?.[product];
              return !productData?.answer;
            });
            if (!hasUnanswered) return false;
          } else {
            const hasAnswer = (row.products || []).some(product => {
              const productData = row.productData?.[product];
              return productData?.answer === currentFilters.answer;
            });
            if (!hasAnswer) return false;
          }
        }
        
        // 应答说明筛选
        if (currentFilters.explain) {
          const hasExplain = (row.products || []).some(product => {
            const productData = row.productData?.[product];
            return (productData?.explain || '').toLowerCase().includes(currentFilters.explain.toLowerCase());
          });
          if (!hasExplain) return false;
        }
        
        // 备注筛选
        if (currentFilters.remark) {
          const hasRemark = (row.products || []).some(product => {
            const productData = row.productData?.[product];
            return (productData?.remark || '').toLowerCase().includes(currentFilters.remark.toLowerCase());
          });
          if (!hasRemark) return false;
        }
        
        return true;
      });
    }

    // 应用分页
    function applyPagination(list) {
      const start = (tablePage.page - 1) * tablePage.pageSize;
      const end = start + tablePage.pageSize;
      return list.slice(start, end);
    }

    // 更新分页信息
    function updatePaginationInfo(currentPageCount, totalCount) {
      const pageInfo = document.getElementById('pageInfo');
      const prevBtn = document.getElementById('prevPageBtn');
      const nextBtn = document.getElementById('nextPageBtn');
      
      if (pageInfo) {
        const totalPages = Math.ceil(totalCount / tablePage.pageSize);
        pageInfo.textContent = `第 ${tablePage.page} 页，共 ${totalPages} 页，总计 ${totalCount} 条`;
      }
      
      if (prevBtn) {
        prevBtn.disabled = tablePage.page <= 1;
      }
      
      if (nextBtn) {
        const totalPages = Math.ceil(totalCount / tablePage.pageSize);
        nextBtn.disabled = tablePage.page >= totalPages;
      }
    }

    // 辅助函数：截断文本
    function truncateText(text, maxLength) {
      if (!text) return '';
      return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }

    // 辅助函数：获取应答类型显示
    function getAnswerTypeDisplay(row) {
      const types = new Set();
      (row.products || []).forEach(product => {
        const productData = row.productData?.[product];
        if (productData?.answerType) {
          types.add(productData.answerType);
        }
      });
      return Array.from(types).map(type => {
        const icon = type === 'AI' ? '🤖' : '👤';
        return `<span class="status-tag" style="background:#e6f7ff;color:#1765d5;">${icon} ${type}</span>`;
      }).join(' ');
    }

    // 辅助函数：获取应答状态显示
    function getAnswerDisplay(row) {
      const answers = new Set();
      (row.products || []).forEach(product => {
        const productData = row.productData?.[product];
        const answer = productData?.answer || '未应答';
        answers.add(answer);
      });
      return Array.from(answers).map(answer => {
        const answerLower = answer.toLowerCase();
        let className = 'status-tag ';
        let icon = '';

        switch (answerLower) {
          case 'fc':
            className += 'fc';
            icon = '✅';
            break;
          case 'pc':
            className += 'pc';
            icon = '⚠️';
            break;
          case 'nc':
            className += 'nc';
            icon = '❌';
            break;
          case 'n/a':
            className += 'na';
            icon = '➖';
            break;
          default:
            className += 'unanswered';
            icon = '❓';
            break;
        }

        return `<span class="${className}">${icon} ${answer}</span>`;
      }).join(' ');
    }

    // 辅助函数：获取应答说明显示
    function getExplainDisplay(row) {
      const explains = [];
      (row.products || []).forEach(product => {
        const productData = row.productData?.[product];
        if (productData?.explain) {
          explains.push(truncateText(productData.explain, 30));
        }
      });
      return explains.join('; ') || '暂无说明';
    }

    // 辅助函数：获取索引显示
    function getIndexDisplay(row) {
      const indexes = [];
      (row.products || []).forEach(product => {
        const productData = row.productData?.[product];
        if (productData?.index) {
          indexes.push(productData.index);
        }
      });
      return indexes.join('; ') || '暂无索引';
    }

    // 辅助函数：获取备注显示
    function getRemarkDisplay(row) {
      const remarks = [];
      (row.products || []).forEach(product => {
        const productData = row.productData?.[product];
        if (productData?.remark) {
          remarks.push(truncateText(productData.remark, 20));
        }
      });
      return remarks.join('; ') || '';
    }

    // ====== 事件绑定 ======
    
    // 绑定表格事件
    function bindTableEvents() {
      // 全选/取消全选
      const selectAllCheckbox = document.getElementById('selectAllRow');
      if (selectAllCheckbox) {
        selectAllCheckbox.onchange = function() {
          const checkboxes = document.querySelectorAll('.rowCheck');
          checkboxes.forEach(cb => cb.checked = this.checked);
        };
      }

      // 行选择事件
      document.querySelectorAll('.rowCheck').forEach(cb => {
        cb.onchange = function() {
          const allCheckboxes = document.querySelectorAll('.rowCheck');
          const checkedCount = document.querySelectorAll('.rowCheck:checked').length;
          const selectAllCheckbox = document.getElementById('selectAllRow');
          
          if (selectAllCheckbox) {
            selectAllCheckbox.checked = checkedCount === allCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < allCheckboxes.length;
          }
        };
      });
    }

    // 绑定筛选事件
    function bindFilterEvents() {
      const filterInputs = [
        { id: 'searchNo', key: 'no' },
        { id: 'searchDesc', key: 'desc' },
        { id: 'searchProduct', key: 'product' },
        { id: 'searchAnswerType', key: 'answerType' },
        { id: 'searchAnswer', key: 'answer' },
        { id: 'searchExplain', key: 'explain' },
        { id: 'searchRemark', key: 'remark' }
      ];

      filterInputs.forEach(({ id, key }) => {
        const element = document.getElementById(id);
        if (element) {
          const handler = debounce((e) => {
            currentFilters[key] = e.target.value.trim();
            tablePage.page = 1; // 重置到第一页
            renderAnswerTable();
          }, 300);
          
          element.addEventListener('input', handler);
          element.addEventListener('change', handler);
        }
      });
    }

    // 绑定分页事件
    function bindPaginationEvents() {
      const prevBtn = document.getElementById('prevPageBtn');
      const nextBtn = document.getElementById('nextPageBtn');
      const pageSizeSelect = document.getElementById('pageSizeSelect');

      if (prevBtn) {
        prevBtn.onclick = function() {
          if (tablePage.page > 1) {
            tablePage.page--;
            renderAnswerTable();
          }
        };
      }

      if (nextBtn) {
        nextBtn.onclick = function() {
          const list = getAnswerList();
          const filteredList = applyFilters(list);
          const totalPages = Math.ceil(filteredList.length / tablePage.pageSize);
          
          if (tablePage.page < totalPages) {
            tablePage.page++;
            renderAnswerTable();
          }
        };
      }

      if (pageSizeSelect) {
        pageSizeSelect.onchange = function() {
          tablePage.pageSize = parseInt(this.value);
          tablePage.page = 1; // 重置到第一页
          renderAnswerTable();
        };
      }
    }

    // ====== 参数设置功能 ======

    // 显示参数设置弹窗
    function showParamSettingDialog() {
      try {
        const modalHtml = createParamSettingModalHtml();

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = modalHtml;

        document.body.appendChild(modal);
        modalStack.push(modal);

        bindParamSettingEvents(modal);

      } catch (e) {
        console.error('显示参数设置失败:', e);
        showError('显示参数设置失败');
      }
    }

    // 创建参数设置弹窗HTML
    function createParamSettingModalHtml() {
      const currentParams = getCurrentParams();

      return `
        <div class="modal-content" style="width:600px;max-width:95vw;padding:0;">
          <div style="padding:20px 24px;border-bottom:1px solid #e8e8e8;display:flex;justify-content:space-between;align-items:center;">
            <h3 style="margin:0;font-size:18px;color:#333;">参数设置</h3>
            <button class="close-modal" style="background:none;border:none;font-size:24px;cursor:pointer;color:#999;">&times;</button>
          </div>

          <div style="padding:20px 24px;max-height:70vh;overflow-y:auto;">
            <!-- 产品选择 -->
            <div style="margin-bottom:20px;">
              <label style="font-weight:500;margin-bottom:8px;display:block;">产品选择 <span style="color:#ff4d4f;">*</span></label>
              <div id="productCheckboxes" style="display:flex;flex-wrap:wrap;gap:12px;">
                <label style="display:flex;align-items:center;gap:6px;cursor:pointer;">
                  <input type="checkbox" value="5GC" ${currentParams.products.includes('5GC') ? 'checked' : ''} />
                  <span>5GC</span>
                </label>
                <label style="display:flex;align-items:center;gap:6px;cursor:pointer;">
                  <input type="checkbox" value="VoLTE" ${currentParams.products.includes('VoLTE') ? 'checked' : ''} />
                  <span>VoLTE</span>
                </label>
                <label style="display:flex;align-items:center;gap:6px;cursor:pointer;">
                  <input type="checkbox" value="IMS" ${currentParams.products.includes('IMS') ? 'checked' : ''} />
                  <span>IMS</span>
                </label>
                <label style="display:flex;align-items:center;gap:6px;cursor:pointer;">
                  <input type="checkbox" value="核心网" ${currentParams.products.includes('核心网') ? 'checked' : ''} />
                  <span>核心网</span>
                </label>
                <label style="display:flex;align-items:center;gap:6px;cursor:pointer;">
                  <input type="checkbox" value="接入网" ${currentParams.products.includes('接入网') ? 'checked' : ''} />
                  <span>接入网</span>
                </label>
              </div>
              <div class="error-message" id="productError">请至少选择一个产品</div>
            </div>

            <!-- 国家选择 -->
            <div style="margin-bottom:20px;">
              <label style="font-weight:500;margin-bottom:8px;display:block;">国家：</label>
              <select id="countrySelect" style="width:100%;padding:8px 12px;border:1px solid #d9d9d9;border-radius:4px;">
                <option value="">请选择国家</option>
                <option value="泰国" ${currentParams.country === '泰国' ? 'selected' : ''}>泰国</option>
                <option value="越南" ${currentParams.country === '越南' ? 'selected' : ''}>越南</option>
                <option value="印尼" ${currentParams.country === '印尼' ? 'selected' : ''}>印尼</option>
                <option value="马来西亚" ${currentParams.country === '马来西亚' ? 'selected' : ''}>马来西亚</option>
                <option value="新加坡" ${currentParams.country === '新加坡' ? 'selected' : ''}>新加坡</option>
                <option value="菲律宾" ${currentParams.country === '菲律宾' ? 'selected' : ''}>菲律宾</option>
              </select>
            </div>

            <!-- 运营商选择 -->
            <div style="margin-bottom:20px;">
              <label style="font-weight:500;margin-bottom:8px;display:block;">运营商：</label>
              <select id="operatorSelect" style="width:100%;padding:8px 12px;border:1px solid #d9d9d9;border-radius:4px;">
                <option value="">请选择运营商</option>
                <option value="AIS" ${currentParams.operator === 'AIS' ? 'selected' : ''}>AIS</option>
                <option value="Viettel" ${currentParams.operator === 'Viettel' ? 'selected' : ''}>Viettel</option>
                <option value="Telkomsel" ${currentParams.operator === 'Telkomsel' ? 'selected' : ''}>Telkomsel</option>
                <option value="Maxis" ${currentParams.operator === 'Maxis' ? 'selected' : ''}>Maxis</option>
                <option value="Singtel" ${currentParams.operator === 'Singtel' ? 'selected' : ''}>Singtel</option>
                <option value="PLDT" ${currentParams.operator === 'PLDT' ? 'selected' : ''}>PLDT</option>
              </select>
            </div>

            <!-- 项目名称 -->
            <div style="margin-bottom:20px;">
              <label style="font-weight:500;margin-bottom:8px;display:block;">项目名称：</label>
              <input type="text" id="projectInput" style="width:100%;padding:8px 12px;border:1px solid #d9d9d9;border-radius:4px;" placeholder="请输入项目名称" value="${currentParams.projectName || ''}" />
            </div>

            <!-- 模板选择 -->
            <div style="margin-bottom:20px;">
              <label style="font-weight:500;margin-bottom:8px;display:block;">参数模板：</label>
              <div style="display:flex;gap:8px;">
                <select id="templateSelect" style="flex:1;padding:8px 12px;border:1px solid #d9d9d9;border-radius:4px;">
                  <option value="">选择模板</option>
                </select>
                <button id="saveTemplateBtn" class="soc-btn">保存为模板</button>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div style="display:flex;gap:8px;margin-bottom:20px;">
              <button id="loadTemplateBtn" class="soc-btn">加载模板</button>
              <button id="resetParamsBtn" class="soc-btn">重置参数</button>
              <button id="clearParamsBtn" class="soc-btn">清空参数</button>
            </div>
          </div>

          <div style="padding:16px 24px;border-top:1px solid #e8e8e8;display:flex;justify-content:flex-end;gap:12px;">
            <button id="confirmParamBtn" class="soc-btn soc-btn-blue">确定</button>
            <button class="close-modal soc-btn">取消</button>
          </div>
        </div>
      `;
    }

    // 获取当前参数
    function getCurrentParams() {
      try {
        const data = storage.getItem('currentParams');
        return data ? JSON.parse(data) : {
          products: ['5GC'],
          country: '',
          operator: '',
          projectName: ''
        };
      } catch (e) {
        console.error('获取当前参数失败:', e);
        return {
          products: ['5GC'],
          country: '',
          operator: '',
          projectName: ''
        };
      }
    }

    // 保存当前参数
    function saveCurrentParams(params) {
      try {
        return storage.setItem('currentParams', JSON.stringify(params));
      } catch (e) {
        console.error('保存当前参数失败:', e);
        return false;
      }
    }

    // 绑定参数设置事件
    function bindParamSettingEvents(modal) {
      // 关闭弹窗
      modal.querySelectorAll('.close-modal').forEach(btn => {
        btn.onclick = () => closeModal(modal);
      });

      // 点击遮罩关闭
      modal.onclick = (e) => {
        if (e.target === modal) {
          closeModal(modal);
        }
      };

      // 确定按钮
      const confirmBtn = modal.querySelector('#confirmParamBtn');
      if (confirmBtn) {
        confirmBtn.onclick = () => saveParamSettings(modal);
      }

      // 保存模板按钮
      const saveTemplateBtn = modal.querySelector('#saveTemplateBtn');
      if (saveTemplateBtn) {
        saveTemplateBtn.onclick = () => saveParamTemplate(modal);
      }

      // 加载模板按钮
      const loadTemplateBtn = modal.querySelector('#loadTemplateBtn');
      if (loadTemplateBtn) {
        loadTemplateBtn.onclick = () => loadParamTemplate(modal);
      }

      // 重置参数按钮
      const resetBtn = modal.querySelector('#resetParamsBtn');
      if (resetBtn) {
        resetBtn.onclick = () => resetParamSettings(modal);
      }

      // 清空参数按钮
      const clearBtn = modal.querySelector('#clearParamsBtn');
      if (clearBtn) {
        clearBtn.onclick = () => clearParamSettings(modal);
      }

      // 产品选择验证
      const productCheckboxes = modal.querySelectorAll('#productCheckboxes input[type="checkbox"]');
      productCheckboxes.forEach(cb => {
        cb.onchange = () => validateProductSelection(modal);
      });

      // 初始化模板列表
      loadTemplateList(modal);
    }

    // 验证产品选择
    function validateProductSelection(modal) {
      const checkboxes = modal.querySelectorAll('#productCheckboxes input[type="checkbox"]:checked');
      const errorDiv = modal.querySelector('#productError');

      if (checkboxes.length === 0) {
        errorDiv.style.display = 'block';
        return false;
      } else {
        errorDiv.style.display = 'none';
        return true;
      }
    }

    // 保存参数设置
    function saveParamSettings(modal) {
      try {
        // 验证产品选择
        if (!validateProductSelection(modal)) {
          return;
        }

        // 收集表单数据
        const products = Array.from(modal.querySelectorAll('#productCheckboxes input[type="checkbox"]:checked'))
          .map(cb => cb.value);
        const country = modal.querySelector('#countrySelect').value;
        const operator = modal.querySelector('#operatorSelect').value;
        const projectName = modal.querySelector('#projectInput').value.trim();

        const params = {
          products,
          country,
          operator,
          projectName,
          timestamp: new Date().toISOString()
        };

        // 保存参数
        if (saveCurrentParams(params)) {
          showSuccess('参数设置已保存');
          closeModal(modal);

          // 可以触发相关更新
          updateProductFilter(products);
        } else {
          showError('保存参数失败');
        }

      } catch (e) {
        console.error('保存参数设置失败:', e);
        showError('保存参数失败');
      }
    }

    // 更新产品筛选器
    function updateProductFilter(products) {
      const productSelect = document.getElementById('searchProduct');
      if (productSelect) {
        // 清空现有选项（保留"全部"）
        const allOption = productSelect.querySelector('option[value=""]');
        productSelect.innerHTML = '';
        if (allOption) {
          productSelect.appendChild(allOption);
        } else {
          productSelect.innerHTML = '<option value="">全部</option>';
        }

        // 添加新的产品选项
        products.forEach(product => {
          const option = document.createElement('option');
          option.value = product;
          option.textContent = product;
          productSelect.appendChild(option);
        });
      }
    }

    // 保存参数模板
    function saveParamTemplate(modal) {
      const templateName = prompt('请输入模板名称：');
      if (!templateName) return;

      try {
        const products = Array.from(modal.querySelectorAll('#productCheckboxes input[type="checkbox"]:checked'))
          .map(cb => cb.value);
        const country = modal.querySelector('#countrySelect').value;
        const operator = modal.querySelector('#operatorSelect').value;
        const projectName = modal.querySelector('#projectInput').value.trim();

        const template = {
          id: 't' + Date.now(),
          name: templateName,
          products,
          country,
          operator,
          projectName,
          createdAt: new Date().toISOString()
        };

        const templates = getParamTemplates();
        templates.push(template);
        saveParamTemplates(templates);

        showSuccess('模板保存成功');
        loadTemplateList(modal);

      } catch (e) {
        console.error('保存模板失败:', e);
        showError('保存模板失败');
      }
    }

    // 加载参数模板
    function loadParamTemplate(modal) {
      const templateSelect = modal.querySelector('#templateSelect');
      const templateId = templateSelect.value;

      if (!templateId) {
        showError('请先选择一个模板');
        return;
      }

      try {
        const templates = getParamTemplates();
        const template = templates.find(t => t.id === templateId);

        if (!template) {
          showError('模板不存在');
          return;
        }

        // 填充表单
        fillParamForm(modal, template);
        showSuccess('模板加载成功');

      } catch (e) {
        console.error('加载模板失败:', e);
        showError('加载模板失败');
      }
    }

    // 填充参数表单
    function fillParamForm(modal, params) {
      // 产品选择
      const productCheckboxes = modal.querySelectorAll('#productCheckboxes input[type="checkbox"]');
      productCheckboxes.forEach(cb => {
        cb.checked = (params.products || []).includes(cb.value);
      });

      // 其他字段
      const countrySelect = modal.querySelector('#countrySelect');
      const operatorSelect = modal.querySelector('#operatorSelect');
      const projectInput = modal.querySelector('#projectInput');

      if (countrySelect) countrySelect.value = params.country || '';
      if (operatorSelect) operatorSelect.value = params.operator || '';
      if (projectInput) projectInput.value = params.projectName || '';
    }

    // 重置参数设置
    function resetParamSettings(modal) {
      const defaultParams = {
        products: ['5GC'],
        country: '',
        operator: '',
        projectName: ''
      };

      fillParamForm(modal, defaultParams);
      showSuccess('参数已重置');
    }

    // 清空参数设置
    function clearParamSettings(modal) {
      const emptyParams = {
        products: [],
        country: '',
        operator: '',
        projectName: ''
      };

      fillParamForm(modal, emptyParams);
      showSuccess('参数已清空');
    }

    // 获取参数模板
    function getParamTemplates() {
      try {
        const data = storage.getItem('paramTemplates');
        return data ? JSON.parse(data) : [];
      } catch (e) {
        console.error('获取参数模板失败:', e);
        return [];
      }
    }

    // 保存参数模板
    function saveParamTemplates(templates) {
      try {
        return storage.setItem('paramTemplates', JSON.stringify(templates));
      } catch (e) {
        console.error('保存参数模板失败:', e);
        return false;
      }
    }

    // 加载模板列表
    function loadTemplateList(modal) {
      const templateSelect = modal.querySelector('#templateSelect');
      if (!templateSelect) return;

      try {
        const templates = getParamTemplates();

        // 清空现有选项
        templateSelect.innerHTML = '<option value="">选择模板</option>';

        // 添加模板选项
        templates.forEach(template => {
          const option = document.createElement('option');
          option.value = template.id;
          option.textContent = template.name;
          templateSelect.appendChild(option);
        });

      } catch (e) {
        console.error('加载模板列表失败:', e);
      }
    }

    // ====== 初始化 ======

    // 页面初始化
    function initPage() {
      try {
        // 绑定事件
        bindFilterEvents();
        bindPaginationEvents();
        bindMainActionEvents();

        // 渲染表格
        renderAnswerTable();

        // 初始化其他组件
        initSidebar();
        initChatbar();

        console.log('页面初始化完成');
      } catch (e) {
        console.error('页面初始化失败:', e);
        showError('页面初始化失败');
      }
    }

    // ====== 导入导出功能 ======

    // 导入Excel文件
    function importExcelFile() {
      const input = document.getElementById('importExcelInput');
      if (input) {
        input.click();
      }
    }

    // 处理文件导入
    function handleFileImport(event) {
      const file = event.target.files[0];
      if (!file) return;

      // 验证文件类型
      const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
      ];

      if (!allowedTypes.includes(file.type)) {
        showError('请选择Excel文件（.xlsx或.xls）');
        return;
      }

      // 显示处理中状态
      showLoading(true);

      // 模拟文件处理
      setTimeout(() => {
        try {
          // 模拟导入数据
          const mockImportData = [
            {
              no: '2.1',
              desc: '导入的条目示例1：系统安全性要求',
              products: ['5GC', 'VoLTE'],
              productData: {
                '5GC': {
                  answer: 'FC',
                  explain: '5GC产品完全满足安全性要求',
                  supplement: '',
                  remark: '导入数据',
                  index: '安全规范-2.1',
                  source: '导入文档',
                  answerType: 'AI'
                },
                'VoLTE': {
                  answer: 'PC',
                  explain: 'VoLTE产品部分满足安全性要求',
                  supplement: '',
                  remark: '导入数据',
                  index: '安全规范-2.1',
                  source: '导入文档',
                  answerType: 'AI'
                }
              }
            },
            {
              no: '2.2',
              desc: '导入的条目示例2：性能指标要求',
              products: ['5GC'],
              productData: {
                '5GC': {
                  answer: 'FC',
                  explain: '5GC产品性能指标完全满足要求',
                  supplement: '',
                  remark: '导入数据',
                  index: '性能规范-2.2',
                  source: '导入文档',
                  answerType: 'AI'
                }
              }
            }
          ];

          // 合并到现有数据
          const existingList = getAnswerList();
          const newList = [...existingList, ...mockImportData];

          if (saveAnswerList(newList)) {
            showSuccess(`成功导入 ${mockImportData.length} 条数据`);
            renderAnswerTable();
          } else {
            showError('导入失败：保存数据时出错');
          }

        } catch (e) {
          console.error('导入文件失败:', e);
          showError('导入文件失败');
        } finally {
          showLoading(false);
          // 清空文件输入
          event.target.value = '';
        }
      }, 2000);
    }

    // 导出数据
    function exportData() {
      try {
        const list = getAnswerList();
        if (list.length === 0) {
          showError('没有数据可以导出');
          return;
        }

        // 模拟导出功能
        showSuccess(`成功导出 ${list.length} 条数据（模拟）`);

        // 实际实现中，这里会生成Excel文件并下载
        console.log('导出数据:', list);

      } catch (e) {
        console.error('导出数据失败:', e);
        showError('导出数据失败');
      }
    }

    // ====== 新增条目功能 ======

    // 显示新增条目弹窗
    function showAddEntryDialog() {
      try {
        const modalHtml = createAddEntryModalHtml();

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = modalHtml;

        document.body.appendChild(modal);
        modalStack.push(modal);

        bindAddEntryEvents(modal);

      } catch (e) {
        console.error('显示新增条目弹窗失败:', e);
        showError('显示新增条目弹窗失败');
      }
    }

    // 创建新增条目弹窗HTML
    function createAddEntryModalHtml() {
      const currentParams = getCurrentParams();

      return `
        <div class="modal-content" style="width:600px;max-width:95vw;padding:0;">
          <div style="padding:20px 24px;border-bottom:1px solid #e8e8e8;display:flex;justify-content:space-between;align-items:center;">
            <h3 style="margin:0;font-size:18px;color:#333;">新增条目</h3>
            <button class="close-modal" style="background:none;border:none;font-size:24px;cursor:pointer;color:#999;">&times;</button>
          </div>

          <div style="padding:20px 24px;max-height:70vh;overflow-y:auto;">
            <!-- 条目编号 -->
            <div style="margin-bottom:20px;">
              <label style="font-weight:500;margin-bottom:8px;display:block;">条目编号 <span style="color:#ff4d4f;">*</span></label>
              <input type="text" id="entryNo" style="width:100%;padding:8px 12px;border:1px solid #d9d9d9;border-radius:4px;" placeholder="如：3.1" />
              <div class="error-message" id="noError">请输入条目编号</div>
            </div>

            <!-- 条目描述 -->
            <div style="margin-bottom:20px;">
              <label style="font-weight:500;margin-bottom:8px;display:block;">条目描述 <span style="color:#ff4d4f;">*</span></label>
              <textarea id="entryDesc" style="width:100%;height:80px;padding:8px 12px;border:1px solid #d9d9d9;border-radius:4px;resize:vertical;font-family:inherit;" placeholder="请输入条目描述..."></textarea>
              <div class="error-message" id="descError">请输入条目描述</div>
            </div>

            <!-- 关联产品 -->
            <div style="margin-bottom:20px;">
              <label style="font-weight:500;margin-bottom:8px;display:block;">关联产品 <span style="color:#ff4d4f;">*</span></label>
              <div id="entryProductCheckboxes" style="display:flex;flex-wrap:wrap;gap:12px;">
                ${(currentParams.products.length > 0 ? currentParams.products : ['5GC', 'VoLTE', 'IMS']).map(product => `
                  <label style="display:flex;align-items:center;gap:6px;cursor:pointer;">
                    <input type="checkbox" value="${product}" />
                    <span>${product}</span>
                  </label>
                `).join('')}
              </div>
              <div class="error-message" id="productError">请至少选择一个产品</div>
            </div>

            <!-- 初始应答（可选） -->
            <div style="margin-bottom:20px;">
              <label style="font-weight:500;margin-bottom:8px;display:block;">初始应答（可选）</label>
              <select id="entryAnswer" style="width:100%;padding:8px 12px;border:1px solid #d9d9d9;border-radius:4px;">
                <option value="">暂不设置</option>
                <option value="FC">FC - 完全满足</option>
                <option value="PC">PC - 部分满足</option>
                <option value="NC">NC - 不满足</option>
                <option value="N/A">N/A - 不适用</option>
              </select>
            </div>

            <!-- 应答说明（可选） -->
            <div style="margin-bottom:20px;">
              <label style="font-weight:500;margin-bottom:8px;display:block;">应答说明（可选）</label>
              <textarea id="entryExplain" style="width:100%;height:60px;padding:8px 12px;border:1px solid #d9d9d9;border-radius:4px;resize:vertical;font-family:inherit;" placeholder="可输入应答说明..."></textarea>
            </div>

            <!-- 备注（可选） -->
            <div style="margin-bottom:20px;">
              <label style="font-weight:500;margin-bottom:8px;display:block;">备注（可选）</label>
              <input type="text" id="entryRemark" style="width:100%;padding:8px 12px;border:1px solid #d9d9d9;border-radius:4px;" placeholder="可添加备注信息" />
            </div>
          </div>

          <div style="padding:16px 24px;border-top:1px solid #e8e8e8;display:flex;justify-content:flex-end;gap:12px;">
            <button id="saveEntryBtn" class="soc-btn soc-btn-blue">保存</button>
            <button id="saveAndAddBtn" class="soc-btn soc-btn-green">保存并继续添加</button>
            <button class="close-modal soc-btn">取消</button>
          </div>
        </div>
      `;
    }

    // 绑定新增条目事件
    function bindAddEntryEvents(modal) {
      // 关闭弹窗
      modal.querySelectorAll('.close-modal').forEach(btn => {
        btn.onclick = () => closeModal(modal);
      });

      // 点击遮罩关闭
      modal.onclick = (e) => {
        if (e.target === modal) {
          closeModal(modal);
        }
      };

      // 保存按钮
      const saveBtn = modal.querySelector('#saveEntryBtn');
      if (saveBtn) {
        saveBtn.onclick = () => saveNewEntry(modal, false);
      }

      // 保存并继续添加按钮
      const saveAndAddBtn = modal.querySelector('#saveAndAddBtn');
      if (saveAndAddBtn) {
        saveAndAddBtn.onclick = () => saveNewEntry(modal, true);
      }

      // 实时验证
      const noInput = modal.querySelector('#entryNo');
      const descTextarea = modal.querySelector('#entryDesc');
      const productCheckboxes = modal.querySelectorAll('#entryProductCheckboxes input[type="checkbox"]');

      if (noInput) {
        noInput.oninput = () => validateEntryField(modal, 'no');
      }

      if (descTextarea) {
        descTextarea.oninput = () => validateEntryField(modal, 'desc');
      }

      productCheckboxes.forEach(cb => {
        cb.onchange = () => validateEntryField(modal, 'product');
      });
    }

    // 验证新增条目字段
    function validateEntryField(modal, field) {
      let isValid = true;

      switch (field) {
        case 'no':
          const noInput = modal.querySelector('#entryNo');
          const noError = modal.querySelector('#noError');
          if (!noInput.value.trim()) {
            noError.style.display = 'block';
            isValid = false;
          } else {
            noError.style.display = 'none';
          }
          break;

        case 'desc':
          const descTextarea = modal.querySelector('#entryDesc');
          const descError = modal.querySelector('#descError');
          if (!descTextarea.value.trim()) {
            descError.style.display = 'block';
            isValid = false;
          } else {
            descError.style.display = 'none';
          }
          break;

        case 'product':
          const productCheckboxes = modal.querySelectorAll('#entryProductCheckboxes input[type="checkbox"]:checked');
          const productError = modal.querySelector('#productError');
          if (productCheckboxes.length === 0) {
            productError.style.display = 'block';
            isValid = false;
          } else {
            productError.style.display = 'none';
          }
          break;
      }

      return isValid;
    }

    // 保存新条目
    function saveNewEntry(modal, continueAdding = false) {
      try {
        // 验证所有必填字段
        const isNoValid = validateEntryField(modal, 'no');
        const isDescValid = validateEntryField(modal, 'desc');
        const isProductValid = validateEntryField(modal, 'product');

        if (!isNoValid || !isDescValid || !isProductValid) {
          showError('请填写所有必填字段');
          return;
        }

        // 收集表单数据
        const no = modal.querySelector('#entryNo').value.trim();
        const desc = modal.querySelector('#entryDesc').value.trim();
        const products = Array.from(modal.querySelectorAll('#entryProductCheckboxes input[type="checkbox"]:checked'))
          .map(cb => cb.value);
        const answer = modal.querySelector('#entryAnswer').value;
        const explain = modal.querySelector('#entryExplain').value.trim();
        const remark = modal.querySelector('#entryRemark').value.trim();

        // 检查编号是否重复
        const existingList = getAnswerList();
        if (existingList.some(item => item.no === no)) {
          showError('条目编号已存在，请使用其他编号');
          return;
        }

        // 构建新条目数据
        const newEntry = {
          no,
          desc,
          products,
          productData: {}
        };

        // 为每个产品创建数据
        products.forEach(product => {
          newEntry.productData[product] = {
            answer: answer || '',
            explain: explain || '',
            supplement: '',
            remark: remark || '',
            index: '',
            source: '手动添加',
            answerType: '人工'
          };
        });

        // 保存数据
        const newList = [...existingList, newEntry];
        if (saveAnswerList(newList)) {
          showSuccess('条目添加成功');
          renderAnswerTable();

          if (continueAdding) {
            // 清空表单继续添加
            modal.querySelector('#entryNo').value = '';
            modal.querySelector('#entryDesc').value = '';
            modal.querySelector('#entryAnswer').value = '';
            modal.querySelector('#entryExplain').value = '';
            modal.querySelector('#entryRemark').value = '';
            modal.querySelectorAll('#entryProductCheckboxes input[type="checkbox"]').forEach(cb => cb.checked = false);

            // 隐藏错误信息
            modal.querySelectorAll('.error-message').forEach(err => err.style.display = 'none');
          } else {
            closeModal(modal);
          }
        } else {
          showError('保存条目失败');
        }

      } catch (e) {
        console.error('保存新条目失败:', e);
        showError('保存条目失败');
      }
    }

    // 绑定主要操作事件
    function bindMainActionEvents() {
      // 导入Excel按钮
      const importExcelBtn = document.getElementById('importExcelBtn');
      const importExcelInput = document.getElementById('importExcelInput');

      if (importExcelBtn) {
        importExcelBtn.onclick = importExcelFile;
      }

      if (importExcelInput) {
        importExcelInput.onchange = handleFileImport;
      }

      // 新增单条按钮
      const addSingleEntryBtn = document.getElementById('addSingleEntryBtn');
      if (addSingleEntryBtn) {
        addSingleEntryBtn.onclick = showAddEntryDialog;
      }

      // 导出按钮
      const exportBtn = document.getElementById('exportBtn');
      if (exportBtn) {
        exportBtn.onclick = exportData;
      }

      // 参数设置按钮
      const openParamSettingBtn = document.getElementById('openParamSettingBtn');
      if (openParamSettingBtn) {
        openParamSettingBtn.onclick = (e) => {
          e.stopPropagation();
          showParamSettingDialog();
        };
      }

      // 更多菜单切换
      const mainActionBtn = document.getElementById('mainActionBtn');
      const mainActionMenu = document.getElementById('mainActionMenu');

      if (mainActionBtn && mainActionMenu) {
        mainActionBtn.onclick = (e) => {
          e.stopPropagation();
          const isVisible = mainActionMenu.style.display === 'block';
          mainActionMenu.style.display = isVisible ? 'none' : 'block';
        };

        // 点击其他地方关闭菜单
        document.addEventListener('click', () => {
          mainActionMenu.style.display = 'none';
        });

        // 菜单项点击事件
        mainActionMenu.addEventListener('click', (e) => {
          e.stopPropagation();
          const action = e.target.dataset.act;

          switch (action) {
            case 'openParamSetting':
              showParamSettingDialog();
              break;
            case 'satisfactionCalc':
              calculateSatisfaction();
              break;
            case 'clearAll':
              confirmClearAllItems();
              break;
            // 其他操作可以在这里添加
          }

          mainActionMenu.style.display = 'none';
        });
      }

      // 批量删除按钮
      const batchDeleteBtn = document.getElementById('batchDeleteBtn');
      if (batchDeleteBtn) {
        batchDeleteBtn.onclick = () => confirmBatchDelete();
      }
    }

    // 初始化侧边栏
    function initSidebar() {
      // 侧边栏相关逻辑
    }

    // 初始化聊天栏
    function initChatbar() {
      // 聊天栏相关逻辑
    }

    // ====== 详情弹窗 ======

    // 显示应答详情
    function showAnswerDetail(idx) {
      try {
        const list = getAnswerList();
        const row = list[idx];
        if (!row) {
          showError('条目不存在');
          return;
        }

        // 创建弹窗HTML
        const modalHtml = createDetailModalHtml(row, idx);

        // 创建弹窗元素
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = modalHtml;

        // 添加到页面
        document.body.appendChild(modal);
        modalStack.push(modal);

        // 绑定弹窗事件
        bindDetailModalEvents(modal, row, idx);

      } catch (e) {
        console.error('显示详情失败:', e);
        showError('显示详情失败');
      }
    }

    // 创建详情弹窗HTML
    function createDetailModalHtml(row, idx) {
      const products = row.products || ['5GC'];
      const currentProduct = products[0]; // 默认显示第一个产品
      const productData = row.productData?.[currentProduct] || {};

      return `
        <div class="modal-content" style="width:900px;max-width:95vw;max-height:90vh;padding:0;">
          <div style="padding:20px 24px;border-bottom:1px solid #e8e8e8;display:flex;justify-content:space-between;align-items:center;">
            <h3 style="margin:0;font-size:18px;color:#333;">应答详情 - 条目 ${row.no || idx + 1}</h3>
            <button class="close-modal" style="background:none;border:none;font-size:24px;cursor:pointer;color:#999;">&times;</button>
          </div>

          <div style="padding:20px 24px;max-height:70vh;overflow-y:auto;">
            <!-- 产品选择 -->
            ${products.length > 1 ? `
            <div style="margin-bottom:20px;">
              <label style="font-weight:500;margin-bottom:8px;display:block;">选择产品：</label>
              <select id="productSelect" style="width:200px;padding:6px 12px;border:1px solid #d9d9d9;border-radius:4px;">
                ${products.map(p => `<option value="${p}" ${p === currentProduct ? 'selected' : ''}>${p}</option>`).join('')}
              </select>
            </div>
            ` : ''}

            <!-- 条目描述 -->
            <div style="margin-bottom:20px;">
              <label style="font-weight:500;margin-bottom:8px;display:block;">条目描述：</label>
              <div style="background:#f8f9fa;border:1px solid #e9ecef;border-radius:6px;padding:12px;line-height:1.6;">
                ${row.desc || '无描述'}
              </div>
            </div>

            <!-- 应答状态 -->
            <div style="margin-bottom:20px;">
              <label style="font-weight:500;margin-bottom:8px;display:block;">应答状态：</label>
              <select id="answerSelect" style="padding:6px 12px;border:1px solid #d9d9d9;border-radius:4px;min-width:150px;">
                <option value="" ${!productData.answer ? 'selected' : ''}>未应答</option>
                <option value="FC" ${productData.answer === 'FC' ? 'selected' : ''}>FC - 完全满足</option>
                <option value="PC" ${productData.answer === 'PC' ? 'selected' : ''}>PC - 部分满足</option>
                <option value="NC" ${productData.answer === 'NC' ? 'selected' : ''}>NC - 不满足</option>
                <option value="N/A" ${productData.answer === 'N/A' ? 'selected' : ''}>N/A - 不适用</option>
              </select>
            </div>

            <!-- 应答说明 -->
            <div style="margin-bottom:20px;">
              <label style="font-weight:500;margin-bottom:8px;display:block;">应答说明：</label>
              <textarea id="explainTextarea" style="width:100%;height:100px;padding:8px 12px;border:1px solid #d9d9d9;border-radius:4px;resize:vertical;font-family:inherit;" placeholder="请输入应答说明...">${productData.explain || ''}</textarea>
            </div>

            <!-- 索引 -->
            <div style="margin-bottom:20px;">
              <label style="font-weight:500;margin-bottom:8px;display:block;">索引：</label>
              <input type="text" id="indexInput" style="width:100%;padding:6px 12px;border:1px solid #d9d9d9;border-radius:4px;" placeholder="如：技术文档-3.2.1" value="${productData.index || ''}" />
            </div>

            <!-- 备注 -->
            <div style="margin-bottom:20px;">
              <label style="font-weight:500;margin-bottom:8px;display:block;">备注：</label>
              <input type="text" id="remarkInput" style="width:100%;padding:6px 12px;border:1px solid #d9d9d9;border-radius:4px;" placeholder="可添加备注信息" value="${productData.remark || ''}" />
            </div>

            <!-- 补充信息 -->
            <div style="margin-bottom:20px;">
              <label style="font-weight:500;margin-bottom:8px;display:block;">补充信息：</label>
              <textarea id="supplementTextarea" style="width:100%;height:60px;padding:8px 12px;border:1px solid #d9d9d9;border-radius:4px;resize:vertical;font-family:inherit;" placeholder="可补充说明，辅助AI推理">${productData.supplement || ''}</textarea>
            </div>
          </div>

          <div style="padding:16px 24px;border-top:1px solid #e8e8e8;display:flex;justify-content:flex-end;gap:12px;">
            <button id="reAnswerBtn" class="soc-btn soc-btn-green">AI重新应答</button>
            <button id="saveDetailBtn" class="soc-btn soc-btn-blue">保存</button>
            <button class="close-modal soc-btn">取消</button>
          </div>
        </div>
      `;
    }

    // 绑定详情弹窗事件
    function bindDetailModalEvents(modal, row, idx) {
      // 关闭弹窗
      modal.querySelectorAll('.close-modal').forEach(btn => {
        btn.onclick = () => closeModal(modal);
      });

      // 点击遮罩关闭
      modal.onclick = (e) => {
        if (e.target === modal) {
          closeModal(modal);
        }
      };

      // 产品切换
      const productSelect = modal.querySelector('#productSelect');
      if (productSelect) {
        productSelect.onchange = function() {
          // 重新渲染弹窗内容
          closeModal(modal);
          showAnswerDetail(idx);
        };
      }

      // 保存按钮
      const saveBtn = modal.querySelector('#saveDetailBtn');
      if (saveBtn) {
        saveBtn.onclick = () => saveDetailChanges(modal, row, idx);
      }

      // AI重新应答按钮
      const reAnswerBtn = modal.querySelector('#reAnswerBtn');
      if (reAnswerBtn) {
        reAnswerBtn.onclick = () => performReAnswer(modal, row, idx);
      }

      // 应答状态变化时更新样式
      const answerSelect = modal.querySelector('#answerSelect');
      if (answerSelect) {
        answerSelect.onchange = function() {
          updateAnswerSelectStyle(this);
        };
        updateAnswerSelectStyle(answerSelect);
      }
    }

    // 更新应答选择框样式
    function updateAnswerSelectStyle(select) {
      const value = select.value;
      const colors = {
        'FC': { bg: '#f6ffed', color: '#52c41a', border: '#b7eb8f' },
        'PC': { bg: '#fff7e6', color: '#faad14', border: '#ffd591' },
        'NC': { bg: '#fff2f0', color: '#ff4d4f', border: '#ffccc7' },
        'N/A': { bg: '#f5f5f5', color: '#666', border: '#d9d9d9' },
        '': { bg: '#f5f5f5', color: '#999', border: '#d9d9d9' }
      };

      const style = colors[value] || colors[''];
      select.style.background = style.bg;
      select.style.color = style.color;
      select.style.borderColor = style.border;
    }

    // 保存详情修改
    function saveDetailChanges(modal, row, idx) {
      try {
        const products = row.products || ['5GC'];
        const currentProduct = modal.querySelector('#productSelect')?.value || products[0];

        // 获取表单数据
        const formData = {
          answer: modal.querySelector('#answerSelect')?.value || '',
          explain: modal.querySelector('#explainTextarea')?.value || '',
          index: modal.querySelector('#indexInput')?.value || '',
          remark: modal.querySelector('#remarkInput')?.value || '',
          supplement: modal.querySelector('#supplementTextarea')?.value || '',
          answerType: '人工' // 手动修改标记为人工
        };

        // 更新数据
        const list = getAnswerList();
        if (!list[idx].productData) {
          list[idx].productData = {};
        }
        if (!list[idx].productData[currentProduct]) {
          list[idx].productData[currentProduct] = {};
        }

        Object.assign(list[idx].productData[currentProduct], formData);

        // 保存数据
        if (saveAnswerList(list)) {
          showSuccess('保存成功');
          renderAnswerTable();
          closeModal(modal);
        } else {
          showError('保存失败');
        }

      } catch (e) {
        console.error('保存详情失败:', e);
        showError('保存失败');
      }
    }

    // 执行AI重新应答
    function performReAnswer(modal, row, idx) {
      try {
        const products = row.products || ['5GC'];
        const currentProduct = modal.querySelector('#productSelect')?.value || products[0];
        const supplement = modal.querySelector('#supplementTextarea')?.value || '';

        // 显示处理中状态
        const reAnswerBtn = modal.querySelector('#reAnswerBtn');
        const originalText = reAnswerBtn.textContent;
        reAnswerBtn.textContent = '处理中...';
        reAnswerBtn.disabled = true;

        // 模拟AI处理
        setTimeout(() => {
          // 生成模拟应答
          const mockAnswers = ['FC', 'PC', 'NC'];
          const randomAnswer = mockAnswers[Math.floor(Math.random() * mockAnswers.length)];
          const mockExplain = `基于条目"${row.desc}"${supplement ? `和补充信息"${supplement}"` : ''}，AI分析后认为${currentProduct}产品${randomAnswer === 'FC' ? '完全满足' : randomAnswer === 'PC' ? '部分满足' : '不满足'}相关要求。`;

          // 更新表单
          const answerSelect = modal.querySelector('#answerSelect');
          const explainTextarea = modal.querySelector('#explainTextarea');

          if (answerSelect) {
            answerSelect.value = randomAnswer;
            updateAnswerSelectStyle(answerSelect);
          }

          if (explainTextarea) {
            explainTextarea.value = mockExplain;
          }

          // 恢复按钮状态
          reAnswerBtn.textContent = originalText;
          reAnswerBtn.disabled = false;

          showSuccess('AI应答完成');

        }, 2000);

      } catch (e) {
        console.error('AI重新应答失败:', e);
        showError('AI应答失败');
      }
    }

    // 关闭弹窗
    function closeModal(modal) {
      if (modal && modal.parentNode) {
        document.body.removeChild(modal);
        const index = modalStack.indexOf(modal);
        if (index > -1) {
          modalStack.splice(index, 1);
        }
      }
    }

    // 关闭所有弹窗
    function closeAllModals() {
      modalStack.forEach(modal => {
        if (modal && modal.parentNode) {
          document.body.removeChild(modal);
        }
      });
      modalStack.length = 0;
    }

    // ====== 满足度计算功能 ======

    // 计算满足度指标
    function calculateSatisfaction() {
      try {
        const list = getAnswerList();

        // 统计各种状态的数量
        let totalItems = 0;
        let fcCount = 0;
        let pcCount = 0;
        let ncCount = 0;
        let naCount = 0;
        let unansweredCount = 0;

        const productStats = {};

        list.forEach(row => {
          if (row.products && row.products.length > 0) {
            row.products.forEach(product => {
              totalItems++;

              if (!productStats[product]) {
                productStats[product] = { fc: 0, pc: 0, nc: 0, na: 0, unanswered: 0 };
              }

              const productData = row.productData?.[product];
              if (productData && productData.answer) {
                const answer = productData.answer.toUpperCase();
                switch (answer) {
                  case 'FC':
                    fcCount++;
                    productStats[product].fc++;
                    break;
                  case 'PC':
                    pcCount++;
                    productStats[product].pc++;
                    break;
                  case 'NC':
                    ncCount++;
                    productStats[product].nc++;
                    break;
                  case 'N/A':
                    naCount++;
                    productStats[product].na++;
                    break;
                  default:
                    unansweredCount++;
                    productStats[product].unanswered++;
                    break;
                }
              } else {
                unansweredCount++;
                productStats[product].unanswered++;
              }
            });
          }
        });

        // 计算满足度和不满足度
        const satisfaction = totalItems > 0 ? ((fcCount + pcCount) / totalItems * 100).toFixed(1) : 0;
        const unsatisfaction = totalItems > 0 ? (ncCount / totalItems * 100).toFixed(1) : 0;
        const completionRate = totalItems > 0 ? ((totalItems - unansweredCount) / totalItems * 100).toFixed(1) : 0;

        // 显示结果弹窗
        showSatisfactionResult({
          totalItems,
          fcCount,
          pcCount,
          ncCount,
          naCount,
          unansweredCount,
          satisfaction,
          unsatisfaction,
          completionRate,
          productStats
        });

      } catch (e) {
        console.error('计算满足度失败:', e);
        showError('计算满足度失败');
      }
    }

    // 显示满足度计算结果
    function showSatisfactionResult(stats) {
      const modalHtml = `
        <div class="modal-content" style="width:700px;max-width:95vw;padding:0;">
          <div style="padding:20px 24px;border-bottom:1px solid #e8e8e8;display:flex;justify-content:space-between;align-items:center;">
            <h3 style="margin:0;font-size:18px;color:#333;">📊 满足度指标计算结果</h3>
            <button class="close-modal" style="background:none;border:none;font-size:24px;cursor:pointer;color:#999;">&times;</button>
          </div>

          <div style="padding:20px 24px;max-height:70vh;overflow-y:auto;">
            <!-- 总体指标 -->
            <div style="background:#f6ffed;border:1px solid #b7eb8f;border-radius:8px;padding:16px;margin-bottom:20px;">
              <h4 style="margin:0 0 16px 0;color:#389e0d;">总体满足度指标</h4>
              <div style="display:grid;grid-template-columns:repeat(3,1fr);gap:16px;margin-bottom:16px;">
                <div style="text-align:center;">
                  <div style="font-size:32px;font-weight:bold;color:#52c41a;">${stats.satisfaction}%</div>
                  <div style="color:#666;font-size:14px;">满足度</div>
                  <div style="color:#999;font-size:12px;">(FC + PC) / 总条目</div>
                </div>
                <div style="text-align:center;">
                  <div style="font-size:32px;font-weight:bold;color:#ff4d4f;">${stats.unsatisfaction}%</div>
                  <div style="color:#666;font-size:14px;">不满足度</div>
                  <div style="color:#999;font-size:12px;">NC / 总条目</div>
                </div>
                <div style="text-align:center;">
                  <div style="font-size:32px;font-weight:bold;color:#1890ff;">${stats.completionRate}%</div>
                  <div style="color:#666;font-size:14px;">完成度</div>
                  <div style="color:#999;font-size:12px;">已应答 / 总条目</div>
                </div>
              </div>

              <div style="border-top:1px solid #d9f7be;padding-top:12px;">
                <div style="display:grid;grid-template-columns:repeat(5,1fr);gap:8px;font-size:13px;">
                  <div style="color:#52c41a;">FC：<strong>${stats.fcCount}</strong></div>
                  <div style="color:#faad14;">PC：<strong>${stats.pcCount}</strong></div>
                  <div style="color:#ff4d4f;">NC：<strong>${stats.ncCount}</strong></div>
                  <div style="color:#666;">N/A：<strong>${stats.naCount}</strong></div>
                  <div style="color:#999;">未应答：<strong>${stats.unansweredCount}</strong></div>
                </div>
                <div style="margin-top:8px;font-size:13px;color:#666;">
                  总条目数：<strong>${stats.totalItems}</strong>
                </div>
              </div>
            </div>

            <!-- 分产品统计 -->
            <div style="margin-bottom:20px;">
              <h4 style="margin:0 0 12px 0;color:#333;">分产品统计</h4>
              <div style="display:grid;gap:12px;">
                ${Object.entries(stats.productStats).map(([product, data]) => {
                  const total = data.fc + data.pc + data.nc + data.na + data.unanswered;
                  const productSatisfaction = total > 0 ? ((data.fc + data.pc) / total * 100).toFixed(1) : 0;
                  return `
                    <div style="background:#fff;border:1px solid #e8e8e8;border-radius:6px;padding:12px;">
                      <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:8px;">
                        <span style="font-weight:500;color:#1765d5;">${product}</span>
                        <span style="font-weight:500;color:#52c41a;">${productSatisfaction}%</span>
                      </div>
                      <div style="display:flex;gap:12px;font-size:12px;">
                        <span style="color:#52c41a;">FC: ${data.fc}</span>
                        <span style="color:#faad14;">PC: ${data.pc}</span>
                        <span style="color:#ff4d4f;">NC: ${data.nc}</span>
                        <span style="color:#666;">N/A: ${data.na}</span>
                        <span style="color:#999;">未应答: ${data.unanswered}</span>
                      </div>
                    </div>
                  `;
                }).join('')}
              </div>
            </div>

            <!-- 建议 -->
            <div style="background:#fff7e6;border:1px solid #ffd591;border-radius:8px;padding:16px;">
              <h4 style="margin:0 0 12px 0;color:#fa8c16;">💡 改进建议</h4>
              <ul style="margin:0;padding-left:20px;color:#666;line-height:1.6;">
                ${stats.unansweredCount > 0 ? `<li>还有 ${stats.unansweredCount} 个条目未应答，建议优先完成</li>` : ''}
                ${stats.ncCount > stats.totalItems * 0.2 ? '<li>不满足条目较多，建议重点关注产品能力提升</li>' : ''}
                ${stats.satisfaction < 80 ? '<li>总体满足度偏低，建议分析具体原因并制定改进计划</li>' : ''}
                ${stats.satisfaction >= 90 ? '<li>满足度表现优秀，可作为成功案例推广</li>' : ''}
              </ul>
            </div>
          </div>

          <div style="padding:16px 24px;border-top:1px solid #e8e8e8;display:flex;justify-content:flex-end;gap:12px;">
            <button id="exportReportBtn" class="soc-btn soc-btn-blue">导出报告</button>
            <button class="close-modal soc-btn">关闭</button>
          </div>
        </div>
      `;

      const modal = document.createElement('div');
      modal.className = 'modal-overlay';
      modal.innerHTML = modalHtml;

      document.body.appendChild(modal);
      modalStack.push(modal);

      // 绑定事件
      modal.querySelectorAll('.close-modal').forEach(btn => {
        btn.onclick = () => closeModal(modal);
      });

      modal.onclick = (e) => {
        if (e.target === modal) {
          closeModal(modal);
        }
      };

      const exportBtn = modal.querySelector('#exportReportBtn');
      if (exportBtn) {
        exportBtn.onclick = () => {
          // 模拟导出功能
          showSuccess('满足度报告导出成功（模拟）');
        };
      }
    }

    // ====== 删除功能 ======

    // 确认删除单个条目
    function confirmDeleteItem(idx) {
      const list = getAnswerList();
      const item = list[idx];
      if (!item) {
        showError('条目不存在');
        return;
      }

      const modalHtml = `
        <div class="modal-content" style="width:420px;max-width:95vw;padding:0;">
          <div style="padding:20px 24px;border-bottom:1px solid #e8e8e8;display:flex;justify-content:space-between;align-items:center;">
            <h3 style="margin:0;font-size:18px;color:#ff4d4f;">⚠️ 确认删除</h3>
            <button class="close-modal" style="background:none;border:none;font-size:24px;cursor:pointer;color:#999;">&times;</button>
          </div>

          <div style="padding:20px 24px;">
            <div style="background:#fff2f0;border:1px solid #ffccc7;border-radius:6px;padding:16px;margin-bottom:16px;">
              <div style="font-weight:500;margin-bottom:8px;">即将删除以下条目：</div>
              <div style="color:#666;">编号：${item.no || idx + 1}</div>
              <div style="color:#666;margin-top:4px;">描述：${item.desc || '无描述'}</div>
            </div>
            <div style="color:#999;font-size:13px;">
              删除后将无法恢复，请确认是否继续？
            </div>
          </div>

          <div style="padding:16px 24px;border-top:1px solid #e8e8e8;display:flex;justify-content:flex-end;gap:12px;">
            <button id="confirmDelete" class="soc-btn soc-btn-red">确认删除</button>
            <button class="close-modal soc-btn">取消</button>
          </div>
        </div>
      `;

      const modal = document.createElement('div');
      modal.className = 'modal-overlay';
      modal.innerHTML = modalHtml;

      document.body.appendChild(modal);
      modalStack.push(modal);

      // 绑定事件
      modal.querySelectorAll('.close-modal').forEach(btn => {
        btn.onclick = () => closeModal(modal);
      });

      modal.onclick = (e) => {
        if (e.target === modal) {
          closeModal(modal);
        }
      };

      const confirmBtn = modal.querySelector('#confirmDelete');
      if (confirmBtn) {
        confirmBtn.onclick = () => {
          deleteItems([idx]);
          closeModal(modal);
        };
      }
    }

    // 确认批量删除
    function confirmBatchDelete() {
      const checkedIndexes = Array.from(document.querySelectorAll('.rowCheck:checked'))
        .map(cb => parseInt(cb.dataset.idx));

      if (checkedIndexes.length === 0) {
        showError('请先选择要删除的条目');
        return;
      }

      const list = getAnswerList();
      const selectedItems = checkedIndexes.map(idx => list[idx]).filter(Boolean);

      const modalHtml = `
        <div class="modal-content" style="width:520px;max-width:95vw;padding:0;">
          <div style="padding:20px 24px;border-bottom:1px solid #e8e8e8;display:flex;justify-content:space-between;align-items:center;">
            <h3 style="margin:0;font-size:18px;color:#ff4d4f;">⚠️ 批量删除确认</h3>
            <button class="close-modal" style="background:none;border:none;font-size:24px;cursor:pointer;color:#999;">&times;</button>
          </div>

          <div style="padding:20px 24px;max-height:60vh;overflow-y:auto;">
            <div style="background:#fff2f0;border:1px solid #ffccc7;border-radius:6px;padding:16px;margin-bottom:16px;">
              <div style="font-weight:500;margin-bottom:8px;">即将删除 ${selectedItems.length} 个条目：</div>
              <div style="max-height:200px;overflow-y:auto;">
                ${selectedItems.map((item, i) => `
                  <div style="color:#666;margin:4px 0;padding:4px 8px;background:#fafafa;border-radius:4px;">
                    ${item.no || checkedIndexes[i] + 1}. ${truncateText(item.desc || '无描述', 40)}
                  </div>
                `).join('')}
              </div>
            </div>
            <div style="color:#999;font-size:13px;">
              删除后将无法恢复，请确认是否继续？
            </div>
          </div>

          <div style="padding:16px 24px;border-top:1px solid #e8e8e8;display:flex;justify-content:flex-end;gap:12px;">
            <button id="confirmBatchDelete" class="soc-btn soc-btn-red">确认删除</button>
            <button class="close-modal soc-btn">取消</button>
          </div>
        </div>
      `;

      const modal = document.createElement('div');
      modal.className = 'modal-overlay';
      modal.innerHTML = modalHtml;

      document.body.appendChild(modal);
      modalStack.push(modal);

      // 绑定事件
      modal.querySelectorAll('.close-modal').forEach(btn => {
        btn.onclick = () => closeModal(modal);
      });

      modal.onclick = (e) => {
        if (e.target === modal) {
          closeModal(modal);
        }
      };

      const confirmBtn = modal.querySelector('#confirmBatchDelete');
      if (confirmBtn) {
        confirmBtn.onclick = () => {
          deleteItems(checkedIndexes);
          closeModal(modal);
        };
      }
    }

    // 确认清空全部条目
    function confirmClearAllItems() {
      const list = getAnswerList();

      if (list.length === 0) {
        showError('当前没有条目可以清空');
        return;
      }

      const modalHtml = `
        <div class="modal-content" style="width:420px;max-width:95vw;padding:0;">
          <div style="padding:20px 24px;border-bottom:1px solid #e8e8e8;display:flex;justify-content:space-between;align-items:center;">
            <h3 style="margin:0;font-size:18px;color:#ff4d4f;">⚠️ 清空全部确认</h3>
            <button class="close-modal" style="background:none;border:none;font-size:24px;cursor:pointer;color:#999;">&times;</button>
          </div>

          <div style="padding:20px 24px;">
            <div style="background:#fff2f0;border:1px solid #ffccc7;border-radius:6px;padding:16px;margin-bottom:16px;">
              <div style="font-weight:500;margin-bottom:8px;">即将清空全部条目：</div>
              <div style="color:#666;">当前共有 <strong>${list.length}</strong> 个条目</div>
            </div>
            <div style="color:#999;font-size:13px;">
              <strong style="color:#ff4d4f;">此操作将删除所有条目，且无法恢复！</strong><br/>
              请确认是否继续？
            </div>
          </div>

          <div style="padding:16px 24px;border-top:1px solid #e8e8e8;display:flex;justify-content:flex-end;gap:12px;">
            <button id="confirmClearAll" class="soc-btn soc-btn-red">确认清空</button>
            <button class="close-modal soc-btn">取消</button>
          </div>
        </div>
      `;

      const modal = document.createElement('div');
      modal.className = 'modal-overlay';
      modal.innerHTML = modalHtml;

      document.body.appendChild(modal);
      modalStack.push(modal);

      // 绑定事件
      modal.querySelectorAll('.close-modal').forEach(btn => {
        btn.onclick = () => closeModal(modal);
      });

      modal.onclick = (e) => {
        if (e.target === modal) {
          closeModal(modal);
        }
      };

      const confirmBtn = modal.querySelector('#confirmClearAll');
      if (confirmBtn) {
        confirmBtn.onclick = () => {
          clearAllItems();
          closeModal(modal);
        };
      }
    }

    // 删除条目
    function deleteItems(indexes) {
      try {
        const list = getAnswerList();
        // 按索引倒序删除，避免索引错位
        const sortedIndexes = indexes.sort((a, b) => b - a);

        sortedIndexes.forEach(idx => {
          if (idx >= 0 && idx < list.length) {
            list.splice(idx, 1);
          }
        });

        saveAnswerList(list);
        renderAnswerTable();
        showSuccess(`已删除 ${indexes.length} 个条目`);

        // 取消所有选中状态
        document.querySelectorAll('.rowCheck').forEach(cb => cb.checked = false);
        const selectAllCheckbox = document.getElementById('selectAllRow');
        if (selectAllCheckbox) {
          selectAllCheckbox.checked = false;
          selectAllCheckbox.indeterminate = false;
        }

      } catch (e) {
        console.error('删除条目失败:', e);
        showError('删除失败');
      }
    }

    // 清空全部条目
    function clearAllItems() {
      try {
        saveAnswerList([]);
        renderAnswerTable();
        showSuccess('已清空全部条目');

        // 取消所有选中状态
        const selectAllCheckbox = document.getElementById('selectAllRow');
        if (selectAllCheckbox) {
          selectAllCheckbox.checked = false;
          selectAllCheckbox.indeterminate = false;
        }

      } catch (e) {
        console.error('清空条目失败:', e);
        showError('清空失败');
      }
    }

    // ====== 键盘快捷键 ======

    // 绑定键盘快捷键
    function bindKeyboardShortcuts() {
      document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + S: 保存（阻止浏览器默认保存）
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
          e.preventDefault();
          // 可以触发保存操作
          showSuccess('快捷键保存（模拟）');
        }

        // Ctrl/Cmd + N: 新增条目
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
          e.preventDefault();
          showAddEntryDialog();
        }

        // Ctrl/Cmd + I: 导入
        if ((e.ctrlKey || e.metaKey) && e.key === 'i') {
          e.preventDefault();
          importExcelFile();
        }

        // Ctrl/Cmd + E: 导出
        if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
          e.preventDefault();
          exportData();
        }

        // Escape: 关闭弹窗
        if (e.key === 'Escape') {
          if (modalStack.length > 0) {
            const topModal = modalStack[modalStack.length - 1];
            closeModal(topModal);
          }
        }

        // F5: 刷新表格
        if (e.key === 'F5') {
          e.preventDefault();
          renderAnswerTable();
          showSuccess('表格已刷新');
        }

        // Delete: 删除选中项
        if (e.key === 'Delete') {
          const checkedItems = document.querySelectorAll('.rowCheck:checked');
          if (checkedItems.length > 0) {
            confirmBatchDelete();
          }
        }
      });
    }

    // ====== 错误边界和异常处理 ======

    // 全局错误处理
    function setupGlobalErrorHandling() {
      // 捕获未处理的Promise错误
      window.addEventListener('unhandledrejection', function(event) {
        console.error('未处理的Promise错误:', event.reason);
        showError('系统出现异常，请刷新页面重试');
        event.preventDefault();
      });

      // 捕获JavaScript运行时错误
      window.addEventListener('error', function(event) {
        console.error('JavaScript错误:', event.error);
        showError('页面出现错误，部分功能可能受影响');
      });
    }

    // ====== 性能优化 ======

    // 表格虚拟滚动（简化版）
    function optimizeTablePerformance() {
      const tableBody = document.getElementById('answerTableBody');
      if (!tableBody) return;

      // 监听滚动事件，实现懒加载
      const tableContainer = tableBody.closest('.table-container');
      if (tableContainer) {
        let scrollTimeout;
        tableContainer.addEventListener('scroll', function() {
          clearTimeout(scrollTimeout);
          scrollTimeout = setTimeout(() => {
            // 可以在这里实现虚拟滚动逻辑
            // 当数据量很大时，只渲染可见区域的行
          }, 100);
        });
      }
    }

    // ====== 数据备份和恢复 ======

    // 自动备份数据
    function autoBackupData() {
      try {
        const list = getAnswerList();
        const backup = {
          data: list,
          timestamp: new Date().toISOString(),
          version: '1.0'
        };

        storage.setItem('socAnswerListBackup', JSON.stringify(backup));
        console.log('数据自动备份完成');
      } catch (e) {
        console.error('自动备份失败:', e);
      }
    }

    // 恢复备份数据
    function restoreBackupData() {
      try {
        const backupData = storage.getItem('socAnswerListBackup');
        if (!backupData) {
          showError('没有找到备份数据');
          return false;
        }

        const backup = JSON.parse(backupData);
        if (backup.data && Array.isArray(backup.data)) {
          if (confirm(`发现备份数据（${backup.timestamp}），是否恢复？这将覆盖当前数据。`)) {
            saveAnswerList(backup.data);
            renderAnswerTable();
            showSuccess('数据恢复成功');
            return true;
          }
        } else {
          showError('备份数据格式错误');
        }
      } catch (e) {
        console.error('恢复备份失败:', e);
        showError('恢复备份失败');
      }
      return false;
    }

    // ====== 用户体验优化 ======

    // 添加加载状态管理
    function showTableLoading(show = true) {
      const tableBody = document.getElementById('answerTableBody');
      const loadingTip = document.getElementById('loadingTip');

      if (show) {
        if (tableBody) {
          tableBody.style.opacity = '0.6';
          tableBody.style.pointerEvents = 'none';
        }
        if (loadingTip) {
          loadingTip.style.display = 'inline';
        }
      } else {
        if (tableBody) {
          tableBody.style.opacity = '1';
          tableBody.style.pointerEvents = 'auto';
        }
        if (loadingTip) {
          loadingTip.style.display = 'none';
        }
      }
    }

    // 添加操作确认
    function confirmAction(message, callback) {
      const confirmed = confirm(message);
      if (confirmed && typeof callback === 'function') {
        callback();
      }
      return confirmed;
    }

    // ====== 初始化增强 ======

    // 增强的页面初始化
    function enhancedInitPage() {
      try {
        // 设置全局错误处理
        setupGlobalErrorHandling();

        // 绑定键盘快捷键
        bindKeyboardShortcuts();

        // 原有初始化
        initPage();

        // 性能优化
        optimizeTablePerformance();

        // 设置自动备份（每5分钟）
        setInterval(autoBackupData, 5 * 60 * 1000);

        // 检查是否有备份数据需要恢复
        const hasBackup = storage.getItem('socAnswerListBackup');
        const currentData = storage.getItem('socAnswerList');

        if (hasBackup && !currentData) {
          // 如果有备份但没有当前数据，提示恢复
          setTimeout(() => {
            if (confirm('检测到备份数据，是否恢复？')) {
              restoreBackupData();
            }
          }, 1000);
        }

        console.log('增强版页面初始化完成');

      } catch (e) {
        console.error('页面初始化失败:', e);
        showError('页面初始化失败，请刷新重试');
      }
    }

    // ====== 页面加载完成后初始化 ======
    document.addEventListener('DOMContentLoaded', function() {
      enhancedInitPage();
    });

    // 页面卸载前保存数据
    window.addEventListener('beforeunload', function(e) {
      try {
        autoBackupData();
      } catch (error) {
        console.error('页面卸载前备份失败:', error);
      }
    });

  </script>
</body>

</html>
