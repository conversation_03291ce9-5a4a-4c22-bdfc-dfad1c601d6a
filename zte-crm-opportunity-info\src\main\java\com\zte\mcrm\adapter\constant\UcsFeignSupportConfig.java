package com.zte.mcrm.adapter.constant;

import com.zte.itp.msa.core.constant.SysGlobalConst;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2021/9/16
 */
public class UcsFeignSupportConfig implements RequestInterceptor {
    @Autowired
    private UcsSpringProperties ucsSpringProperties;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());


    @Override
    public void apply(RequestTemplate requestTemplate) {
        createRequestHeaderMap(requestTemplate);
    }

    /**
     * 用户中心鉴权请求头
     * <AUTHOR>
     * @date 2021-03-11
     *
     */
    private void createRequestHeaderMap(RequestTemplate requestTemplate) {
        String uuid = UUID.randomUUID().toString();
        // 调用方access key
        String accessKey = ucsSpringProperties.getAccessKey();
        // 调用方id
        String appId = ucsSpringProperties.getAppId();
        String tenantId = ucsSpringProperties.getTenantId();
        String secretKey = ucsSpringProperties.getSecretKey();

        StringBuilder sb = new StringBuilder();
        sb.append(appId).append(accessKey).append(secretKey).append(uuid);

        String authValue = DigestUtils.md5Hex(sb.toString());

        logger.info("UcsFeignSupportConfig,accessKey:{},appId:{},tenantId:{},secretKey:{},authValue:{}",
                accessKey,appId,tenantId,secretKey,authValue);

        // 调用方access key
        requestTemplate.header(UcsConstant.HTTP_HEADER_X_ACCESS_KEY, accessKey);
        // 调用方id
        requestTemplate.header(UcsConstant.HTTP_HEADER_X_APP_ID, appId);
        // 调用方鉴权码
        requestTemplate.header(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE, authValue);
        // 租户id
        requestTemplate.header(SysGlobalConst.HTTP_HEADER_X_TENANT_ID, tenantId);
        // 调用链追踪码
        requestTemplate.header(UcsConstant.HTTP_HEADER_X_UUID, uuid);
    }
}
