点击创建配套设计文档

详细设计\_升级包模板

详细设计\_升级包模板

详细设计\_标准项目模板

详细设计\_标准项目模板

测试设计评审

测试设计评审

1 概述

1.1 术语

1.2 修订记录

1.3 背景和价值

2 功能需求

2.1用户体验设计目标

2.2可编排可配置设计目标

2.3功能1：任务管理

2.4功能2：任务详情-条目管理

2.5 功能3：任务详情-数据分析

2.6 功能4：任务详情-条目管理-AI应答

2.7 功能5：任务详情-条目管理-人工应答

2.8 功能6：快捷应答

2.9 功能7：Agent人机交互

3 需求内容检查单

附：评审纪要模板

# 1 概述

## 1.1 术语

| 名称<br> | 描述<br> |
--- | ---
| 条目<br> | 从标书中梳理出来的要求/问题。<br> |
| 应答<br> | 对条目的回答，通常将以一个产品维度对条目的应答作为一个应答<br> |
| SOC<br> | Statement of Compliance，符合性声明, 也称为逐点应答<br> |
| FC<br> | Full Compliance 完全满足<br> |
| PC<br> | Partially Compliance 部分满足<br> |
| NC<br> | NotCompliance 不满足<br> |

# 

## 1.2 修订记录

修订详情

| 版本<br> | 内容<br> | 负责人<br> | 更新时间<br> | 备注<br> |
--- | --- | --- | --- | ---
| V1.0<br> | <br> | <br> | <br> | <br> |

## 1.3 背景和价值

<对本需求文档中要求的功能给出背景和价值分析，说明功能背景和价值，包含为什么要做？不做会有什么影响？做了有什么价值？等多方面>

背景：05 SOC应答

为什么要做？

* 传统标书应答流程依赖人工查找资料、分析问题、撰写答案，效率低下且质量不稳定
* 缺乏统一的知识管理和智能推荐机制，导致重复工作多，经验难以复用
* 多信息源（项目文档、文档库、GBBS、历史SOC文档）分散，缺乏有效整合

不做会有什么影响？

* 标书应答效率低，影响项目交付进度
* 知识资产无法有效沉淀和复用，造成资源浪费
* 人工成本高，且容易出现遗漏和错误

做了有什么价值？

SOC应答作业提效：假设 1000个标/年，每个标书需要应答1000条，需要应答100 0000条，人工应答，一天100条；人工需要100 0000（条）/100条/人天=10000人天；

基于5000条应答条目的标书，AI 工具半天可以完成初版应答，再辅以人工的检查完善，需要 5人天做完；

总共需要1000标\*1000条/5000条\*5人\*1天=1000人天。节省10000-1000=9000 人天。

SOC应答质量提升：人工有时会有疏忽，如果AI能力足够，则可以避免人为造成的错误。

项目运作质量提升：作业提效，有助于项目组人员更专注于项目运作等MKT活动，提升项目运作质量。

# 2 功能需求

## 2.1用户体验设计目标

<介绍文档交互设计的用户体验目标，可参考【案例】用户体验目标进行编写，如若不涉及，需说明：此功能不涉及。>

## 2.2可编排可配置设计目标

<可基于市场需求一页纸领域建模、可编排可配置设计目标，参考可配置可编排的定义进行编写，如若不涉及，需说明：此功能不涉及。>

参考案例：可编排可配置PO协同案例

## 2.3功能1：任务管理

#### 

### 场景描述：用户开始/查看SOC智能应答任务情况

<介绍功能1的使用场景，说清楚功能即可，不要求必须要按照下面格式编写，但建议包含下面提到的信息。>

用户角色：SOC智能应答-普通用户

前置条件：用户已申请SOC智能应答-普通用户权限

基本事件流程

N0010 用户进入任务管理页面，可以点击【创建任务】按钮，直接新建一个任务开始进行SOC智能应答技术标。

N0020 点击【创建任务】打开创建任务的弹窗界面，需要输入的任务要素为：任务名称、国家/MTO、MTO分支、客户、项目、数据源、应答条目文件。具体描述见【数据项描述】。

N0030 任务列表查询，支持的查询条件为：关键字搜索（支持模糊搜索【任务名称】）。列表查询分页默认20条/页，支持选取10条/20条/50条/100条/200条/页。

N0040 任务列表字段为：任务编码、任务名称、产品、产品、国家、客户、项目、应答条目数、应答进度、操作。具体描述见【数据项描述】。

N0050 操作应包含：应答、编辑、复制、删除。

异常事件流程

N0010 用户不具备【SOC智能应答技术对标用户】权限，无法看到菜单【任务管理】，如果通过链接进入页面，页面仅展示“抱歉，您没有相关权限，如需进行SOC智能应答，请前往IT网站-业务申请-应用系统权限-应用系统权限申请-投标智能体-SOC智能应答用户下申请角色：SOC智能应答-普通用户”换行，下面展示“点击此处申请权限”。

N0020 结束。

### 界面原型(选填)

| #<br><br> | 设计类型<br> | 链接/图示/资料<br> |
--- | --- | ---
| 1<br> | BA输出的低保真设计（通常仅展示交互设计）<br> | SOC智能应答-UX及DEMO<br> |
| 2<br> | UX输出的高保真设计（通常需要展示视觉设计&交互设计）<br> | SOC智能应答-UX及DEMO<br> |

### 数据项描述

<列出有关功能的数据元素，或信息结构。若原型部分已经描述过，可以省略。>

| #<br> | 数据项<br> | 类型<br> | 默认值<br> | 是否必填<br> | 描述<br> |
--- | --- | --- | --- | --- | ---
| 1<br> | 任务新建<br> | | | | |
| 2<br> | 任务名称<br> | 文本框输入<br> | 空<br> | 是<br> | 不支持重复<br> |
| 3<br> | 国家/MTO<br> | 搜索下拉框<br> | 空<br> | 否<br> | 当数据源为GBBS时，取GBBS中的国家/MTO<br> |
| 4<br> | 运营商/分支<br> | 搜索下拉框<br> | 空<br> | 否<br> | 当数据源为GBBS时，取GBBS中的运营商/分支<br> |
| 5<br> | 客户<br> | 搜索下拉框<br> | 空<br> | 否<br> | 当数据源为GBBS时，取GBBS中的客户<br> |
| 6<br> | 项目<br> | 搜索下拉框<br>+<br>文本输入框<br> | 空<br> | 否<br> | 搜索项目：/projectWeb/myProject<br>也支持手工输入<br>InOne查询这个/projectWeb/myProject<br> |
| 7<br> | 数据源<br> | 下拉框<br> | GBBS<br> | 是<br> | GBBS、文档库、项目文档、历史SOC文档<br>目前仅支持GBBS，先不在前端展示<br> |
| 8<br> | 应答条目文件<br> | 文件上传按钮<br> | 空<br> | 否<br> | 1、支持上传Excel文件<br>2、如未上传，支持后续通过编辑或者进入任务详情页面上传。<br> |
| 9<br> | 查询条件<br> | | | | |
| 10<br> | 任务编码<br> | 文本框输入<br> | 空<br> | 否<br> | 精确查询<br> |
| 11<br> | 任务名称<br> | 文本框输入<br> | 空<br> | 否<br> | 模糊查询<br> |
| 12<br> | 国家<br> | 搜索下拉框<br> | 空<br> | 否<br> | 精确查询<br> |
| 13<br> | 客户<br> | 搜索下拉框<br> | 空<br> | 否<br> | 精确查询<br> |
| 14<br> | 项目<br> | 搜索下拉框<br> | 空<br> | 否<br> | 精确查询<br> |
| 15<br> | 任务列表<br> | | | | |
| 16<br> | 任务名称<br> | 文本展示列<br> | NA<br> | NA<br> | 源自任务创建时的要素信息-任务名称<br> |
| 17<br> | 国家/MTO<br> | 文本展示列<br> | NA<br> | NA<br> | 源自任务创建时的要素信息-国家/MTO<br> |
| 18<br> | 项目<br> | 文本展示列<br> | NA<br> | NA<br> | 源自任务创建时的要素信息-项目<br> |
| 19<br> | 客户<br> | 文本展示列<br> | NA<br> | NA<br> | 源自任务创建时的要素信息-客户<br> |
| 20<br> | 应答进度<br> | 已应答条目数/总条目数<br> | NA<br> | NA<br> | 展示任务应答进度：已应答条目数/总条目数<br> |
| 21<br> | 总满足度<br> | 百分比<br> | NA<br> | NA<br> | (所有FC数量 + 所有PC数量) / 所有应答数量 × 100%<br> |
| 22<br> | 创建人<br> | 文本展示列<br> | NA<br> | NA<br> | <br> |
| 23<br> | 创建时间<br> | 年月日时分秒<br> | NA<br> | NA<br> | 任务被创建的时间<br> |
| 24<br> | 更新人<br> | 文本展示列<br> | NA<br> | NA<br> | <br> |
| 25<br> | 最近更新时间<br> | 年月日时分秒<br> | NA<br> | NA<br> | 任务最近被更新的时间与条目之间最新的一个更新时间<br> |
| 26<br> | 操作<br> | | | | |
| 27<br> | 应答<br> | 按钮<br> | NA<br> | NA<br> | 1、创建人及条目【指派给】可见<br>2、点击可进入任务详情页面，进行条目应答等操作<br> |
| 28<br> | 编辑<br> | 按钮<br> | NA<br> | NA<br> | 1、创建人及条目【指派给】可见<br>2、点击可调整任务相关要素：国家/MTO、省公司/分支、客户、项目、数据源、应答条目文件。<br> |
| 29<br> | 复制<br> | 按钮<br> | NA<br> | NA<br> | 1、创建人及条目【指派给】可见<br>2、点击可弹出同新建页面一样的弹窗，任务的相关要素自动按照复制对象的内容填充，可修改。其中任务名称后面要加一个后缀“\_复制”<br>3、并支持勾选是否复制条目应答结果，如果勾选复制条目应答结果，则应答条目置灰不可操作。<br> |
| 30<br> | 删除<br> | 按钮<br> | NA<br> | NA<br> | 1、创建人及条目【指派给】可见<br>2、点击可删除该条任务<br> |

### 国际化命名规则

| #<br> | 使用场景说明<br> | 中文<br> | 英文<br> |
--- | --- | --- | ---
| 1<br> | <br> | <br> | <br> |

### 埋点定义(选填)

<产品完成后用户行为收集分析埋点，方便后期产品运维和用户行为分析>

| #<br> | 模块<br> | 指标名称<br> | 指标定义<br> | PC/移动端<br> | 触发时机<br> | 频率<br> |
--- | --- | --- | --- | --- | --- | ---
| <br> | <br> | <br> | <br> | <br> | <br> | <br> |

### 验收准则(必填)

<验收准则写法请参考：用户满意条件（验收准则）的写法，主要推荐使用下面的情境式写法 Scenario-based>

| #<br> | Given<br> | When<br> | Then<br> | 是否需要自动化测试用例维护<br> |
--- | --- | --- | --- | ---
| 1<br> | 用户具备SOC智能应答-普通用户权限<br> | 用户点击创建任务按钮<br> | 弹出创建任务弹窗，包含所有必填和选填字段<br> | 是<br> |
| 2<br> | 用户在创建任务弹窗中填写必填信息<br> | 用户点击确认创建<br> | 任务创建成功，任务列表刷新显示新任务<br> | 是<br> |
| 3<br> | 任务列表中存在多个任务<br> | 用户在关键字搜索框输入任务名称<br> | 列表过滤显示匹配的任务<br> | 是<br> |
| 4<br> | 用户是任务创建人<br> | 用户点击任务的应答按钮<br> | 跳转到任务详情页面<br> | 是<br> |
| 5<br> | 用户是任务创建人<br> | 用户点击编辑按钮<br> | 弹出编辑弹窗，可修改任务相关要素<br> | 是<br> |
| 6<br> | 用户是任务创建人<br> | 用户点击复制按钮<br> | 弹出复制弹窗，任务信息自动填充，任务名称加"\_复制"后缀<br> | 是<br> |
| 7<br> | 用户是任务创建人<br> | 用户点击删除按钮<br> | 弹出确认删除提示，确认后删除任务<br> | 是<br> |
| 8<br> | 用户不具备相关权限<br> | 用户尝试访问任务管理页面<br> | 显示权限不足提示信息和申请权限链接<br> | 是<br> |

## 

## 2.4功能2：任务详情-条目管理

#### 

### 场景描述：用户创建任务后开始进行条目应答作业

<介绍功能1的使用场景，说清楚功能即可，不要求必须要按照下面格式编写，但建议包含下面提到的信息。>

用户角色：SOC智能应答-普通用户

前置条件：任务已创建，用户是该任务的创建人或条目的【指派给】

基本事件流程

N0010 用户从任务管理点击指定任务的应答按钮进入任务详情，页面头部展示：任务名称、国家/MTO、运营商/分支、客户、项目、数据源。

N0020 页面头部下方具备两个页签：条目管理、数据分析。条目管理可以进行条目应答相关操作，数据分析可以展示条目应答的分析情况。

N0030 【条目添加】：用户可在条目管理通过批量导入/单条录入添加待应答的条目，条目需补充的信息见【数据项描述】。

1）如开启了自动应答，则录入/导入的条目状态变为【应答中】，否则状态为【未应答】，直到应答完成，应答状态转为【已应答】。

2）应答状态为【应答中】的条目（细化到产品维度）不支持对其进行操作，可以导出数据。

3）应当支持可控制每个条目是否可自动应答以及重复时是否覆盖。

4）优先级：2）> 3）。

5）条目新增自动进行相似度分析

N0040 【列表查询】：用户在条目管理可通过查询条件：编号、条目描述、产品、应答状态、标签、应答、指派给、应答方式、应答来源查询条目列表。

1）列表查询分页默认20条/页，支持选取10条/20条/50条/100条/200条/页。

2）条目列字段【见数据项描述】，支持进行列的筛选，避免展示过多干扰信息。列筛选默认字段：编号、条目描述、产品、应答状态、应答、应答说明、索引、操作。

3）定时刷新当前条目信息（5s/次），不影响勾选、编辑等批量操作。

4）点击编辑，在列表支持对【应答】、【应答说明】、【备注】这三个字段进行编辑，操作栏【编辑】按钮消失，新增【保存】、【取消】这两个按钮。

5）一个条目下可根据产品细分多行，按照条目汇聚在一起，列表布局如下：

条目列表布局

| 序号<br> | 编号<br> | 条目描述<br> | 产品<br> | 标签<br> | 应答状态<br> | 应答<br> | 指派给<br> | 应答方式<br> | 应答说明<br> | 应答来源<br> | 索引<br> | 备注<br> | 最后更新人<br> | 最后更新时间<br> | 操作<br> |
--- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | ---
| 1<br> | CODE1<br> | 描述1<br> | 产品1<br> | <br> | 应答中<br> | <br> | 姓名 + 工号<br> | AI<br> | 说明1<br> | GBBS<br> | xxx-5.1<br> | <br> | 姓名 + 工号<br> | <br> | 删除<br> |
| 2<br> | | | 产品2<br> | <br> | 已应答<br> | PC<br> | 姓名 + 工号<br> | 手工<br> | 说明2<br> | GBBS<br> | xxx-1.1<br> | <br> | 姓名 + 工号<br> | <br> | 手工应答 AI应答 编辑删除<br> |
| 3<br> | | | 产品3<br> | <br> | 应答中<br> | <br> | 姓名 + 工号<br> | AI<br> | 说明3<br> | GBBS<br> | xxx-2.1<br> | <br> | 姓名 + 工号<br> | <br> | 删除<br> |
| 4<br> | CODE2<br> | 描述2<br> | 产品1<br> | <br> | 未应答<br> | <br> | 姓名 + 工号<br> | <br> | 说明1<br> | GBBS<br> | xxx-5.1<br> | <br> | 姓名 + 工号<br> | <br> | 手工应答 AI应答 编辑删除<br> |
| 5<br> | CODE3<br> | 描述3<br> | 产品1<br> | <br> | 已应答<br> | FC<br> | 姓名 + 工号<br> | AI<br> | 说明1<br> | GBBS<br> | xxx-5.1<br> | <br> | 姓名 + 工号<br> | <br> | 手工应答 AI应答 编辑删除<br> |
| 6<br> | | | 产品2<br> | <br> | 已应答<br> | PC<br> | 姓名 + 工号<br> | AI<br> | 说明2<br> | GBBS<br> | xxx-1.1<br> | <br> | 姓名 + 工号<br> | <br> | 手工应答 AI应答 编辑删除<br> |

N0050 用户点击【开始应答】、【导出】、【批量删除】、【批量添加标签】、【批量删除标签】、【设置产品】、【权限管理】，可对勾选/全量的条目进行相关条目管理的操作，详见【数据项描述】。

N0060 【权限】：仅任务【创建人】可查看并操作全部条目，条目【指派给】可查看所有条目，仅能操作指定其应答的条目，具体见下方列表。

| 用户<br> | 开始应答<br> | 批量删除<br> | 批量添加标签<br> | 批量移除标签<br> | 指派给<br> | 导出<br> | 单条录入<br> | 批量录入<br> | 手工应答<br> | AI应答<br> | 删除<br> | 编辑<br> |
--- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | ---
| 任务创建人<br> | √<br> | √<br> | √<br> | √<br> | √<br> | √<br> | √<br> | √<br> | √<br> | √<br> | √<br> | √<br> |
| 条目【指派给】<br> | √<br> | ×<br> | √<br> | √<br> | √<br> | √<br> | √<br> | √<br> | √<br> | √<br> | ×<br> | √<br> |
| 只读<br> | ×<br> | ×<br> | ×<br> | ×<br> | ×<br> | ×<br> | ×<br> | ×<br> | ×<br> | ×<br> | ×<br> | ×<br> |

1）不与项目强绑定，如关联项目，项目（技术方案负责人、市场商务负责人、合同交付负责人、投标经理、项目经理）可查看该任务。

2）授权只读功能。

N0070 【通知】应答指派给相关人员应答时，需要发送应答通知，对接通知中心，支持【邮件】和【工作通知】两种方式。

通知样式

标题：【SOC应答通知】您好，关于#{任务名称}，您名下有新增的SOC应答，请前往系统处理！

内容：

您名下有新增的SOC应答，请前往系统处理！

任务：#{任务名称}

产品：#{产品名称}

新增应答数：#{新增被指派的条目应答数}

异常事件流程

N0010 用户不具备【SOC智能应答技术对标用户】权限，无法看到菜单【任务管理】，如果通过链接进入页面，页面仅展示“抱歉，您没有相关权限，如需进行SOC智能应答，请前往IT网站-业务申请-应用系统权限-应用系统权限申请-投标智能体-SOC智能应答用户下申请角色：SOC智能应答-普通用户”换行，下面展示“点击此处申请权限”。

N0020 用户不具备该任务相关的权限，如果通过链接进入页面，则提示“抱歉，您不具备该任务查看权限！”

N0030 结束。

### 界面原型(选填)

| #<br><br> | 设计类型<br> | 链接/图示/资料<br> |
--- | --- | ---
| 1<br> | BA输出的低保真设计（通常仅展示交互设计）<br> | <br> |
| 2<br> | UX输出的高保真设计（通常需要展示视觉设计&交互设计）<br> | <br> |

### 数据项描述

<列出有关功能的数据元素，或信息结构。若原型部分已经描述过，可以省略。>

| #<br> | 数据项<br> | 类型<br> | 默认值<br> | 是否必填<br> | 描述<br> |
--- | --- | --- | --- | --- | ---
| 1<br> | 条目管理-批量操作<br> | <br> | <br> | <br> | <br> |
| 2<br> | 开始应答<br> | 按钮<br> | NA<br> | NA<br> | 1、如已勾选条目，则直接提交进行操作，随后刷新条目状态至【应答中】。<br>1.2、 如未勾选条目，提示“是否启动所有条目的应答？”，确认则关闭弹窗刷新条目状态至【应答中】，取消则关闭弹窗。<br>2、点击弹窗的取消按钮，关闭弹窗，刷新页面。<br>3、只可选择【已应答】、【未应答】的条目<br> |
| 3<br> | 批量删除<br> | 按钮<br> | NA<br> | NA<br> | 1、弹窗提示“是否要删除所选条目/全部条目？”<br>1.1、点击确认，则执行删除操作，关闭弹窗，刷新列表。<br>1.2、点击取消，则关闭弹窗。<br> |
| 4<br> | 批量添加标签<br> | 按钮<br> | NA<br> | NA<br> | 1、在弹窗的输入框中输入标签，通过回车确认单个标签，可添加多个，可选取已有标签<br>2、点击弹窗的确认按钮，提交添加的标签。<br>2.1、如已勾选条目，则直接提交进行操作，随后关闭弹窗，刷新页面。<br>2.2、 如未勾选条目，提示“是否给所有条目添加标签？”，确认则提交添加的标签，取消则关闭弹窗。<br>3、点击弹窗的取消按钮，关闭弹窗，刷新页面。<br>4、只可选择【已应答】、【未应答】的条目<br> |
| 5<br> | 批量移除标签<br> | 按钮<br> | NA<br> | NA<br> | 1、弹窗展示条目的全部标签，可选择要删除的标签<br>2、点击弹窗的确认按钮，提示“确认删除勾选的条目/全部条目的XX标签？”<br>2.1、点击确认，则执行删除操作。<br>2.2 、点击取消，则回到标签选取页面。<br>3、只可选择【已应答】、【未应答】的条目<br> |
| 6<br> | 新增产品<br> | 按钮<br> | NA<br> | NA<br> | 1、勾选条目，点击该按钮，如果没有勾选，默认对全部条目进行产品新增。<br>2、打开弹窗，为选择的条目描述新增一条关于所选产品的应答，不能选择已有产品。<br>2.1、当数据源为GBBS时，一级子节点为：SOC标准库目录树、SOC积累库目录树、产品目录树。同时按照用户申请的产品权限进行过滤。<br>2.2、其他场景，应该支持用户手工输入<br>3、点击确认，提示“拟将产品调整至产品B并触发自动应答，请确认是否继续？”<br>3.1 点击确认，则提交操作，并刷新列表。<br>3.2 点击取消，则回到产品选取弹窗。<br> |
| 7<br> | 权限管理<br> | 按钮<br> | NA<br> | NA<br> | 1、点击后需选择设置权限是只读管理还是指派应答。只读无需勾选条目，可以针对只读人员进行添加或删除；指派应答不勾选具体条目则是默认对全部条目进行操作。<br>2、展开选人桥，进行人员选取。<br>3、点击确认，提交指派/只读操作。<br>3.1、如果已勾选条目，则直接提交操作，关闭弹窗，刷新列表。<br>3.2、如果未勾选条且是【指派应答】，则提示“请确认是否将所有条目指派给XXX？”，如点击确认，则提交操作，关闭弹窗刷新列表，点击取消，则关闭弹窗。<br>3.3 如果是授权只读，则直接提交操作，关闭弹窗，刷新列表。<br> |
| 8<br> | 导出<br> | 按钮<br> | NA<br> | NA<br> | 1、点击后，展开弹窗进行要导出的产品进行选取，默认导出全部<br>2、可选的产品基于导出条目进行统计得出<br>3、点击下载，则进行文件导出，可停留在弹窗继续进行下载<br>4、点击取消，关闭弹窗。<br> |
| 9<br> | 条目管理-单条录入<br> | | | | |
| 10<br> | 编号<br> | 文本框<br> | 默认为空<br> | 是<br> | 条目的原始编号<br> |
| 11<br> | 条目描述<br> | 文本框<br> | 默认为空<br> | 是<br> | 条目描述内容<br>编码对应的条目描述必须唯一<br> |
| 12<br> | 产品<br> | 搜索下拉框<br> | 默认为空<br> | 否<br> | 1、当数据源为GBBS时为必填，一级子节点为：SOC标准库目录树、SOC积累库目录树、产品目录树。同时按照用户申请的产品权限进行过滤。<br>2、其他场景，应该支持用户手工输入<br>3、弹窗选取调整后的产品，无法选取到勾选的条目包含的产品。<br>4、用户输入后，搜索匹配到的产品，下拉框展示。<br> |
| 13<br> | 指派给<br> | 搜索框<br> | 默认为自己<br> | 否<br> | 可指定谁负责该条目的应答<br> |
| 14<br> | 自动应答<br> | 勾选框<br> | 是<br> | 是<br> | 1、枚举值：是/否<br>2、如果为是，则自动匹配数据源数据进行应答<br>3、如果为否，则仅录入条目，不进行应答<br> |
| 15<br> | 备注<br> | 文本框<br> | 默认为空<br> | 否<br> | 条目其他事项说明<br> |
| 16<br> | 重复时覆盖<br> | 勾选框<br> | 是<br> | 是<br> | 应答（条目编码+产品）的维度不能重复<br>1、枚举值：是/否<br>2、如果为是，则覆盖原应答<br>3、如果为否，则提示：“条目：#{编码}基于#{产品}已存在，请修改后再试！”<br> |
| 17<br> | 提交<br> | 按钮<br> | NA<br> | NA<br> | 点击后，执行单条录入操作，执行成功后关闭新增录入弹窗，并刷新条目列表。<br> |
| 18<br> | 取消<br> | 按钮<br> | NA<br> | NA<br> | 点击后，关闭新增录入弹窗。<br> |
| 19<br> | 条目管理-批量导入<br> | | | | |
| 20<br> | 导入文件<br> | xls、xlsx<br> | 空<br> | 是<br> | 1、条目字段规则同新增录入：编码、条目描述、产品、指派给、自动应答、备注、重复时覆盖<br>2、目前仅支持导入Excel<br>3、如果重复时覆盖为是，则批量覆盖原条目<br>4、如果重复时覆盖为否，则跳过重复条目，并在导入结束后提示：“条目：#{编码（存在多个则使用英文逗号分隔）}已存在，请修改后再试！”<br>5、如果导入的条目已有标签，则不再为其生成标签，基于已有标签进行相似度分析。<br>6、没有填必填项的，当前即使填了自动应答，也不会进行自动应答。<br>7、编码对应的条目描述必须唯一<br> |
| 21<br> | 下载模板<br> | 按钮<br> | NA<br> | NA<br> | 点击可下载导入模板<br> |
| 22<br> | 提交<br> | 按钮<br> | NA<br> | NA<br> | 1、点击后，执行条目批量导入操作，执行成功后关闭新增录入弹窗，并刷新条目列表。<br>2、如果导入失败，如果是列表填值有问题，必须把异常信息提示完全，如必填项没有填等等；如果是其他问题，则立即返回错误信息。<br><br> |
| 23<br> | 取消<br> | 按钮<br> | NA<br> | NA<br> | 点击后，关闭新增录入弹窗。<br> |
| 24<br> | 条目管理-列表查询条件<br> | | | | |
| 25<br> | 编号<br> | 文本输入框<br> | 空<br> | 否<br> | 精确查询<br> |
| 26<br> | 条目描述<br> | 文本输入框<br> | 空<br> | 否<br> | 模糊查询<br> |
| 27<br> | 产品<br> | 下拉框<br> | 空<br> | 空<br> | 精确查询<br> |
| 28<br> | 应答状态<br> | 下拉框<br> | 空<br> | 否<br> | 精确查询<br> |
| 29<br> | 标签<br> | 文本输入框<br> | 空<br> | 否<br> | 精确查询<br> |
| 30<br> | 应答<br> | 下拉框<br> | 空<br> | 否<br> | 精确查询<br> |
| 31<br> | 指派给<br> | 下拉搜索框<br> | 空<br> | 否<br> | 精确查询，能够选取到所有已指派的人<br> |
| 32<br> | 应答方式<br> | 下拉框<br> | 空<br> | 否<br> | 精确查询<br> |
| 33<br> | 应答来源<br> | 下拉框<br> | 空<br> | 否<br> | 精确查询<br> |
| 34<br> | 条目管理-条目列表<br> | | | | |
| 35<br> | 编号<br> | 文本<br> | NA<br> | NA<br> | 编号，用于生成大纲标题编号<br> |
| 36<br> | 条目描述<br> | 文本<br> | NA<br> | NA<br> | 条目描述，用于生成大纲标题<br> |
| 37<br> | 标签<br> | 文本<br> | NA<br> | NA<br> | 标签，用于进行相似度分析分组<br> |
| 38<br> | 应答状态<br> | 枚举<br> | NA<br> | NA<br> | 条目应答情况<br>未应答：还未开始应答<br>应答中：条目下还存在未完成应答的产品<br>已应答：条目下所有产品均已完成了应答<br> |
| 39<br> | 产品<br> | 文本<br> | NA<br> | NA<br> | 源自任务的产品要素<br> |
| 40<br> | 应答<br> | 枚举<br> | NA<br> | NA<br> | 1、应答结果的满足度，源自数据源匹配结果<br>2、可人工修改<br> |
| 41<br> | 应答说明<br> | 富文本<br> | NA<br> | NA<br> | 应答的结果，可插入图片<br> |
| 42<br> | 索引<br> | 链接/文件<br> | NA<br> | NA<br> | 应答结果的出处，根据数据源的不同，展示样式不同<br>1、GBBS：GBBS的条目详情地址，点击可跳转<br>2、文档：文档名称-章节/页码<br> |
| 43<br> | 备注<br> | 文本<br> | NA<br> | NA<br> | <br> |
| 44<br> | 条目管理-条目列表-操作<br> | | | | |
| 45<br> | 手工应答<br> | 按钮<br> | NA<br> | NA<br> | 点击进入条目详情页面，可进行应答结果处理和查询，见功能5：<br> |
| 46<br> | AI应答<br> | 按钮<br> | NA<br> | NA<br> | 1、状态为【未应答】、【已应答】的条目可见，可执行该动作<br>2、点击后进入状态：【应答中】<br> |
| 47<br> | 编辑<br> | 按钮<br> | NA<br> | NA<br> | 1、在列表支持对【应答】、【应答说明】、【备注】这三个字段进行编辑<br>2、操作栏【编辑】按钮消失，新增【保存】、【取消】这两个按钮。<br> |
| 48<br> | 删除<br> | 按钮<br> | NA<br> | NA<br> | 1、点击后向用户确认<br> |

### 国际化命名规则

| #<br> | 使用场景说明<br> | 中文<br> | 英文<br> |
--- | --- | --- | ---
| 1<br> | <br> | <br> | <br> |

### 埋点定义(选填)

<产品完成后用户行为收集分析埋点，方便后期产品运维和用户行为分析>

| #<br> | 模块<br> | 指标名称<br> | 指标定义<br> | PC/移动端<br> | 触发时机<br> | 频率<br> |
--- | --- | --- | --- | --- | --- | ---
| <br> | <br> | <br> | <br> | <br> | <br> | <br> |

### 验收准则(必填)

<验收准则写法请参考：用户满意条件（验收准则）的写法，主要推荐使用下面的情境式写法 Scenario-based>

| #<br> | Given<br> | When<br> | Then<br> | 是否需要自动化测试用例维护<br> |
--- | --- | --- | --- | ---
| 1<br> | 用户进入任务详情页面<br> | 点击条目管理页签<br> | 显示条目管理界面，包含条目列表和批量操作按钮<br> | 是<br> |
| 2<br> | 用户在条目管理页面<br> | 点击单条录入按钮<br> | 弹出条目录入对话框，包含所有必填字段<br> | 是<br> |
| 3<br> | 用户上传合法的Excel文件<br> | 执行批量导入操作<br> | 条目成功导入，列表显示新增条目，状态根据自动应答设置确定<br> | 是<br> |
| 4<br> | 用户选择未应答条目<br> | 点击开始应答按钮<br> | 选中条目状态变为"应答中"，开始AI自动应答流程<br> | 是<br> |
| 5<br> | 用户在条目列表中<br> | 点击条目的编辑按钮<br> | 该行变为编辑模式，显示保存和取消按钮<br> | 是<br> |
| 6<br> | 任务创建人在条目管理页面<br> | 执行权限管理操作<br> | 可以指派条目给其他用户或设置只读权限<br> | 是<br> |
| 7<br> | 条目状态为"应答中"<br> | 用户尝试编辑该条目<br> | 编辑按钮不可用，提示条目正在应答中<br> | 是<br> |
| 8<br> | 用户选择多个条目<br> | 执行批量添加标签操作<br> | 弹出标签添加对话框，可添加多个标签<br> | 是<br> |

## 2.5 功能3：任务详情-数据分析

#### 

### 场景描述：用户创建任务后开始进行条目应答作业

<介绍功能1的使用场景，说清楚功能即可，不要求必须要按照下面格式编写，但建议包含下面提到的信息。>

用户角色：SOC智能应答-普通用户

前置条件：任务已创建，用户是该任务的创建人或条目的【指派给】

基本事件流程

N0010 用户进入数据分析页面，可看到条目的总体进展，【总条目数】、【已应答数】、【未应答数】、【应答中数】、【应答完成率】、【FC】、【PC】、【NC】、【满足度】。

1）权限同任务详情-条目管理

N0020 同时根据产品维度细分，通过列表展示各个产品的条目完成情况。列表展示字段：【产品】、【总条目数】、【已应答数】、【FC】、【PC】、【NC】、【满足度】。

1）满足度计算公式：(所有FC数量 + 所有PC数量) / (所有条目总数) × 100%

异常事件流程

N0010 用户不具备【SOC智能应答技术对标用户】权限，无法看到菜单【任务管理】，如果通过链接进入页面，页面仅展示“抱歉，您没有相关权限，如需进行SOC智能应答，请前往IT网站-业务申请-应用系统权限-应用系统权限申请-投标智能体-SOC智能应答用户下申请角色：SOC智能应答-普通用户”换行，下面展示“点击此处申请权限”。

N0020 用户不具备该任务相关的权限，如果通过链接进入页面，则提示“抱歉，您不具备该任务查看权限！”

N0030 结束。

### 界面原型(选填)

| #<br><br> | 设计类型<br> | 链接/图示/资料<br> |
--- | --- | ---
| 1<br> | BA输出的低保真设计（通常仅展示交互设计）<br> | <br> |
| 2<br> | UX输出的高保真设计（通常需要展示视觉设计&交互设计）<br> | <br> |

### 数据项描述

<列出有关功能的数据元素，或信息结构。若原型部分已经描述过，可以省略。>

| #<br> | 数据项<br> | 类型<br> | 默认值<br> | 是否必填<br> | 描述<br> |
--- | --- | --- | --- | --- | ---
| 1<br> | 总条目数<br> | 整数<br> | 0<br> | NA<br> | 同任务详情的权限管理<br> |
| 2<br> | 已应答数<br> | 整数<br> | 0<br> | NA<br> | 状态为【已应答】的条目数量<br> |
| 3<br> | 未应答数<br> | 整数<br> | 0<br> | NA<br> | 状态为【未应答】的条目数量<br> |
| 4<br> | 应答中数<br> | 整数<br> | 0<br> | NA<br> | 状态为【中应答】的条目数量<br> |
| 5<br> | 应答完成率<br> | 百分比<br> | 0%<br> | NA<br> | 已应答数/总条目数<br> |
| 6<br> | FC<br> | 整数<br> | 0<br> | NA<br> | 满足度为【FC】的条目数量<br> |
| 7<br> | PC<br> | 整数<br> | 0<br> | NA<br> | 满足度为【PC】的条目数量<br> |
| 8<br> | NC<br> | 整数<br> | 0<br> | NA<br> | 满足度为【NC】的条目数量<br> |
| 9<br> | 满足度<br> | 百分比<br> | 0%<br> | NA<br> | (所有FC数量 + 所有PC数量) / (所有条目总数) × 100%<br> |

### 国际化命名规则

| #<br> | 使用场景说明<br> | 中文<br> | 英文<br> |
--- | --- | --- | ---
| 1<br> | <br> | <br> | <br> |

### 埋点定义(选填)

<产品完成后用户行为收集分析埋点，方便后期产品运维和用户行为分析>

| #<br> | 模块<br> | 指标名称<br> | 指标定义<br> | PC/移动端<br> | 触发时机<br> | 频率<br> |
--- | --- | --- | --- | --- | --- | ---
| <br> | <br> | <br> | <br> | <br> | <br> | <br> |

### 验收准则(必填)

<验收准则写法请参考：用户满意条件（验收准则）的写法，主要推荐使用下面的情境式写法 Scenario-based>

| #<br> | Given<br> | When<br> | Then<br> | 是否需要自动化测试用例维护<br> |
--- | --- | --- | --- | ---
| 1<br> | 用户在任务详情页面<br> | 点击数据分析页签<br> | 显示任务的整体统计数据，包括总条目数、应答进度等<br> | 是<br> |
| 2<br> | 任务创建人在数据分析页面<br> | 选择特定指派人进行筛选<br> | 显示该指派人相关的统计数据<br> | 是<br> |
| 3<br> | 普通用户在数据分析页面<br> | 查看统计数据<br> | 只能看到与自己相关的条目统计信息<br> | 是<br> |
| 4<br> | 存在多个产品的条目<br> | 查看产品维度统计<br> | 按产品分组显示统计数据，包括各产品的满足度<br> | 是<br> |
| 5<br> | 任务条目应答状态发生变化<br> | 刷新数据分析页面<br> | 统计数据实时更新，反映最新状态<br> | 是<br> |

## 2.6 功能4：任务详情-条目管理-AI应答

#### 

### 场景描述：用户创建任务后开始进行条目应答作业

<介绍功能1的使用场景，说清楚功能即可，不要求必须要按照下面格式编写，但建议包含下面提到的信息。>

用户角色：SOC智能应答-普通用户

前置条件：任务条目已创建

基本事件流程

N0010 触发自动应答的触点

自动应答触点

| 界面<br> | 场景<br> |
--- | ---
| 快捷应答<br> | 录入待应答的条目<br> |
| 任务管理<br> | 新建任务，导入文件中有条目配置了自动应答<br> |
| 任务详情<br> | 单条录入，勾选了自动应答<br> |
| 任务详情<br> | 批量新增，有条目配置了自动应答<br> |
| 任务详情<br> | 点击开始应答按钮<br> |
| 任务详情<br> | 点击列表操作栏中的AI应答按钮<br> |

N0020 AI应答数据源管理，目前仅支持GBBS。

数据源管理

| 数据源<br> | 数据源描述<br> |
--- | ---
| GBBS<br> | 基于产品、国家、分支、客户、条目描述和GBBS中的工程、售前SOC应答数据进行匹配。<br> |

N0030 匹配度分析

匹配度分析

| 数据源<br> | 匹配规则<br> |
--- | ---
| GBBS<br> | 1、基于产品进行筛选<br>2、基于国家/MTO、运营商/分支、客户、条目描述等维度的信息进行匹配度分析<br>3、条目描述等存在匹配度的百分比<br>4、国家、分支、客户等，可以判断是否匹配，匹配则亮星<br>5、默认选择匹配度最高的一条结果进行展示。<br>并支持在条目详情的匹配详情中查看匹配度最高的几条，以及切换。<br> |

异常事件流程

N0010 用户不具备【SOC智能应答技术对标用户】权限，无法看到菜单【任务管理】，如果通过链接进入页面，页面仅展示“抱歉，您没有相关权限，如需进行SOC智能应答，请前往IT网站-业务申请-应用系统权限-应用系统权限申请-投标智能体-SOC智能应答用户下申请角色：SOC智能应答-普通用户”换行，下面展示“点击此处申请权限”。

N0020 用户不具备该任务相关的权限，如果通过链接进入页面，则提示“抱歉，您不具备该任务查看权限！”

N0030 结束。

### 界面原型(选填)

| #<br><br> | 设计类型<br> | 链接/图示/资料<br> |
--- | --- | ---
| 1<br> | BA输出的低保真设计（通常仅展示交互设计）<br> | <br> |
| 2<br> | UX输出的高保真设计（通常需要展示视觉设计&交互设计）<br> | <br> |

### 数据项描述

<列出有关功能的数据元素，或信息结构。若原型部分已经描述过，可以省略。>

| #<br> | 数据项<br> | 类型<br> | 默认值<br> | 是否必填<br> | 描述<br> |
--- | --- | --- | --- | --- | ---
| 1<br> | <br> | <br> | <br> | <br> | <br> |

### 国际化命名规则

| #<br> | 使用场景说明<br> | 中文<br> | 英文<br> |
--- | --- | --- | ---
| 1<br> | <br> | <br> | <br> |

### 埋点定义(选填)

<产品完成后用户行为收集分析埋点，方便后期产品运维和用户行为分析>

| #<br> | 模块<br> | 指标名称<br> | 指标定义<br> | PC/移动端<br> | 触发时机<br> | 频率<br> |
--- | --- | --- | --- | --- | --- | ---
| <br> | <br> | <br> | <br> | <br> | <br> | <br> |

### 验收准则(必填)

<验收准则写法请参考：用户满意条件（验收准则）的写法，主要推荐使用下面的情境式写法 Scenario-based>

| #<br> | Given<br> | When<br> | Then<br> | 是否需要自动化测试用例维护<br> |
--- | --- | --- | --- | ---
| 1<br> | 条目状态为"未应答"或"已应答"<br> | 点击AI应答按钮<br> | 条目状态变为"应答中"，开始AI匹配流程<br> | 是<br> |
| 2<br> | AI应答流程启动<br> | 系统基于GBBS数据源进行匹配<br> | 返回匹配结果，包含匹配度、应答内容等信息<br> | 是<br> |
| 3<br> | AI应答完成<br> | 系统生成应答结果<br> | 条目状态变为"已应答"，应答方式标记为"AI"<br> | 是<br> |
| 4<br> | 条目配置了自动应答<br> | 条目被导入或录入系统<br> | 自动触发AI应答流程，无需人工干预<br> | 是<br> |
| 5<br> | AI应答基于产品、国家等信息<br> | 系统进行匹配度计算<br> | 返回匹配度百分比和相关匹配要素的标识<br> | 是<br> |

## 2.7 功能5：任务详情-条目管理-人工应答

#### 

### 场景描述：用户创建任务后开始进行条目应答作业

<介绍功能1的使用场景，说清楚功能即可，不要求必须要按照下面格式编写，但建议包含下面提到的信息。>

用户角色：任务创建人/条目【指派给】

前置条件：任务条目已创建。状态为【已应答】、【未应答】，用户点击操作栏中的按钮【人工应答】

基本事件流程

N0010 进入人工应答详情页面中，在页面头展示条目描述、国家、分支、客户等信息，可以选择历史版本；可以查看到应答结果和匹配详情这两个页签，能够切换产品查看该条目相关的其他产品情况。

N0020 用户在应答结果中能够使用AI应答提供补充信息、编辑满足度、编辑应答说明（AI润色、AI翻译）、编辑索引、查看来源、添加备注等操作。

N0030 用户在匹配详情中，可以查看到来自各个数据源的应答匹配情况（每个数据源匹配度的Top50）。

1）可通过满足度、匹配度、数据源进行筛选

2）可选取匹配情况并应用为当前应答结果的。

3）每个匹配情况需展示：条目描述、应答状态、应答说明、索引等信息。

4）每个数据源的匹配情况可以进行展开收起操作。

5）数据源中的匹配情况按照卡片展示，每行展示三个卡片，最多展示三行，点击下一页可查看下一页的匹配情况。

6）在数据源的名称旁边显示总条数，FC、PC、NC这几个应答的数量。

异常事件流程

N0010 用户不具备【SOC智能应答技术对标用户】权限，无法看到菜单【任务管理】，如果通过链接进入页面，页面仅展示“抱歉，您没有相关权限，如需进行SOC智能应答，请前往IT网站-业务申请-应用系统权限-应用系统权限申请-投标智能体-SOC智能应答用户下申请角色：SOC智能应答-普通用户”换行，下面展示“点击此处申请权限”。

N0020 用户不具备该任务相关的权限，如果通过链接进入页面，则提示“抱歉，您不具备该任务查看权限！”

N0030 结束。

### 界面原型(选填)

| #<br><br> | 设计类型<br> | 链接/图示/资料<br> |
--- | --- | ---
| 1<br> | BA输出的低保真设计（通常仅展示交互设计）<br> | <br> |
| 2<br> | UX输出的高保真设计（通常需要展示视觉设计&交互设计）<br> | <br> |

### 数据项描述

<列出有关功能的数据元素，或信息结构。若原型部分已经描述过，可以省略。>

| #<br> | 数据项<br> | 类型<br> | 默认值<br> | 是否必填<br> | 描述<br> |
--- | --- | --- | --- | --- | ---
| 1<br> | 应答结果<br> | | | | |
| 2<br> | 补充信息<br> | 文本输入框<br> | 空<br> | 否<br> | 1、点击【AI应答】可在此处进行信息补充<br>2、用户可参考此处补充信息进行应答<br> |
| 3<br> | 满足度<br> | 下拉框<br> | 空<br> | 否<br> | 1、用户可下拉修改本条目的满足度FC、PC、NC<br> |
| 4<br> | 应答说明<br> | 富文本框<br> | 空<br> | 否<br> | 1、用户可插入图片<br>2、可点击【AI润色】对原应答说明进行修饰，可根据答案选择是否应用，选择应用则填充到应答说明中，基于IGPT的功能实现。<br>3、可点击【AI翻译】对原应答说明进行翻译，基于IGPT的功能实现。<br>4、索引<br>数据源为GBBS时，应当为GBBS对应条目的跳转链接<br> |
| 5<br> | 来源<br> | 文本<br> | 空<br> | 否<br> | 等同于数据源<br> |
| 6<br> | 备注<br> | 文本<br> | 空<br> | 空<br> | 可添加额外的描述<br> |
| 7<br> | 保存<br> | 按钮<br> | NA<br> | NA<br> | 1、保存当前修改的内容<br>2、应答方式置为：手工<br> |
| 8<br> | 重置<br> | 按钮<br> | NA<br> | NA<br> | 当前编辑的内容全部重置回编辑前<br> |
| 9<br> | 匹配详情-查询条件<br> | | | | |
| 10<br> | 满足度<br> | 下拉框<br> | 全部<br> | 是<br> | 枚举：全部、FC、PC、NC<br> |
| 11<br> | 匹配度<br> | 下拉框<br> | 全部<br> | 是<br> | 枚举：全部，≥90%，≥80%，≥70%，<br> |
| 12<br> | 数据源<br> | 下拉框<br> | 全部<br> | 是<br> | 枚举：GBBS<br> |
| 13<br> | 匹配详情-数据源模块（直接以数据源名称展示如GBBS）-单个匹配情况的展示如：总数: 15 FC: 8 PC: 5 NC: 2<br> | | | | |
| 14<br> | 匹配度<br> | 百分比<br> | 0%<br> | NA<br> | 基于实现方式得出的匹配度<br> |
| 15<br> | 国家/分支/客户<br> | 匹配情况<br> | ☆☆☆<br> | NA<br> | 每满足一个要素则亮一颗心，如★★★<br> |
| 16<br> | 条目描述<br> | 文本<br> | NA<br> | NA<br> | 来自数据源的条目描述<br> |
| 17<br> | 满足度<br> | 文本<br> | NA<br> | NA<br> | 来自数据源的满足度<br> |
| 18<br> | 应答说明<br> | 文本<br> | NA<br> | NA<br> | 来自数据源的应答说明<br> |
| 19<br> | 索引<br> | 文本<br> | NA<br> | NA<br> | 来自数据源的索引<br>1、数据源为GBBS时，应当为GBBS对应条目的跳转链接<br> |
| 20<br> | 应用<br> | 按钮<br> | NA<br> | NA<br> | 1、点击按钮，提示“将覆盖当前应答结果，请确认是否继续！”<br>2、点确认，则覆盖当前的应答结果，并且应答方式置为AI<br>2、点取消，则不执行该操作。<br> |

### 国际化命名规则

| #<br> | 使用场景说明<br> | 中文<br> | 英文<br> |
--- | --- | --- | ---
| 1<br> | <br> | <br> | <br> |

### 埋点定义(选填)

<产品完成后用户行为收集分析埋点，方便后期产品运维和用户行为分析>

| #<br> | 模块<br> | 指标名称<br> | 指标定义<br> | PC/移动端<br> | 触发时机<br> | 频率<br> |
--- | --- | --- | --- | --- | --- | ---
| <br> | <br> | <br> | <br> | <br> | <br> | <br> |

### 验收准则(必填)

<验收准则写法请参考：用户满意条件（验收准则）的写法，主要推荐使用下面的情境式写法 Scenario-based>

| #<br> | Given<br> | When<br> | Then<br> | 是否需要自动化测试用例维护<br> |
--- | --- | --- | --- | ---
| 1<br> | 用户在条目列表中<br> | 点击人工应答按钮<br> | 打开人工应答详情页面，显示条目信息和应答结果页签<br> | 是<br> |
| 2<br> | 用户在应答结果页签<br> | 编辑应答说明并保存<br> | 应答内容更新，应答方式标记为"手工"<br> | 是<br> |
| 3<br> | 用户在应答结果页签<br> | 点击AI润色按钮<br> | 调用IGPT服务，返回润色后的文本供用户选择<br> | 是<br> |
| 4<br> | 用户在匹配详情页签<br> | 查看各数据源的匹配情况<br> | 按数据源分组展示匹配结果，包含匹配度和详细信息<br> | 是<br> |
| 5<br> | 用户选择某个匹配结果<br> | 点击应用按钮<br> | 弹出确认对话框，确认后覆盖当前应答结果<br> | 是<br> |
| 6<br> | 用户在匹配详情中<br> | 使用筛选条件进行过滤<br> | 列表显示符合筛选条件的匹配结果<br> | 是<br> |
| 7<br> | 用户修改应答后<br> | 点击重置按钮<br> | 所有编辑内容恢复到编辑前状态<br> | 是<br> |

## 2.8 功能6：快捷应答

#### 

### 场景描述：用户开始/查看SOC智能应答任务情况

<介绍功能1的使用场景，说清楚功能即可，不要求必须要按照下面格式编写，但建议包含下面提到的信息。>

用户角色：SOC智能应答-普通用户

前置条件：用户已申请SOC智能应答-普通用户权限

基本事件流程

N0010 用户点击快捷应答菜单，填写数据源、产品等必填数据后即可开始输入条目进行应答，也可补充国家、分支、客户等信息。

N0020 输入条目进行应答后，会跳转至自动创建的任务（个人任务）详情中进行自动应答。

N0030 通过快捷应答创建的应答条目都会放置在个人任务区，个人任务区无法重复，个人任务除了无法被删除外，和其他任务区没有区别。

异常事件流程

N0010 用户不具备【SOC智能应答技术对标用户】权限，无法看到菜单【快捷应答】，如果通过链接进入页面，页面仅展示“抱歉，您没有相关权限，如需进行SOC智能应答，请前往IT网站-业务申请-应用系统权限-应用系统权限申请-投标智能体-SOC智能应答用户下申请角色：SOC智能应答-普通用户”换行，下面展示“点击此处申请权限”。

N0020 结束。

### 界面原型(选填)

| #<br><br> | 设计类型<br> | 链接/图示/资料<br> |
--- | --- | ---
| 1<br> | BA输出的低保真设计（通常仅展示交互设计）<br> | <br> |
| 2<br> | UX输出的高保真设计（通常需要展示视觉设计&交互设计）<br> | <br> |

### 数据项描述

<列出有关功能的数据元素，或信息结构。若原型部分已经描述过，可以省略。>

| #<br> | 数据项<br> | 类型<br> | 默认值<br> | 是否必填<br> | 描述<br> |
--- | --- | --- | --- | --- | ---
| 1<br> | <br> | <br> | <br> | <br> | <br> |

### 国际化命名规则

| #<br> | 使用场景说明<br> | 中文<br> | 英文<br> |
--- | --- | --- | ---
| 1<br> | <br> | <br> | <br> |

### 埋点定义(选填)

<产品完成后用户行为收集分析埋点，方便后期产品运维和用户行为分析>

| #<br> | 模块<br> | 指标名称<br> | 指标定义<br> | PC/移动端<br> | 触发时机<br> | 频率<br> |
--- | --- | --- | --- | --- | --- | ---
| <br> | <br> | <br> | <br> | <br> | <br> | <br> |

### 验收准则(必填)

<验收准则写法请参考：用户满意条件（验收准则）的写法，主要推荐使用下面的情境式写法 Scenario-based>

| #<br> | Given<br> | When<br> | Then<br> | 是否需要自动化测试用例维护<br> |
--- | --- | --- | --- | ---
| 1<br> | 用户具备SOC智能应答-普通用户权限<br> | 用户点击快捷应答菜单<br> | 进入快捷应答页面，显示必填和选填字段<br> | 是<br> |
| 2<br> | 用户在快捷应答页面<br> | 页面加载完成<br> | 数据源和产品字段标记为必填，国家、分支、客户为选填<br> | 是<br> |
| 3<br> | 用户填写必填信息<br> | 用户输入条目描述<br> | 条目输入框可正常输入文本<br> | 是<br> |
| 4<br> | 用户填写完必填信息和条目<br> | 用户点击开始应答<br> | 系统自动创建个人任务并跳转到任务详情页面<br> | 是<br> |
| 5<br> | 系统创建个人任务<br> | 条目进入自动应答流程<br> | 条目状态变为"应答中"，开始执行AI应答<br> | 是<br> |
| 6<br> | 通过快捷应答创建的任务<br> | 用户查看任务列表<br> | 任务显示在个人任务区，标识为个人任务<br> | 是<br> |
| 7<br> | 个人任务已创建<br> | 用户尝试删除个人任务<br> | 个人任务无法被删除，删除按钮不可用<br> | 是<br> |
| 8<br> | 个人任务存在<br> | 用户对个人任务进行其他操作<br> | 除删除外的其他操作（编辑、复制、应答）与普通任务相同<br> | 是<br> |
| 9<br> | 用户不具备相关权限<br> | 用户尝试访问快捷应答<br> | 显示权限不足提示信息和申请权限链接<br> | 是<br> |
| 10<br> | 用户填写信息不完整<br> | 用户点击开始应答<br> | 提示必填字段未填写，阻止提交<br> | 是<br> |

## 2.9 功能7：Agent人机交互

#### 

### 场景描述：用户创建任务后开始进行条目应答作业

<介绍功能1的使用场景，说清楚功能即可，不要求必须要按照下面格式编写，但建议包含下面提到的信息。>

用户角色：SOC智能应答-普通用户

前置条件：用户已申请权限

基本事件流程

N0010 Agent场景

Agent场景

| Tools<br> | 场景<br> | 能力描述<br> | 示例<br> |
--- | --- | --- | ---
| 创建任务<br> | 创建任务<br> | 支持直接通过自然语言创建任务，自动解析任务名称、国家、MTO分支、客户<br> | “开始中国电信的招标任务1”<br> |
| 查询<br> | 查询任务<br> | 支持使用自然语言查询与用户相关的任务信息<br> | “和我有关的任务有哪些”—— 可以在用户进入SOC智能应答Agent时自动查询，提供一个任务列表<br>“当前任务XXX有指派给我”<br> |
| | 查询条目<br> | 支持使用自然语言查询与用户相关的任务下的条目信息<br> | “任务XXX下XX条目的应答情况”<br> |
| | 数据分析<br> | 查看任务的应答进度、满足度情况，可基于个人维度和产品维度进行分析<br> | “任务XXX的应答情况如何？”<br>“任务XXX的数据分析”<br> |
| 应答<br> | 应答单条目<br> | 支持使用自然语言应答与用户相关的任务下的条目<br>如果用户没有指定任务，则默认在个人任务区进行应答<br> | “基于任务XXX回答：XXXX”<br>“基于产品回答问题1”<br> |
| | 批量应答<br> | 支持使用自然语言批量应答与用户相关的任务下的已有条目<br> | “批量应答任务的条目”<br> |
| 导入<br> | Excel导入<br> | 支持用户上传Excel文件，并实时展示进度<br> | 用户上传Excel文件<br>“请确定您要导入的任务”，展示近10条最新的任务，支持用户输入。<br>“任务1”<br>“好的，任务已确定，文件解析中，进度80%”<br>“文件导入完毕，前往查看”，点击卡片/链接可进入SOC智能应答应用查看应答情况<br> |
| 导出<br> | 导出相关任务的条目应答情况<br> | 支持用户导出指定任务的符合筛选条件的条目应答情况<br> | “请下载任务1的应答情况”<br> |
| 批量操作<br> | 指派给<br> | 支持用户将自己有权限查询到的任务的条目指派给指定的人<br> | “把任务1中与产品1有关的应答指派给张三”<br> |

### 界面原型(选填)

| #<br><br> | 设计类型<br> | 链接/图示/资料<br> |
--- | --- | ---
| 1<br> | BA输出的低保真设计（通常仅展示交互设计）<br> | <br> |
| 2<br> | UX输出的高保真设计（通常需要展示视觉设计&交互设计）<br> | <br> |

### 数据项描述

<列出有关功能的数据元素，或信息结构。若原型部分已经描述过，可以省略。>

| #<br> | 数据项<br> | 类型<br> | 默认值<br> | 是否必填<br> | 描述<br> |
--- | --- | --- | --- | --- | ---
| 1<br> | <br> | <br> | <br> | <br> | <br> |

### 国际化命名规则

| #<br> | 使用场景说明<br> | 中文<br> | 英文<br> |
--- | --- | --- | ---
| 1<br> | <br> | <br> | <br> |

### 埋点定义(选填)

<产品完成后用户行为收集分析埋点，方便后期产品运维和用户行为分析>

| #<br> | 模块<br> | 指标名称<br> | 指标定义<br> | PC/移动端<br> | 触发时机<br> | 频率<br> |
--- | --- | --- | --- | --- | --- | ---
| <br> | <br> | <br> | <br> | <br> | <br> | <br> |

### 验收准则(必填)

<验收准则写法请参考：用户满意条件（验收准则）的写法，主要推荐使用下面的情境式写法 Scenario-based>

| #<br> | Given<br> | When<br> | Then<br> | 是否需要自动化测试用例维护<br> |
--- | --- | --- | --- | ---
| 1<br> | 用户进入Agent交互界面<br> | 发送自然语言指令"开始中国电信的招标任务1"<br> | Agent解析指令并自动创建相应任务<br> | 是<br> |
| 2<br> | 用户向Agent询问任务信息<br> | 发送"和我有关的任务有哪些"<br> | Agent返回用户相关的任务列表<br> | 是<br> |
| 3<br> | 用户请求应答特定条目<br> | 发送"基于任务XXX回答：XXXX"<br> | Agent识别任务和条目，执行应答操作<br> | 是<br> |
| 4<br> | 用户上传Excel文件<br> | Agent提示选择目标任务<br> | 显示最近10条任务供选择，支持自然语言输入<br> | 是<br> |
| 5<br> | 用户请求数据分析<br> | 发送"任务XXX的应答情况如何？"<br> | Agent返回任务的详细统计分析数据<br> | 是<br> |
| 6<br> | 用户请求批量操作<br> | 发送"把任务1中与产品1有关的应答指派给张三"<br> | Agent执行相应的批量指派操作<br> | 是<br> |
| 7<br> | Agent处理文件导入<br> | 显示处理进度和状态<br> | 实时更新进度，完成后提供查看链接<br> | 是<br> |

# 3 需求内容检查单

<完成产品需求说明书后应根据检查单进行自查，需求编写是否满足以下规范要求。检查人不填默认为文档编辑人，如果团队有专人进行检查则需要填写检查人>

以下内容会用于度量检查，请认真填写，在填写前请先选择部门：选项宏业务运营

| 类型<br><br> | 检查人<br> | 检查结果<br> | 具体描述<br> |
--- | --- | --- | ---
| 懒人原则（必选）<br> | <br> | 选项宏未完成懒人原则检查<br><br> | 检查参考文档：懒人原则设计开发准则-V2.0(发布版)<br> |
| 可编排可配置（必选）<br> | <br> | 选项宏未完成可编排可配置设计检查<br><br> | 参见：可编排可配置设计目标；关注：可编排可配置设计目标，需从长期收益和短期收益综合考虑。<br>不涉及可编排可配置请说明原因说明；<br> |
| 产品安全（必选）<br> | <br> | 选项宏未完成产品安全检查<br><br> | 检查参考文档：产品需求检查单-V1.0<br>特别注意：涉及绝密、机密、个人数据对公司外部系统共享的接口（显示、导出、接口交换），需要走TS评估流程来判断需求是否可以做。<br> |
| 个人数据保护（必选）<br> | <br> | 选项宏未完成个人数据保护检查<br><br> | 在个人数据保护调查问卷及管控要求中识别涉及到个人数据相关的“处理”动作时，需要针对对应涉及到的内容输出个人数据保护需求<br>检查参考文档：个人数据保护合规检查单<br> |
| 性能需求（必选）<br> | <br> | 选项宏涉及性能需求<br> | 参考文档：01 性能通用要求<br> |
| 兼容性（可选）<br> | <br> | 不涉及兼容性需求<br> | 需要有简要描述，举例：支持新版本兼容老版本，移动端和网页端对于不同的操作系统兼容<br> |
| 可维护性（可选）<br> | <br> | 不涉及可维护性需求<br> | 需要有简要描述，举例：后端xxx操作需要有日志信息记录/某后端接口运维操作，需要给出详细功能操作流程文档<br> |
| 可测试性（可选）<br> | <br> | 不涉及可测试性需求<br> | 需要有简要描述，举例：后端xxx操作的结果，需要支持测试可测试，并提供对应的测试和工具<br> |
| 可靠性（可选）<br> | <br> | 不涉及可靠性需求<br> | 需要有简要描述，举例：xxx功能需要支持异地容灾，健壮性<br> |
| 可扩展性（可选）<br> | <br> | 不涉及可扩展性需求<br> | 需要有简要描述，举例：支持模块对外依赖可切换，设计解耦<br> |

# 附：评审纪要模板

<直接正文输出或者拷贝模板输出到评论区>

| 产品需求评审<br> | | | | | | |
--- | --- | --- | --- | --- | --- | ---
| 评审时间<br> | <br> | | | | | |
| 评审地点<br> | <br> | | | | | |
| 评审结论<br> | 请选择<br> | | | | | |
| 参会人员<br><br> | | | | | | |
| 用户代表（必填）<br> | <br> | | | | | |
| 开发代表（必填）<br> | <br> | | | | | |
| 测试代表（必填）<br> | <br> | | | | | |
| 其他（可选）<br> | <br> | | | | | |
| 评审建议<br><br> | | | | | | |
| 建议编号<br> | 建议/问题<br> | 提出人<br> | 是否采纳<br> | 责任人<br> | 是否完成<br> | 备注说明<br> |
| 1<br> | <br> | <br> | <br> | <br> | <br> | <br> |
| 2<br> | <br> | <br> | <br> | <br> | <br> | <br> |
| 3<br> | <br> | <br> | <br> | <br> | <br> | <br> |

点击查看页面浏览者数据