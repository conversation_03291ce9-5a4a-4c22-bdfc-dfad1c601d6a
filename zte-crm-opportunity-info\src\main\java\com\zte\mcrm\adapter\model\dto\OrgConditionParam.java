package com.zte.mcrm.adapter.model.dto;

import java.util.List;

/**
 * <AUTHOR> by 10265625
 * @date 2021/9/16
 * @description 查询组织机构树的条件参数
 */
public class OrgConditionParam {

    /**
     * 组织机构类型 GEBSDC 代表政企 SDT代表三营
     */
    private String orgType;

    /**
     * 是否可见 Y N 如果不传默认查可见和不可见的
     */
    private String isVisible;

    /*
     * 是否业绩单位 Y N
     **/
    private String isPerformanceDept;

    /*
     * 机构id
     **/
    private List<String> orgIds;

    /**
     * 组织状态ID（1有效，2待撤销，0已撤销）
     * 如果不传查所有状态的
     */
    private List<Integer> orgStatusIds;

    /**
     * 数据逻辑删除标识，1标识未删除，0标识已删除
     * 如果不传默认查未删除和已删除的
     */
    private String enabledFlag;

    /**
     * 机构名称 支持按照机构名称模糊搜索
     */
    private String orgName;

    /*
     * 机构标识 1标识总监办 2  标识办事处
     **/
    private String orgLabel;

    /*
     * 页码
     **/
    private Integer pageNum;

    /*
     * 每页条数
     **/
    private Integer pageSize;


    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public String getIsVisible() {
        return isVisible;
    }

    public void setIsVisible(String isVisible) {
        this.isVisible = isVisible;
    }


    public String getIsPerformanceDept() {
        return isPerformanceDept;
    }

    public void setIsPerformanceDept(String isPerformanceDept) {
        this.isPerformanceDept = isPerformanceDept;
    }

    public List<String> getOrgIds() {
        return orgIds;
    }

    public void setOrgIds(List<String> orgIds) {
        this.orgIds = orgIds;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgLabel() {
        return orgLabel;
    }

    public void setOrgLabel(String orgLabel) {
        this.orgLabel = orgLabel;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public void setOrgStatusIds(List<Integer> orgStatusIds) {
        this.orgStatusIds = orgStatusIds;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag;
    }

    public List<Integer> getOrgStatusIds() {
        return orgStatusIds;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    @Override
    public String toString() {
        return "OrgConditionParam{" +
                "orgType='" + orgType + '\'' +
                ", isVisible='" + isVisible + '\'' +
                ", isPerformanceDept='" + isPerformanceDept + '\'' +
                ", orgIds=" + orgIds +
                ", orgStatusIds=" + orgStatusIds +
                ", enabledFlag='" + enabledFlag + '\'' +
                ", orgName='" + orgName + '\'' +
                ", orgLabel='" + orgLabel + '\'' +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                '}';
    }
}
