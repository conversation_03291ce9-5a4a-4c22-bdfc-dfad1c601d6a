package com.zte.mcrm.adapter.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Data
public class ChannelAccountDetailDTO {
    @ApiModelProperty(value = "渠道商id customer_id")
    private String customerId;

    @ApiModelProperty(value = "渠道商名称 customer_name")
    @JsonProperty(value="channelName")
    private String customerName;

    @ApiModelProperty(value = "客户编码 Customer Code")
    private String crmCustomerCode;

    @ApiModelProperty(value = "合作伙伴id  Partner ID")
    private String partnerId;

}
