package com.zte.mcrm.adapter.clouddisk.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.zte.itp.msa.core.config.ICommonConfig;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.clouddisk.config.CloudDiskConfig;
import com.zte.mcrm.adapter.clouddisk.model.EncryptProfileVO;
import com.zte.mcrm.adapter.clouddisk.model.FinishPartObjectVO;
import com.zte.mcrm.adapter.clouddisk.util.JsonUtils;
import com.zte.mcrm.adapter.clouddisk.util.ServiceDataUtil;
import com.zte.mcrm.common.consts.CommonConstant;
import com.zte.mcrm.common.consts.CommonRetCode;
import com.zte.mcrm.common.exception.BusinessRuntimeException;
import com.zte.mcrm.common.util.UploadUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.ByteArrayBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.poi.util.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.Charset;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;

import static com.alibaba.fastjson.JSON.parseObject;
import static com.zte.mcrm.adapter.clouddisk.service.CloudDiskBaseService.FILE_MD5;

/**
 * api主要接口示例service实现类
 * <AUTHOR>
 */
@Service
@Primary
@Getter
@Setter
public class DocCloudServiceImpl implements DocCloudService {

    @Value("${tmp.path}")
    private String tmpPath;
    /**
     * 日志对象
     */
    private static final Logger log = LoggerFactory.getLogger(DocCloudServiceImpl.class);
    public static final String FILE_SEND_PART_DATA = "/file/sendPartData";
    public static final String OBJECTS_FINISH_PART_OBJECT = "/objects/finishPartObject";
    public static final String OBJECTS_DOWNLOAD_TOKEN = "/objects/downloadToken";
    public static final String OBJECTS_PREVIEW_URL = "/api/docview/getPreviewUrl";
    public static final String EMPTY_FILE_NAME = "";
    public static final int CHUNK_SIZE = 2 * 1024 * 1024;
    public static final String UNCONSTRAINT = "-1";

    @Autowired
    private CloudDiskBaseService cloudDiskBaseService;
    @Autowired
    private CloudDiskConfig cloudDiskConfig;
    @Autowired
    private ICommonConfig commonConfig;
    /**
     * 文件上传
     *
     * @param userId 用户名
     * @return 文件Key
     * @throws Exception
     */
    @Override
    public String upload(String userId, MultipartFile file) throws Exception {
        int maxSize = CommonConstant.FILE_SIZE_UNIT * CommonConstant.FILE_SIZE_UNIT * CommonConstant.TWO_HUNDRED;
        if (file != null && file.getSize() > maxSize) {
            throw new BusinessRuntimeException(RetCode.BUSINESSERROR_CODE, CommonRetCode.FILE_SIZE_BEYOND);
        }
        if (null == file) {
            throw new BusinessRuntimeException(RetCode.BUSINESSERROR_CODE, CommonRetCode.VARIABLE_NULL);
        }
        String path = file.getOriginalFilename();
        // 得到去除路径的文件名
        String uploadDif = tmpPath;
        String fileName = path.substring(path.lastIndexOf('\\') + 1);
        InputStream inputStream = file.getInputStream();
        String fileMd5 = DigestUtils.md5Hex(inputStream);
        // 校验是否加密
        validFileEncryption(file, fileMd5);
        File file1 = new File(uploadDif+ path);
        String filePath = file1.getPath();
        file.transferTo(file1);
        // 上传文件
        uploadFile(fileMd5, filePath, userId);
        Thread.sleep(100);
        // 清除
        FileUtils.deleteQuietly(file1);
        // 保存文件数据
        return finishPartObject(fileMd5, fileName, userId);
    }

    private void validFileEncryption(MultipartFile file, String fileMd5) throws IOException, InvalidKeyException, NoSuchAlgorithmException {
        String filePath = UploadUtils.checkFileName(Objects.requireNonNull(file.getOriginalFilename()));
        File fileToCheck = new File(tmpPath + filePath);
        inputStreamToFileWithLimit(file.getInputStream(), fileToCheck, 256);
        // 调用文档云接口校验是否加密
        boolean isEncryption = cloudDiskBaseService.validFileEncryption(fileToCheck, fileMd5);
        if (isEncryption) {
            // 清除
            FileUtils.deleteQuietly(fileToCheck);
            throw new BusinessRuntimeException(RetCode.BUSINESSERROR_CODE, CommonRetCode.FILE_ENCRYPTION);
        }
        // 清除
        FileUtils.deleteQuietly(fileToCheck);
    }

    /**
     * 将给定 InputStream 中的指定长度的数据复制到 File。 limit为-1时为复制所有。
     * 它使两个流都打开，因此完成后您仍然需要关闭它们。
     */
    private void inputStreamToFileWithLimit(InputStream source, File destination, long limit) throws IOException {
        try {
            FileOutputStream outputStream = FileUtils.openOutputStream(destination);
            try{
                copy(source, outputStream, limit);
            }finally {
                IOUtils.closeQuietly(outputStream);
            }
        }finally {
            IOUtils.closeQuietly(source);
        }
    }

    /**
     * Copies all the data from the given InputStream to the OutputStream. It
     * leaves both streams open, so you will still need to close them once done.
     *
     * @param inp The {@link InputStream} which provides the data
     * @param out The {@link OutputStream} to write the data to
     * @param limit limit the copied bytes - use {@code -1} for no limit
     * @return the amount of bytes copied
     *
     * @throws IOException If copying the data fails.
     */
    public static long copy(InputStream inp, OutputStream out, long limit) throws IOException {
        final byte[] buff = new byte[4096];
        long totalCount = 0;
        int readBytes = -1;
        do {
            int todoBytes = (int)((limit < 0) ? buff.length : Math.min(limit-totalCount, buff.length));
            if (todoBytes > 0) {
                readBytes = inp.read(buff, 0, todoBytes);
                if (readBytes > 0) {
                    out.write(buff, 0, readBytes);
                    totalCount += readBytes;
                }
            }
        } while (readBytes >= 0 && (limit == -1 || totalCount < limit));

        return totalCount;
    }

    /**
     * 服务器端分片上传
     *
     * @param fileMd5  文件上传标识
     * @param filePath 文件路径
     * @param userId   工号
     * @throws Exception
     */
    @Override
    public void uploadFile(String fileMd5, String filePath, String userId) throws Exception {
        int chunkSize = CHUNK_SIZE;
        File diskFile = new File(filePath);

        int chunkNum = (int) diskFile.length() / chunkSize + (diskFile.length() % chunkSize == 0 ? 0 : 1);
        try (InputStream inputStream = FileUtils.openInputStream(diskFile)) {
            for (int i = 0; i < chunkNum; i++) {
                int fileSize = i == chunkNum - 1 ? (int) diskFile.length() % chunkSize : chunkSize;
                uploadFileInner(fileMd5, userId, fileSize, inputStream, i);
            }
        }
    }

    /**
     * 分片上传文件
     *
     * @param fileMd5
     * @param userId
     * @param fileSize
     * @param inputStream
     * @param chunk
     * @throws Exception
     */
    public void uploadFileInner(String fileMd5, String userId, int fileSize, InputStream inputStream, int chunk) throws Exception {
        File temp = writePartDataToFile(inputStream, fileMd5 + "-" + chunk, fileSize);
        MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create().setCharset(Charset.defaultCharset()).setLaxMode();
        multipartEntityBuilder.addBinaryBody("file", temp);
        multipartEntityBuilder.addTextBody("chunk", String.valueOf(chunk));
        multipartEntityBuilder.addTextBody(FILE_MD5, fileMd5);
        HttpEntity httpEntity = multipartEntityBuilder.build();
        try {
            String url = cloudDiskConfig.getUrl() + FILE_SEND_PART_DATA;
            String response = cloudDiskBaseService.send(url, httpEntity, userId);
            ServiceData<Boolean> sd = JsonUtils.parseObject(response, new TypeReference<ServiceData<Boolean>>() {
            });
            if (null == sd || BooleanUtils.isFalse(sd.getBo())) {
                throw new BusinessRuntimeException("response of sending part data error" + response);
            }
        } catch (Exception e) {
            log.error("send part data: fileMd5:{} chunk:{} error:{}", fileMd5, chunk, e.getMessage(), e);
            throw new BusinessRuntimeException("send part data error");
        } finally {
            FileUtils.deleteQuietly(temp);
        }
    }

    /**
     * 文件分片
     *
     * @param inputStream
     * @param fileName
     * @param fileSize
     * @return
     * @throws Exception
     */
    private File writePartDataToFile(InputStream inputStream, String fileName, int fileSize) throws Exception {

        String legalFileName = getLegalFilePath(fileName);
        File temp = File.createTempFile(legalFileName, "tmp", null);
        byte[] bytes = new byte[fileSize];
        try (OutputStream os = new FileOutputStream(temp)) {
            int readSize = inputStream.read(bytes, 0, fileSize);
            os.write(new byte[0]);
            os.flush();
            if (readSize > 0) {
                os.write(bytes, 0, readSize);
                os.flush();
            }
        }
        return temp;
    }

    private static String getLegalFilePath(String orignalFilePath){
        if (orignalFilePath.contains(CommonConstant.FILEPATH_1)||orignalFilePath.contains(CommonConstant.FILEPATH_2))
        {
            orignalFilePath = "";
        }
        return orignalFilePath;
    }

    /**
     * 保存上传文件
     *
     * @param fileMd5  文件上传标识
     * @param fileName 文件名
     * @param userId   工号
     * @return
     * @throws Exception
     */
    public String finishPartObject(String fileMd5, String fileName, String userId) throws Exception {
        MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
        multipartEntityBuilder.addTextBody(FILE_MD5, fileMd5);
        ContentType contentType = ContentType.create("text/plain", Consts.UTF_8);
        multipartEntityBuilder.addTextBody("fileName", fileName, contentType);
        HttpEntity httpEntity = multipartEntityBuilder.build();
        try {
            String url = cloudDiskConfig.getUrl() + OBJECTS_FINISH_PART_OBJECT;
            String response = cloudDiskBaseService.send(url, httpEntity, userId);
            ServiceData<FinishPartObjectVO> sd = JsonUtils.parseObject(response, new TypeReference<ServiceData<FinishPartObjectVO>>() {
            });
            if (ServiceDataUtil.isFailed(sd)) {
                throw new BusinessRuntimeException("response of finishing part object error:" + response);
            }
            return sd.getBo().getKey();
        } catch (Exception e) {
            log.error("send part data: fileMd5:{} fileName:{} error:{}", fileMd5, fileName, e.getMessage(), e);
            throw new BusinessRuntimeException("send part data error");
        }
    }

    @Override
    public String getDownloadLink(String docKey, String userId, String downloadNum, boolean httpsFlag) throws Exception {
        String downloadToken = getDownloadToken(docKey, EMPTY_FILE_NAME, userId, downloadNum);
        return cloudDiskBaseService.getDownloadLink(downloadToken, httpsFlag);
    }

    @Override
    public Map<String, String> getDownloadLinks(List<String> dmeKeys, String userId, String onceTime) throws Exception {
        Map<String,String> downLoadLinkMap = Maps.newHashMap();
        for (String dmeKey : dmeKeys) {
            String downloadToken = getDownloadToken(dmeKey, EMPTY_FILE_NAME, userId, onceTime);
            String downloadLink = cloudDiskBaseService.getDownloadLink(downloadToken,true);
            downLoadLinkMap.put(dmeKey,downloadLink);
        }
        return downLoadLinkMap;
    }

    /**
     * 下载文档云文件
     *
     * @param docKey 文档云文件key值
     * @param userId 用户Id
     * @return 浏览器下载
     * @throws Exception
     */
    @Override
    public ByteArrayBody download(String docKey, String userId) throws Exception {
        String downLink = getDownloadLink(docKey, userId, UNCONSTRAINT, false);
        boolean isMultiple = docKey.split(CommonConstant.COMMA).length > 1;
        return cloudDiskBaseService.getFile(downLink, userId, isMultiple);

    }

    /**
     * 获取下载token
     *
     * @param docKey   文件key
     * @param fileName 文件名
     * @param userId   工号
     * @return 返回下载Token值
     * @throws Exception
     */
    private String getDownloadToken(String docKey, String fileName, String userId, String downloadNum) throws Exception {
        List<EncryptProfileVO> profileList = new ArrayList<EncryptProfileVO>();
        Arrays.stream(docKey.split(CommonConstant.COMMA)).forEach(e -> {
            EncryptProfileVO encryptProfil = new EncryptProfileVO();
            encryptProfil.setUseId(userId);
            encryptProfil.setKey(e);
            encryptProfil.setEncrypEnable(false);
            profileList.add(encryptProfil);
        });
        String encryptProfile = JsonUtils.toJSONString(profileList);

        MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
        multipartEntityBuilder.addTextBody("key", docKey);
        ContentType contentType = ContentType.create("text/plain", Consts.UTF_8);
        multipartEntityBuilder.addTextBody("downloadName", fileName, contentType);
        //链接可下载次数，默认为1。大于0则为有限次下载次数，传-1值则不限制下载次数
        multipartEntityBuilder.addTextBody("limitCount", downloadNum);
        multipartEntityBuilder.addTextBody("encryptProfile", encryptProfile);
        HttpEntity httpEntity = multipartEntityBuilder.build();
        try {
            String url = cloudDiskConfig.getUrl() + OBJECTS_DOWNLOAD_TOKEN;
            String response = cloudDiskBaseService.send(url, httpEntity, userId);

            ServiceData<String> sd = JsonUtils.parseObject(response, new TypeReference<ServiceData<String>>() {
            });
            if (ServiceDataUtil.isFailed(sd)) {
                throw new BusinessRuntimeException("response of getting download-token error:" + response);
            }
            return sd.getBo();
        } catch (Exception e) {
            log.error("userId: {} get download-link of key: {} error: {}", userId, docKey, e.getMessage(), e);
            throw new BusinessRuntimeException("getting download link error");
        }
    }

    /**
     * 文件预览
     *
     * @param docKey   文档云key
     * @param fileName 文件名
     * @return String
     * <AUTHOR>
     * @date 2021/7/29
     */
    @Override
    public String previewFile(String docKey, String fileName) {
        String url = getPreviewUrl();
        HttpPost httpPost = new HttpPost(url);
        //获取请求参数
        Map<String,String> paramsMap = retrieveRequestEntity(docKey,fileName);
        try (CloseableHttpClient httpClient = HttpClientBuilder.create().build()) {
            httpPost.setEntity(new StringEntity(JSON.toJSONString(paramsMap), ContentType.APPLICATION_JSON));
            CloseableHttpResponse execute = httpClient.execute(httpPost);
            String response = EntityUtils.toString(execute.getEntity());
            JSONObject jsonObject = parseObject(response);
            String code = jsonObject.getString("Code");
            if (!Objects.equals(RetCode.SUCCESS_CODE, code)) {
                log.error("An error occurred when accessing the url:{},response:{}", url, response);
                throw new BusinessRuntimeException(RetCode.BUSINESSERROR_CODE, CommonRetCode.DOC_CLOUD_SERVICE_ERROR);
            }
            return cloudDiskConfig.getMoaUrl() + jsonObject.getString("Bo");
        } catch (IOException e) {
            log.error("An error occurred when accessing the url:{},docKey:{}", url, docKey);
            throw new BusinessRuntimeException(RetCode.BUSINESSERROR_CODE, CommonRetCode.DOC_CLOUD_SERVICE_ERROR);
        }
    }

    /**
     * 获取预览接口
     *
     * @return 预览接口url
     * <AUTHOR>
     * @date 2021/7/30
     */
    private String getPreviewUrl() {
        return cloudDiskConfig.getMoaUrl() + OBJECTS_PREVIEW_URL;
    }

    /**
     * 获取请求参数Map
     *
     * @param docKey   文档云key
     * @param fileName 文件名
     * @return Map集合
     * <AUTHOR>
     * @date 2021/8/5
     */
    private Map<String,String> retrieveRequestEntity(String docKey, String fileName){
        Map<String,String> paramsMap = Maps.newHashMap();
        String userId = commonConfig.getTokenMap().get(SysGlobalConst.SYS_CODE);
        paramsMap.put("uid", userId);
        paramsMap.put("fileId", docKey);
        paramsMap.put("fileName", fileName);
        paramsMap.put("sysCode", CommonConstant.PRM_SYS_CODE);
        String fileUrl;
        try {
            fileUrl = getDownloadLink(docKey, userId, UNCONSTRAINT, true);
        } catch (Exception e) {
            log.error("userId: {} get download-link of key: {} error: {}", userId, docKey, e.getMessage(), e);
            throw new BusinessRuntimeException(RetCode.BUSINESSERROR_CODE, CommonRetCode.DOC_CLOUD_SERVICE_ERROR);
        }
        paramsMap.put("fileUrl", fileUrl);
        return paramsMap;
    }

}
