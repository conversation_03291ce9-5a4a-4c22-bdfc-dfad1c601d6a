package com.zte.mcrm.adapter.authorization.service;

import com.zte.itp.authorityclient.entity.output.UserVO;
import com.zte.itp.authorityclient.entity.output.ServiceData;
import com.zte.mcrm.adapter.authorization.dto.RoleInfoDTO;
import com.zte.mcrm.adapter.authorization.model.RoleInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/10/7
 */
public interface PrmAuthService {

    /**
     * 根据角色Id、模块Id、行业Id、组织Id从权限平台查找角色信息
     * 默认查询PRM侧人员信息
     *
     * @param entity：角色Id、模块Id、行业Id、组织Id集合
     * @return List<RoleInfo>
     * <AUTHOR>
     * @date 2021/10/30
     */
    List<RoleInfo> getUserByRoleAndData(RoleInfoDTO entity);

    /**
     * 根据角色编号集合查询用户集合-无约束
     * @param roleCodes
     * @return
     */
    Map<String,List<UserVO>> getPersonsUnconstrained(List<String> roleCodes);

    /**
     * 根据角色Id、模块Id、行业Id、组织Id从权限平台查找角色信息
     * @param isPrm：是否在PRM页面展示
     *             true： 返回不加密的工号
     *             false：返回加密的工号
     * @param entity：角色Id、模块Id、行业Id、组织Id集合
     * @return List<RoleInfo>
     * <AUTHOR>
     * @date 2021/10/30
     */
    List<RoleInfo> getUserByRoleAndData(RoleInfoDTO entity, Boolean isPrm);

    /**
     * 根据模块Id和角色编码获取角色Id
     *
     * @param moduleId 模块Id
     * @param roleCode 角色编码
     * @param itpValue Ucs的ItpValue
     * @return ServiceData
     * <AUTHOR>
     * @date 2021/10/30
     */
    ServiceData getRoleByRoleCodeAndModuleId(String moduleId, String roleCode, String itpValue);
}
