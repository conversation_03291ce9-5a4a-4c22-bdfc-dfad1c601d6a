<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zte.mcrm.channel.dao.OpptyCustomerCreateRecordDao" >
    <!-- 屏蔽mybatis的2级缓存  <cache />  -->
  
    <!-- 记录和实体映射配置 -->
    <resultMap id="BaseMap" type="com.zte.mcrm.channel.model.entity.OpptyCustomerCreateRecord" >
        <id column="row_id" property="rowId" jdbcType="VARCHAR" />
		<result column="opty_id" property="optyId" jdbcType="VARCHAR" />	
		<result column="customer_name" property="customerName" jdbcType="VARCHAR" />	
		<result column="crm_customer_code" property="crmCustomerCode" jdbcType="VARCHAR" />	
		<result column="x_last_acc_name" property="xLastAccName" jdbcType="VARCHAR" />	
		<result column="x_last_acc_id" property="xLastAccId" jdbcType="VARCHAR" />	
		<result column="dept_no" property="deptNo" jdbcType="VARCHAR" />	
		<result column="effected_flag" property="effectedFlag" jdbcType="VARCHAR" />	
		<result column="status" property="status" jdbcType="VARCHAR" />	
		<result column="final_customer_parent_trade" property="finalCustomerParentTrade" jdbcType="VARCHAR" />
		<result column="final_customer_child_trade" property="finalCustomerChildTrade" jdbcType="VARCHAR" />
		<result column="business_manager_id" property="businessManagerId" jdbcType="VARCHAR" />
		<result column="business_manager_name" property="businessManagerName" jdbcType="VARCHAR" />	
		<result column="created_by" property="createdBy" jdbcType="VARCHAR" />
		<result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />	
		<result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR" />	
		<result column="last_updated_date" property="lastUpdatedDate" jdbcType="TIMESTAMP" />	
		<result column="enabled_flag" property="enabledFlag" jdbcType="VARCHAR" />	
    </resultMap>

    <sql id="base_column">
        t.row_id ,
        t.opty_id ,
        t.customer_name ,
        t.crm_customer_code ,
        t.x_last_acc_name ,
        t.x_last_acc_id ,
        t.dept_no ,
        t.effected_flag ,
        t.status ,
        t.final_customer_parent_trade ,
        t.final_customer_child_trade ,
        t.business_manager_id ,
        t.business_manager_name ,
        t.created_by ,
        t.created_date ,
        t.last_updated_by ,
        t.last_updated_date ,
        t.enabled_flag 
    </sql>

    <sql id="base_where">
        <if test="rowId != null and rowId != ''"> and t.row_id = #{rowId}</if>
        <if test="optyId != null and optyId != ''"> and t.opty_id = #{optyId}</if>
        <if test="customerName != null and customerName != ''"> and t.customer_name = #{customerName}</if>
        <if test="crmCustomerCode != null and crmCustomerCode != ''"> and t.crm_customer_code = #{crmCustomerCode}</if>
        <if test="xLastAccName != null and xLastAccName != ''"> and t.x_last_acc_name = #{xLastAccName}</if>
        <if test="xLastAccId != null and xLastAccId != ''"> and t.x_last_acc_id = #{xLastAccId}</if>
        <if test="deptNo != null and deptNo != ''"> and t.dept_no = #{deptNo}</if>
        <if test="effectedFlag != null and effectedFlag != ''"> and t.effected_flag = #{effectedFlag}</if>
        <if test="status != null and status != ''"> and t.status = #{status}</if>
        <if test="finalCustomerParentTrade != null and finalCustomerParentTrade != ''"> and t.final_customer_parent_trade = #{finalCustomerParentTrade}</if>
        <if test="finalCustomerChildTrade != null and finalCustomerChildTrade != ''"> and t.final_customer_child_trade = #{finalCustomerChildTrade}</if>
        <if test="businessManagerId != null and businessManagerId != ''"> and t.business_manager_id = #{businessManagerId}</if>
        <if test="businessManagerName != null and businessManagerName != ''"> and t.business_manager_name = #{businessManagerName}</if>
        <if test="createdBy != null and createdBy != ''"> and t.created_by = #{createdBy}</if>
        <if test="createdDate != null"> and t.created_date = #{createdDate}</if>
        <if test="lastUpdatedBy != null and lastUpdatedBy != ''"> and t.last_updated_by = #{lastUpdatedBy}</if>
        <if test="lastUpdatedDate != null"> and t.last_updated_date = #{lastUpdatedDate}</if>
        <if test="enabledFlag != null and enabledFlag != ''"> and t.enabled_flag = #{enabledFlag}</if>
    and t.tenant_id = #{headerTenantId}
    </sql>

    <!-- 获取一条记录 -->
    <select id="get"  resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM oppty_customer_create_record t
        WHERE
        t.tenant_id = #{headerTenantId} AND 
        t.row_id=#{rowId, jdbcType=VARCHAR}
    </select>
 
    <!-- 获取符合条件的记录列表 -->
    <select id="getList" parameterType="java.util.Map" resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM oppty_customer_create_record t
        WHERE 1=1
        <include refid="base_where"/>
    </select>
  
    <!-- 软删除一条记录 -->
    <update id="softDelete" >
        UPDATE oppty_customer_create_record
        SET enabled_flag = 'N'
        WHERE
        tenant_id = #{headerTenantId} AND 
        row_id = #{rowId, jdbcType=VARCHAR}
    </update>
    
    <!-- 删除一条记录 -->
    <delete id="delete" >
        DELETE FROM oppty_customer_create_record
        WHERE
        tenant_id = #{headerTenantId} AND 
        row_id = #{rowId, jdbcType=VARCHAR}
    </delete>

    <!--添加一条记录 -->
    <insert id="insert" parameterType="com.zte.mcrm.channel.model.entity.OpptyCustomerCreateRecord" >
        INSERT INTO oppty_customer_create_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
		    <if test="rowId != null">row_id ,</if>
		    <if test="optyId != null">opty_id ,</if>
		    <if test="customerName != null">customer_name ,</if>
		    <if test="crmCustomerCode != null">crm_customer_code ,</if>
		    <if test="xLastAccName != null">x_last_acc_name ,</if>
		    <if test="xLastAccId != null">x_last_acc_id ,</if>
		    <if test="deptNo != null">dept_no ,</if>
		    <if test="effectedFlag != null">effected_flag ,</if>
		    <if test="status != null">status ,</if>
		    <if test="finalCustomerParentTrade != null">final_customer_parent_trade ,</if>
		    <if test="finalCustomerChildTrade != null">final_customer_child_trade ,</if>
		    <if test="businessManagerId != null">business_manager_id ,</if>
		    <if test="businessManagerName != null">business_manager_name ,</if>
		    <if test="createdBy != null">created_by ,</if>
		    <if test="createdDate != null">created_date ,</if>
		    <if test="lastUpdatedBy != null">last_updated_by ,</if>
		    <if test="lastUpdatedDate != null">last_updated_date ,</if>
		    <if test="enabledFlag != null">enabled_flag ,</if>
    		tenant_id
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
    	    <if test="rowId != null">#{rowId, jdbcType=VARCHAR} ,</if>
    	    <if test="optyId != null">#{optyId, jdbcType=VARCHAR} ,</if>
    	    <if test="customerName != null">#{customerName, jdbcType=VARCHAR} ,</if>
    	    <if test="crmCustomerCode != null">#{crmCustomerCode, jdbcType=VARCHAR} ,</if>
    	    <if test="xLastAccName != null">#{xLastAccName, jdbcType=VARCHAR} ,</if>
    	    <if test="xLastAccId != null">#{xLastAccId, jdbcType=VARCHAR} ,</if>
    	    <if test="deptNo != null">#{deptNo, jdbcType=VARCHAR} ,</if>
    	    <if test="effectedFlag != null">#{effectedFlag, jdbcType=VARCHAR} ,</if>
    	    <if test="status != null">#{status, jdbcType=VARCHAR} ,</if>
    	    <if test="finalCustomerParentTrade != null">#{finalCustomerParentTrade, jdbcType=VARCHAR} ,</if>
    	    <if test="finalCustomerChildTrade != null">#{finalCustomerChildTrade, jdbcType=VARCHAR} ,</if>
    	    <if test="businessManagerId != null">#{businessManagerId, jdbcType=VARCHAR} ,</if>
    	    <if test="businessManagerName != null">#{businessManagerName, jdbcType=VARCHAR} ,</if>
    	    <if test="createdBy != null">#{createdBy, jdbcType=VARCHAR} ,</if>
    	    <if test="createdDate != null">#{createdDate, jdbcType=TIMESTAMP} ,</if>
    	    <if test="lastUpdatedBy != null">#{lastUpdatedBy, jdbcType=VARCHAR} ,</if>
    	    <if test="lastUpdatedDate != null">#{lastUpdatedDate, jdbcType=TIMESTAMP} ,</if>
    	    <if test="enabledFlag != null">#{enabledFlag, jdbcType=VARCHAR} ,</if>
			#{headerTenantId, jdbcType=BIGINT}
        </trim>
    </insert>

    <!--批量添加记录 -->
    <insert id="insertByBatch" parameterType="java.util.List" >
        INSERT INTO oppty_customer_create_record
        (
		    row_id ,
    	    opty_id ,
    	    customer_name ,
    	    crm_customer_code ,
    	    x_last_acc_name ,
    	    x_last_acc_id ,
    	    dept_no ,
    	    effected_flag ,
    	    status ,
			final_customer_parent_trade ,
			final_customer_child_trade ,
    	    business_manager_id ,
    	    business_manager_name ,
    	    created_by ,
    	    created_date ,
    	    last_updated_by ,
    	    last_updated_date ,
    	    enabled_flag ,
    		tenant_id
        )
        VALUES
        <foreach collection ="list" item="item" index= "index" separator =",">
        (
    	    #{item.rowId, jdbcType=VARCHAR} ,
    	    #{item.optyId, jdbcType=VARCHAR} ,
    	    #{item.customerName, jdbcType=VARCHAR} ,
    	    #{item.crmCustomerCode, jdbcType=VARCHAR} ,
    	    #{item.xLastAccName, jdbcType=VARCHAR} ,
    	    #{item.xLastAccId, jdbcType=VARCHAR} ,
    	    #{item.deptNo, jdbcType=VARCHAR} ,
    	    #{item.effectedFlag, jdbcType=VARCHAR} ,
    	    #{item.status, jdbcType=VARCHAR} ,
    	    #{item.finalCustomerParentTrade, jdbcType=VARCHAR} ,
    	    #{item.finalCustomerChildTrade, jdbcType=VARCHAR} ,
    	    #{item.businessManagerId, jdbcType=VARCHAR} ,
    	    #{item.businessManagerName, jdbcType=VARCHAR} ,
    	    #{item.createdBy, jdbcType=VARCHAR} ,
    	    #{item.createdDate, jdbcType=TIMESTAMP} ,
    	    #{item.lastUpdatedBy, jdbcType=VARCHAR} ,
    	    #{item.lastUpdatedDate, jdbcType=TIMESTAMP} ,
    	    #{item.enabledFlag, jdbcType=VARCHAR} ,
			#{headerTenantId, jdbcType=BIGINT}
        )
        </foreach>
    </insert>

	<!--批量更新数据-->
	<update id="updateByBatch" parameterType="com.zte.mcrm.channel.model.entity.OpptyCustomerCreateRecord">
		update oppty_customer_create_record
		set effected_flag =
		<foreach collection="list" item="item" index="index" separator=" " open="CASE row_id" close="END">
			when #{item.rowId,jdbcType=VARCHAR} then #{item.effectedFlag,jdbcType=VARCHAR}
		</foreach>
		where row_id in
		<foreach collection="list" item="item" index="index" separator="," open="(" close=")">
			#{item.rowId, jdbcType=VARCHAR}
		</foreach>
	</update>

    <!-- 更新一条记录 -->
    <update id="update" parameterType="com.zte.mcrm.channel.model.entity.OpptyCustomerCreateRecord" >
        UPDATE oppty_customer_create_record
        <set>
		    <if test="optyId != null">opty_id=#{optyId, jdbcType=VARCHAR} ,</if>
		    <if test="customerName != null">customer_name=#{customerName, jdbcType=VARCHAR} ,</if>
		    <if test="crmCustomerCode != null">crm_customer_code=#{crmCustomerCode, jdbcType=VARCHAR} ,</if>
		    <if test="xLastAccName != null">x_last_acc_name=#{xLastAccName, jdbcType=VARCHAR} ,</if>
		    <if test="xLastAccId != null">x_last_acc_id=#{xLastAccId, jdbcType=VARCHAR} ,</if>
		    <if test="deptNo != null">dept_no=#{deptNo, jdbcType=VARCHAR} ,</if>
		    <if test="effectedFlag != null">effected_flag=#{effectedFlag, jdbcType=VARCHAR} ,</if>
		    <if test="status != null">status=#{status, jdbcType=VARCHAR} ,</if>
		    <if test="finalCustomerParentTrade != null">final_customer_parent_trade=#{finalCustomerParentTrade, jdbcType=VARCHAR} ,</if>
		    <if test="finalCustomerChildTrade != null">final_customer_child_trade=#{finalCustomerChildTrade, jdbcType=VARCHAR} ,</if>
		    <if test="businessManagerId != null">business_manager_id=#{businessManagerId, jdbcType=VARCHAR} ,</if>
		    <if test="businessManagerName != null">business_manager_name=#{businessManagerName, jdbcType=VARCHAR} ,</if>
		    <if test="createdBy != null">created_by=#{createdBy, jdbcType=VARCHAR} ,</if>
		    <if test="createdDate != null">created_date=#{createdDate, jdbcType=TIMESTAMP} ,</if>
		    <if test="lastUpdatedBy != null">last_updated_by=#{lastUpdatedBy, jdbcType=VARCHAR} ,</if>
		    <if test="lastUpdatedDate != null">last_updated_date=#{lastUpdatedDate, jdbcType=TIMESTAMP} ,</if>
		    <if test="enabledFlag != null">enabled_flag=#{enabledFlag, jdbcType=VARCHAR} ,</if>
	    </set>
        WHERE
        tenant_id = #{headerTenantId} AND 
        row_id=#{rowId, jdbcType=VARCHAR}
    </update>
  
    <!-- 翻页函数:获取符合条件的记录数 -->
    <select id="getCount" parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT count(*) FROM oppty_customer_create_record t
        WHERE 1=1
        <include refid="base_where"/>
    </select>
	
    <!-- 翻页函数:获取一页的记录集 -->
    <select id="getPage" parameterType="java.util.Map" resultMap="BaseMap">
        SELECT <include refid="base_column"/>
        FROM oppty_customer_create_record t
        WHERE 1=1
        <include refid="base_where"/>
        <if test="orderField != null and orderField == 'rowId'"> order by t.row_id
            <if test="order != null and order == 'desc'"> desc </if>
        </if>
        <if test="startRow != null and rowSize != null"> limit #{startRow},#{rowSize} </if>
    </select>

	<select id="queryCustomerRecordByCondition" parameterType="com.zte.mcrm.channel.model.entity.OpptyCustomerCreateRecordQuery" resultMap="BaseMap">
		SELECT <include refid="base_column"/>
		FROM oppty_customer_create_record t
		WHERE 1=1
		<if test="item.lastAccName !=null and item.lastAccName != ''">AND t.x_last_acc_name like concat('%',#{item.lastAccName},'%')</if>
		<if test="item.customerName !=null and item.customerName != ''">OR t.customer_name like concat('%',#{item.customerName},'%')</if>
		<if test="item.crmCustomerCode !=null and item.crmCustomerCode != ''">AND t.crm_customer_code = #{item.crmCustomerCode}</if>
		AND t.enabled_flag = 'Y'
		AND t.tenant_id = #{headerTenantId}
		GROUP BY t.x_last_acc_name,t.created_by
		ORDER BY t.created_date DESC
	</select>


     <select id="getNotEffectedCustomers" resultMap="BaseMap">
		 SELECT <include refid="base_column"/>
		 FROM oppty_customer_create_record t
		 where t.enabled_flag = 'Y'
		 <include refid="base_where"/>
		 order by t.created_date desc
		 limit 1000
	 </select>
</mapper>