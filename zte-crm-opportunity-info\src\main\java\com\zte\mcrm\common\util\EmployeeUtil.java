package com.zte.mcrm.common.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zte.mcrm.common.model.PersonAndOrgInfoVO;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.common.model.HttpResultData;
import com.zte.springbootframe.common.serviceregister.MicroServiceWithConfigUtil;
import com.zte.springbootframe.util.string.StringHelper;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @Author: 10261899
 * @Date: 2019/12/3
 * @Description:
 */
public class EmployeeUtil {

    /**
     * 根据员工工号获取员工信息
     * @param empNo
     * @return
     * @throws BusiException
     */
    public static PersonAndOrgInfoVO getEmployeeByEmpNo(String empNo) throws BusiException {

        JSONObject param=new JSONObject();

        param.put("empNo",empNo);

        HttpResultData httpResultData = MicroServiceWithConfigUtil.invokeServiceWithGetMethod("4", "/employee/getEmpInfoById", param, RequestMessage.getHeader("pcOpportunity"));

        if(httpResultData!=null&&httpResultData.getBo()!=null){
            String result= JSONObject.toJSONString(httpResultData.getBo());
            return JSONObject.parseObject(result,PersonAndOrgInfoVO.class);
        }

        return null;

    }

    public static List<PersonAndOrgInfoVO> getEmployeesByEmpNos(Set<String> empNos) throws BusiException {

        List<PersonAndOrgInfoVO> result=new ArrayList<>();
        if(StringHelper.isEmpty(empNos)){
            return result;
        }
        HttpResultData httpResultData = MicroServiceWithConfigUtil.invokeServiceWithPostMethod("4", "/employee/getEmpInfoByIds", empNos, RequestMessage.getHeader("pcOpportunity"));

        if(httpResultData!=null&&httpResultData.getBo()!=null){
            String resultString= JSONObject.toJSONString(httpResultData.getBo());
            result= JSONArray.parseArray(resultString,PersonAndOrgInfoVO.class);
        }

        return result;

    }

    /**
     * 查询人员信息，,异常时，直接抛运行时异常
     * @param empNos
     * @return
     * @throws BusiException
     */
    public static List<PersonAndOrgInfoVO> getEmployeesByEmpNoList(Set<String> empNos) {

        List<PersonAndOrgInfoVO> personAndOrgInfoVOList = null;
        try {
            personAndOrgInfoVOList = getEmployeesByEmpNos(empNos);
        } catch (BusiException e) {
            throw new com.zte.itp.msa.core.exception.BusiException(e.getExCode(),e.getExMsg());
        }

        return personAndOrgInfoVOList;

    }
}
