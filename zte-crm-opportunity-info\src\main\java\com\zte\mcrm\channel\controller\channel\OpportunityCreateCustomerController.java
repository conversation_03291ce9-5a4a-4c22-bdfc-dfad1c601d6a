package com.zte.mcrm.channel.controller.channel;

import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.channel.model.dto.CustomerInfoDTO;
import com.zte.mcrm.channel.service.channel.OpportunityCreateCustomerService;
import com.zte.mcrm.common.framework.exception.ErrorCode;
import com.zte.mcrm.common.framework.exception.ErrorCodeException;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.common.exception.ValidationException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.zte.mcrm.channel.constant.OpportunityConstant.CREATE_CUSTOMER_FAILURE;

/**
 * 新建商机相关 MVC 控制类
 * <AUTHOR>
 * @date 2021/09/15
 */
@Api(tags = "创建客户")
@RestController
@RequestMapping("/channel/")
public class OpportunityCreateCustomerController {

    private static final Logger LOGGER = LoggerFactory.getLogger(OpportunityCreateCustomerController.class);

    @Autowired
    OpportunityCreateCustomerService opportunityCreateCustomerService;

    @ApiOperation("创建客户")
    @PostMapping("/createCustomer")
    public ServiceData<String> createCustomer(@Validated @RequestBody CustomerInfoDTO companyInfoDTO, BindingResult bindingResult) throws ValidationException, BusiException {
        // 数据校验
        if (bindingResult != null && bindingResult.hasErrors()) {
            throw new com.zte.springbootframe.common.exception.ValidationException(bindingResult);
        }
        try {
            ServiceData<String> result = new ServiceData<>();
            result.setCode(opportunityCreateCustomerService.createCustomer(companyInfoDTO));
            return result;
        } catch (ErrorCodeException errorCodeException) {
            throw errorCodeException;
        } catch (Exception e) {
            LOGGER.error("商机id: {}, 最终用户:{} 创建失败， error:{}", companyInfoDTO.getOptyId(), companyInfoDTO.getLastAccName(), e.getMessage(), e);
            throw new ErrorCodeException(ErrorCode.UniversalErrorEnum.OU3153);
        }
    }
}
