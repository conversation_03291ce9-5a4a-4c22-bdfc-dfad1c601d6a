package com.zte.mcrm.channel.service.prm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.itp.authorityclient.entity.output.UserVO;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.mcrm.adapter.approval.model.FlowParameter;
import com.zte.mcrm.adapter.approval.model.dto.*;
import com.zte.mcrm.adapter.approval.service.ApprovalFlowService;
import com.zte.mcrm.adapter.authorization.constant.UppAuthPropertiesConstant;
import com.zte.mcrm.adapter.authorization.dto.RoleInfoDTO;
import com.zte.mcrm.adapter.authorization.model.RoleInfo;
import com.zte.mcrm.adapter.authorization.service.PrmAuthService;
import com.zte.mcrm.adapter.authorization.service.RoleService;
import com.zte.mcrm.adapter.mail.service.SendMailService;
import com.zte.mcrm.adapter.model.AuthConstraintDTO;
import com.zte.mcrm.adapter.model.dto.AccountInfo;
import com.zte.mcrm.adapter.model.dto.ChannelCustomerRes;
import com.zte.mcrm.adapter.model.dto.OrgConditionParam;
import com.zte.mcrm.adapter.model.vo.OrgConditionVO;
import com.zte.mcrm.adapter.model.vo.OrgConditionVos;
import com.zte.mcrm.adapter.service.ComposeService;
import com.zte.mcrm.adapter.service.CustomerInfoService;
import com.zte.mcrm.adapter.service.PrmService;
import com.zte.mcrm.channel.constant.*;
import com.zte.mcrm.channel.dao.ComApprovalRecordDao;
import com.zte.mcrm.channel.dao.OpportunityDao;
import com.zte.mcrm.channel.dao.OpportunityDetailDao;
import com.zte.mcrm.channel.dao.OpportunityQueryDao;
import com.zte.mcrm.channel.model.dto.ApprovalCallBackMsgInfo;
import com.zte.mcrm.channel.model.dto.AuthorizationRoleInfoDTO;
import com.zte.mcrm.channel.model.dto.OpportunityOpinionDTO;
import com.zte.mcrm.channel.model.dto.SimilarOpportunityQueryDTO;
import com.zte.mcrm.channel.model.entity.*;
import com.zte.mcrm.channel.model.vo.OpportunityOpinionVO;
import com.zte.mcrm.channel.model.vo.PrmLastUserStatusVO;
import com.zte.mcrm.channel.model.vo.RestrictedPartyVO;
import com.zte.mcrm.channel.service.channel.IOpportunityDetailService;
import com.zte.mcrm.channel.service.channel.IOpportunityService;
import com.zte.mcrm.channel.service.common.TeamConverter;
import com.zte.mcrm.channel.util.ExceptionMsgUtils;
import com.zte.mcrm.channel.util.MapUtil;
import com.zte.mcrm.common.access.vo.ComDictionaryMaintainVO;
import com.zte.mcrm.common.business.service.IComDictionaryMaintainService;
import com.zte.mcrm.common.business.service.IKeyIdService;
import com.zte.mcrm.common.consts.ComDictionaryMaintainConsts;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.consts.CommonConstant;
import com.zte.mcrm.common.exception.BusinessRuntimeException;
import com.zte.mcrm.common.model.SysGlobalConstVo;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.mcrm.common.util.EmpProcessUtil;
import com.zte.mcrm.common.util.FormDataHelpUtil;
import com.zte.mcrm.logger.bussiness.service.LoggerService;
import com.zte.opty.common.enums.OptyStatusEnum;
import com.zte.opty.dao.SOpportunityRepository;
import com.zte.opty.dao.SOptyRedundancyDao;
import com.zte.opty.dao.SOptyTeamDao;
import com.zte.opty.model.bo.SOptyBO;
import com.zte.opty.model.bo.SOptyRedundancyBO;
import com.zte.opty.model.bo.SOptyTeamBO;
import com.zte.opty.sync.util.CommonMapUtil;
import com.zte.springbootframe.util.local.LocalMessageUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * prm商机审批页面相关接口 服务
 *
 * <AUTHOR>
 * @date 2021/9/30
 */
@Service
public class IPrmOpportunityApprovalServiceImpl implements IPrmOpportunityApprovalService {


    private static final Logger logger = LoggerFactory.getLogger(IPrmOpportunityApprovalServiceImpl.class);

    @Autowired
    OpportunityQueryDao opportunityQueryDao;
    @Autowired
    OpportunityDetailDao opportunityDetailDao;
    @Autowired
    IOpportunityDetailService opportunityDetailService;
    @Autowired
    PrmService prmService;
    @Autowired
    ComposeService composeService;
    @Autowired
    private OpportunityDao opportunityDao ;
    @Autowired
    private RoleService roleService;
    @Autowired
    private PrmAuthService authService;
    @Autowired
    ApprovalFlowService approvalFlowService;
    @Autowired
    ComApprovalRecordDao comApprovalRecordDao;
    @Autowired
    private IComDictionaryMaintainService comDictionaryMaintainService;
    @Autowired
    private CustomerInfoService customerInfoService;

    private static final String TYPE_STR = "String";
    private static final String TYPE_INTEGER = "Integer";

    @Autowired
    IOpportunityService iOpportunityService;

    @Autowired
    private SendMailService sendMailService;

    @Autowired
    private LoggerService loggerService;

    @Autowired
    private IKeyIdService iKeyIdService;

    @Autowired
    private SOptyTeamDao sOptyTeamDao;

    @Autowired
    private SOptyRedundancyDao sOptyRedundancyDao;

    @Autowired
    private TeamConverter teamConverter;

    @Autowired
    private SOpportunityRepository opportunityRepository;

    /**
     * 查询类似商机:
     *      审批中： 默认查询逻辑  getSimilarOpportunityApprovaling()
     *      审批后：直接返回 审批中心返回的  similarOptyCd 对应的 类似商机
     *
     * <AUTHOR>
     * @date 2021/12/02
     */
    @Override
    public PageRows<SimilarOpportunity> getSimilarOpportunity(FormData<SimilarOpportunityQueryDTO> formParam) throws Exception {

        ApprovalDetail approvalDetail =  this.getApprovalDetailList(formParam.getBo().getRowId()).stream().filter(fillterApprovalDetail->
                  StringUtils.equals(fillterApprovalDetail.getNodeName(),OpportunityConstant.APPROVAL_NODE)).findFirst().orElse(new ApprovalDetail());
            String result = approvalDetail.getResult();

        // 根据 result 判断 【审批中】 还是 【审批后】
        if (OpportunityConstant.APPROVAL_Y_NAME.equals(result) || OpportunityConstant.APPROVAL_N_NAME.equals(result)) {
            // 【审批后】 拿到对应 商机编号 的类似商机信息
            String similarOptyCd = approvalDetail.getSimilarOptyCd();
            PageRows<SimilarOpportunity> returnPage = new PageRows<>();
            List<SimilarOpportunity> rowData = new ArrayList<>();
            if (StringUtil.isNotEmpty(similarOptyCd)) {
                returnPage.setTotal(OpportunityConstant.ONE);
                rowData = opportunityQueryDao.querySimilarOpportunityByOptyCd(similarOptyCd);
                rowData.stream().map(vo ->{
                    try {
                        return changeDataByExternalService(vo);
                    }catch (Exception e){
                        logger.error("商机所属部门转化异常",e);
                        return vo;
                    }
                }).collect(Collectors.toList());
            } else {
                returnPage.setTotal(OpportunityConstant.ZERO);
            }
            returnPage.setRows(rowData);
            return returnPage;
        } else {
            // 【审批中】
            return this.getSimilarOpportunityApprovaling(formParam);
        }
    }

    /**
     * 查询【审批中】的类似商机，然后按照 预计发标日期由近及远排列 ：
     * 1. 如果 商机名称 与 渠道商名称 都为空时，默认查询 并过滤 ABCD 条件
     *      - 数据库查，过滤条件 <A: 最终用户名称相同>、
     *                        <B: 报备产品（包含该产品）相同，至少有一类产品相同>、
     *                        <C: 报备状态为报备成功、报备审批中、仲裁中、赢单>、
     *                        <D: 招标类型:
     *                           1）当前商机为招标项目时， 已有商机也是招标项目，当前商机A与已有商机B的按“预计发标日期/预计签约日期”比较，上3个月下6个月内（共9个月）
     *                              预计发标日期A-90<=预计发标日期B<=预计发标日期A+180
     *                           2）当前商机为招标项目时，已有商机非招标项目，按当前商机A的“竞标截止日期”+30天和已有商机B的“预计发标日期/预计签约日期”比较，上3个月下6个月内（共9个月）；
     *                              竞标截止日期A+30-90<=预计发标日期B<=竞标截止日期A+30+180
     *                           3）当前商机为非招标项目时，已有商机非招标项目，按照当前商机A与已有商机B的按“预计发标日期/预计签约日期”比较，上3个月下6个月内（共9个月）；
     *                              预计签约日期A-90<=预计签约日期B<=预计签约日期A+180
     *                           4）当前商机为非招标项目时，已有商机招标项目，当前商机A的“预计发标日期/预计签约日期”与已有商机B“竞标截止日期”+30天比较，上3个月下6个月内（共9个月）；
     *                              预计签约日期A-90<=竞标截止日期B+30<=预计签约日期A+180
     *                        >
     *
     * 2. 如果 商机名称 与 渠道商名称 其中一个有数据，
     *       - 数据库查满足 <C: 报备状态为报备成功、报备审批中、仲裁中、赢单> 条件即可
     *       - 直接查出数据返回
     *
     * 3. 把非本渠道商机放前，本渠道商机放后，同时把渠道商级别查出返回数据
     *
     * <AUTHOR>
     * @date 2021/9/30
     */
    @Override
    public PageRows<SimilarOpportunity> getSimilarOpportunityApprovaling(FormData<SimilarOpportunityQueryDTO> formParam) throws Exception {

        CurrentOptyInfo currentOptyInfo = opportunityQueryDao.queryCurrentOpportunityByRowId(formParam.getBo().getRowId());
        currentOptyInfo.setCurrentOptyTenderType(CommonMapUtil.BIDDING_TYPE_MAP.inverse().getOrDefault(currentOptyInfo.getCurrentOptyTenderType(), currentOptyInfo.getCurrentOptyTenderType()));
        PageRows<SimilarOpportunity> returnPage = new PageRows<>();
        PageMethod.startPage(FormDataHelpUtil.getPageNum(formParam), FormDataHelpUtil.getPageSize(formParam));
        SimilarOpportunityQueryDTO param = formParam.getBo();

        // 1 与 2 互斥关系，分页查询完 result
        List<SimilarOpportunity> result;

        // 1. 如果 商机名称 与 渠道商名称 都为空时，按 当前商机 的主键 row-id 查询条件精确
        if (StringUtil.isEmpty(param.getOpportunityName()) && StringUtil.isEmpty(param.getChannelVendor())) {

            param.setCurrentOptyLastName(currentOptyInfo.getCurrentOptyLastName());
            param.setCurrentDeadline(currentOptyInfo.getCurrentDeadline());
            param.setCurrentOptyTime(currentOptyInfo.getCurrentOptyTime());
            param.setCurrentOptyTenderType(currentOptyInfo.getCurrentOptyTenderType());
            // 查出来的数据已经按时间排序, 非本渠道商排在前面
            result = opportunityQueryDao.querySimilarOpportunityByRowId(param);
        }
        // 2. 其他情况，则只满足 C 条件即可
        else {
            result = opportunityQueryDao.querySimilarOpportunityByName(param);
        }
        // 3. 非本渠道商的排序 和 时间排序已经在查询数据库时完成
        if (CollectionUtils.isEmpty(result)) {
            returnPage.setTotal(0);
            return returnPage;
        }
        for (SimilarOpportunity opportunity : result) {
            opportunity.setTenderTypeCode(CommonMapUtil.BIDDING_TYPE_MAP.inverse().getOrDefault(opportunity.getTenderTypeCode(), opportunity.getTenderTypeCode()));
        }

        PageInfo<SimilarOpportunity> pageInfo = new PageInfo<>(result);
        returnPage.setCurrent(FormDataHelpUtil.getPageNum(formParam));
        returnPage.setTotal(pageInfo.getTotal());

        // 把 组织、行业 代码 转为 中文
        List<SimilarOpportunity> resultNew = new ArrayList<>();
        for (SimilarOpportunity item: result) {
            resultNew.add(changeDataByExternalService(item));
        }

        returnPage.setRows(resultNew);
        return returnPage;
    }

    /**
     * 根据 row-id 查询类似商机
     */
    @Override
    public List<SimilarOpportunity> querySimilarOpportunityByRowId(SimilarOpportunityQueryDTO param){
        param.setCurrentOptyTenderType(CommonMapUtil.BIDDING_TYPE_MAP.inverse().getOrDefault(param.getCurrentOptyTenderType(), param.getCurrentOptyTenderType()));
        return opportunityQueryDao.querySimilarOpportunityByRowId(param);
    }


    private SimilarOpportunity changeDataByExternalService(SimilarOpportunity opportunity) throws Exception {
        List<OrgConditionVO> orgDatas = null;

        // 获取组织中文名
        try {
            OrgConditionParam orgConditionParam = new OrgConditionParam();
            orgConditionParam.setOrgIds(Lists.newArrayList(opportunity.getOpportunityDepartment()));
            orgConditionParam.setIsVisible(CommonConstant.COMMON_FLAG_Y);
            orgConditionParam.setOrgType(CommonConstant.ORGANIZATION_CHINA);
            orgConditionParam.setPageSize(CommonConstant.ONE_THOUSAND);

            OrgConditionVos descendants = prmService.getCondition(orgConditionParam);
            if(Objects.nonNull(descendants)) {
                orgDatas = descendants.getOrgDatas();
            }
            if (CollectionUtils.isNotEmpty(orgDatas)) {
                OrgConditionVO orgConditionVO = orgDatas.get(0);
                opportunity.setOpportunityDepartment(orgConditionVO.getOrganizationName());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 获取行业中文名称
        try {
            // 从行业树获取行业列表
            List<AuthConstraintDTO> subIndustryList = prmService.getSubIndustryListWithNoException("Y");
            Map<String, String> mapSubIndustry = subIndustryList.stream()
                    .collect(Collectors.toMap(AuthConstraintDTO::getShowValue, AuthConstraintDTO::getShowName));
            String defaultTrade = StringUtils.isNotBlank(opportunity.getFinalCustomerTradeChildCode()) ?
                    opportunity.getFinalCustomerTradeCode() + CommonConstant.MID_LINE + opportunity.getFinalCustomerTradeChildCode() : null;

            opportunity.setIndustry(mapSubIndustry.getOrDefault(opportunity.getFinalCustomerTradeChildCode(), defaultTrade));

        } catch (Exception e) {
            e.printStackTrace();
        }
        return opportunity;
    }

    /**
     * 依据角色、组织、子行业从权限平台找人
     *
     * @param dto
     * @return
     */
    @Override
    public List<RoleInfo> queryRoleInfoWithConstraint(AuthorizationRoleInfoDTO dto) {
        RoleInfoDTO entity = new RoleInfoDTO();
        //1. 将模块编码变成模块Id
        String moduleId = UppAuthPropertiesConstant.MODULE_MAP.get(dto.getModuleCode());
        //2. 将RoleCode变成RoleId
        String roleId = roleService.getRoleByRoleCodeAndModuleId(moduleId, dto.getRoleCode(),Boolean.TRUE);
        //3. 调用PrmAuthService#getUserByRoleAndData
        entity.setRoleId(roleId);
        entity.setModuleId(moduleId);
        entity.setIndustryIds(Lists.newArrayList(dto.getIndustryId()));
        entity.setOrgIds(Lists.newArrayList(dto.getOrgId()));

        String sysFlag = dto.getSysFlag();
        boolean isPrm = StringUtils.equalsIgnoreCase(OpportunityConstant.PRM, sysFlag);
        return authService.getUserByRoleAndData(entity, isPrm);
    }

    @Override
    public Integer modifyBusinessManager(BusinessManagerBO businessManager) {
        OpportunityDetail opportunityDetail = opportunityDetailDao.get(businessManager.getRowId());
        if (null == opportunityDetail){
            throw new BusiException(RetCode.BUSINESSERROR_CODE, LocalMessageUtils.getMessage("opportunity.activation.rowIdNotExisted"));
        }
        if (StringUtils.equals(opportunityDetail.getBusinessManagerId(), businessManager.getBusinessManagerId())){
            throw new BusiException(RetCode.BUSINESSERROR_CODE, LocalMessageUtils.getMessage("businessManager.not.modify"));
        }
        OpportunityDetail entity = new OpportunityDetail();
        entity.setRowId(businessManager.getRowId());

        entity.setBusinessManagerId(businessManager.getBusinessManagerId());
        entity.setBusinessManagerName(businessManager.getBusinessManagerName());
        entity.setLastUpdBy(CommonUtils.getEmpNo());
        entity.setLastUpd(new Date());
        logger.info("修改中兴业务经理,{}", businessManager);
        loggerService.saveLogger(JSON.toJSONString(businessManager), OpportunityConstant.MODIFY_BUSINESS_MANAGER_LOG_TIP);

        SOptyTeamBO sOptyTeamBO = teamConverter.teamUtil(null,entity, TeamConverter.ROLE_OWNER_CODE);
        SOptyRedundancyBO sOptyRedundancyBO = new SOptyRedundancyBO();
        sOptyRedundancyBO.setResponsiblePerson(sOptyTeamBO.getEmployee());
        sOptyRedundancyDao.update(sOptyRedundancyBO, Wrappers.lambdaUpdate(SOptyRedundancyBO.class)
                .eq(SOptyRedundancyBO::getId, entity.getRowId()));
        SOptyBO optyBO = new SOptyBO();
        optyBO.setId(entity.getRowId());
        optyBO.setLastModifiedTime(new Date());
        opportunityRepository.updateById(optyBO);
        return teamConverter.saveOrUpdate(sOptyTeamBO);
    }

    @Override
    public boolean reassignApproval(ReassignDTO reassignDTO) {
        logger.info("转交:{}", reassignDTO);
        if (StringUtils.isEmpty(reassignDTO.getTaskId())) {
            throw new BusinessRuntimeException(RetCode.BUSINESSERROR_CODE, "RetCode.ApprovalExistError");
        }
        String nodeName = reassignDTO.getNodeName();
        String result = approvalFlowService.reassignApproval(reassignDTO);
        logger.info("转交,reassignDTO:{},result:{}", reassignDTO, result);
        // 创建客户草稿节点需要修改审判中心审批人
        if(StringUtils.isNotBlank(nodeName) && OpportunityConstant.CUSTOMER_DRAFT_CREATED.equals(nodeName)){
            modifyApprovalCenterApprovingPerson(reassignDTO.getFlowInstanceId(), reassignDTO.getTaskReceiver());
        }
        return Boolean.TRUE;
    }


    private void modifyApprovalCenterApprovingPerson(String flowInstanceId, String newApprovingPerson){
        if (StringUtils.isBlank(newApprovingPerson)){
            throw new BusiException(RetCode.BUSINESSERROR_CODE, LocalMessageUtils.getMessage("approvingPerson.newApprovingPerson.null"));
        }
        ApprovalResetStartParamsDTO params = new ApprovalResetStartParamsDTO();
        params.setFlowInstanceId(flowInstanceId);
        Map<String, Parameter> parameterMap = Maps.newHashMap();
        Parameter parameter = new Parameter();
        parameter.setParameterType(TYPE_STR);
        parameter.setParameterValue(newApprovingPerson);
        parameterMap.put("approvingPerson", parameter);
        params.setParameterMap(parameterMap);
        String message = approvalFlowService.resetFlowParams(params);
        loggerService.saveLogger(JSON.toJSONString(params), OpportunityConstant.RESET_PROCESS_PARAMETERS_LOG_TIP);
        logger.info("重设approvingPerson流程参数成功，变更后的值：{}",message);
    }

    @Override
    public String modifyApprovalCenterProcessVariables(String flowInstanceId,
                                                       ApprovalStartParamsBO approvalStartParamsBO,
                                                       Map<String, Object> affixParams)
            throws com.zte.springbootframe.common.exception.BusiException {
        Map<String, Object> stringObjectMap = MapUtil.objectToMap(approvalStartParamsBO);
        if (null == stringObjectMap){
            throw new com.zte.springbootframe.common.exception.BusiException(RetCode.BUSINESSERROR_CODE, LocalMessageUtils.getMessage("modifyApprovalCenterProcessVariables.paramMap.null"));
        }
        if (null != affixParams){
            stringObjectMap.putAll(affixParams);
        }
        ApprovalResetStartParamsDTO approvalResetStartParamsDTO = new ApprovalResetStartParamsDTO();
        approvalResetStartParamsDTO.setFlowInstanceId(flowInstanceId);
        approvalResetStartParamsDTO.setParameterMap(convert2RestartVariableMap(stringObjectMap));
        String message = approvalFlowService.resetFlowParams(approvalResetStartParamsDTO);
        loggerService.saveLogger(JSON.toJSONString(approvalResetStartParamsDTO), OpportunityConstant.RESET_PROCESS_PARAMETERS_LOG_TIP);
        logger.info("重设流程参数成功，入参：{}, 变更后的值：{}", stringObjectMap, message);
        return message;
    }

    private Map<String, Parameter> convert2RestartVariableMap(Map<String, Object> params) {
        if (null == params || params.isEmpty()) {
            return new HashMap<>();
        }
        Map<String, Parameter> resultMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            Parameter parameter;
            if (entry.getValue() instanceof Integer) {
                parameter = new Parameter(TYPE_INTEGER, entry.getValue().toString());
            }else{
                parameter = new Parameter(TYPE_STR, entry.getValue().toString());
            }
            resultMap.put(entry.getKey(), parameter);
        }
        return resultMap;
    }
    @Override
    public String arbitrationApprove(OpportunityOpinionDTO opinion) throws Exception{
        //校验审批结果
        if(StringUtils.isBlank(opinion.getResult())) {
            throw new BusiException(RetCode.VALIDATIONERROR_CODE, LocalMessageUtils.getMessage("approvalReasonNull"));
        }
        //校验审批意见
        if(StringUtils.isBlank(opinion.getOpinion())) {
            throw new BusiException(RetCode.VALIDATIONERROR_CODE, LocalMessageUtils.getMessage("approvalOpinionNull"));
        }
        logger.info("仲裁节点审批，rowId:{},taskId:{}", opinion.getBusinessManagerBO().getRowId(), opinion.getTaskId());
        loggerService.saveLogger(opinion.getBusinessManagerBO().getRowId(), OpportunityConstant.ARBITRATION_APPROVAL_PARAMS);
        OpinionDTO entity = new OpinionDTO();
        entity.setTaskId(opinion.getTaskId());
        entity.setResult(opinion.getResult());
        entity.setOpinion(opinion.getOpinion());
        approvalFlowService.approve(entity);
        return RetCode.SUCCESS_MSGID;
    }
    /**
     * 节点审批
     * @param opinion
     * @return
     */
    @Override
    public String approve(OpportunityOpinionDTO opinion) throws Exception {
        RestrictedPartyVO restrictedPartyInfo = getRestrictedPartyV2(opinion.getLastAccId(), opinion.getCrmCustomerCode(),opinion.getDeptNo());
        verifyParameters(opinion,restrictedPartyInfo);

        String rowId = opinion.getBusinessManagerBO().getRowId();
        String businessManagerId = opinion.getBusinessManagerBO().getBusinessManagerId();
        if(StringUtils.isNotBlank(rowId) && StringUtils.isNotBlank(businessManagerId)) {
            OpportunityDetail entity = new OpportunityDetail();
            entity.setRowId(rowId);

            entity.setTsApprovalNumber(opinion.getTsApprovalNo());
            entity.setLastUpd(new Date());
            opportunityDetailDao.updateOpportunityApproveInfo(entity);

            entity.setBusinessManagerName(opinion.getBusinessManagerBO().getBusinessManagerName());
            entity.setBusinessManagerId(businessManagerId);

            SOptyTeamBO sOptyTeamBO = teamConverter.teamUtil(null,entity, TeamConverter.ROLE_OWNER_CODE);
            teamConverter.saveOrUpdate(sOptyTeamBO);

            logger.info("节点审批，修改中兴业务经理||TS审批单号:{},taskId:{}", JSON.toJSONString(entity), opinion.getTaskId());
            loggerService.saveLogger(JSON.toJSONString(entity), OpportunityConstant.MODIFY_BUSINESS_MANAGER_LOG_TIP);
        }

        //调用审批中心接口重设流程参数
        updateApprovalVariable(opinion, rowId);

        approvalFlowService.approve(setApprovalParam(opinion));
        return RetCode.SUCCESS_MSGID;
    }

    private void updateApprovalVariable(OpportunityOpinionDTO opinion, String rowId) throws com.zte.springbootframe.common.exception.BusiException {
        String flowInstanceId = comApprovalRecordDao.queryFlowInstance(rowId);
        ApprovalStartParamsBO approvalParams = new ApprovalStartParamsBO();
        if (null != opinion.getFailureReason() && opinion.getFailureReason() > 0) {
            approvalParams.setArbitrationType(opinion.getFailureReason());
            approvalParams.setArbitrationTypeName(ArbitrationTypeEnum.getArbitrationTypeNameByCode(opinion.getFailureReason()));
        }
        if (StringUtils.isNotBlank(opinion.getBusinessManagerBO().getBusinessManagerId())) {
            approvalParams.setBusinessManagerId(opinion.getBusinessManagerBO().getBusinessManagerId());
            approvalParams.setBusinessManager(opinion.getBusinessManagerBO().getBusinessManagerName() + opinion.getBusinessManagerBO().getBusinessManagerId());
        }
        if (StringUtils.isNotBlank(opinion.getTsApprovalNo())) {
            approvalParams.setTsApprovalNo(opinion.getTsApprovalNo());
        }
        if (StringUtils.isNotBlank(opinion.getSimilarOptyCd())){
            approvalParams.setCheckedSimilarOpportunity(opinion.getSimilarOptyCd());
        }
        String message = modifyApprovalCenterProcessVariables(flowInstanceId, approvalParams, null);
        logger.info("重设流程参数成功，变更后的值：{}",message);
    }

    /**
     * 审批中心评审校验回调接口
     *
     * @param approvalVerificationCallback
     * @return
     */
    @Override
    public ApprovalVerificationResult approveVerificationCallBack(ApprovalVerificationCallback approvalVerificationCallback) throws Exception {
        logger.info("approveVerificationCallBack,input:{}", approvalVerificationCallback);
        ApprovalVerificationResult result = new ApprovalVerificationResult();
        String logStr = "input:" + JSON.toJSONString(approvalVerificationCallback);
        try {
           result = doApprovalVerification(approvalVerificationCallback);
            logger.info("approveVerificationCallBack,input:{},output:{}", approvalVerificationCallback, result);
            logStr = logStr + ";output:" + JSON.toJSONString(result);
        }catch (Exception e){
            logger.error("approveVerificationCallBack Error, input:{}", approvalVerificationCallback, e);
            result.setSubmit(CommonConst.N);
            result.setMsg(LocalMessageUtils.getMessage("approveVerificationCallBack.error"));
            logStr = logStr + ";Exception:" + ExceptionMsgUtils.getStackTrace(e, 500);
        }
        loggerService.synSaveLogger(logStr, "approveVerificationCallBack");
        return result;
    }

    private ApprovalVerificationResult doApprovalVerification(ApprovalVerificationCallback callback) throws Exception {
        String rowId = callback.getBusinessId();
        ApprovalVerificationResult result = new ApprovalVerificationResult();
        result.setSubmit(CommonConst.Y);
        OpportunityDetail opportunityDetail = opportunityDetailService.get(callback.getBusinessId());
        if (null == opportunityDetail){
            logger.info("商机不存在或已失效,approvalVerificationCallback:{}", callback);
            result.setSubmit(CommonConst.N);
            result.setMsg(LocalMessageUtils.getMessage("approve.opportunity.null"));
            return result;
        }
        OpportunityOpinionVO opinionVO = JSON.parseObject(callback.getExtOpinion(), OpportunityOpinionVO.class);
        if (null == opinionVO || null == opinionVO.getFailureReason()){
            logger.info("必填项未选择,approvalVerificationCallback:{}", callback);
            result.setSubmit(CommonConst.N);
            result.setMsg(LocalMessageUtils.getMessage("approve.required.fields"));
            return result;
        }
        // 通过，但选择了不通过原因
        if (opinionVO.getFailureReason() > 0 && callback.getResult().equals(CommonConst.Y)){
            logger.info("未选择通过原因,approvalVerificationCallback:{}", callback);
            result.setSubmit(CommonConst.N);
            result.setMsg(LocalMessageUtils.getMessage("approve.pass.reason"));
            return result;
        }
        // 通过，但选择了通过原因
        if (opinionVO.getFailureReason() <= 0 && callback.getResult().equals(CommonConst.N)){
            logger.info("未选择不通过原因,approvalVerificationCallback:{}", callback);
            result.setSubmit(CommonConst.N);
            result.setMsg(LocalMessageUtils.getMessage("approve.reject.reason"));
            return result;
        }
        RestrictedPartyVO restrictedPartyInfo = getRestrictedPartyV2(opportunityDetail.getLastAccId(), opportunityDetail.getCrmCustomerCode(),opportunityDetail.getDeptNo());
        List<RestrictedPartyEnum> restrictSubmissionSubjectTypes = Arrays.asList(RestrictedPartyEnum.EMBARGO,RestrictedPartyEnum.PENDING);
        boolean isRestricted = CommonConst.Y.equals(callback.getResult())
                && (restrictSubmissionSubjectTypes.contains(RestrictedPartyEnum.valueOfCode(restrictedPartyInfo.getCustomerRestrictedPartyCode()))
                || restrictSubmissionSubjectTypes.contains(RestrictedPartyEnum.valueOfCode(restrictedPartyInfo.getEndUserRestrictedPartyCode())));
        if (isRestricted) {
            logger.info("禁运国或待确认不允许报备通过,approvalVerificationCallback:{}", callback);
            result.setSubmit(CommonConst.N);
            result.setMsg(LocalMessageUtils.getMessage("restrictedParty.notPass"));
            return result;
        }
        this.tsApprovalNoNull(callback, result, opinionVO, restrictedPartyInfo);
        // 不通过原因选已有报备但未选择类似商机
        if (Objects.equals(opinionVO.getFailureReason(), ArbitrationTypeEnum.FAILURE_REASON_ALREADY.getCode())
                && (StringUtils.isBlank(opinionVO.getSimilarOptyCd())
                || opinionVO.getSimilarOptyCd().equals(OpportunityConstant.NO_THING)
                || opinionVO.getSimilarOptyCd().equals(OpportunityConstant.NO_THING_ENG))){
            logger.info("未选择类似商机,approvalVerificationCallback:{}", callback);
            result.setSubmit(CommonConst.N);
            result.setMsg(LocalMessageUtils.getMessage("approve.reject.similarOpportunity"));
            return result;
        }
        // 可以继续审批的话，修改流程参数变量
        setProcessVariable(rowId, opinionVO, callback.getFlowInstanceId());
        return result;
    }

    private void tsApprovalNoNull(ApprovalVerificationCallback callback, ApprovalVerificationResult result, OpportunityOpinionVO opinionVO, RestrictedPartyVO restrictedPartyInfo) {
        // 判断是否包含受限制主体
        boolean restricted = RestrictedPartyEnum.isRestrictedParty(restrictedPartyInfo.getCustomerRestrictedPartyCode())
                || RestrictedPartyEnum.isRestrictedParty(restrictedPartyInfo.getEndUserRestrictedPartyCode());

        if(restricted && StringUtils.isBlank(opinionVO.getTsApprovalNumber())){
            logger.info("为受限制主体时,ts单不能为空,approvalVerificationCallback:{}", callback);
            result.setSubmit(CommonConst.N);
            result.setMsg(LocalMessageUtils.getMessage("tsApprovalNoNull1"));
        }
    }

    private void setProcessVariable(String rowId, OpportunityOpinionVO opinionVO, String flowInstanceId) throws com.zte.springbootframe.common.exception.BusiException {
        ApprovalStartParamsBO paramsBO = new ApprovalStartParamsBO();
        paramsBO.setArbitrationType(opinionVO.getFailureReason());
        paramsBO.setArbitrationTypeName(ArbitrationTypeEnum.getArbitrationTypeNameByCode(opinionVO.getFailureReason()));
        if (StringUtils.isNotBlank(opinionVO.getTsApprovalNumber())) {
            paramsBO.setTsApprovalNo(opinionVO.getTsApprovalNumber());
        }
        if (!Arrays.asList(OpportunityConstant.NO_THING, OpportunityConstant.NO_THING_ENG)
                .contains(opinionVO.getSimilarOptyCd())){
            paramsBO.setCheckedSimilarOpportunity(opinionVO.getSimilarOptyCd());
        }
        OpportunityDetail opportunityDetail = buildOpportunityDetail(rowId,opinionVO);
        if (StringUtils.isNotBlank(opinionVO.getBusinessManagerName())){
            paramsBO.setBusinessManager(opinionVO.getBusinessManagerName());
            paramsBO.setBusinessManagerId(opportunityDetail.getBusinessManagerId());

            SOptyTeamBO sOptyTeamBO = teamConverter.teamUtil(null,opportunityDetail, TeamConverter.ROLE_OWNER_CODE);
            teamConverter.saveOrUpdate(sOptyTeamBO);
        }
        opportunityDetailDao.updateOpportunityApproveInfo(opportunityDetail);

        modifyApprovalCenterProcessVariables(flowInstanceId, paramsBO, null);
    }


    private OpportunityDetail buildOpportunityDetail(String businessId, OpportunityOpinionVO approvalOpinion) {
        OpportunityDetail opportunityDetail = new OpportunityDetail();
        opportunityDetail.setRowId(businessId);
        opportunityDetail.setTsApprovalNumber(approvalOpinion.getTsApprovalNumber());
        if (StringUtils.isNotEmpty(approvalOpinion.getBusinessManagerName())) {
            String businessManagerName = approvalOpinion.getBusinessManagerName().replaceAll(OpportunityConstant.REG_CN, "");
            String businessManagerId = approvalOpinion.getBusinessManagerName().replaceAll(OpportunityConstant.REG_NO, "");
            opportunityDetail.setBusinessManagerId(businessManagerId);
            opportunityDetail.setBusinessManagerName(businessManagerName);
        }
        opportunityDetail.setLastUpd(new Date());
        return opportunityDetail;
    }

    /**
     * 获取仲裁审批人
     * @param callBackMsgInfo
     * @return
     */
    @Override
    public String getArbitrationApprover(ApprovalCallBackMsgInfo callBackMsgInfo) {
        String logStr = "getArbitrationApprover,input:" + JSON.toJSONString(callBackMsgInfo);
        List<String> roleCodes = OpportunityRoleEnum.getOpportunityRoleCodeByNodeNameOrCode(callBackMsgInfo.getNodeName());
//        if (CollectionUtils.isEmpty(roleCodes)){
//            throw new BusiException(RetCode.SERVERERROR_CODE, "getArbitrationApprover.role.null");
//        }
        OpportunityDetail opportunityDetail = opportunityDetailService.get(callBackMsgInfo.getBusinessId());
        String approvalPersons;
        if (null == opportunityDetail){
            throw new BusiException(RetCode.SERVERERROR_CODE, "getArbitrationApprover.opportunity.null");
        }
        try {
            logger.info("getArbitrationApprover,input:{}", callBackMsgInfo);
            approvalPersons = doGetArbitrationApprover(opportunityDetail, roleCodes);
            logStr = logStr + "; output:" + approvalPersons;
            logger.info("getArbitrationApprover,{}", logStr);
            loggerService.synSaveLogger(logStr, "getArbitrationApprover");
        }catch (Exception e){
            logger.error("getArbitrationApprover error", e);
            logStr = logStr + "; error:" + ExceptionMsgUtils.getStackTrace(e, 500);
            loggerService.synSaveLogger(logStr, "getArbitrationApprover");
            throw new BusiException(RetCode.SERVERERROR_CODE,
                    LocalMessageUtils.getMessage("getArbitrationApprover.error", callBackMsgInfo.getNodeName(),
                            roleCodes.toString()));
        }
        if (StringUtils.isBlank(approvalPersons)){
            throw new BusiException(RetCode.SERVERERROR_CODE,
                    LocalMessageUtils.getMessage("getArbitrationApprover.null", callBackMsgInfo.getNodeName(),
                            roleCodes.toString()));
        }
        return approvalPersons;
    }

    private String doGetArbitrationApprover(OpportunityDetail opportunityDetail, List<String> roleCodes) throws Exception{
        List<CompletableFuture<List<RoleInfo>>> approvalPersonFutures = new ArrayList<>();
        for (String roleCode : roleCodes) {
            approvalPersonFutures.add(getApprovalPersons(opportunityDetail, roleCode));
        }
        CompletableFuture<List<UserVO>> personFutureAdmin = getPersonsUnconstrained(OpportunityRoleEnum.GEC_OPPORTUNITY_ADMIN.getCode());
        List<RoleInfo> roleInfos = approvalPersonFutures.stream().map(CompletableFuture::join).flatMap(Collection::stream).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(roleInfos)){
            return roleInfos.stream().map(RoleInfo::getEmpNo).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(","));
        }else{
            return this.personFutureAdminResult(personFutureAdmin);
        }
    }

    private String personFutureAdminResult(CompletableFuture<List<UserVO>> personFutureAdmin) throws Exception {
        if(null == personFutureAdmin) {
            return "";
        }
        List<UserVO> adminPerson = personFutureAdmin.get();
        return adminPerson.stream().map(UserVO::getEmpidui)
                .filter(StringUtils::isNotBlank).distinct()
                .collect(Collectors.joining(","));
    }

    /**
     * 获取商机审批人-异步
     * @param opportunityDetail
     * @param roleCode
     * @return
     */
    @Override
    public CompletableFuture<List<RoleInfo>> getApprovalPersons(OpportunityDetail opportunityDetail, String roleCode){
        SysGlobalConstVo sysGlobalConstVo = CommonUtils.getSysGlobalConstVo();
        return CompletableFuture.supplyAsync(() -> {
            CommonUtils.setSysGlobalConstVo(sysGlobalConstVo);
            AuthorizationRoleInfoDTO entity = new AuthorizationRoleInfoDTO();
            entity.setIndustryId(opportunityDetail.getFinalCustomerChildTrade());
            entity.setOrgId(opportunityDetail.getDeptNo());
            entity.setModuleCode(OpportunityConstant.OPPORTUNITY_MANAGEMENT);
            entity.setSysFlag(OpportunityConstant.PRM);
            entity.setRoleCode(roleCode);
            return queryRoleInfoWithConstraint(entity);
        });
    }

    /**
     * 无约束获取角色人员
     * @param roleCode
     * @return
     */
    private CompletableFuture<List<UserVO>> getPersonsUnconstrained(String roleCode){
        SysGlobalConstVo sysGlobalConstVo = CommonUtils.getSysGlobalConstVo();
        return CompletableFuture.supplyAsync(() -> {
            CommonUtils.setSysGlobalConstVo(sysGlobalConstVo);
            Map<String, List<UserVO>> result = authService.getPersonsUnconstrained(Collections.singletonList(roleCode));
            if (null == result){
                return Collections.emptyList();
            }
            return result.get(roleCode);
        });
    }

    /**
     * 参数校验
     * @param opinion
     * @return
     */
    private void verifyParameters(OpportunityOpinionDTO opinion, RestrictedPartyVO restrictedPartyInfo) {
        //校验审批结果
        if(StringUtils.isBlank(opinion.getResult())) {
            throw new BusiException(RetCode.VALIDATIONERROR_CODE, LocalMessageUtils.getMessage("approvalReasonNull"));
        }
        //校验审批意见
        if(StringUtils.isBlank(opinion.getOpinion())) {
            throw new BusiException(RetCode.VALIDATIONERROR_CODE, LocalMessageUtils.getMessage("approvalOpinionNull"));
        }
        //校验中兴业务经理
        if(Objects.equals(opinion.getResult(), ApprovalOpinionEnum.Y.getDescEn()) && Objects.isNull(opinion.getBusinessManagerBO())) {
            throw new BusiException(RetCode.VALIDATIONERROR_CODE, LocalMessageUtils.getMessage("businessManagerNull"));
        }
        // 如果点击通过，校验受限制主体
        if(Objects.equals(opinion.getResult(), ApprovalOpinionEnum.Y.getDescEn())){
            checkRestrictedParty(restrictedPartyInfo, opinion);
        }
    }

    private void checkRestrictedParty(RestrictedPartyVO restrictedPartyInfo, OpportunityOpinionDTO opinion){
        List<RestrictedPartyEnum> restrictSubmissionSubjectTypes = Arrays.asList(RestrictedPartyEnum.EMBARGO,RestrictedPartyEnum.PENDING);

        boolean conditionA = restrictSubmissionSubjectTypes.contains(RestrictedPartyEnum.valueOfCode(restrictedPartyInfo.getCustomerRestrictedPartyCode()))
                || restrictSubmissionSubjectTypes.contains(RestrictedPartyEnum.valueOfCode(restrictedPartyInfo.getEndUserRestrictedPartyCode()));

        boolean conditionB = (RestrictedPartyEnum.YES.getCode().equalsIgnoreCase(restrictedPartyInfo.getCustomerRestrictedPartyCode())
                || RestrictedPartyEnum.YES.getCode().equalsIgnoreCase(restrictedPartyInfo.getEndUserRestrictedPartyCode()))
                && StringUtils.isBlank(opinion.getTsApprovalNo());

        if (conditionA){
            throw new BusiException(RetCode.VALIDATIONERROR_CODE, LocalMessageUtils.getMessage("restrictedParty.notPass"));
        }else if (conditionB){
            throw new BusiException(RetCode.VALIDATIONERROR_CODE, LocalMessageUtils.getMessage("tsApprovalNoNull"));
        }
    }


    private OpinionDTO setApprovalParam(OpportunityOpinionDTO opinion) {

        OpportunityOpinionVO vo = new OpportunityOpinionVO();
        vo.setSimilarOptyCd(opinion.getSimilarOptyCd());
        vo.setFailureReason(opinion.getFailureReason());

        OpinionDTO entity = new OpinionDTO();
        entity.setTaskId(opinion.getTaskId());
        entity.setResult(opinion.getResult());
        entity.setOpinion(opinion.getOpinion());
        entity.setExtOpinion(JSONObject.toJSONString(vo));

        return entity;
    }


    @Override
    public Boolean checkRestrictedParty(String deptNo,String lastAccId, String crmCustomerCode) throws Exception {
        String restrictedParty = null;
        //校验最终用户是否受限制主体
        if(StringUtils.isNotBlank(lastAccId)) {
            AccountInfo accountInfo = customerInfoService.getCustomerDetailsV2(deptNo,lastAccId);
            if(Objects.nonNull(accountInfo)) {
                restrictedParty = accountInfo.getRestrictedPartyCode();
            }
        }
        logger.info("最终用户lastAccId:{},受限制主体为{}", lastAccId, restrictedParty);
        if(Boolean.TRUE.equals(RestrictedPartyEnum.isRestrictedParty(restrictedParty))) {
            return Boolean.TRUE;
        }
        //校验渠道商是否受限制主体
        if(StringUtils.isNotBlank(crmCustomerCode)) {
            ChannelCustomerRes channelInfo = composeService.getChannelInfo(crmCustomerCode);
            if(Objects.nonNull(channelInfo)) {
                restrictedParty = channelInfo.getPartnerGtsFlag();
            }
        }
        logger.info("渠道商crmCustomerCode:{},受限制主体为{}", crmCustomerCode, restrictedParty);
        if(Boolean.TRUE.equals(RestrictedPartyEnum.isRestrictedParty(restrictedParty))) {
            return true;
        }
        return false;
    }

    @Override
    public RestrictedPartyVO getRestrictedParty(String lastAccId, String crmCustomerCode) throws Exception {
        List<AccountInfo> accountInfoList = customerInfoService.getCustomerInformationBatch(Arrays.asList(lastAccId, crmCustomerCode));
        return getRestrictedPartyVO(lastAccId, crmCustomerCode, accountInfoList);
    }

    @Override
    public RestrictedPartyVO getRestrictedPartyV2(String lastAccId, String crmCustomerCode,String deptNo) throws Exception {
        List<AccountInfo> accountInfoList = customerInfoService.getCustomerInformationBatchV2(deptNo,Arrays.asList(lastAccId, crmCustomerCode));
        return getRestrictedPartyVO(lastAccId, crmCustomerCode, accountInfoList);
    }

    private static RestrictedPartyVO getRestrictedPartyVO(String lastAccId, String crmCustomerCode, List<AccountInfo> accountInfoList) {
        AccountInfo accountInfo = null;
        AccountInfo channelInfo = null;
        RestrictedPartyVO.RestrictedPartyVOBuilder restrictedPartyVOBuilder = RestrictedPartyVO.builder();
        Map<String, AccountInfo> accountInfoMap = null;

        if (CollectionUtils.isNotEmpty(accountInfoList)){
            accountInfoMap = accountInfoList.stream()
                    .collect(Collectors.toMap(AccountInfo::getAccountNum, Function.identity()));
            Map<String, AccountInfo> accountInfoIdMap = accountInfoList.stream()
                    .collect(Collectors.toMap(AccountInfo::getId, Function.identity()));
            accountInfoMap.putAll(accountInfoIdMap);
        }

        if (null != accountInfoMap){
            channelInfo = accountInfoMap.get(crmCustomerCode);
            accountInfo = accountInfoMap.get(lastAccId);
        }

        if(Objects.nonNull(channelInfo)) {
            restrictedPartyVOBuilder.customerName(channelInfo.getAccountName())
                    .customerRestrictedPartyCode(channelInfo.getRestrictedPartyCode())
                    .customerRestrictedPartyName(channelInfo.getRestrictedParty());
            logger.info("渠道商crmCustomerCode:{},受限制主体为{}", crmCustomerCode, channelInfo.getRestrictedParty());
        }

        if(Objects.nonNull(accountInfo)) {
            restrictedPartyVOBuilder.endUserName(accountInfo.getAccountName())
                    .endUserRestrictedPartyCode(accountInfo.getRestrictedPartyCode())
                    .endUserRestrictedPartyName(accountInfo.getRestrictedParty());
            logger.info("最终用户lastAccId:{},受限制主体为{}", lastAccId, accountInfo.getRestrictedParty());
        }
        return restrictedPartyVOBuilder.build();
    }

    @Override
    public List<OpptyStatus> queryOpptyProgress(String rowId) throws Exception {
        Opportunity opportunity = opportunityDao.get(rowId);
        // 根据状态生成进度条
        if (opportunity == null) {
            return Collections.EMPTY_LIST;
        }
        opportunity.setBusinessTypeCd(CommonMapUtil.DELIVERY_INTERFACE_MAP.inverse().getOrDefault(opportunity.getBusinessTypeCd(), opportunity.getBusinessTypeCd()));
        String statusCd = opportunity.getStatusCd();
        if (!opportunity.getOptyCd().startsWith(OpportunityConstant.OPPORTUNITY_PREFIX) && OptyStatusEnum.TICKET_LOSS.getCode().equals(statusCd)){
            statusCd = OptyStatusEnum.OPTY_SUSPEND.getCode();
        }
        List<ComDictionaryMaintainVO> dictMaintain = comDictionaryMaintainService
                .queryByType(ComDictionaryMaintainConsts.OPPORTUNITY_STATUS);
        return OpptyProgressEnum.getOpptyStatus(statusCd, dictMaintain);
    }

    @Override
    public List<ApprovalNode> queryApprovalNodes(String rowId) throws Exception {
        // 查询商机审批任务
        List<ApprovedTask> approvalTasks = queryApprovedTasks(rowId);
        if (CollectionUtils.isEmpty(approvalTasks)) {
            return  Collections.EMPTY_LIST;
        }
        String workFlowInstanceId = comApprovalRecordDao.queryFlowInstance(rowId);
        List<ApprovalNode> approvalNodes = Lists.newArrayList();
        for (ApprovedTask approvedTask : approvalTasks) {
            if (StringUtils.equalsIgnoreCase(approvedTask.getTaskStatus(), OpportunityConstant.APPROVAL_TASK_COMPLETED)
                    && StringUtils.equalsIgnoreCase(approvedTask.getResult(),OpportunityConstant.APPROVAL_Y)) {
                approvedTask.setResultCN(OpportunityConstant.APPROVAL_Y_NAME);
            }else if(StringUtils.equalsIgnoreCase(approvedTask.getResult(),OpportunityConstant.APPROVAL_N)) {
                approvedTask.setResultCN(OpportunityConstant.APPROVAL_N_NAME);
            }
            ApprovalNode approvalNode = new ApprovalNode();
            BeanUtils.copyProperties(approvedTask,approvalNode);
            if (OpportunityConstant.APPROVAL_TASK_ACTIVE.equalsIgnoreCase(approvalNode.getTaskStatus())) {
                if (StringUtils.equalsIgnoreCase(approvalNode.getApprover(), CommonUtils.getEmpNo())) {
                    approvalNode.setReassignOp(Boolean.TRUE);
                }else{
                    approvalNode.setReassignOp(iOpportunityService.verifyAuth(rowId, OpportunityConstant.PRM));
                }
            }
            approvalNode.setFlowInstanceId(workFlowInstanceId);
            approvalNodes.add(approvalNode);

        }
        //替换工号
        EmpProcessUtil.replaceEmpNo(approvalNodes);
        return approvalNodes;
    }

    @Override
    public List<ApprovalDetail> queryApprovalDetail(String rowId) throws Exception {
        List<ApprovalDetail> approvalDetailList = getApprovalDetailList(rowId);
        EmpProcessUtil.replaceEmpNo(Lists.newArrayList(approvalDetailList));
        return approvalDetailList;
    }
    private List<ApprovalDetail> getApprovalDetailList(String rowId) throws Exception{
        List<ApprovedTask> approvalTasks = queryApprovedTasks(rowId);
        if (CollectionUtils.isEmpty(approvalTasks)){
            return Collections.EMPTY_LIST;
        }
        OpportunityDetail detail = opportunityDetailDao.get(rowId);
        List<ApprovalDetail> approvalDetails = new ArrayList<>();
        for (ApprovedTask approvalTask : approvalTasks) {
            OpportunityOpinionVO opinionDTO = null;
            ApprovalDetail approvalDetail = new ApprovalDetail();
            try {
                opinionDTO = JSONObject.parseObject(approvalTask.getExtOpinion(), OpportunityOpinionVO.class);
            } catch (Exception e){
                logger.info("json to parse OpportunityOpinionDTO error: {} ",e.getMessage());
            }

            if (null != opinionDTO){
                approvalDetail.setFailureReason(opinionDTO.getFailureReason());
                approvalDetail.setSimilarOptyCd(opinionDTO.getSimilarOptyCd());
            }
            setBusinessManager(approvalDetail, opinionDTO, detail);
            approvalDetail.setOpinion(approvalTask.getOpinion());
            approvalDetail.setSubmitter(approvalTask.getApprover());
            approvalDetail.setCreatedDate(approvalTask.getApprovalDate());
            approvalDetail.setNodeName(approvalTask.getNodeName());
            if (StringUtils.equalsIgnoreCase(approvalTask.getTaskStatus(), OpportunityConstant.APPROVAL_TASK_COMPLETED)
                    && StringUtils.equalsIgnoreCase(approvalTask.getResult(),OpportunityConstant.APPROVAL_Y)) {
                approvalDetail.setResult(OpportunityConstant.APPROVAL_Y_NAME);
            }else if(StringUtils.equalsIgnoreCase(approvalTask.getResult(),OpportunityConstant.APPROVAL_N)) {
                approvalDetail.setResult(OpportunityConstant.APPROVAL_N_NAME);
            }

            if (StringUtils.equals(approvalTask.getTaskStatus(),"COMPLETED")){
                approvalDetails.add(approvalDetail);
            }
        }
        return approvalDetails;
    }

    private void setBusinessManager(ApprovalDetail approvalDetail, OpportunityOpinionVO opinionDTO, OpportunityDetail detail) {
        if (null != opinionDTO && StringUtils.isNotBlank(opinionDTO.getBusinessManagerName())) {
            String businessManagerName = opinionDTO.getBusinessManagerName().replaceAll(OpportunityConstant.REG_CN, "");
            String businessManagerId = opinionDTO.getBusinessManagerName().replaceAll(OpportunityConstant.REG_NO, "");
            approvalDetail.setBusinessManagerId(businessManagerId);
            approvalDetail.setBusinessManagerName(businessManagerName);
        } else {
            approvalDetail.setBusinessManagerId(detail.getBusinessManagerId());
            approvalDetail.setBusinessManagerName(detail.getBusinessManagerName());
        }
    }

    @Override
    public List<ApprovedTask> queryApprovedTasks(String rowId) throws Exception {
        ComApprovalRecord comApprovalRecord = comApprovalRecordDao.getByBusinessId(rowId);
        if (comApprovalRecord == null) {
            return Collections.EMPTY_LIST;
        }
        String workFlowInstanceId = comApprovalRecord.getWorkFlowInstanceId();
        return getApprovedTasks(workFlowInstanceId);
    }

    @Override
    public List<ApprovedTask> getApprovedTasks(String workFlowInstanceId) throws Exception {
        FlowParameter flowParameter = new FlowParameter();
        flowParameter.setFlowInstanceId(workFlowInstanceId);
        List<ApprovalRecordsDTO> approvalRecords = approvalFlowService.getApprovalRecords(flowParameter);
        logger.info("getApprovedTasks,workFlowInstanceId:{},return:{}",workFlowInstanceId, approvalRecords);
        if (CollectionUtils.isEmpty(approvalRecords)) {
            return Collections.EMPTY_LIST;
        }
        List<ApprovedTask> approvalTasks = approvalRecords.get(0).getApprovalTaskRecordList();

        return approvalTasks;
    }

    /**
     * 最终用户状态查询
     *
     * @param rowId
     */
    @Override
    public PrmLastUserStatusVO queryLastUserStatus(String rowId) throws com.zte.springbootframe.common.exception.BusiException {
        OpportunityDetail opportunityDetail = opportunityDetailDao.get(rowId);
        if (Objects.isNull(opportunityDetail)) {
            return null;
        }
        PrmLastUserStatusVO prmLastUserStatusVO = new PrmLastUserStatusVO();
        BeanUtils.copyProperties(opportunityDetail, prmLastUserStatusVO);
        boolean needJudge = opportunityDetail.getLastAccStatus() == null
                || LastAccStatusEnum.WAIT_CREATE_CUSTOMER_DRAFT.getKey().equals(opportunityDetail.getLastAccStatus())
                || LastAccStatusEnum.NO_STATUS.getKey().equals(opportunityDetail.getLastAccStatus());

        if (needJudge) {
            LastAccStatusEnum lastAccStatus = getCustomerStatus(opportunityDetail.getDeptNo(),opportunityDetail.getLastAccName());
            if (LastAccStatusEnum.NO_STATUS != lastAccStatus) {
                opportunityDetailService.updateLastAccStatus(rowId, LastAccStatusEnum.CUSTOMER_DRAFT_CREATED.getKey());
            }
            prmLastUserStatusVO.setLastAccStatus(lastAccStatus.getKey());
        }
        return prmLastUserStatusVO;
    }

    private LastAccStatusEnum getCustomerStatus(String deptNo,String customerName) throws com.zte.springbootframe.common.exception.BusiException {
        //查询最终客户是否存在且生效
        List<AccountInfo> accountInfoList = customerInfoService.getCustomerInformationByNameV2(deptNo,customerName);
        if(CollectionUtils.isNotEmpty(accountInfoList)){
            List<AccountInfo> existedCustomer = accountInfoList.stream()
                    .filter(accountInfo -> AccountStatusEnum.isCreatedCustomer(accountInfo.getActiveStatusCode()))
                    .filter(accountInfo -> OpportunityConstant.CUSTOMER_COUNTRY_ID_CHINA.equals(accountInfo.getCountryId()))
                    .collect(Collectors.toList());
            List<AccountInfo> activatedCustomer = accountInfoList.stream()
                    .filter(accountInfo -> AccountStatusEnum.ACCT_ACTIVE_STATUS.getValue().equals(accountInfo.getActiveStatusCode()))
                    .filter(accountInfo -> OpportunityConstant.CUSTOMER_COUNTRY_ID_CHINA.equals(accountInfo.getCountryId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existedCustomer)){
               return LastAccStatusEnum.CUSTOMER_DRAFT_CREATED;
            }else if (CollectionUtils.isNotEmpty(activatedCustomer)){
                return LastAccStatusEnum.EFFECTIVE_CUSTOMER;
            }
        }
        return LastAccStatusEnum.NO_STATUS;
    }
}
