package com.zte.mcrm.adapter.controller;

import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import com.zte.mcrm.adapter.model.dto.*;
import com.zte.mcrm.adapter.model.vo.*;
import com.zte.mcrm.adapter.service.CustomerInfoService;
import com.zte.mcrm.adapter.service.ICpcService;
import com.zte.mcrm.adapter.service.ICpcServiceApi;
import com.zte.springbootframe.common.exception.BusiException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 描述：客户信息API
 * 创建时间：2021/9/15
 *
 * @author：王丹凤**********
 */
@Api(tags ="客户信息API")
@RestController
@RequestMapping("/customerInfo")
public class CustomerInfoController {

    @Autowired
    private CustomerInfoService customerInfoService;

    @Autowired
    private ICpcService cpcService;

    @Autowired
    private ICpcServiceApi cpcServiceApi;

    @ApiOperation("根据客户名称或编码模糊查询客户信息---客户管理系统")
    @PostMapping(value = "/account/getCustomerInfoByNameOrCode")
    public ServiceData<PageRows<AccountsVO>> getCustomerInfoByNameOrCode(@RequestBody FormData<ChannelCompanySearchDTO> form
            ) {
        PageRows<AccountsVO> pageRows = customerInfoService.getCustomerInfoByNameOrCode(form);
        return ServiceResultUtil.success(pageRows);
    }

    @ApiOperation("根据客户名称或编码模糊查询客户信息---支持租户和法人信息")
    @PostMapping(value = "/account/getCustomerInfoByNameOrCode/v2")
    public ServiceData<PageRows<AccountsVO>> getCustomerInfoByNameOrCodeV2(@RequestBody FormData<ChannelCompanySearchDTO> form) {
        PageRows<AccountsVO> pageRows = customerInfoService.getCustomerInfoByNameOrCodeV2(form);
        return ServiceResultUtil.success(pageRows);
    }

    @ApiOperation("根据公司名称模糊搜索分页获取公司信息接口---合作伙伴中心")
    @PostMapping(value = "/cpc/searchCompanyInfoList")
    public ServiceData<PageRows<UnregisteredCompanyVO>> queryCompanyInfoList(@RequestBody FormData<ChannelCompanySearchDTO> form) throws Exception{
        PageRows<UnregisteredCompanyVO> pageRows = cpcService.queryCompanyInfoList(form);
        return ServiceResultUtil.success(pageRows);
    }

    @ApiOperation("第三方法人基本信息查询服务-按关键字---合作伙伴中心")
    @PostMapping(value = "/cpc/queryOrgInfoByKeyword")
    public ServiceData<CompanyOrgInfoVO> queryOrgInfoByKeyword(@RequestBody ChannelCompanySearchDTO channelCompanySearch) throws Exception{
        CompanyOrgInfoVO companyOrgInfo = cpcService.queryOrgInfoByCustomerName(channelCompanySearch.toCompanyInfoSearchDTO());
        return ServiceResultUtil.success(companyOrgInfo);
    }

    @ApiOperation("法人工商快照查询服务---合作伙伴中心")
    @PostMapping(value = "/cpc/querySnapshot")
    public ServiceData<CompanySnapshotVO> querySnapshot(@RequestBody ChannelCompanySearchDTO channelCompanySearch) throws Exception{
        CompanySnapshotVO companySnapshotVO = cpcServiceApi.querySnapshot(channelCompanySearch.toCompanyInfoSearchDTO());
        return ServiceResultUtil.success(companySnapshotVO);
    }

    @ApiOperation("分页获取渠道商信息接口---合作伙伴中心")
    @PostMapping(value = "/cpc/partnerObscureQuery")
    public ServiceData<PageRows<PartnerListVO>> partnerObscureQuery(@RequestBody FormData<PartnerObscureQueryDTO> form) throws Exception{
        PageRows<PartnerListVO> pageRows = cpcServiceApi.partnerObscureQuery(form);
        return ServiceResultUtil.success(pageRows);
    }

    @ApiOperation("分页模糊查询渠道商信息接口")
    @PostMapping(value = "/cpc/partnerQueryByName")
    public ServiceData<PageRows<PartnerInfoVO>> partnerQueryByName(@RequestBody FormData<PartnerObscureQueryDTO> formData) throws Exception{
        PageRows<PartnerInfoVO> pageRows = customerInfoService.partnerQueryByName(formData);
        return ServiceResultUtil.success(pageRows);
    }

    @ApiOperation("查询渠道商认证等级信息接口")
    @GetMapping(value = "/partnerLevelByCrmCustomerCode")
    public ServiceData<PartnerLevelVO> partnerLevelByCrmCustomerCode(String crmCustomerCode) throws Exception{
        PartnerLevelVO result = customerInfoService.partnerLevelByCrmCustomerCode(crmCustomerCode);
        return ServiceResultUtil.success(result);
    }

    @ApiOperation("根据客户名称或编码模糊查询最终客户")
    @PostMapping(value = "/getEndCustomers")
    public ServiceData<List<EndCustomerVO>> getEndCustomerByCustomerNamekeyWord(@RequestBody FormData<ChannelCompanySearchDTO> form)
    {
        List<EndCustomerVO> list = customerInfoService.getEndCustomerList(form);
        return ServiceResultUtil.success(list);
    }

    @ApiOperation("根据客户编码查询客户")
    @GetMapping(value = "/getCustomerInfo")
    public ServiceData<AccountInfo> getCustomerInfo(@RequestParam(required = false)String organizationId,@RequestParam String customerCode) throws BusiException {
        AccountInfo accountInfo = customerInfoService.getCustomerDetailsV2(organizationId,customerCode);
        return ServiceResultUtil.success(accountInfo);
    }


    @ApiOperation("根据客户编码批量查询客户")
    @PostMapping(value = "/getCustomerInfoBatch")
    public ServiceData<List<AccountInfo>> getCustomerInfo(@RequestParam(required = false) String organizationId,
                                                          @RequestBody List<String> customerCodeList) throws BusiException {
        List<AccountInfo> accountInfoList = customerInfoService.getCustomerInformationBatchV2(organizationId,customerCodeList);
        return ServiceResultUtil.success(accountInfoList);
    }

    @ApiOperation("通过渠道商客户编码获取公司的黄黑名单信息")
    @PostMapping(value = "/cpc/getBlackListInfo")
    public ServiceData<List<BlackListDTO>> getBlackListInfo(@RequestParam(name="crmCustomerCode") String crmCustomerCode) throws Exception {
        List<BlackListDTO> list = cpcService.getBlackListInfoByCrmCustomerCode(crmCustomerCode);
        return ServiceResultUtil.success(list);
    }

    @ApiOperation("创建客户草稿---客户管理系统")
    @PostMapping(value = "/createCustomer")
    public ServiceData<String> createCustomer(@RequestBody CreateCustomerParam param) throws Exception {
        String result = customerInfoService.createCustomer(param);
        return ServiceResultUtil.success(result);
    }

    @ApiOperation("客户主体分类查询——可通过客户编码列表精确查询或客户名称列表精确查询或客户名称模糊查询")
    @PostMapping(value = "/custClassify")
    public ServiceData<List<CustClassifyExternalVO>> getCustClassify(@RequestBody CustClassifyExternalQueryDTO custClassifyExternalQueryDTO) throws Exception {
        List<CustClassifyExternalVO> result = customerInfoService.getCustClassify(custClassifyExternalQueryDTO);
        return ServiceResultUtil.success(result);
    }

    @ApiOperation("客户主体分类查询——可通过客户编码列表精确查询或客户名称列表精确查询或客户名称模糊查询")
    @PostMapping(value = "/custClassifyAndValidate")
    public ServiceData<List<CustClassifyExternalVO>> getCustClassifyAndValidate(@RequestBody CustClassifyExternalQueryDTO custClassifyExternalQueryDTO) throws Exception {
        List<CustClassifyExternalVO> result = customerInfoService.getCustClassifyAndValidate(custClassifyExternalQueryDTO);
        return ServiceResultUtil.success(result);
    }

    @ApiOperation("查询第三方法人基本信息---合作伙伴中心")
    @GetMapping(value="/cpc/getCompanyInfo")
    public ServiceData<UnregisteredCompanyVO> getCompanyDeatil(@RequestParam("partnerName") String partnerName)
    {
        return ServiceResultUtil.success(cpcService.searchCompanyInfo(partnerName));
    }
}

