package com.zte.mcrm.adapter.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zte.itp.msa.core.model.FormData;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.mcrm.adapter.constant.CustomerConstant;
import com.zte.mcrm.adapter.model.dto.*;
import com.zte.mcrm.adapter.model.mapper.CustomerDetailInfoMapper;
import com.zte.mcrm.adapter.model.mapper.PartnerLevelMapper;
import com.zte.mcrm.adapter.model.vo.*;
import com.zte.mcrm.adapter.service.*;
import com.zte.mcrm.channel.constant.AccountStatusEnum;
import com.zte.mcrm.channel.constant.AcctMergeFlagEnum;
import com.zte.mcrm.channel.constant.FrozenFlagEnum;
import com.zte.mcrm.channel.constant.OpportunityConstant;
import com.zte.mcrm.channel.model.mapper.PartnerMapper;
import com.zte.mcrm.clues.common.CluesSysConst;
import com.zte.mcrm.common.consts.CommonConst;
import com.zte.mcrm.common.consts.HttpMethodEnum;
import com.zte.mcrm.common.exception.BusinessRuntimeException;
import com.zte.mcrm.common.framework.exception.ErrorCode;
import com.zte.mcrm.common.framework.exception.ErrorCodeException;
import com.zte.mcrm.common.util.CommonUtils;
import com.zte.mcrm.common.util.MsaUtils;
import com.zte.mcrm.common.util.RequestMessage;
import com.zte.mcrm.feign.InOneOrgInfoClient;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import com.zte.springbootframe.common.exception.BusiException;
import com.zte.springbootframe.common.model.HttpResultData;
import com.zte.springbootframe.common.serviceregister.MicroServiceWithConfigUtil;
import com.zte.springbootframe.common.serviceregister.UniworkServiceUtil;
import com.zte.springbootframe.util.json.JacksonJsonConverUtil;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述：客户信息查询
 * 创建时间：2021/9/15
 *
 * @author：王丹凤6396000572
 */
@Service
public class CustomerInfoServiceImpl implements CustomerInfoService {

    /** 日志对象*/
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ICpcService cpcService;
    @Autowired
    private ICpcServiceApi cpcServiceApi;
    @Autowired
    private PrmService prmServiceImpl;

    @Autowired
    private IHrmHolOrgInfo iHrmHolEmployee;

    @Value("${cpc.partnerSmallCategory:channelVendor}")
    private String partnerSmallCategory;

    private final static String CMS_ACCOUNTS_URL="/cms/accounts";

    private final static String CMS_ACCOUNTS_V2_URL="/cms/accounts/v2";

    /**
     * 查询客户信息
     */
    private final static String NO_PERMISON_ACCOUNT_BATCHBY_NAME_URL="/noPermisonAccountBatchByName";
    private final static String NO_PERMISON_ACCOUNT_BATCHBY_NAME_URL_V2="/noPermisonAccountBatchByName/v2";

    /**
     * 商机详情，最终用户受限制主体
     */
    private final static String NO_PERMISON_ACCOUNT_BATCH_URL="/noPermisonAccountBatch";
    private final static String NO_PERMISON_ACCOUNT_BATCH_V2_URL="/noPermisonAccountBatch/v2";

    /**
     * 查询客户详情
     */
    private final static String NO_MKT_ACCOUNT_URL="/noMktAccount";
    private final static String NO_MKT_ACCOUNT_V2_URL="/noMktAccount/v2";

    /**
     * 查询客户主体分类信息
     */
    private final static String CUSTCLASSIFY_URL="/custClassify/external";
    private final static String CUSTCLASSIFY_V2_URL="/custClassify/external/v2";

    /**
     * 查询客户信息
     */
    private final static String NO_PERMISON_ACCOUNT_V2_URL="/noPermisonAccount/v2";
    private final static String NO_PERMISON_ACCOUNT_URL="/noPermisonAccount";

    /* Started by AICoder, pid:60b0ae6e9ae24ba99c507e48227ddae5 */
    @Override
    public PageRows<AccountsVO> getCustomerInfoByNameOrCode(FormData<ChannelCompanySearchDTO> form) {
        Map<String, String> headerMap = RequestMessage.getHeader("SIEBEL_PROJECT");
        ChannelCompanySearchDTO paramBO = form.getBo();
        if (paramBO == null) {
            return createEmptyPageRows();
        }
        //参数
        Map<String, String> paramMap = getAccountParamMap(paramBO, form);
        return getAccountsVOPageRows(paramMap, headerMap, CMS_ACCOUNTS_URL);
    }

    @Override
    public PageRows<AccountsVO> getCustomerInfoByNameOrCodeV2(FormData<ChannelCompanySearchDTO> form) {
        ChannelCompanySearchDTO paramBO = form.getBo();
        if (paramBO == null) {
            return createEmptyPageRows();
        }
        //参数
        Map<String, String> paramMap = getAccountParamMap(paramBO, form);
        if (StringUtils.isNotEmpty(paramBO.getOrganizationId())) {
            //1、根据组织ID获取HR对应组织信息
            OrgInfoDTO orgInfo = iHrmHolEmployee.getOrgInfo(paramBO.getOrganizationId());
            //2、根据公司ID，获取uniwork法人编码
            BizLegalEntityVO bizLegalEntityVO = UniworkServiceUtil.getUniworkLegalEntityV2(orgInfo);
            if (Objects.nonNull(bizLegalEntityVO)) {
                //法人id
                paramMap.put("corporateNo", bizLegalEntityVO.getLegalId());
            }
            //header
            Map<String, String> headerMap = RequestMessage.getHeaderExt(orgInfo.getTenantId());
            //3、获取Account客户信息
            return getAccountsVOPageRows(paramMap, headerMap, CMS_ACCOUNTS_V2_URL);
        } else {
            //参数
            Map<String, String> headerMap = RequestMessage.getHeader("SIEBEL_PROJECT");
            return getAccountsVOPageRows(paramMap, headerMap, CMS_ACCOUNTS_URL);
        }
    }

    private Map<String, String> getAccountParamMap(ChannelCompanySearchDTO paramBO, FormData<ChannelCompanySearchDTO> form) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("accountName", paramBO.getCustomerNamekeyWord());
        paramMap.put("filterHistoryName", paramBO.getFilterHistoryName());
        paramMap.put("filterFrozenCustomer", paramBO.getFilterFrozenCustomer());
        paramMap.put("pageNo", String.valueOf(form.getPage()));
        paramMap.put("pageSize", String.valueOf(form.getRows()));
        return paramMap;
    }

    private PageRows<AccountsVO> getAccountsVOPageRows(Map<String, String> paramMap, Map<String, String> headerMap, String url) {
        PageRows<AccountsVO> pageRows = new PageRows<>();
        pageRows.setTotal(0L);
        try {
            logger.info("getAccountsVOPageRows,paramBO:{},header:{}", paramMap, headerMap);
            HttpResultData accountHttpResult = MicroServiceWithConfigUtil.invokeServiceWithGetMethod(CluesSysConst.MIROSERVICES_FIVE,
                    url, paramMap, headerMap);
            if (accountHttpResult != null) {
                String accounts = JacksonJsonConverUtil.beanToJson(accountHttpResult.getBo());
                logger.info("getCustomerInfoByNameOrCode,paramBO:{},accounts:{}", paramMap, accounts);
                pageRows = JacksonJsonConverUtil.jsonToListBeanOther(accounts, new TypeReference<PageRows<AccountsVO>>() {
                });
            }
        } catch (Exception e) {
            logger.error("CustomerInfoServiceImpl.getCustomerInfoByNameOrCode exceptiton：" + e);
        }
        return pageRows;
    }

    private PageRows<AccountsVO> createEmptyPageRows() {
        PageRows<AccountsVO> pageRows = new PageRows<>();
        pageRows.setTotal(0L);
        pageRows.setRows(Collections.emptyList());
        return pageRows;
    }
    /* Ended by AICoder, pid:60b0ae6e9ae24ba99c507e48227ddae5 */

    @Override
    public List<EndCustomerVO> getEndCustomerList(FormData<ChannelCompanySearchDTO> form) {
        ChannelCompanySearchDTO channelCompanySearchDTO = form.getBo();
        if (Objects.isNull(channelCompanySearchDTO) || StringUtils.isBlank(channelCompanySearchDTO.getCustomerNamekeyWord())){
            return Collections.emptyList();
        }
        List<EndCustomerVO> endCustomerList = new ArrayList<>();
        //先从客户管理系统查询客户信息，
        PageRows<AccountsVO> accountPageRows  = getCustomerInfoByNameOrCodeV2(form);
        if(accountPageRows!=null&&accountPageRows.getTotal()>0)
        {
            List<AccountsVO> accountsVOList = accountPageRows.getRows();
            for(AccountsVO account:accountsVOList)
            {
                EndCustomerVO endCustomerVO = new EndCustomerVO();
                endCustomerVO.setEndCustomerId(account.getAccountNum());
                endCustomerVO.setEndCustomerName(account.getAccountName());
                endCustomerVO.setRestrictedParty(account.getSanctionedPatry());
                endCustomerVO.setRestrictedPartyCode(account.getSanctionedPatryCode());
                endCustomerList.add(endCustomerVO);
            }
            return endCustomerList;
        }
        else
            {
                //如果客户管理系统没有查询到记录，就从合作伙伴中心查询
                PageRows<UnregisteredCompanyVO> cpcCompanyPageRows = cpcService.queryCompanyInfoList(form);
                if (null==cpcCompanyPageRows||cpcCompanyPageRows.getTotal()==0)
                {
                    return endCustomerList;
                }
                List<UnregisteredCompanyVO> companyVOList = cpcCompanyPageRows.getRows();
                for(UnregisteredCompanyVO companyVO:companyVOList)
                {
                    EndCustomerVO endCustomer = new EndCustomerVO();
                    endCustomer.setEndCustomerName(companyVO.getCustomerName());
                    endCustomerList.add(endCustomer);
                }

            }
        return endCustomerList;
    }

    @Override
    public List<AccountInfo> getCustomerInformationByName(String accountName) throws BusiException {
        Map<String,String> paramMap = new HashMap<>(3);
        paramMap.put("accountName", accountName);
        Map<String, String> headerMap = RequestMessage.getHeader("SIEBEL_ACCOUNT");
        HttpResultData accountHttpResult = MicroServiceWithConfigUtil.invokeServiceWithGetMethod(CluesSysConst.MIROSERVICES_FIVE,
                NO_PERMISON_ACCOUNT_BATCHBY_NAME_URL, paramMap, headerMap);
        HttpResultData httpResultData = Optional.ofNullable(accountHttpResult).orElse(new HttpResultData());
        List<AccountInfo> accountInfos = JSON.parseArray(JSON.toJSONString(httpResultData.getBo()), AccountInfo.class);
        if (CollectionUtils.isEmpty(accountInfos)) {
            return Lists.newArrayList();
        }
        return accountInfos;
    }

    @Override
    public List<AccountInfo> getCustomerInformationByNameV2(String deptNo,String accountName) throws BusiException {
        if (StringUtils.isEmpty(accountName)) {
           return Lists.newArrayList();
        }
        Map<String,String> paramMap = new HashMap<>(3);
        paramMap.put("accountName", accountName);
        HttpResultData accountHttpResult;
        if (StringUtils.isNotEmpty(deptNo)) {
            accountHttpResult = getHttpResultData(deptNo, paramMap, NO_PERMISON_ACCOUNT_BATCHBY_NAME_URL_V2);
        } else {
            Map<String, String> headerMap = RequestMessage.getHeader("SIEBEL_ACCOUNT");
            accountHttpResult = MicroServiceWithConfigUtil.invokeServiceWithGetMethod(CluesSysConst.MIROSERVICES_FIVE,
                    NO_PERMISON_ACCOUNT_BATCHBY_NAME_URL, paramMap, headerMap);
        }
        HttpResultData httpResultData = Optional.ofNullable(accountHttpResult).orElse(new HttpResultData());
        List<AccountInfo> accountInfos = JSON.parseArray(JSON.toJSONString(httpResultData.getBo()), AccountInfo.class);
        if (CollectionUtils.isEmpty(accountInfos)) {
            return Lists.newArrayList();
        }
        return accountInfos;
    }

    private HttpResultData getHttpResultData(String deptNo, Map<String, String> paramMap, String requestUrl) throws BusiException {
        //1、根据组织ID获取HR对应组织信息
        OrgInfoDTO orgInfo = iHrmHolEmployee.getOrgInfo(deptNo);
        logger.info("TenantId===="+orgInfo.getTenantId()+"CompanyId===="+orgInfo.getCompanyId());
        /**
         * 1、根据公司ID，获取uniwork法人编码
         */
        BizLegalEntityVO bizLegalEntityVO = UniworkServiceUtil.getUniworkLegalEntityV2(orgInfo);
        if (Objects.nonNull(bizLegalEntityVO)) {
            //法人id
            paramMap.put("corporateNo", bizLegalEntityVO.getLegalId());
        }
        //组织对应租户ID
        String tenantId = orgInfo.getTenantId();
        Map<String, String> headerMap = RequestMessage.getHeaderExt(tenantId);
        HttpResultData accountHttpResult = MicroServiceWithConfigUtil.invokeServiceWithGetMethod(CluesSysConst.MIROSERVICES_FIVE,
                requestUrl, paramMap, headerMap);
        return accountHttpResult;
    }

    @Override
    public List<AccountInfo> getAccountInfoBatchByName(List<String> accountNameList) {
        CustomerQueryCondition customerQueryCondition = new CustomerQueryCondition();
        customerQueryCondition.setFilterFrozen(false);
        customerQueryCondition.setFilterMerge(false);
        customerQueryCondition.setOnlyEffect(false);
        customerQueryCondition.setNameList(accountNameList);
        List<CustomerDetailInfoDTO> customerDetailInfoDTOS = queryCustomerDetailInfo(customerQueryCondition);
        if (CollectionUtils.isEmpty(customerDetailInfoDTOS)) {
            return Lists.newArrayList();
        }
        return CustomerDetailInfoMapper.INSTANCE.customerDetailInfoDTOToAccountInfoBatch(customerDetailInfoDTOS);
    }

    public List<CustomerDetailInfoDTO> queryCustomerDetailInfo(CustomerQueryCondition customerQueryCondition) {
        Map<String, String> paramString = new HashMap<>(3);
        paramString.put("serviceName", "zte-crm-account-info");
        paramString.put("url", "/api/customer/batchDetail");
        try {
            return MsaUtils.invokeServiceAndReturnBO(
                    paramString,
                    HttpMethodEnum.POST,
                    customerQueryCondition,
                    new com.alibaba.fastjson.TypeReference<ServiceData<List<CustomerDetailInfoDTO>>>() {
                    });
        } catch (Exception e) {
            logger.error("invoke service to query customer info failed, args:{},  error:{}", customerQueryCondition.toString(), e.getMessage(), e);
            throw new ErrorCodeException(ErrorCode.UniversalErrorEnum.OU3193);
        }
    }

    @Override
    public Map<String,AccountInfo> getAccountInfoByBatch(List<String> accountNameList) {
        Map<String, AccountInfo> accountInfoMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(accountNameList)) {
            return accountInfoMap;
        }
        // 去重
        List<String> accountNameListAfterDeduplication = accountNameList.stream().distinct().collect(Collectors.toList());
        //拆分列表，客户系统每次最多支持100个
        List<List<String>> accountNames = ListUtil.split(accountNameListAfterDeduplication, CustomerConstant.CUSTOM_ACCOUNT_BATCH_QUERY_MAX_SIZE);
        for (List<String> list : accountNames) {
            accountInfoMap.putAll(getAccountInfoByPriorityMap(list));
        }
        return accountInfoMap;
    }

    /**
     * 如果存在一个客户名称对应多条客户数据，如果存在有效的，则取有效的，否则取第一条数据
     *
     * @param   accountInfos 客户数据列表
     * @return  客户信息
     * <AUTHOR>
     * @date    2023/5/10
     */
    private AccountInfo getEffectAccountInfo(List<AccountInfo> accountInfos) {
        for (AccountInfo accountInfo : accountInfos) {
            if (StringUtils.equals(accountInfo.getActiveStatusCode(), AccountStatusEnum.ACCT_ACTIVE_STATUS.getValue())) {
                return accountInfo;
            }
        }
        return accountInfos.get(0);
    }

    @Override
    public List<AccountInfo> getCustomerInformationBatch(List<String> accountCodeList) throws BusiException {
        Map<String,String> paramMap = new HashMap<>(3);
        // 去重并去除空值
        accountCodeList = accountCodeList.stream()
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        paramMap.put("accountCode", String.join(",", accountCodeList));
        Map<String, String> headerMap = RequestMessage.getHeader("SIEBEL_ACCOUNT");
        HttpResultData accountHttpResult = MicroServiceWithConfigUtil.invokeServiceWithGetMethod(CluesSysConst.MIROSERVICES_FIVE,
                NO_PERMISON_ACCOUNT_BATCH_URL, paramMap, headerMap);
        HttpResultData httpResultData = Optional.ofNullable(accountHttpResult).orElse(new HttpResultData());
        return JSON.parseArray(JSON.toJSONString(httpResultData.getBo()), AccountInfo.class);
    }

    /**
     *
     * @param deptNo 组织编码
     * @param accountCodeList
     * @return
     * @throws BusiException
     */
    @Override
    public List<AccountInfo> getCustomerInformationBatchV2(String deptNo,List<String> accountCodeList) throws BusiException {
        Map<String, String> paramMap = new HashMap<>(3);

        accountCodeList = accountCodeList.stream()
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(accountCodeList)) {
            return null;
        }
        paramMap.put("accountCode", String.join(",", accountCodeList));
        HttpResultData accountHttpResult;
        if(StringUtils.isNotEmpty(deptNo)){
             accountHttpResult = getHttpResultData(deptNo,paramMap,NO_PERMISON_ACCOUNT_BATCH_V2_URL);
        }else {
            Map<String, String> headerMap = RequestMessage.getHeader("SIEBEL_ACCOUNT");
             accountHttpResult = MicroServiceWithConfigUtil.invokeServiceWithGetMethod(CluesSysConst.MIROSERVICES_FIVE,
                    NO_PERMISON_ACCOUNT_BATCH_URL, paramMap, headerMap);
        }
        HttpResultData httpResultData = Optional.ofNullable(accountHttpResult).orElse(new HttpResultData());
        return JSON.parseArray(JSON.toJSONString(httpResultData.getBo()), AccountInfo.class);
    }

    @Override
    public AccountInfo getCustomerDetails(String lastAccId) throws BusiException {
        Map<String,String> paramMap = new HashMap<>(3);
        paramMap.put("accountId", lastAccId);
        Map<String, String> headerMap = RequestMessage.getHeader("SIEBEL_ACCOUNT");
        HttpResultData accountHttpResult = MicroServiceWithConfigUtil.invokeServiceWithGetMethod(CluesSysConst.MIROSERVICES_FIVE,
                NO_PERMISON_ACCOUNT_URL, paramMap, headerMap);
        HttpResultData httpResultData = Optional.ofNullable(accountHttpResult).orElse(new HttpResultData());
        AccountInfo accountInfo = JSON.parseObject(JSON.toJSONString(httpResultData.getBo()), AccountInfo.class);
        logger.info("getCustomerDetails,accountId:{},accountInfo:{}",lastAccId,accountInfo);
        return accountInfo;
    }

    /**
     * 根据组织编码，获取不同对应法人下的客户信息
     * @param deptNo
     * @param lastAccId
     * @return
     * @throws BusiException
     */
    @Override
    public AccountInfo getCustomerDetailsV2(String deptNo,String lastAccId) throws BusiException {
        if(StringUtils.isEmpty(lastAccId)){
            return null;
        }
        Map<String,String> paramMap = new HashMap<>(3);
        paramMap.put("accountId", lastAccId);
        HttpResultData accountHttpResult;
        if (StringUtils.isNotEmpty(deptNo)) {
            accountHttpResult = getHttpResultData(deptNo,paramMap,NO_PERMISON_ACCOUNT_V2_URL);
        } else {
            Map<String, String> headerMap = RequestMessage.getHeader("SIEBEL_ACCOUNT");
            accountHttpResult = MicroServiceWithConfigUtil.invokeServiceWithGetMethod(CluesSysConst.MIROSERVICES_FIVE,
                    NO_PERMISON_ACCOUNT_URL, paramMap, headerMap);
        }
        HttpResultData httpResultData = Optional.ofNullable(accountHttpResult).orElse(new HttpResultData());
        AccountInfo accountInfo = JSON.parseObject(JSON.toJSONString(httpResultData.getBo()), AccountInfo.class);
        logger.info("getCustomerDetails,accountId:{},accountInfo:{}",lastAccId,accountInfo);
        return accountInfo;
    }

    @Override
    public AccountInfo getAccountByCodeOrId(String customerCode){
        AccountInfo accountInfo = new AccountInfo();
        try{
            AccountInfo accountInfoResult = getCustomerDetails(customerCode);
            if(null != accountInfoResult) {
                accountInfo = accountInfoResult;
            }
        }catch (Exception e){
            logger.error("获取客户id失败, customerCode:{}", customerCode, e);
        }
        return accountInfo;
    }


    @Override
    public String getAccountIdByCode(String customerCode){
        try{
            AccountInfo accountInfo = getCustomerDetails(customerCode);
            customerCode = accountInfo.getId();
        }catch (Exception e){
            logger.error("获取客户id失败, customerCode:{}", customerCode, e);
        }
        return customerCode;
    }



    @Override
    public String createCustomer(CreateCustomerParam paramObj) throws Exception {
        String result = "";
        if(null==paramObj)
        {
            return result;
        }
        Map<String, String> headerMap = RequestMessage.getHeader("SIEBEL_PROJECT");
        headerMap.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO, paramObj.getCreatedBy());
        HttpResultData accountHttpResult = MicroServiceWithConfigUtil.invokeServiceWithPostMethod(
                CluesSysConst.MIROSERVICES_FIVE,
                NO_MKT_ACCOUNT_URL,
                paramObj,
                headerMap);
        if(accountHttpResult!=null)
        {
            String rtnStr = JacksonJsonConverUtil.beanToJson(accountHttpResult.getBo());

            result = JacksonJsonConverUtil.jsonToListBeanOther(rtnStr, new TypeReference<String>() {});
        }
        return result;
    }

    /**
     * @param custClassifyExternalQueryDTO
     * @return
     * @throws BusiException
     * @throws RouteException
     */
    @Override
    public List<CustClassifyExternalVO> getCustClassifyAndValidate(CustClassifyExternalQueryDTO custClassifyExternalQueryDTO) throws BusiException, RouteException {
        List<CustClassifyExternalVO> result = getCustClassify(custClassifyExternalQueryDTO);
        if(CollectionUtils.isNotEmpty(result)) {
            Date now = new Date();
            Calendar cal = Calendar.getInstance();
            cal.setTime(now);
            cal.add(Calendar.YEAR, -1);
            result.forEach(custClassifyExternalVO ->
                    custClassifyExternalVO.setIsInTheOneYearValidityPeriod(!custClassifyExternalVO.getClassificationDate()
                            .before(cal.getTime())));
        }
        return result;
    }


    @Override
    public List<CustClassifyExternalVO> getCustClassify(CustClassifyExternalQueryDTO custClassifyExternalQueryDTO) throws BusiException, RouteException {
        List<CustClassifyExternalVO> result = new ArrayList<>();
        HttpResultData accountHttpResult;
        if (StringUtils.isNotEmpty(custClassifyExternalQueryDTO.getOrganizationId())) {
            //1、根据组织ID获取HR对应组织信息
            OrgInfoDTO orgInfo = iHrmHolEmployee.getOrgInfo(custClassifyExternalQueryDTO.getOrganizationId());
            //2、获取Uniwork 法人ID
            BizLegalEntityVO bizLegalEntityVO = UniworkServiceUtil.getUniworkLegalEntityV2(orgInfo);
            if (Objects.nonNull(bizLegalEntityVO)) {
                //法人id
                custClassifyExternalQueryDTO.setCorporateNo(bizLegalEntityVO.getLegalId());
            }
            Map<String, String> headerMap = RequestMessage.getHeaderExt(CommonUtils.getSubTenantId());
            accountHttpResult = MicroServiceWithConfigUtil.invokeServiceWithPostMethod(
                    CluesSysConst.MIROSERVICES_FIVE,
                    CUSTCLASSIFY_V2_URL,
                    custClassifyExternalQueryDTO,
                    headerMap);
        }else{
            Map<String, String> headerMap = RequestMessage.getHeader("SIEBEL_ACCOUNT");
            accountHttpResult = MicroServiceWithConfigUtil.invokeServiceWithPostMethod(
                    CluesSysConst.MIROSERVICES_FIVE,
                    CUSTCLASSIFY_URL,
                    custClassifyExternalQueryDTO,
                    headerMap);
        }
        if(accountHttpResult!=null)
        {
            String rtnStr = JacksonJsonConverUtil.beanToJson(accountHttpResult.getBo());
            result = JacksonJsonConverUtil.jsonToListBeanOther(rtnStr, new TypeReference<List<CustClassifyExternalVO>>() {});
        }
        return result;
    }



    /**
     * 分页模糊查询渠道商信息接口
     *
     * @param formData
     * @return
     */
    @Override
    public PageRows<PartnerInfoVO> partnerQueryByName(FormData<PartnerObscureQueryDTO> formData) throws Exception {
        formData.getBo().setPartnerSmallCategory(partnerSmallCategory);
        PageRows<PartnerListVO> pageRows = cpcServiceApi.partnerObscureQuery(formData);
        PageRows<PartnerInfoVO> newPageRows = new PageRows<>();
        List<PartnerInfoVO> newRows = new ArrayList<>();
        if(null == pageRows || CollectionUtils.isEmpty(pageRows.getRows())){
            newPageRows.setRows(newRows);
            newPageRows.setCurrent(formData.getPage());
            return newPageRows;
        }

        List<PartnerListVO> rows = pageRows.getRows();
        for(PartnerListVO partnerListVO : rows) {
            PartnerInfoVO partnerInfoVO = PartnerMapper.INSTANCE.transPartnerListVoToPartnerInfoVO(partnerListVO);
            newRows.add(partnerInfoVO);
        }

        newPageRows.setRows(newRows);
        newPageRows.setTotal(pageRows.getTotal());
        newPageRows.setCurrent(pageRows.getCurrent());

        return newPageRows;
    }

    /**
     * 查询渠道商认证等级信息接口
     * @param crmCustomerCode
     * @return
     */
    @Override
    public PartnerLevelVO partnerLevelByCrmCustomerCode(String crmCustomerCode) {
        CertificationQueryInfo entity = new CertificationQueryInfo();
        entity.setCrmCustomerCode(crmCustomerCode);
        entity.setAuthenStatus("certification_takes_effect");
        entity.setPage(CommonConst.DEFAULT_PAGE_NUM);
        entity.setRows(CommonConst.DEFAULT_PAGE_SIZE);

        PageRows<CertificationQueryResult> pageRows = prmServiceImpl.queryCertificationInfoCustomer(entity);
        if (null == pageRows){
            return null;
        }
        List<CertificationQueryResult> queryResultList = pageRows.getTotal()>0 ? pageRows.getRows() : Collections.emptyList();
        if(CollectionUtils.isEmpty(queryResultList)) {
            return null;
        }
        CertificationQueryResult certificationQueryResult = queryResultList.get(0);
        PartnerLevelVO partnerLevelVO = PartnerLevelMapper.INSTANCE.transToPartnerLevel(certificationQueryResult);
        String certificationInfoString = splicingAuthenticationInformation(certificationQueryResult);
        partnerLevelVO.setCertificationInfos(certificationInfoString);
        return partnerLevelVO;
    }

    private String splicingAuthenticationInformation(CertificationQueryResult certificationQueryResult){
        Set<String> certificationInfoStringSet = new HashSet<>();
        List<CertificationInfo> certificationInfoList = certificationQueryResult.getCertificationInfos();
        for (CertificationInfo certificationInfo : certificationInfoList) {
            String qualificationCategory = certificationInfo.getQualificationCategory();
            if(Objects.equals(qualificationCategory, OpportunityConstant.PRE_SALES)) {
                certificationInfoStringSet.add(certificationInfo.getAuthenProductName() + certificationInfo.getAuthenLevelName());
            }

            if(Objects.equals(qualificationCategory, OpportunityConstant.AFTER_SALES)) {
                StringBuilder sb = new StringBuilder();
                sb.append(certificationInfo.getAuthenLevelName());
                if(StringUtils.isNotBlank(certificationInfo.getAuthenProductName())) {
                    sb.append("-").append(certificationInfo.getAuthenProductName());
                }
                if(StringUtils.isNotBlank(certificationInfo.getSmallProduct())) {
                    sb.append("-").append(certificationInfo.getSmallProduct());
                }
                certificationInfoStringSet.add(sb.toString());
            }
        }
        String certificationInfoString = String.join(";", certificationInfoStringSet);
        if(certificationInfoString.length() > OpportunityConstant.CERTIFICATION_INFO_MAX_LENGTH){
            certificationInfoString = certificationInfoString.substring(0, OpportunityConstant.CERTIFICATION_INFO_MAX_LENGTH - 3)
                    + OpportunityConstant.ELLIPSIS;
        }
        return certificationInfoString;
    }

    /**
     * 创建客户
     * @param customerSysCreateParam
     * @throws BusiException
     */
    @Override
    public Boolean importCustomer(CustomerSysCreateParam customerSysCreateParam) throws BusiException {
        Map<String, String> headerParamsMap = new HashMap<String, String>(3);
        headerParamsMap.put("X-Tenant-Id", "SIEBEL_PROJECT");
        headerParamsMap.put("X-Lang-Id", "zh_CN");
        headerParamsMap.put("X-Emp-No", customerSysCreateParam.getBusinessManagerNo());
        HttpResultData accountHttpResult = MicroServiceWithConfigUtil.invokeServiceWithPostMethod(
                CluesSysConst.MIROSERVICES_FIVE,
                "/prm/importCustomer",
                customerSysCreateParam.getSsQueryParams(),
                headerParamsMap);

        boolean isSuccess = accountHttpResult != null && accountHttpResult.getCode() != null && RetCode.SUCCESS_CODE.equals(accountHttpResult.getCode().getCode());
        if (!isSuccess) {
            throw new BusiException(RetCode.BUSINESSERROR_CODE, "create customer error");
        }
        return Boolean.TRUE;
    }

    /**
     * 创建客户
     * @param customerCreateInfoDTO
     * @return
     */
    public CustomerCreateResult createFinalCustomer(CustomerCreateInfoDTO customerCreateInfoDTO) {
        Map<String, String> paramString = new HashMap<>(3);
        paramString.put("serviceName", "zte-crm-account-info");
        paramString.put("url", "/api/customer/operate/channel/generateAvailableCustomer");
        try {
            return MsaUtils.invokeServiceAndReturnBO(
                    paramString,
                    HttpMethodEnum.POST,
                    customerCreateInfoDTO,
                    new com.alibaba.fastjson.TypeReference<ServiceData<CustomerCreateResult>>() {
                    });
        } catch (BusinessRuntimeException e) {
            logger.error("invoke service to create customer:{}  error:{}", customerCreateInfoDTO.getAccountName(), e.getExMsg(), e);
            throw new ErrorCodeException(ErrorCode.UniversalErrorEnum.OU3012, e.getArguments());
        } catch (Exception e) {
            throw new ErrorCodeException(ErrorCode.UniversalErrorEnum.OU3153);
        }
    }

    /**
     * 查询客户创建审批状态
     * @param customerCode
     * @return
     */
    public CustomerApproveResult queryCustomerCreateProcessStatus(String customerCode) {
        Map<String, String> paramString = new HashMap<>(3);
        paramString.put("serviceName", "zte-crm-account-info");
        paramString.put("url", "/api/customer/getApproveStatus");
        Map<String, String> params = new HashMap<>(2);
        params.put("accountCode", customerCode);
        params.put("approveType", "Account validation Approve");
        try {
            return MsaUtils.invokeServiceAndReturnBO(
                    paramString,
                    HttpMethodEnum.POST,
                    params,
                    new com.alibaba.fastjson.TypeReference<ServiceData<CustomerApproveResult>>() {
                    });
        } catch (Exception e) {
            logger.error("invoke service to query customer:{} approve status,  error:{}", customerCode, e.getMessage(), e);
            throw new ErrorCodeException(ErrorCode.UniversalErrorEnum.OU3153);
        }
    }

    /**
     * 按优先级获取客户(有生效取生效，无生效取第一个，多个生效不取沉默合并)
     * @param customerNames
     * @return
     */
    @Override
    public Map<String, AccountInfo> getAccountInfoByPriorityMap(List<String> customerNames) {
        List<AccountInfo> accountInfos = getAccountInfoBatchByName(customerNames);
        if(CollUtil.isEmpty(accountInfos)) {
            return Maps.newHashMap();
        }
        Map<String, AccountInfo> result = Maps.newHashMap();
        Map<String, List<AccountInfo>> accountInfoByGroup = accountInfos.stream()
                .filter(x -> StringUtils.isNotBlank(x.getAccountName()))
                .collect(Collectors.groupingBy(AccountInfo::getAccountName));
        for (Map.Entry<String, List<AccountInfo>> entry : accountInfoByGroup.entrySet()) {
            AccountInfo accountInfo = getAccountInfoByPriority(entry.getValue());
            result.put(entry.getKey(), accountInfo);
        }
        return result;
    }

    public AccountInfo getAccountInfoByPriority(List<AccountInfo> accountInfos) {
        AccountInfo accountInfo;
        // 生效
        List<AccountInfo> accountInfoActiveStatusList = accountInfos.stream().filter(a ->
                AccountStatusEnum.ACCT_ACTIVE_STATUS.getValue().equals(a.getActiveStatusCode())).collect(Collectors.toList());
        // 生效 不为沉默且合并
        List<AccountInfo> accountInfoActiveStatusMainAndNotSilentList = accountInfoActiveStatusList.stream().filter(a ->
                !AcctMergeFlagEnum.mergeCustomer(a.getAcctMergeFlag())
                        && !FrozenFlagEnum.silentCustomer(a.getFrozenFlag())).collect(Collectors.toList());
        // 生效不为沉默
        List<AccountInfo>  accountInfoStatusActiveAndNoSilentList = accountInfoActiveStatusList.stream().filter(a ->
                !FrozenFlagEnum.silentCustomer(a.getFrozenFlag())).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(accountInfoActiveStatusMainAndNotSilentList)) {
            accountInfo = accountInfoActiveStatusMainAndNotSilentList.get(0);
        } else if(CollUtil.isNotEmpty(accountInfoStatusActiveAndNoSilentList)){
            accountInfo =  accountInfoStatusActiveAndNoSilentList.get(0);
        } else if (CollUtil.isNotEmpty(accountInfoActiveStatusList)) {
            accountInfo = accountInfoActiveStatusList.get(0);
        } else {
            accountInfo = accountInfos.get(0);
        }
        return accountInfo;
    }
}
