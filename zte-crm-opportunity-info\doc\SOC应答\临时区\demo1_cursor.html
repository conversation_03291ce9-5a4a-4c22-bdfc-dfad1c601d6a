<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <title>SOC应答系统 DEMO - 修复版</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <style>
    body {
      margin: 0;
      font-family: 'Segoe UI', 'PingFang SC', Arial, sans-serif;
      background: #ffffff;
    }

    .soc-navbar {
      height: 48px;
      background: #ffffff;
      color: #7c7c7c;
      display: flex;
      align-items: center;
      padding: 0 32px;
      font-size: 16px;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
      z-index: 10;
    }

    .soc-navbar-logo {
      font-weight: bold;
      font-size: 20px;
      margin-right: 32px;
      letter-spacing: 1px;
    }

    .soc-navbar-menu {
      flex: 1;
      display: flex;
      align-items: center;
    }

    .soc-navbar-menu>div {
      margin-right: 24px;
      cursor: pointer;
    }

    .soc-navbar-search {
      background: #fff;
      border-radius: 4px;
      padding: 2px 8px;
      margin-right: 24px;
      color: #333;
      display: flex;
      align-items: center;
    }

    .soc-navbar-search input {
      border: none;
      outline: none;
      background: transparent;
      font-size: 14px;
      width: 120px;
    }

    .soc-navbar-user {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .soc-navbar-avatar {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: #e6eaf2;
      display: inline-block;
    }

    .soc-layout {
      display: flex;
      height: calc(100vh - 48px);
    }

    .soc-sidebar,
    .soc-chatbar {
      background: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      z-index: 2;
    }

    .soc-sidebar {
      width: 220px;
      min-width: 180px;
      max-width: 260px;
      border-right: 1px solid #e8eaec;
      display: flex;
      flex-direction: column;
    }

    .soc-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-width: 0;
    }

    .soc-header {
      height: 48px;
      background: #fff;
      border-bottom: 1px solid #e8eaec;
      display: flex;
      align-items: center;
      padding: 0 24px;
      font-size: 18px;
      font-weight: 500;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    }

    .soc-main {
      flex: 1;
      overflow: auto;
      padding: 16px;
      background: #ffffff;
    }

    /* 表格样式 */
    #answerTableCard {
      background: none;
      border-radius: 0;
      box-shadow: none;
      margin: 0;
      overflow-x: auto;
      padding: 0;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      margin: 8px 0;
      position: relative;
    }

    table.soc-table {
      border-collapse: separate;
      border-spacing: 0;
      width: 100%;
      min-width: 1100px;
      font-size: 13px;
      background: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      border-radius: 6px;
      border: 1px solid #e8eaec;
      overflow: hidden;
    }

    table.soc-table th,
    table.soc-table td {
      border-bottom: 1px solid #e8eaec;
      border-right: 1px solid #e8eaec;
      padding: 8px 8px;
      text-align: left;
      vertical-align: middle;
      font-size: 13px;
    }

    table.soc-table th:last-child,
    table.soc-table td:last-child {
      border-right: none;
    }

    table.soc-table td {
      word-wrap: break-word;
      overflow-wrap: break-word;
    }

    table.soc-table th {
      background: #EEEEEE;
      font-weight: 500;
      color: #717171;
      border-top: 1px solid #d6d6d6;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    table.soc-table tr:last-child td {
      border-bottom: none;
    }

    table.soc-table tbody tr:hover {
      background-color: #f5f7fa;
    }

    table.soc-table tbody tr {
      transition: background-color 0.2s ease;
    }

    /* 合并单元格样式 */
    table.soc-table td[rowspan] {
      vertical-align: top;
      background-color: #fafafa;
    }

    /* 产品列样式 */
    table.soc-table td:nth-child(4) {
      font-weight: 500;
      color: #1765d5;
    }

    /* AnswerType标签样式 */
    table.soc-table td:nth-child(5) span {
      display: inline-block;
      font-size: 12px;
      border-radius: 4px;
      padding: 2px 8px;
    }

    /* 表格内输入框通用样式 */
    table.soc-table input[type="text"],
    table.soc-table textarea,
    table.soc-table select {
      border: none;
      outline: none;
      background: transparent;
      width: 100%;
      font-family: inherit;
      font-size: inherit;
      padding: 4px;
      box-sizing: border-box;
    }

    /* 表格内输入框聚焦时的样式 */
    table.soc-table input[type="text"]:focus,
    table.soc-table textarea:focus,
    table.soc-table select:focus {
      background: #f8f9fa;
      border-radius: 4px;
    }

    /* 表格内输入框悬停时的样式 */
    table.soc-table input[type="text"]:hover,
    table.soc-table textarea:hover,
    table.soc-table select:hover {
      background: #f8f9fa;
    }

    .soc-table-img {
      max-width: 80px;
      max-height: 40px;
      border-radius: 4px;
      border: 1px solid #eee;
    }

    .soc-table-action {
      color: #1890ff;
      cursor: pointer;
    }

    .soc-chatbar {
      width: 300px;
      min-width: 180px;
      max-width: 340px;
      border-left: 1px solid #e8eaec;
      display: flex;
      flex-direction: column;
    }

    .soc-hide-btn {
      position: absolute;
      top: 12px;
      right: -12px;
      background: #fff;
      border: 1px solid #ececec;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      cursor: pointer;
      box-shadow: 0 2px 8px #eee;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .soc-sidebar,
    .soc-chatbar {
      position: relative;
    }

    .soc-sidebar .soc-header,
    .soc-chatbar .soc-header {
      font-size: 15px;
      font-weight: 500;
      border-bottom: 1px solid #f0f0f0;
    }

    .soc-sidebar>div,
    .soc-chatbar>div {
      padding: 12px 16px;
    }

    /* 响应式设计 */
    @media (max-width: 1200px) {
      .soc-sidebar {
        width: 140px;
      }

      .soc-chatbar {
        width: 160px;
      }
    }

    @media (max-width: 900px) {
      .soc-sidebar,
      .soc-chatbar {
        display: none;
      }

      .soc-content {
        flex: 1 1 100%;
      }
    }

    /* 按钮样式 */
    .soc-btn {
      height: 32px;
      padding: 0 16px;
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      background: #fff;
      color: #666;
      font-size: 14px;
      cursor: pointer;
      transition: all .2s ease;
      margin-right: 0;
      outline: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
      display: inline-flex;
      align-items: center;
      gap: 4px;
      font-weight: 400;
      line-height: 1;
    }

    .soc-btn:hover {
      background: #fff;
      color: #1890ff;
      border-color: #1890ff;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.12);
    }

    .soc-btn-green {
      background: #52c41a;
      color: #fff;
      border: 1px solid #52c41a;
      box-shadow: 0 2px 4px rgba(82, 196, 26, 0.2);
    }

    .soc-btn-green:hover {
      background: #73d13d;
      border-color: #73d13d;
      color: #fff;
      box-shadow: 0 4px 8px rgba(82, 196, 26, 0.3);
    }

    .soc-btn-orange {
      background: #fa8c16;
      color: #fff;
      border: 1px solid #fa8c16;
      box-shadow: 0 2px 4px rgba(250, 140, 22, 0.2);
    }

    .soc-btn-orange:hover {
      background: #ffa940;
      border-color: #ffa940;
      color: #fff;
      box-shadow: 0 4px 8px rgba(250, 140, 22, 0.3);
    }

    .soc-btn-blue {
      background: #0077ff;
      color: #fff;
      border: 1px solid #1871D6;
    }

    .soc-btn-blue:hover {
      background: #40a9ff;
      border-color: #40a9ff;
      color: #fff;
    }

    .soc-btn-red {
      background: #f5222d;
      border: 1px solid #f5222d;
      color: #fff;
    }

    .soc-btn-red:hover {
      background: #ff4d4f;
      border-color: #ff4d4f;
      color: #fff;
    }

    .soc-btn-yellow {
      background: #fadb14;
      color: #fff;
      border: 1px solid #fadb14;
    }

    .soc-btn-yellow:hover {
      background: #ffec3d;
      border-color: #ffec3d;
      color: #fff;
    }

    .soc-btn-2 {
      background: #ffffff;
      border: 1px solid #1871D6;
      color: #0077ff;
    }

    .soc-btn-3 {
      background: #ffffff;
      border: 1px solid #D8D8D8;
      color: #0077ff;
    }

    .soc-btn-4 {
      background: #ffffff;
      border: 1px solid #D8D8D8;
      color: #9f9f9f;
    }

    /* 输入框通用样式 */
    input[type="text"],
    input[type="search"],
    select {
      border: 1px solid #d4d7db;
      border-radius: 4px;
      padding: 4px 8px;
      font-size: 13px;
      transition: border-color 0.2s ease, box-shadow 0.2s ease;
      background: #fff;
    }

    input[type="text"]:focus,
    input[type="search"]:focus,
    select:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      outline: none;
    }

    /* 智能提示模板样式 */
    .smart-prompt-btn {
      background: #e6f7ff !important;
      color: #1890ff !important;
      border: 1px solid #91d5ff !important;
      border-radius: 6px !important;
      padding: 4px 8px !important;
      font-size: 12px !important;
      cursor: pointer !important;
      transition: all 0.2s !important;
      margin: 2px !important;
    }

    .smart-prompt-btn:hover {
      background: #e6f7ff !important;
      border-color: #91d5ff !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 2px 4px rgba(23, 101, 213, 0.1) !important;
    }

    /* 快捷指令按钮样式 */
    .quick-cmd-btn {
      background: #f6ffed;
      color: #389e0d;
      border: 1px solid #b7eb8f;
      border-radius: 6px;
      padding: 4px 8px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s;
      margin: 2px;
    }

    .quick-cmd-btn:hover {
      background: #d9f7be;
      border-color: #95de64;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(56, 158, 13, 0.1);
    }

    /* 主菜单项样式 */
    .main-action-item {
      transition: background 0.2s;
    }

    .main-action-item:hover {
      background: #e6f7ff !important;
      color: #1890ff !important;
    }

    /* 材料标签样式 */
    .material-tag {
      display: inline-flex;
      align-items: center;
      border-radius: 4px;
      padding: 4px 8px;
      font-size: 13px;
      margin: 2px;
      transition: all 0.2s;
    }

    .material-tag:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .material-tag .doc-name {
      margin-right: 6px;
      font-weight: 500;
    }

    .material-tag .doc-type {
      font-size: 11px;
      color: #666;
      margin-right: 6px;
    }

    .material-tag .doc-action {
      cursor: pointer;
      margin-right: 4px;
      opacity: 0.7;
      transition: opacity 0.2s;
    }

    .material-tag .doc-action:hover {
      opacity: 1;
    }

    /* 树形选择器样式 */
    .tree-select-container {
      position: relative;
    }

    .tree-select-input {
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      padding: 8px 12px;
      min-height: 36px;
      background: #fff;
      cursor: pointer;
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      align-items: center;
      transition: border-color 0.3s;
    }

    .tree-select-input:hover {
      border-color: #40a9ff;
    }

    .tree-select-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: #fff;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      z-index: 1000;
      max-height: 300px;
      overflow-y: auto;
      display: none;
    }

    .tree-node {
      padding: 6px 8px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 13px;
      border-radius: 4px;
      transition: background-color 0.2s;
    }

    .tree-node:hover {
      background-color: #f5f5f5;
    }

    .tree-node.selected {
      background-color: #e6f7ff;
      color: #1890ff;
    }

    .tree-node-toggle {
      font-size: 10px;
      color: #666;
      cursor: pointer;
      user-select: none;
      width: 12px;
      text-align: center;
    }

    .tree-node-label {
      flex: 1;
    }

    .tree-node-type {
      font-size: 11px;
      color: #999;
    }

    /* 搜索下拉框样式 */
    .searchable-select-container {
      position: relative;
    }

    .searchable-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: #fff;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      z-index: 1000;
      max-height: 200px;
      overflow-y: auto;
      display: none;
    }

    .dropdown-option {
      padding: 8px 12px;
      cursor: pointer;
      font-size: 13px;
      transition: background-color 0.2s;
    }

    .dropdown-option:hover {
      background-color: #f5f5f5;
    }

    /* 大纲面板样式 */
    #outline-float {
      position: fixed;
      top: 80px;
      left: 0;
      z-index: 9999;
      transition: all .2s;
    }

    #outline-panel {
      width: 260px;
      background: #fff;
      border-radius: 0 12px 12px 0;
      box-shadow: 2px 0 12px #eee;
      padding: 12px 8px 12px 0;
      overflow-y: auto;
      max-height: 80vh;
      display: block;
    }

    #outline-expand {
      display: none;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background: #1765d5;
      color: #fff;
      border: none;
      box-shadow: 0 2px 8px #eee;
      cursor: pointer;
      font-size: 18px;
    }

    /* Tab样式 */
    .soc-tab-btn {
      padding: 6px 12px;
      border: none;
      background: none;
      cursor: pointer;
      border-bottom: 2px solid transparent;
      transition: all 0.2s;
    }

    .soc-tab-btn.active {
      background: #e6eaf2;
      border-bottom-color: #1765d5;
    }
  </style>
</head>

<body>
  <div class="soc-layout">
    <!-- 任务区/侧边栏 -->
    <aside class="soc-sidebar" id="socSidebar">
      <div class="soc-header" style="display:flex;align-items:center;justify-content:space-between;">
        <span>任务区</span>
        <button id="taskManageBtn"
          style="background:#e6f7ff;color:#1765d5;border:1px solid #91d5ff;border-radius:4px;padding:2px 10px;cursor:pointer;font-size:13px;line-height:1;">📋
          任务管理</button>
      </div>
      <div style="padding:0;">
        <div id="taskTabBar" style="display:flex;">
          <button class="soc-tab-btn" data-tab="todo">待办任务</button>
          <button class="soc-tab-btn" data-tab="history">历史归档</button>
          <button class="soc-tab-btn" data-tab="template">模板管理</button>
        </div>
        <div id="taskTabContent" style="padding:12px 8px 0 8px;"></div>
      </div>
      <button class="soc-hide-btn" title="收起侧边栏" id="hideSidebarBtn">&lt;</button>
    </aside>

    <!-- 内容主区 -->
    <main class="soc-content" id="socContent">
      <div class="soc-header">SOC应答任务</div>
      <div class="soc-main">
        <!-- 表格卡片 -->
        <div id="answerTableCard">
          <div class="soc-toolbar" style="display:flex;align-items:center;gap:0;padding:0 0 12px 0;">
            <!-- 操作按钮区 -->
            <div style="display:flex;gap:10px;">
              <button id="importExcelBtn" class="soc-btn soc-btn-blue">导入</button>
              <button id="addSingleEntryBtn" class="soc-btn soc-btn-2">新增单条</button>
              <input type="file" id="importExcelInput" accept=".xlsx,.xls" style="display:none;" />
              <button id="exportBtn" class="soc-btn soc-btn-2">导出</button>
              <button id="batchReAnswerBtn" class="soc-btn soc-btn-2">开始应答</button>
              <div style="position:relative;">
                <button id="mainActionBtn" class="soc-btn soc-btn-3">更多 ▾</button>
                <div id="mainActionMenu"
                  style="display:none;position:absolute;left:0;top:36px;z-index:20;background:#fff;border:1px solid #ececec;box-shadow:0 2px 8px #eee;border-radius:6px;min-width:160px;overflow:hidden;">
                  <div class="main-action-item" data-act="exportSetting" tabindex="0"
                    style="padding:8px 16px;cursor:pointer;transition:background .2s;">⚙️ 导出设置</div>
                  <div class="main-action-item" data-act="openParamSetting" tabindex="0"
                    style="padding:8px 16px;cursor:pointer;transition:background .2s;">⚙️ 参数设置</div>
                  <div class="main-action-item" data-act="openReferenceDoc" tabindex="0"
                    style="padding:8px 16px;cursor:pointer;transition:background .2s;">📑 参考文档</div>
                  <div class="main-action-item" data-act="priorityConfig" tabindex="0"
                    style="padding:8px 16px;cursor:pointer;transition:background .2s;">🔧 优先级配置</div>
                  <div class="main-action-item" data-act="satisfactionCalc" tabindex="0"
                    style="padding:8px 16px;cursor:pointer;transition:background .2s;">📊 满足度计算</div>
                  <div class="main-action-item" data-act="viewSimilar" tabindex="0"
                    style="padding:8px 16px;cursor:pointer;transition:background .2s;">🔍 查看相似条目</div>
                  <div class="main-action-item" data-act="historyRecord" tabindex="0"
                    style="padding:8px 16px;cursor:pointer;transition:background .2s;">🕓 历史记录</div>
                  <div style="border-top:1px solid #f0f0f0;margin:4px 0;"></div>
                  <div class="main-action-item" data-act="clearAll" tabindex="0"
                    style="padding:8px 16px;cursor:pointer;transition:background .2s;color:#ff4d4f;">🗑️ 清空全部条目</div>
                </div>
                <button id="batchDeleteBtn" class="soc-btn soc-btn-4">删除</button>
              </div>
            </div>
            <span style="flex:1"></span>
          </div>
          
          <div style="display:flex;align-items:center;gap:12px;margin-bottom:8px;">
            <span id="similarTip" style="color:#faad14;display:none;">发现<span id="similarCount"></span>组相似条目，<a href="#"
                id="showSimilarBtn">查看</a></span>
            <span id="productFilterTip" style="color:#1765d5;font-size:13px;display:none;">当前筛选产品：<span
                id="currentFilterProduct"></span></span>
          </div>
          
          <table class="soc-table" id="answerTable">
            <thead>
              <tr>
                <th><input type="checkbox" id="selectAllRow" /></th>
                <th style="width:60px;">
                  编号
                  <input id="searchNo" type="text" placeholder="筛选"
                    style="width:40px;font-size:12px;margin-left:2px;" />
                </th>
                <th style="min-width:120px;width:180px;max-width:220px;">
                  条目描述
                  <input id="searchDesc" type="text" placeholder="筛选"
                    style="width:80px;font-size:12px;margin-left:2px;" />
                </th>
                <th style="min-width:50px;">
                  产品
                  <div style="margin-top:4px;">
                    <select id="searchProduct"
                      style="height:28px;border:none;outline:none;background:transparent;font-size:13px;padding:4px;width:100%;box-sizing:border-box;border-radius:0;">
                      <option value="">全部</option>
                      <option value="5GC">5GC</option>
                      <option value="VoLTE">VoLTE</option>
                      <option value="IMS">IMS</option>
                    </select>
                  </div>
                </th>
                <th style="min-width:30px;">
                  方式
                  <div style="margin-top:4px;">
                    <select id="searchAnswerType"
                      style="height:28px;border:none;outline:none;background:transparent;font-size:13px;padding:4px;width:100%;box-sizing:border-box;border-radius:0;">
                      <option value="">全部</option>
                      <option value="AI">AI</option>
                      <option value="人工">人工</option>
                    </select>
                  </div>
                </th>
                <th style="min-width:30px;">
                  应答
                  <div style="margin-top:4px;">
                    <select id="searchAnswer"
                      style="height:28px;border:none;outline:none;background:transparent;font-size:13px;padding:4px;width:100%;box-sizing:border-box;border-radius:0;">
                      <option value="">全部</option>
                      <option value="FC">FC</option>
                      <option value="PC">PC</option>
                      <option value="NC">NC</option>
                      <option value="N/A">N/A</option>
                      <option value="未应答">未应答</option>
                    </select>
                  </div>
                </th>
                <th style="min-width:150px;">
                  应答说明
                  <div style="margin-top:4px;">
                    <input type="text" id="searchExplain" placeholder="应答说明"
                      style="height:28px;border:none;outline:none;background:transparent;font-size:13px;padding:4px;width:100%;box-sizing:border-box;border-radius:0;" />
                  </div>
                </th>
                <th style="min-width:120px;">索引</th>
                <th style="min-width:60px;">
                  备注
                  <div style="margin-top:4px;">
                    <input type="text" id="searchRemark" placeholder="备注"
                      style="height:28px;border:none;outline:none;background:transparent;font-size:13px;padding:4px;width:100%;box-sizing:border-box;border-radius:0;" />
                  </div>
                </th>
                <th style="min-width:40px;padding-left:8px;">操作</th>
              </tr>
            </thead>
            <tbody id="answerTableBody"></tbody>
          </table>
          
          <!-- 分页条 -->
          <div id="tablePagination"
            style="display:flex;align-items:center;justify-content:flex-end;gap:12px;margin:12px 0 0 0;">
            <span>每页</span>
            <select id="pageSizeSelect" style="width:60px;height:28px;">
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
            <span>条</span>
            <button id="prevPageBtn" style="padding:2px 10px;">上一页</button>
            <span id="pageInfo"></span>
            <button id="nextPageBtn" style="padding:2px 10px;">下一页</button>
          </div>
        </div>
        
        <!-- 详情弹窗容器 -->
        <div id="answerDetailDialog" style="display:none;"></div>
      </div>
    </main>

    <!-- 对话区/右侧栏 -->
    <aside class="soc-chatbar" id="socChatbar">
      <div class="soc-header">投标Agent</div>
      <div style="padding:0;display:flex;flex-direction:column;height:calc(100% - 48px);position:relative;">
        <!-- 对话历史区 -->
        <div style="flex:1;display:flex;flex-direction:column;overflow:hidden;">
          <div style="padding:8px 12px 0 12px;flex:1;overflow:auto;" id="luiHistoryWrap">
            <div id="luiHistory" style="font-size:13px;line-height:1.7;display:flex;flex-direction:column;gap:8px;">
            </div>
          </div>
        </div>
        
        <!-- 快捷指令栏 -->
        <div style="padding:8px 12px 0 12px; min-height:60px;">
          <div id="quickCmdHeader"
            style="font-weight:500;margin-bottom:4px;cursor:pointer;display:flex;justify-content:space-between;align-items:center;user-select:none;background:#fff;position:relative;">
            <span>快捷指令</span>
            <span id="quickCmdToggle" style="transition: transform 0.2s; display:inline-block;">▼</span>
          </div>
          <div id="quickCmdBar" style="display:flex;flex-wrap:wrap;gap:6px 8px;transition: all 0.3s ease;"></div>
        </div>
        
        <!-- LUI输入区，固定底部 -->
        <form id="luiForm"
          style="display:flex;gap:4px;padding:10px 12px 10px 12px;background:#fff;box-shadow:0 -2px 8px #f0f0f0;position:absolute;left:0;right:0;bottom:0;">
          <input id="luiInput" type="text"
            placeholder="请输入自然语言描述，如'帮我设置项目参数：产品5GC和VoLTE，国家泰国，运营商AIS'或'上传招标文件.pdf，再分析5GC安全要求'"
            style="flex:1;padding:8px 12px;border-radius:8px;border:1px solid #d9d9d9;font-size:14px;outline:none;transition:border .2s;" />
          <button type="submit"
            style="background:#1765d5;color:#fff;border:none;border-radius:8px;padding:8px 16px;cursor:pointer;font-size:14px;">发送</button>
        </form>

        <!-- 智能提示模板区域 -->
        <div id="smartPromptArea"
          style="position:absolute;left:0;right:0;bottom:60px;background:#fff;border-top:1px solid #f0f0f0;padding:8px 12px;display:none;">
          <div style="font-size:12px;color: #fbfbfb;;margin-bottom:6px;">💡 智能推荐：</div>
          <div id="smartPrompts" style="display:flex;flex-wrap:wrap;gap:6px;"></div>
        </div>
      </div>
      <button class="soc-hide-btn" title="收起对话区" id="hideChatbarBtn">&gt;</button>
    </aside>
  </div>

  <!-- 大纲浮窗 -->
  <div id="outline-float">
    <div id="outline-panel">
      <div style="display:flex;align-items:center;justify-content:space-between;margin-bottom:8px;padding-left:8px;">
        <span style="font-weight:600;font-size:16px;color:#333;">大纲</span>
        <button id="outline-collapse"
          style="background:none;border:none;font-size:18px;cursor:pointer;color:#666;padding:4px;">&laquo;</button>
      </div>
      <div id="outline-tree" style="padding-left:8px;"></div>
    </div>
    <button id="outline-expand">&#9776;</button>
  </div>

  <script>
    // SOC应答系统 - 修复版本
    // 避免全局变量冲突，使用命名空间
    window.SOCSystem = {
      // 全局配置
      config: {
        version: '1.0.0-cursor',
        pageSize: 10,
        maxFileSize: 50 * 1024 * 1024, // 50MB
        allowedFileTypes: ['.pdf', '.doc', '.docx', '.xlsx', '.xls']
      },
      
      // 数据管理
      data: {
        answerList: [],
        materialList: [],
        templateList: [],
        luiHistory: [],
        tablePage: { page: 1, pageSize: 10 },
        tableSort: { col: '', asc: true },
        tableSearch: { no: '', desc: '', answer: '', explain: '', index: '', remark: '' }
      },
      
      // 状态管理
      state: {
        currentTab: 'todo',
        currentProductTab: '5GC',
        sidebarHidden: false,
        chatbarHidden: false,
        outlineCollapsed: false
      },
      
      // 初始化系统
      init: function() {
        console.log('SOC系统初始化开始 - 版本', this.config.version);
        this.loadStoredData();
        this.initEventListeners();
        this.initUI();
        this.renderInitialData();
        console.log('SOC系统初始化完成');
      },

      // 数据持久化相关方法
      loadStoredData: function() {
        try {
          this.data.answerList = JSON.parse(localStorage.getItem('socAnswerList') || '[]');
          this.data.materialList = JSON.parse(localStorage.getItem('socMaterialList') || '[]');
          this.data.templateList = JSON.parse(localStorage.getItem('socParamTemplates') || '[]');
          this.data.luiHistory = JSON.parse(localStorage.getItem('socLuiHistory') || '[]');
          
          // 状态恢复
          this.state.sidebarHidden = localStorage.getItem('socSidebarHidden') === '1';
          this.state.chatbarHidden = localStorage.getItem('socChatbarHidden') === '1';
          this.state.outlineCollapsed = localStorage.getItem('outlineCollapsed') === '1';
          
          // 如果没有数据，初始化演示数据
          if (this.data.answerList.length === 0) {
            this.initDemoData();
          }
        } catch (error) {
          console.error('数据加载失败:', error);
          this.initDemoData();
        }
      },

      saveAnswerList: function(list) {
        this.data.answerList = list || this.data.answerList;
        localStorage.setItem('socAnswerList', JSON.stringify(this.data.answerList));
      },

      saveMaterialList: function(list) {
        this.data.materialList = list || this.data.materialList;
        localStorage.setItem('socMaterialList', JSON.stringify(this.data.materialList));
      },

      saveTemplateList: function(list) {
        this.data.templateList = list || this.data.templateList;
        localStorage.setItem('socParamTemplates', JSON.stringify(this.data.templateList));
      },

      saveLuiHistory: function(list) {
        this.data.luiHistory = list || this.data.luiHistory;
        localStorage.setItem('socLuiHistory', JSON.stringify(this.data.luiHistory));
      },

      // 初始化演示数据
      initDemoData: function() {
        const demoData = [
          {
            no: 1,
            desc: '系统支持多语言',
            products: ['5GC', 'VoLTE', 'IMS'],
            productData: {
              '5GC': {
                answer: 'FC',
                explain: '5GC系统完全支持中英文双语切换，用户界面、系统提示、错误信息等均可根据用户偏好进行语言设置。',
                supplement: '支持动态语言切换，无需重启系统',
                remark: '重要功能',
                index: '5GC技术白皮书-3.2.1',
                source: '文档库',
                answerType: 'AI'
              },
              'VoLTE': {
                answer: 'PC',
                explain: 'VoLTE系统支持中文和英文两种语言，但部分高级功能提示信息仍为英文。',
                supplement: '基础功能支持，高级功能待完善',
                remark: '需优化',
                index: 'VoLTE解决方案-2.1.3',
                source: '文档库',
                answerType: 'AI'
              },
              'IMS': {
                answer: 'FC',
                explain: 'IMS系统完全支持多语言，包括中文、英文、日文等多种语言。',
                supplement: '多语言支持完善',
                remark: '功能完整',
                index: 'IMS技术规范-4.1.2',
                source: '文档库',
                answerType: 'AI'
              }
            }
          },
          {
            no: 2,
            desc: '支持5G网络切片',
            products: ['5GC', 'VoLTE', 'IMS'],
            productData: {
              '5GC': {
                answer: 'FC',
                explain: '5GC系统完全支持5G网络切片技术，可为不同业务场景提供独立的网络资源。',
                supplement: '支持端到端切片管理',
                remark: '核心功能',
                index: '5GC切片白皮书-2.3.1',
                source: '文档库',
                answerType: 'AI'
              },
              'VoLTE': {
                answer: 'N/A',
                explain: 'VoLTE系统不涉及5G网络切片技术。',
                supplement: '4G技术不包含切片',
                remark: '不适用',
                index: 'VoLTE技术规范-1.0.0',
                source: '文档库',
                answerType: 'AI'
              },
              'IMS': {
                answer: 'PC',
                explain: 'IMS系统部分支持网络切片概念，但实现方式与5G切片不同。',
                supplement: '基于IMS的切片实现',
                remark: '部分支持',
                index: 'IMS切片方案-3.1.5',
                source: '文档库',
                answerType: 'AI'
              }
            }
          },
          {
            no: 3,
            desc: '保障系统安全性',
            products: ['5GC', 'VoLTE', 'IMS'],
            productData: {
              '5GC': {
                answer: 'FC',
                explain: '5GC系统采用多层次安全防护，包括接入安全、传输安全、数据安全等。',
                supplement: '符合3GPP安全标准',
                remark: '安全等级高',
                index: '5GC安全规范-5.2.1',
                source: '文档库',
                answerType: 'AI'
              },
              'VoLTE': {
                answer: 'FC',
                explain: 'VoLTE系统具备完善的安全机制，支持加密通信和身份认证。',
                supplement: '基于IMS安全架构',
                remark: '安全可靠',
                index: 'VoLTE安全指南-2.4.3',
                source: '文档库',
                answerType: 'AI'
              },
              'IMS': {
                answer: 'FC',
                explain: 'IMS系统提供全面的安全保护，包括SIP安全、媒体安全等。',
                supplement: '端到端安全防护',
                remark: '安全体系完善',
                index: 'IMS安全标准-4.2.1',
                source: '文档库',
                answerType: 'AI'
              }
            }
          }
        ];

        this.data.answerList = demoData;
        this.saveAnswerList();

        // 初始化演示素材
        const demoMaterials = [
          { name: '泰国AIS招标文件.pdf', type: '项目文档', time: Date.now() - 86400000, id: 'p1' },
          { name: '5GC技术白皮书.pdf', type: '文档库', time: Date.now() - 172800000, id: 'lib1' },
          { name: '2023年SOC应答经验.xlsx', type: '历史SOC文档', time: Date.now() - 259200000, id: 's1' }
        ];
        this.data.materialList = demoMaterials;
        this.saveMaterialList();
      },

      // UI初始化
      initUI: function() {
        this.applySidebarState();
        this.applyChatbarState();
        this.applyOutlineState();
        this.renderTaskTab(this.state.currentTab);
        this.renderQuickCommands();
      },

      // 渲染初始数据
      renderInitialData: function() {
        this.renderAnswerTable();
        this.renderLuiHistory();
        this.updateOutline();
      },

      // 侧边栏状态管理
      applySidebarState: function() {
        const sidebar = document.getElementById('socSidebar');
        const content = document.getElementById('socContent');
        if (this.state.sidebarHidden) {
          sidebar.style.display = 'none';
          content.style.marginLeft = '0';
        } else {
          sidebar.style.display = '';
          content.style.marginLeft = '';
        }
      },

      // 对话栏状态管理
      applyChatbarState: function() {
        const chatbar = document.getElementById('socChatbar');
        const content = document.getElementById('socContent');
        if (this.state.chatbarHidden) {
          chatbar.style.display = 'none';
          content.style.marginRight = '0';
        } else {
          chatbar.style.display = '';
          content.style.marginRight = '';
        }
      },

      // 大纲状态管理
      applyOutlineState: function() {
        const panel = document.getElementById('outline-panel');
        const expand = document.getElementById('outline-expand');
        if (this.state.outlineCollapsed) {
          panel.style.display = 'none';
          expand.style.display = '';
        } else {
          panel.style.display = '';
          expand.style.display = 'none';
        }
      }
    };

    // 表格渲染功能
    SOCSystem.renderAnswerTable = function() {
      let list = [...this.data.answerList];
      
      // 应用搜索过滤
      const searchProduct = document.getElementById('searchProduct')?.value || '';
      const searchAnswer = document.getElementById('searchAnswer')?.value || '';
      const searchExplain = document.getElementById('searchExplain')?.value.trim() || '';
      const searchRemark = document.getElementById('searchRemark')?.value.trim() || '';
      const searchAnswerType = document.getElementById('searchAnswerType')?.value || '';
      const searchNo = this.data.tableSearch.no || '';
      const searchDesc = this.data.tableSearch.desc || '';

      // 过滤逻辑
      list = list.filter(row => {
        const products = Array.isArray(row.products) ? row.products : [];
        if (products.length === 0) return false;
        
        // 产品筛选
        let displayProducts = products;
        if (searchProduct) displayProducts = products.filter(p => p === searchProduct);
        if (displayProducts.length === 0) return false;
        
        // 其他筛选条件
        let hasMatch = false;
        for (const product of displayProducts) {
          const pdata = (row.productData && row.productData[product]) || {};
          if (searchAnswerType && pdata.answerType !== searchAnswerType) continue;
          if (searchAnswer && pdata.answer !== searchAnswer) continue;
          if (searchExplain && (!pdata.explain || pdata.explain.indexOf(searchExplain) === -1)) continue;
          if (searchRemark && (!pdata.remark || pdata.remark.indexOf(searchRemark) === -1)) continue;
          hasMatch = true;
          break;
        }
        if (!hasMatch) return false;
        
        if (searchNo && String(row.no || '').indexOf(searchNo) === -1) return false;
        if (searchDesc && (!row.desc || row.desc.indexOf(searchDesc) === -1)) return false;
        return true;
      });

      // 排序
      if (this.data.tableSort.col) {
        list = list.sort((a, b) => {
          let va = a[this.data.tableSort.col] || '';
          let vb = b[this.data.tableSort.col] || '';
          if (typeof va === 'number' && typeof vb === 'number') {
            return this.data.tableSort.asc ? va - vb : vb - va;
          }
          return this.data.tableSort.asc ? String(va).localeCompare(String(vb)) : String(vb).localeCompare(String(va));
        });
      }

      // 分页处理
      const pageSize = this.data.tablePage.pageSize;
      const page = this.data.tablePage.page;
      const total = list.length;
      const pageCount = Math.max(1, Math.ceil(total / pageSize));
      if (page > pageCount) this.data.tablePage.page = pageCount;
      
      const start = (this.data.tablePage.page - 1) * pageSize;
      const end = start + pageSize;
      const pageList = list.slice(start, end);

      // 渲染表格内容
      this.renderTableBody(pageList, searchProduct);
      this.renderPagination(this.data.tablePage.page, pageCount);
      this.updateOutline();
    };

    // 渲染表格主体
    SOCSystem.renderTableBody = function(pageList, searchProduct) {
      const tableBody = document.getElementById('answerTableBody');
      if (!tableBody) return;

      let html = '';
      pageList.forEach((row, i) => {
        const products = Array.isArray(row.products) ? row.products : [];
        let displayProducts = products;
        if (searchProduct) displayProducts = products.filter(p => p === searchProduct);

        displayProducts.forEach((product, productIndex) => {
          const pdata = (row.productData && row.productData[product]) || { 
            answer: '', explain: '', remark: '', index: '', imgList: [], answerType: 'AI'
          };
          
          const answerType = pdata.answerType || 'AI';
          const tagColor = answerType === 'AI' ? '#e6f7ff' : '#fffbe6';
          const tagTextColor = answerType === 'AI' ? '#1765d5' : '#faad14';

          const isFirstProduct = productIndex === 0;
          const rowspan = isFirstProduct ? displayProducts.length : 0;

          html += `<tr id="outline-item-${i}-${product}" data-row-idx="${i}" data-product="${product}">
            <td><input type="checkbox" class="rowCheck" data-idx="${i}" data-product="${product}" /></td>
            ${isFirstProduct ? `<td rowspan="${rowspan}">${row.no || i + 1}</td>` : ''}
            ${isFirstProduct ? `<td rowspan="${rowspan}">${this.escapeHtml(row.desc || '')}</td>` : ''}
            <td style="font-weight:500;color:#1765d5;">${product}</td>
            <td><span style="background:${tagColor};color:${tagTextColor};font-size:12px;border-radius:4px;padding:2px 8px;">${answerType}</span></td>
            <td>
              <select class="answer-select" data-idx="${i}" data-product="${product}" style="height:26px;font-size:13px;background:${this.getAnswerBgColor(pdata.answer)};color:${this.getAnswerTextColor(pdata.answer)};">
                <option value="">未应答</option>
                <option value="FC" ${pdata.answer === 'FC' ? 'selected' : ''}>FC</option>
                <option value="PC" ${pdata.answer === 'PC' ? 'selected' : ''}>PC</option>
                <option value="NC" ${pdata.answer === 'NC' ? 'selected' : ''}>NC</option>
                <option value="N/A" ${pdata.answer === 'N/A' ? 'selected' : ''}>N/A</option>
              </select>
            </td>
            <td>
              <textarea class="explain-input" data-idx="${i}" data-product="${product}" maxlength="1000" rows="2" style="font-size:13px;resize:vertical;width:100%;" title="${this.escapeHtml(pdata.explain || '')}">${this.escapeHtml(pdata.explain || '')}</textarea>
            </td>
            <td title="${this.escapeHtml(pdata.index || '')}">${pdata.index || '--'}</td>
            <td>
              <textarea class="remark-input" data-idx="${i}" data-product="${product}" maxlength="500" rows="2" style="font-size:13px;resize:vertical;width:100%;" title="${this.escapeHtml(pdata.remark || '')}">${this.escapeHtml(pdata.remark || '')}</textarea>
            </td>
            <td style="text-align:right;">
              <span class="soc-table-action" data-act="detail" data-idx="${i}" data-product="${product}">详情</span> |
              <span class="soc-table-action" data-act="reanswer" data-idx="${i}" data-product="${product}">AI应答</span> |
              <span class="soc-table-action" data-act="delete" data-idx="${i}" data-product="${product}" style="color:#ff4d4f;">删除</span>
            </td>
          </tr>`;
        });
      });

      tableBody.innerHTML = html;
      this.bindTableEvents();
    };

    // 工具函数
    SOCSystem.escapeHtml = function(text) {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    };

    SOCSystem.getAnswerBgColor = function(answer) {
      const colors = {
        'FC': '#f6ffed',
        'PC': '#fff7e6', 
        'NC': '#fff2f0',
        'N/A': '#f5f5f5',
        '': '#f5f5f5'
      };
      return colors[answer] || '#f5f5f5';
    };

    SOCSystem.getAnswerTextColor = function(answer) {
      const colors = {
        'FC': '#52c41a',
        'PC': '#faad14',
        'NC': '#ff4d4f', 
        'N/A': '#666',
        '': '#666'
      };
      return colors[answer] || '#666';
    };

    // 绑定表格事件
    SOCSystem.bindTableEvents = function() {
      // 应答下拉框事件
      document.querySelectorAll('.answer-select').forEach(sel => {
        sel.onchange = (e) => {
          const idx = parseInt(e.target.dataset.idx);
          const product = e.target.dataset.product;
          const list = this.data.answerList;
          if (list[idx] && list[idx].productData && list[idx].productData[product]) {
            list[idx].productData[product].answer = e.target.value;
            list[idx].productData[product].answerType = '人工';
            this.saveAnswerList(list);
            this.renderAnswerTable();
          }
        };
      });

      // 说明文本框事件
      document.querySelectorAll('.explain-input').forEach(inp => {
        inp.onblur = (e) => {
          const idx = parseInt(e.target.dataset.idx);
          const product = e.target.dataset.product;
          const list = this.data.answerList;
          if (list[idx] && list[idx].productData && list[idx].productData[product]) {
            list[idx].productData[product].explain = e.target.value;
            list[idx].productData[product].answerType = '人工';
            this.saveAnswerList(list);
          }
        };
      });

      // 备注文本框事件
      document.querySelectorAll('.remark-input').forEach(inp => {
        inp.onblur = (e) => {
          const idx = parseInt(e.target.dataset.idx);
          const product = e.target.dataset.product;
          const list = this.data.answerList;
          if (list[idx] && list[idx].productData && list[idx].productData[product]) {
            list[idx].productData[product].remark = e.target.value;
            list[idx].productData[product].answerType = '人工';
            this.saveAnswerList(list);
          }
        };
      });

      // 操作按钮事件
      document.querySelectorAll('.soc-table-action').forEach(action => {
        action.onclick = (e) => {
          const act = e.target.dataset.act;
          const idx = parseInt(e.target.dataset.idx);
          const product = e.target.dataset.product;
          
          if (act === 'detail') {
            this.showAnswerDetail(idx, product);
          } else if (act === 'reanswer') {
            this.reAnswerSingleRow(idx, product);
          } else if (act === 'delete') {
            this.confirmDeleteSingleRow(idx, product);
          }
        };
      });
    };

    // 渲染分页
    SOCSystem.renderPagination = function(currentPage, totalPages) {
      const pageInfo = document.getElementById('pageInfo');
      const prevBtn = document.getElementById('prevPageBtn');
      const nextBtn = document.getElementById('nextPageBtn');
      
      if (pageInfo) pageInfo.textContent = `${currentPage} / ${totalPages}`;
      if (prevBtn) prevBtn.disabled = currentPage <= 1;
      if (nextBtn) nextBtn.disabled = currentPage >= totalPages;
    };

    // 事件监听器初始化
    SOCSystem.initEventListeners = function() {
      // 侧边栏隐藏/显示
      const hideSidebarBtn = document.getElementById('hideSidebarBtn');
      if (hideSidebarBtn) {
        hideSidebarBtn.onclick = () => {
          this.state.sidebarHidden = true;
          localStorage.setItem('socSidebarHidden', '1');
          this.applySidebarState();
        };
      }

      // 对话栏隐藏/显示
      const hideChatbarBtn = document.getElementById('hideChatbarBtn');
      if (hideChatbarBtn) {
        hideChatbarBtn.onclick = () => {
          this.state.chatbarHidden = true;
          localStorage.setItem('socChatbarHidden', '1');
          this.applyChatbarState();
        };
      }

      // 大纲面板控制
      const outlineCollapse = document.getElementById('outline-collapse');
      const outlineExpand = document.getElementById('outline-expand');
      if (outlineCollapse) {
        outlineCollapse.onclick = () => {
          this.state.outlineCollapsed = true;
          localStorage.setItem('outlineCollapsed', '1');
          this.applyOutlineState();
        };
      }
      if (outlineExpand) {
        outlineExpand.onclick = () => {
          this.state.outlineCollapsed = false;
          localStorage.setItem('outlineCollapsed', '0');
          this.applyOutlineState();
        };
      }

      // 双击内容区域展开侧边栏
      const content = document.getElementById('socContent');
      if (content) {
        content.ondblclick = (e) => {
          if (e.clientX < 40) {
            this.state.sidebarHidden = false;
            localStorage.setItem('socSidebarHidden', '0');
            this.applySidebarState();
          }
          if (window.innerWidth - e.clientX < 40) {
            this.state.chatbarHidden = false;
            localStorage.setItem('socChatbarHidden', '0');
            this.applyChatbarState();
          }
        };
      }

      // 主要按钮事件
      this.bindMainButtons();
      
      // 搜索过滤事件
      this.bindSearchFilters();
      
      // 分页事件
      this.bindPaginationEvents();
      
      // 任务管理事件
      this.bindTaskEvents();
      
      // LUI对话事件
      this.bindLuiEvents();

      // 全选事件
      const selectAllRow = document.getElementById('selectAllRow');
      if (selectAllRow) {
        selectAllRow.onclick = () => {
          const checkboxes = document.querySelectorAll('.rowCheck');
          checkboxes.forEach(cb => cb.checked = selectAllRow.checked);
        };
      }

      // 响应式布局
      window.addEventListener('resize', () => {
        if (window.innerWidth < 900) {
          document.getElementById('socSidebar').style.display = 'none';
          document.getElementById('socChatbar').style.display = 'none';
        } else {
          this.applySidebarState();
          this.applyChatbarState();
        }
      });
    };

    // 绑定主要按钮事件
    SOCSystem.bindMainButtons = function() {
      // 导入Excel按钮
      const importExcelBtn = document.getElementById('importExcelBtn');
      if (importExcelBtn) {
        importExcelBtn.onclick = () => this.handleBatchImport();
      }

      // 新增单条按钮
      const addSingleEntryBtn = document.getElementById('addSingleEntryBtn');
      if (addSingleEntryBtn) {
        addSingleEntryBtn.onclick = () => this.showAddSingleEntryDialog();
      }

      // 导出按钮
      const exportBtn = document.getElementById('exportBtn');
      if (exportBtn) {
        exportBtn.onclick = () => this.showExportDialog();
      }

      // 批量应答按钮
      const batchReAnswerBtn = document.getElementById('batchReAnswerBtn');
      if (batchReAnswerBtn) {
        batchReAnswerBtn.onclick = () => this.handleBatchReAnswer();
      }

      // 批量删除按钮
      const batchDeleteBtn = document.getElementById('batchDeleteBtn');
      if (batchDeleteBtn) {
        batchDeleteBtn.onclick = () => this.handleBatchDelete();
      }

      // 更多菜单按钮
      const mainActionBtn = document.getElementById('mainActionBtn');
      const mainActionMenu = document.getElementById('mainActionMenu');
      if (mainActionBtn && mainActionMenu) {
        mainActionBtn.onclick = (e) => {
          e.stopPropagation();
          mainActionMenu.style.display = mainActionMenu.style.display === 'none' ? 'block' : 'none';
        };

        // 点击其他地方关闭菜单
        document.addEventListener('click', (e) => {
          if (!mainActionMenu.contains(e.target) && !mainActionBtn.contains(e.target)) {
            mainActionMenu.style.display = 'none';
          }
        });

        // 菜单项点击事件
        mainActionMenu.onclick = (e) => {
          if (!e.target.classList.contains('main-action-item')) return;
          e.stopPropagation();
          
          const action = e.target.dataset.act;
          switch (action) {
            case 'exportSetting':
              this.showExportSettingDialog();
              break;
            case 'openParamSetting':
              this.showParamSettingDialog();
              break;
            case 'openReferenceDoc':
              this.showReferenceDocDialog();
              break;
            case 'priorityConfig':
              this.showPriorityConfigDialog();
              break;
            case 'satisfactionCalc':
              this.calculateSatisfaction();
              break;
            case 'viewSimilar':
              this.showSimilarItems();
              break;
            case 'historyRecord':
              this.showHistoryDialog();
              break;
            case 'clearAll':
              this.confirmClearAllItems();
              break;
          }
          mainActionMenu.style.display = 'none';
        };
      }
    };

    // 绑定搜索过滤事件
    SOCSystem.bindSearchFilters = function() {
      const searchInputs = ['searchProduct', 'searchAnswer', 'searchExplain', 'searchRemark', 'searchAnswerType'];
      searchInputs.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
          element.onchange = () => this.renderAnswerTable();
          element.oninput = () => this.renderAnswerTable();
        }
      });

      // 编号和描述搜索
      const searchNo = document.getElementById('searchNo');
      const searchDesc = document.getElementById('searchDesc');
      if (searchNo) {
        searchNo.oninput = (e) => {
          this.data.tableSearch.no = e.target.value.trim();
          this.renderAnswerTable();
        };
      }
      if (searchDesc) {
        searchDesc.oninput = (e) => {
          this.data.tableSearch.desc = e.target.value.trim();
          this.renderAnswerTable();
        };
      }
    };

    // 绑定分页事件
    SOCSystem.bindPaginationEvents = function() {
      const prevPageBtn = document.getElementById('prevPageBtn');
      const nextPageBtn = document.getElementById('nextPageBtn');
      const pageSizeSelect = document.getElementById('pageSizeSelect');

      if (prevPageBtn) {
        prevPageBtn.onclick = () => {
          if (this.data.tablePage.page > 1) {
            this.data.tablePage.page--;
            this.renderAnswerTable();
          }
        };
      }

      if (nextPageBtn) {
        nextPageBtn.onclick = () => {
          this.data.tablePage.page++;
          this.renderAnswerTable();
        };
      }

      if (pageSizeSelect) {
        pageSizeSelect.value = this.data.tablePage.pageSize;
        pageSizeSelect.onchange = (e) => {
          this.data.tablePage.pageSize = parseInt(e.target.value);
          this.data.tablePage.page = 1;
          this.renderAnswerTable();
        };
      }
    };

    // 批量导入功能
    SOCSystem.handleBatchImport = function() {
      // 检查是否已配置产品参数
      const configuredProducts = this.getConfiguredProducts();
      
      if (configuredProducts.length === 0) {
        this.showParamSettingDialogWithCallback(() => {
          const newConfiguredProducts = this.getConfiguredProducts();
          if (newConfiguredProducts.length > 0) {
            this.showBatchImportDialog(newConfiguredProducts);
          }
        });
      } else {
        this.showBatchImportDialog(configuredProducts);
      }
    };

    // 获取已配置的产品列表
    SOCSystem.getConfiguredProducts = function() {
      try {
        const currentParams = localStorage.getItem('currentParams');
        if (currentParams) {
          const params = JSON.parse(currentParams);
          return params.products || [];
        }
      } catch (e) {
        console.error('解析当前参数失败:', e);
      }
      return ['5GC', 'VoLTE', 'IMS']; // 默认产品
    };

    // 显示批量导入对话框
    SOCSystem.showBatchImportDialog = function(configuredProducts) {
      // 模拟Excel数据
      const excelQuestions = [
        { no: 1, desc: '系统支持多语言界面' },
        { no: '1.1', desc: '是否支持自定义词库' },
        { no: '1.2', desc: '系统支持多语言功能' },
        { no: 2, desc: '是否支持批量导入' },
        { no: '2.1', desc: '系统是否支持自定义词库' },
        { no: '2.2', desc: '是否支持云部署' },
        { no: '2.2.1', desc: '支持哪些云平台' },
        { no: '2.2.2', desc: '是否支持容器化部署' },
        { no: 3, desc: '系统安全性如何' },
        { no: 4, desc: '是否支持高可用部署' }
      ];

      const fileName = `模拟Excel文件_${new Date().toLocaleString('zh-CN').replace(/[\/\s:]/g, '')}.xlsx`;

      const dialogHtml = `
        <div style='width:480px;max-width:96vw;background:#fff;border-radius:12px;box-shadow:0 4px 24px #aaa;padding:24px;'>
          <div style='font-size:18px;font-weight:600;margin-bottom:16px;text-align:center;'>
            <span style='color:#1765d5;'>📄</span> 批量应答处理
          </div>
          <div style='background:#f5f5f5;border-radius:8px;padding:16px;margin-bottom:16px;'>
            <div style='font-size:14px;color:#666;margin-bottom:8px;'>文件信息</div>
            <div style='font-size:15px;font-weight:500;'>${fileName}</div>
            <div style='font-size:13px;color:#666;margin-top:4px;'>已解析 ${excelQuestions.length} 个条目</div>
          </div>
          <div style='margin-bottom:16px;'>
            <div style='font-size:15px;font-weight:500;margin-bottom:8px;'>请选择待应答的产品</div>
            <div style='font-size:13px;color:#666;margin-bottom:12px;'>从已配置的产品中选择（可多选）</div>
            <div style='display:flex;flex-wrap:wrap;gap:8px;'>
              ${configuredProducts.map(product => `
                <label style='display:flex;align-items:center;padding:8px 12px;border:1px solid #d9d9d9;border-radius:6px;cursor:pointer;transition:all 0.2s;' 
                       onmouseover='this.style.borderColor="#1765d5";this.style.backgroundColor="#f6f9ff"' 
                       onmouseout='this.style.borderColor="#d9d9d9";this.style.backgroundColor="transparent"'>
                  <input type='checkbox' class='batch-product-checkbox' value='${product}' style='margin-right:6px;' checked/>
                  <span style='font-size:14px;'>${product}</span>
                </label>
              `).join('')}
            </div>
          </div>
          <div style='background:#e6f7ff;border-radius:6px;padding:12px;margin-bottom:20px;'>
            <div style='font-size:13px;color:#1765d5;'>
              <span style='margin-right:4px;'>💡</span>
              系统将为每个选中的产品生成独立的应答结果
            </div>
            <div style='font-size:12px;color:#666;margin-top:6px;'>
              Ctrl+Enter 确定  |  ESC 取消 
            </div>
          </div>
          <div style='text-align:center;display:flex;justify-content:center;gap:16px;'>
            <button id='confirmBatchProcessBtn' style='background:#1765d5;color:#fff;border:none;border-radius:8px;padding:10px 24px;font-size:15px;cursor:pointer;box-shadow:0 2px 8px #e6f7ff;transition:all 0.2s;'>确定生成应答</button>
            <button id='cancelBatchProcessBtn' style='background:#f5f6fa;color:#333;border:none;border-radius:8px;padding:10px 24px;font-size:15px;cursor:pointer;transition:all 0.2s;'>取消</button>
          </div>
        </div>
      `;

      this.showModal(dialogHtml, (modal) => {
        // 键盘事件支持
        modal.addEventListener('keydown', (e) => {
          if (e.key === 'Escape') {
            modal.remove();
          } else if (e.key === 'Enter' && e.ctrlKey) {
            modal.querySelector('#confirmBatchProcessBtn').click();
          }
        });

        // 取消按钮
        modal.querySelector('#cancelBatchProcessBtn').onclick = () => modal.remove();

        // 确定按钮
        modal.querySelector('#confirmBatchProcessBtn').onclick = () => {
          const selectedProducts = Array.from(modal.querySelectorAll('.batch-product-checkbox:checked')).map(cb => cb.value);
          
          if (selectedProducts.length === 0) {
            alert('请至少选择一个产品');
            return;
          }

          this.generateBatchAnswers(excelQuestions, selectedProducts, fileName);
          modal.remove();
        };
      });
    };

    // 生成批量应答数据
    SOCSystem.generateBatchAnswers = function(questions, selectedProducts, fileName) {
      const loadingHtml = `
        <div style='background:#fff;border-radius:12px;padding:32px;text-align:center;min-width:300px;'>
          <div style='font-size:18px;font-weight:600;margin-bottom:16px;'>正在生成应答...</div>
          <div style='font-size:14px;color:#666;margin-bottom:16px;'>为 ${questions.length} 个条目 × ${selectedProducts.length} 个产品生成应答</div>
          <div style='width:100%;height:4px;background:#f0f0f0;border-radius:2px;overflow:hidden;'>
            <div id='batchProgress' style='width:0%;height:100%;background:#1765d5;transition:width 0.3s;'></div>
          </div>
        </div>
      `;

      this.showModal(loadingHtml, null, 'rgba(0,0,0,0.5)');

      // 模拟批量生成过程
      let processedCount = 0;
      const totalCount = questions.length;

      questions.forEach((question, index) => {
        setTimeout(() => {
          // 为每个产品生成独立的应答数据
          const productData = {};
          selectedProducts.forEach(product => {
            productData[product] = {
              answer: ['FC', 'PC', 'NC', 'N/A'][Math.floor(Math.random() * 4)],
              explain: `${product}系统${['完全支持', '部分支持', '暂不支持', '不适用'][Math.floor(Math.random() * 4)]}该功能（批量导入）`,
              supplement: 'AI生成的补充说明',
              remark: `来源：${fileName}`,
              index: `${product}文档-${Math.floor(Math.random() * 10) + 1}.${Math.floor(Math.random() * 10) + 1}`,
              source: '文档库',
              answerType: 'AI'
            };
          });

          this.data.answerList.push({
            no: question.no,
            desc: question.desc,
            products: [...selectedProducts],
            productData: productData
          });

          processedCount++;
          const progress = (processedCount / totalCount) * 100;
          const progressBar = document.getElementById('batchProgress');
          if (progressBar) {
            progressBar.style.width = progress + '%';
          }

          // 最后一个条目处理完成
          if (processedCount === totalCount) {
            setTimeout(() => {
              this.closeModal();
              this.saveAnswerList();
              this.renderAnswerTable();

              // 显示成功提示
              const successMsg = `批量应答生成完成！\n\n` +
                `📄 文件：${fileName}\n` +
                `📝 条目数：${questions.length}\n` +
                `🏷️ 产品数：${selectedProducts.length}\n` +
                `📊 总应答数：${questions.length * selectedProducts.length}\n` +
                `🎯 选中产品：${selectedProducts.join('、')}`;

              alert(successMsg);
              this.updateOutline();
            }, 500);
          }
        }, index * 100);
      });
    };

    // 模态对话框工具函数
    SOCSystem.showModal = function(content, callback = null, bgColor = 'rgba(0,0,0,0.18)') {
      const modal = document.createElement('div');
      modal.style.cssText = `position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:999;background:${bgColor};display:flex;align-items:center;justify-content:center;`;
      modal.innerHTML = content;
      document.body.appendChild(modal);
      
      if (callback) callback(modal);
      return modal;
    };

    SOCSystem.closeModal = function() {
      const modals = document.querySelectorAll('div[style*="position:fixed"][style*="z-index:999"]');
      modals.forEach(modal => modal.remove());
    };

    // 大纲树相关功能
    SOCSystem.updateOutline = function() {
      const tree = this.buildOutlineTree(this.data.answerList);
      const treeRoot = document.createElement('ul');
      treeRoot.style.cssText = 'margin:0;padding:0;';
      this.renderOutlineTree(tree, treeRoot);
      
      const treeDiv = document.getElementById('outline-tree');
      if (treeDiv) {
        treeDiv.innerHTML = '';
        treeDiv.appendChild(treeRoot);
        this.bindOutlineEvents();
      }
    };

    SOCSystem.buildOutlineTree = function(list) {
      const root = [];
      const nodeMap = {};

      // 创建所有节点
      list.forEach((row, idx) => {
        const no = String(row.no || idx + 1);
        const desc = row.desc || '';
        const node = { no, desc, children: [], idx };
        nodeMap[no] = node;
      });

      // 建立父子关系
      list.forEach((row, idx) => {
        const no = String(row.no || idx + 1);
        const node = nodeMap[no];
        const parts = no.split('.');

        if (parts.length === 1) {
          root.push(node);
        } else {
          const parentNo = parts.slice(0, -1).join('.');
          const parentNode = nodeMap[parentNo];
          if (parentNode) {
            parentNode.children.push(node);
          } else {
            root.push(node);
          }
        }
      });

      return root;
    };

    SOCSystem.renderOutlineTree = function(tree, parentUl) {
      tree.forEach(node => {
        const li = document.createElement('li');
        li.style.cssText = 'list-style:none;margin:2px 0;font-size:14px;cursor:pointer;';
        
        let toggleHtml = '';
        if (node.children && node.children.length > 0) {
          toggleHtml = '<span class="outline-toggle" style="font-size:12px;margin-right:4px;cursor:pointer;color:#666;user-select:none;">▼</span>';
        }
        
        li.innerHTML = `${toggleHtml}<span class="outline-node" data-idx="${node.idx}" data-no="${node.no}" style="padding:2px 4px;border-radius:4px;display:inline-block;max-width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;" title="${node.desc}">${node.no}. ${node.desc}</span>`;
        
        if (node.children && node.children.length > 0) {
          const childUl = document.createElement('ul');
          childUl.style.cssText = 'margin-left:18px;padding-left:8px;';
          this.renderOutlineTree(node.children, childUl);
          li.appendChild(childUl);
        }
        
        parentUl.appendChild(li);
      });
    };

    SOCSystem.bindOutlineEvents = function() {
      // 节点点击事件
      document.querySelectorAll('.outline-node').forEach(node => {
        node.onclick = (e) => {
          e.stopPropagation();
          const idx = node.dataset.idx;
          const element = document.querySelector(`[data-row-idx="${idx}"]`);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            element.style.background = '#e6f7ff';
            setTimeout(() => { element.style.background = ''; }, 1200);
          }
        };

        node.onmouseenter = function() { this.style.background = '#f0f0f0'; };
        node.onmouseleave = function() { this.style.background = ''; };
      });

      // 展开/收起事件
      document.querySelectorAll('.outline-toggle').forEach(toggle => {
        toggle.onclick = (e) => {
          e.stopPropagation();
          const childUl = toggle.parentElement.querySelector('ul');
          if (childUl) {
            if (childUl.style.display === 'none') {
              childUl.style.display = '';
              toggle.textContent = '▼';
            } else {
              childUl.style.display = 'none';
              toggle.textContent = '▶';
            }
          }
        };
      });
    };

    // 任务管理相关功能
    SOCSystem.bindTaskEvents = function() {
      const taskTabBar = document.getElementById('taskTabBar');
      const taskManageBtn = document.getElementById('taskManageBtn');

      if (taskTabBar) {
        taskTabBar.onclick = (e) => {
          if (e.target.tagName === 'BUTTON') {
            this.renderTaskTab(e.target.dataset.tab);
          }
        };
      }

      if (taskManageBtn) {
        taskManageBtn.onclick = () => this.showTaskManageDialog();
      }
    };

    SOCSystem.renderTaskTab = function(tab) {
      this.state.currentTab = tab;
      const taskTabContent = document.getElementById('taskTabContent');
      const buttons = document.querySelectorAll('.soc-tab-btn');
      
      buttons.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.tab === tab);
      });

      if (!taskTabContent) return;

      switch (tab) {
        case 'todo':
          taskTabContent.innerHTML = '<div>暂无待办任务</div>';
          break;
        case 'history':
          taskTabContent.innerHTML = '<div>暂无历史归档</div>';
          break;
        case 'template':
          this.renderTemplateManage();
          break;
      }
    };

    SOCSystem.renderTemplateManage = function() {
      const taskTabContent = document.getElementById('taskTabContent');
      if (!taskTabContent) return;

      let html = '<div style="font-weight:500;margin-bottom:8px;">模板列表</div>';
      if (this.data.templateList.length === 0) {
        html += '<div>暂无模板</div>';
      } else {
        html += '<ul style="padding-left:0;list-style:none;">' + 
          this.data.templateList.map(t => `
            <li style='margin-bottom:6px;'>
              <span>${t.name}${t.isDefault ? ' <span style="color:#1765d5;font-size:12px;">(默认)</span>' : ''}</span>
              <button data-act="use" data-id="${t.id}" style="margin-left:8px;">使用</button>
              <button data-act="rename" data-id="${t.id}">重命名</button>
              <button data-act="del" data-id="${t.id}">删除</button>
              <button data-act="setDefault" data-id="${t.id}">设为默认</button>
            </li>
          `).join('') + '</ul>';
      }
      taskTabContent.innerHTML = html;
    };

    SOCSystem.showTaskManageDialog = function() {
      const dialogHtml = `
        <div style='width:600px;max-width:98vw;background:#fff;border-radius:12px;padding:24px;'>
          <div style='font-size:18px;font-weight:500;margin-bottom:12px;'>任务管理</div>
          <div style='margin-bottom:10px;'>
            <button id='createTaskBtn' style='background:#52c41a;color:#fff;border:none;border-radius:4px;padding:6px 16px;cursor:pointer;'>新建任务</button>
          </div>
          <div style='border:1px solid #e9ecef;border-radius:6px;min-height:200px;padding:16px;'>
            <div>当前任务：SOC应答任务（默认）</div>
            <div style='margin-top:12px;color:#666;'>任务管理功能正在完善中...</div>
          </div>
          <div style='text-align:right;margin-top:16px;'>
            <button id='closeTaskBtn' style='background:#f5f5f5;color:#666;border:1px solid #d9d9d9;border-radius:4px;padding:6px 16px;cursor:pointer;'>关闭</button>
          </div>
        </div>
      `;

      this.showModal(dialogHtml, (modal) => {
        modal.querySelector('#closeTaskBtn').onclick = () => modal.remove();
        modal.querySelector('#createTaskBtn').onclick = () => {
          const name = prompt('请输入任务名称');
          if (name) {
            alert(`任务"${name}"创建成功（演示）`);
          }
        };
      });
    };

    // LUI对话相关功能
    SOCSystem.bindLuiEvents = function() {
      const luiForm = document.getElementById('luiForm');
      const luiInput = document.getElementById('luiInput');
      const quickCmdHeader = document.getElementById('quickCmdHeader');
      const quickCmdBar = document.getElementById('quickCmdBar');
      const quickCmdToggle = document.getElementById('quickCmdToggle');

      if (luiForm && luiInput) {
        luiForm.onsubmit = (e) => {
          e.preventDefault();
          const query = luiInput.value.trim();
          if (!query) return;
          
          this.addLuiHistory('user', query);
          luiInput.value = '';
          
          // 模拟AI应答
          setTimeout(() => {
            const response = this.simulateAIResponse(query);
            this.addLuiHistory('ai', response);
          }, 500);
        };
      }

      // 快捷指令展开/收起
      if (quickCmdHeader && quickCmdToggle) {
        quickCmdHeader.onclick = () => {
          const isHidden = quickCmdBar.style.display === 'none';
          quickCmdBar.style.display = isHidden ? 'flex' : 'none';
          quickCmdToggle.style.transform = isHidden ? 'rotate(0deg)' : 'rotate(180deg)';
        };
      }

      // 快捷指令点击事件
      if (quickCmdBar) {
        quickCmdBar.onclick = (e) => {
          if (e.target.classList.contains('quick-cmd-btn')) {
            if (luiInput) {
              luiInput.value = e.target.textContent;
              luiInput.focus();
            }
          }
        };
      }
    };

    SOCSystem.addLuiHistory = function(role, text) {
      this.data.luiHistory.push({ role, text, time: Date.now() });
      this.saveLuiHistory();
      this.renderLuiHistory();
    };

    SOCSystem.renderLuiHistory = function() {
      const luiHistory = document.getElementById('luiHistory');
      if (!luiHistory) return;

      luiHistory.innerHTML = this.data.luiHistory.map(item => `
        <div style='display:flex;${item.role === "user" ? "justify-content:flex-end;" : "justify-content:flex-start;"}'>
          <div style='max-width:70%;padding:8px 14px;border-radius:16px;box-shadow:0 1px 4px #eee;${item.role === "user" ? "background:#c2dfff;color:#1a1a1a;margin-left:30px;text-align:right;" : "background:#fafbfc;color:#1a1a1a;margin-right:30px;text-align:left;"}'>
            ${item.text}
          </div>
        </div>
      `).join('');
      
      luiHistory.scrollTop = luiHistory.scrollHeight;
    };

    SOCSystem.renderQuickCommands = function() {
      const quickCmdBar = document.getElementById('quickCmdBar');
      if (!quickCmdBar) return;

      const quickCmds = [
        '设置项目参数：产品5GC和VoLTE，国家泰国，运营商AIS',
        '查看当前项目的满足度统计',
        '重新应答PC状态的条目',
        '导出满足条件的条目',
        '设置优先级配置',
        '查看相似条目'
      ];

      quickCmdBar.innerHTML = quickCmds.map(cmd => 
        `<button type='button' class='quick-cmd-btn'>${cmd}</button>`
      ).join('');
    };

    SOCSystem.simulateAIResponse = function(query) {
      // 简单的AI应答模拟
      if (query.includes('参数') || query.includes('设置')) {
        return '【参数设置】已为您处理参数设置请求（模拟）';
      } else if (query.includes('统计') || query.includes('满足度')) {
        const list = this.data.answerList;
        let fcCount = 0, pcCount = 0, ncCount = 0;
        list.forEach(row => {
          if (row.productData) {
            Object.values(row.productData).forEach(pdata => {
              if (pdata.answer === 'FC') fcCount++;
              else if (pdata.answer === 'PC') pcCount++;
              else if (pdata.answer === 'NC') ncCount++;
            });
          }
        });
        return `【查询结果】当前项目满足度统计：FC ${fcCount}条，PC ${pcCount}条，NC ${ncCount}条。`;
      } else if (query.includes('应答') || query.includes('重新')) {
        return '【批量操作】已触发重新应答处理（模拟）';
      } else if (query.includes('导出')) {
        return '【导出操作】已启动导出流程（模拟）';
      } else {
        return '我理解您的需求，正在为您处理...（模拟AI应答）';
      }
    };

    // 删除相关功能
    SOCSystem.handleBatchDelete = function() {
      const checkedIndexes = Array.from(document.querySelectorAll('.rowCheck:checked')).map(cb => parseInt(cb.dataset.idx));
      
      if (checkedIndexes.length === 0) {
        alert('请先选择要删除的条目');
        return;
      }

      this.confirmBatchDelete(checkedIndexes);
    };

    SOCSystem.confirmBatchDelete = function(indexes) {
      const dialogHtml = `
        <div style='width:420px;max-width:96vw;background:#fff;border-radius:12px;padding:24px;'>
          <div style='font-size:18px;font-weight:500;margin-bottom:12px;color:#ff4d4f;'>⚠️ 批量删除确认</div>
          <div style='padding:16px;background:#fff2f0;border:1px solid #ffccc7;border-radius:6px;margin-bottom:16px;'>
            <div style='font-weight:500;margin-bottom:8px;'>即将删除 ${indexes.length} 个条目</div>
            <div style='color:#666;'>删除后将无法恢复，请确认是否继续？</div>
          </div>
          <div style='text-align:right;display:flex;justify-content:flex-end;gap:12px;'>
            <button id='confirmBatchDeleteBtn' style='background:#ff4d4f;color:#fff;border:none;border-radius:4px;padding:8px 24px;cursor:pointer;'>确认删除</button>
            <button id='cancelBatchDeleteBtn' style='background:#f5f5f5;color:#666;border:1px solid #d9d9d9;border-radius:4px;padding:8px 24px;cursor:pointer;'>取消</button>
          </div>
        </div>
      `;

      this.showModal(dialogHtml, (modal) => {
        modal.querySelector('#cancelBatchDeleteBtn').onclick = () => modal.remove();
        modal.querySelector('#confirmBatchDeleteBtn').onclick = () => {
          this.deleteItems(indexes);
          modal.remove();
          this.addLuiHistory('system', `已批量删除 ${indexes.length} 个条目`);
        };
      });
    };

    SOCSystem.deleteItems = function(indexes) {
      const sortedIndexes = indexes.sort((a, b) => b - a);
      sortedIndexes.forEach(idx => {
        if (idx >= 0 && idx < this.data.answerList.length) {
          this.data.answerList.splice(idx, 1);
        }
      });
      
      this.saveAnswerList();
      this.renderAnswerTable();
      this.updateOutline();
      
      // 取消所有选中状态
      document.querySelectorAll('.rowCheck').forEach(cb => cb.checked = false);
      const selectAllCheckbox = document.getElementById('selectAllRow');
      if (selectAllCheckbox) selectAllCheckbox.checked = false;
    };

    // 其他功能的占位符实现
    SOCSystem.showAddSingleEntryDialog = function() {
      const dialogHtml = `
        <div style='width:400px;max-width:96vw;background:#fff;border-radius:12px;padding:24px;'>
          <div style='font-size:20px;font-weight:600;margin-bottom:18px;text-align:center;'>新增应答条目</div>
          <div style='margin-bottom:16px;'>
            <label style='font-size:15px;font-weight:500;'>编号</label><br/>
            <input id='singleNo' style='width:100%;height:38px;border-radius:8px;border:1px solid #d9d9d9;padding:0 12px;font-size:15px;outline:none;' placeholder='自动编号或自定义' />
          </div>
          <div style='margin-bottom:16px;'>
            <label style='font-size:15px;font-weight:500;'>条目描述 <span style="color:#ff4d4f;">*</span></label><br/>
            <textarea id='singleDesc' style='width:100%;height:60px;border-radius:8px;border:1px solid #d9d9d9;padding:8px 12px;font-size:15px;outline:none;resize:vertical;' placeholder='请输入条目描述' required></textarea>
          </div>
          <div style='margin-bottom:20px;'>
            <label style='font-size:15px;font-weight:500;'>备注</label><br/>
            <input id='singleRemark' style='width:100%;height:38px;border-radius:8px;border:1px solid #d9d9d9;padding:0 12px;font-size:15px;outline:none;' placeholder='可填写备注' />
          </div>
          <div style='text-align:center;margin-top:24px;display:flex;justify-content:center;gap:16px;'>
            <button id='doAddSingleEntry' style='background:#1765d5;color:#fff;border:none;border-radius:8px;padding:10px 40px;font-size:16px;cursor:pointer;'>确定</button>
            <button id='cancelAddSingleEntry' style='background:#f5f6fa;color:#333;border-radius:8px;padding:10px 32px;font-size:16px;border:none;cursor:pointer;'>取消</button>
          </div>
        </div>
      `;

      this.showModal(dialogHtml, (modal) => {
        modal.querySelector('#cancelAddSingleEntry').onclick = () => modal.remove();
        modal.querySelector('#doAddSingleEntry').onclick = () => {
          const no = modal.querySelector('#singleNo').value.trim();
          const desc = modal.querySelector('#singleDesc').value.trim();
          const remark = modal.querySelector('#singleRemark').value.trim();
          
          if (!desc) {
            alert('条目描述不能为空');
            return;
          }

          const products = ['5GC', 'VoLTE', 'IMS'];
          const productData = {};
          products.forEach(p => {
            productData[p] = {
              answer: '',
              explain: '',
              supplement: '',
              remark: remark || '',
              index: '',
              source: '',
              answerType: 'AI'
            };
          });

          this.data.answerList.push({
            no: no || (this.data.answerList.length + 1),
            products,
            desc,
            productData
          });

          this.saveAnswerList();
          this.renderAnswerTable();
          modal.remove();
        };
      });
    };

    // 占位符功能实现
    SOCSystem.showExportDialog = function() {
      alert('导出功能（演示版本）');
    };

    SOCSystem.handleBatchReAnswer = function() {
      const checkedIndexes = Array.from(document.querySelectorAll('.rowCheck:checked')).map(cb => parseInt(cb.dataset.idx));
      if (checkedIndexes.length === 0) {
        alert('请先选择要重新应答的条目');
        return;
      }
      alert(`已对${checkedIndexes.length}个条目重新应答（演示版本）`);
    };

    SOCSystem.showAnswerDetail = function(idx, product) {
      alert(`查看详情：条目${idx + 1}，产品${product}（演示版本）`);
    };

    SOCSystem.reAnswerSingleRow = function(idx, product) {
      const list = this.data.answerList;
      if (list[idx] && list[idx].productData && list[idx].productData[product]) {
        list[idx].productData[product].answer = ['FC', 'PC', 'NC', 'N/A'][Math.floor(Math.random() * 4)];
        list[idx].productData[product].explain = `AI重新生成的${product}应答说明`;
        list[idx].productData[product].answerType = 'AI';
        this.saveAnswerList();
        this.renderAnswerTable();
        alert(`条目${idx + 1}的${product}产品重新应答完成`);
      }
    };

    SOCSystem.confirmDeleteSingleRow = function(idx, product) {
      if (confirm(`确定删除条目${idx + 1}的${product}产品应答？`)) {
        const list = this.data.answerList;
        if (list[idx] && list[idx].productData && list[idx].productData[product]) {
          delete list[idx].productData[product];
          const productIndex = list[idx].products.indexOf(product);
          if (productIndex > -1) {
            list[idx].products.splice(productIndex, 1);
          }
          if (Object.keys(list[idx].productData).length === 0) {
            list.splice(idx, 1);
          }
          this.saveAnswerList();
          this.renderAnswerTable();
        }
      }
    };

    // 其他弹窗的占位符实现
    SOCSystem.showExportSettingDialog = function() {
      alert('导出设置功能（演示版本）');
    };

    SOCSystem.showParamSettingDialog = function() {
      alert('参数设置功能（演示版本）');
    };

    SOCSystem.showParamSettingDialogWithCallback = function(callback) {
      alert('参数设置功能（演示版本）');
      if (callback) callback();
    };

    SOCSystem.showReferenceDocDialog = function() {
      alert('参考文档功能（演示版本）');
    };

    SOCSystem.showPriorityConfigDialog = function() {
      alert('优先级配置功能（演示版本）');
    };

    SOCSystem.calculateSatisfaction = function() {
      const list = this.data.answerList;
      let totalItems = 0, fcCount = 0, pcCount = 0, ncCount = 0;
      
      list.forEach(row => {
        if (row.productData) {
          Object.values(row.productData).forEach(pdata => {
            totalItems++;
            if (pdata.answer === 'FC') fcCount++;
            else if (pdata.answer === 'PC') pcCount++;
            else if (pdata.answer === 'NC') ncCount++;
          });
        }
      });

      const satisfaction = totalItems > 0 ? ((fcCount + pcCount) / totalItems * 100).toFixed(1) : 0;
      const resultHtml = `
        <div style="background:#f6ffed;border:1px solid #b7eb8f;border-radius:6px;padding:16px;margin:8px 0;">
          <div style="font-size:16px;font-weight:500;color:#389e0d;margin-bottom:12px;">📊 满足度计算结果</div>
          <div style="text-align:center;">
            <div style="font-size:28px;font-weight:bold;color:#52c41a;">${satisfaction}%</div>
            <div style="color:#666;font-size:14px;">满足度 (FC+PC)/总条目</div>
          </div>
          <div style="margin-top:12px;display:grid;grid-template-columns:repeat(3,1fr);gap:8px;font-size:13px;">
            <div style="color:#52c41a;">FC：${fcCount}</div>
            <div style="color:#faad14;">PC：${pcCount}</div>
            <div style="color:#ff4d4f;">NC：${ncCount}</div>
          </div>
        </div>
      `;
      
      this.addLuiHistory('system', resultHtml);
    };

    SOCSystem.showSimilarItems = function() {
      alert('相似条目查看功能（演示版本）');
    };

    SOCSystem.showHistoryDialog = function() {
      alert('历史记录功能（演示版本）');
    };

    SOCSystem.confirmClearAllItems = function() {
      if (confirm('确定清空全部条目？此操作不可恢复！')) {
        this.data.answerList = [];
        this.saveAnswerList();
        this.renderAnswerTable();
        this.updateOutline();
        this.addLuiHistory('system', '已清空全部条目');
      }
    };

    // 系统启动
    document.addEventListener('DOMContentLoaded', function() {
      SOCSystem.init();
    });
  </script>
</body>
</html> 