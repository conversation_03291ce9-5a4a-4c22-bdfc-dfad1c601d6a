package com.zte.mcrm.channel.controller.channel;

import com.alibaba.fastjson.JSON;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.ServiceResultUtil;
import com.zte.mcrm.adapter.constant.HeaderNameConst;
import com.zte.mcrm.adapter.service.IChannelValidateService;
import com.zte.mcrm.channel.model.dto.CrmCustomerDertimineParamDTO;
import com.zte.mcrm.channel.model.dto.OpportunityQueryParamDTO;
import com.zte.mcrm.channel.model.dto.OpportunityStatusDTO;
import com.zte.mcrm.channel.model.entity.ExternalOpportunityDetail;
import com.zte.mcrm.channel.model.vo.CrmCustomerDetermineResultVO;
import com.zte.mcrm.channel.model.vo.OpportunityDataVO;
import com.zte.mcrm.channel.model.vo.OpportunityQueryParamVO;
import com.zte.mcrm.channel.service.channel.IOpportunityQueryService;
import com.zte.mcrm.common.annotation.SystemAuthVerify;
import com.zte.mcrm.logger.bussiness.service.LoggerService;
import com.zte.springbootframe.common.consts.SysGlobalConst;
import com.zte.springbootframe.util.local.LocalMessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 描述：商机查询
 * 创建时间：2021/9/22
 *
 * @author：王丹凤6396000572
 */
@Api(tags ="商机查询API(对外接口)")
@RestController
@RequestMapping("/opportunity")
public class OpportunityQueryController {

    private static final Logger logger = LoggerFactory.getLogger(OpportunityQueryController.class);

    @Autowired
    private IOpportunityQueryService opportunityQueryService;

    @Autowired
    private IChannelValidateService channelValidateService;

    @Autowired
    private LoggerService loggerService;

    @ApiOperation("分页查询商机列表")
    @PostMapping(value = "/getOpportunityList")
    @SystemAuthVerify(serviceNamesWhitelist = {"zte-crm"})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = HeaderNameConst.X_AUTH_NONCE, dataType="String", dataTypeClass=String.class, required = false, value = "随机字符串"),
            @ApiImplicitParam(paramType = "header", name = HeaderNameConst.X_AUTH_TIMESTAMP, dataType="String", dataTypeClass=String.class, required = false, value = "时间戳"),
            @ApiImplicitParam(paramType = "header", name = HeaderNameConst.X_AUTH_ACCESSKEY, dataType="String", dataTypeClass=String.class, required = false, value = "accessKey"),
            @ApiImplicitParam(paramType = "header", name = HeaderNameConst.X_AUTH_SIGNATURE, dataType="String", dataTypeClass=String.class, required = false, value = "签名")
    })
    public ServiceData getOpportunityList(@RequestBody OpportunityQueryParamDTO paramDTO
    ) {
        OpportunityQueryParamVO paramVO = paramDTO.toOpportunityQueryParamVO();
        PageRows<OpportunityDataVO> pageRows = null;
        try {
            pageRows = opportunityQueryService.getOpportunityList(paramVO);
        } catch (Exception e) {
            logger.error("OpportunityQueryController.getOpportunityList error ", e);
            return ServiceResultUtil.businessError(null);
        }
        return ServiceResultUtil.success(pageRows);
    }


    @ApiOperation("查询商机详情接口--对外提供 -Get方式")
    @GetMapping(value="/external/opportunityinfo/{optyCd}")
    @SystemAuthVerify(serviceNamesWhitelist = {"zte-crm"})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = HeaderNameConst.X_AUTH_NONCE, dataType="String", dataTypeClass=String.class, required = false, value = "随机字符串"),
            @ApiImplicitParam(paramType = "header", name = HeaderNameConst.X_AUTH_TIMESTAMP, dataType="String", dataTypeClass=String.class, required = false, value = "时间戳"),
            @ApiImplicitParam(paramType = "header", name = HeaderNameConst.X_AUTH_ACCESSKEY, dataType="String", dataTypeClass=String.class, required = false, value = "accessKey"),
            @ApiImplicitParam(paramType = "header", name = HeaderNameConst.X_AUTH_SIGNATURE, dataType="String", dataTypeClass=String.class, required = false, value = "签名")
    })
    public ServiceData<ExternalOpportunityDetail> getExternalOpportunityDetail(@PathVariable("optyCd") String optyCd) throws Exception {
        //业务操作可以不捕获异常,由统一的异常处理方法处理
        ExternalOpportunityDetail externalOpportunityDetail = opportunityQueryService.getExternalOpportunityDetail(optyCd);
        //返回统一的服务端数据
        return ServiceResultUtil.success(externalOpportunityDetail);
    }

    @ApiOperation("项目与交易汇总查询接口")
    @PostMapping(value = "/external/determineIntransitiveAndValidDocuments")
    public ServiceData<List<CrmCustomerDetermineResultVO>> determineIntransitsAndValidDocuments(
            @Validated @RequestBody CrmCustomerDertimineParamDTO crmCustomerDertimineParamDTO,
            BindingResult bindingResult) throws Exception {
        // 数据校验
        if (bindingResult != null && bindingResult.hasErrors()) {
            throw new com.zte.springbootframe.common.exception.ValidationException(bindingResult);
        }
        return ServiceResultUtil.success(opportunityQueryService.determineIntransitsAndValidDocuments(crmCustomerDertimineParamDTO));
    }

    @ApiOperation("根据渠道商客户编码及年份查询是否存在有效【政企】单据接口")
    @PostMapping(value = "/external/determineIntransitiveAndValidGovs")
    public ServiceData<List<CrmCustomerDetermineResultVO>> determineIntransitiveAndValidGovs(
            @Validated @RequestBody CrmCustomerDertimineParamDTO crmCustomerDertimineParamDTO,
            BindingResult bindingResult) throws Exception {
        // 数据校验
        if (bindingResult != null && bindingResult.hasErrors()) {
            throw new com.zte.springbootframe.common.exception.ValidationException(bindingResult);
        }
        return ServiceResultUtil.success(channelValidateService.determineIntransitsAndValidByGov(crmCustomerDertimineParamDTO));
    }

    @ApiOperation("根据渠道商客户编码及年份查询是否存在有效【订单】单据接口")
    @PostMapping(value = "/external/determineIntransitiveAndValidOrders")
    public ServiceData<List<CrmCustomerDetermineResultVO>> determineIntransitiveAndValidOrders(
            @Validated @RequestBody CrmCustomerDertimineParamDTO crmCustomerDertimineParamDTO,
            BindingResult bindingResult) throws Exception {
        // 数据校验
        if (bindingResult != null && bindingResult.hasErrors()) {
            throw new com.zte.springbootframe.common.exception.ValidationException(bindingResult);
        }
        return ServiceResultUtil.success(channelValidateService.determineIntransitsAndValidByOrderForm(crmCustomerDertimineParamDTO));
    }

    @ApiOperation("根据渠道商客户编码及年份查询是否存在有效【项目授权】单据接口")
    @PostMapping(value = "/external/determineIntransitiveAndValidAuths")
    public ServiceData<List<CrmCustomerDetermineResultVO>> determineIntransitiveAndValidAuths(
            @Validated @RequestBody CrmCustomerDertimineParamDTO crmCustomerDertimineParamDTO,
            BindingResult bindingResult) throws Exception {
        // 数据校验
        if (bindingResult != null && bindingResult.hasErrors()) {
            throw new com.zte.springbootframe.common.exception.ValidationException(bindingResult);
        }
        return ServiceResultUtil.success(channelValidateService.determineIntransitsAndValidByAuth(crmCustomerDertimineParamDTO));
    }

    @ApiOperation("根据渠道商客户编码及年份查询是否存在有效【配置报价】单据接口")
    @PostMapping(value = "/external/determineIntransitiveAndValidCpqs")
    public ServiceData<List<CrmCustomerDetermineResultVO>> determineIntransitiveAndValidCpqs(
            @Validated @RequestBody CrmCustomerDertimineParamDTO crmCustomerDertimineParamDTO,
            BindingResult bindingResult) throws Exception {
        // 数据校验
        if (bindingResult != null && bindingResult.hasErrors()) {
            throw new com.zte.springbootframe.common.exception.ValidationException(bindingResult);
        }
        return ServiceResultUtil.success(channelValidateService.determineIntransitsAndValidByCpq(crmCustomerDertimineParamDTO));
    }

    @ApiOperation("根据渠道商客户编码及年份查询是否存在有效【商机】接口")
    @PostMapping(value = "/external/determineIntransitiveAndValidOptys")
    public ServiceData<List<CrmCustomerDetermineResultVO>> determineIntransitsAndValidOptys(
            @Validated @RequestBody CrmCustomerDertimineParamDTO crmCustomerDertimineParamDTO,
            BindingResult bindingResult) throws Exception {
        // 数据校验
        if (bindingResult != null && bindingResult.hasErrors()) {
            throw new com.zte.springbootframe.common.exception.ValidationException(bindingResult);
        }
        return ServiceResultUtil.success(opportunityQueryService.determineIntransitsAndValidOptys(crmCustomerDertimineParamDTO));
    }

    @ApiOperation("根据商机编码更新商机状态接口--对外提供")
    @PostMapping("/external/updateOpportunityStatus")
    @SystemAuthVerify(serviceNamesWhitelist = {"zte-crm"})
    public ServiceData<Boolean> updateOpporytunityStatus(
            @RequestHeader(name = SysGlobalConst.HTTP_HEADER_X_ORIGIN_SERVICE_NAME, required = false) String serviceName,
            @Validated @RequestBody OpportunityStatusDTO opportunityStatusDTO,  BindingResult bindingResult
            ) throws Exception{
        // 数据校验
        if (bindingResult != null && bindingResult.hasErrors()) {
            throw new com.zte.springbootframe.common.exception.ValidationException(bindingResult);
        }
        String content = JSON.toJSONString(opportunityStatusDTO) + "," + LocalMessageUtils.getMessage("oppertunity.source.differentiate")+"："+serviceName ;
        loggerService.saveLogger(content, "updateOpportunityStatusForDecision");
        return ServiceResultUtil.success(opportunityQueryService.updateOpportunityStatus(opportunityStatusDTO));
    }


}
