package com.zte.mcrm.adapter.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CustClassifyExternalVO {

    /**申请单号**/
    @ApiModelProperty(value="申请单号")
    private String approveId;

    /**客户编码**/
    @ApiModelProperty(value="客户编码")
    private String ouNum;

    /**客户名字**/
    @ApiModelProperty(value="客户名字")
    private String custName;

    /**分类结果**/
    @ApiModelProperty(value="分类结果")
    private String classificationResult;

    /**分类日期**/
    @ApiModelProperty(value="分类日期")
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date classificationDate;

    @ApiModelProperty(value = "是否在一年有效期内")
    private Boolean isInTheOneYearValidityPeriod;
}
