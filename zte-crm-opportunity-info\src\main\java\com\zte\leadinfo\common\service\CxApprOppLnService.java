package com.zte.leadinfo.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zte.leadinfo.common.entity.CxApprOpHead;
import com.zte.leadinfo.common.entity.CxApprOppLn;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cx_appr_opp_ln】的数据库操作Service
* @createDate 2024-07-12 23:29:27
*/
public interface CxApprOppLnService extends IService<CxApprOppLn> {

    List<CxApprOppLn> listByOpptyIds(List<String> optyIds, List<String> approveObjects);
}
